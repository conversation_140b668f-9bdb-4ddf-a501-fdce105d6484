{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/firebase/admin.ts"], "sourcesContent": ["// Firebase Admin SDK configuration\r\nimport { initializeApp, getApps, cert } from 'firebase-admin/app';\r\nimport { getFirestore } from 'firebase-admin/firestore';\r\nimport { getAuth } from 'firebase-admin/auth';\r\nimport { getStorage } from 'firebase-admin/storage';\r\n\r\n// Initialize Firebase Admin\r\nlet adminAppInstance: any = null;\r\n\r\nconst initializeFirebaseAdmin = () => {\r\n  if (adminAppInstance) return adminAppInstance;\r\n  \r\n  if (getApps().length === 0) {\r\n    // In production, use service account key. Support two formats:\r\n    // 1) Full JSON in FIREBASE_SERVICE_ACCOUNT_KEY\r\n    // 2) Individual env vars FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL, FIREBASE_PROJECT_ID\r\n    if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {\r\n      try {\r\n        // Clean up the service account key - remove any surrounding quotes and whitespace\r\n        let rawServiceKey = process.env.FIREBASE_SERVICE_ACCOUNT_KEY.trim();\r\n        \r\n        // Remove surrounding quotes if present\r\n        if ((rawServiceKey.startsWith('\"') && rawServiceKey.endsWith('\"')) || \r\n            (rawServiceKey.startsWith(\"'\") && rawServiceKey.endsWith(\"'\"))) {\r\n          rawServiceKey = rawServiceKey.slice(1, -1);\r\n        }\r\n        \r\n        const serviceAccount = JSON.parse(rawServiceKey);\r\n        adminAppInstance = initializeApp({\r\n          credential: cert(serviceAccount),\r\n          projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'localbuzz-mpkuv',\r\n          storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\r\n        });\r\n        return adminAppInstance;\r\n      } catch (error) {\r\n        // Don't throw - fall through to try individual env vars instead\r\n      }\r\n    }\r\n\r\n    // Support separated service account env vars (useful on some hosting platforms)\r\n    if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PROJECT_ID) {\r\n      // The private key may contain escaped \\n sequences or surrounding quotes; normalize it.\r\n      let rawKey = process.env.FIREBASE_PRIVATE_KEY || '';\r\n      // Remove surrounding quotes if present\r\n      if ((rawKey.startsWith('\"') && rawKey.endsWith('\"')) || (rawKey.startsWith(\"'\") && rawKey.endsWith(\"'\"))) {\r\n        rawKey = rawKey.slice(1, -1);\r\n      }\r\n      // Convert escaped newlines to real newlines, then trim\r\n      const privateKey = rawKey.includes('\\\\n') ? rawKey.replace(/\\\\n/g, '\\n').trim() : rawKey.trim();\r\n\r\n      const serviceAccount = {\r\n        project_id: process.env.FIREBASE_PROJECT_ID,\r\n        client_email: process.env.FIREBASE_CLIENT_EMAIL,\r\n        private_key: privateKey,\r\n      } as any;\r\n\r\n      adminAppInstance = initializeApp({\r\n        credential: cert(serviceAccount),\r\n        projectId: process.env.FIREBASE_PROJECT_ID,\r\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\r\n      });\r\n      return adminAppInstance;\r\n    }\r\n\r\n    // In development, use default credentials or emulator\r\n    adminAppInstance = initializeApp({\r\n      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'localbuzz-mpkuv',\r\n    });\r\n    return adminAppInstance;\r\n  }\r\n\r\n  adminAppInstance = getApps()[0];\r\n  return adminAppInstance;\r\n};\r\n\r\n// Lazy getters for Firebase services\r\nexport const getAdminApp = () => initializeFirebaseAdmin();\r\nexport const getAdminDb = () => getFirestore(getAdminApp());\r\nexport const getAdminAuth = () => getAuth(getAdminApp());\r\nexport const getAdminStorage = () => getStorage(getAdminApp());\r\n\r\n// Backward compatibility exports that initialize lazily\r\nexport const adminDb = new Proxy({} as any, {\r\n  get: (target, prop) => (getAdminDb() as any)[prop]\r\n});\r\n\r\nexport const adminAuth = new Proxy({} as any, {\r\n  get: (target, prop) => (getAdminAuth() as any)[prop]\r\n});\r\n\r\nexport const adminStorage = new Proxy({} as any, {\r\n  get: (target, prop) => (getAdminStorage() as any)[prop]\r\n});\r\n\r\n// Default export\r\nexport const adminApp = new Proxy({} as any, {\r\n  get: (target, prop) => (getAdminApp() as any)[prop]\r\n});\r\n\r\nexport default adminApp;\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;;;;;;AACnC;AACA;AACA;AACA;;;;;;;;;;;;AAEA,4BAA4B;AAC5B,IAAI,mBAAwB;AAE5B,MAAM,0BAA0B;IAC9B,IAAI,kBAAkB,OAAO;IAE7B,IAAI,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,GAAG;QAC1B,+DAA+D;QAC/D,+CAA+C;QAC/C,0FAA0F;QAC1F,IAAI,QAAQ,GAAG,CAAC,4BAA4B,EAAE;YAC5C,IAAI;gBACF,kFAAkF;gBAClF,IAAI,gBAAgB,QAAQ,GAAG,CAAC,4BAA4B,CAAC,IAAI;gBAEjE,uCAAuC;gBACvC,IAAI,AAAC,cAAc,UAAU,CAAC,QAAQ,cAAc,QAAQ,CAAC,QACxD,cAAc,UAAU,CAAC,QAAQ,cAAc,QAAQ,CAAC,MAAO;oBAClE,gBAAgB,cAAc,KAAK,CAAC,GAAG,CAAC;gBAC1C;gBAEA,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,mBAAmB,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE;oBAC/B,YAAY,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;oBACjB,WAAW,uDAA+C;oBAC1D,aAAa;gBACf;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;YACd,gEAAgE;YAClE;QACF;QAEA,gFAAgF;QAChF,IAAI,QAAQ,GAAG,CAAC,oBAAoB,IAAI,QAAQ,GAAG,CAAC,qBAAqB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;YAC5G,wFAAwF;YACxF,IAAI,SAAS,QAAQ,GAAG,CAAC,oBAAoB,IAAI;YACjD,uCAAuC;YACvC,IAAI,AAAC,OAAO,UAAU,CAAC,QAAQ,OAAO,QAAQ,CAAC,QAAU,OAAO,UAAU,CAAC,QAAQ,OAAO,QAAQ,CAAC,MAAO;gBACxG,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC;YAC5B;YACA,uDAAuD;YACvD,MAAM,aAAa,OAAO,QAAQ,CAAC,SAAS,OAAO,OAAO,CAAC,QAAQ,MAAM,IAAI,KAAK,OAAO,IAAI;YAE7F,MAAM,iBAAiB;gBACrB,YAAY,QAAQ,GAAG,CAAC,mBAAmB;gBAC3C,cAAc,QAAQ,GAAG,CAAC,qBAAqB;gBAC/C,aAAa;YACf;YAEA,mBAAmB,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE;gBAC/B,YAAY,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;gBACjB,WAAW,QAAQ,GAAG,CAAC,mBAAmB;gBAC1C,aAAa;YACf;YACA,OAAO;QACT;QAEA,sDAAsD;QACtD,mBAAmB,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE;YAC/B,WAAW,uDAA+C;QAC5D;QACA,OAAO;IACT;IAEA,mBAAmB,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;IAC/B,OAAO;AACT;AAGO,MAAM,cAAc,IAAM;AAC1B,MAAM,aAAa,IAAM,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE;AACtC,MAAM,eAAe,IAAM,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE;AACnC,MAAM,kBAAkB,IAAM,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE;AAGzC,MAAM,UAAU,IAAI,MAAM,CAAC,GAAU;IAC1C,KAAK,CAAC,QAAQ,OAAS,AAAC,YAAoB,CAAC,KAAK;AACpD;AAEO,MAAM,YAAY,IAAI,MAAM,CAAC,GAAU;IAC5C,KAAK,CAAC,QAAQ,OAAS,AAAC,cAAsB,CAAC,KAAK;AACtD;AAEO,MAAM,eAAe,IAAI,MAAM,CAAC,GAAU;IAC/C,KAAK,CAAC,QAAQ,OAAS,AAAC,iBAAyB,CAAC,KAAK;AACzD;AAGO,MAAM,WAAW,IAAI,MAAM,CAAC,GAAU;IAC3C,KAAK,CAAC,QAAQ,OAAS,AAAC,aAAqB,CAAC,KAAK;AACrD;uCAEe", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/auth/check-session/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport adminApp, { adminAuth, adminDb } from '@/lib/firebase/admin';\r\n\r\nexport async function POST(req: Request) {\r\n  try {\r\n    const authHeader = req.headers.get('authorization') || '';\r\n    if (!authHeader.startsWith('Bearer ')) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n\r\n    const idToken = authHeader.split(' ')[1];\r\n    const decoded = await adminAuth.verifyIdToken(idToken).catch(() => null);\r\n    if (!decoded) return NextResponse.json({ error: 'Invalid token' }, { status: 401 });\r\n\r\n    const uid = decoded.uid;\r\n\r\n    try {\r\n      const userRef = adminDb.collection('users').doc(uid);\r\n      const snap = await userRef.get();\r\n      if (!snap.exists) return NextResponse.json({ ok: false, reason: 'no-user' });\r\n      const data = snap.data() as any;\r\n      const session = data.session || null;\r\n\r\n      if (!session) return NextResponse.json({ ok: false, reason: 'no-session' });\r\n\r\n      const now = new Date();\r\n      const lastActive = session.lastActive ? new Date(session.lastActive) : null;\r\n      const sessionExpiresAt = session.sessionExpiresAt ? new Date(session.sessionExpiresAt) : null;\r\n\r\n      // Inactivity rule: 30 minutes = logout\r\n      const inactivityLimitMs = 30 * 60 * 1000;\r\n      if (lastActive && now.getTime() - lastActive.getTime() > inactivityLimitMs) {\r\n        // mark session inactive\r\n        await userRef.set({ session: { active: false } }, { merge: true });\r\n        return NextResponse.json({ ok: false, reason: 'inactive' });\r\n      }\r\n\r\n      // Max session duration rule: 12 hours\r\n      if (sessionExpiresAt && now > sessionExpiresAt) {\r\n        await userRef.set({ session: { active: false } }, { merge: true });\r\n        return NextResponse.json({ ok: false, reason: 'expired' });\r\n      }\r\n\r\n      return NextResponse.json({ ok: true, lastActive: session.lastActive, sessionExpiresAt: session.sessionExpiresAt });\r\n    } catch (e: any) {\r\n      // Detect Firestore permission errors and return a helpful hint\r\n      const msg = String(e?.message || e);\r\n      if (msg.includes('PERMISSION_DENIED') || msg.includes('Missing or insufficient permissions')) {\r\n        return NextResponse.json({ ok: false, reason: 'permission_denied', hint: 'Check Firebase Admin credentials and IAM roles (service account needs Firestore access).' }, { status: 503 });\r\n      }\r\n      return NextResponse.json({ ok: false, reason: 'error' }, { status: 500 });\r\n    }\r\n  } catch (error: any) {\r\n    return NextResponse.json({ error: error?.message || String(error) }, { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe,KAAK,GAAY;IACrC,IAAI;QACF,MAAM,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB;QACvD,IAAI,CAAC,WAAW,UAAU,CAAC,YAAY,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;QAEzG,MAAM,UAAU,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE;QACxC,MAAM,UAAU,MAAM,iIAAA,CAAA,YAAS,CAAC,aAAa,CAAC,SAAS,KAAK,CAAC,IAAM;QACnE,IAAI,CAAC,SAAS,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgB,GAAG;YAAE,QAAQ;QAAI;QAEjF,MAAM,MAAM,QAAQ,GAAG;QAEvB,IAAI;YACF,MAAM,UAAU,iIAAA,CAAA,UAAO,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC;YAChD,MAAM,OAAO,MAAM,QAAQ,GAAG;YAC9B,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,IAAI;gBAAO,QAAQ;YAAU;YAC1E,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,UAAU,KAAK,OAAO,IAAI;YAEhC,IAAI,CAAC,SAAS,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,IAAI;gBAAO,QAAQ;YAAa;YAEzE,MAAM,MAAM,IAAI;YAChB,MAAM,aAAa,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,IAAI;YACvE,MAAM,mBAAmB,QAAQ,gBAAgB,GAAG,IAAI,KAAK,QAAQ,gBAAgB,IAAI;YAEzF,uCAAuC;YACvC,MAAM,oBAAoB,KAAK,KAAK;YACpC,IAAI,cAAc,IAAI,OAAO,KAAK,WAAW,OAAO,KAAK,mBAAmB;gBAC1E,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,CAAC;oBAAE,SAAS;wBAAE,QAAQ;oBAAM;gBAAE,GAAG;oBAAE,OAAO;gBAAK;gBAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,IAAI;oBAAO,QAAQ;gBAAW;YAC3D;YAEA,sCAAsC;YACtC,IAAI,oBAAoB,MAAM,kBAAkB;gBAC9C,MAAM,QAAQ,GAAG,CAAC;oBAAE,SAAS;wBAAE,QAAQ;oBAAM;gBAAE,GAAG;oBAAE,OAAO;gBAAK;gBAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,IAAI;oBAAO,QAAQ;gBAAU;YAC1D;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,IAAI;gBAAM,YAAY,QAAQ,UAAU;gBAAE,kBAAkB,QAAQ,gBAAgB;YAAC;QAClH,EAAE,OAAO,GAAQ;YACf,+DAA+D;YAC/D,MAAM,MAAM,OAAO,GAAG,WAAW;YACjC,IAAI,IAAI,QAAQ,CAAC,wBAAwB,IAAI,QAAQ,CAAC,wCAAwC;gBAC5F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,IAAI;oBAAO,QAAQ;oBAAqB,MAAM;gBAA2F,GAAG;oBAAE,QAAQ;gBAAI;YACvL;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,IAAI;gBAAO,QAAQ;YAAQ,GAAG;gBAAE,QAAQ;YAAI;QACzE;IACF,EAAE,OAAO,OAAY;QACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO,OAAO,WAAW,OAAO;QAAO,GAAG;YAAE,QAAQ;QAAI;IACrF;AACF", "debugId": null}}]}