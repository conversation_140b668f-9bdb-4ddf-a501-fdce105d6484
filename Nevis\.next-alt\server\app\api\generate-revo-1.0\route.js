const CHUNK_PUBLIC_PATH = "server/app/api/generate-revo-1.0/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_@google_generative-ai_dist_index_mjs_966d02a8._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__56320ec2._.js");
runtime.loadChunk("server/chunks/node_modules_a9f0bbd4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/generate-revo-1.0/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-revo-1.0/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-revo-1.0/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
