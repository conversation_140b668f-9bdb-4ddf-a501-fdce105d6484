{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/google-ai-direct.ts"], "sourcesContent": ["/**\r\n * Direct Google AI API Service for Gemini 2.5\r\n * Bypasses Genkit to access latest Gemini 2.5 models directly\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\n\r\n// Initialize Google AI with API key\r\nconst apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\nconst genAI = new GoogleGenerativeAI(apiKey!);\r\n\r\n// Available Gemini 2.5 models\r\nexport const GEMINI_2_5_MODELS = {\r\n  FLASH: 'gemini-2.5-flash',\r\n  PRO: 'gemini-2.5-pro',\r\n  FLASH_LITE: 'gemini-2.5-flash-lite',\r\n  FLASH_IMAGE_PREVIEW: 'gemini-2.5-flash-image-preview'\r\n} as const;\r\n\r\nexport type Gemini25Model = typeof GEMINI_2_5_MODELS[keyof typeof GEMINI_2_5_MODELS];\r\n\r\nexport interface Gemini25GenerateOptions {\r\n  model?: Gemini25Model;\r\n  temperature?: number;\r\n  maxOutputTokens?: number;\r\n  topK?: number;\r\n  topP?: number;\r\n}\r\n\r\nexport interface Gemini25TextResponse {\r\n  text: string;\r\n  finishReason?: string;\r\n  safetyRatings?: any[];\r\n}\r\n\r\nexport interface Gemini25ImageResponse {\r\n  imageData: string; // Base64 encoded image\r\n  mimeType: string;\r\n  finishReason?: string;\r\n  safetyRatings?: any[];\r\n}\r\n\r\n/**\r\n * Generate text using Gemini 2.5 models\r\n */\r\nexport async function generateText(\r\n  prompt: string,\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25TextResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.7,\r\n      maxOutputTokens = 2048,\r\n      topK = 40,\r\n      topP = 0.95\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n        topK,\r\n        topP,\r\n      },\r\n    });\r\n\r\n    const result = await geminiModel.generateContent(prompt);\r\n    const response = await result.response;\r\n    const text = response.text();\r\n\r\n\r\n    return {\r\n      text,\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate image using Gemini 2.5 models (when image generation is available)\r\n */\r\nexport async function generateImage(\r\n  prompt: string,\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25ImageResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.8,\r\n      maxOutputTokens = 1024,\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n      },\r\n    });\r\n\r\n    // For now, Gemini 2.5 doesn't have direct image generation\r\n    // This is a placeholder for when it becomes available\r\n    // We'll use text generation to create detailed design specifications\r\n    const designPrompt = `Create a detailed visual design specification for: ${prompt}\r\n\r\nPlease provide:\r\n1. Color palette (specific hex codes)\r\n2. Layout composition details\r\n3. Typography specifications\r\n4. Visual elements and their positioning\r\n5. Style and mood descriptors\r\n6. Technical implementation details\r\n\r\nFormat as JSON for easy parsing.`;\r\n\r\n    const result = await geminiModel.generateContent(designPrompt);\r\n    const response = await result.response;\r\n    const designSpecs = response.text();\r\n\r\n\r\n    // Return design specifications as \"image data\" for now\r\n    // This will be used to generate actual images via other services\r\n    return {\r\n      imageData: Buffer.from(designSpecs).toString('base64'),\r\n      mimeType: 'application/json',\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate multimodal content (text + image analysis)\r\n */\r\nexport async function generateMultimodal(\r\n  textPrompt: string,\r\n  imageData?: string, // Base64 encoded image\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25TextResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.7,\r\n      maxOutputTokens = 2048,\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n      },\r\n    });\r\n\r\n    let parts: any[] = [{ text: textPrompt }];\r\n\r\n    // Add image if provided\r\n    if (imageData) {\r\n      parts.push({\r\n        inlineData: {\r\n          mimeType: 'image/jpeg', // Assume JPEG for now\r\n          data: imageData\r\n        }\r\n      });\r\n    }\r\n\r\n    const result = await geminiModel.generateContent(parts);\r\n    const response = await result.response;\r\n    const text = response.text();\r\n\r\n\r\n    return {\r\n      text,\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 multimodal generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Test connection to Gemini 2.5 API\r\n */\r\nexport async function testConnection(): Promise<boolean> {\r\n  try {\r\n\r\n    const response = await generateText('Hello, this is a test message. Please respond with \"Connection successful!\"', {\r\n      model: GEMINI_2_5_MODELS.FLASH,\r\n      maxOutputTokens: 50\r\n    });\r\n\r\n    const isSuccessful = response.text.toLowerCase().includes('connection successful') ||\r\n      response.text.toLowerCase().includes('hello') ||\r\n      response.text.length > 0;\r\n\r\n    if (isSuccessful) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Get available models and their capabilities\r\n */\r\nexport function getAvailableModels() {\r\n  return {\r\n    models: GEMINI_2_5_MODELS,\r\n    capabilities: {\r\n      [GEMINI_2_5_MODELS.FLASH]: {\r\n        description: 'Fast and efficient for most tasks',\r\n        bestFor: ['content generation', 'design specifications', 'quick responses'],\r\n        costEfficiency: 'high'\r\n      },\r\n      [GEMINI_2_5_MODELS.PRO]: {\r\n        description: 'Most capable model for complex reasoning',\r\n        bestFor: ['complex analysis', 'detailed design planning', 'sophisticated content'],\r\n        costEfficiency: 'medium'\r\n      },\r\n      [GEMINI_2_5_MODELS.FLASH_LITE]: {\r\n        description: 'Lightweight and cost-effective',\r\n        bestFor: ['simple tasks', 'quick responses', 'high-volume requests'],\r\n        costEfficiency: 'very high'\r\n      }\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;;AAEA,oCAAoC;AACpC,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;AAE3G,IAAI,CAAC,QAAQ,CACb;AAEA,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;AAG9B,MAAM,oBAAoB;IAC/B,OAAO;IACP,KAAK;IACL,YAAY;IACZ,qBAAqB;AACvB;AA4BO,eAAe,aACpB,MAAc,EACd,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACtB,OAAO,EAAE,EACT,OAAO,IAAI,EACZ,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;gBACA;gBACA;YACF;QACF;QAEA,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAG1B,OAAO;YACL;YACA,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,cACpB,MAAc,EACd,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACvB,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;YACF;QACF;QAEA,2DAA2D;QAC3D,sDAAsD;QACtD,qEAAqE;QACrE,MAAM,eAAe,CAAC,mDAAmD,EAAE,OAAO;;;;;;;;;;gCAUtD,CAAC;QAE7B,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,cAAc,SAAS,IAAI;QAGjC,uDAAuD;QACvD,iEAAiE;QACjE,OAAO;YACL,WAAW,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;YAC7C,UAAU;YACV,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,mBACpB,UAAkB,EAClB,SAAkB,EAClB,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACvB,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;YACF;QACF;QAEA,IAAI,QAAe;YAAC;gBAAE,MAAM;YAAW;SAAE;QAEzC,wBAAwB;QACxB,IAAI,WAAW;YACb,MAAM,IAAI,CAAC;gBACT,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;YACF;QACF;QAEA,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAG1B,OAAO;YACL;YACA,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACxH;AACF;AAKO,eAAe;IACpB,IAAI;QAEF,MAAM,WAAW,MAAM,aAAa,+EAA+E;YACjH,OAAO,kBAAkB,KAAK;YAC9B,iBAAiB;QACnB;QAEA,MAAM,eAAe,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,4BACxD,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YACrC,SAAS,IAAI,CAAC,MAAM,GAAG;QAEzB,IAAI,cAAc;YAChB,OAAO;QACT,OAAO;YACL,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAKO,SAAS;IACd,OAAO;QACL,QAAQ;QACR,cAAc;YACZ,CAAC,kBAAkB,KAAK,CAAC,EAAE;gBACzB,aAAa;gBACb,SAAS;oBAAC;oBAAsB;oBAAyB;iBAAkB;gBAC3E,gBAAgB;YAClB;YACA,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBACvB,aAAa;gBACb,SAAS;oBAAC;oBAAoB;oBAA4B;iBAAwB;gBAClF,gBAAgB;YAClB;YACA,CAAC,kBAAkB,UAAU,CAAC,EAAE;gBAC9B,aAAa;gBACb,SAAS;oBAAC;oBAAgB;oBAAmB;iBAAuB;gBACpE,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-1.5-enhanced-design.ts"], "sourcesContent": ["/**\r\n * Revo 1.5 Enhanced Design Service\r\n * Two-step process: Gemini 2.5 Flash for design planning + Gemini 2.5 Flash Image Preview for final generation\r\n */\r\n\r\nimport { generateText, generateMultimodal, GEMINI_2_5_MODELS } from './google-ai-direct';\r\nimport { BrandProfile } from '@/lib/types';\r\n\r\nexport interface Revo15DesignInput {\r\n  businessType: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  imageText: string;\r\n  brandProfile: BrandProfile;\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  };\r\n  artifactInstructions?: string;\r\n  designReferences?: string[]; // Base64 encoded reference images\r\n  includePeopleInDesigns?: boolean; // Control whether designs should include people (default: true)\r\n  useLocalLanguage?: boolean; // Control whether to use local language in text (default: false)\r\n}\r\n\r\nexport interface Revo15DesignResult {\r\n  imageUrl: string;\r\n  designSpecs: any;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n  model: string;\r\n  planningModel: string;\r\n  generationModel: string;\r\n}\r\n\r\n/**\r\n * Clean website URL by removing https://, http://, and www.\r\n */\r\nfunction cleanWebsiteUrl(url: string): string {\r\n  if (!url) return '';\r\n  return url\r\n    .replace(/^https?:\\/\\//, '')\r\n    .replace(/^www\\./, '')\r\n    .replace(/\\/$/, ''); // Remove trailing slash\r\n}\r\n\r\n/**\r\n * Step 1: Generate design specifications using Gemini 2.5 Flash\r\n */\r\nexport async function generateDesignPlan(\r\n  input: Revo15DesignInput\r\n): Promise<any> {\r\n\r\n  const brandColors = [\r\n    input.brandProfile.primaryColor,\r\n    input.brandProfile.accentColor,\r\n    input.brandProfile.backgroundColor\r\n  ].filter(Boolean);\r\n\r\n  const designPlanningPrompt = `You are an expert design strategist for Revo 1.5. Create a comprehensive design plan for a ${input.platform} post.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.brandProfile.businessName}\r\n- Type: ${input.businessType}\r\n- Location: ${input.brandProfile.location || 'Global'}\r\n- Website: ${cleanWebsiteUrl(input.brandProfile.website || '')}\r\n\r\nBRAND PROFILE:\r\n- Primary Color: ${input.brandProfile.primaryColor || '#000000'}\r\n- Accent Color: ${input.brandProfile.accentColor || '#666666'}\r\n- Background Color: ${input.brandProfile.backgroundColor || '#FFFFFF'}\r\n- Writing Tone: ${input.brandProfile.writingTone || 'professional'}\r\n- Target Audience: ${input.brandProfile.targetAudience || 'General audience'}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Platform: ${input.platform}\r\n- Visual Style: ${input.visualStyle}\r\n- Text Content: \"${input.imageText}\"\r\n- Include People: ${input.includePeopleInDesigns !== false ? 'Yes' : 'No'}\r\n- Use Local Language: ${input.useLocalLanguage === true ? 'Yes' : 'No'}\r\n\r\nREVO 1.5 DESIGN PRINCIPLES:\r\n1. **Advanced Composition**: Use sophisticated layout principles (rule of thirds, golden ratio, visual hierarchy)\r\n2. **Color Harmony**: Create advanced color schemes using brand colors as foundation\r\n3. **Typography Excellence**: Select premium fonts that match brand personality\r\n4. **Visual Depth**: Add layers, shadows, gradients for professional depth\r\n5. **Brand Integration**: Seamlessly incorporate brand elements without overwhelming\r\n6. **Platform Optimization**: Tailor design for ${input.platform} best practices\r\n7. **Emotional Connection**: Design should evoke appropriate emotional response\r\n8. **Cultural Sensitivity**: Consider local cultural elements if applicable\r\n\r\nCreate a detailed design plan including:\r\n1. **Layout Strategy**: Composition approach and element placement\r\n2. **Color Palette**: Extended palette based on brand colors\r\n3. **Typography Plan**: Font selections and text hierarchy\r\n4. **Visual Elements**: Icons, shapes, patterns to include\r\n5. **Brand Integration**: How to incorporate logo and brand elements\r\n6. **Mood & Atmosphere**: Overall feeling and aesthetic direction\r\n7. **Technical Specs**: Aspect ratio, resolution, key measurements\r\n\r\nProvide a structured plan that will guide the image generation process.`;\r\n\r\n  try {\r\n    const planResponse = await generateText(\r\n      designPlanningPrompt,\r\n      {\r\n        model: GEMINI_2_5_MODELS.FLASH,\r\n        temperature: 0.7,\r\n        maxOutputTokens: 2048\r\n      }\r\n    );\r\n\r\n    return {\r\n      plan: planResponse.text,\r\n      brandColors,\r\n      timestamp: Date.now()\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Design planning failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Step 2: Generate final image using Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateFinalImage(\r\n  input: Revo15DesignInput,\r\n  designPlan: any\r\n): Promise<string> {\r\n\r\n  // Build comprehensive image generation prompt based on the design plan\r\n  const imagePrompt = buildEnhancedImagePrompt(input, designPlan);\r\n\r\n  try {\r\n    // Use the working creative asset generation with the enhanced model\r\n    const { generateCreativeAsset } = await import('@/ai/flows/generate-creative-asset');\r\n\r\n    const creativeResult = await generateCreativeAsset({\r\n      prompt: imagePrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: input.brandProfile,\r\n      maskDataUrl: null,\r\n      // Force use of Gemini 2.5 Flash Image Preview for final generation\r\n      preferredModel: GEMINI_2_5_MODELS.FLASH_IMAGE_PREVIEW\r\n    });\r\n\r\n    const imageUrl = creativeResult.imageUrl;\r\n    if (!imageUrl) {\r\n      throw new Error('No image URL returned from Gemini 2.5 Flash Image Preview');\r\n    }\r\n\r\n    return imageUrl;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Final image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build enhanced image prompt based on design plan\r\n */\r\nfunction buildEnhancedImagePrompt(input: Revo15DesignInput, designPlan: any): string {\r\n  const brandColors = [\r\n    input.brandProfile.primaryColor,\r\n    input.brandProfile.accentColor,\r\n    input.brandProfile.backgroundColor\r\n  ].filter(Boolean);\r\n\r\n  return `Create a premium ${input.platform} design following this comprehensive plan:\r\n\r\nDESIGN PLAN CONTEXT:\r\n${designPlan.plan}\r\n\r\nBRAND INTEGRATION:\r\n- Business: ${input.brandProfile.businessName}\r\n- Colors: ${brandColors.join(', ')}\r\n- Style: ${input.visualStyle}\r\n- Logo: ${input.brandProfile.logoDataUrl ? 'Include brand logo prominently' : 'No logo available'}\r\n\r\nTEXT CONTENT TO INCLUDE:\r\n\"${input.imageText}\"\r\n\r\nREVO 1.5 PREMIUM REQUIREMENTS:\r\n✨ VISUAL EXCELLENCE: Ultra-high quality, professional design standards\r\n🎨 ADVANCED COMPOSITION: Sophisticated layout with perfect visual hierarchy  \r\n🌈 COLOR MASTERY: Harmonious color palette extending brand colors\r\n📝 TYPOGRAPHY PREMIUM: Elegant, readable fonts with perfect spacing\r\n🏢 BRAND INTEGRATION: Seamless logo and brand element incorporation\r\n📱 PLATFORM OPTIMIZATION: Perfect for ${input.platform} specifications\r\n🎯 EMOTIONAL IMPACT: Design should evoke appropriate brand emotions\r\n✨ FINISHING TOUCHES: Professional polish, shadows, gradients, depth\r\n\r\nCRITICAL REQUIREMENTS:\r\n- Aspect ratio: ${input.platform === 'Instagram' ? '1:1 (square)' : '16:9 (landscape)'}\r\n- Resolution: Ultra-high quality (minimum 1024x1024)\r\n- Text readability: ALL text must be crystal clear and readable\r\n- Brand consistency: Follow brand colors and style guidelines\r\n- Professional finish: Add depth, shadows, and premium visual effects\r\n- No generic templates: Create unique, custom design\r\n\r\nGenerate a stunning, professional design that represents the pinnacle of ${input.platform} visual content.`;\r\n}\r\n\r\n/**\r\n * Main Revo 1.5 Enhanced Design Generation Function\r\n */\r\nexport async function generateRevo15EnhancedDesign(\r\n  input: Revo15DesignInput\r\n): Promise<Revo15DesignResult> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [\r\n    'Two-Step Design Process',\r\n    'Gemini 2.5 Flash Planning',\r\n    'Gemini 2.5 Flash Image Preview Generation',\r\n    'Advanced Design Strategy',\r\n    'Premium Visual Quality'\r\n  ];\r\n\r\n  try {\r\n\r\n    // Step 1: Generate design plan with Gemini 2.5 Flash\r\n    const designPlan = await generateDesignPlan(input);\r\n    enhancementsApplied.push('Strategic Design Planning');\r\n\r\n    // Step 2: Generate final image with Gemini 2.5 Flash Image Preview\r\n    const imageUrl = await generateFinalImage(input, designPlan);\r\n    enhancementsApplied.push('Premium Image Generation');\r\n\r\n    const result: Revo15DesignResult = {\r\n      imageUrl,\r\n      designSpecs: designPlan,\r\n      qualityScore: 9.8, // Higher quality score for two-step process\r\n      enhancementsApplied,\r\n      processingTime: Date.now() - startTime,\r\n      model: 'revo-1.5-enhanced',\r\n      planningModel: GEMINI_2_5_MODELS.FLASH,\r\n      generationModel: GEMINI_2_5_MODELS.FLASH_IMAGE_PREVIEW\r\n    };\r\n\r\n\r\n    return result;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.5 enhanced design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;;AA8BA;;CAEC,GACD,SAAS,gBAAgB,GAAW;IAClC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IACJ,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,UAAU,IAClB,OAAO,CAAC,OAAO,KAAK,wBAAwB;AACjD;AAKO,eAAe,mBACpB,KAAwB;IAGxB,MAAM,cAAc;QAClB,MAAM,YAAY,CAAC,YAAY;QAC/B,MAAM,YAAY,CAAC,WAAW;QAC9B,MAAM,YAAY,CAAC,eAAe;KACnC,CAAC,MAAM,CAAC;IAET,MAAM,uBAAuB,CAAC,2FAA2F,EAAE,MAAM,QAAQ,CAAC;;;YAGhI,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;QACtC,EAAE,MAAM,YAAY,CAAC;YACjB,EAAE,MAAM,YAAY,CAAC,QAAQ,IAAI,SAAS;WAC3C,EAAE,gBAAgB,MAAM,YAAY,CAAC,OAAO,IAAI,IAAI;;;iBAG9C,EAAE,MAAM,YAAY,CAAC,YAAY,IAAI,UAAU;gBAChD,EAAE,MAAM,YAAY,CAAC,WAAW,IAAI,UAAU;oBAC1C,EAAE,MAAM,YAAY,CAAC,eAAe,IAAI,UAAU;gBACtD,EAAE,MAAM,YAAY,CAAC,WAAW,IAAI,eAAe;mBAChD,EAAE,MAAM,YAAY,CAAC,cAAc,IAAI,mBAAmB;;;YAGjE,EAAE,MAAM,QAAQ,CAAC;gBACb,EAAE,MAAM,WAAW,CAAC;iBACnB,EAAE,MAAM,SAAS,CAAC;kBACjB,EAAE,MAAM,sBAAsB,KAAK,QAAQ,QAAQ,KAAK;sBACpD,EAAE,MAAM,gBAAgB,KAAK,OAAO,QAAQ,KAAK;;;;;;;;gDAQvB,EAAE,MAAM,QAAQ,CAAC;;;;;;;;;;;;;uEAaM,CAAC;IAEtE,IAAI;QACF,MAAM,eAAe,MAAM,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD,EACpC,sBACA;YACE,OAAO,qIAAA,CAAA,oBAAiB,CAAC,KAAK;YAC9B,aAAa;YACb,iBAAiB;QACnB;QAGF,OAAO;YACL,MAAM,aAAa,IAAI;YACvB;YACA,WAAW,KAAK,GAAG;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACvG;AACF;AAKO,eAAe,mBACpB,KAAwB,EACxB,UAAe;IAGf,uEAAuE;IACvE,MAAM,cAAc,yBAAyB,OAAO;IAEpD,IAAI;QACF,oEAAoE;QACpE,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAElC,MAAM,iBAAiB,MAAM,sBAAsB;YACjD,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,iBAAiB;YACjB,cAAc,MAAM,YAAY;YAChC,aAAa;YACb,mEAAmE;YACnE,gBAAgB,qIAAA,CAAA,oBAAiB,CAAC,mBAAmB;QACvD;QAEA,MAAM,WAAW,eAAe,QAAQ;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC9G;AACF;AAEA;;CAEC,GACD,SAAS,yBAAyB,KAAwB,EAAE,UAAe;IACzE,MAAM,cAAc;QAClB,MAAM,YAAY,CAAC,YAAY;QAC/B,MAAM,YAAY,CAAC,WAAW;QAC9B,MAAM,YAAY,CAAC,eAAe;KACnC,CAAC,MAAM,CAAC;IAET,OAAO,CAAC,iBAAiB,EAAE,MAAM,QAAQ,CAAC;;;AAG5C,EAAE,WAAW,IAAI,CAAC;;;YAGN,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;UACpC,EAAE,YAAY,IAAI,CAAC,MAAM;SAC1B,EAAE,MAAM,WAAW,CAAC;QACrB,EAAE,MAAM,YAAY,CAAC,WAAW,GAAG,mCAAmC,oBAAoB;;;CAGjG,EAAE,MAAM,SAAS,CAAC;;;;;;;;sCAQmB,EAAE,MAAM,QAAQ,CAAC;;;;;gBAKvC,EAAE,MAAM,QAAQ,KAAK,cAAc,iBAAiB,mBAAmB;;;;;;;yEAOd,EAAE,MAAM,QAAQ,CAAC,gBAAgB,CAAC;AAC3G;AAKO,eAAe,6BACpB,KAAwB;IAExB,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAgC;QACpC;QACA;QACA;QACA;QACA;KACD;IAED,IAAI;QAEF,qDAAqD;QACrD,MAAM,aAAa,MAAM,mBAAmB;QAC5C,oBAAoB,IAAI,CAAC;QAEzB,mEAAmE;QACnE,MAAM,WAAW,MAAM,mBAAmB,OAAO;QACjD,oBAAoB,IAAI,CAAC;QAEzB,MAAM,SAA6B;YACjC;YACA,aAAa;YACb,cAAc;YACd;YACA,gBAAgB,KAAK,GAAG,KAAK;YAC7B,OAAO;YACP,eAAe,qIAAA,CAAA,oBAAiB,CAAC,KAAK;YACtC,iBAAiB,qIAAA,CAAA,oBAAiB,CAAC,mBAAmB;QACxD;QAGA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,4CAA4C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3H;AACF", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/generate-revo-1.5/route.ts"], "sourcesContent": ["/**\n * Revo 1.5 Generation API Route\n * Enhanced Model with Advanced Features and Artifact Support\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { generateRevo15EnhancedDesign } from '@/ai/revo-1.5-enhanced-design';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      businessType,\n      platform,\n      brandProfile,\n      visualStyle,\n      imageText,\n      aspectRatio,\n      includePeopleInDesigns,\n      useLocalLanguage,\n      artifactIds\n    } = body;\n\n    // Validate required fields\n    if (!businessType || !platform || !brandProfile) {\n      return NextResponse.json({\n        success: false,\n        error: 'Missing required fields: businessType, platform, brandProfile'\n      }, { status: 400 });\n    }\n\n    console.log('Revo 1.5 generation request:', {\n      businessType,\n      platform,\n      visualStyle: visualStyle || 'modern',\n      aspectRatio: aspectRatio || '1:1',\n      artifactIds: artifactIds?.length || 0\n    });\n\n    // Prepare image text for Revo 1.5\n    const finalImageText = imageText || `${brandProfile.businessName || businessType} - Premium Content`;\n\n    // Generate content with Revo 1.5 Enhanced Design\n    const result = await generateRevo15EnhancedDesign({\n      businessType,\n      platform: platform.toLowerCase(),\n      visualStyle: visualStyle || 'modern',\n      imageText: finalImageText,\n      brandProfile: {\n        businessName: brandProfile.businessName || businessType,\n        businessType: brandProfile.businessType || businessType,\n        location: brandProfile.location || 'Location',\n        targetAudience: brandProfile.targetAudience || 'General',\n        brandVoice: brandProfile.brandVoice || 'professional',\n        contentThemes: brandProfile.contentThemes || [],\n        services: brandProfile.services || [],\n        keyFeatures: brandProfile.keyFeatures || [],\n        competitiveAdvantages: brandProfile.competitiveAdvantages || [],\n        visualStyle: visualStyle || 'modern',\n        primaryColor: brandProfile.primaryColor || '#3B82F6',\n        accentColor: brandProfile.accentColor || '#10B981',\n        backgroundColor: brandProfile.backgroundColor || '#FFFFFF',\n        logoUrl: brandProfile.logoUrl,\n        writingTone: brandProfile.brandVoice || 'professional'\n      },\n      brandConsistency: {\n        strictConsistency: false,\n        followBrandColors: true\n      },\n      artifactInstructions: artifactIds?.map((id: string) => ({\n        artifactId: id,\n        usage: 'reference'\n      })) || [],\n      includePeopleInDesigns: includePeopleInDesigns || true,\n      useLocalLanguage: useLocalLanguage || false\n    });\n\n    return NextResponse.json({\n      success: true,\n      imageUrl: result.imageUrl,\n      model: 'Revo 1.5 (Enhanced with Gemini 2.5 Flash)',\n      qualityScore: result.qualityScore || 8.8,\n      processingTime: result.processingTime || '35s',\n      enhancementsApplied: result.enhancementsApplied || [\n        'Two-Step Design Process',\n        'Advanced AI Engine',\n        'Real-Time Context',\n        'Trending Topics',\n        'Artifact Integration',\n        'Enhanced Quality Control'\n      ],\n      headline: result.headline || `${brandProfile.businessName || businessType} Excellence`,\n      subheadline: result.subheadline || 'Premium quality and service',\n      caption: result.caption || `Experience the best with ${brandProfile.businessName || businessType}. Quality you can trust, service you can rely on.`,\n      cta: result.cta || 'Learn More',\n      hashtags: result.hashtags || [\n        `#${businessType.replace(/\\s+/g, '')}`,\n        '#Quality',\n        '#Premium',\n        '#Excellence',\n        '#Professional'\n      ],\n      businessIntelligence: result.businessIntelligence || {\n        contentGoal: 'Brand awareness and engagement',\n        businessStrengths: ['Quality service', 'Professional approach'],\n        marketOpportunities: ['Digital presence', 'Customer engagement'],\n        valueProposition: 'Premium quality and reliable service'\n      },\n      artifactsUsed: artifactIds?.length || 0,\n      message: 'Revo 1.5 content generated successfully'\n    });\n\n  } catch (error) {\n    console.error('Revo 1.5 generation error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      message: 'Revo 1.5 generation failed'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({\n    message: 'Revo 1.5 Generation API',\n    description: 'Use POST method to generate content with Revo 1.5',\n    requiredFields: ['businessType', 'platform', 'brandProfile'],\n    optionalFields: ['visualStyle', 'imageText', 'aspectRatio', 'artifactIds'],\n    model: 'Enhanced with Gemini 2.5 Flash',\n    version: '1.5.0',\n    status: 'enhanced',\n    capabilities: [\n      'Advanced AI engine with superior capabilities',\n      'Enhanced content generation algorithms',\n      'Superior quality control and consistency',\n      'Professional design generation',\n      'Real-time context and trending topics',\n      'Full artifact support',\n      'Multiple aspect ratios (1:1, 16:9, 9:16)',\n      'Advanced text overlay'\n    ],\n    features: [\n      'Two-step design process',\n      'Artifact integration (up to 5 artifacts)',\n      'Real-time context awareness',\n      'Enhanced brand consistency',\n      'Advanced prompting techniques',\n      'Quality optimization algorithms'\n    ],\n    pricing: {\n      creditsPerGeneration: 2,\n      tier: 'enhanced'\n    }\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,SAAS,EACT,WAAW,EACX,sBAAsB,EACtB,gBAAgB,EAChB,WAAW,EACZ,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,gCAAgC;YAC1C;YACA;YACA,aAAa,eAAe;YAC5B,aAAa,eAAe;YAC5B,aAAa,aAAa,UAAU;QACtC;QAEA,kCAAkC;QAClC,MAAM,iBAAiB,aAAa,GAAG,aAAa,YAAY,IAAI,aAAa,kBAAkB,CAAC;QAEpG,iDAAiD;QACjD,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,+BAA4B,AAAD,EAAE;YAChD;YACA,UAAU,SAAS,WAAW;YAC9B,aAAa,eAAe;YAC5B,WAAW;YACX,cAAc;gBACZ,cAAc,aAAa,YAAY,IAAI;gBAC3C,cAAc,aAAa,YAAY,IAAI;gBAC3C,UAAU,aAAa,QAAQ,IAAI;gBACnC,gBAAgB,aAAa,cAAc,IAAI;gBAC/C,YAAY,aAAa,UAAU,IAAI;gBACvC,eAAe,aAAa,aAAa,IAAI,EAAE;gBAC/C,UAAU,aAAa,QAAQ,IAAI,EAAE;gBACrC,aAAa,aAAa,WAAW,IAAI,EAAE;gBAC3C,uBAAuB,aAAa,qBAAqB,IAAI,EAAE;gBAC/D,aAAa,eAAe;gBAC5B,cAAc,aAAa,YAAY,IAAI;gBAC3C,aAAa,aAAa,WAAW,IAAI;gBACzC,iBAAiB,aAAa,eAAe,IAAI;gBACjD,SAAS,aAAa,OAAO;gBAC7B,aAAa,aAAa,UAAU,IAAI;YAC1C;YACA,kBAAkB;gBAChB,mBAAmB;gBACnB,mBAAmB;YACrB;YACA,sBAAsB,aAAa,IAAI,CAAC,KAAe,CAAC;oBACtD,YAAY;oBACZ,OAAO;gBACT,CAAC,MAAM,EAAE;YACT,wBAAwB,0BAA0B;YAClD,kBAAkB,oBAAoB;QACxC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,OAAO,QAAQ;YACzB,OAAO;YACP,cAAc,OAAO,YAAY,IAAI;YACrC,gBAAgB,OAAO,cAAc,IAAI;YACzC,qBAAqB,OAAO,mBAAmB,IAAI;gBACjD;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU,OAAO,QAAQ,IAAI,GAAG,aAAa,YAAY,IAAI,aAAa,WAAW,CAAC;YACtF,aAAa,OAAO,WAAW,IAAI;YACnC,SAAS,OAAO,OAAO,IAAI,CAAC,yBAAyB,EAAE,aAAa,YAAY,IAAI,aAAa,iDAAiD,CAAC;YACnJ,KAAK,OAAO,GAAG,IAAI;YACnB,UAAU,OAAO,QAAQ,IAAI;gBAC3B,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;gBACtC;gBACA;gBACA;gBACA;aACD;YACD,sBAAsB,OAAO,oBAAoB,IAAI;gBACnD,aAAa;gBACb,mBAAmB;oBAAC;oBAAmB;iBAAwB;gBAC/D,qBAAqB;oBAAC;oBAAoB;iBAAsB;gBAChE,kBAAkB;YACpB;YACA,eAAe,aAAa,UAAU;YACtC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,aAAa;QACb,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,gBAAgB;YAAC;YAAe;YAAa;YAAe;SAAc;QAC1E,OAAO;QACP,SAAS;QACT,QAAQ;QACR,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP,sBAAsB;YACtB,MAAM;QACR;IACF;AACF", "debugId": null}}]}