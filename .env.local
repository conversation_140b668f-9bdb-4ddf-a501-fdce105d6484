# Google AI API Key for Gemini
# Get your API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=AIzaSyA5YywCoIFeRajFEYObPwVREOzrdgPk07E

# Alternative environment variable names (any of these will work):
# GOOGLE_API_KEY=your_api_key_here
# GOOGLE_GENAI_API_KEY=your_api_key_here

# Firebase Configuration
# Get these values from your Firebase project settings
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAIQQLuNAc0YhNz4o9LF1Zyw_Fy0nJUfwI
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=localbuzz-mpkuv.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=localbuzz-mpkuv
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=localbuzz-mpkuv.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:3f6b7d195dd4a847c4e1a2

# OpenAI Configuration (GPT-Image 1 - Latest Image Generation Model)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Firebase Admin SDK (for server-side operations)
# This should be the entire service account key JSON as a string
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Google Cloud Configuration for Imagen 4
# Project ID for Google Cloud Vertex AI
GOOGLE_CLOUD_PROJECT_ID=localbuzz-mpkuv

# AIML API Configuration for Revo 2.0 (FLUX Kontext Max)
# Get your API key from: https://aimlapi.com/
# Using your real AIML API key for Revo 2.0
AIML_API_KEY=fa736f2cd7fe42829d2196c15357ae6e

# Stripe Configuration (for payments)
# Get these from your Stripe dashboard: https://dashboard.stripe.com/apikeys
# For development, you can use test keys or leave empty to disable payments
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_placeholder
STRIPE_SECRET_KEY=sk_test_placeholder

# Development Mode (set to true to bypass authentication for testing)
NEXT_PUBLIC_DEV_MODE=true
