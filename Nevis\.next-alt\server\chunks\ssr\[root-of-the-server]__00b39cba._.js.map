{"version": 3, "sources": [], "sections": [{"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/genkit.ts"], "sourcesContent": ["import { genkit } from 'genkit';\r\nimport { googleAI } from '@genkit-ai/googleai';\r\n\r\n// Get API key from environment variables\r\nconst apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\nexport const ai = genkit({\r\n  plugins: [googleAI({ apiKey })],\r\n  model: 'googleai/gemini-2.0-flash', // Back to working Gemini 2.0 Flash\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEA,yCAAyC;AACzC,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;AAE3G,IAAI,CAAC,QAAQ,CACb;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;YAAE;QAAO;KAAG;IAC/B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/analyze-brand.ts"], "sourcesContent": ["'use server';\r\n\r\n/**\r\n * @fileOverview Analyzes a brand's website and design examples to extract brand voice, visual style, and other key business details.\r\n *\r\n * - analyzeBrand - A function that initiates the brand analysis process.\r\n * - AnalyzeBrandInput - The input type for the analyzeBrand function.\r\n * - AnalyzeBrandOutput - The return type for the analyzeBrand function.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'genkit';\r\n\r\nconst AnalyzeBrandInputSchema = z.object({\r\n  websiteUrl: z.string().describe('The URL of the brand\\'s website to analyze.'),\r\n  designImageUris: z.array(z.string()).describe(\"A list of data URIs of previous design examples. Each must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'.\"),\r\n  websiteContent: z.string().optional().describe('The scraped content from the website for analysis.'),\r\n});\r\nexport type AnalyzeBrandInput = z.infer<typeof AnalyzeBrandInputSchema>;\r\n\r\nconst AnalyzeBrandOutputSchema = z.object({\r\n  // Core Business Information\r\n  businessName: z.string().describe('The EXACT business name, company name, or brand name as it appears on the website. This should be the PROPER NAME like \"Apple Inc.\", \"Microsoft Corporation\", \"Joe\\'s Pizza\", NOT a description of what they do. Look for the company name in headers, logos, titles, \"About Us\" sections, or anywhere the business identifies itself. Extract the precise name they use, not their business type or industry.'),\r\n  description: z.string().describe('A comprehensive, detailed summary of the business that includes: what they do, how they do it, their mission/values, their approach, their history, and what makes them unique. Combine information from multiple website sections to create a thorough description. Minimum 3-4 sentences using the company\\'s own words.'),\r\n  businessType: z.string().optional().describe('The specific type/category of business like \"Software Company\", \"Restaurant\", \"Consulting Firm\", \"E-commerce Store\" - this describes WHAT they do, not WHO they are. This is different from the business name.'),\r\n  industry: z.string().optional().describe('The specific industry sector the business operates in using their own terminology.'),\r\n  targetAudience: z.string().describe('DETAILED description of the specific target audience, customer base, client types, demographics, business types, industries, or customer characteristics this company mentions they serve. Be very specific and comprehensive. Include customer examples, business sizes, industries, or any specific customer details mentioned on the website.'),\r\n\r\n  // Services and Products\r\n  services: z.string().describe('A comprehensive newline-separated list of ALL services, products, packages, plans, or offerings this specific company provides. Search the entire website content thoroughly. Format each as \"Service Name: Detailed description as written on their website including features, benefits, what\\'s included\". Extract the company\\'s own descriptions, not generic ones. Include pricing, packages, service tiers, features, or any details mentioned. Be comprehensive and don\\'t miss any services.'),\r\n  keyFeatures: z.string().optional().describe('ALL the SPECIFIC key features, benefits, or unique selling propositions that THIS company highlights about their offerings. Use their exact wording and claims. Be comprehensive and detailed.'),\r\n  competitiveAdvantages: z.string().optional().describe('What THIS specific company says makes them different from competitors. Extract their own competitive claims and differentiators, not generic industry advantages. Use their exact wording.'),\r\n\r\n  // Brand Identity and Voice\r\n  visualStyle: z.string().describe('A detailed description of THIS company\\'s specific visual style based on their actual design examples and website. Describe the exact colors, typography, layout patterns, imagery style, and aesthetic choices THEY use. Reference specific design elements visible in their materials.'),\r\n  writingTone: z.string().describe('The SPECIFIC writing tone and voice THIS company uses in their actual website content. Analyze their actual text, headlines, and copy to describe their unique communication style. Use examples from their content.'),\r\n  contentThemes: z.string().describe('The SPECIFIC themes, topics, and messaging patterns THIS company focuses on in their actual content. Extract the exact topics they discuss and how they position themselves.'),\r\n  brandPersonality: z.string().optional().describe('THIS company\\'s specific brand personality as expressed through their actual content and design choices. Base this on their real communications, not generic assumptions.'),\r\n\r\n  // Visual Design Analysis\r\n  colorPalette: z.object({\r\n    primary: z.string().optional().describe('Primary brand color in hex format extracted from the uploaded design examples. Look carefully at the most prominent color used in logos, headers, buttons, or main design elements in the images.'),\r\n    secondary: z.string().optional().describe('Secondary brand color in hex format extracted from the uploaded design examples. Look for the second most used color in the designs.'),\r\n    accent: z.string().optional().describe('Accent color in hex format extracted from the uploaded design examples. Look for colors used for highlights, calls-to-action, or accent elements in the images.'),\r\n    description: z.string().optional().describe('Detailed description of the overall color scheme and palette used in the design examples. Describe the colors you can actually see in the uploaded images and the mood/feeling they create.'),\r\n  }).optional().describe('Color palette analysis extracted from the uploaded design examples. Analyze the actual colors visible in the design images provided.'),\r\n\r\n  typography: z.object({\r\n    style: z.string().optional().describe('Typography style (e.g., modern, classic, playful, professional).'),\r\n    characteristics: z.string().optional().describe('Font characteristics and typography choices observed.'),\r\n  }).optional().describe('Typography analysis from design examples and website.'),\r\n\r\n  // Contact and Location Information\r\n  contactInfo: z.object({\r\n    phone: z.string().optional().describe('The main contact phone number.'),\r\n    email: z.string().optional().describe('The main contact email address.'),\r\n    address: z.string().optional().describe('The physical business address.'),\r\n    website: z.string().optional().describe('Additional website URLs or domains mentioned.'),\r\n    hours: z.string().optional().describe('Business hours if mentioned on the website.'),\r\n  }).describe('The contact information for the business, extracted from the website.'),\r\n\r\n  // Social Media and Online Presence\r\n  socialMedia: z.object({\r\n    facebook: z.string().optional().describe('Facebook page URL if found on the website.'),\r\n    instagram: z.string().optional().describe('Instagram profile URL if found on the website.'),\r\n    twitter: z.string().optional().describe('Twitter profile URL if found on the website.'),\r\n    linkedin: z.string().optional().describe('LinkedIn profile URL if found on the website.'),\r\n    youtube: z.string().optional().describe('YouTube channel URL if found on the website.'),\r\n    other: z.array(z.string()).optional().describe('Other social media or platform URLs found.'),\r\n  }).optional().describe('Social media presence and URLs found on the website.'),\r\n\r\n  // Additional Business Details\r\n  location: z.string().optional().describe('Geographic location or service area of the business.'),\r\n  establishedYear: z.string().optional().describe('Year the business was established if mentioned.'),\r\n  teamSize: z.string().optional().describe('Information about team size or company size if mentioned.'),\r\n  certifications: z.array(z.string()).optional().describe('Professional certifications, awards, or credentials mentioned.'),\r\n\r\n  // Content and Marketing Insights\r\n  contentStrategy: z.string().optional().describe('Insights into their content marketing strategy based on website content.'),\r\n  callsToAction: z.array(z.string()).optional().describe('Common calls-to-action used throughout the website.'),\r\n  valueProposition: z.string().optional().describe('The main value proposition or promise to customers.'),\r\n});\r\nexport type BrandAnalysisResult = z.infer<typeof AnalyzeBrandOutputSchema>;\r\n\r\nexport async function analyzeBrand(input: AnalyzeBrandInput): Promise<BrandAnalysisResult> {\r\n  return analyzeBrandFlow(input);\r\n}\r\n\r\nconst analyzeBrandPrompt = ai.definePrompt({\r\n  name: 'analyzeBrandPrompt',\r\n  input: { schema: AnalyzeBrandInputSchema },\r\n  output: { schema: AnalyzeBrandOutputSchema },\r\n  prompt: `You are an expert brand strategist, business analyst, and design consultant with deep expertise in brand identity, visual design, and digital marketing. Your task is to perform an extremely comprehensive and detailed analysis of THIS SPECIFIC BUSINESS based on its website and design examples.\r\n\r\n  **CRITICAL INSTRUCTION: BE COMPANY-SPECIFIC, NOT GENERIC**\r\n  - Extract ONLY information that is specifically mentioned on THIS company's website\r\n  - Use the EXACT wording and terminology that THIS company uses\r\n  - Do NOT provide generic industry descriptions or assumptions\r\n  - Focus on what makes THIS specific business unique and different\r\n  - Extract the company's OWN words about their services, not generic descriptions\r\n\r\n  **Source Information:**\r\n  - Website URL: {{{websiteUrl}}}\r\n  - Website Content: {{{websiteContent}}}\r\n  - Design Examples: These are crucial for understanding visual style, color palette, typography, and brand aesthetic.\r\n  {{#each designImageUris}}\r\n  Design Example: {{media url=this}}\r\n  {{/each}}\r\n\r\n  **COMPANY-SPECIFIC ANALYSIS REQUIREMENTS:**\r\n\r\n  **🏢 THIS COMPANY'S BUSINESS DETAILS (Extract from Website Content Above):**\r\n  1. **Business Name:** Extract the EXACT business name, company name, or brand name as it appears on the website. Look for the company name in headers, logos, page titles, \"About Us\" sections, contact information, or anywhere the business identifies itself. Extract the precise name they use - this is critical for brand identity.\r\n  2. **Business Description:** Find and extract a COMPREHENSIVE and DETAILED description of this company from the website content. Look for \"About Us\", \"Who We Are\", \"Our Story\", \"Mission\", \"Vision\" sections. Combine multiple sections to create a thorough description that includes: what they do, how they do it, their mission/values, their history, their approach, and what makes them unique. Use their own words but create a complete picture. Minimum 3-4 sentences.\r\n  3. **Business Type & Industry:** Use the SPECIFIC terms this company uses in their website content to describe their business type and industry. Be precise and specific.\r\n  4. **Target Audience:** This is CRITICAL - Extract EXACTLY who this company says they serve from the website content. Look for \"Who We Serve\", \"Our Clients\", \"Target Market\", \"Perfect For\", \"Ideal Customers\", \"We Help\" sections. Also look for customer testimonials, case studies, or examples that indicate their target market. Include demographics, business types, industries, or specific customer characteristics they mention. Be very detailed and specific.\r\n  5. **Services/Products:** Extract EVERY service and product this company specifically offers from the website content. Look in \"Services\", \"What We Do\", \"Products\", \"Solutions\", \"Offerings\", \"Packages\", \"Plans\", \"Pricing\" sections. Use their EXACT service names and descriptions as written. Include ALL services, packages, tiers, or offerings mentioned. Format as \"Service Name: Detailed description as written on their website\" on separate lines. Don't miss any services.\r\n  6. **Key Features & Benefits:** Extract ALL the SPECIFIC features and benefits this company highlights about their offerings from the website content. Look in \"Features\", \"Benefits\", \"Why Choose Us\" sections. Use their exact wording and claims. Be comprehensive.\r\n  7. **Competitive Advantages:** Extract what THIS company specifically says makes them different or better from the website content. Look for \"Why Choose Us\", \"What Makes Us Different\", \"Our Advantage\", \"Why We're Better\" sections. Use their own competitive claims and differentiators.\r\n  8. **Value Proposition:** Extract the EXACT value proposition or promises this company makes to their customers from the website content. What do they promise to deliver?\r\n\r\n  **🎨 VISUAL DESIGN DEEP ANALYSIS (Analyze the Design Examples Carefully):**\r\n  8. **Visual Style:** Provide a detailed analysis of the overall visual aesthetic, design approach, imagery style, layout patterns, and visual hierarchy. Base this primarily on the design examples provided. Describe the specific design elements you can see in the uploaded images.\r\n  9. **Color Palette Analysis - CRITICAL:**\r\n     - CAREFULLY examine each design example image to identify the EXACT colors used\r\n     - Extract specific colors in hex format from the designs (look at backgrounds, text, buttons, accents, logos)\r\n     - Identify the primary brand color (most prominent color in the designs)\r\n     - Identify secondary colors and accent colors used in the designs\r\n     - Describe the overall color scheme and mood it creates\r\n     - Be very specific about the colors you can see in the uploaded design examples\r\n  10. **Typography Analysis:**\r\n     - Examine the design examples to describe the actual font styles and typography choices used\r\n     - Identify if fonts are modern, classic, playful, professional, etc. based on what you see in the images\r\n     - Note any distinctive typographic elements visible in the design examples\r\n\r\n  **✍️ BRAND VOICE & CONTENT ANALYSIS:**\r\n  11. **Writing Tone:** Analyze the brand's communication style in detail (formal, casual, witty, professional, friendly, authoritative, conversational, etc.).\r\n  12. **Content Themes:** Identify recurring themes, topics, and messaging patterns throughout the website and designs.\r\n  13. **Brand Personality:** Describe the overall brand character and personality as expressed through content and design.\r\n  14. **Content Strategy:** Analyze their approach to content marketing and communication.\r\n  15. **Calls to Action:** Extract common CTAs and action-oriented language used.\r\n\r\n  **📞 CONTACT & BUSINESS DETAILS:**\r\n  16. **Complete Contact Information:** Extract phone numbers, email addresses, physical addresses, business hours, and any additional contact methods.\r\n  17. **Location & Service Area:** Identify geographic location and areas they serve.\r\n  18. **Business Details:** Look for establishment year, team size, company history, certifications, awards, or credentials.\r\n\r\n  **🌐 DIGITAL PRESENCE ANALYSIS:**\r\n  19. **Social Media Presence:** Extract ALL social media URLs found (Facebook, Instagram, Twitter, LinkedIn, YouTube, TikTok, etc.).\r\n  20. **Additional Websites:** Note any additional domains, subdomains, or related websites mentioned.\r\n\r\n  **CRITICAL ANALYSIS INSTRUCTIONS - COMPANY-SPECIFIC EXTRACTION:**\r\n\r\n  **FOR SERVICES/PRODUCTS (Search ALL Sections in Website Content Above):**\r\n  - Search the website content for \"Services\", \"What We Do\", \"Our Services\", \"Products\", \"Solutions\", \"Offerings\", \"Packages\", \"Plans\", \"Pricing\" sections\r\n  - Extract EVERY service name as the company lists them in their website content - don't miss any\r\n  - Include the company's OWN descriptions of each service from their website text\r\n  - Look for pricing information, package details, service tiers, features included in each service\r\n  - Look in multiple sections - services might be mentioned in different parts of the website\r\n  - Format as: \"Service Name: Company's exact description of what this service includes, features, benefits, etc.\"\r\n  - Include any pricing tiers, packages, or service levels mentioned in the content\r\n  - Be comprehensive - extract ALL services, not just the main ones\r\n\r\n  **FOR TARGET AUDIENCE (Search ALL Sections for Customer Information):**\r\n  - Search the website content for \"Who We Serve\", \"Our Clients\", \"Target Market\", \"Perfect For\", \"Ideal Customers\", \"We Help\", \"Client Types\" sections\r\n  - Look for customer testimonials, case studies, or examples that indicate their target market\r\n  - Extract specific demographics, business types, industries, company sizes, or customer characteristics they mention\r\n  - Look for phrases like \"small businesses\", \"enterprise clients\", \"startups\", \"restaurants\", \"healthcare providers\", etc.\r\n  - Include any specific customer examples or client types mentioned\r\n  - Be very detailed and specific about who they serve\r\n\r\n  **FOR BUSINESS DESCRIPTION (Create Comprehensive Description):**\r\n  - Search the website content for \"About Us\", \"Who We Are\", \"Our Story\", \"Mission\", \"Vision\", \"Company\" sections\r\n  - Combine information from multiple sections to create a thorough, detailed description\r\n  - Include what they do, how they do it, their mission/values, their approach, their history, what makes them unique\r\n  - Use their own words but create a complete, comprehensive picture\r\n  - Make it detailed and informative - minimum 3-4 sentences\r\n\r\n  **FOR TARGET AUDIENCE:**\r\n  - Look for \"Who We Serve\", \"Our Clients\", \"Target Market\" information\r\n  - Extract the SPECIFIC customer types they mention\r\n  - Use their exact terminology for their customer base\r\n\r\n  **FOR COMPETITIVE ADVANTAGES:**\r\n  - Find \"Why Choose Us\", \"What Makes Us Different\", \"Our Advantage\" sections\r\n  - Extract their SPECIFIC claims about what makes them unique\r\n  - Use their exact competitive positioning statements\r\n\r\n  **GENERAL EXTRACTION RULES:**\r\n  - Be extremely thorough and detailed in your analysis\r\n  - Extract every piece of relevant information you can find\r\n  - For design analysis, pay close attention to the uploaded design examples\r\n  - Look for subtle details like color codes, font choices, layout patterns\r\n  - Extract contact information from headers, footers, contact pages, and anywhere else it appears\r\n  - Look for social media links in headers, footers, and throughout the site\r\n  - If information is not available, leave those fields empty rather than guessing\r\n  - NEVER use generic industry descriptions - only use company-specific information\r\n  - Quote the company's exact wording whenever possible\r\n\r\n  **FINAL REQUIREMENTS:**\r\n  - Be EXTREMELY thorough and comprehensive in your analysis\r\n  - Extract EVERY piece of relevant information from the website content\r\n  - Don't miss any services, features, or customer details\r\n  - Analyze the design examples carefully for exact colors\r\n  - Create detailed, informative descriptions using the company's own words\r\n  - Target audience description must be specific and detailed\r\n  - Services list must be comprehensive and complete\r\n  - Color analysis must be based on actual colors visible in the design examples\r\n\r\n  **OUTPUT FORMAT:**\r\n  Provide a complete, detailed analysis in the required JSON format with all available information extracted and organized according to the schema.\r\n  `,\r\n});\r\n\r\n// Website scraping function with enhanced content extraction\r\nasync function scrapeWebsiteContent(url: string): Promise<string> {\r\n  try {\r\n\r\n    // Import cheerio for HTML parsing\r\n    const cheerio = await import('cheerio');\r\n\r\n    // Use fetch to get the website content\r\n    const response = await fetch(url, {\r\n      headers: {\r\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    const html = await response.text();\r\n    const $ = cheerio.load(html);\r\n\r\n    // Remove unwanted elements\r\n    $('script, style, nav, footer, header, .cookie-banner, .popup, .modal').remove();\r\n\r\n    // Extract structured content\r\n    const extractedContent = {\r\n      title: $('title').text().trim(),\r\n      metaDescription: $('meta[name=\"description\"]').attr('content') || '',\r\n      headings: {\r\n        h1: $('h1').map((_, el) => $(el).text().trim()).get(),\r\n        h2: $('h2').map((_, el) => $(el).text().trim()).get(),\r\n        h3: $('h3').map((_, el) => $(el).text().trim()).get(),\r\n      },\r\n      // Look for common business sections with more comprehensive selectors\r\n      aboutSection: $('section:contains(\"About\"), div:contains(\"About Us\"), .about, #about, section:contains(\"Who We Are\"), div:contains(\"Our Story\"), .story, #story').text().trim(),\r\n      servicesSection: $('section:contains(\"Services\"), div:contains(\"Services\"), .services, #services, section:contains(\"What We Do\"), div:contains(\"What We Do\"), section:contains(\"Solutions\"), div:contains(\"Solutions\"), .solutions, #solutions, section:contains(\"Offerings\"), div:contains(\"Offerings\")').text().trim(),\r\n      contactSection: $('section:contains(\"Contact\"), div:contains(\"Contact\"), .contact, #contact, section:contains(\"Get in Touch\"), div:contains(\"Reach Us\")').text().trim(),\r\n      // Enhanced target audience extraction\r\n      targetAudienceSection: $('section:contains(\"Who We Serve\"), div:contains(\"Who We Serve\"), section:contains(\"Our Clients\"), div:contains(\"Our Clients\"), section:contains(\"Target\"), div:contains(\"Target\"), section:contains(\"For\"), div:contains(\"Perfect For\"), .clients, #clients, .audience, #audience').text().trim(),\r\n      // More comprehensive service extraction\r\n      featuresSection: $('section:contains(\"Features\"), div:contains(\"Features\"), .features, #features, section:contains(\"Benefits\"), div:contains(\"Benefits\"), .benefits, #benefits').text().trim(),\r\n      packagesSection: $('section:contains(\"Packages\"), div:contains(\"Packages\"), .packages, #packages, section:contains(\"Plans\"), div:contains(\"Plans\"), .plans, #plans, section:contains(\"Pricing\"), div:contains(\"Pricing\"), .pricing, #pricing').text().trim(),\r\n      // Extract all paragraph text\r\n      paragraphs: $('p').map((_, el) => $(el).text().trim()).get().filter(text => text.length > 20),\r\n      // Extract list items (often contain services/features)\r\n      listItems: $('li').map((_, el) => $(el).text().trim()).get().filter(text => text.length > 10),\r\n      // Extract any text that might contain business info\r\n      mainContent: $('main, .main, .content, .container').text().trim(),\r\n    };\r\n\r\n    // Combine all extracted content into a structured format\r\n    let structuredContent = '';\r\n\r\n    if (extractedContent.title) {\r\n      structuredContent += `WEBSITE TITLE: ${extractedContent.title}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.metaDescription) {\r\n      structuredContent += `META DESCRIPTION: ${extractedContent.metaDescription}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.headings.h1.length > 0) {\r\n      structuredContent += `MAIN HEADINGS: ${extractedContent.headings.h1.join(' | ')}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.aboutSection) {\r\n      structuredContent += `ABOUT SECTION: ${extractedContent.aboutSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.servicesSection) {\r\n      structuredContent += `SERVICES SECTION: ${extractedContent.servicesSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.targetAudienceSection) {\r\n      structuredContent += `TARGET AUDIENCE SECTION: ${extractedContent.targetAudienceSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.featuresSection) {\r\n      structuredContent += `FEATURES/BENEFITS SECTION: ${extractedContent.featuresSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.packagesSection) {\r\n      structuredContent += `PACKAGES/PRICING SECTION: ${extractedContent.packagesSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.contactSection) {\r\n      structuredContent += `CONTACT SECTION: ${extractedContent.contactSection}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.listItems.length > 0) {\r\n      structuredContent += `KEY POINTS/SERVICES: ${extractedContent.listItems.slice(0, 20).join(' | ')}\\n\\n`;\r\n    }\r\n\r\n    if (extractedContent.paragraphs.length > 0) {\r\n      structuredContent += `MAIN CONTENT: ${extractedContent.paragraphs.slice(0, 10).join(' ')}\\n\\n`;\r\n    }\r\n\r\n    // Fallback to main content if structured extraction didn't work well\r\n    if (structuredContent.length < 500 && extractedContent.mainContent) {\r\n      structuredContent += `FULL CONTENT: ${extractedContent.mainContent}`;\r\n    }\r\n\r\n    // Clean up and limit content length\r\n    structuredContent = structuredContent\r\n      .replace(/\\s+/g, ' ')\r\n      .trim();\r\n\r\n    // Limit content length to avoid token limits (increased for better analysis)\r\n    if (structuredContent.length > 15000) {\r\n      structuredContent = structuredContent.substring(0, 15000) + '...';\r\n    }\r\n\r\n\r\n    return structuredContent;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Failed to scrape website content: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\nconst analyzeBrandFlow = ai.defineFlow(\r\n  {\r\n    name: 'analyzeBrandFlow',\r\n    inputSchema: AnalyzeBrandInputSchema,\r\n    outputSchema: AnalyzeBrandOutputSchema,\r\n  },\r\n  async input => {\r\n    // First, scrape the website content\r\n    const websiteContent = await scrapeWebsiteContent(input.websiteUrl);\r\n\r\n    // Create enhanced input with website content\r\n    const enhancedInput = {\r\n      ...input,\r\n      websiteContent\r\n    };\r\n\r\n    const { output } = await analyzeBrandPrompt(enhancedInput);\r\n    return output!;\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,0BAA0B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC9C,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACjD;AAGA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,4BAA4B;IAC5B,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC7C,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAEpC,wBAAwB;IACxB,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,uBAAuB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEtD,2BAA2B;IAC3B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEjD,yBAAyB;IACzB,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACxC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC1C,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACvC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC9C,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACtC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAClD,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,mCAAmC;IACnC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACtC,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACtC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACxC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACxC,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACxC,GAAG,QAAQ,CAAC;IAEZ,mCAAmC;IACnC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACzC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC1C,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACxC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACzC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACxC,OAAO,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACjD,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,8BAA8B;IAC9B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAChD,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,gBAAgB,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;IAExD,iCAAiC;IACjC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAChD,eAAe,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACvD,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACnD;AAGO,eAAe,aAAa,KAAwB;IACzD,OAAO,iBAAiB;AAC1B;AAEA,MAAM,qBAAqB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACzC,MAAM;IACN,OAAO;QAAE,QAAQ;IAAwB;IACzC,QAAQ;QAAE,QAAQ;IAAyB;IAC3C,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAuHT,CAAC;AACH;AAEA,6DAA6D;AAC7D,eAAe,qBAAqB,GAAW;IAC7C,IAAI;QAEF,kCAAkC;QAClC,MAAM,UAAU;QAEhB,uCAAuC;QACvC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,IAAI,QAAQ,IAAI,CAAC;QAEvB,2BAA2B;QAC3B,EAAE,sEAAsE,MAAM;QAE9E,6BAA6B;QAC7B,MAAM,mBAAmB;YACvB,OAAO,EAAE,SAAS,IAAI,GAAG,IAAI;YAC7B,iBAAiB,EAAE,4BAA4B,IAAI,CAAC,cAAc;YAClE,UAAU;gBACR,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;gBACnD,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;gBACnD,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;YACrD;YACA,sEAAsE;YACtE,cAAc,EAAE,kJAAkJ,IAAI,GAAG,IAAI;YAC7K,iBAAiB,EAAE,wRAAwR,IAAI,GAAG,IAAI;YACtT,gBAAgB,EAAE,wIAAwI,IAAI,GAAG,IAAI;YACrK,sCAAsC;YACtC,uBAAuB,EAAE,oRAAoR,IAAI,GAAG,IAAI;YACxT,wCAAwC;YACxC,iBAAiB,EAAE,8JAA8J,IAAI,GAAG,IAAI;YAC5L,iBAAiB,EAAE,4NAA4N,IAAI,GAAG,IAAI;YAC1P,6BAA6B;YAC7B,YAAY,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAC1F,uDAAuD;YACvD,WAAW,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAC1F,oDAAoD;YACpD,aAAa,EAAE,qCAAqC,IAAI,GAAG,IAAI;QACjE;QAEA,yDAAyD;QACzD,IAAI,oBAAoB;QAExB,IAAI,iBAAiB,KAAK,EAAE;YAC1B,qBAAqB,CAAC,eAAe,EAAE,iBAAiB,KAAK,CAAC,IAAI,CAAC;QACrE;QAEA,IAAI,iBAAiB,eAAe,EAAE;YACpC,qBAAqB,CAAC,kBAAkB,EAAE,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAClF;QAEA,IAAI,iBAAiB,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;YAC3C,qBAAqB,CAAC,eAAe,EAAE,iBAAiB,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC;QACvF;QAEA,IAAI,iBAAiB,YAAY,EAAE;YACjC,qBAAqB,CAAC,eAAe,EAAE,iBAAiB,YAAY,CAAC,IAAI,CAAC;QAC5E;QAEA,IAAI,iBAAiB,eAAe,EAAE;YACpC,qBAAqB,CAAC,kBAAkB,EAAE,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAClF;QAEA,IAAI,iBAAiB,qBAAqB,EAAE;YAC1C,qBAAqB,CAAC,yBAAyB,EAAE,iBAAiB,qBAAqB,CAAC,IAAI,CAAC;QAC/F;QAEA,IAAI,iBAAiB,eAAe,EAAE;YACpC,qBAAqB,CAAC,2BAA2B,EAAE,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAC3F;QAEA,IAAI,iBAAiB,eAAe,EAAE;YACpC,qBAAqB,CAAC,0BAA0B,EAAE,iBAAiB,eAAe,CAAC,IAAI,CAAC;QAC1F;QAEA,IAAI,iBAAiB,cAAc,EAAE;YACnC,qBAAqB,CAAC,iBAAiB,EAAE,iBAAiB,cAAc,CAAC,IAAI,CAAC;QAChF;QAEA,IAAI,iBAAiB,SAAS,CAAC,MAAM,GAAG,GAAG;YACzC,qBAAqB,CAAC,qBAAqB,EAAE,iBAAiB,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC;QACxG;QAEA,IAAI,iBAAiB,UAAU,CAAC,MAAM,GAAG,GAAG;YAC1C,qBAAqB,CAAC,cAAc,EAAE,iBAAiB,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;QAChG;QAEA,qEAAqE;QACrE,IAAI,kBAAkB,MAAM,GAAG,OAAO,iBAAiB,WAAW,EAAE;YAClE,qBAAqB,CAAC,cAAc,EAAE,iBAAiB,WAAW,EAAE;QACtE;QAEA,oCAAoC;QACpC,oBAAoB,kBACjB,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,6EAA6E;QAC7E,IAAI,kBAAkB,MAAM,GAAG,OAAO;YACpC,oBAAoB,kBAAkB,SAAS,CAAC,GAAG,SAAS;QAC9D;QAGA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACjH;AACF;AAEA,MAAM,mBAAmB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACpC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAM;IACJ,oCAAoC;IACpC,MAAM,iBAAiB,MAAM,qBAAqB,MAAM,UAAU;IAElE,6CAA6C;IAC7C,MAAM,gBAAgB;QACpB,GAAG,KAAK;QACR;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mBAAmB;IAC5C,OAAO;AACT;;;IA5QoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/registry/model-registry.ts"], "sourcesContent": ["/**\r\n * Model Registry\r\n * Central registry for managing all Revo model implementations\r\n */\r\n\r\nimport type {\r\n  RevoModelId,\r\n  ModelStatus,\r\n  IModelImplementation,\r\n  IModelRegistry,\r\n  ModelSelectionCriteria,\r\n  ModelCapabilities\r\n} from '../types/model-types';\r\n\r\nclass ModelRegistry implements IModelRegistry {\r\n  private models = new Map<RevoModelId, IModelImplementation>();\r\n  private initialized = false;\r\n\r\n  /**\r\n   * Register a model implementation\r\n   */\r\n  registerModel(implementation: IModelImplementation): void {\r\n    const modelId = implementation.model.id;\r\n\r\n    if (this.models.has(modelId)) {\r\n    }\r\n\r\n    this.models.set(modelId, implementation);\r\n  }\r\n\r\n  /**\r\n   * Get a specific model implementation\r\n   */\r\n  getModel(id: RevoModelId): IModelImplementation | null {\r\n    return this.models.get(id) || null;\r\n  }\r\n\r\n  /**\r\n   * Get all registered models\r\n   */\r\n  getAllModels(): IModelImplementation[] {\r\n    return Array.from(this.models.values());\r\n  }\r\n\r\n  /**\r\n   * Get only available models (those that pass availability check)\r\n   */\r\n  async getAvailableModels(): Promise<IModelImplementation[]> {\r\n    const allModels = this.getAllModels();\r\n    const availabilityChecks = await Promise.allSettled(\r\n      allModels.map(async (model) => ({\r\n        model,\r\n        available: await model.isAvailable()\r\n      }))\r\n    );\r\n\r\n    return availabilityChecks\r\n      .filter((result): result is PromiseFulfilledResult<{ model: IModelImplementation, available: boolean }> =>\r\n        result.status === 'fulfilled' && result.value.available\r\n      )\r\n      .map(result => result.value.model);\r\n  }\r\n\r\n  /**\r\n   * Get models by status\r\n   */\r\n  getModelsByStatus(status: ModelStatus): IModelImplementation[] {\r\n    return this.getAllModels().filter(impl => impl.model.status === status);\r\n  }\r\n\r\n  /**\r\n   * Get models by capability\r\n   */\r\n  getModelsByCapability(capability: keyof ModelCapabilities): IModelImplementation[] {\r\n    return this.getAllModels().filter(impl => impl.model.capabilities[capability]);\r\n  }\r\n\r\n  /**\r\n   * Find the best model based on selection criteria\r\n   */\r\n  async selectBestModel(criteria: ModelSelectionCriteria): Promise<IModelImplementation | null> {\r\n    const availableModels = await this.getAvailableModels();\r\n\r\n    if (availableModels.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    // If user has a preference, try to use it\r\n    if (criteria.userPreference) {\r\n      const preferredModel = availableModels.find(m => m.model.id === criteria.userPreference);\r\n      if (preferredModel && this.meetsRequirements(preferredModel, criteria)) {\r\n        return preferredModel;\r\n      }\r\n    }\r\n\r\n    // Score models based on criteria\r\n    const scoredModels = availableModels\r\n      .map(model => ({\r\n        model,\r\n        score: this.scoreModel(model, criteria)\r\n      }))\r\n      .filter(({ score }) => score > 0) // Only models that meet minimum requirements\r\n      .sort((a, b) => b.score - a.score); // Sort by score descending\r\n\r\n    return scoredModels.length > 0 ? scoredModels[0].model : null;\r\n  }\r\n\r\n  /**\r\n   * Check if a model meets the minimum requirements\r\n   */\r\n  private meetsRequirements(model: IModelImplementation, criteria: ModelSelectionCriteria): boolean {\r\n    // Check required capabilities\r\n    if (criteria.requiredCapabilities) {\r\n      for (const capability of criteria.requiredCapabilities) {\r\n        if (!model.model.capabilities[capability]) {\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check credit limit\r\n    if (criteria.maxCredits && model.model.pricing.creditsPerGeneration > criteria.maxCredits) {\r\n      return false;\r\n    }\r\n\r\n    // Check platform support\r\n    if (criteria.platform && !model.model.capabilities.supportedPlatforms.includes(criteria.platform)) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Score a model based on selection criteria\r\n   */\r\n  private scoreModel(model: IModelImplementation, criteria: ModelSelectionCriteria): number {\r\n    let score = 0;\r\n\r\n    // Base score for meeting requirements\r\n    if (!this.meetsRequirements(model, criteria)) {\r\n      return 0;\r\n    }\r\n\r\n    score += 50; // Base score for meeting requirements\r\n\r\n    // Quality preference scoring\r\n    if (criteria.qualityPreference) {\r\n      switch (criteria.qualityPreference) {\r\n        case 'quality':\r\n          score += model.model.capabilities.maxQuality * 2;\r\n          break;\r\n        case 'speed':\r\n          // Prefer models with faster processing (lower tier = faster)\r\n          score += model.model.pricing.tier === 'basic' ? 20 :\r\n            model.model.pricing.tier === 'premium' ? 10 : 5;\r\n          break;\r\n        case 'balanced':\r\n          score += model.model.capabilities.maxQuality;\r\n          score += model.model.pricing.tier === 'premium' ? 15 : 10;\r\n          break;\r\n      }\r\n    }\r\n\r\n    // Tier preference scoring\r\n    if (criteria.preferredTier) {\r\n      if (model.model.pricing.tier === criteria.preferredTier) {\r\n        score += 20;\r\n      }\r\n    }\r\n\r\n    // Credit efficiency scoring\r\n    if (criteria.maxCredits) {\r\n      const efficiency = criteria.maxCredits / model.model.pricing.creditsPerGeneration;\r\n      score += Math.min(efficiency * 5, 15); // Cap at 15 points\r\n    }\r\n\r\n    // Status bonus\r\n    switch (model.model.status) {\r\n      case 'stable':\r\n        score += 10;\r\n        break;\r\n      case 'enhanced':\r\n        score += 15;\r\n        break;\r\n      case 'development':\r\n        score += 5;\r\n        break;\r\n      case 'beta':\r\n        score += 3;\r\n        break;\r\n    }\r\n\r\n    return score;\r\n  }\r\n\r\n  /**\r\n   * Get model statistics\r\n   */\r\n  getRegistryStats() {\r\n    const models = this.getAllModels();\r\n    const statusCounts = models.reduce((acc, model) => {\r\n      acc[model.model.status] = (acc[model.model.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<ModelStatus, number>);\r\n\r\n    const tierCounts = models.reduce((acc, model) => {\r\n      acc[model.model.pricing.tier] = (acc[model.model.pricing.tier] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    return {\r\n      totalModels: models.length,\r\n      statusDistribution: statusCounts,\r\n      tierDistribution: tierCounts,\r\n      averageCreditsPerGeneration: models.reduce((sum, m) => sum + m.model.pricing.creditsPerGeneration, 0) / models.length,\r\n      supportedPlatforms: [...new Set(models.flatMap(m => m.model.capabilities.supportedPlatforms))]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Initialize the registry with default models\r\n   */\r\n  async initialize(): Promise<void> {\r\n    if (this.initialized) {\r\n      return;\r\n    }\r\n\r\n\r\n    try {\r\n      // Import and register all model implementations\r\n      const { Revo10Implementation } = await import('../versions/revo-1.0');\r\n\r\n      const { Revo15Implementation } = await import('../versions/revo-1.5');\r\n\r\n      this.registerModel(new Revo10Implementation());\r\n      this.registerModel(new Revo15Implementation());\r\n\r\n      this.initialized = true;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if registry is initialized\r\n   */\r\n  isInitialized(): boolean {\r\n    return this.initialized;\r\n  }\r\n\r\n  /**\r\n   * Reset the registry (mainly for testing)\r\n   */\r\n  reset(): void {\r\n    this.models.clear();\r\n    this.initialized = false;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const modelRegistry = new ModelRegistry();\r\n\r\n// Export class for testing\r\nexport { ModelRegistry };\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAWD,MAAM;IACI,SAAS,IAAI,MAAyC;IACtD,cAAc,MAAM;IAE5B;;GAEC,GACD,cAAc,cAAoC,EAAQ;QACxD,MAAM,UAAU,eAAe,KAAK,CAAC,EAAE;QAEvC,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAC9B;QAEA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,SAAS,EAAe,EAA+B;QACrD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;IAChC;IAEA;;GAEC,GACD,eAAuC;QACrC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA;;GAEC,GACD,MAAM,qBAAsD;QAC1D,MAAM,YAAY,IAAI,CAAC,YAAY;QACnC,MAAM,qBAAqB,MAAM,QAAQ,UAAU,CACjD,UAAU,GAAG,CAAC,OAAO,QAAU,CAAC;gBAC9B;gBACA,WAAW,MAAM,MAAM,WAAW;YACpC,CAAC;QAGH,OAAO,mBACJ,MAAM,CAAC,CAAC,SACP,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,CAAC,SAAS,EAExD,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK,CAAC,KAAK;IACrC;IAEA;;GAEC,GACD,kBAAkB,MAAmB,EAA0B;QAC7D,OAAO,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,MAAM,KAAK;IAClE;IAEA;;GAEC,GACD,sBAAsB,UAAmC,EAA0B;QACjF,OAAO,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,YAAY,CAAC,WAAW;IAC/E;IAEA;;GAEC,GACD,MAAM,gBAAgB,QAAgC,EAAwC;QAC5F,MAAM,kBAAkB,MAAM,IAAI,CAAC,kBAAkB;QAErD,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO;QACT;QAEA,0CAA0C;QAC1C,IAAI,SAAS,cAAc,EAAE;YAC3B,MAAM,iBAAiB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,EAAE,KAAK,SAAS,cAAc;YACvF,IAAI,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,WAAW;gBACtE,OAAO;YACT;QACF;QAEA,iCAAiC;QACjC,MAAM,eAAe,gBAClB,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb;gBACA,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;YAChC,CAAC,GACA,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,QAAQ,GAAG,6CAA6C;SAC9E,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,2BAA2B;QAEjE,OAAO,aAAa,MAAM,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,KAAK,GAAG;IAC3D;IAEA;;GAEC,GACD,AAAQ,kBAAkB,KAA2B,EAAE,QAAgC,EAAW;QAChG,8BAA8B;QAC9B,IAAI,SAAS,oBAAoB,EAAE;YACjC,KAAK,MAAM,cAAc,SAAS,oBAAoB,CAAE;gBACtD,IAAI,CAAC,MAAM,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE;oBACzC,OAAO;gBACT;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,SAAS,UAAU,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,oBAAoB,GAAG,SAAS,UAAU,EAAE;YACzF,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,SAAS,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAAG;YACjG,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,WAAW,KAA2B,EAAE,QAAgC,EAAU;QACxF,IAAI,QAAQ;QAEZ,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,WAAW;YAC5C,OAAO;QACT;QAEA,SAAS,IAAI,sCAAsC;QAEnD,6BAA6B;QAC7B,IAAI,SAAS,iBAAiB,EAAE;YAC9B,OAAQ,SAAS,iBAAiB;gBAChC,KAAK;oBACH,SAAS,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG;oBAC/C;gBACF,KAAK;oBACH,6DAA6D;oBAC7D,SAAS,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,KAC9C,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,KAAK;oBAChD;gBACF,KAAK;oBACH,SAAS,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU;oBAC5C,SAAS,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,KAAK;oBACvD;YACJ;QACF;QAEA,0BAA0B;QAC1B,IAAI,SAAS,aAAa,EAAE;YAC1B,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,aAAa,EAAE;gBACvD,SAAS;YACX;QACF;QAEA,4BAA4B;QAC5B,IAAI,SAAS,UAAU,EAAE;YACvB,MAAM,aAAa,SAAS,UAAU,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,oBAAoB;YACjF,SAAS,KAAK,GAAG,CAAC,aAAa,GAAG,KAAK,mBAAmB;QAC5D;QAEA,eAAe;QACf,OAAQ,MAAM,KAAK,CAAC,MAAM;YACxB,KAAK;gBACH,SAAS;gBACT;YACF,KAAK;gBACH,SAAS;gBACT;YACF,KAAK;gBACH,SAAS;gBACT;YACF,KAAK;gBACH,SAAS;gBACT;QACJ;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB;QACjB,MAAM,SAAS,IAAI,CAAC,YAAY;QAChC,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,KAAK;YACvC,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;YAC3D,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;YACrC,GAAG,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACvE,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO;YACL,aAAa,OAAO,MAAM;YAC1B,oBAAoB;YACpB,kBAAkB;YAClB,6BAA6B,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,oBAAoB,EAAE,KAAK,OAAO,MAAM;YACrH,oBAAoB;mBAAI,IAAI,IAAI,OAAO,OAAO,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,YAAY,CAAC,kBAAkB;aAAG;QAChG;IACF;IAEA;;GAEC,GACD,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB;QACF;QAGA,IAAI;YACF,gDAAgD;YAChD,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,IAAI,CAAC,aAAa,CAAC,IAAI;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI;YAEvB,IAAI,CAAC,WAAW,GAAG;QACrB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAyB;QACvB,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,QAAc;QACZ,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,WAAW,GAAG;IACrB;AACF;AAGO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-video-post.ts"], "sourcesContent": ["// src/ai/flows/generate-video-post.ts\r\n'use server';\r\n\r\n/**\r\n * @fileOverview A Genkit flow for generating a short promotional video for a social media post.\r\n *\r\n * This flow utilizes a text-to-video model to create dynamic content based on brand information,\r\n * local context, and specific post details.\r\n */\r\n\r\nimport {ai} from '@/ai/genkit';\r\nimport {z} from 'genkit';\r\nimport { MediaPart } from 'genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\n\r\n// Define the input schema for the video generation flow.\r\nconst GenerateVideoInputSchema = z.object({\r\n  businessType: z.string().describe('The type of business (e.g., restaurant, salon).'),\r\n  location: z.string().describe('The location of the business (city, state).'),\r\n  visualStyle: z.string().describe('The visual style of the brand (e.g., modern, vintage).'),\r\n  imageText: z.string().describe('A brief, catchy headline for the video.'),\r\n  postContent: z.string().describe('The full text content of the social media post for additional context.'),\r\n});\r\nexport type GenerateVideoInput = z.infer<typeof GenerateVideoInputSchema>;\r\n\r\n// Define the output schema for the video generation flow.\r\nconst GenerateVideoOutputSchema = z.object({\r\n  videoUrl: z.string().describe('The data URI of the generated video file.'),\r\n});\r\nexport type GenerateVideoOutput = z.infer<typeof GenerateVideoOutputSchema>;\r\n\r\n/**\r\n * An exported wrapper function that calls the video generation flow.\r\n * @param input - The input data for video generation.\r\n * @returns A promise that resolves to the generated video URL.\r\n */\r\nexport async function generateVideoPost(input: GenerateVideoInput): Promise<GenerateVideoOutput> {\r\n  return generateVideoPostFlow(input);\r\n}\r\n\r\n/**\r\n * Helper function to download video and convert to data URI\r\n */\r\nasync function videoToDataURI(videoPart: MediaPart): Promise<string> {\r\n    if (!videoPart.media || !videoPart.media.url) {\r\n        throw new Error('Media URL not found in video part.');\r\n    }\r\n\r\n    const fetch = (await import('node-fetch')).default;\r\n    // Add API key before fetching the video.\r\n    const videoDownloadResponse = await fetch(\r\n        `${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`\r\n    );\r\n\r\n    if (!videoDownloadResponse.ok) {\r\n        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);\r\n    }\r\n\r\n    const videoBuffer = await videoDownloadResponse.arrayBuffer();\r\n    const base64Video = Buffer.from(videoBuffer).toString('base64');\r\n    // Default to video/mp4 if contentType is not provided\r\n    const contentType = videoPart.media.contentType || 'video/mp4';\r\n\r\n    return `data:${contentType};base64,${base64Video}`;\r\n}\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n    for (let i = 0; i < retries; i++) {\r\n        try {\r\n            const result = await ai.generate(request);\r\n            return result;\r\n        } catch (e: any) {\r\n            if (e.message && e.message.includes('503') && i < retries - 1) {\r\n                await new Promise(resolve => setTimeout(resolve, delay));\r\n            } else {\r\n                if (e.message && e.message.includes('503')) {\r\n                    throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n                }\r\n                throw e; // Rethrow other errors immediately\r\n            }\r\n        }\r\n    }\r\n    // This line should not be reachable if retries are configured, but as a fallback:\r\n    throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\n/**\r\n * The core Genkit flow for generating a video post.\r\n */\r\nconst generateVideoPostFlow = ai.defineFlow(\r\n  {\r\n    name: 'generateVideoPostFlow',\r\n    inputSchema: GenerateVideoInputSchema,\r\n    outputSchema: GenerateVideoOutputSchema,\r\n  },\r\n  async (input) => {\r\n    const videoPrompt = `Create a short, engaging promotional video with sound for a ${input.businessType} in ${input.location}.\r\nThe visual style should be ${input.visualStyle}.\r\nThe video should be visually appealing and suitable for a social media post.\r\n\r\nThe main headline for the video is: \"${input.imageText}\".\r\n\r\nFor additional context, here is the full post content that will accompany the video: \"${input.postContent}\".\r\n\r\nGenerate a video that is cinematically interesting, has relevant sound, and captures the essence of the post content.`;\r\n\r\n    try {\r\n      const result = await generateWithRetry({\r\n        model: 'googleai/veo-3.0-generate-preview',\r\n        prompt: videoPrompt,\r\n      });\r\n      let operation = result.operation;\r\n\r\n      if (!operation) {\r\n          throw new Error('Expected the model to return an operation');\r\n      }\r\n\r\n      // Poll for completion\r\n      while (!operation.done) {\r\n          await new Promise(resolve => setTimeout(resolve, 5000)); // wait 5s\r\n          operation = await ai.checkOperation(operation);\r\n      }\r\n\r\n      if (operation.error) {\r\n          throw new Error(`Video generation failed. Please try again. Error: ${operation.error.message}`);\r\n      }\r\n\r\n      // Relaxed check for the video part\r\n      const videoPart = operation.output?.message?.content.find(p => !!p.media);\r\n      \r\n      if (!videoPart || !videoPart.media) {\r\n          throw new Error('No video was generated in the operation result.');\r\n      }\r\n      \r\n      const videoDataUrl = await videoToDataURI(videoPart);\r\n\r\n      return {\r\n        videoUrl: videoDataUrl, \r\n      };\r\n    } catch (e: any) {\r\n        throw new Error(e.message || \"Video generation failed. The model may be overloaded. Please try again in a few moments.\");\r\n    }\r\n  }\r\n);\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;AAGtC;;;;;CAKC,GAED;AACA;AAAA;;;;;;AAIA,yDAAyD;AACzD,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACnC;AAGA,0DAA0D;AAC1D,MAAM,4BAA4B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAChC;AAQO,eAAe,kBAAkB,KAAyB;IAC/D,OAAO,sBAAsB;AAC/B;AAEA;;CAEC,GACD,eAAe,eAAe,SAAoB;IAC9C,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG,EAAE;QAC1C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,QAAQ,CAAC,6IAA0B,EAAE,OAAO;IAClD,yCAAyC;IACzC,MAAM,wBAAwB,MAAM,MAChC,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;IAG9D,IAAI,CAAC,sBAAsB,EAAE,EAAE;QAC3B,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,sBAAsB,UAAU,EAAE;IACnF;IAEA,MAAM,cAAc,MAAM,sBAAsB,WAAW;IAC3D,MAAM,cAAc,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;IACtD,sDAAsD;IACtD,MAAM,cAAc,UAAU,KAAK,CAAC,WAAW,IAAI;IAEnD,OAAO,CAAC,KAAK,EAAE,YAAY,QAAQ,EAAE,aAAa;AACtD;AAEA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAChF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAC9B,IAAI;YACA,MAAM,SAAS,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACX,EAAE,OAAO,GAAQ;YACb,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACrD,OAAO;gBACH,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACxC,MAAM,IAAI,MAAM;gBACpB;gBACA,MAAM,GAAG,mCAAmC;YAChD;QACJ;IACJ;IACA,kFAAkF;IAClF,MAAM,IAAI,MAAM;AACpB;AAEA;;CAEC,GACD,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,MAAM,cAAc,CAAC,4DAA4D,EAAE,MAAM,YAAY,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC;2BACpG,EAAE,MAAM,WAAW,CAAC;;;qCAGV,EAAE,MAAM,SAAS,CAAC;;sFAE+B,EAAE,MAAM,WAAW,CAAC;;qHAEW,CAAC;IAElH,IAAI;QACF,MAAM,SAAS,MAAM,kBAAkB;YACrC,OAAO;YACP,QAAQ;QACV;QACA,IAAI,YAAY,OAAO,SAAS;QAEhC,IAAI,CAAC,WAAW;YACZ,MAAM,IAAI,MAAM;QACpB;QAEA,sBAAsB;QACtB,MAAO,CAAC,UAAU,IAAI,CAAE;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,UAAU;YACnE,YAAY,MAAM,mHAAA,CAAA,KAAE,CAAC,cAAc,CAAC;QACxC;QAEA,IAAI,UAAU,KAAK,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,kDAAkD,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAClG;QAEA,mCAAmC;QACnC,MAAM,YAAY,UAAU,MAAM,EAAE,SAAS,QAAQ,KAAK,CAAA,IAAK,CAAC,CAAC,EAAE,KAAK;QAExE,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,EAAE;YAChC,MAAM,IAAI,MAAM;QACpB;QAEA,MAAM,eAAe,MAAM,eAAe;QAE1C,OAAO;YACL,UAAU;QACZ;IACF,EAAE,OAAO,GAAQ;QACb,MAAM,IAAI,MAAM,EAAE,OAAO,IAAI;IACjC;AACF;;;IA7GoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/advanced-design-prompts.ts"], "sourcesContent": ["/**\r\n * Advanced Design Generation Prompts\r\n * \r\n * Professional-grade prompts incorporating design principles, composition rules,\r\n * typography best practices, color theory, and modern design trends.\r\n */\r\n\r\nexport const ADVANCED_DESIGN_PRINCIPLES = `\r\n**COMPOSITION & VISUAL HIERARCHY:**\r\n- Apply the Rule of Thirds: Position key elements along the grid lines or intersections\r\n- Create clear visual hierarchy using size, contrast, and positioning\r\n- Establish a strong focal point that draws the eye immediately\r\n- Use negative space strategically to create breathing room and emphasis\r\n- Balance elements using symmetrical or asymmetrical composition\r\n- Guide the viewer's eye through the design with leading lines and flow\r\n\r\n**TYPOGRAPHY EXCELLENCE:**\r\n- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)\r\n- Use maximum 2-3 font families with strong contrast between them\r\n- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)\r\n- Apply proper letter spacing, line height, and text alignment\r\n- Scale typography appropriately for the platform and viewing distance\r\n- Use typography as a design element, not just information delivery\r\n\r\n**COLOR THEORY & HARMONY:**\r\n- Apply color psychology appropriate to the business type and message\r\n- Use complementary colors for high contrast and attention\r\n- Apply analogous colors for harmony and cohesion\r\n- Implement triadic color schemes for vibrant, balanced designs\r\n- Ensure sufficient contrast between text and background\r\n- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent\r\n\r\n**MODERN DESIGN TRENDS:**\r\n- Embrace minimalism with purposeful use of white space\r\n- Use bold, geometric shapes and clean lines\r\n- Apply subtle gradients and depth effects when appropriate\r\n- Incorporate authentic, diverse photography when using people\r\n- Use consistent border radius and spacing throughout\r\n- Apply subtle shadows and depth for modern dimensionality\r\n`;\r\n\r\nexport const PLATFORM_SPECIFIC_GUIDELINES = {\r\n  instagram: `\r\n**INSTAGRAM OPTIMIZATION:**\r\n- Design for mobile-first viewing with bold, clear elements\r\n- Use high contrast colors that pop on small screens\r\n- Keep text large and readable (minimum 24px equivalent)\r\n- Center important elements for square crop compatibility\r\n- Use Instagram's native color palette trends\r\n- Design for both feed and story formats\r\n- Optimize for thumb-stopping power in fast scroll feeds\r\n- Logo placement: Bottom right corner or integrated naturally into design\r\n- Ensure logo is visible but doesn't overwhelm the main content\r\n`,\r\n\r\n  facebook: `\r\n**FACEBOOK OPTIMIZATION:**\r\n- Design for both desktop and mobile viewing\r\n- Use Facebook blue (#1877F2) strategically for CTAs\r\n- Optimize for news feed algorithm preferences\r\n- Include clear value proposition in visual hierarchy\r\n- Design for engagement and shareability\r\n- Use authentic, relatable imagery\r\n- Optimize for both organic and paid placement\r\n- Logo placement: Top left or bottom right corner for brand recognition\r\n- Ensure logo works well in both desktop and mobile formats\r\n`,\r\n\r\n  twitter: `\r\n**TWITTER/X OPTIMIZATION:**\r\n- Design for rapid consumption and high engagement\r\n- Use bold, contrasting colors that stand out in timeline\r\n- Keep text minimal and impactful\r\n- Design for retweet and quote tweet functionality\r\n- Use trending visual styles and memes appropriately\r\n- Optimize for both light and dark mode viewing\r\n- Create thumb-stopping visuals for fast-scrolling feeds\r\n- Logo placement: Small, subtle placement that doesn't interfere with content\r\n- Ensure logo is readable in both light and dark modes\r\n`,\r\n\r\n  linkedin: `\r\n**LINKEDIN OPTIMIZATION:**\r\n- Use professional, business-appropriate color schemes\r\n- Apply corporate design standards and clean aesthetics\r\n- Include clear value proposition for business audience\r\n- Use professional photography and imagery\r\n- Design for thought leadership and expertise positioning\r\n- Apply subtle, sophisticated design elements\r\n- Optimize for professional networking context\r\n- Logo placement: Prominent placement for brand authority and recognition\r\n- Ensure logo conveys professionalism and trustworthiness\r\n`\r\n};\r\n\r\nexport const BUSINESS_TYPE_DESIGN_DNA = {\r\n  restaurant: `\r\n**RESTAURANT DESIGN DNA:**\r\n- Use warm, appetizing colors (reds, oranges, warm yellows)\r\n- Include high-quality food photography with proper lighting\r\n- Apply rustic or modern clean aesthetics based on restaurant type\r\n- Use food-focused typography (script for upscale, bold sans for casual)\r\n- Include appetite-triggering visual elements\r\n- Apply golden hour lighting effects for food imagery\r\n- Use complementary colors that enhance food appeal\r\n- Show diverse people enjoying meals in authentic, social settings\r\n- Include cultural food elements that reflect local cuisine traditions\r\n- Display chefs, staff, and customers from the local community\r\n- Use table settings and dining environments that feel culturally authentic\r\n`,\r\n\r\n  fitness: `\r\n**FITNESS DESIGN DNA:**\r\n- Use energetic, motivational color schemes (bright blues, oranges, greens)\r\n- Include dynamic action shots and movement\r\n- Apply bold, strong typography with impact\r\n- Use high-contrast designs for motivation and energy\r\n- Include progress and achievement visual metaphors\r\n- Apply athletic and performance-focused imagery\r\n- Use inspiring and empowering visual language\r\n- Show diverse athletes and fitness enthusiasts in action\r\n- Include people of different body types, ages, and fitness levels\r\n- Display authentic workout environments and community settings\r\n- Use culturally relevant sports and fitness activities for the region\r\n`,\r\n\r\n  beauty: `\r\n**BEAUTY DESIGN DNA:**\r\n- Use sophisticated, elegant color palettes (pastels, metallics)\r\n- Include high-quality beauty photography with perfect lighting\r\n- Apply clean, minimalist aesthetics with luxury touches\r\n- Use elegant, refined typography\r\n- Include aspirational and transformational imagery\r\n- Apply soft, flattering lighting effects\r\n- Use premium and luxurious visual elements\r\n- Show diverse models representing different skin tones, ages, and beauty standards\r\n- Include authentic beauty routines and self-care moments\r\n- Display culturally relevant beauty practices and aesthetics\r\n- Use inclusive representation that celebrates natural beauty diversity\r\n`,\r\n\r\n  tech: `\r\n**TECH DESIGN DNA (CANVA-QUALITY):**\r\n- Use sophisticated, professional color schemes (modern blues, elegant grays, clean whites)\r\n- Include polished, well-designed layouts with strategic geometric elements and refined shapes\r\n- Apply professional business visual metaphors with premium stock photography quality\r\n- Use modern, bold typography with clear hierarchy (multiple font weights and sizes)\r\n- Include high-quality business imagery: professional office spaces, authentic workplace scenarios\r\n- Apply elegant design effects: subtle gradients, refined shadows, tasteful borders\r\n- Use trustworthy and sophisticated visual language that matches premium Canva templates\r\n- Show diverse tech professionals in polished, well-lit business environments\r\n- Include people using technology in professional, aspirational business contexts\r\n- Display modern office spaces, premium remote work setups, and sophisticated business environments\r\n- Use strategic design elements: elegant shapes, professional patterns, refined layouts\r\n- Create designs that look intentionally crafted and professionally designed\r\n- FOCUS: Premium stock photography quality, sophisticated layouts, Canva-level polish\r\n`,\r\n\r\n  ecommerce: `\r\n**E-COMMERCE DESIGN DNA:**\r\n- Use conversion-focused color schemes (trust blues, urgency reds, success greens)\r\n- Include high-quality product photography with lifestyle context\r\n- Apply clean, scannable layouts with clear hierarchy\r\n- Use action-oriented typography and compelling CTAs\r\n- Include social proof and trust signals\r\n- Apply mobile-first responsive design principles\r\n- Use persuasive and benefit-focused visual language\r\n- Show diverse customers using products in real-life situations\r\n- Include authentic unboxing and product experience moments\r\n- Display culturally relevant usage scenarios and lifestyle contexts\r\n`,\r\n\r\n  healthcare: `\r\n**HEALTHCARE DESIGN DNA:**\r\n- Use calming, trustworthy color palettes (soft blues, greens, whites)\r\n- Include professional medical imagery with human warmth\r\n- Apply clean, accessible design with clear information hierarchy\r\n- Use readable, professional typography\r\n- Include caring and compassionate visual elements\r\n- Apply medical accuracy with approachable aesthetics\r\n- Use reassuring and professional visual language\r\n- Show diverse healthcare professionals and patients\r\n- Include authentic care moments and medical environments\r\n- Display culturally sensitive healthcare interactions and settings\r\n`,\r\n\r\n  education: `\r\n**EDUCATION DESIGN DNA:**\r\n- Use inspiring, growth-focused color schemes (blues, greens, warm oranges)\r\n- Include diverse learning environments and educational moments\r\n- Apply organized, structured layouts with clear learning paths\r\n- Use friendly, accessible typography\r\n- Include knowledge and achievement visual metaphors\r\n- Apply bright, optimistic design elements\r\n- Use encouraging and empowering visual language\r\n- Show students and educators from diverse backgrounds\r\n- Include authentic classroom and learning environments\r\n- Display culturally relevant educational practices and settings\r\n`,\r\n\r\n  default: `\r\n**UNIVERSAL DESIGN DNA:**\r\n- Use brand-appropriate color psychology\r\n- Include authentic, high-quality imagery\r\n- Apply clean, professional aesthetics\r\n- Use readable, accessible typography\r\n- Include relevant industry visual metaphors\r\n- Apply consistent brand visual language\r\n- Use trustworthy and professional design elements\r\n- Show diverse people in authentic, relevant contexts\r\n- Include culturally appropriate imagery and design elements\r\n- Display real human connections and authentic moments\r\n`\r\n};\r\n\r\nexport const QUALITY_ENHANCEMENT_INSTRUCTIONS = `\r\n**DESIGN QUALITY STANDARDS:**\r\n- Ensure all text is perfectly readable with sufficient contrast\r\n- Apply consistent spacing and alignment throughout\r\n- Use high-resolution imagery without pixelation or artifacts\r\n- Maintain visual balance and proper proportions\r\n- Ensure brand elements are prominently but naturally integrated\r\n- Apply professional color grading and visual polish\r\n- Create designs that work across different screen sizes\r\n- Ensure accessibility compliance for color contrast and readability\r\n\r\n**TECHNICAL EXCELLENCE:**\r\n- Generate crisp, high-resolution images suitable for social media\r\n- Apply proper aspect ratios for platform requirements\r\n- Ensure text overlay is perfectly positioned and readable\r\n- Use consistent visual style throughout the design\r\n- Apply professional lighting and shadow effects\r\n- Ensure logo integration feels natural and branded\r\n- Create designs that maintain quality when compressed for social media\r\n`;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AAEM,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC3C,CAAC;AAEM,MAAM,+BAA+B;IAC1C,WAAW,CAAC;;;;;;;;;;;AAWd,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;AAWZ,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;AACD;AAEO,MAAM,2BAA2B;IACtC,YAAY,CAAC;;;;;;;;;;;;;AAaf,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;;AAaZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;;;;;AAaX,CAAC;IAEC,MAAM,CAAC;;;;;;;;;;;;;;;AAeT,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,YAAY,CAAC;;;;;;;;;;;;AAYf,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;AAYZ,CAAC;AACD;AAEO,MAAM,mCAAmC,CAAC;;;;;;;;;;;;;;;;;;;AAmBjD,CAAC", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-analysis.ts"], "sourcesContent": ["/**\r\n * Design Analysis Utilities\r\n * \r\n * Intelligent analysis and processing of design examples for better AI generation\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design analysis results\r\nexport const DesignAnalysisSchema = z.object({\r\n  colorPalette: z.object({\r\n    primary: z.string().describe('Primary color in hex format'),\r\n    secondary: z.string().describe('Secondary color in hex format'),\r\n    accent: z.string().describe('Accent color in hex format'),\r\n    colorHarmony: z.enum(['complementary', 'analogous', 'triadic', 'monochromatic', 'split-complementary']).describe('Type of color harmony used'),\r\n    colorMood: z.string().describe('Overall mood conveyed by the color scheme')\r\n  }),\r\n  composition: z.object({\r\n    layout: z.enum(['centered', 'left-aligned', 'right-aligned', 'asymmetrical', 'grid-based']).describe('Primary layout structure'),\r\n    visualHierarchy: z.string().describe('How visual hierarchy is established'),\r\n    focalPoint: z.string().describe('Primary focal point and how it\\'s created'),\r\n    balance: z.enum(['symmetrical', 'asymmetrical', 'radial']).describe('Type of visual balance'),\r\n    whitespace: z.enum(['minimal', 'moderate', 'generous']).describe('Use of negative space')\r\n  }),\r\n  typography: z.object({\r\n    primaryFont: z.string().describe('Primary font style/category'),\r\n    hierarchy: z.string().describe('Typographic hierarchy structure'),\r\n    textTreatment: z.string().describe('Special text treatments or effects'),\r\n    readability: z.enum(['high', 'medium', 'stylized']).describe('Text readability level')\r\n  }),\r\n  style: z.object({\r\n    aesthetic: z.string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),\r\n    mood: z.string().describe('Emotional mood and feeling'),\r\n    sophistication: z.enum(['casual', 'professional', 'luxury', 'playful']).describe('Level of sophistication'),\r\n    trends: z.array(z.string()).describe('Current design trends incorporated')\r\n  }),\r\n  effectiveness: z.object({\r\n    attention: z.number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),\r\n    clarity: z.number().min(1).max(10).describe('Message clarity (1-10)'),\r\n    brandAlignment: z.number().min(1).max(10).describe('Brand alignment strength (1-10)'),\r\n    platformOptimization: z.number().min(1).max(10).describe('Platform optimization (1-10)')\r\n  })\r\n});\r\n\r\nexport type DesignAnalysis = z.infer<typeof DesignAnalysisSchema>;\r\n\r\n// Design analysis prompt\r\nconst designAnalysisPrompt = ai.definePrompt({\r\n  name: 'analyzeDesignExample',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string().optional(),\r\n      designContext: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignAnalysisSchema\r\n  },\r\n  prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.\r\n\r\nAnalyze the provided design image and extract detailed insights about its design elements and effectiveness.\r\n\r\nBusiness Context: {{businessType}}\r\nPlatform: {{platform}}\r\nContext: {{designContext}}\r\n\r\nProvide a comprehensive analysis covering:\r\n\r\n1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact\r\n2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space\r\n3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment\r\n4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation\r\n5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization\r\n\r\nBe specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`\r\n});\r\n\r\n/**\r\n * Analyzes a design example to extract key design elements and patterns\r\n */\r\nexport async function analyzeDesignExample(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform?: string,\r\n  context?: string\r\n): Promise<DesignAnalysis> {\r\n  try {\r\n    // For now, return a mock analysis to avoid API issues\r\n    // This can be replaced with actual AI analysis once the prompt system is stable\r\n    return {\r\n      colorPalette: {\r\n        primary: '#FF6B6B',\r\n        secondary: '#4ECDC4',\r\n        accent: '#45B7D1',\r\n        colorHarmony: 'complementary',\r\n        colorMood: 'Energetic and modern'\r\n      },\r\n      composition: {\r\n        layout: 'centered',\r\n        visualHierarchy: 'Clear size-based hierarchy with strong focal point',\r\n        focalPoint: 'Central logo and headline combination',\r\n        balance: 'symmetrical',\r\n        whitespace: 'moderate'\r\n      },\r\n      typography: {\r\n        primaryFont: 'Modern sans-serif',\r\n        hierarchy: 'Large headline, medium subtext, small details',\r\n        textTreatment: 'Bold headlines with subtle shadows',\r\n        readability: 'high'\r\n      },\r\n      style: {\r\n        aesthetic: 'Modern minimalist',\r\n        mood: 'Professional and approachable',\r\n        sophistication: 'professional',\r\n        trends: ['Bold typography', 'Minimalist design', 'High contrast']\r\n      },\r\n      effectiveness: {\r\n        attention: 8,\r\n        clarity: 9,\r\n        brandAlignment: 8,\r\n        platformOptimization: 7\r\n      }\r\n    };\r\n  } catch (error) {\r\n    throw new Error('Failed to analyze design example');\r\n  }\r\n}\r\n\r\n/**\r\n * Selects the best design examples based on content type and platform\r\n */\r\nexport function selectOptimalDesignExamples(\r\n  designExamples: string[],\r\n  analyses: DesignAnalysis[],\r\n  contentType: string,\r\n  platform: string,\r\n  maxExamples: number = 3\r\n): string[] {\r\n  if (!analyses.length || !designExamples.length) {\r\n    return designExamples.slice(0, maxExamples);\r\n  }\r\n\r\n  // Score each design based on relevance and effectiveness\r\n  const scoredExamples = designExamples.map((example, index) => {\r\n    const analysis = analyses[index];\r\n    if (!analysis) return { example, score: 0 };\r\n\r\n    let score = 0;\r\n\r\n    // Weight effectiveness metrics\r\n    score += analysis.effectiveness.attention * 0.3;\r\n    score += analysis.effectiveness.clarity * 0.25;\r\n    score += analysis.effectiveness.brandAlignment * 0.25;\r\n    score += analysis.effectiveness.platformOptimization * 0.2;\r\n\r\n    // Bonus for sophisticated designs\r\n    if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {\r\n      score += 1;\r\n    }\r\n\r\n    // Bonus for modern trends\r\n    score += analysis.style.trends.length * 0.5;\r\n\r\n    return { example, score, analysis };\r\n  });\r\n\r\n  // Sort by score and return top examples\r\n  return scoredExamples\r\n    .sort((a, b) => b.score - a.score)\r\n    .slice(0, maxExamples)\r\n    .map(item => item.example);\r\n}\r\n\r\n/**\r\n * Generates design DNA from analyzed examples\r\n */\r\nexport function extractDesignDNA(analyses: DesignAnalysis[]): string {\r\n  if (!analyses.length) return '';\r\n\r\n  const commonElements = {\r\n    colors: analyses.map(a => a.colorPalette.colorHarmony),\r\n    layouts: analyses.map(a => a.composition.layout),\r\n    aesthetics: analyses.map(a => a.style.aesthetic),\r\n    moods: analyses.map(a => a.style.mood)\r\n  };\r\n\r\n  // Find most common elements\r\n  const mostCommonColor = getMostCommon(commonElements.colors);\r\n  const mostCommonLayout = getMostCommon(commonElements.layouts);\r\n  const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);\r\n  const mostCommonMood = getMostCommon(commonElements.moods);\r\n\r\n  return `\r\n**EXTRACTED DESIGN DNA:**\r\n- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes\r\n- **Layout Pattern**: Favors ${mostCommonLayout} compositions\r\n- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach\r\n- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout\r\n- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation\r\n- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure\r\n`;\r\n}\r\n\r\n/**\r\n * Helper function to find most common element in array\r\n */\r\nfunction getMostCommon<T>(arr: T[]): T {\r\n  const counts = arr.reduce((acc, item) => {\r\n    acc[item as string] = (acc[item as string] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n\r\n  return Object.entries(counts).reduce((a, b) => counts[a[0]] > counts[b[0]] ? a : b)[0] as T;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AACA;;;AAGO,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,cAAc,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAiB;YAAa;YAAW;YAAiB;SAAsB,EAAE,QAAQ,CAAC;QACjH,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;IACA,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,QAAQ,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAY;YAAgB;YAAiB;YAAgB;SAAa,EAAE,QAAQ,CAAC;QACrG,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACrC,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,SAAS,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAgB;SAAS,EAAE,QAAQ,CAAC;QACpE,YAAY,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAW;YAAY;SAAW,EAAE,QAAQ,CAAC;IACnE;IACA,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACjC,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACnC,aAAa,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAW,EAAE,QAAQ,CAAC;IAC/D;IACA,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,gBAAgB,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAgB;YAAU;SAAU,EAAE,QAAQ,CAAC;QACjF,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACvC;IACA,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC9C,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC5C,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QACnD,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;IAC3D;AACF;AAIA,yBAAyB;AACzB,MAAM,uBAAuB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC3C,MAAM;IACN,OAAO;QACL,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;iHAgBsG,CAAC;AAClH;AAKO,eAAe,qBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAiB,EACjB,OAAgB;IAEhB,IAAI;QACF,sDAAsD;QACtD,gFAAgF;QAChF,OAAO;YACL,cAAc;gBACZ,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,cAAc;gBACd,WAAW;YACb;YACA,aAAa;gBACX,QAAQ;gBACR,iBAAiB;gBACjB,YAAY;gBACZ,SAAS;gBACT,YAAY;YACd;YACA,YAAY;gBACV,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,aAAa;YACf;YACA,OAAO;gBACL,WAAW;gBACX,MAAM;gBACN,gBAAgB;gBAChB,QAAQ;oBAAC;oBAAmB;oBAAqB;iBAAgB;YACnE;YACA,eAAe;gBACb,WAAW;gBACX,SAAS;gBACT,gBAAgB;gBAChB,sBAAsB;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,4BACd,cAAwB,EACxB,QAA0B,EAC1B,WAAmB,EACnB,QAAgB,EAChB,cAAsB,CAAC;IAEvB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,eAAe,MAAM,EAAE;QAC9C,OAAO,eAAe,KAAK,CAAC,GAAG;IACjC;IAEA,yDAAyD;IACzD,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAC,SAAS;QAClD,MAAM,WAAW,QAAQ,CAAC,MAAM;QAChC,IAAI,CAAC,UAAU,OAAO;YAAE;YAAS,OAAO;QAAE;QAE1C,IAAI,QAAQ;QAEZ,+BAA+B;QAC/B,SAAS,SAAS,aAAa,CAAC,SAAS,GAAG;QAC5C,SAAS,SAAS,aAAa,CAAC,OAAO,GAAG;QAC1C,SAAS,SAAS,aAAa,CAAC,cAAc,GAAG;QACjD,SAAS,SAAS,aAAa,CAAC,oBAAoB,GAAG;QAEvD,kCAAkC;QAClC,IAAI,SAAS,KAAK,CAAC,cAAc,KAAK,kBAAkB,SAAS,KAAK,CAAC,cAAc,KAAK,UAAU;YAClG,SAAS;QACX;QAEA,0BAA0B;QAC1B,SAAS,SAAS,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG;QAExC,OAAO;YAAE;YAAS;YAAO;QAAS;IACpC;IAEA,wCAAwC;IACxC,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;AAC7B;AAKO,SAAS,iBAAiB,QAA0B;IACzD,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO;IAE7B,MAAM,iBAAiB;QACrB,QAAQ,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,YAAY;QACrD,SAAS,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,CAAC,MAAM;QAC/C,YAAY,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,SAAS;QAC/C,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,IAAI;IACvC;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,cAAc,eAAe,MAAM;IAC3D,MAAM,mBAAmB,cAAc,eAAe,OAAO;IAC7D,MAAM,sBAAsB,cAAc,eAAe,UAAU;IACnE,MAAM,iBAAiB,cAAc,eAAe,KAAK;IAEzD,OAAO,CAAC;;oCAE0B,EAAE,gBAAgB;6BACzB,EAAE,iBAAiB;kCACd,EAAE,oBAAoB;gCACxB,EAAE,eAAe;6BACpB,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,eAAe;2BACtC,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,UAAU;AAC/D,CAAC;AACD;AAEA;;CAEC,GACD,SAAS,cAAiB,GAAQ;IAChC,MAAM,SAAS,IAAI,MAAM,CAAC,CAAC,KAAK;QAC9B,GAAG,CAAC,KAAe,GAAG,CAAC,GAAG,CAAC,KAAe,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;AACxF", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-quality.ts"], "sourcesContent": ["/**\r\n * Design Quality Validation and Enhancement\r\n * \r\n * System for validating, scoring, and iteratively improving generated designs\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design quality assessment\r\nexport const DesignQualitySchema = z.object({\r\n  overall: z.object({\r\n    score: z.number().min(1).max(10).describe('Overall design quality score (1-10)'),\r\n    grade: z.enum(['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F']).describe('Letter grade for design quality'),\r\n    summary: z.string().describe('Brief summary of design strengths and weaknesses')\r\n  }),\r\n  composition: z.object({\r\n    score: z.number().min(1).max(10).describe('Composition and layout quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on composition'),\r\n    improvements: z.array(z.string()).describe('Suggested composition improvements')\r\n  }),\r\n  typography: z.object({\r\n    score: z.number().min(1).max(10).describe('Typography quality and readability (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on typography'),\r\n    improvements: z.array(z.string()).describe('Suggested typography improvements')\r\n  }),\r\n  colorDesign: z.object({\r\n    score: z.number().min(1).max(10).describe('Color usage and harmony (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on color choices'),\r\n    improvements: z.array(z.string()).describe('Suggested color improvements')\r\n  }),\r\n  brandAlignment: z.object({\r\n    score: z.number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on brand alignment'),\r\n    improvements: z.array(z.string()).describe('Suggested brand alignment improvements')\r\n  }),\r\n  platformOptimization: z.object({\r\n    score: z.number().min(1).max(10).describe('Platform-specific optimization (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on platform optimization'),\r\n    improvements: z.array(z.string()).describe('Suggested platform optimization improvements')\r\n  }),\r\n  technicalQuality: z.object({\r\n    score: z.number().min(1).max(10).describe('Technical execution quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on technical aspects'),\r\n    improvements: z.array(z.string()).describe('Suggested technical improvements')\r\n  }),\r\n  recommendedActions: z.array(z.object({\r\n    priority: z.enum(['high', 'medium', 'low']).describe('Priority level of the action'),\r\n    action: z.string().describe('Specific action to take'),\r\n    expectedImpact: z.string().describe('Expected impact of the action')\r\n  })).describe('Prioritized list of recommended improvements')\r\n});\r\n\r\nexport type DesignQuality = z.infer<typeof DesignQualitySchema>;\r\n\r\n// Design quality assessment prompt\r\nconst designQualityPrompt = ai.definePrompt({\r\n  name: 'assessDesignQuality',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string(),\r\n      visualStyle: z.string(),\r\n      brandColors: z.string().optional(),\r\n      designGoals: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignQualitySchema\r\n  },\r\n  prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.\r\n\r\nEvaluate the provided design image with the highest professional standards.\r\n\r\n**Context:**\r\n- Business Type: {{businessType}}\r\n- Platform: {{platform}}\r\n- Visual Style Goal: {{visualStyle}}\r\n- Brand Colors: {{brandColors}}\r\n- Design Goals: {{designGoals}}\r\n\r\n**Assessment Criteria:**\r\n\r\n1. **Composition & Layout** (25%):\r\n   - Visual hierarchy and flow\r\n   - Balance and proportion\r\n   - Use of negative space\r\n   - Rule of thirds application\r\n   - Focal point effectiveness\r\n\r\n2. **Typography** (20%):\r\n   - Readability and legibility\r\n   - Hierarchy and contrast\r\n   - Font choice appropriateness\r\n   - Text positioning and spacing\r\n   - Accessibility compliance\r\n\r\n3. **Color Design** (20%):\r\n   - Color harmony and theory\r\n   - Brand color integration\r\n   - Contrast and accessibility\r\n   - Psychological impact\r\n   - Platform appropriateness\r\n\r\n4. **Brand Alignment** (15%):\r\n   - Brand consistency\r\n   - Logo integration\r\n   - Visual style adherence\r\n   - Brand personality expression\r\n   - Professional presentation\r\n\r\n5. **Platform Optimization** (10%):\r\n   - Platform-specific best practices\r\n   - Mobile optimization\r\n   - Engagement potential\r\n   - Algorithm friendliness\r\n   - Format appropriateness\r\n\r\n6. **Technical Quality** (10%):\r\n   - Image resolution and clarity\r\n   - Professional finish\r\n   - Technical execution\r\n   - Scalability\r\n   - Print/digital readiness\r\n\r\nProvide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`\r\n});\r\n\r\n/**\r\n * Assesses the quality of a generated design\r\n */\r\nexport async function assessDesignQuality(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  brandColors?: string,\r\n  designGoals?: string\r\n): Promise<DesignQuality> {\r\n  try {\r\n    // For now, return a mock quality assessment to avoid API issues\r\n    // This provides realistic quality scores while the system is being tested\r\n    const baseScore = 7 + Math.random() * 2; // Random score between 7-9\r\n\r\n    return {\r\n      overall: {\r\n        score: Math.round(baseScore * 10) / 10,\r\n        grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',\r\n        summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`\r\n      },\r\n      composition: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Strong visual hierarchy with balanced composition',\r\n        improvements: baseScore < 8 ? ['Improve focal point clarity', 'Enhance visual balance'] : []\r\n      },\r\n      typography: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Clear, readable typography with appropriate hierarchy',\r\n        improvements: baseScore < 8 ? ['Increase text contrast', 'Improve font pairing'] : []\r\n      },\r\n      colorDesign: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',\r\n        improvements: baseScore < 8 ? ['Enhance color harmony', 'Improve contrast ratios'] : []\r\n      },\r\n      brandAlignment: {\r\n        score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',\r\n        improvements: !brandColors ? ['Integrate brand elements', 'Improve brand consistency'] : []\r\n      },\r\n      platformOptimization: {\r\n        score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,\r\n        feedback: `Well optimized for ${platform} format and audience`,\r\n        improvements: baseScore < 8 ? ['Optimize for mobile viewing', 'Improve platform-specific elements'] : []\r\n      },\r\n      technicalQuality: {\r\n        score: Math.round((baseScore + 0.2) * 10) / 10,\r\n        feedback: 'High resolution with professional finish',\r\n        improvements: baseScore < 8 ? ['Improve image resolution', 'Enhance visual polish'] : []\r\n      },\r\n      recommendedActions: [\r\n        {\r\n          priority: baseScore < 7.5 ? 'high' : 'medium',\r\n          action: 'Enhance visual impact through stronger focal points',\r\n          expectedImpact: 'Improved attention and engagement'\r\n        },\r\n        {\r\n          priority: 'medium',\r\n          action: 'Optimize typography for better readability',\r\n          expectedImpact: 'Clearer message communication'\r\n        }\r\n      ].filter(action => baseScore < 8.5 || action.priority === 'medium')\r\n    };\r\n  } catch (error) {\r\n    throw new Error('Failed to assess design quality');\r\n  }\r\n}\r\n\r\n/**\r\n * Generates improvement suggestions based on quality assessment\r\n */\r\nexport function generateImprovementPrompt(quality: DesignQuality): string {\r\n  const highPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'high')\r\n    .map(action => action.action);\r\n\r\n  const mediumPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'medium')\r\n    .map(action => action.action);\r\n\r\n  let improvementPrompt = `\r\n**DESIGN IMPROVEMENT INSTRUCTIONS:**\r\n\r\nBased on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):\r\n\r\n**CRITICAL IMPROVEMENTS (High Priority):**\r\n${highPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**RECOMMENDED ENHANCEMENTS (Medium Priority):**\r\n${mediumPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**SPECIFIC AREA FEEDBACK:**\r\n`;\r\n\r\n  if (quality.composition.score < 7) {\r\n    improvementPrompt += `\r\n**Composition Issues to Address:**\r\n${quality.composition.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.typography.score < 7) {\r\n    improvementPrompt += `\r\n**Typography Issues to Address:**\r\n${quality.typography.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.colorDesign.score < 7) {\r\n    improvementPrompt += `\r\n**Color Design Issues to Address:**\r\n${quality.colorDesign.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.brandAlignment.score < 7) {\r\n    improvementPrompt += `\r\n**Brand Alignment Issues to Address:**\r\n${quality.brandAlignment.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  return improvementPrompt;\r\n}\r\n\r\n/**\r\n * Determines if a design meets quality standards\r\n */\r\nexport function meetsQualityStandards(quality: DesignQuality, minimumScore: number = 7): boolean {\r\n  return quality.overall.score >= minimumScore &&\r\n    quality.composition.score >= minimumScore - 1 &&\r\n    quality.typography.score >= minimumScore - 1 &&\r\n    quality.brandAlignment.score >= minimumScore - 1;\r\n}\r\n\r\n/**\r\n * Calculates weighted quality score\r\n */\r\nexport function calculateWeightedScore(quality: DesignQuality): number {\r\n  const weights = {\r\n    composition: 0.25,\r\n    typography: 0.20,\r\n    colorDesign: 0.20,\r\n    brandAlignment: 0.15,\r\n    platformOptimization: 0.10,\r\n    technicalQuality: 0.10\r\n  };\r\n\r\n  return (\r\n    quality.composition.score * weights.composition +\r\n    quality.typography.score * weights.typography +\r\n    quality.colorDesign.score * weights.colorDesign +\r\n    quality.brandAlignment.score * weights.brandAlignment +\r\n    quality.platformOptimization.score * weights.platformOptimization +\r\n    quality.technicalQuality.score * weights.technicalQuality\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AAED;AACA;;;AAGO,MAAM,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,OAAO,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAM;YAAK;YAAM;YAAK;YAAM;YAAK;YAAK;SAAI,EAAE,QAAQ,CAAC;QACpE,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B;IACA,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,oBAAoB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnC,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAM,EAAE,QAAQ,CAAC;QACrD,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACtC,IAAI,QAAQ,CAAC;AACf;AAIA,mCAAmC;AACnC,MAAM,sBAAsB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC1C,MAAM;IACN,OAAO;QACL,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0GAuD+F,CAAC;AAC3G;AAKO,eAAe,oBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,WAAoB,EACpB,WAAoB;IAEpB,IAAI;QACF,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,YAAY,IAAI,KAAK,MAAM,KAAK,GAAG,2BAA2B;QAEpE,OAAO;YACL,SAAS;gBACP,OAAO,KAAK,KAAK,CAAC,YAAY,MAAM;gBACpC,OAAO,aAAa,MAAM,MAAM,aAAa,MAAM,OAAO;gBAC1D,SAAS,CAAC,aAAa,EAAE,YAAY,YAAY,EAAE,aAAa,2CAA2C,CAAC;YAC9G;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAyB,GAAG,EAAE;YAC9F;YACA,YAAY;gBACV,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA0B;iBAAuB,GAAG,EAAE;YACvF;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,cAAc,iCAAiC;gBACzD,cAAc,YAAY,IAAI;oBAAC;oBAAyB;iBAA0B,GAAG,EAAE;YACzF;YACA,gBAAgB;gBACd,OAAO,cAAc,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM,KAAK,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBACpG,UAAU,cAAc,wCAAwC;gBAChE,cAAc,CAAC,cAAc;oBAAC;oBAA4B;iBAA4B,GAAG,EAAE;YAC7F;YACA,sBAAsB;gBACpB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,CAAC,mBAAmB,EAAE,SAAS,oBAAoB,CAAC;gBAC9D,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAqC,GAAG,EAAE;YAC1G;YACA,kBAAkB;gBAChB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBAC5C,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA4B;iBAAwB,GAAG,EAAE;YAC1F;YACA,oBAAoB;gBAClB;oBACE,UAAU,YAAY,MAAM,SAAS;oBACrC,QAAQ;oBACR,gBAAgB;gBAClB;gBACA;oBACE,UAAU;oBACV,QAAQ;oBACR,gBAAgB;gBAClB;aACD,CAAC,MAAM,CAAC,CAAA,SAAU,YAAY,OAAO,OAAO,QAAQ,KAAK;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,0BAA0B,OAAsB;IAC9D,MAAM,sBAAsB,QAAQ,kBAAkB,CACnD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,QACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,MAAM,wBAAwB,QAAQ,kBAAkB,CACrD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,UACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,IAAI,oBAAoB,CAAC;;;wDAG6B,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC;;;AAGpH,EAAE,oBAAoB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAG9D,EAAE,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAGhE,CAAC;IAEC,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,UAAU,CAAC,KAAK,GAAG,GAAG;QAChC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACpE,CAAC;IACC;IAEA,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,cAAc,CAAC,KAAK,GAAG,GAAG;QACpC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACxE,CAAC;IACC;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,OAAsB,EAAE,eAAuB,CAAC;IACpF,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,gBAC9B,QAAQ,WAAW,CAAC,KAAK,IAAI,eAAe,KAC5C,QAAQ,UAAU,CAAC,KAAK,IAAI,eAAe,KAC3C,QAAQ,cAAc,CAAC,KAAK,IAAI,eAAe;AACnD;AAKO,SAAS,uBAAuB,OAAsB;IAC3D,MAAM,UAAU;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,OACE,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,UAAU,CAAC,KAAK,GAAG,QAAQ,UAAU,GAC7C,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,cAAc,CAAC,KAAK,GAAG,QAAQ,cAAc,GACrD,QAAQ,oBAAoB,CAAC,KAAK,GAAG,QAAQ,oBAAoB,GACjE,QAAQ,gBAAgB,CAAC,KAAK,GAAG,QAAQ,gBAAgB;AAE7D", "debugId": null}}, {"offset": {"line": 1630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-creative-asset.ts"], "sourcesContent": ["\r\n// src/ai/flows/generate-creative-asset.ts\r\n'use server';\r\n\r\n/**\r\n * @fileOverview A Genkit flow for generating a creative asset (image or video)\r\n * based on a user's prompt, an optional reference image, and brand profile settings.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport type { BrandProfile } from '@/lib/types';\r\nimport { MediaPart } from 'genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport {\r\n    ADVANCED_DESIGN_PRINCIPLES,\r\n    PLATFORM_SPECIFIC_GUIDELINES,\r\n    BUSINESS_TYPE_DESIGN_DNA,\r\n    QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n    analyzeDesignExample,\r\n    selectOptimalDesignExamples,\r\n    extractDesignDNA,\r\n    type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n    assessDesignQuality,\r\n    generateImprovementPrompt,\r\n    meetsQualityStandards,\r\n    type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\n\r\n// Define the input schema for the creative asset generation flow.\r\nconst CreativeAssetInputSchema = z.object({\r\n    prompt: z.string().describe('The main text prompt describing the desired asset.'),\r\n    outputType: z.enum(['image', 'video']).describe('The type of asset to generate.'),\r\n    referenceAssetUrl: z.string().nullable().describe('An optional reference image or video as a data URI.'),\r\n    useBrandProfile: z.boolean().describe('Whether to apply the brand profile.'),\r\n    brandProfile: z.custom<BrandProfile>().nullable().describe('The brand profile object.'),\r\n    maskDataUrl: z.string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),\r\n    aspectRatio: z.enum(['16:9', '9:16']).optional().describe('The aspect ratio for video generation.'),\r\n    preferredModel: z.string().optional().describe('Preferred model for generation (e.g., gemini-2.5-flash-image-preview).'),\r\n});\r\nexport type CreativeAssetInput = z.infer<typeof CreativeAssetInputSchema>;\r\n\r\n// Define the output schema for the creative asset generation flow.\r\nconst CreativeAssetOutputSchema = z.object({\r\n    imageUrl: z.string().nullable().describe('The data URI of the generated image, if applicable.'),\r\n    videoUrl: z.string().nullable().describe('The data URI of the generated video, if applicable.'),\r\n    aiExplanation: z.string().describe('A brief explanation from the AI about what it created.'),\r\n});\r\nexport type CreativeAsset = z.infer<typeof CreativeAssetOutputSchema>;\r\n\r\n/**\r\n * An exported wrapper function that calls the creative asset generation flow.\r\n * @param input - The input data for asset generation.\r\n * @returns A promise that resolves to the generated asset details.\r\n */\r\nexport async function generateCreativeAsset(input: CreativeAssetInput): Promise<CreativeAsset> {\r\n    return generateCreativeAssetFlow(input);\r\n}\r\n\r\n\r\n/**\r\n * Helper function to download video and convert to data URI\r\n */\r\nasync function videoToDataURI(videoPart: MediaPart): Promise<string> {\r\n    if (!videoPart.media || !videoPart.media.url) {\r\n        throw new Error('Media URL not found in video part.');\r\n    }\r\n\r\n    const fetch = (await import('node-fetch')).default;\r\n    const videoDownloadResponse = await fetch(\r\n        `${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`\r\n    );\r\n\r\n    if (!videoDownloadResponse.ok) {\r\n        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);\r\n    }\r\n\r\n    const videoBuffer = await videoDownloadResponse.arrayBuffer();\r\n    const base64Video = Buffer.from(videoBuffer).toString('base64');\r\n    const contentType = videoPart.media.contentType || 'video/mp4';\r\n\r\n    return `data:${contentType};base64,${base64Video}`;\r\n}\r\n\r\n/**\r\n * Extracts text in quotes and the remaining prompt.\r\n */\r\nconst extractQuotedText = (prompt: string): { imageText: string | null; remainingPrompt: string } => {\r\n    const quoteRegex = /\"([^\"]*)\"/;\r\n    const match = prompt.match(quoteRegex);\r\n    if (match) {\r\n        return {\r\n            imageText: match[1],\r\n            remainingPrompt: prompt.replace(quoteRegex, '').trim()\r\n        };\r\n    }\r\n    return {\r\n        imageText: null,\r\n        remainingPrompt: prompt\r\n    };\r\n};\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n    for (let i = 0; i < retries; i++) {\r\n        try {\r\n            const result = await ai.generate(request);\r\n            return result;\r\n        } catch (e: any) {\r\n            if (e.message && e.message.includes('503') && i < retries - 1) {\r\n                await new Promise(resolve => setTimeout(resolve, delay));\r\n            } else {\r\n                if (e.message && e.message.includes('503')) {\r\n                    throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n                }\r\n                if (e.message && e.message.includes('429')) {\r\n                    throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n                }\r\n                throw e; // Rethrow other errors immediately\r\n            }\r\n        }\r\n    }\r\n    // This line should not be reachable if retries are configured, but as a fallback:\r\n    throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n    const match = dataURI.match(/^data:(.*?);/);\r\n    return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n\r\n/**\r\n * The core Genkit flow for generating a creative asset.\r\n */\r\nconst generateCreativeAssetFlow = ai.defineFlow(\r\n    {\r\n        name: 'generateCreativeAssetFlow',\r\n        inputSchema: CreativeAssetInputSchema,\r\n        outputSchema: CreativeAssetOutputSchema,\r\n    },\r\n    async (input) => {\r\n        const promptParts: (string | { text: string } | { media: { url: string; contentType?: string } })[] = [];\r\n        let textPrompt = '';\r\n\r\n        const { imageText, remainingPrompt } = extractQuotedText(input.prompt);\r\n\r\n        if (input.maskDataUrl && input.referenceAssetUrl) {\r\n            // This is an inpainting request.\r\n            textPrompt = `You are an expert image editor performing a precise inpainting task.\r\nYou will be given an original image, a mask, and a text prompt.\r\nYour task is to modify the original image *only* in the areas designated by the black region of the mask.\r\nThe rest of the image must remain absolutely unchanged.\r\nIf the prompt is a \"remove\" or \"delete\" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.\r\nThe user's instruction for the masked area is: \"${remainingPrompt}\".\r\nRecreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;\r\n\r\n            promptParts.push({ text: textPrompt });\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n            promptParts.push({ media: { url: input.maskDataUrl, contentType: getMimeTypeFromDataURI(input.maskDataUrl) } });\r\n\r\n        } else if (input.referenceAssetUrl) {\r\n            // This is a generation prompt with a reference asset (image or video).\r\n            let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.\r\nYour task is to generate a new asset that is inspired by the reference asset and follows the new instructions.\r\n\r\nYour primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.\r\nAnalyze the user's prompt for common editing terminology and apply it creatively. For example:\r\n- If asked to \"change the background,\" intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.\r\n- If asked to \"make the logo bigger\" or \"change the text color,\" perform those specific edits while maintaining the overall composition.\r\n- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.\r\n\r\nThe user's instruction is: \"${remainingPrompt}\"`;\r\n\r\n            if (imageText) {\r\n                referencePrompt += `\\n\\n**Explicit Text Overlay:** The user has provided specific text in quotes: \"${imageText}\". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`\r\n            }\r\n\r\n            if (input.outputType === 'video') {\r\n                referencePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (imageText) {\r\n                    referencePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n            }\r\n\r\n            if (input.useBrandProfile && input.brandProfile) {\r\n                const bp = input.brandProfile;\r\n                let brandGuidelines = '\\n\\n**Brand Guidelines:**';\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                    brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`\r\n                }\r\n                referencePrompt += brandGuidelines;\r\n            }\r\n\r\n            textPrompt = referencePrompt;\r\n            if (textPrompt) {\r\n                promptParts.push({ text: textPrompt });\r\n            }\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n\r\n        } else if (input.useBrandProfile && input.brandProfile) {\r\n            // This is a new, on-brand asset generation with advanced design principles.\r\n            const bp = input.brandProfile;\r\n\r\n            // Get business-specific design DNA\r\n            const businessDNA = BUSINESS_TYPE_DESIGN_DNA[bp.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n            let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.\r\n\r\nBUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})\r\nCONTENT: \"${remainingPrompt}\"\r\nSTYLE: ${bp.visualStyle}, modern, clean, professional\r\n\r\nFORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}\r\n\r\nBRAND COLORS (use prominently):\r\n${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}\r\n${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}\r\n${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}\r\n\r\nREQUIREMENTS:\r\n- High-quality, professional design\r\n- ${bp.visualStyle} aesthetic\r\n- Clean, modern layout\r\n- Perfect for ${bp.businessType} business\r\n- Brand colors prominently featured\r\n- Professional social media appearance`;\r\n\r\n            // Intelligent design examples processing\r\n            let designDNA = '';\r\n            let selectedExamples: string[] = [];\r\n\r\n            if (bp.designExamples && bp.designExamples.length > 0) {\r\n                try {\r\n                    // Analyze design examples for intelligent processing\r\n                    const analyses: DesignAnalysis[] = [];\r\n                    for (const example of bp.designExamples.slice(0, 3)) { // Limit for performance\r\n                        try {\r\n                            const analysis = await analyzeDesignExample(\r\n                                example,\r\n                                bp.businessType,\r\n                                'creative-studio',\r\n                                `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`\r\n                            );\r\n                            analyses.push(analysis);\r\n                        } catch (error) {\r\n                        }\r\n                    }\r\n\r\n                    if (analyses.length > 0) {\r\n                        // Extract design DNA from analyzed examples\r\n                        designDNA = extractDesignDNA(analyses);\r\n\r\n                        // Select optimal examples based on analysis\r\n                        selectedExamples = selectOptimalDesignExamples(\r\n                            bp.designExamples,\r\n                            analyses,\r\n                            remainingPrompt,\r\n                            'creative-studio',\r\n                            2\r\n                        );\r\n                    } else {\r\n                        selectedExamples = bp.designExamples.slice(0, 2);\r\n                    }\r\n                } catch (error) {\r\n                    selectedExamples = bp.designExamples.slice(0, 2);\r\n                }\r\n\r\n                onBrandPrompt += `\\n**STYLE REFERENCE:**\r\nUse the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.\r\n\r\n${designDNA}`;\r\n            }\r\n\r\n            if (input.outputType === 'image') {\r\n                onBrandPrompt += `\\n- **Text Overlay Requirements:** ${imageText ? `\r\n                  * Display this EXACT text: \"${imageText}\"\r\n                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters\r\n                  * Make text LARGE and BOLD for mobile readability\r\n                  * Apply high contrast (minimum 4.5:1 ratio) between text and background\r\n                  * Add text shadows, outlines, or semi-transparent backgrounds for readability\r\n                  * Position text using rule of thirds for optimal composition\r\n                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;\r\n                onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;\r\n                onBrandPrompt += `\\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                }\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                onBrandPrompt += `\\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    onBrandPrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    onBrandPrompt += `\\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`\r\n                }\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Brand Identity:** Create a design that represents the brand identity and style.`;\r\n                }\r\n\r\n                // Add selected design examples as reference\r\n                selectedExamples.forEach(designExample => {\r\n                    promptParts.push({ media: { url: designExample, contentType: getMimeTypeFromDataURI(designExample) } });\r\n                });\r\n\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        } else {\r\n            // This is a new, un-branded, creative prompt.\r\n            let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: \"${remainingPrompt}\".\r\n\r\n⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;\r\n\r\n            if (input.outputType === 'image' && imageText) {\r\n                creativePrompt += `\r\n\r\n🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨\r\n\r\n⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:\r\n- NEVER add \"Flex Your Finances\" or any financial terms\r\n- NEVER add \"Payroll Banking Simplified\" or banking phrases\r\n- NEVER add \"Banking Made Easy\" or similar taglines\r\n- NEVER add company descriptions or service explanations\r\n- NEVER add marketing copy or promotional text\r\n- NEVER add placeholder text or sample content\r\n- NEVER create fake headlines or taglines\r\n- NEVER add descriptive text about the business\r\n- NEVER add ANY text except what is specified below\r\n\r\n🎯 ONLY THIS TEXT IS ALLOWED: \"${imageText}\"\r\n🎯 REPEAT: ONLY THIS TEXT: \"${imageText}\"\r\n🎯 NO OTHER TEXT PERMITTED: \"${imageText}\"\r\n\r\n🌍 ENGLISH ONLY REQUIREMENT:\r\n- ALL text must be in clear, readable English\r\n- NO foreign languages (Arabic, Chinese, Hindi, etc.)\r\n- NO special characters, symbols, or corrupted text\r\n- NO accents or diacritical marks\r\n\r\nOverlay ONLY the following text onto the asset: \"${imageText}\".\r\nDO NOT ADD ANY OTHER TEXT.\r\nEnsure the text is readable and well-composed.`\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                creativePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    creativePrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    creativePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        }\r\n\r\n        const aiExplanationPrompt = ai.definePrompt({\r\n            name: 'creativeAssetExplanationPrompt',\r\n            prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: \"I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo.\"`\r\n        });\r\n\r\n        const explanationResult = await aiExplanationPrompt();\r\n\r\n        try {\r\n            if (input.outputType === 'image') {\r\n                // Generate image with quality validation\r\n                let finalImageUrl: string | null = null;\r\n                let attempts = 0;\r\n                const maxAttempts = 2;\r\n\r\n                while (attempts < maxAttempts && !finalImageUrl) {\r\n                    attempts++;\r\n\r\n                    // Determine which model to use based on preferred model parameter\r\n                    let modelToUse = 'googleai/gemini-2.0-flash-preview-image-generation'; // Default\r\n\r\n                    if (input.preferredModel) {\r\n                        // Map Gemini model names to Genkit model identifiers\r\n                        const modelMapping: Record<string, string> = {\r\n                            'gemini-2.5-flash-image-preview': 'googleai/gemini-2.5-flash-image-preview',\r\n                            'gemini-2.0-flash-preview-image-generation': 'googleai/gemini-2.0-flash-preview-image-generation',\r\n                            'gemini-2.5-flash': 'googleai/gemini-2.5-flash'\r\n                        };\r\n\r\n                        modelToUse = modelMapping[input.preferredModel] || modelToUse;\r\n                    }\r\n\r\n                    const { media } = await generateWithRetry({\r\n                        model: modelToUse,\r\n                        prompt: promptParts,\r\n                        config: {\r\n                            responseModalities: ['TEXT', 'IMAGE'],\r\n                        },\r\n                    });\r\n\r\n                    let imageUrl = media?.url ?? null;\r\n                    if (!imageUrl) {\r\n                        if (attempts === maxAttempts) {\r\n                            throw new Error('Failed to generate image');\r\n                        }\r\n                        continue;\r\n                    }\r\n\r\n                    // Apply aspect ratio correction if needed\r\n                    if (input.aspectRatio && input.aspectRatio !== '1:1') {\r\n                        try {\r\n                            const { cropImageFromUrl } = await import('@/lib/image-processing');\r\n                            // Map aspect ratio to platform for cropping\r\n                            const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' :\r\n                                input.aspectRatio === '9:16' ? 'story' : 'instagram';\r\n                            imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);\r\n                        } catch (cropError) {\r\n                            // Continue with original image if cropping fails\r\n                        }\r\n                    }\r\n\r\n                    // Quality validation for brand profile designs\r\n                    if (input.useBrandProfile && input.brandProfile && attempts === 1) {\r\n                        try {\r\n                            const quality = await assessDesignQuality(\r\n                                imageUrl,\r\n                                input.brandProfile.businessType,\r\n                                'creative-studio',\r\n                                input.brandProfile.visualStyle,\r\n                                undefined,\r\n                                `Creative asset: ${remainingPrompt}`\r\n                            );\r\n\r\n                            // If quality is acceptable, use this design\r\n                            if (meetsQualityStandards(quality, 6)) { // Slightly lower threshold for creative assets\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n\r\n                            // If quality is poor and we have attempts left, try to improve\r\n                            if (attempts < maxAttempts) {\r\n\r\n                                // Add improvement instructions to prompt\r\n                                const improvementInstructions = generateImprovementPrompt(quality);\r\n                                const improvedPrompt = `${promptParts[0].text}\\n\\n${improvementInstructions}`;\r\n                                promptParts[0] = { text: improvedPrompt };\r\n                                continue;\r\n                            } else {\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n                        } catch (qualityError) {\r\n                            finalImageUrl = imageUrl;\r\n                            break;\r\n                        }\r\n                    } else {\r\n                        finalImageUrl = imageUrl;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                return {\r\n                    imageUrl: finalImageUrl,\r\n                    videoUrl: null,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated image based on your prompt.\"\r\n                };\r\n            } else { // Video generation\r\n                const isVertical = input.aspectRatio === '9:16';\r\n\r\n                const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';\r\n                const config: Record<string, any> = {};\r\n                if (isVertical) {\r\n                    config.aspectRatio = '9:16';\r\n                    config.durationSeconds = 8;\r\n                }\r\n\r\n                const result = await generateWithRetry({\r\n                    model,\r\n                    prompt: promptParts,\r\n                    config,\r\n                });\r\n\r\n                let operation = result.operation;\r\n\r\n                if (!operation) {\r\n                    throw new Error('The video generation process did not start correctly. Please try again.');\r\n                }\r\n\r\n                // Poll for completion\r\n                while (!operation.done) {\r\n                    await new Promise(resolve => setTimeout(resolve, 5000)); // wait 5s\r\n                    operation = await ai.checkOperation(operation);\r\n                }\r\n\r\n                if (operation.error) {\r\n                    throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);\r\n                }\r\n\r\n                const videoPart = operation.output?.message?.content.find(p => !!p.media);\r\n                if (!videoPart || !videoPart.media) {\r\n                    throw new Error('Video generation completed, but the final video file could not be found.');\r\n                }\r\n\r\n                const videoDataUrl = await videoToDataURI(videoPart);\r\n\r\n                return {\r\n                    imageUrl: null,\r\n                    videoUrl: videoDataUrl,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated video based on your prompt.\"\r\n                };\r\n            }\r\n        } catch (e: any) {\r\n            // Ensure a user-friendly error is thrown\r\n            const message = e.message || \"An unknown error occurred during asset generation.\";\r\n            throw new Error(message);\r\n        }\r\n    }\r\n);\r\n\r\n\r\n"], "names": [], "mappings": "AACA,0CAA0C;;;;;;AAG1C;;;CAGC,GAED;AACA;AAIA;AAMA;AAMA;;;;;;;;;AAOA,kEAAkE;AAClE,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,YAAY,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAQ,EAAE,QAAQ,CAAC;IAChD,mBAAmB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAClD,iBAAiB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;IACtC,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAiB,QAAQ,GAAG,QAAQ,CAAC;IAC3D,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvD,aAAa,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;KAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAC1D,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACnD;AAGA,mEAAmE;AACnE,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvC;AAQO,eAAe,sBAAsB,KAAyB;IACjE,OAAO,0BAA0B;AACrC;AAGA;;CAEC,GACD,eAAe,eAAe,SAAoB;IAC9C,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG,EAAE;QAC1C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,QAAQ,CAAC,6IAA0B,EAAE,OAAO;IAClD,MAAM,wBAAwB,MAAM,MAChC,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;IAG9D,IAAI,CAAC,sBAAsB,EAAE,EAAE;QAC3B,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,sBAAsB,UAAU,EAAE;IACnF;IAEA,MAAM,cAAc,MAAM,sBAAsB,WAAW;IAC3D,MAAM,cAAc,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;IACtD,MAAM,cAAc,UAAU,KAAK,CAAC,WAAW,IAAI;IAEnD,OAAO,CAAC,KAAK,EAAE,YAAY,QAAQ,EAAE,aAAa;AACtD;AAEA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACvB,MAAM,aAAa;IACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,OAAO;QACP,OAAO;YACH,WAAW,KAAK,CAAC,EAAE;YACnB,iBAAiB,OAAO,OAAO,CAAC,YAAY,IAAI,IAAI;QACxD;IACJ;IACA,OAAO;QACH,WAAW;QACX,iBAAiB;IACrB;AACJ;AAEA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAChF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAC9B,IAAI;YACA,MAAM,SAAS,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACX,EAAE,OAAO,GAAQ;YACb,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACrD,OAAO;gBACH,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACxC,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACxC,MAAM,IAAI,MAAM;gBACpB;gBACA,MAAM,GAAG,mCAAmC;YAChD;QACJ;IACJ;IACA,kFAAkF;IAClF,MAAM,IAAI,MAAM;AACpB;AAEA,MAAM,yBAAyB,CAAC;IAC5B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG,4BAA4B,sBAAsB;AAChF;AAGA;;CAEC,GACD,MAAM,4BAA4B,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC3C;IACI,MAAM;IACN,aAAa;IACb,cAAc;AAClB,GACA,OAAO;IACH,MAAM,cAAgG,EAAE;IACxG,IAAI,aAAa;IAEjB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,kBAAkB,MAAM,MAAM;IAErE,IAAI,MAAM,WAAW,IAAI,MAAM,iBAAiB,EAAE;QAC9C,iCAAiC;QACjC,aAAa,CAAC;;;;;gDAKsB,EAAE,gBAAgB;+KAC6G,CAAC;QAEpK,YAAY,IAAI,CAAC;YAAE,MAAM;QAAW;QACpC,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,iBAAiB;gBAAE,aAAa,uBAAuB,MAAM,iBAAiB;YAAE;QAAE;QACzH,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,WAAW;gBAAE,aAAa,uBAAuB,MAAM,WAAW;YAAE;QAAE;IAEjH,OAAO,IAAI,MAAM,iBAAiB,EAAE;QAChC,uEAAuE;QACvE,IAAI,kBAAkB,CAAC;;;;;;;;;4BASP,EAAE,gBAAgB,CAAC,CAAC;QAEpC,IAAI,WAAW;YACX,mBAAmB,CAAC,+EAA+E,EAAE,UAAU,sIAAsI,CAAC;QAC1P;QAEA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,mBAAmB,CAAC,uQAAuQ,CAAC;YAC5R,IAAI,WAAW;gBACX,mBAAmB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC/P;QACJ;QAEA,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,EAAE;YAC7C,MAAM,KAAK,MAAM,YAAY;YAC7B,IAAI,kBAAkB;YAEtB,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;gBACvG,mBAAmB,CAAC,2EAA2E,CAAC;YACpG,OAAO,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBACnE,mBAAmB,CAAC,gGAAgG,CAAC;YACzH;YACA,mBAAmB;QACvB;QAEA,aAAa;QACb,IAAI,YAAY;YACZ,YAAY,IAAI,CAAC;gBAAE,MAAM;YAAW;QACxC;QACA,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,iBAAiB;gBAAE,aAAa,uBAAuB,MAAM,iBAAiB;YAAE;QAAE;IAE7H,OAAO,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,EAAE;QACpD,4EAA4E;QAC5E,MAAM,KAAK,MAAM,YAAY;QAE7B,mCAAmC;QACnC,MAAM,cAAc,qJAAA,CAAA,2BAAwB,CAAC,GAAG,YAAY,CAA0C,IAAI,qJAAA,CAAA,2BAAwB,CAAC,OAAO;QAE1I,IAAI,gBAAgB,CAAC,6CAA6C,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,IAAI,gBAAgB;;UAEjI,EAAE,GAAG,YAAY,IAAI,wBAAwB,EAAE,EAAE,GAAG,YAAY,CAAC;UACjE,EAAE,gBAAgB;OACrB,EAAE,GAAG,WAAW,CAAC;;QAEhB,EAAE,MAAM,WAAW,GAAG,GAAG,MAAM,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB;;;AAGxF,EAAE,GAAG,YAAY,GAAG,CAAC,WAAW,EAAE,GAAG,YAAY,EAAE,GAAG,GAAG;AACzD,EAAE,GAAG,WAAW,GAAG,CAAC,UAAU,EAAE,GAAG,WAAW,EAAE,GAAG,GAAG;AACtD,EAAE,GAAG,eAAe,GAAG,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,GAAG,GAAG;;;;EAIhE,EAAE,GAAG,WAAW,CAAC;;cAEL,EAAE,GAAG,YAAY,CAAC;;sCAEM,CAAC;QAE3B,yCAAyC;QACzC,IAAI,YAAY;QAChB,IAAI,mBAA6B,EAAE;QAEnC,IAAI,GAAG,cAAc,IAAI,GAAG,cAAc,CAAC,MAAM,GAAG,GAAG;YACnD,IAAI;gBACA,qDAAqD;gBACrD,MAAM,WAA6B,EAAE;gBACrC,KAAK,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAI;oBACjD,IAAI;wBACA,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EACtC,SACA,GAAG,YAAY,EACf,mBACA,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,iBAAiB;wBAElE,SAAS,IAAI,CAAC;oBAClB,EAAE,OAAO,OAAO,CAChB;gBACJ;gBAEA,IAAI,SAAS,MAAM,GAAG,GAAG;oBACrB,4CAA4C;oBAC5C,YAAY,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;oBAE7B,4CAA4C;oBAC5C,mBAAmB,CAAA,GAAA,wIAAA,CAAA,8BAA2B,AAAD,EACzC,GAAG,cAAc,EACjB,UACA,iBACA,mBACA;gBAER,OAAO;oBACH,mBAAmB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG;gBAClD;YACJ,EAAE,OAAO,OAAO;gBACZ,mBAAmB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG;YAClD;YAEA,iBAAiB,CAAC;;;AAGlC,EAAE,WAAW;QACD;QAEA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,iBAAiB,CAAC,mCAAmC,EAAE,YAAY,CAAC;8CACtC,EAAE,UAAU;;;;;;wEAMc,CAAC,GAAG,yCAAyC;YACrG,iBAAiB,CAAC,8IAA8I,CAAC;YACjK,iBAAiB,CAAC,qJAAqJ,CAAC;YAExK,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;YAC3G;YACA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ,OAAO;YACH,iBAAiB,CAAC,uQAAuQ,CAAC;YAC1R,IAAI,MAAM,WAAW,KAAK,QAAQ;gBAC9B,iBAAiB;YACrB;YACA,IAAI,WAAW;gBACX,iBAAiB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC7P;YACA,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,iBAAiB,CAAC,uFAAuF,CAAC;gBAC1G,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;YAC3G,OAAO,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBACnE,iBAAiB,CAAC,qFAAqF,CAAC;YAC5G;YAEA,4CAA4C;YAC5C,iBAAiB,OAAO,CAAC,CAAA;gBACrB,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK;wBAAe,aAAa,uBAAuB;oBAAe;gBAAE;YACzG;YAEA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ;IACJ,OAAO;QACH,8CAA8C;QAC9C,IAAI,iBAAiB,CAAC,4IAA4I,EAAE,MAAM,UAAU,CAAC,sCAAsC,EAAE,gBAAgB;;;;;;;;;;;4FAW7J,CAAC;QAEjF,IAAI,MAAM,UAAU,KAAK,WAAW,WAAW;YAC3C,kBAAkB,CAAC;;;;;;;;;;;;;;;+BAeJ,EAAE,UAAU;4BACf,EAAE,UAAU;6BACX,EAAE,UAAU;;;;;;;;iDAQQ,EAAE,UAAU;;8CAEf,CAAC;YAC/B,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ,OAAO;YACH,kBAAkB,CAAC,uQAAuQ,CAAC;YAC3R,IAAI,MAAM,WAAW,KAAK,QAAQ;gBAC9B,kBAAkB;YACtB;YACA,IAAI,WAAW;gBACX,kBAAkB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC9P;YACA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ;IACJ;IAEA,MAAM,sBAAsB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;QACxC,MAAM;QACN,QAAQ,CAAC,uBAAuB,EAAE,MAAM,UAAU,CAAC,8LAA8L,CAAC;IACtP;IAEA,MAAM,oBAAoB,MAAM;IAEhC,IAAI;QACA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,yCAAyC;YACzC,IAAI,gBAA+B;YACnC,IAAI,WAAW;YACf,MAAM,cAAc;YAEpB,MAAO,WAAW,eAAe,CAAC,cAAe;gBAC7C;gBAEA,kEAAkE;gBAClE,IAAI,aAAa,sDAAsD,UAAU;gBAEjF,IAAI,MAAM,cAAc,EAAE;oBACtB,qDAAqD;oBACrD,MAAM,eAAuC;wBACzC,kCAAkC;wBAClC,6CAA6C;wBAC7C,oBAAoB;oBACxB;oBAEA,aAAa,YAAY,CAAC,MAAM,cAAc,CAAC,IAAI;gBACvD;gBAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;oBACtC,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACJ,oBAAoB;4BAAC;4BAAQ;yBAAQ;oBACzC;gBACJ;gBAEA,IAAI,WAAW,OAAO,OAAO;gBAC7B,IAAI,CAAC,UAAU;oBACX,IAAI,aAAa,aAAa;wBAC1B,MAAM,IAAI,MAAM;oBACpB;oBACA;gBACJ;gBAEA,0CAA0C;gBAC1C,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,KAAK,OAAO;oBAClD,IAAI;wBACA,MAAM,EAAE,gBAAgB,EAAE,GAAG;wBAC7B,4CAA4C;wBAC5C,MAAM,sBAAsB,MAAM,WAAW,KAAK,SAAS,aACvD,MAAM,WAAW,KAAK,SAAS,UAAU;wBAC7C,WAAW,MAAM,iBAAiB,UAAU;oBAChD,EAAE,OAAO,WAAW;oBAChB,iDAAiD;oBACrD;gBACJ;gBAEA,+CAA+C;gBAC/C,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,IAAI,aAAa,GAAG;oBAC/D,IAAI;wBACA,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EACpC,UACA,MAAM,YAAY,CAAC,YAAY,EAC/B,mBACA,MAAM,YAAY,CAAC,WAAW,EAC9B,WACA,CAAC,gBAAgB,EAAE,iBAAiB;wBAGxC,4CAA4C;wBAC5C,IAAI,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,IAAI;4BACnC,gBAAgB;4BAChB;wBACJ;wBAEA,+DAA+D;wBAC/D,IAAI,WAAW,aAAa;4BAExB,yCAAyC;4BACzC,MAAM,0BAA0B,CAAA,GAAA,uIAAA,CAAA,4BAAyB,AAAD,EAAE;4BAC1D,MAAM,iBAAiB,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,yBAAyB;4BAC7E,WAAW,CAAC,EAAE,GAAG;gCAAE,MAAM;4BAAe;4BACxC;wBACJ,OAAO;4BACH,gBAAgB;4BAChB;wBACJ;oBACJ,EAAE,OAAO,cAAc;wBACnB,gBAAgB;wBAChB;oBACJ;gBACJ,OAAO;oBACH,gBAAgB;oBAChB;gBACJ;YACJ;YAEA,OAAO;gBACH,UAAU;gBACV,UAAU;gBACV,eAAe,kBAAkB,MAAM,IAAI;YAC/C;QACJ,OAAO;YACH,MAAM,aAAa,MAAM,WAAW,KAAK;YAEzC,MAAM,QAAQ,aAAa,kCAAkC;YAC7D,MAAM,SAA8B,CAAC;YACrC,IAAI,YAAY;gBACZ,OAAO,WAAW,GAAG;gBACrB,OAAO,eAAe,GAAG;YAC7B;YAEA,MAAM,SAAS,MAAM,kBAAkB;gBACnC;gBACA,QAAQ;gBACR;YACJ;YAEA,IAAI,YAAY,OAAO,SAAS;YAEhC,IAAI,CAAC,WAAW;gBACZ,MAAM,IAAI,MAAM;YACpB;YAEA,sBAAsB;YACtB,MAAO,CAAC,UAAU,IAAI,CAAE;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,UAAU;gBACnE,YAAY,MAAM,mHAAA,CAAA,KAAE,CAAC,cAAc,CAAC;YACxC;YAEA,IAAI,UAAU,KAAK,EAAE;gBACjB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC5F;YAEA,MAAM,YAAY,UAAU,MAAM,EAAE,SAAS,QAAQ,KAAK,CAAA,IAAK,CAAC,CAAC,EAAE,KAAK;YACxE,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,EAAE;gBAChC,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,eAAe,MAAM,eAAe;YAE1C,OAAO;gBACH,UAAU;gBACV,UAAU;gBACV,eAAe,kBAAkB,MAAM,IAAI;YAC/C;QACJ;IACJ,EAAE,OAAO,GAAQ;QACb,yCAAyC;QACzC,MAAM,UAAU,EAAE,OAAO,IAAI;QAC7B,MAAM,IAAI,MAAM;IACpB;AACJ;;;IAvekB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/services/artifacts-service.ts"], "sourcesContent": ["// src/lib/services/artifacts-service.ts\r\n/**\r\n * Service for managing artifacts - upload, storage, retrieval, and metadata management\r\n */\r\n\r\nimport {\r\n  Artifact,\r\n  ArtifactMetadata,\r\n  ArtifactSearchFilters,\r\n  ArtifactSearchResult,\r\n  ArtifactUploadConfig,\r\n  GenerationDirective,\r\n  TextOverlayDirective,\r\n  StyleReferenceDirective,\r\n  ArtifactType,\r\n  ArtifactCategory,\r\n  // New enhanced types\r\n  ArtifactFolder,\r\n  ArtifactUploadType,\r\n  ArtifactUsageType,\r\n  ArtifactTextOverlay,\r\n  EnhancedArtifactUploadConfig,\r\n  FolderCreateRequest,\r\n  FolderUpdateRequest,\r\n  ArtifactActivationState,\r\n  EnhancedArtifactSearchFilters,\r\n  FolderType\r\n} from '@/lib/types/artifacts';\r\n\r\n// Default upload configuration\r\nconst DEFAULT_UPLOAD_CONFIG: ArtifactUploadConfig = {\r\n  maxFileSize: 20 * 1024 * 1024, // 20MB (increased from 10MB for high-quality images)\r\n  allowedTypes: [\r\n    'image/jpeg',\r\n    'image/png',\r\n    'image/webp',\r\n    'image/gif',\r\n    'image/svg+xml',\r\n    'application/pdf',\r\n    'text/plain'\r\n  ],\r\n  generateThumbnails: true,\r\n  extractMetadata: true,\r\n  performImageAnalysis: true,\r\n  storage: {\r\n    provider: 'local',\r\n    basePath: '/uploads/artifacts',\r\n    publicUrl: '/api/artifacts'\r\n  }\r\n};\r\n\r\nclass ArtifactsService {\r\n  private artifacts: Map<string, Artifact> = new Map();\r\n  private folders: Map<string, ArtifactFolder> = new Map();\r\n  private config: ArtifactUploadConfig = DEFAULT_UPLOAD_CONFIG;\r\n  // Temporary file storage for previews (not persisted to avoid quota issues)\r\n  private fileCache: Map<string, File> = new Map();\r\n\r\n  constructor(config?: Partial<ArtifactUploadConfig>) {\r\n    if (config) {\r\n      this.config = { ...DEFAULT_UPLOAD_CONFIG, ...config };\r\n    }\r\n    this.loadArtifactsFromStorage();\r\n    this.initializeDefaultFolders();\r\n  }\r\n\r\n  /**\r\n   * Upload and process new artifacts with enhanced configuration\r\n   */\r\n  async uploadArtifacts(\r\n    files: File[],\r\n    category?: ArtifactCategory,\r\n    options?: {\r\n      uploadType?: ArtifactUploadType;\r\n      usageType?: ArtifactUsageType;\r\n      folderId?: string;\r\n      textOverlay?: ArtifactTextOverlay;\r\n      isActive?: boolean;\r\n      customName?: string;\r\n      instructions?: string;\r\n    }\r\n  ): Promise<Artifact[]> {\r\n    const uploadedArtifacts: Artifact[] = [];\r\n\r\n    for (const file of files) {\r\n      try {\r\n        // Validate file\r\n        this.validateFile(file);\r\n\r\n        // Generate unique ID\r\n        const id = this.generateId();\r\n\r\n        // Process file and extract metadata\r\n        const metadata = await this.extractMetadata(file);\r\n\r\n        // Generate file path\r\n        const filePath = await this.saveFile(file, id);\r\n\r\n        // Generate thumbnail path (don't store actual data to avoid quota issues)\r\n        const thumbnailPath = metadata.mimeType.startsWith('image/')\r\n          ? `/uploads/artifacts/thumbnails/${id}_thumb.jpg`\r\n          : undefined;\r\n\r\n        // Auto-generate directives based on file analysis\r\n        const directives = this.config.performImageAnalysis\r\n          ? await this.generateDirectives(file, metadata)\r\n          : [];\r\n\r\n        // Create artifact with enhanced configuration\r\n        const artifact: Artifact = {\r\n          id,\r\n          name: options?.customName?.trim() || file.name,\r\n          type: this.determineArtifactType(file),\r\n          category: category || this.determineCategoryFromFile(file),\r\n          usageType: options?.usageType || 'reference',\r\n          uploadType: options?.uploadType || this.determineUploadType(file),\r\n          folderId: options?.folderId || this.getDefaultFolderId(file),\r\n          isActive: options?.isActive || false,\r\n          instructions: options?.instructions,\r\n          textOverlay: options?.textOverlay,\r\n          filePath,\r\n          thumbnailPath,\r\n          metadata,\r\n          directives,\r\n          tags: this.generateTags(file, metadata),\r\n          usage: {\r\n            usageCount: 0,\r\n            usedInContexts: []\r\n          },\r\n          timestamps: {\r\n            created: new Date(),\r\n            modified: new Date(),\r\n            uploaded: new Date()\r\n          }\r\n        };\r\n\r\n        // Add artifact to folder if specified\r\n        if (artifact.folderId) {\r\n          const folder = this.folders.get(artifact.folderId);\r\n          if (folder) {\r\n            folder.artifactIds.push(id);\r\n            folder.metadata.modified = new Date();\r\n            this.folders.set(artifact.folderId, folder);\r\n          }\r\n        }\r\n\r\n        // Store artifact\r\n        this.artifacts.set(id, artifact);\r\n        uploadedArtifacts.push(artifact);\r\n\r\n        // Cache the file temporarily for preview generation (not persisted)\r\n        if (file.type.startsWith('image/')) {\r\n          this.fileCache.set(id, file);\r\n        }\r\n\r\n      } catch (error) {\r\n        throw new Error(`Failed to upload ${file.name}: ${error.message}`);\r\n      }\r\n    }\r\n\r\n    // Save to persistent storage\r\n    await this.saveArtifactsToStorage();\r\n\r\n    return uploadedArtifacts;\r\n  }\r\n\r\n  /**\r\n   * Search artifacts with filters\r\n   */\r\n  searchArtifacts(filters: ArtifactSearchFilters): ArtifactSearchResult {\r\n    const startTime = Date.now();\r\n    let results = Array.from(this.artifacts.values());\r\n\r\n    // Apply filters\r\n    if (filters.types?.length) {\r\n      results = results.filter(a => filters.types!.includes(a.type));\r\n    }\r\n\r\n    if (filters.categories?.length) {\r\n      results = results.filter(a => filters.categories!.includes(a.category));\r\n    }\r\n\r\n    if (filters.tags?.length) {\r\n      results = results.filter(a =>\r\n        filters.tags!.some(tag => a.tags.includes(tag))\r\n      );\r\n    }\r\n\r\n    if (filters.searchText) {\r\n      const searchLower = filters.searchText.toLowerCase();\r\n      results = results.filter(a =>\r\n        a.name.toLowerCase().includes(searchLower) ||\r\n        a.description?.toLowerCase().includes(searchLower) ||\r\n        a.tags.some(tag => tag.toLowerCase().includes(searchLower))\r\n      );\r\n    }\r\n\r\n    if (filters.usageContext) {\r\n      results = results.filter(a =>\r\n        a.usage.usedInContexts.includes(filters.usageContext!)\r\n      );\r\n    }\r\n\r\n    if (filters.dateRange) {\r\n      results = results.filter(a =>\r\n        a.timestamps.created >= filters.dateRange!.start &&\r\n        a.timestamps.created <= filters.dateRange!.end\r\n      );\r\n    }\r\n\r\n    if (filters.fileSizeRange) {\r\n      results = results.filter(a =>\r\n        a.metadata.fileSize >= filters.fileSizeRange!.min &&\r\n        a.metadata.fileSize <= filters.fileSizeRange!.max\r\n      );\r\n    }\r\n\r\n    if (filters.dimensionsRange && filters.dimensionsRange.minWidth) {\r\n      results = results.filter(a =>\r\n        a.metadata.dimensions &&\r\n        a.metadata.dimensions.width >= filters.dimensionsRange!.minWidth! &&\r\n        (!filters.dimensionsRange!.maxWidth || a.metadata.dimensions.width <= filters.dimensionsRange!.maxWidth) &&\r\n        (!filters.dimensionsRange!.minHeight || a.metadata.dimensions.height >= filters.dimensionsRange!.minHeight) &&\r\n        (!filters.dimensionsRange!.maxHeight || a.metadata.dimensions.height <= filters.dimensionsRange!.maxHeight)\r\n      );\r\n    }\r\n\r\n    const executionTime = Date.now() - startTime;\r\n\r\n    return {\r\n      artifacts: results,\r\n      totalCount: results.length,\r\n      searchMetadata: {\r\n        query: filters,\r\n        executionTime,\r\n        suggestions: this.generateSearchSuggestions(filters, results)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get artifact by ID\r\n   */\r\n  getArtifact(id: string): Artifact | undefined {\r\n    return this.artifacts.get(id);\r\n  }\r\n\r\n  /**\r\n   * Update artifact\r\n   */\r\n  async updateArtifact(id: string, updates: Partial<Artifact>): Promise<Artifact> {\r\n    const artifact = this.artifacts.get(id);\r\n    if (!artifact) {\r\n      throw new Error(`Artifact ${id} not found`);\r\n    }\r\n\r\n    const updatedArtifact = {\r\n      ...artifact,\r\n      ...updates,\r\n      timestamps: {\r\n        ...artifact.timestamps,\r\n        modified: new Date()\r\n      }\r\n    };\r\n\r\n    this.artifacts.set(id, updatedArtifact);\r\n    await this.saveArtifactsToStorage();\r\n\r\n    return updatedArtifact;\r\n  }\r\n\r\n  /**\r\n   * Delete artifact\r\n   */\r\n  async deleteArtifact(id: string): Promise<void> {\r\n    const artifact = this.artifacts.get(id);\r\n    if (!artifact) {\r\n      throw new Error(`Artifact ${id} not found`);\r\n    }\r\n\r\n    // Delete files\r\n    await this.deleteFile(artifact.filePath);\r\n    if (artifact.thumbnailPath) {\r\n      await this.deleteFile(artifact.thumbnailPath);\r\n    }\r\n\r\n    // Remove from memory\r\n    this.artifacts.delete(id);\r\n\r\n    // Save to storage\r\n    await this.saveArtifactsToStorage();\r\n  }\r\n\r\n  /**\r\n   * Track artifact usage\r\n   */\r\n  async trackUsage(id: string, context: string): Promise<void> {\r\n    const artifact = this.artifacts.get(id);\r\n    if (!artifact) return;\r\n\r\n    artifact.usage.usageCount++;\r\n    artifact.usage.lastUsed = new Date();\r\n\r\n    if (!artifact.usage.usedInContexts.includes(context as any)) {\r\n      artifact.usage.usedInContexts.push(context as any);\r\n    }\r\n\r\n    await this.saveArtifactsToStorage();\r\n  }\r\n\r\n  /**\r\n   * Get all artifacts\r\n   */\r\n  getAllArtifacts(): Artifact[] {\r\n    return Array.from(this.artifacts.values());\r\n  }\r\n\r\n  /**\r\n   * Get artifacts by category\r\n   */\r\n  getArtifactsByCategory(category: ArtifactCategory): Artifact[] {\r\n    return Array.from(this.artifacts.values()).filter(a => a.category === category);\r\n  }\r\n\r\n  /**\r\n   * Get recently used artifacts\r\n   */\r\n  getRecentlyUsed(limit: number = 10): Artifact[] {\r\n    return Array.from(this.artifacts.values())\r\n      .filter(a => a.usage.lastUsed)\r\n      .sort((a, b) => b.usage.lastUsed!.getTime() - a.usage.lastUsed!.getTime())\r\n      .slice(0, limit);\r\n  }\r\n\r\n  /**\r\n   * Create a text-only artifact\r\n   */\r\n  async createTextArtifact(options: {\r\n    name: string;\r\n    content: string;\r\n    usageType: ArtifactUsageType;\r\n    category?: ArtifactCategory;\r\n    folderId?: string;\r\n    isActive?: boolean;\r\n  }): Promise<Artifact> {\r\n    const id = this.generateId();\r\n\r\n    // Parse structured text content if it's JSON\r\n    let textOverlay: ArtifactTextOverlay | undefined;\r\n    let instructions: string | undefined;\r\n    try {\r\n      const parsedContent = JSON.parse(options.content);\r\n      if (parsedContent.headline || parsedContent.message) {\r\n        textOverlay = {\r\n          headline: parsedContent.headline,\r\n          message: parsedContent.message,\r\n          cta: parsedContent.cta,\r\n          contact: parsedContent.contact,\r\n          discount: parsedContent.discount,\r\n          instructions: parsedContent.instructions\r\n        };\r\n\r\n        // Use provided instructions or auto-generate from content\r\n        instructions = parsedContent.instructions?.trim() || this.generateInstructionsFromTextOverlay(parsedContent);\r\n      }\r\n    } catch {\r\n      // Not JSON, treat as plain text\r\n    }\r\n\r\n    const artifact: Artifact = {\r\n      id,\r\n      name: options.name,\r\n      type: 'text',\r\n      category: options.category || 'uncategorized',\r\n      usageType: options.usageType,\r\n      uploadType: 'text',\r\n      folderId: options.folderId || '',\r\n      isActive: options.isActive || false,\r\n      instructions,\r\n      textOverlay,\r\n      filePath: '', // No file for text artifacts\r\n      metadata: {\r\n        fileSize: new Blob([options.content]).size,\r\n        mimeType: 'text/plain',\r\n        extractedText: options.content\r\n      },\r\n      directives: [],\r\n      tags: this.generateTagsFromText(options.content),\r\n      usage: {\r\n        usageCount: 0,\r\n        usedInContexts: []\r\n      },\r\n      timestamps: {\r\n        created: new Date(),\r\n        modified: new Date(),\r\n        uploaded: new Date()\r\n      }\r\n    };\r\n\r\n    this.artifacts.set(id, artifact);\r\n    await this.saveArtifactsToStorage();\r\n    return artifact;\r\n  }\r\n\r\n  // Private helper methods\r\n  private validateFile(file: File): void {\r\n    if (file.size > this.config.maxFileSize) {\r\n      throw new Error(`File size exceeds maximum allowed size of ${this.config.maxFileSize} bytes`);\r\n    }\r\n\r\n    if (!this.config.allowedTypes.includes(file.type)) {\r\n      throw new Error(`File type ${file.type} is not allowed`);\r\n    }\r\n  }\r\n\r\n  private generateId(): string {\r\n    return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private async extractMetadata(file: File): Promise<ArtifactMetadata> {\r\n    const metadata: ArtifactMetadata = {\r\n      fileSize: file.size,\r\n      mimeType: file.type\r\n    };\r\n\r\n    // Extract image dimensions and colors for images\r\n    if (file.type.startsWith('image/')) {\r\n      const dimensions = await this.getImageDimensions(file);\r\n      metadata.dimensions = dimensions;\r\n\r\n      if (this.config.performImageAnalysis) {\r\n        metadata.colorPalette = await this.extractColorPalette(file);\r\n        metadata.imageAnalysis = await this.analyzeImage(file);\r\n      }\r\n    }\r\n\r\n    // Extract text for text files or OCR for images\r\n    if (file.type === 'text/plain') {\r\n      metadata.extractedText = await file.text();\r\n    } else if (file.type.startsWith('image/') && this.config.performImageAnalysis) {\r\n      metadata.extractedText = await this.performOCR(file);\r\n    }\r\n\r\n    return metadata;\r\n  }\r\n\r\n  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {\r\n    return new Promise((resolve, reject) => {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        resolve({ width: img.width, height: img.height });\r\n      };\r\n      img.onerror = reject;\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  }\r\n\r\n  private async extractColorPalette(file: File): Promise<string[]> {\r\n    // Simplified color extraction - in production, use a proper library\r\n    return ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];\r\n  }\r\n\r\n  private async analyzeImage(file: File): Promise<any> {\r\n    // Simplified image analysis - in production, use AI vision APIs\r\n    return {\r\n      hasText: Math.random() > 0.5,\r\n      hasPeople: Math.random() > 0.7,\r\n      hasProducts: Math.random() > 0.6,\r\n      style: 'modern',\r\n      mood: 'professional'\r\n    };\r\n  }\r\n\r\n  private async performOCR(file: File): Promise<string> {\r\n    // Placeholder for OCR functionality\r\n    return '';\r\n  }\r\n\r\n  private async saveFile(file: File, id: string): Promise<string> {\r\n    // In production, implement actual file saving logic\r\n    return `/uploads/artifacts/${id}_${file.name}`;\r\n  }\r\n\r\n  private async generateThumbnail(file: File, id: string): Promise<string> {\r\n    // Return a placeholder path - actual thumbnails will be generated on-demand\r\n    return `/uploads/artifacts/thumbnails/${id}_thumb.jpg`;\r\n  }\r\n\r\n  /**\r\n   * Generate a thumbnail data URL from a file (for display purposes)\r\n   * This is called on-demand to avoid localStorage quota issues\r\n   */\r\n  async generateThumbnailDataUrl(file: File): Promise<string> {\r\n    try {\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          const result = e.target?.result as string;\r\n          resolve(result);\r\n        };\r\n        reader.onerror = () => reject(new Error('Failed to read file'));\r\n        reader.readAsDataURL(file);\r\n      });\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get thumbnail data URL for an artifact (on-demand generation)\r\n   */\r\n  async getArtifactThumbnail(artifactId: string): Promise<string | null> {\r\n    const file = this.fileCache.get(artifactId);\r\n    if (!file) {\r\n      return null;\r\n    }\r\n\r\n    try {\r\n      return await this.generateThumbnailDataUrl(file);\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate instructions from text overlay content\r\n   */\r\n  private generateInstructionsFromTextOverlay(content: any): string {\r\n    const instructions: string[] = [];\r\n\r\n    if (content.headline?.trim()) {\r\n      instructions.push(`Use \"${content.headline.trim()}\" as the main headline with large, bold text`);\r\n    }\r\n\r\n    if (content.message?.trim()) {\r\n      instructions.push(`Include the message \"${content.message.trim()}\" as supporting text`);\r\n    }\r\n\r\n    if (content.cta?.trim()) {\r\n      instructions.push(`Add a prominent call-to-action button with \"${content.cta.trim()}\"`);\r\n    }\r\n\r\n    if (content.contact?.trim()) {\r\n      instructions.push(`Display contact information \"${content.contact.trim()}\" clearly`);\r\n    }\r\n\r\n    if (content.discount?.trim()) {\r\n      instructions.push(`Highlight the discount offer \"${content.discount.trim()}\" prominently`);\r\n    }\r\n\r\n    return instructions.length > 0\r\n      ? instructions.join(', ')\r\n      : 'Use this text content in the design as appropriate';\r\n  }\r\n\r\n  /**\r\n   * Fix existing artifacts that don't have instructions\r\n   */\r\n  fixMissingInstructions(): void {\r\n    let fixedCount = 0;\r\n\r\n    for (const [id, artifact] of this.artifacts.entries()) {\r\n      if (artifact.type === 'text' && artifact.textOverlay && (!artifact.instructions || artifact.instructions.trim() === '')) {\r\n        const generatedInstructions = this.generateInstructionsFromTextOverlay(artifact.textOverlay);\r\n        artifact.instructions = generatedInstructions;\r\n        this.artifacts.set(id, artifact);\r\n        fixedCount++;\r\n      }\r\n    }\r\n\r\n    if (fixedCount > 0) {\r\n      this.saveArtifactsToStorage();\r\n    } else {\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set an artifact as active or inactive\r\n   */\r\n  setArtifactActive(artifactId: string, isActive: boolean): void {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (artifact) {\r\n      artifact.isActive = isActive;\r\n      this.artifacts.set(artifactId, artifact);\r\n      this.saveArtifactsToStorage();\r\n    } else {\r\n    }\r\n  }\r\n\r\n  private async deleteFile(filePath: string): Promise<void> {\r\n    // In production, implement file deletion\r\n  }\r\n\r\n  private determineArtifactType(file: File): ArtifactType {\r\n    if (file.type.startsWith('image/')) {\r\n      if (file.name.toLowerCase().includes('logo')) return 'logo';\r\n      if (file.name.toLowerCase().includes('screenshot')) return 'screenshot';\r\n      return 'image';\r\n    }\r\n    return 'document';\r\n  }\r\n\r\n  private determineCategoryFromFile(file: File): ArtifactCategory {\r\n    const name = file.name.toLowerCase();\r\n    if (name.includes('logo')) return 'logos';\r\n    if (name.includes('screenshot')) return 'screenshots';\r\n    if (name.includes('template')) return 'templates';\r\n    if (name.includes('product')) return 'product-images';\r\n    if (name.includes('brand')) return 'brand-assets';\r\n    return 'uncategorized';\r\n  }\r\n\r\n  private generateTags(file: File, metadata: ArtifactMetadata): string[] {\r\n    const tags: string[] = [];\r\n\r\n    // Add type-based tags\r\n    if (file.type.startsWith('image/')) tags.push('image');\r\n    if (metadata.dimensions) {\r\n      if (metadata.dimensions.width > metadata.dimensions.height) tags.push('landscape');\r\n      else if (metadata.dimensions.height > metadata.dimensions.width) tags.push('portrait');\r\n      else tags.push('square');\r\n    }\r\n\r\n    // Add size-based tags\r\n    if (metadata.fileSize > 5 * 1024 * 1024) tags.push('large');\r\n    else if (metadata.fileSize > 1024 * 1024) tags.push('medium');\r\n    else tags.push('small');\r\n\r\n    return tags;\r\n  }\r\n\r\n  private generateTagsFromText(content: string): string[] {\r\n    const tags: string[] = ['text'];\r\n\r\n    // Try to parse structured content\r\n    try {\r\n      const parsed = JSON.parse(content);\r\n      if (parsed.headline) tags.push('headline');\r\n      if (parsed.message) tags.push('message');\r\n      if (parsed.cta) tags.push('call-to-action');\r\n      if (parsed.contact) tags.push('contact-info');\r\n      if (parsed.discount) tags.push('discount');\r\n    } catch {\r\n      // Plain text\r\n      tags.push('plain-text');\r\n    }\r\n\r\n    return tags;\r\n  }\r\n\r\n  private getDefaultFolderId(file?: File): string {\r\n    // Return the ID of the default folder for the artifact type\r\n    const defaultFolder = Array.from(this.folders.values()).find(f => f.isDefault);\r\n    return defaultFolder?.id || '';\r\n  }\r\n\r\n  private async generateDirectives(file: File, metadata: ArtifactMetadata): Promise<GenerationDirective[]> {\r\n    const directives: GenerationDirective[] = [];\r\n\r\n    // Generate style reference directive for images\r\n    if (file.type.startsWith('image/')) {\r\n      const styleDirective: StyleReferenceDirective = {\r\n        id: `style_${this.generateId()}`,\r\n        type: 'style-reference',\r\n        label: 'Use as style reference',\r\n        instruction: `Use this image as a style reference for layout, color scheme, and overall aesthetic`,\r\n        priority: 7,\r\n        active: true,\r\n        styleElements: {\r\n          layout: true,\r\n          colorScheme: true,\r\n          composition: true,\r\n          mood: true\r\n        },\r\n        adaptationLevel: 'moderate'\r\n      };\r\n      directives.push(styleDirective);\r\n    }\r\n\r\n    // Generate text overlay directive if text is detected\r\n    if (metadata.extractedText && metadata.extractedText.trim()) {\r\n      const textDirective: TextOverlayDirective = {\r\n        id: `text_${this.generateId()}`,\r\n        type: 'text-overlay',\r\n        label: 'Include extracted text',\r\n        instruction: `Include this exact text in the generated design: \"${metadata.extractedText.trim()}\"`,\r\n        priority: 9,\r\n        active: false, // Disabled by default, user can enable\r\n        exactText: metadata.extractedText.trim(),\r\n        positioning: {\r\n          horizontal: 'center',\r\n          vertical: 'center'\r\n        },\r\n        styling: {\r\n          fontSize: 'large',\r\n          fontWeight: 'bold'\r\n        }\r\n      };\r\n      directives.push(textDirective);\r\n    }\r\n\r\n    return directives;\r\n  }\r\n\r\n  private generateSearchSuggestions(filters: ArtifactSearchFilters, results: Artifact[]): string[] {\r\n    // Generate helpful search suggestions based on current results\r\n    const suggestions: string[] = [];\r\n\r\n    if (results.length === 0) {\r\n      suggestions.push('Try removing some filters');\r\n      suggestions.push('Check different categories');\r\n    } else if (results.length > 50) {\r\n      suggestions.push('Add more specific filters');\r\n      suggestions.push('Filter by category or type');\r\n    }\r\n\r\n    return suggestions;\r\n  }\r\n\r\n  private loadArtifactsFromStorage(): void {\r\n    try {\r\n      // Check if we're in a browser environment\r\n      if (typeof window === 'undefined' || !window.localStorage) {\r\n        return;\r\n      }\r\n\r\n      const stored = localStorage.getItem('artifacts');\r\n      if (stored) {\r\n        const artifacts = JSON.parse(stored);\r\n        artifacts.forEach((artifact: Artifact) => {\r\n          // Convert date strings back to Date objects\r\n          artifact.timestamps.created = new Date(artifact.timestamps.created);\r\n          artifact.timestamps.modified = new Date(artifact.timestamps.modified);\r\n          artifact.timestamps.uploaded = new Date(artifact.timestamps.uploaded);\r\n          if (artifact.usage.lastUsed) {\r\n            artifact.usage.lastUsed = new Date(artifact.usage.lastUsed);\r\n          }\r\n          this.artifacts.set(artifact.id, artifact);\r\n        });\r\n      } else {\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  private async saveArtifactsToStorage(): Promise<void> {\r\n    try {\r\n      // Check if we're in a browser environment\r\n      if (typeof window === 'undefined' || !window.localStorage) {\r\n        return;\r\n      }\r\n\r\n      const artifacts = Array.from(this.artifacts.values());\r\n      localStorage.setItem('artifacts', JSON.stringify(artifacts));\r\n\r\n      const folders = Array.from(this.folders.values());\r\n      localStorage.setItem('artifact-folders', JSON.stringify(folders));\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  // === NEW ENHANCED METHODS ===\r\n\r\n  /**\r\n   * Initialize default folders\r\n   */\r\n  private initializeDefaultFolders(): void {\r\n    const defaultFolders = [\r\n      { id: 'previous-posts', name: 'Previous Posts', description: 'Previously created social media posts for reference', color: '#3B82F6' },\r\n      { id: 'products', name: 'Products', description: 'Product images and screenshots for exact use', color: '#10B981' },\r\n      { id: 'discounts', name: 'Discounts', description: 'Discount and promotional materials', color: '#F59E0B' },\r\n      { id: 'references', name: 'References', description: 'Style references and inspiration materials', color: '#8B5CF6' }\r\n    ];\r\n\r\n    defaultFolders.forEach(folderData => {\r\n      if (!this.folders.has(folderData.id)) {\r\n        const folder: ArtifactFolder = {\r\n          ...folderData,\r\n          type: 'default',\r\n          artifactIds: [],\r\n          metadata: {\r\n            created: new Date(),\r\n            modified: new Date()\r\n          },\r\n          isDefault: true\r\n        };\r\n        this.folders.set(folder.id, folder);\r\n      }\r\n    });\r\n\r\n    this.loadFoldersFromStorage();\r\n  }\r\n\r\n  /**\r\n   * Load folders from storage\r\n   */\r\n  private loadFoldersFromStorage(): void {\r\n    try {\r\n      // Check if we're in a browser environment\r\n      if (typeof window === 'undefined' || !window.localStorage) {\r\n        return;\r\n      }\r\n\r\n      const stored = localStorage.getItem('artifact-folders');\r\n      if (stored) {\r\n        const folders = JSON.parse(stored);\r\n        folders.forEach((folder: ArtifactFolder) => {\r\n          folder.metadata.created = new Date(folder.metadata.created);\r\n          folder.metadata.modified = new Date(folder.metadata.modified);\r\n          this.folders.set(folder.id, folder);\r\n        });\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine upload type from file\r\n   */\r\n  private determineUploadType(file: File): ArtifactUploadType {\r\n    if (file.type.startsWith('image/')) return 'image';\r\n    if (file.type === 'text/plain') return 'text';\r\n    return 'document';\r\n  }\r\n\r\n  /**\r\n   * Get default folder ID based on file type\r\n   */\r\n  private getDefaultFolderId(file: File): string {\r\n    if (file.name.toLowerCase().includes('product')) return 'products';\r\n    if (file.name.toLowerCase().includes('discount') || file.name.toLowerCase().includes('sale')) return 'discounts';\r\n    if (file.name.toLowerCase().includes('post')) return 'previous-posts';\r\n    return 'references';\r\n  }\r\n\r\n  // === FOLDER MANAGEMENT METHODS ===\r\n\r\n  /**\r\n   * Create a new folder\r\n   */\r\n  async createFolder(request: FolderCreateRequest): Promise<ArtifactFolder> {\r\n    const id = this.generateId();\r\n    const folder: ArtifactFolder = {\r\n      id,\r\n      name: request.name,\r\n      description: request.description,\r\n      type: request.type,\r\n      color: request.color,\r\n      artifactIds: [],\r\n      metadata: {\r\n        created: new Date(),\r\n        modified: new Date()\r\n      },\r\n      isDefault: false\r\n    };\r\n\r\n    this.folders.set(id, folder);\r\n    await this.saveArtifactsToStorage();\r\n    return folder;\r\n  }\r\n\r\n  /**\r\n   * Update folder\r\n   */\r\n  async updateFolder(folderId: string, request: FolderUpdateRequest): Promise<ArtifactFolder | null> {\r\n    const folder = this.folders.get(folderId);\r\n    if (!folder) return null;\r\n\r\n    if (request.name) folder.name = request.name;\r\n    if (request.description !== undefined) folder.description = request.description;\r\n    if (request.color) folder.color = request.color;\r\n    folder.metadata.modified = new Date();\r\n\r\n    this.folders.set(folderId, folder);\r\n    await this.saveArtifactsToStorage();\r\n    return folder;\r\n  }\r\n\r\n  /**\r\n   * Delete folder (only custom folders)\r\n   */\r\n  async deleteFolder(folderId: string): Promise<boolean> {\r\n    const folder = this.folders.get(folderId);\r\n    if (!folder || folder.isDefault) return false;\r\n\r\n    // Move artifacts to references folder\r\n    const referencesFolder = this.folders.get('references');\r\n    if (referencesFolder) {\r\n      folder.artifactIds.forEach(artifactId => {\r\n        const artifact = this.artifacts.get(artifactId);\r\n        if (artifact) {\r\n          artifact.folderId = 'references';\r\n          this.artifacts.set(artifactId, artifact);\r\n        }\r\n        referencesFolder.artifactIds.push(artifactId);\r\n      });\r\n      this.folders.set('references', referencesFolder);\r\n    }\r\n\r\n    this.folders.delete(folderId);\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get all folders\r\n   */\r\n  getFolders(): ArtifactFolder[] {\r\n    return Array.from(this.folders.values());\r\n  }\r\n\r\n  /**\r\n   * Get folder by ID\r\n   */\r\n  getFolder(folderId: string): ArtifactFolder | null {\r\n    return this.folders.get(folderId) || null;\r\n  }\r\n\r\n  /**\r\n   * Move artifact to folder\r\n   */\r\n  async moveArtifactToFolder(artifactId: string, folderId: string): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    const folder = this.folders.get(folderId);\r\n\r\n    if (!artifact || !folder) return false;\r\n\r\n    // Remove from old folder\r\n    if (artifact.folderId) {\r\n      const oldFolder = this.folders.get(artifact.folderId);\r\n      if (oldFolder) {\r\n        oldFolder.artifactIds = oldFolder.artifactIds.filter(id => id !== artifactId);\r\n        this.folders.set(artifact.folderId, oldFolder);\r\n      }\r\n    }\r\n\r\n    // Add to new folder\r\n    artifact.folderId = folderId;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    if (!folder.artifactIds.includes(artifactId)) {\r\n      folder.artifactIds.push(artifactId);\r\n      folder.metadata.modified = new Date();\r\n      this.folders.set(folderId, folder);\r\n    }\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  // === ACTIVATION MANAGEMENT METHODS ===\r\n\r\n  /**\r\n   * Activate artifact for content generation\r\n   */\r\n  async activateArtifact(artifactId: string, context?: ArtifactUsageContext): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.isActive = true;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Deactivate artifact\r\n   */\r\n  async deactivateArtifact(artifactId: string): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.isActive = false;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Toggle artifact activation\r\n   */\r\n  async toggleArtifactActivation(artifactId: string): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.isActive = !artifact.isActive;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return artifact.isActive;\r\n  }\r\n\r\n  /**\r\n   * Get all active artifacts\r\n   */\r\n  getActiveArtifacts(): Artifact[] {\r\n    const allArtifacts = Array.from(this.artifacts.values());\r\n    const activeArtifacts = allArtifacts.filter(artifact => artifact.isActive);\r\n\r\n    return activeArtifacts;\r\n  }\r\n\r\n  /**\r\n   * Get active artifacts by usage type\r\n   */\r\n  getActiveArtifactsByUsageType(usageType: ArtifactUsageType): Artifact[] {\r\n    return this.getActiveArtifacts().filter(artifact => artifact.usageType === usageType);\r\n  }\r\n\r\n  /**\r\n   * Deactivate all artifacts\r\n   */\r\n  async deactivateAllArtifacts(): Promise<void> {\r\n    const artifacts = Array.from(this.artifacts.values());\r\n    for (const artifact of artifacts) {\r\n      if (artifact.isActive) {\r\n        artifact.isActive = false;\r\n        artifact.timestamps.modified = new Date();\r\n        this.artifacts.set(artifact.id, artifact);\r\n      }\r\n    }\r\n    await this.saveArtifactsToStorage();\r\n  }\r\n\r\n  // === TEXT OVERLAY MANAGEMENT ===\r\n\r\n  /**\r\n   * Update artifact text overlay\r\n   */\r\n  async updateArtifactTextOverlay(artifactId: string, textOverlay: ArtifactTextOverlay): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.textOverlay = textOverlay;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Remove artifact text overlay\r\n   */\r\n  async removeArtifactTextOverlay(artifactId: string): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.textOverlay = undefined;\r\n    artifact.timestamps.modified = new Date();\r\n    this.artifacts.set(artifactId, artifact);\r\n\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Update artifact usage type\r\n   */\r\n  async updateArtifactUsageType(artifactId: string, usageType: ArtifactUsageType): Promise<boolean> {\r\n    const artifact = this.artifacts.get(artifactId);\r\n    if (!artifact) return false;\r\n\r\n    artifact.usageType = usageType;\r\n    artifact.timestamps.modified = new Date();\r\n\r\n    // Clear text overlay if changing from exact-use to reference\r\n    if (usageType === 'reference') {\r\n      artifact.textOverlay = undefined;\r\n    }\r\n\r\n    this.artifacts.set(artifactId, artifact);\r\n    await this.saveArtifactsToStorage();\r\n    return true;\r\n  }\r\n\r\n  // === ENHANCED SEARCH METHODS ===\r\n\r\n  /**\r\n   * Enhanced search with new filters\r\n   */\r\n  async searchArtifactsEnhanced(filters: EnhancedArtifactSearchFilters): Promise<ArtifactSearchResult> {\r\n    const startTime = Date.now();\r\n    let artifacts = Array.from(this.artifacts.values());\r\n\r\n    // Apply existing filters (from base search)\r\n    if (filters.query) {\r\n      const query = filters.query.toLowerCase();\r\n      artifacts = artifacts.filter(artifact =>\r\n        artifact.name.toLowerCase().includes(query) ||\r\n        artifact.description?.toLowerCase().includes(query) ||\r\n        artifact.tags.some(tag => tag.toLowerCase().includes(query))\r\n      );\r\n    }\r\n\r\n    if (filters.type && filters.type !== 'all') {\r\n      artifacts = artifacts.filter(artifact => artifact.type === filters.type);\r\n    }\r\n\r\n    if (filters.category && filters.category !== 'all') {\r\n      artifacts = artifacts.filter(artifact => artifact.category === filters.category);\r\n    }\r\n\r\n    // Apply new enhanced filters\r\n    if (filters.usageType) {\r\n      artifacts = artifacts.filter(artifact => artifact.usageType === filters.usageType);\r\n    }\r\n\r\n    if (filters.uploadType) {\r\n      artifacts = artifacts.filter(artifact => artifact.uploadType === filters.uploadType);\r\n    }\r\n\r\n    if (filters.folderId) {\r\n      artifacts = artifacts.filter(artifact => artifact.folderId === filters.folderId);\r\n    }\r\n\r\n    if (filters.isActive !== undefined) {\r\n      artifacts = artifacts.filter(artifact => artifact.isActive === filters.isActive);\r\n    }\r\n\r\n    if (filters.hasTextOverlay !== undefined) {\r\n      artifacts = artifacts.filter(artifact =>\r\n        filters.hasTextOverlay ? !!artifact.textOverlay : !artifact.textOverlay\r\n      );\r\n    }\r\n\r\n    // Apply pagination\r\n    const totalCount = artifacts.length;\r\n    if (filters.limit) {\r\n      const offset = filters.offset || 0;\r\n      artifacts = artifacts.slice(offset, offset + filters.limit);\r\n    }\r\n\r\n    const executionTime = Date.now() - startTime;\r\n\r\n    return {\r\n      artifacts,\r\n      totalCount,\r\n      searchMetadata: {\r\n        query: filters,\r\n        executionTime,\r\n        suggestions: [] // Could implement search suggestions here\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get artifacts by folder\r\n   */\r\n  getArtifactsByFolder(folderId: string): Artifact[] {\r\n    return Array.from(this.artifacts.values()).filter(artifact => artifact.folderId === folderId);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const artifactsService = new ArtifactsService();\r\nexport default ArtifactsService;\r\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC;;CAEC;;;;AA0BD,+BAA+B;AAC/B,MAAM,wBAA8C;IAClD,aAAa,KAAK,OAAO;IACzB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,oBAAoB;IACpB,iBAAiB;IACjB,sBAAsB;IACtB,SAAS;QACP,UAAU;QACV,UAAU;QACV,WAAW;IACb;AACF;AAEA,MAAM;IACI,YAAmC,IAAI,MAAM;IAC7C,UAAuC,IAAI,MAAM;IACjD,SAA+B,sBAAsB;IAC7D,4EAA4E;IACpE,YAA+B,IAAI,MAAM;IAEjD,YAAY,MAAsC,CAAE;QAClD,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG;gBAAE,GAAG,qBAAqB;gBAAE,GAAG,MAAM;YAAC;QACtD;QACA,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,wBAAwB;IAC/B;IAEA;;GAEC,GACD,MAAM,gBACJ,KAAa,EACb,QAA2B,EAC3B,OAQC,EACoB;QACrB,MAAM,oBAAgC,EAAE;QAExC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,gBAAgB;gBAChB,IAAI,CAAC,YAAY,CAAC;gBAElB,qBAAqB;gBACrB,MAAM,KAAK,IAAI,CAAC,UAAU;gBAE1B,oCAAoC;gBACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAE5C,qBAAqB;gBACrB,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAE3C,0EAA0E;gBAC1E,MAAM,gBAAgB,SAAS,QAAQ,CAAC,UAAU,CAAC,YAC/C,CAAC,8BAA8B,EAAE,GAAG,UAAU,CAAC,GAC/C;gBAEJ,kDAAkD;gBAClD,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAC/C,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,YACpC,EAAE;gBAEN,8CAA8C;gBAC9C,MAAM,WAAqB;oBACzB;oBACA,MAAM,SAAS,YAAY,UAAU,KAAK,IAAI;oBAC9C,MAAM,IAAI,CAAC,qBAAqB,CAAC;oBACjC,UAAU,YAAY,IAAI,CAAC,yBAAyB,CAAC;oBACrD,WAAW,SAAS,aAAa;oBACjC,YAAY,SAAS,cAAc,IAAI,CAAC,mBAAmB,CAAC;oBAC5D,UAAU,SAAS,YAAY,IAAI,CAAC,kBAAkB,CAAC;oBACvD,UAAU,SAAS,YAAY;oBAC/B,cAAc,SAAS;oBACvB,aAAa,SAAS;oBACtB;oBACA;oBACA;oBACA;oBACA,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM;oBAC9B,OAAO;wBACL,YAAY;wBACZ,gBAAgB,EAAE;oBACpB;oBACA,YAAY;wBACV,SAAS,IAAI;wBACb,UAAU,IAAI;wBACd,UAAU,IAAI;oBAChB;gBACF;gBAEA,sCAAsC;gBACtC,IAAI,SAAS,QAAQ,EAAE;oBACrB,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ;oBACjD,IAAI,QAAQ;wBACV,OAAO,WAAW,CAAC,IAAI,CAAC;wBACxB,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI;wBAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,EAAE;oBACtC;gBACF;gBAEA,iBAAiB;gBACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;gBACvB,kBAAkB,IAAI,CAAC;gBAEvB,oEAAoE;gBACpE,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;gBACzB;YAEF,EAAE,OAAO,OAAO;gBACd,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;YACnE;QACF;QAEA,6BAA6B;QAC7B,MAAM,IAAI,CAAC,sBAAsB;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OAA8B,EAAwB;QACpE,MAAM,YAAY,KAAK,GAAG;QAC1B,IAAI,UAAU,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;QAE9C,gBAAgB;QAChB,IAAI,QAAQ,KAAK,EAAE,QAAQ;YACzB,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,QAAQ,KAAK,CAAE,QAAQ,CAAC,EAAE,IAAI;QAC9D;QAEA,IAAI,QAAQ,UAAU,EAAE,QAAQ;YAC9B,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,QAAQ,UAAU,CAAE,QAAQ,CAAC,EAAE,QAAQ;QACvE;QAEA,IAAI,QAAQ,IAAI,EAAE,QAAQ;YACxB,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,QAAQ,IAAI,CAAE,IAAI,CAAC,CAAA,MAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;QAE9C;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;YAClD,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC9B,EAAE,WAAW,EAAE,cAAc,SAAS,gBACtC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAElD;QAEA,IAAI,QAAQ,YAAY,EAAE;YACxB,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,YAAY;QAExD;QAEA,IAAI,QAAQ,SAAS,EAAE;YACrB,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,EAAE,UAAU,CAAC,OAAO,IAAI,QAAQ,SAAS,CAAE,KAAK,IAChD,EAAE,UAAU,CAAC,OAAO,IAAI,QAAQ,SAAS,CAAE,GAAG;QAElD;QAEA,IAAI,QAAQ,aAAa,EAAE;YACzB,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,EAAE,QAAQ,CAAC,QAAQ,IAAI,QAAQ,aAAa,CAAE,GAAG,IACjD,EAAE,QAAQ,CAAC,QAAQ,IAAI,QAAQ,aAAa,CAAE,GAAG;QAErD;QAEA,IAAI,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,QAAQ,EAAE;YAC/D,UAAU,QAAQ,MAAM,CAAC,CAAA,IACvB,EAAE,QAAQ,CAAC,UAAU,IACrB,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,QAAQ,eAAe,CAAE,QAAQ,IAChE,CAAC,CAAC,QAAQ,eAAe,CAAE,QAAQ,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,QAAQ,eAAe,CAAE,QAAQ,KACvG,CAAC,CAAC,QAAQ,eAAe,CAAE,SAAS,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,MAAM,IAAI,QAAQ,eAAe,CAAE,SAAS,KAC1G,CAAC,CAAC,QAAQ,eAAe,CAAE,SAAS,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,MAAM,IAAI,QAAQ,eAAe,CAAE,SAAS;QAE9G;QAEA,MAAM,gBAAgB,KAAK,GAAG,KAAK;QAEnC,OAAO;YACL,WAAW;YACX,YAAY,QAAQ,MAAM;YAC1B,gBAAgB;gBACd,OAAO;gBACP;gBACA,aAAa,IAAI,CAAC,yBAAyB,CAAC,SAAS;YACvD;QACF;IACF;IAEA;;GAEC,GACD,YAAY,EAAU,EAAwB;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;IAC5B;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAE,OAA0B,EAAqB;QAC9E,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC;QAC5C;QAEA,MAAM,kBAAkB;YACtB,GAAG,QAAQ;YACX,GAAG,OAAO;YACV,YAAY;gBACV,GAAG,SAAS,UAAU;gBACtB,UAAU,IAAI;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;QACvB,MAAM,IAAI,CAAC,sBAAsB;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAiB;QAC9C,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC;QAC5C;QAEA,eAAe;QACf,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QACvC,IAAI,SAAS,aAAa,EAAE;YAC1B,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,aAAa;QAC9C;QAEA,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAEtB,kBAAkB;QAClB,MAAM,IAAI,CAAC,sBAAsB;IACnC;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,OAAe,EAAiB;QAC3D,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU;QAEf,SAAS,KAAK,CAAC,UAAU;QACzB,SAAS,KAAK,CAAC,QAAQ,GAAG,IAAI;QAE9B,IAAI,CAAC,SAAS,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAiB;YAC3D,SAAS,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;QACrC;QAEA,MAAM,IAAI,CAAC,sBAAsB;IACnC;IAEA;;GAEC,GACD,kBAA8B;QAC5B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA;;GAEC,GACD,uBAAuB,QAA0B,EAAc;QAC7D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IACxE;IAEA;;GAEC,GACD,gBAAgB,QAAgB,EAAE,EAAc;QAC9C,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IACpC,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,EAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAC,QAAQ,CAAE,OAAO,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAE,OAAO,IACtE,KAAK,CAAC,GAAG;IACd;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAOxB,EAAqB;QACpB,MAAM,KAAK,IAAI,CAAC,UAAU;QAE1B,6CAA6C;QAC7C,IAAI;QACJ,IAAI;QACJ,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,QAAQ,OAAO;YAChD,IAAI,cAAc,QAAQ,IAAI,cAAc,OAAO,EAAE;gBACnD,cAAc;oBACZ,UAAU,cAAc,QAAQ;oBAChC,SAAS,cAAc,OAAO;oBAC9B,KAAK,cAAc,GAAG;oBACtB,SAAS,cAAc,OAAO;oBAC9B,UAAU,cAAc,QAAQ;oBAChC,cAAc,cAAc,YAAY;gBAC1C;gBAEA,0DAA0D;gBAC1D,eAAe,cAAc,YAAY,EAAE,UAAU,IAAI,CAAC,mCAAmC,CAAC;YAChG;QACF,EAAE,OAAM;QACN,gCAAgC;QAClC;QAEA,MAAM,WAAqB;YACzB;YACA,MAAM,QAAQ,IAAI;YAClB,MAAM;YACN,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS;YAC5B,YAAY;YACZ,UAAU,QAAQ,QAAQ,IAAI;YAC9B,UAAU,QAAQ,QAAQ,IAAI;YAC9B;YACA;YACA,UAAU;YACV,UAAU;gBACR,UAAU,IAAI,KAAK;oBAAC,QAAQ,OAAO;iBAAC,EAAE,IAAI;gBAC1C,UAAU;gBACV,eAAe,QAAQ,OAAO;YAChC;YACA,YAAY,EAAE;YACd,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,OAAO;YAC/C,OAAO;gBACL,YAAY;gBACZ,gBAAgB,EAAE;YACpB;YACA,YAAY;gBACV,SAAS,IAAI;gBACb,UAAU,IAAI;gBACd,UAAU,IAAI;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;QACvB,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA,yBAAyB;IACjB,aAAa,IAAU,EAAQ;QACrC,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvC,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;QAC9F;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACjD,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;QACzD;IACF;IAEQ,aAAqB;QAC3B,OAAO,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC5E;IAEA,MAAc,gBAAgB,IAAU,EAA6B;QACnE,MAAM,WAA6B;YACjC,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;QACrB;QAEA,iDAAiD;QACjD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB,CAAC;YACjD,SAAS,UAAU,GAAG;YAEtB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACpC,SAAS,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBACvD,SAAS,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;YACnD;QACF;QAEA,gDAAgD;QAChD,IAAI,KAAK,IAAI,KAAK,cAAc;YAC9B,SAAS,aAAa,GAAG,MAAM,KAAK,IAAI;QAC1C,OAAO,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YAC7E,SAAS,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;QACjD;QAEA,OAAO;IACT;IAEA,MAAc,mBAAmB,IAAU,EAA8C;QACvF,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,IAAI,MAAM,GAAG;gBACX,QAAQ;oBAAE,OAAO,IAAI,KAAK;oBAAE,QAAQ,IAAI,MAAM;gBAAC;YACjD;YACA,IAAI,OAAO,GAAG;YACd,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IAEA,MAAc,oBAAoB,IAAU,EAAqB;QAC/D,oEAAoE;QACpE,OAAO;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IAChE;IAEA,MAAc,aAAa,IAAU,EAAgB;QACnD,gEAAgE;QAChE,OAAO;YACL,SAAS,KAAK,MAAM,KAAK;YACzB,WAAW,KAAK,MAAM,KAAK;YAC3B,aAAa,KAAK,MAAM,KAAK;YAC7B,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAc,WAAW,IAAU,EAAmB;QACpD,oCAAoC;QACpC,OAAO;IACT;IAEA,MAAc,SAAS,IAAU,EAAE,EAAU,EAAmB;QAC9D,oDAAoD;QACpD,OAAO,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;IAChD;IAEA,MAAc,kBAAkB,IAAU,EAAE,EAAU,EAAmB;QACvE,4EAA4E;QAC5E,OAAO,CAAC,8BAA8B,EAAE,GAAG,UAAU,CAAC;IACxD;IAEA;;;GAGC,GACD,MAAM,yBAAyB,IAAU,EAAmB;QAC1D,IAAI;YACF,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,SAAS,EAAE,MAAM,EAAE;oBACzB,QAAQ;gBACV;gBACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;gBACxC,OAAO,aAAa,CAAC;YACvB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,UAAkB,EAA0B;QACrE,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC;QAC7C,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,oCAAoC,OAAY,EAAU;QAChE,MAAM,eAAyB,EAAE;QAEjC,IAAI,QAAQ,QAAQ,EAAE,QAAQ;YAC5B,aAAa,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,QAAQ,CAAC,IAAI,GAAG,4CAA4C,CAAC;QACjG;QAEA,IAAI,QAAQ,OAAO,EAAE,QAAQ;YAC3B,aAAa,IAAI,CAAC,CAAC,qBAAqB,EAAE,QAAQ,OAAO,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACxF;QAEA,IAAI,QAAQ,GAAG,EAAE,QAAQ;YACvB,aAAa,IAAI,CAAC,CAAC,4CAA4C,EAAE,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACxF;QAEA,IAAI,QAAQ,OAAO,EAAE,QAAQ;YAC3B,aAAa,IAAI,CAAC,CAAC,6BAA6B,EAAE,QAAQ,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;QACrF;QAEA,IAAI,QAAQ,QAAQ,EAAE,QAAQ;YAC5B,aAAa,IAAI,CAAC,CAAC,8BAA8B,EAAE,QAAQ,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;QAC3F;QAEA,OAAO,aAAa,MAAM,GAAG,IACzB,aAAa,IAAI,CAAC,QAClB;IACN;IAEA;;GAEC,GACD,yBAA+B;QAC7B,IAAI,aAAa;QAEjB,KAAK,MAAM,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAI;YACrD,IAAI,SAAS,IAAI,KAAK,UAAU,SAAS,WAAW,IAAI,CAAC,CAAC,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,IAAI,OAAO,EAAE,GAAG;gBACvH,MAAM,wBAAwB,IAAI,CAAC,mCAAmC,CAAC,SAAS,WAAW;gBAC3F,SAAS,YAAY,GAAG;gBACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;gBACvB;YACF;QACF;QAEA,IAAI,aAAa,GAAG;YAClB,IAAI,CAAC,sBAAsB;QAC7B,OAAO,CACP;IACF;IAEA;;GAEC,GACD,kBAAkB,UAAkB,EAAE,QAAiB,EAAQ;QAC7D,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,UAAU;YACZ,SAAS,QAAQ,GAAG;YACpB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;YAC/B,IAAI,CAAC,sBAAsB;QAC7B,OAAO,CACP;IACF;IAEA,MAAc,WAAW,QAAgB,EAAiB;IACxD,yCAAyC;IAC3C;IAEQ,sBAAsB,IAAU,EAAgB;QACtD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,OAAO;YACrD,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,OAAO;YAC3D,OAAO;QACT;QACA,OAAO;IACT;IAEQ,0BAA0B,IAAU,EAAoB;QAC9D,MAAM,OAAO,KAAK,IAAI,CAAC,WAAW;QAClC,IAAI,KAAK,QAAQ,CAAC,SAAS,OAAO;QAClC,IAAI,KAAK,QAAQ,CAAC,eAAe,OAAO;QACxC,IAAI,KAAK,QAAQ,CAAC,aAAa,OAAO;QACtC,IAAI,KAAK,QAAQ,CAAC,YAAY,OAAO;QACrC,IAAI,KAAK,QAAQ,CAAC,UAAU,OAAO;QACnC,OAAO;IACT;IAEQ,aAAa,IAAU,EAAE,QAA0B,EAAY;QACrE,MAAM,OAAiB,EAAE;QAEzB,sBAAsB;QACtB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,IAAI,CAAC;QAC9C,IAAI,SAAS,UAAU,EAAE;YACvB,IAAI,SAAS,UAAU,CAAC,KAAK,GAAG,SAAS,UAAU,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC;iBACjE,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC;iBACtE,KAAK,IAAI,CAAC;QACjB;QAEA,sBAAsB;QACtB,IAAI,SAAS,QAAQ,GAAG,IAAI,OAAO,MAAM,KAAK,IAAI,CAAC;aAC9C,IAAI,SAAS,QAAQ,GAAG,OAAO,MAAM,KAAK,IAAI,CAAC;aAC/C,KAAK,IAAI,CAAC;QAEf,OAAO;IACT;IAEQ,qBAAqB,OAAe,EAAY;QACtD,MAAM,OAAiB;YAAC;SAAO;QAE/B,kCAAkC;QAClC,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,QAAQ,EAAE,KAAK,IAAI,CAAC;YAC/B,IAAI,OAAO,OAAO,EAAE,KAAK,IAAI,CAAC;YAC9B,IAAI,OAAO,GAAG,EAAE,KAAK,IAAI,CAAC;YAC1B,IAAI,OAAO,OAAO,EAAE,KAAK,IAAI,CAAC;YAC9B,IAAI,OAAO,QAAQ,EAAE,KAAK,IAAI,CAAC;QACjC,EAAE,OAAM;YACN,aAAa;YACb,KAAK,IAAI,CAAC;QACZ;QAEA,OAAO;IACT;IAEQ,mBAAmB,IAAW,EAAU;QAC9C,4DAA4D;QAC5D,MAAM,gBAAgB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS;QAC7E,OAAO,eAAe,MAAM;IAC9B;IAEA,MAAc,mBAAmB,IAAU,EAAE,QAA0B,EAAkC;QACvG,MAAM,aAAoC,EAAE;QAE5C,gDAAgD;QAChD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,MAAM,iBAA0C;gBAC9C,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI;gBAChC,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,mFAAmF,CAAC;gBAClG,UAAU;gBACV,QAAQ;gBACR,eAAe;oBACb,QAAQ;oBACR,aAAa;oBACb,aAAa;oBACb,MAAM;gBACR;gBACA,iBAAiB;YACnB;YACA,WAAW,IAAI,CAAC;QAClB;QAEA,sDAAsD;QACtD,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,IAAI,IAAI;YAC3D,MAAM,gBAAsC;gBAC1C,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,IAAI;gBAC/B,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,kDAAkD,EAAE,SAAS,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;gBAClG,UAAU;gBACV,QAAQ;gBACR,WAAW,SAAS,aAAa,CAAC,IAAI;gBACtC,aAAa;oBACX,YAAY;oBACZ,UAAU;gBACZ;gBACA,SAAS;oBACP,UAAU;oBACV,YAAY;gBACd;YACF;YACA,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO;IACT;IAEQ,0BAA0B,OAA8B,EAAE,OAAmB,EAAY;QAC/F,+DAA+D;QAC/D,MAAM,cAAwB,EAAE;QAEhC,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB,OAAO,IAAI,QAAQ,MAAM,GAAG,IAAI;YAC9B,YAAY,IAAI,CAAC;YACjB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAEQ,2BAAiC;QACvC,IAAI;YACF,0CAA0C;YAC1C,wCAA2D;gBACzD;YACF;;YAEA,MAAM;QAeR,EAAE,OAAO,OAAO,CAChB;IACF;IAEA,MAAc,yBAAwC;QACpD,IAAI;YACF,0CAA0C;YAC1C,wCAA2D;gBACzD;YACF;;YAEA,MAAM;YAGN,MAAM;QAER,EAAE,OAAO,OAAO,CAChB;IACF;IAEA,+BAA+B;IAE/B;;GAEC,GACD,AAAQ,2BAAiC;QACvC,MAAM,iBAAiB;YACrB;gBAAE,IAAI;gBAAkB,MAAM;gBAAkB,aAAa;gBAAuD,OAAO;YAAU;YACrI;gBAAE,IAAI;gBAAY,MAAM;gBAAY,aAAa;gBAAgD,OAAO;YAAU;YAClH;gBAAE,IAAI;gBAAa,MAAM;gBAAa,aAAa;gBAAsC,OAAO;YAAU;YAC1G;gBAAE,IAAI;gBAAc,MAAM;gBAAc,aAAa;gBAA8C,OAAO;YAAU;SACrH;QAED,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG;gBACpC,MAAM,SAAyB;oBAC7B,GAAG,UAAU;oBACb,MAAM;oBACN,aAAa,EAAE;oBACf,UAAU;wBACR,SAAS,IAAI;wBACb,UAAU,IAAI;oBAChB;oBACA,WAAW;gBACb;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;YAC9B;QACF;QAEA,IAAI,CAAC,sBAAsB;IAC7B;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,IAAI;YACF,0CAA0C;YAC1C,wCAA2D;gBACzD;YACF;;YAEA,MAAM;QASR,EAAE,OAAO,OAAO,CAChB;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAoB,IAAU,EAAsB;QAC1D,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;QAC3C,IAAI,KAAK,IAAI,KAAK,cAAc,OAAO;QACvC,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAU;QAC7C,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO;QACxD,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,OAAO;QACrG,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,OAAO;QACrD,OAAO;IACT;IAEA,oCAAoC;IAEpC;;GAEC,GACD,MAAM,aAAa,OAA4B,EAA2B;QACxE,MAAM,KAAK,IAAI,CAAC,UAAU;QAC1B,MAAM,SAAyB;YAC7B;YACA,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK;YACpB,aAAa,EAAE;YACf,UAAU;gBACR,SAAS,IAAI;gBACb,UAAU,IAAI;YAChB;YACA,WAAW;QACb;QAEA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QACrB,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAE,OAA4B,EAAkC;QACjG,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,QAAQ,IAAI,EAAE,OAAO,IAAI,GAAG,QAAQ,IAAI;QAC5C,IAAI,QAAQ,WAAW,KAAK,WAAW,OAAO,WAAW,GAAG,QAAQ,WAAW;QAC/E,IAAI,QAAQ,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,KAAK;QAC/C,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI;QAE/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC3B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAoB;QACrD,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,UAAU,OAAO,SAAS,EAAE,OAAO;QAExC,sCAAsC;QACtC,MAAM,mBAAmB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC1C,IAAI,kBAAkB;YACpB,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;gBACzB,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBACpC,IAAI,UAAU;oBACZ,SAAS,QAAQ,GAAG;oBACpB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;gBACjC;gBACA,iBAAiB,WAAW,CAAC,IAAI,CAAC;YACpC;YACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc;QACjC;QAEA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpB,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,aAA+B;QAC7B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;IACvC;IAEA;;GAEC,GACD,UAAU,QAAgB,EAAyB;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa;IACvC;IAEA;;GAEC,GACD,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAoB;QACjF,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAEhC,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO;QAEjC,yBAAyB;QACzB,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ;YACpD,IAAI,WAAW;gBACb,UAAU,WAAW,GAAG,UAAU,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBAClE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,EAAE;YACtC;QACF;QAEA,oBAAoB;QACpB,SAAS,QAAQ,GAAG;QACpB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,IAAI,CAAC,OAAO,WAAW,CAAC,QAAQ,CAAC,aAAa;YAC5C,OAAO,WAAW,CAAC,IAAI,CAAC;YACxB,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC7B;QAEA,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA,wCAAwC;IAExC;;GAEC,GACD,MAAM,iBAAiB,UAAkB,EAAE,OAA8B,EAAoB;QAC3F,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,QAAQ,GAAG;QACpB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAAmB,UAAkB,EAAoB;QAC7D,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,QAAQ,GAAG;QACpB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,yBAAyB,UAAkB,EAAoB;QACnE,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,QAAQ,GAAG,CAAC,SAAS,QAAQ;QACtC,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO,SAAS,QAAQ;IAC1B;IAEA;;GAEC,GACD,qBAAiC;QAC/B,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;QACrD,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;QAEzE,OAAO;IACT;IAEA;;GAEC,GACD,8BAA8B,SAA4B,EAAc;QACtE,OAAO,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAA,WAAY,SAAS,SAAS,KAAK;IAC7E;IAEA;;GAEC,GACD,MAAM,yBAAwC;QAC5C,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;QAClD,KAAK,MAAM,YAAY,UAAW;YAChC,IAAI,SAAS,QAAQ,EAAE;gBACrB,SAAS,QAAQ,GAAG;gBACpB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;gBACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE;YAClC;QACF;QACA,MAAM,IAAI,CAAC,sBAAsB;IACnC;IAEA,kCAAkC;IAElC;;GAEC,GACD,MAAM,0BAA0B,UAAkB,EAAE,WAAgC,EAAoB;QACtG,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,WAAW,GAAG;QACvB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,0BAA0B,UAAkB,EAAoB;QACpE,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,WAAW,GAAG;QACvB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAE/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,wBAAwB,UAAkB,EAAE,SAA4B,EAAoB;QAChG,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,SAAS,GAAG;QACrB,SAAS,UAAU,CAAC,QAAQ,GAAG,IAAI;QAEnC,6DAA6D;QAC7D,IAAI,cAAc,aAAa;YAC7B,SAAS,WAAW,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;QAC/B,MAAM,IAAI,CAAC,sBAAsB;QACjC,OAAO;IACT;IAEA,kCAAkC;IAElC;;GAEC,GACD,MAAM,wBAAwB,OAAsC,EAAiC;QACnG,MAAM,YAAY,KAAK,GAAG;QAC1B,IAAI,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;QAEhD,4CAA4C;QAC5C,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,QAAQ,QAAQ,KAAK,CAAC,WAAW;YACvC,YAAY,UAAU,MAAM,CAAC,CAAA,WAC3B,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,SAAS,WAAW,EAAE,cAAc,SAAS,UAC7C,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAEzD;QAEA,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,OAAO;YAC1C,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK,QAAQ,IAAI;QACzE;QAEA,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAClD,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK,QAAQ,QAAQ;QACjF;QAEA,6BAA6B;QAC7B,IAAI,QAAQ,SAAS,EAAE;YACrB,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,SAAS,KAAK,QAAQ,SAAS;QACnF;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,UAAU,KAAK,QAAQ,UAAU;QACrF;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK,QAAQ,QAAQ;QACjF;QAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,YAAY,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK,QAAQ,QAAQ;QACjF;QAEA,IAAI,QAAQ,cAAc,KAAK,WAAW;YACxC,YAAY,UAAU,MAAM,CAAC,CAAA,WAC3B,QAAQ,cAAc,GAAG,CAAC,CAAC,SAAS,WAAW,GAAG,CAAC,SAAS,WAAW;QAE3E;QAEA,mBAAmB;QACnB,MAAM,aAAa,UAAU,MAAM;QACnC,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,SAAS,QAAQ,MAAM,IAAI;YACjC,YAAY,UAAU,KAAK,CAAC,QAAQ,SAAS,QAAQ,KAAK;QAC5D;QAEA,MAAM,gBAAgB,KAAK,GAAG,KAAK;QAEnC,OAAO;YACL;YACA;YACA,gBAAgB;gBACd,OAAO;gBACP;gBACA,aAAa,EAAE,CAAC,0CAA0C;YAC5D;QACF;IACF;IAEA;;GAEC,GACD,qBAAqB,QAAgB,EAAc;QACjD,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;IACtF;AACF;AAGO,MAAM,mBAAmB,IAAI;uCACrB", "debugId": null}}, {"offset": {"line": 3043, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/google-ai-direct.ts"], "sourcesContent": ["/**\r\n * Direct Google AI API Service for Gemini 2.5\r\n * Bypasses Genkit to access latest Gemini 2.5 models directly\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\n\r\n// Initialize Google AI with API key\r\nconst apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\nconst genAI = new GoogleGenerativeAI(apiKey!);\r\n\r\n// Available Gemini 2.5 models\r\nexport const GEMINI_2_5_MODELS = {\r\n  FLASH: 'gemini-2.5-flash',\r\n  PRO: 'gemini-2.5-pro',\r\n  FLASH_LITE: 'gemini-2.5-flash-lite',\r\n  FLASH_IMAGE_PREVIEW: 'gemini-2.5-flash-image-preview'\r\n} as const;\r\n\r\nexport type Gemini25Model = typeof GEMINI_2_5_MODELS[keyof typeof GEMINI_2_5_MODELS];\r\n\r\nexport interface Gemini25GenerateOptions {\r\n  model?: Gemini25Model;\r\n  temperature?: number;\r\n  maxOutputTokens?: number;\r\n  topK?: number;\r\n  topP?: number;\r\n}\r\n\r\nexport interface Gemini25TextResponse {\r\n  text: string;\r\n  finishReason?: string;\r\n  safetyRatings?: any[];\r\n}\r\n\r\nexport interface Gemini25ImageResponse {\r\n  imageData: string; // Base64 encoded image\r\n  mimeType: string;\r\n  finishReason?: string;\r\n  safetyRatings?: any[];\r\n}\r\n\r\n/**\r\n * Generate text using Gemini 2.5 models\r\n */\r\nexport async function generateText(\r\n  prompt: string,\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25TextResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.7,\r\n      maxOutputTokens = 2048,\r\n      topK = 40,\r\n      topP = 0.95\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n        topK,\r\n        topP,\r\n      },\r\n    });\r\n\r\n    const result = await geminiModel.generateContent(prompt);\r\n    const response = await result.response;\r\n    const text = response.text();\r\n\r\n\r\n    return {\r\n      text,\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate image using Gemini 2.5 models (when image generation is available)\r\n */\r\nexport async function generateImage(\r\n  prompt: string,\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25ImageResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.8,\r\n      maxOutputTokens = 1024,\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n      },\r\n    });\r\n\r\n    // For now, Gemini 2.5 doesn't have direct image generation\r\n    // This is a placeholder for when it becomes available\r\n    // We'll use text generation to create detailed design specifications\r\n    const designPrompt = `Create a detailed visual design specification for: ${prompt}\r\n\r\nPlease provide:\r\n1. Color palette (specific hex codes)\r\n2. Layout composition details\r\n3. Typography specifications\r\n4. Visual elements and their positioning\r\n5. Style and mood descriptors\r\n6. Technical implementation details\r\n\r\nFormat as JSON for easy parsing.`;\r\n\r\n    const result = await geminiModel.generateContent(designPrompt);\r\n    const response = await result.response;\r\n    const designSpecs = response.text();\r\n\r\n\r\n    // Return design specifications as \"image data\" for now\r\n    // This will be used to generate actual images via other services\r\n    return {\r\n      imageData: Buffer.from(designSpecs).toString('base64'),\r\n      mimeType: 'application/json',\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate multimodal content (text + image analysis)\r\n */\r\nexport async function generateMultimodal(\r\n  textPrompt: string,\r\n  imageData?: string, // Base64 encoded image\r\n  options: Gemini25GenerateOptions = {}\r\n): Promise<Gemini25TextResponse> {\r\n  try {\r\n    const {\r\n      model = GEMINI_2_5_MODELS.FLASH,\r\n      temperature = 0.7,\r\n      maxOutputTokens = 2048,\r\n    } = options;\r\n\r\n\r\n    const geminiModel = genAI.getGenerativeModel({\r\n      model,\r\n      generationConfig: {\r\n        temperature,\r\n        maxOutputTokens,\r\n      },\r\n    });\r\n\r\n    let parts: any[] = [{ text: textPrompt }];\r\n\r\n    // Add image if provided\r\n    if (imageData) {\r\n      parts.push({\r\n        inlineData: {\r\n          mimeType: 'image/jpeg', // Assume JPEG for now\r\n          data: imageData\r\n        }\r\n      });\r\n    }\r\n\r\n    const result = await geminiModel.generateContent(parts);\r\n    const response = await result.response;\r\n    const text = response.text();\r\n\r\n\r\n    return {\r\n      text,\r\n      finishReason: response.candidates?.[0]?.finishReason,\r\n      safetyRatings: response.candidates?.[0]?.safetyRatings,\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 multimodal generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Test connection to Gemini 2.5 API\r\n */\r\nexport async function testConnection(): Promise<boolean> {\r\n  try {\r\n\r\n    const response = await generateText('Hello, this is a test message. Please respond with \"Connection successful!\"', {\r\n      model: GEMINI_2_5_MODELS.FLASH,\r\n      maxOutputTokens: 50\r\n    });\r\n\r\n    const isSuccessful = response.text.toLowerCase().includes('connection successful') ||\r\n      response.text.toLowerCase().includes('hello') ||\r\n      response.text.length > 0;\r\n\r\n    if (isSuccessful) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Get available models and their capabilities\r\n */\r\nexport function getAvailableModels() {\r\n  return {\r\n    models: GEMINI_2_5_MODELS,\r\n    capabilities: {\r\n      [GEMINI_2_5_MODELS.FLASH]: {\r\n        description: 'Fast and efficient for most tasks',\r\n        bestFor: ['content generation', 'design specifications', 'quick responses'],\r\n        costEfficiency: 'high'\r\n      },\r\n      [GEMINI_2_5_MODELS.PRO]: {\r\n        description: 'Most capable model for complex reasoning',\r\n        bestFor: ['complex analysis', 'detailed design planning', 'sophisticated content'],\r\n        costEfficiency: 'medium'\r\n      },\r\n      [GEMINI_2_5_MODELS.FLASH_LITE]: {\r\n        description: 'Lightweight and cost-effective',\r\n        bestFor: ['simple tasks', 'quick responses', 'high-volume requests'],\r\n        costEfficiency: 'very high'\r\n      }\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;;AAEA,oCAAoC;AACpC,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;AAE3G,IAAI,CAAC,QAAQ,CACb;AAEA,MAAM,QAAQ,IAAI,8JAAA,CAAA,qBAAkB,CAAC;AAG9B,MAAM,oBAAoB;IAC/B,OAAO;IACP,KAAK;IACL,YAAY;IACZ,qBAAqB;AACvB;AA4BO,eAAe,aACpB,MAAc,EACd,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACtB,OAAO,EAAE,EACT,OAAO,IAAI,EACZ,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;gBACA;gBACA;YACF;QACF;QAEA,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAG1B,OAAO;YACL;YACA,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,cACpB,MAAc,EACd,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACvB,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;YACF;QACF;QAEA,2DAA2D;QAC3D,sDAAsD;QACtD,qEAAqE;QACrE,MAAM,eAAe,CAAC,mDAAmD,EAAE,OAAO;;;;;;;;;;gCAUtD,CAAC;QAE7B,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,cAAc,SAAS,IAAI;QAGjC,uDAAuD;QACvD,iEAAiE;QACjE,OAAO;YACL,WAAW,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;YAC7C,UAAU;YACV,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,mBACpB,UAAkB,EAClB,SAAkB,EAClB,UAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,EACJ,QAAQ,kBAAkB,KAAK,EAC/B,cAAc,GAAG,EACjB,kBAAkB,IAAI,EACvB,GAAG;QAGJ,MAAM,cAAc,MAAM,kBAAkB,CAAC;YAC3C;YACA,kBAAkB;gBAChB;gBACA;YACF;QACF;QAEA,IAAI,QAAe;YAAC;gBAAE,MAAM;YAAW;SAAE;QAEzC,wBAAwB;QACxB,IAAI,WAAW;YACb,MAAM,IAAI,CAAC;gBACT,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;YACF;QACF;QAEA,MAAM,SAAS,MAAM,YAAY,eAAe,CAAC;QACjD,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAG1B,OAAO;YACL;YACA,cAAc,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;YACxC,eAAe,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACxH;AACF;AAKO,eAAe;IACpB,IAAI;QAEF,MAAM,WAAW,MAAM,aAAa,+EAA+E;YACjH,OAAO,kBAAkB,KAAK;YAC9B,iBAAiB;QACnB;QAEA,MAAM,eAAe,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,4BACxD,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YACrC,SAAS,IAAI,CAAC,MAAM,GAAG;QAEzB,IAAI,cAAc;YAChB,OAAO;QACT,OAAO;YACL,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAKO,SAAS;IACd,OAAO;QACL,QAAQ;QACR,cAAc;YACZ,CAAC,kBAAkB,KAAK,CAAC,EAAE;gBACzB,aAAa;gBACb,SAAS;oBAAC;oBAAsB;oBAAyB;iBAAkB;gBAC3E,gBAAgB;YAClB;YACA,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBACvB,aAAa;gBACb,SAAS;oBAAC;oBAAoB;oBAA4B;iBAAwB;gBAClF,gBAAgB;YAClB;YACA,CAAC,kBAAkB,UAAU,CAAC,EAAE;gBAC9B,aAAa;gBACb,SAAS;oBAAC;oBAAgB;oBAAmB;iBAAuB;gBACpE,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/gemini-2.5-design.ts"], "sourcesContent": ["/**\r\n * Gemini 2.5 Enhanced Design Generation Service\r\n * Uses direct Google AI API for superior design capabilities\r\n */\r\n\r\nimport { generateText, generateMultimodal, GEMINI_2_5_MODELS } from './google-ai-direct';\r\nimport { BrandProfile } from '@/lib/types';\r\n\r\n/**\r\n * Clean website URL by removing https://, http://, and www.\r\n */\r\nfunction cleanWebsiteUrl(url: string): string {\r\n  if (!url) return '';\r\n  return url\r\n    .replace(/^https?:\\/\\//, '')\r\n    .replace(/^www\\./, '')\r\n    .replace(/\\/$/, ''); // Remove trailing slash\r\n}\r\n\r\nexport interface Gemini25DesignInput {\r\n  businessType: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  imageText: string;\r\n  brandProfile: BrandProfile;\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  };\r\n  artifactInstructions?: string;\r\n  designReferences?: string[]; // Base64 encoded reference images\r\n  includePeopleInDesigns?: boolean; // Control whether designs should include people (default: true)\r\n  useLocalLanguage?: boolean; // Control whether to use local language in text (default: false)\r\n}\r\n\r\nexport interface Gemini25DesignResult {\r\n  imageUrl: string;\r\n  designSpecs: any;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n  model: string;\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifications using Gemini 2.5\r\n */\r\nexport async function generateDesignSpecs(\r\n  input: Gemini25DesignInput\r\n): Promise<any> {\r\n  const startTime = Date.now();\r\n\r\n  try {\r\n\r\n    const designPrompt = `You are an expert graphic designer and brand strategist with deep expertise in 2024-2025 design trends, visual design, color theory, typography, and brand identity. Create a comprehensive ultra-modern design specification for a ${input.platform} post.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.brandProfile.businessName}\r\n- Industry: ${input.businessType}\r\n- Target Audience: ${input.brandProfile.targetAudience}\r\n- Brand Voice: ${input.brandProfile.writingTone}\r\n- Services: ${Array.isArray(input.brandProfile.services) ? input.brandProfile.services.join(', ') : input.brandProfile.services || 'Various services'}\r\n\r\nBRAND COLORS:\r\n- Primary Color: ${input.brandProfile.primaryColor}\r\n- Accent Color: ${input.brandProfile.accentColor}\r\n- Background Color: ${input.brandProfile.backgroundColor}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Platform: ${input.platform} (1080x1080px)\r\n- Visual Style: ${input.visualStyle} with cutting-edge 2024-2025 trends\r\n- Text to Include: \"${input.imageText}\"\r\n- Brand Consistency: ${input.brandConsistency?.strictConsistency ? 'Strict' : 'Flexible'}\r\n\r\nMODERN DESIGN TRENDS TO IMPLEMENT:\r\n- Glassmorphism: Semi-transparent backgrounds with blur effects\r\n- Neumorphism: Subtle shadows and highlights for depth\r\n- Bold typography: Oversized, modern fonts with creative spacing\r\n- Gradient meshes: Complex, multi-directional gradients\r\n- Organic shapes: Fluid, natural forms mixed with geometric elements\r\n- Modern color palettes: Vibrant, saturated colors with high contrast\r\n- Contemporary layouts: Asymmetrical, dynamic compositions\r\n- Advanced shadows: Soft, realistic multi-layered shadows\r\n- Modern iconography: Minimal, line-based icons\r\n- Subtle textures: Noise, grain, or organic patterns\r\n\r\n${input.artifactInstructions ? `SPECIAL INSTRUCTIONS: ${input.artifactInstructions}` : ''}\r\n\r\nPlease create a detailed design specification that includes:\r\n\r\n1. **LAYOUT COMPOSITION:**\r\n   - Overall layout structure and grid system\r\n   - Visual hierarchy and focal points\r\n   - Text placement and sizing\r\n   - Logo/brand element positioning\r\n   - White space and balance\r\n\r\n2. **COLOR PALETTE - MAXIMUM 3 COLORS ONLY, NO LINES:**\r\n   - Primary: ${input.brandProfile.primaryColor} (DOMINANT 60-70% usage)\r\n   - Accent: ${input.brandProfile.accentColor} (HIGHLIGHTS 20-30% usage)\r\n   - Background: ${input.brandProfile.backgroundColor} (BASE 10-20% usage)\r\n   - Text: High contrast white or black only for readability\r\n   - ABSOLUTE LIMITS: These 3 colors only - NO 4th color allowed, NO LINES\r\n   - FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers\r\n\r\n3. **TYPOGRAPHY:**\r\n   - Font families and weights\r\n   - Text sizes and line heights\r\n   - Text alignment and spacing\r\n   - Hierarchy (headlines, subheads, body)\r\n\r\n4. **VISUAL ELEMENTS:**\r\n   - Geometric shapes and patterns\r\n   - Icons or illustrations needed\r\n   - Background textures or effects\r\n   - Border and frame elements\r\n   - Shadow and depth effects\r\n\r\n5. **BRAND INTEGRATION:**\r\n   - Logo treatment and placement\r\n   - Brand color application\r\n   - Consistent visual language\r\n   - Brand personality expression\r\n\r\n6. **TECHNICAL SPECS:**\r\n   - Exact dimensions and positioning\r\n   - Color codes (hex, RGB)\r\n   - Export settings and formats\r\n   - Responsive considerations\r\n\r\nFormat your response as a detailed JSON object with all specifications clearly organized. Be specific with measurements, colors, and positioning.`;\r\n\r\n    const response = await generateText(designPrompt, {\r\n      model: GEMINI_2_5_MODELS.PRO, // Use Pro for complex design reasoning\r\n      temperature: 0.8, // Higher creativity for design\r\n      maxOutputTokens: 4096 // More tokens for detailed specs\r\n    });\r\n\r\n\r\n    // Parse the JSON response\r\n    let designSpecs;\r\n    try {\r\n      // Extract JSON from the response\r\n      const jsonMatch = response.text.match(/\\{[\\s\\S]*\\}/);\r\n      if (jsonMatch) {\r\n        designSpecs = JSON.parse(jsonMatch[0]);\r\n      } else {\r\n        // Fallback: create structured specs from text\r\n        designSpecs = parseDesignSpecsFromText(response.text, input);\r\n      }\r\n    } catch (parseError) {\r\n      designSpecs = parseDesignSpecsFromText(response.text, input);\r\n    }\r\n\r\n    return designSpecs;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Gemini 2.5 design specs generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate complete design using REAL AI image generation with Gemini 2.0 Flash\r\n * NO MORE HARDCODED SVG TEMPLATES - ONLY REAL AI GENERATION\r\n */\r\nexport async function generateEnhancedDesign(\r\n  input: Gemini25DesignInput\r\n): Promise<Gemini25DesignResult> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = ['Gemini 2.5 Flash Image Preview Generation', 'Professional Design Principles', 'Brand Integration'];\r\n\r\n  try {\r\n\r\n    // Use the superior Gemini 2.5 Flash Image Preview generation\r\n    const { generateCreativeAsset } = await import('@/ai/flows/generate-creative-asset');\r\n\r\n    // Build comprehensive AI prompt for image generation\r\n    const imagePrompt = buildComprehensiveImagePrompt(input);\r\n    enhancementsApplied.push('Comprehensive AI Prompting');\r\n\r\n\r\n    // Generate image with Gemini 2.5 Flash Image Preview (superior text rendering)\r\n    const creativeResult = await generateCreativeAsset({\r\n      prompt: imagePrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: input.brandProfile,\r\n      maskDataUrl: null,\r\n      preferredModel: 'gemini-2.5-flash-image-preview'\r\n    });\r\n\r\n    const imageUrl = creativeResult.imageUrl;\r\n    if (!imageUrl) {\r\n      throw new Error('No image URL returned from Gemini 2.5 Flash Image Preview');\r\n    }\r\n\r\n    enhancementsApplied.push(\r\n      'Gemini 2.5 Flash Image Preview HD Generation',\r\n      'Ultra-High Quality Settings',\r\n      'Perfect Text Rendering',\r\n      'Professional Design Generation',\r\n      'Brand Color Compliance',\r\n      'Platform Optimization'\r\n    );\r\n\r\n    const result: Gemini25DesignResult = {\r\n      imageUrl,\r\n      designSpecs: { prompt: imagePrompt }, // Store the prompt as specs\r\n      qualityScore: 9.5, // High quality score for real AI generation\r\n      enhancementsApplied,\r\n      processingTime: Date.now() - startTime,\r\n      model: 'gemini-2.0-flash-image'\r\n    };\r\n\r\n\r\n    return result;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Real AI enhanced design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * Parse design specifications from text response\r\n */\r\nfunction parseDesignSpecsFromText(text: string, input: Gemini25DesignInput): any {\r\n  // Extract key information from the text response\r\n  const colorRegex = /#[0-9A-Fa-f]{6}/g;\r\n  const colors = text.match(colorRegex) || [];\r\n\r\n  return {\r\n    layout: {\r\n      style: input.visualStyle || 'modern-professional',\r\n      dimensions: { width: 1080, height: 1080 },\r\n      textPlacement: 'center'\r\n    },\r\n    colors: {\r\n      primary: input.brandProfile.primaryColor || colors[0] || '#1e40af',\r\n      secondary: input.brandProfile.accentColor || colors[1] || '#3b82f6',\r\n      background: input.brandProfile.backgroundColor || '#ffffff',\r\n      text: '#333333'\r\n    },\r\n    typography: {\r\n      headline: { size: 36, weight: 'bold', family: 'Arial, sans-serif' },\r\n      subheadline: { size: 24, weight: 'normal', family: 'Arial, sans-serif' },\r\n      body: { size: 16, weight: 'normal', family: 'Arial, sans-serif' }\r\n    },\r\n    elements: {\r\n      logo: { position: 'top-left', size: 80 },\r\n      shapes: ['gradient-background', 'accent-shapes'],\r\n      effects: ['subtle-shadow', 'modern-gradient']\r\n    },\r\n    concept: text.substring(0, 200) + '...'\r\n  };\r\n}\r\n\r\n/**\r\n * Create DYNAMIC SVG design that actually uses the AI specifications\r\n * This replaces the hardcoded template with spec-driven generation\r\n */\r\nasync function createDynamicSVGFromSpecs(specs: any, input: Gemini25DesignInput): Promise<string> {\r\n  const { colors = {}, layout = {}, typography = {}, elements = {} } = specs || {};\r\n  const { width = 1080, height = 1080 } = layout?.dimensions || {};\r\n\r\n  // Extract design style from specs\r\n  const designStyle = layout?.style || input.visualStyle || 'modern';\r\n  const layoutType = layout?.textPlacement || 'center';\r\n\r\n  // Dynamic color palette based on specs and brand\r\n  const primaryColor = colors.primary || input.brandProfile.primaryColor || '#6366f1';\r\n  const secondaryColor = colors.secondary || input.brandProfile.accentColor || '#8b5cf6';\r\n  const backgroundColor = colors.background || input.brandProfile.backgroundColor || '#ffffff';\r\n  const textColor = colors.text || '#1f2937';\r\n\r\n  // Parse text content dynamically\r\n  const textLines = input.imageText.split('\\n').filter(line => line.trim());\r\n  const mainText = textLines[0] || input.brandProfile.businessName || 'Business';\r\n  const subText = textLines[1] || '';\r\n  const ctaText = textLines[2] || 'Learn More';\r\n\r\n  // Generate layout based on business type and style\r\n  return generateLayoutBasedOnSpecs(designStyle, {\r\n    width,\r\n    height,\r\n    primaryColor,\r\n    secondaryColor,\r\n    backgroundColor,\r\n    textColor,\r\n    mainText,\r\n    subText,\r\n    ctaText,\r\n    businessType: input.businessType,\r\n    brandName: input.brandProfile.businessName,\r\n    elements: elements.shapes || [],\r\n    typography\r\n  });\r\n}\r\n\r\n/**\r\n * Generate different layouts based on design specifications\r\n */\r\nfunction generateLayoutBasedOnSpecs(designStyle: string, params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, backgroundColor, textColor, mainText, subText, ctaText, businessType, brandName, elements, typography } = params;\r\n\r\n  // Choose layout based on design style and business type\r\n  if (designStyle.includes('minimal') || businessType.includes('tech')) {\r\n    return generateMinimalLayout(params);\r\n  } else if (designStyle.includes('bold') || businessType.includes('fitness') || businessType.includes('food')) {\r\n    return generateBoldLayout(params);\r\n  } else if (designStyle.includes('elegant') || businessType.includes('fashion') || businessType.includes('beauty')) {\r\n    return generateElegantLayout(params);\r\n  } else if (businessType.includes('medical') || businessType.includes('professional')) {\r\n    return generateProfessionalLayout(params);\r\n  } else {\r\n    return generateModernLayout(params);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate minimal tech-focused layout\r\n */\r\nfunction generateMinimalLayout(params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <linearGradient id=\"minimalGrad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:${primaryColor};stop-opacity:0.1\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${secondaryColor};stop-opacity:0.05\" />\r\n        </linearGradient>\r\n      </defs>\r\n\r\n      <!-- Clean background -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"#ffffff\" />\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#minimalGrad)\" />\r\n\r\n      <!-- Minimal geometric accent -->\r\n      <rect x=\"80\" y=\"80\" width=\"4\" height=\"200\" fill=\"${primaryColor}\" />\r\n      <rect x=\"80\" y=\"800\" width=\"200\" height=\"4\" fill=\"${primaryColor}\" />\r\n\r\n      <!-- Content area -->\r\n      <g transform=\"translate(540, 400)\">\r\n        <!-- Brand initial -->\r\n        <rect x=\"-25\" y=\"-120\" width=\"50\" height=\"50\" fill=\"${primaryColor}\" />\r\n        <text x=\"0\" y=\"-85\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"20\" font-weight=\"600\">\r\n          ${brandName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Main text -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"48\" font-weight=\"300\" letter-spacing=\"-0.02em\">\r\n          ${mainText}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <text x=\"0\" y=\"60\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"16\" font-weight=\"400\" opacity=\"0.7\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Minimal CTA -->\r\n        <text x=\"0\" y=\"140\" text-anchor=\"middle\" fill=\"${primaryColor}\" font-family=\"system-ui\" font-size=\"14\" font-weight=\"500\" text-decoration=\"underline\">\r\n          ${ctaText}\r\n        </text>\r\n      </g>\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Generate bold, energetic layout for fitness/food businesses\r\n */\r\nfunction generateBoldLayout(params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <radialGradient id=\"boldGrad\" cx=\"50%\" cy=\"50%\" r=\"70%\">\r\n          <stop offset=\"0%\" style=\"stop-color:${primaryColor};stop-opacity:0.8\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${secondaryColor};stop-opacity:0.4\" />\r\n        </radialGradient>\r\n        <filter id=\"boldShadow\">\r\n          <feDropShadow dx=\"0\" dy=\"8\" stdDeviation=\"16\" flood-color=\"rgba(0,0,0,0.3)\"/>\r\n        </filter>\r\n      </defs>\r\n\r\n      <!-- Dynamic background -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#boldGrad)\" />\r\n\r\n      <!-- Bold geometric shapes -->\r\n      <polygon points=\"0,0 300,0 200,200\" fill=\"${primaryColor}\" opacity=\"0.2\" />\r\n      <polygon points=\"1080,1080 780,1080 880,880\" fill=\"${secondaryColor}\" opacity=\"0.2\" />\r\n\r\n      <!-- Content area -->\r\n      <g transform=\"translate(540, 540)\">\r\n        <!-- Bold brand circle -->\r\n        <circle cx=\"0\" cy=\"-150\" r=\"50\" fill=\"${primaryColor}\" filter=\"url(#boldShadow)\" />\r\n        <text x=\"0\" y=\"-135\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"32\" font-weight=\"900\">\r\n          ${brandName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Bold main text -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"56\" font-weight=\"900\" letter-spacing=\"-0.03em\" filter=\"url(#boldShadow)\">\r\n          ${mainText.toUpperCase()}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <text x=\"0\" y=\"80\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"20\" font-weight=\"600\" opacity=\"0.9\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Bold CTA button -->\r\n        <rect x=\"-100\" y=\"120\" width=\"200\" height=\"60\" rx=\"30\" fill=\"white\" filter=\"url(#boldShadow)\" />\r\n        <text x=\"0\" y=\"160\" text-anchor=\"middle\" fill=\"${primaryColor}\" font-family=\"system-ui\" font-size=\"18\" font-weight=\"700\">\r\n          ${ctaText.toUpperCase()}\r\n        </text>\r\n      </g>\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Generate elegant layout for fashion/beauty businesses\r\n */\r\nfunction generateElegantLayout(params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <linearGradient id=\"elegantGrad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:#f8f9fa;stop-opacity:1\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${primaryColor};stop-opacity:0.1\" />\r\n        </linearGradient>\r\n      </defs>\r\n\r\n      <!-- Elegant background -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#elegantGrad)\" />\r\n\r\n      <!-- Elegant decorative elements -->\r\n      <circle cx=\"150\" cy=\"150\" r=\"80\" fill=\"none\" stroke=\"${primaryColor}\" stroke-width=\"1\" opacity=\"0.3\" />\r\n      <circle cx=\"930\" cy=\"930\" r=\"60\" fill=\"none\" stroke=\"${secondaryColor}\" stroke-width=\"1\" opacity=\"0.3\" />\r\n\r\n      <!-- Content area -->\r\n      <g transform=\"translate(540, 450)\">\r\n        <!-- Elegant brand mark -->\r\n        <rect x=\"-30\" y=\"-120\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"${primaryColor}\" stroke-width=\"2\" />\r\n        <text x=\"0\" y=\"-75\" text-anchor=\"middle\" fill=\"${primaryColor}\" font-family=\"serif\" font-size=\"24\" font-weight=\"400\" font-style=\"italic\">\r\n          ${brandName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Elegant main text -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"serif\" font-size=\"44\" font-weight=\"300\" letter-spacing=\"0.02em\">\r\n          ${mainText}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <text x=\"0\" y=\"60\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"serif\" font-size=\"16\" font-weight=\"300\" opacity=\"0.8\" font-style=\"italic\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Elegant CTA -->\r\n        <rect x=\"-80\" y=\"120\" width=\"160\" height=\"40\" fill=\"none\" stroke=\"${primaryColor}\" stroke-width=\"1\" />\r\n        <text x=\"0\" y=\"145\" text-anchor=\"middle\" fill=\"${primaryColor}\" font-family=\"serif\" font-size=\"14\" font-weight=\"400\" letter-spacing=\"0.1em\">\r\n          ${ctaText.toUpperCase()}\r\n        </text>\r\n      </g>\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Generate professional layout for medical/corporate businesses\r\n */\r\nfunction generateProfessionalLayout(params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <linearGradient id=\"profGrad\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:#ffffff;stop-opacity:1\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${primaryColor};stop-opacity:0.05\" />\r\n        </linearGradient>\r\n      </defs>\r\n\r\n      <!-- Professional background -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#profGrad)\" />\r\n\r\n      <!-- Professional header bar -->\r\n      <rect x=\"0\" y=\"0\" width=\"100%\" height=\"120\" fill=\"${primaryColor}\" />\r\n\r\n      <!-- Content area -->\r\n      <g transform=\"translate(540, 500)\">\r\n        <!-- Professional logo area -->\r\n        <rect x=\"-40\" y=\"-200\" width=\"80\" height=\"80\" fill=\"white\" />\r\n        <text x=\"0\" y=\"-145\" text-anchor=\"middle\" fill=\"${primaryColor}\" font-family=\"system-ui\" font-size=\"28\" font-weight=\"600\">\r\n          ${brandName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Professional main text -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"40\" font-weight=\"600\" letter-spacing=\"-0.01em\">\r\n          ${mainText}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <text x=\"0\" y=\"50\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"18\" font-weight=\"400\" opacity=\"0.8\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Professional CTA -->\r\n        <rect x=\"-120\" y=\"100\" width=\"240\" height=\"50\" fill=\"${primaryColor}\" />\r\n        <text x=\"0\" y=\"130\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"16\" font-weight=\"500\">\r\n          ${ctaText}\r\n        </text>\r\n      </g>\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Generate modern default layout\r\n */\r\nfunction generateModernLayout(params: any): string {\r\n  const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <linearGradient id=\"modernGrad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:${primaryColor};stop-opacity:0.1\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${secondaryColor};stop-opacity:0.05\" />\r\n        </linearGradient>\r\n      </defs>\r\n\r\n      <!-- Modern background -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"#ffffff\" />\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#modernGrad)\" />\r\n\r\n      <!-- Modern accent shapes -->\r\n      <circle cx=\"200\" cy=\"200\" r=\"100\" fill=\"${primaryColor}\" opacity=\"0.1\" />\r\n      <rect x=\"700\" y=\"700\" width=\"200\" height=\"200\" rx=\"20\" fill=\"${secondaryColor}\" opacity=\"0.1\" />\r\n\r\n      <!-- Content area -->\r\n      <g transform=\"translate(540, 450)\">\r\n        <!-- Modern brand mark -->\r\n        <circle cx=\"0\" cy=\"-100\" r=\"40\" fill=\"${primaryColor}\" />\r\n        <text x=\"0\" y=\"-90\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"24\" font-weight=\"600\">\r\n          ${brandName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Modern main text -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"46\" font-weight=\"700\" letter-spacing=\"-0.02em\">\r\n          ${mainText}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <text x=\"0\" y=\"60\" text-anchor=\"middle\" fill=\"${textColor}\" font-family=\"system-ui\" font-size=\"18\" font-weight=\"400\" opacity=\"0.8\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Modern CTA -->\r\n        <rect x=\"-100\" y=\"120\" width=\"200\" height=\"50\" rx=\"25\" fill=\"${primaryColor}\" />\r\n        <text x=\"0\" y=\"150\" text-anchor=\"middle\" fill=\"white\" font-family=\"system-ui\" font-size=\"16\" font-weight=\"600\">\r\n          ${ctaText}\r\n        </text>\r\n      </g>\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Create SVG design from specifications (LEGACY - keeping for compatibility)\r\n */\r\nasync function createSVGFromSpecs(specs: any, input: Gemini25DesignInput): Promise<string> {\r\n  const { colors = {}, layout = {}, typography = {}, elements = {} } = specs || {};\r\n  const { width = 1080, height = 1080 } = layout?.dimensions || {};\r\n\r\n  // Modern color palette with enhanced gradients\r\n  const primaryColor = colors.primary || input.brandProfile.primaryColor || '#6366f1';\r\n  const secondaryColor = colors.secondary || input.brandProfile.accentColor || '#8b5cf6';\r\n  const accentColor = '#f59e0b';\r\n  const textColor = colors.text || '#1f2937';\r\n  const glassColor = 'rgba(255,255,255,0.1)';\r\n\r\n  // Parse image text components\r\n  const textLines = input.imageText.split('\\n').filter(line => line.trim());\r\n  const mainText = textLines[0] || 'Modern Business';\r\n  const subText = textLines[1] || '';\r\n  const ctaText = textLines[2] || 'Get Started';\r\n\r\n  return `\r\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <defs>\r\n        <!-- Modern gradient definitions -->\r\n        <linearGradient id=\"modernGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:${primaryColor};stop-opacity:1\" />\r\n          <stop offset=\"50%\" style=\"stop-color:${secondaryColor};stop-opacity:0.9\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${accentColor};stop-opacity:0.8\" />\r\n        </linearGradient>\r\n\r\n        <radialGradient id=\"glowGradient\" cx=\"50%\" cy=\"30%\" r=\"70%\">\r\n          <stop offset=\"0%\" style=\"stop-color:${primaryColor};stop-opacity:0.3\" />\r\n          <stop offset=\"100%\" style=\"stop-color:${secondaryColor};stop-opacity:0.1\" />\r\n        </radialGradient>\r\n\r\n        <linearGradient id=\"glassmorphism\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n          <stop offset=\"0%\" style=\"stop-color:rgba(255,255,255,0.25);stop-opacity:1\" />\r\n          <stop offset=\"100%\" style=\"stop-color:rgba(255,255,255,0.05);stop-opacity:1\" />\r\n        </linearGradient>\r\n\r\n        <!-- Modern shadow filters -->\r\n        <filter id=\"modernShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\r\n          <feDropShadow dx=\"0\" dy=\"20\" stdDeviation=\"25\" flood-color=\"rgba(0,0,0,0.1)\"/>\r\n        </filter>\r\n\r\n        <filter id=\"glowEffect\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\r\n          <feGaussianBlur stdDeviation=\"8\" result=\"coloredBlur\"/>\r\n          <feMerge>\r\n            <feMergeNode in=\"coloredBlur\"/>\r\n            <feMergeNode in=\"SourceGraphic\"/>\r\n          </feMerge>\r\n        </filter>\r\n\r\n        <filter id=\"glassmorphismBlur\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\r\n          <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"10\"/>\r\n        </filter>\r\n      </defs>\r\n\r\n      <!-- Modern background with gradient mesh -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#modernGradient)\" />\r\n\r\n      <!-- Glow overlay -->\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#glowGradient)\" />\r\n\r\n      <!-- Modern geometric elements -->\r\n      <circle cx=\"900\" cy=\"200\" r=\"150\" fill=\"${primaryColor}\" opacity=\"0.1\" filter=\"url(#glowEffect)\" />\r\n      <circle cx=\"200\" cy=\"800\" r=\"100\" fill=\"${secondaryColor}\" opacity=\"0.15\" />\r\n\r\n      <!-- Organic shapes -->\r\n      <path d=\"M0,0 Q300,100 600,50 T1080,80 L1080,0 Z\" fill=\"${accentColor}\" opacity=\"0.08\" />\r\n      <path d=\"M0,1080 Q400,950 800,1000 T1080,950 L1080,1080 Z\" fill=\"${primaryColor}\" opacity=\"0.12\" />\r\n\r\n      <!-- Main glassmorphism card -->\r\n      <rect x=\"120\" y=\"250\" width=\"840\" height=\"580\" rx=\"32\"\r\n            fill=\"url(#glassmorphism)\"\r\n            stroke=\"rgba(255,255,255,0.2)\"\r\n            stroke-width=\"1\"\r\n            filter=\"url(#modernShadow)\" />\r\n\r\n      <!-- Content area with modern layout -->\r\n      <g transform=\"translate(540, 400)\">\r\n        <!-- Brand mark -->\r\n        <circle cx=\"0\" cy=\"-80\" r=\"35\" fill=\"${primaryColor}\" filter=\"url(#glowEffect)\" />\r\n        <text x=\"0\" y=\"-70\" text-anchor=\"middle\" fill=\"white\"\r\n              font-family=\"system-ui, -apple-system, sans-serif\"\r\n              font-size=\"24\" font-weight=\"700\">\r\n          ${input.brandProfile.businessName?.charAt(0) || 'B'}\r\n        </text>\r\n\r\n        <!-- Main headline with modern typography -->\r\n        <text x=\"0\" y=\"0\" text-anchor=\"middle\" fill=\"${textColor}\"\r\n              font-family=\"system-ui, -apple-system, sans-serif\"\r\n              font-size=\"42\" font-weight=\"800\" letter-spacing=\"-0.02em\">\r\n          ${mainText}\r\n        </text>\r\n\r\n        ${subText ? `\r\n        <!-- Subheadline -->\r\n        <text x=\"0\" y=\"50\" text-anchor=\"middle\" fill=\"${textColor}\"\r\n              font-family=\"system-ui, -apple-system, sans-serif\"\r\n              font-size=\"18\" font-weight=\"400\" opacity=\"0.8\">\r\n          ${subText}\r\n        </text>\r\n        ` : ''}\r\n\r\n        <!-- Modern CTA button -->\r\n        <g transform=\"translate(0, 120)\">\r\n          <rect x=\"-120\" y=\"-25\" width=\"240\" height=\"50\" rx=\"25\"\r\n                fill=\"${primaryColor}\" filter=\"url(#modernShadow)\" />\r\n          <rect x=\"-120\" y=\"-25\" width=\"240\" height=\"50\" rx=\"25\"\r\n                fill=\"url(#glowGradient)\" opacity=\"0.3\" />\r\n          <text x=\"0\" y=\"5\" text-anchor=\"middle\" fill=\"white\"\r\n                font-family=\"system-ui, -apple-system, sans-serif\"\r\n                font-size=\"16\" font-weight=\"600\">\r\n            ${ctaText}\r\n          </text>\r\n        </g>\r\n      </g>\r\n\r\n      <!-- Modern decorative elements -->\r\n      <circle cx=\"150\" cy=\"150\" r=\"3\" fill=\"${accentColor}\" opacity=\"0.6\" />\r\n      <circle cx=\"930\" cy=\"930\" r=\"4\" fill=\"${primaryColor}\" opacity=\"0.5\" />\r\n      <circle cx=\"200\" cy=\"900\" r=\"2\" fill=\"${secondaryColor}\" opacity=\"0.7\" />\r\n\r\n      <!-- Subtle grid pattern -->\r\n      <defs>\r\n        <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\r\n          <path d=\"M 40 0 L 0 0 0 40\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/>\r\n        </pattern>\r\n      </defs>\r\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\r\n    </svg>\r\n  `;\r\n}\r\n\r\n/**\r\n * Build comprehensive AI image prompt for Imagen 4\r\n * This creates extremely detailed prompts that leverage Imagen 4's instruction-following capabilities\r\n * for high-quality, professional designs with perfect text readability\r\n */\r\nfunction buildComprehensiveImagePrompt(input: Gemini25DesignInput): string {\r\n  const { businessType, platform, visualStyle, imageText, brandProfile, includePeopleInDesigns = true, useLocalLanguage = false } = input;\r\n\r\n  // People inclusion logic with creative variety\r\n  const peopleInstructions = includePeopleInDesigns\r\n    ? `- CREATIVE VARIETY: Include diverse, authentic people in VARIED settings:\r\n  * Professional environments (offices, studios, workshops)\r\n  * Lifestyle settings (homes, cafes, outdoor spaces, community areas)\r\n  * Industry-specific contexts (retail spaces, service areas, creative studios)\r\n  * Cultural celebrations and modern community gatherings\r\n  * Contemporary urban settings (co-working spaces, tech hubs)\r\n  * Traditional meets modern (cultural heritage with contemporary life)\r\n- Show real people in natural, engaging situations that vary by design\r\n- Ensure representation reflects the target demographic and cultural values\r\n- Use photography styles ranging from candid to professional to artistic\r\n- Vary the mood: energetic, calm, celebratory, focused, collaborative`\r\n    : `- Focus on products, services, and brand elements without people\r\n- Use lifestyle imagery, product showcases, and brand-focused visuals\r\n- Create compelling designs through typography, graphics, and product photography\r\n- Maintain professional aesthetic through clean, modern design elements`;\r\n\r\n  // Simplified, focused prompt that works better for AI image generation\r\n  const prompt = `Create a stunning, professional ${platform} social media design for ${brandProfile.businessName || businessType}.\r\n\r\nBUSINESS: ${brandProfile.businessName || businessType} (${businessType})\r\nTEXT TO INCLUDE: \"${imageText}\"\r\nSTYLE: ${visualStyle}, modern, clean, professional\r\n\r\nMAXIMUM 3 COLORS ONLY, NO LINES - BRAND COLORS:\r\n- Primary: ${brandProfile.primaryColor || '#2563eb'} (DOMINANT 60-70%)\r\n- Accent: ${brandProfile.accentColor || '#7c3aed'} (HIGHLIGHTS 20-30%)\r\n- Background: ${brandProfile.backgroundColor || '#ffffff'} (BASE 10-20%)\r\n- ABSOLUTE LIMITS: These 3 colors only - NO 4th color allowed, NO LINES\r\n- FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers\r\n\r\nVISUAL APPROACH:\r\n${peopleInstructions}\r\n\r\nLANGUAGE INSTRUCTIONS:\r\n${useLocalLanguage ? `- You may use local language text when 100% certain of accuracy\r\n- Mix local language with English naturally (1-2 local words maximum)\r\n- Only use commonly known local words that add cultural connection` : `- USE ONLY ENGLISH for all text in the design\r\n- Do not use any local language words or phrases\r\n- Keep all text elements in clear, professional English`}\r\n\r\nCREATIVE DESIGN VARIETY:\r\n- DESIGN STYLES (rotate for variety):\r\n  * Ultra-modern minimalist with bold typography\r\n  * Dynamic geometric patterns with vibrant colors\r\n  * Sophisticated gradient overlays with premium feel\r\n  * Clean photography-focused with subtle overlays\r\n  * Artistic illustration style with contemporary elements\r\n  * Bold graphic design with strong visual hierarchy\r\n  * Elegant luxury aesthetic with refined typography\r\n\r\n- LAYOUT VARIATIONS:\r\n  * Split-screen compositions (text/visual balance)\r\n  * Centered hero with surrounding elements\r\n  * Asymmetrical modern layouts with visual balance\r\n  * Grid-based structured designs\r\n  * Organic flowing compositions\r\n  * Layered depth with foreground/background elements\r\n\r\nREQUIREMENTS:\r\n- Square 1:1 aspect ratio for ${platform}\r\n- High-quality, professional design\r\n- Clear, readable text with good contrast\r\n- Modern typography (sans-serif fonts)\r\n- Clean composition with proper spacing\r\n- Brand colors prominently featured\r\n- ${visualStyle} aesthetic\r\n- Perfect for ${businessType} business\r\n${brandProfile.websiteUrl ? `- Website available for CTAs when contextually appropriate: ${cleanWebsiteUrl(brandProfile.websiteUrl)}` : ''}\r\n\r\nSTYLE DETAILS:\r\n- Clean, modern layout with creative variety\r\n- Professional appearance with artistic flair\r\n- Eye-catching but not cluttered\r\n- Perfect text readability\r\n- Brand-appropriate imagery with creative contexts\r\n- High contrast for mobile viewing\r\n- Sophisticated color harmony with dynamic elements\r\n\r\nCreate a beautiful, professional design that represents ${brandProfile.businessName || businessType} perfectly.`;\r\n\r\n  return prompt;\r\n}\r\n\r\n/**\r\n * Build comprehensive brand color system with specific usage instructions\r\n */\r\nfunction buildBrandColorSystem(brandProfile: BrandProfile): string {\r\n  const primaryColor = brandProfile.primaryColor || '#2563eb';\r\n  const accentColor = brandProfile.accentColor || '#7c3aed';\r\n  const backgroundColor = brandProfile.backgroundColor || '#ffffff';\r\n\r\n  return `- Primary Brand Color: ${primaryColor} (DOMINANT 60-70% of design - main brand elements, headlines, key accents)\r\n- Secondary/Accent Color: ${accentColor} (HIGHLIGHTS 20-30% of design - call-to-action elements, highlights, buttons)\r\n- Background Color: ${backgroundColor} (BASE 10-20% of design - primary background)\r\n- MAXIMUM 3 COLORS TOTAL: These are the ONLY colors allowed in the entire design\r\n- ABSOLUTE LIMITS: No 4th, 5th, or additional colors beyond these 3, NO LINES of any kind\r\n- FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers/linear elements\r\n- STRICT RULES: Maximum 3 colors, no lines in entire design - NO exceptions\r\n- Text colors: Use high contrast white or black text only when needed for readability`;\r\n}\r\n\r\n/**\r\n * Build advanced typography system for perfect readability\r\n */\r\nfunction buildTypographySystem(businessType: string, platform: string): string {\r\n  const isLandscape = platform.toLowerCase() === 'twitter' || platform.toLowerCase() === 'linkedin';\r\n\r\n  return `- Headline: Large, bold, highly readable font (${isLandscape ? '48-64px' : '36-48px'})\r\n- Subheadline: Medium weight, clear font (${isLandscape ? '24-32px' : '20-28px'})\r\n- Body Text: Clean, readable font (${isLandscape ? '16-20px' : '14-18px'})\r\n- Font Family: Modern sans-serif (Helvetica, Arial, Roboto, or similar)\r\n- Text Color: High contrast against background (dark text on light backgrounds, light text on dark backgrounds)\r\n- Text Effects: Subtle shadows, outlines, or background overlays to ensure readability\r\n- Letter Spacing: Optimal spacing for digital readability\r\n- Line Height: 1.2-1.4 for optimal readability`;\r\n}\r\n\r\n/**\r\n * Build layout guidance for optimal composition\r\n */\r\nfunction buildLayoutGuidance(platform: string, businessType: string): string {\r\n  const isLandscape = platform.toLowerCase() === 'twitter' || platform.toLowerCase() === 'linkedin';\r\n\r\n  return `- Composition: ${isLandscape ? 'Horizontal layout with left-right or center focus' : 'Vertical layout with top-bottom hierarchy'}\r\n- Visual Hierarchy: Clear focal points with proper element sizing\r\n- White Space: Generous use of negative space for clean, professional appearance\r\n- Alignment: Perfect alignment of all elements using invisible grid system\r\n- Balance: Harmonious distribution of visual weight across the design\r\n- Focus Areas: Clear primary and secondary focus points\r\n- Margins: Adequate padding from edges (${isLandscape ? '60-80px' : '40-60px'} minimum)\r\n- Grid System: Use professional grid-based layout for perfect alignment`;\r\n}\r\n\r\n/**\r\n * Get platform-specific specifications for image generation\r\n */\r\nfunction getPlatformSpecifications(platform: string) {\r\n  const specs = {\r\n    instagram: {\r\n      name: 'Instagram',\r\n      dimensions: '1080x1080px (square)',\r\n      description: 'Instagram feed post optimized for mobile viewing'\r\n    },\r\n    facebook: {\r\n      name: 'Facebook',\r\n      dimensions: '1200x630px (landscape)',\r\n      description: 'Facebook post optimized for news feed'\r\n    },\r\n    twitter: {\r\n      name: 'Twitter/X',\r\n      dimensions: '1200x675px (landscape)',\r\n      description: 'Twitter post optimized for timeline viewing'\r\n    },\r\n    linkedin: {\r\n      name: 'LinkedIn',\r\n      dimensions: '1200x627px (landscape)',\r\n      description: 'LinkedIn post optimized for professional networking'\r\n    }\r\n  };\r\n\r\n  return specs[platform.toLowerCase() as keyof typeof specs] || specs.instagram;\r\n}\r\n\r\n/**\r\n * Get business type-specific design guidance with industry elements\r\n */\r\nfunction getBusinessTypeGuidance(businessType: string): string {\r\n  const guidance = {\r\n    'restaurant': `- Warm, appetizing color palette (oranges, reds, warm browns)\r\n- Food-related imagery and culinary elements\r\n- Inviting, cozy atmosphere in design\r\n- Focus on creating hunger appeal and comfort\r\n- Use textures that evoke freshness and quality`,\r\n\r\n    'fitness': `- Energetic, dynamic color palette (vibrant oranges, reds, blues)\r\n- Strong, powerful visual elements\r\n- Motion and energy in design composition\r\n- Emphasis on strength, vitality, and achievement\r\n- Athletic and motivational visual language`,\r\n\r\n    'technology': `- Clean, futuristic design with tech-inspired elements\r\n- Cool color palette (blues, grays, whites)\r\n- Modern geometric shapes and digital aesthetics\r\n- Emphasis on innovation and cutting-edge feel\r\n- Sleek, high-tech visual language`,\r\n\r\n    'healthcare': `- Calming, trustworthy color palette (blues, greens, whites)\r\n- Clean, sterile aesthetic with medical professionalism\r\n- Focus on trust, care, and reliability\r\n- Soothing visual elements that inspire confidence\r\n- Professional medical imagery and symbols`,\r\n\r\n    'education': `- Inspiring, knowledge-focused design\r\n- Warm, encouraging color palette\r\n- Elements that suggest growth and learning\r\n- Professional yet approachable aesthetic\r\n- Symbols of knowledge, growth, and achievement`,\r\n\r\n    'retail': `- Attractive, product-focused design\r\n- Commercial appeal with shopping psychology\r\n- Colors that encourage purchasing decisions\r\n- Focus on quality, value, and desirability\r\n- Professional retail presentation standards`,\r\n\r\n    'finance': `- Professional, trustworthy design language\r\n- Conservative color palette (blues, grays, whites)\r\n- Emphasis on security, stability, and reliability\r\n- Corporate-level visual standards\r\n- Symbols of growth, security, and trust`,\r\n\r\n    'real estate': `- Sophisticated, aspirational design aesthetic\r\n- Premium color palette suggesting luxury and quality\r\n- Focus on lifestyle and investment appeal\r\n- Professional property presentation standards\r\n- Elements suggesting home, security, and success`,\r\n\r\n    'beauty': `- Elegant, attractive design with aesthetic appeal\r\n- Soft, beautiful color palette (pinks, golds, whites)\r\n- Focus on transformation and enhancement\r\n- Luxurious, premium visual language\r\n- Elements suggesting beauty, care, and self-improvement`,\r\n\r\n    'automotive': `- Strong, reliable design with performance appeal\r\n- Bold color palette (reds, blacks, metallics)\r\n- Focus on power, performance, and quality\r\n- Dynamic visual elements suggesting speed and strength\r\n- Professional automotive presentation standards`\r\n  };\r\n\r\n  const type = businessType.toLowerCase();\r\n  for (const [key, value] of Object.entries(guidance)) {\r\n    if (type.includes(key)) {\r\n      return value;\r\n    }\r\n  }\r\n\r\n  return `- Professional, versatile design appropriate for business\r\n- Balanced color palette suitable for general business use\r\n- Clean, modern aesthetic with broad appeal\r\n- Trustworthy and professional appearance\r\n- Industry-neutral visual elements`;\r\n}\r\n\r\n/**\r\n * Get advanced visual style guidance with 2024-2025 trends\r\n */\r\nfunction getAdvancedVisualStyleGuidance(visualStyle: string): string {\r\n  const styles = {\r\n    'modern': `- Clean, minimalist design with contemporary 2024-2025 aesthetics\r\n- Subtle gradients and soft shadows for depth\r\n- Modern geometric shapes and clean lines\r\n- Sophisticated color transitions and overlays\r\n- Contemporary typography with perfect spacing`,\r\n\r\n    'professional': `- Sophisticated, business-appropriate design\r\n- Premium quality appearance with polished finish\r\n- Corporate-level visual standards\r\n- Trustworthy and authoritative visual language\r\n- Executive-level presentation quality`,\r\n\r\n    'creative': `- Artistic and innovative design elements\r\n- Unique compositions with creative flair\r\n- Bold color combinations and artistic effects\r\n- Creative typography and layout experimentation\r\n- Inspiring and visually striking appearance`,\r\n\r\n    'elegant': `- Refined, luxurious aesthetic with premium feel\r\n- Sophisticated typography and perfect spacing\r\n- Subtle, high-end color palette\r\n- Graceful design elements and smooth transitions\r\n- Timeless elegance with modern touches`,\r\n\r\n    'bold': `- Strong visual impact with dynamic energy\r\n- Vibrant, attention-grabbing color schemes\r\n- Powerful typography and dramatic compositions\r\n- High-contrast elements for maximum impact\r\n- Confident and assertive visual language`,\r\n\r\n    'minimalist': `- Ultra-clean design with generous white space\r\n- Minimal elements with maximum impact\r\n- Perfect typography hierarchy and spacing\r\n- Subtle color palette with strategic accents\r\n- Zen-like simplicity with purposeful design`,\r\n\r\n    'playful': `- Fun, engaging design with creative energy\r\n- Bright, cheerful color combinations\r\n- Casual, approachable typography\r\n- Interactive visual elements and patterns\r\n- Youthful and energetic aesthetic`,\r\n\r\n    'luxury': `- Premium, high-end aesthetic with sophisticated appeal\r\n- Rich color palette with metallic accents\r\n- Elegant typography with refined spacing\r\n- Luxurious textures and premium materials feel\r\n- Exclusive, aspirational visual language`\r\n  };\r\n\r\n  const style = visualStyle.toLowerCase();\r\n  for (const [key, value] of Object.entries(styles)) {\r\n    if (style.includes(key)) {\r\n      return value;\r\n    }\r\n  }\r\n\r\n  return `- Modern, professional design with clean aesthetics\r\n- Contemporary visual language with 2024-2025 trends\r\n- Balanced composition with good visual hierarchy\r\n- Sophisticated color palette and typography\r\n- Versatile design suitable for professional use`;\r\n}\r\n\r\n/**\r\n * Get industry-specific visual elements and symbols\r\n */\r\nfunction getIndustrySpecificElements(businessType: string): string {\r\n  const elements = {\r\n    'restaurant': `- Subtle food-related icons or patterns\r\n- Warm lighting effects and cozy atmosphere\r\n- Natural textures (wood, stone) if appropriate\r\n- Appetizing color gradients and warm tones`,\r\n\r\n    'technology': `- Geometric patterns and digital elements\r\n- Circuit board inspired subtle patterns\r\n- Modern icons and tech symbols\r\n- Futuristic lighting effects and gradients`,\r\n\r\n    'healthcare': `- Medical cross or health symbols (subtle)\r\n- Clean, sterile visual elements\r\n- Calming nature elements (leaves, water)\r\n- Professional medical color schemes`,\r\n\r\n    'fitness': `- Dynamic shapes suggesting movement\r\n- Strength and energy visual metaphors\r\n- Athletic-inspired design elements\r\n- Motivational visual language`,\r\n\r\n    'education': `- Book, graduation, or learning symbols (subtle)\r\n- Growth metaphors (trees, arrows, stairs)\r\n- Knowledge-inspired visual elements\r\n- Academic color schemes and typography`,\r\n\r\n    'retail': `- Shopping and commerce visual elements\r\n- Product showcase design principles\r\n- Commercial appeal aesthetics\r\n- Quality and value visual indicators`,\r\n\r\n    'finance': `- Growth charts, arrows, or financial symbols (subtle)\r\n- Professional corporate design elements\r\n- Trust and security visual metaphors\r\n- Conservative, reliable aesthetic choices`,\r\n\r\n    'real estate': `- Home, building, or property symbols (subtle)\r\n- Architectural elements and clean lines\r\n- Luxury and quality visual indicators\r\n- Professional property presentation elements`,\r\n\r\n    'beauty': `- Elegant, aesthetic design elements\r\n- Soft, beautiful patterns and textures\r\n- Luxury and premium visual indicators\r\n- Transformation and enhancement metaphors`,\r\n\r\n    'automotive': `- Speed and performance visual elements\r\n- Dynamic lines and powerful shapes\r\n- Metallic textures and bold contrasts\r\n- Professional automotive presentation standards`\r\n  };\r\n\r\n  const type = businessType.toLowerCase();\r\n  for (const [key, value] of Object.entries(elements)) {\r\n    if (type.includes(key)) {\r\n      return value;\r\n    }\r\n  }\r\n\r\n  return `- Universal business symbols and elements\r\n- Professional geometric patterns\r\n- Versatile design elements suitable for any industry\r\n- Clean, modern visual language`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAGA;;CAEC,GACD,SAAS,gBAAgB,GAAW;IAClC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IACJ,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,UAAU,IAClB,OAAO,CAAC,OAAO,KAAK,wBAAwB;AACjD;AA8BO,eAAe,oBACpB,KAA0B;IAE1B,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QAEF,MAAM,eAAe,CAAC,oOAAoO,EAAE,MAAM,QAAQ,CAAC;;;YAGnQ,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;YAClC,EAAE,MAAM,YAAY,CAAC;mBACd,EAAE,MAAM,YAAY,CAAC,cAAc,CAAC;eACxC,EAAE,MAAM,YAAY,CAAC,WAAW,CAAC;YACpC,EAAE,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,QAAQ,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,MAAM,YAAY,CAAC,QAAQ,IAAI,mBAAmB;;;iBAGrI,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;gBACnC,EAAE,MAAM,YAAY,CAAC,WAAW,CAAC;oBAC7B,EAAE,MAAM,YAAY,CAAC,eAAe,CAAC;;;YAG7C,EAAE,MAAM,QAAQ,CAAC;gBACb,EAAE,MAAM,WAAW,CAAC;oBAChB,EAAE,MAAM,SAAS,CAAC;qBACjB,EAAE,MAAM,gBAAgB,EAAE,oBAAoB,WAAW,WAAW;;;;;;;;;;;;;;AAczF,EAAE,MAAM,oBAAoB,GAAG,CAAC,sBAAsB,EAAE,MAAM,oBAAoB,EAAE,GAAG,GAAG;;;;;;;;;;;;cAY5E,EAAE,MAAM,YAAY,CAAC,YAAY,CAAC;aACnC,EAAE,MAAM,YAAY,CAAC,WAAW,CAAC;iBAC7B,EAAE,MAAM,YAAY,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iJA8B2F,CAAC;QAE9I,MAAM,WAAW,MAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,cAAc;YAChD,OAAO,mIAAA,CAAA,oBAAiB,CAAC,GAAG;YAC5B,aAAa;YACb,iBAAiB,KAAK,iCAAiC;QACzD;QAGA,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,iCAAiC;YACjC,MAAM,YAAY,SAAS,IAAI,CAAC,KAAK,CAAC;YACtC,IAAI,WAAW;gBACb,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;YACvC,OAAO;gBACL,8CAA8C;gBAC9C,cAAc,yBAAyB,SAAS,IAAI,EAAE;YACxD;QACF,EAAE,OAAO,YAAY;YACnB,cAAc,yBAAyB,SAAS,IAAI,EAAE;QACxD;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC1H;AACF;AAMO,eAAe,uBACpB,KAA0B;IAE1B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAgC;QAAC;QAA6C;QAAkC;KAAoB;IAE1I,IAAI;QAEF,6DAA6D;QAC7D,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAElC,qDAAqD;QACrD,MAAM,cAAc,8BAA8B;QAClD,oBAAoB,IAAI,CAAC;QAGzB,+EAA+E;QAC/E,MAAM,iBAAiB,MAAM,sBAAsB;YACjD,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,iBAAiB;YACjB,cAAc,MAAM,YAAY;YAChC,aAAa;YACb,gBAAgB;QAClB;QAEA,MAAM,WAAW,eAAe,QAAQ;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,oBAAoB,IAAI,CACtB,gDACA,+BACA,0BACA,kCACA,0BACA;QAGF,MAAM,SAA+B;YACnC;YACA,aAAa;gBAAE,QAAQ;YAAY;YACnC,cAAc;YACd;YACA,gBAAgB,KAAK,GAAG,KAAK;YAC7B,OAAO;QACT;QAGA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC1H;AACF;AAMA;;CAEC,GACD,SAAS,yBAAyB,IAAY,EAAE,KAA0B;IACxE,iDAAiD;IACjD,MAAM,aAAa;IACnB,MAAM,SAAS,KAAK,KAAK,CAAC,eAAe,EAAE;IAE3C,OAAO;QACL,QAAQ;YACN,OAAO,MAAM,WAAW,IAAI;YAC5B,YAAY;gBAAE,OAAO;gBAAM,QAAQ;YAAK;YACxC,eAAe;QACjB;QACA,QAAQ;YACN,SAAS,MAAM,YAAY,CAAC,YAAY,IAAI,MAAM,CAAC,EAAE,IAAI;YACzD,WAAW,MAAM,YAAY,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE,IAAI;YAC1D,YAAY,MAAM,YAAY,CAAC,eAAe,IAAI;YAClD,MAAM;QACR;QACA,YAAY;YACV,UAAU;gBAAE,MAAM;gBAAI,QAAQ;gBAAQ,QAAQ;YAAoB;YAClE,aAAa;gBAAE,MAAM;gBAAI,QAAQ;gBAAU,QAAQ;YAAoB;YACvE,MAAM;gBAAE,MAAM;gBAAI,QAAQ;gBAAU,QAAQ;YAAoB;QAClE;QACA,UAAU;YACR,MAAM;gBAAE,UAAU;gBAAY,MAAM;YAAG;YACvC,QAAQ;gBAAC;gBAAuB;aAAgB;YAChD,SAAS;gBAAC;gBAAiB;aAAkB;QAC/C;QACA,SAAS,KAAK,SAAS,CAAC,GAAG,OAAO;IACpC;AACF;AAEA;;;CAGC,GACD,eAAe,0BAA0B,KAAU,EAAE,KAA0B;IAC7E,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IAC/E,MAAM,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,QAAQ,cAAc,CAAC;IAE/D,kCAAkC;IAClC,MAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI;IAC1D,MAAM,aAAa,QAAQ,iBAAiB;IAE5C,iDAAiD;IACjD,MAAM,eAAe,OAAO,OAAO,IAAI,MAAM,YAAY,CAAC,YAAY,IAAI;IAC1E,MAAM,iBAAiB,OAAO,SAAS,IAAI,MAAM,YAAY,CAAC,WAAW,IAAI;IAC7E,MAAM,kBAAkB,OAAO,UAAU,IAAI,MAAM,YAAY,CAAC,eAAe,IAAI;IACnF,MAAM,YAAY,OAAO,IAAI,IAAI;IAEjC,iCAAiC;IACjC,MAAM,YAAY,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IACtE,MAAM,WAAW,SAAS,CAAC,EAAE,IAAI,MAAM,YAAY,CAAC,YAAY,IAAI;IACpE,MAAM,UAAU,SAAS,CAAC,EAAE,IAAI;IAChC,MAAM,UAAU,SAAS,CAAC,EAAE,IAAI;IAEhC,mDAAmD;IACnD,OAAO,2BAA2B,aAAa;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,cAAc,MAAM,YAAY;QAChC,WAAW,MAAM,YAAY,CAAC,YAAY;QAC1C,UAAU,SAAS,MAAM,IAAI,EAAE;QAC/B;IACF;AACF;AAEA;;CAEC,GACD,SAAS,2BAA2B,WAAmB,EAAE,MAAW;IAClE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;IAE/J,wDAAwD;IACxD,IAAI,YAAY,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,SAAS;QACpE,OAAO,sBAAsB;IAC/B,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,SAAS;QAC5G,OAAO,mBAAmB;IAC5B,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,WAAW;QACjH,OAAO,sBAAsB;IAC/B,OAAO,IAAI,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,iBAAiB;QACpF,OAAO,2BAA2B;IACpC,OAAO;QACL,OAAO,qBAAqB;IAC9B;AACF;AAEA;;CAEC,GACD,SAAS,sBAAsB,MAAW;IACxC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE1G,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;8CAGG,EAAE,aAAa;gDACb,EAAE,eAAe;;;;;;;;;uDASV,EAAE,aAAa;wDACd,EAAE,aAAa;;;;;4DAKX,EAAE,aAAa;;UAEjE,EAAE,WAAW,OAAO,MAAM,IAAI;;;;qDAIa,EAAE,UAAU;UACvD,EAAE,SAAS;;;QAGb,EAAE,UAAU,CAAC;sDACiC,EAAE,UAAU;UACxD,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;uDAGwC,EAAE,aAAa;UAC5D,EAAE,QAAQ;;;;EAIlB,CAAC;AACH;AAEA;;CAEC,GACD,SAAS,mBAAmB,MAAW;IACrC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE1G,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;8CAGG,EAAE,aAAa;gDACb,EAAE,eAAe;;;;;;;;;;;gDAWjB,EAAE,aAAa;yDACN,EAAE,eAAe;;;;;8CAK5B,EAAE,aAAa;;UAEnD,EAAE,WAAW,OAAO,MAAM,IAAI;;;;;UAK9B,EAAE,SAAS,WAAW,GAAG;;;QAG3B,EAAE,UAAU,CAAC;;UAEX,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;;uDAIwC,EAAE,aAAa;UAC5D,EAAE,QAAQ,WAAW,GAAG;;;;EAIhC,CAAC;AACH;AAEA;;CAEC,GACD,SAAS,sBAAsB,MAAW;IACxC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE1G,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;;gDAIK,EAAE,aAAa;;;;;;;;2DAQJ,EAAE,aAAa;2DACf,EAAE,eAAe;;;;;0EAKF,EAAE,aAAa;uDAClC,EAAE,aAAa;UAC5D,EAAE,WAAW,OAAO,MAAM,IAAI;;;;qDAIa,EAAE,UAAU;UACvD,EAAE,SAAS;;;QAGb,EAAE,UAAU,CAAC;sDACiC,EAAE,UAAU;UACxD,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;0EAG2D,EAAE,aAAa;uDAClC,EAAE,aAAa;UAC5D,EAAE,QAAQ,WAAW,GAAG;;;;EAIhC,CAAC;AACH;AAEA;;CAEC,GACD,SAAS,2BAA2B,MAAW;IAC7C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE1G,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;;gDAIK,EAAE,aAAa;;;;;;;;wDAQP,EAAE,aAAa;;;;;;wDAMf,EAAE,aAAa;UAC7D,EAAE,WAAW,OAAO,MAAM,IAAI;;;;qDAIa,EAAE,UAAU;UACvD,EAAE,SAAS;;;QAGb,EAAE,UAAU,CAAC;sDACiC,EAAE,UAAU;UACxD,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;6DAG8C,EAAE,aAAa;;UAElE,EAAE,QAAQ;;;;EAIlB,CAAC;AACH;AAEA;;CAEC,GACD,SAAS,qBAAqB,MAAW;IACvC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG;IAE1G,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;8CAGG,EAAE,aAAa;gDACb,EAAE,eAAe;;;;;;;;;8CASnB,EAAE,aAAa;mEACM,EAAE,eAAe;;;;;8CAKtC,EAAE,aAAa;;UAEnD,EAAE,WAAW,OAAO,MAAM,IAAI;;;;qDAIa,EAAE,UAAU;UACvD,EAAE,SAAS;;;QAGb,EAAE,UAAU,CAAC;sDACiC,EAAE,UAAU;UACxD,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;qEAGsD,EAAE,aAAa;;UAE1E,EAAE,QAAQ;;;;EAIlB,CAAC;AACH;AAEA;;CAEC,GACD,eAAe,mBAAmB,KAAU,EAAE,KAA0B;IACtE,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IAC/E,MAAM,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,QAAQ,cAAc,CAAC;IAE/D,+CAA+C;IAC/C,MAAM,eAAe,OAAO,OAAO,IAAI,MAAM,YAAY,CAAC,YAAY,IAAI;IAC1E,MAAM,iBAAiB,OAAO,SAAS,IAAI,MAAM,YAAY,CAAC,WAAW,IAAI;IAC7E,MAAM,cAAc;IACpB,MAAM,YAAY,OAAO,IAAI,IAAI;IACjC,MAAM,aAAa;IAEnB,8BAA8B;IAC9B,MAAM,YAAY,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IACtE,MAAM,WAAW,SAAS,CAAC,EAAE,IAAI;IACjC,MAAM,UAAU,SAAS,CAAC,EAAE,IAAI;IAChC,MAAM,UAAU,SAAS,CAAC,EAAE,IAAI;IAEhC,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;;8CAIG,EAAE,aAAa;+CACd,EAAE,eAAe;gDAChB,EAAE,YAAY;;;;8CAIhB,EAAE,aAAa;gDACb,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAiCnB,EAAE,aAAa;8CACf,EAAE,eAAe;;;8DAGD,EAAE,YAAY;uEACL,EAAE,aAAa;;;;;;;;;;;;6CAYzC,EAAE,aAAa;;;;UAIlD,EAAE,MAAM,YAAY,CAAC,YAAY,EAAE,OAAO,MAAM,IAAI;;;;qDAIT,EAAE,UAAU;;;UAGvD,EAAE,SAAS;;;QAGb,EAAE,UAAU,CAAC;;sDAEiC,EAAE,UAAU;;;UAGxD,EAAE,QAAQ;;QAEZ,CAAC,GAAG,GAAG;;;;;sBAKO,EAAE,aAAa;;;;;;YAMzB,EAAE,QAAQ;;;;;;4CAMsB,EAAE,YAAY;4CACd,EAAE,aAAa;4CACf,EAAE,eAAe;;;;;;;;;;EAU3D,CAAC;AACH;AAEA;;;;CAIC,GACD,SAAS,8BAA8B,KAA0B;IAC/D,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,yBAAyB,IAAI,EAAE,mBAAmB,KAAK,EAAE,GAAG;IAElI,+CAA+C;IAC/C,MAAM,qBAAqB,yBACvB,CAAC;;;;;;;;;;qEAU8D,CAAC,GAChE,CAAC;;;uEAGgE,CAAC;IAEtE,uEAAuE;IACvE,MAAM,SAAS,CAAC,gCAAgC,EAAE,SAAS,yBAAyB,EAAE,aAAa,YAAY,IAAI,aAAa;;UAExH,EAAE,aAAa,YAAY,IAAI,aAAa,EAAE,EAAE,aAAa;kBACrD,EAAE,UAAU;OACvB,EAAE,YAAY;;;WAGV,EAAE,aAAa,YAAY,IAAI,UAAU;UAC1C,EAAE,aAAa,WAAW,IAAI,UAAU;cACpC,EAAE,aAAa,eAAe,IAAI,UAAU;;;;;AAK1D,EAAE,mBAAmB;;;AAGrB,EAAE,mBAAmB,CAAC;;kEAE4C,CAAC,GAAG,CAAC;;uDAEhB,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;8BAqB3B,EAAE,SAAS;;;;;;EAMvC,EAAE,YAAY;cACF,EAAE,aAAa;AAC7B,EAAE,aAAa,UAAU,GAAG,CAAC,4DAA4D,EAAE,gBAAgB,aAAa,UAAU,GAAG,GAAG,GAAG;;;;;;;;;;;wDAWnF,EAAE,aAAa,YAAY,IAAI,aAAa,WAAW,CAAC;IAE9G,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,YAA0B;IACvD,MAAM,eAAe,aAAa,YAAY,IAAI;IAClD,MAAM,cAAc,aAAa,WAAW,IAAI;IAChD,MAAM,kBAAkB,aAAa,eAAe,IAAI;IAExD,OAAO,CAAC,uBAAuB,EAAE,aAAa;0BACtB,EAAE,YAAY;oBACpB,EAAE,gBAAgB;;;;;qFAK+C,CAAC;AACtF;AAEA;;CAEC,GACD,SAAS,sBAAsB,YAAoB,EAAE,QAAgB;IACnE,MAAM,cAAc,SAAS,WAAW,OAAO,aAAa,SAAS,WAAW,OAAO;IAEvF,OAAO,CAAC,+CAA+C,EAAE,cAAc,YAAY,UAAU;0CACrD,EAAE,cAAc,YAAY,UAAU;mCAC7C,EAAE,cAAc,YAAY,UAAU;;;;;8CAK3B,CAAC;AAC/C;AAEA;;CAEC,GACD,SAAS,oBAAoB,QAAgB,EAAE,YAAoB;IACjE,MAAM,cAAc,SAAS,WAAW,OAAO,aAAa,SAAS,WAAW,OAAO;IAEvF,OAAO,CAAC,eAAe,EAAE,cAAc,sDAAsD,4CAA4C;;;;;;wCAMnG,EAAE,cAAc,YAAY,UAAU;uEACP,CAAC;AACxE;AAEA;;CAEC,GACD,SAAS,0BAA0B,QAAgB;IACjD,MAAM,QAAQ;QACZ,WAAW;YACT,MAAM;YACN,YAAY;YACZ,aAAa;QACf;QACA,UAAU;YACR,MAAM;YACN,YAAY;YACZ,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,YAAY;YACZ,aAAa;QACf;QACA,UAAU;YACR,MAAM;YACN,YAAY;YACZ,aAAa;QACf;IACF;IAEA,OAAO,KAAK,CAAC,SAAS,WAAW,GAAyB,IAAI,MAAM,SAAS;AAC/E;AAEA;;CAEC,GACD,SAAS,wBAAwB,YAAoB;IACnD,MAAM,WAAW;QACf,cAAc,CAAC;;;;+CAI4B,CAAC;QAE5C,WAAW,CAAC;;;;2CAI2B,CAAC;QAExC,cAAc,CAAC;;;;kCAIe,CAAC;QAE/B,cAAc,CAAC;;;;0CAIuB,CAAC;QAEvC,aAAa,CAAC;;;;+CAI6B,CAAC;QAE5C,UAAU,CAAC;;;;4CAI6B,CAAC;QAEzC,WAAW,CAAC;;;;wCAIwB,CAAC;QAErC,eAAe,CAAC;;;;iDAI6B,CAAC;QAE9C,UAAU,CAAC;;;;wDAIyC,CAAC;QAErD,cAAc,CAAC;;;;gDAI6B,CAAC;IAC/C;IAEA,MAAM,OAAO,aAAa,WAAW;IACrC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;QACnD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,OAAO;QACT;IACF;IAEA,OAAO,CAAC;;;;kCAIwB,CAAC;AACnC;AAEA;;CAEC,GACD,SAAS,+BAA+B,WAAmB;IACzD,MAAM,SAAS;QACb,UAAU,CAAC;;;;8CAI+B,CAAC;QAE3C,gBAAgB,CAAC;;;;sCAIiB,CAAC;QAEnC,YAAY,CAAC;;;;4CAI2B,CAAC;QAEzC,WAAW,CAAC;;;;uCAIuB,CAAC;QAEpC,QAAQ,CAAC;;;;yCAI4B,CAAC;QAEtC,cAAc,CAAC;;;;4CAIyB,CAAC;QAEzC,WAAW,CAAC;;;;kCAIkB,CAAC;QAE/B,UAAU,CAAC;;;;yCAI0B,CAAC;IACxC;IAEA,MAAM,QAAQ,YAAY,WAAW;IACrC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,OAAO;QACT;IACF;IAEA,OAAO,CAAC;;;;gDAIsC,CAAC;AACjD;AAEA;;CAEC,GACD,SAAS,4BAA4B,YAAoB;IACvD,MAAM,WAAW;QACf,cAAc,CAAC;;;2CAGwB,CAAC;QAExC,cAAc,CAAC;;;2CAGwB,CAAC;QAExC,cAAc,CAAC;;;oCAGiB,CAAC;QAEjC,WAAW,CAAC;;;8BAGc,CAAC;QAE3B,aAAa,CAAC;;;uCAGqB,CAAC;QAEpC,UAAU,CAAC;;;qCAGsB,CAAC;QAElC,WAAW,CAAC;;;0CAG0B,CAAC;QAEvC,eAAe,CAAC;;;6CAGyB,CAAC;QAE1C,UAAU,CAAC;;;0CAG2B,CAAC;QAEvC,cAAc,CAAC;;;gDAG6B,CAAC;IAC/C;IAEA,MAAM,OAAO,aAAa,WAAW;IACrC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;QACnD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,OAAO;QACT;IACF;IAEA,OAAO,CAAC;;;+BAGqB,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 4204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Enhanced brand profile data extraction\r\n    const enhancedProfile = {\r\n      ...profile,\r\n      // Ensure brand colors are available\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      // Extract services information\r\n      servicesArray: typeof profile.services === 'string'\r\n        ? profile.services.split('\\n').filter(s => s.trim())\r\n        : Array.isArray(profile.services)\r\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\r\n          : [],\r\n      // Extract contact information for brand context\r\n      contactInfo: profile.contactInfo || {},\r\n      socialMedia: profile.socialMedia || {},\r\n    };\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    // Ensure model registry is initialized\r\n    if (!modelRegistry.isInitialized()) {\r\n      await modelRegistry.initialize();\r\n    }\r\n\r\n\r\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\r\n    const revo10Model = modelRegistry.getModel('revo-1.0');\r\n    if (!revo10Model) {\r\n      throw new Error('Revo 1.0 model not available');\r\n    }\r\n\r\n\r\n    const generationRequest = {\r\n      modelId: 'revo-1.0',\r\n      profile: enhancedProfile,\r\n      platform: platform,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\r\n      contentThemes: enhancedProfile.contentThemes || [],\r\n      writingTone: enhancedProfile.writingTone || 'professional',\r\n      targetAudience: enhancedProfile.targetAudience || 'General',\r\n      keyFeatures: enhancedProfile.keyFeatures || [],\r\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\r\n      services: enhancedProfile.services || [],\r\n      visualStyle: enhancedProfile.visualStyle || 'modern',\r\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\r\n      accentColor: enhancedProfile.accentColor || '#10B981',\r\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\r\n      logoDataUrl: enhancedProfile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples,\r\n      dayOfWeek: dayOfWeek,\r\n      currentDate: currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }]\r\n    };\r\n\r\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error || 'Content generation failed');\r\n    }\r\n\r\n    const postDetails = result.data;\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline || '',\r\n      callToAction: postDetails.callToAction || '',\r\n      // Revo 1.0 doesn't include these advanced features\r\n      contentVariants: undefined,\r\n      hashtagAnalysis: undefined,\r\n      marketIntelligence: undefined,\r\n      localContext: undefined,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n        includePeopleInDesigns,\r\n        useLocalLanguage,\r\n      });\r\n\r\n\r\n    } catch (gemini25Error) {\r\n\r\n      try {\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      } catch (openaiError) {\r\n\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      }\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n\r\n    if (targetArtifacts.length === 0) {\r\n    } else {\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;;;;;;;;AAGrB;AACA;AACA;AACA;AAEA;AAEA;;;;;;;;;;AAeO,eAAe,mBACpB,UAAkB,EAClB,eAAyB;IAEzB,IAAI;QAEF,sBAAsB;QACtB,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW;YACb;QACF;QAEA,0BAA0B;QAC1B,IAAI,WAAW,WAAW,IAAI;QAC9B,IAAI,CAAC,SAAS,UAAU,CAAC,cAAc,CAAC,SAAS,UAAU,CAAC,aAAa;YACvE,WAAW,aAAa;QAC1B;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAgB,AAAD,EAAE;YACpC,YAAY;YACZ,iBAAiB,mBAAmB,EAAE;QACxC;QAGA,IAAI,CAAC,QAAQ;YACX,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW;YACb;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QAEd,uDAAuD;QACvD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,YAAY;YACtG,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW;YACb;QACF,OAAO,IAAI,aAAa,QAAQ,CAAC,YAAY;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW;YACb;QACF,OAAO,IAAI,aAAa,QAAQ,CAAC,SAAS;YACxC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,WAAW;YACb;QACF,OAAO;YACL,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,iBAAiB,EAAE,cAAc;gBACzC,WAAW;YACb;QACF;IACF;AACF;AAEA,MAAM,4BAA4B,CAAC;IACjC,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,SAAS;QACzB,KAAK;YACH,OAAO,QAAQ,kDAAkD;QACnE,KAAK;YACH,OAAO,QAAQ,YAAY;QAC7B,KAAK;YACH,OAAO,QAAQ,kDAAkD;QACnE;YACE,OAAO;IACX;AACF;AAEO,eAAe,sBACpB,OAAqB,EACrB,QAAkB,EAClB,gBAA6E,EAC7E,mBAA4B,KAAK;IAEjC,IAAI;QACF,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,MAAM,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QACtE,MAAM,cAAc,MAAM,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;YAAQ,KAAK;QAAU;QAEvG,gCAAgC;QAChC,MAAM,0BAA0B,kBAAkB,oBAC7C,QAAQ,cAAc,IAAI,EAAE,GAC7B,EAAE,EAAE,sDAAsD;QAE9D,yCAAyC;QACzC,MAAM,kBAAkB;YACtB,GAAG,OAAO;YACV,oCAAoC;YACpC,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,WAAW,IAAI;YACpC,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,+BAA+B;YAC/B,eAAe,OAAO,QAAQ,QAAQ,KAAK,WACvC,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,MAC/C,MAAM,OAAO,CAAC,QAAQ,QAAQ,IAC5B,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,OAAO,MAAM,WAAW,IAAI,EAAE,IAAI,IAAI,EAAE,WAAW,IAAI,MACjF,EAAE;YACR,gDAAgD;YAChD,aAAa,QAAQ,WAAW,IAAI,CAAC;YACrC,aAAa,QAAQ,WAAW,IAAI,CAAC;QACvC;QAEA,gEAAgE;QAChE,MAAM,oBAAoB,MAAM,OAAO,CAAC,QAAQ,WAAW,IACvD,QAAQ,WAAW,CAAC,IAAI,CAAC,QACzB,QAAQ,WAAW,IAAI;QAE3B,MAAM,8BAA8B,MAAM,OAAO,CAAC,QAAQ,qBAAqB,IAC3E,QAAQ,qBAAqB,CAAC,IAAI,CAAC,QACnC,QAAQ,qBAAqB,IAAI;QAErC,qDAAqD;QACrD,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,QAAQ,IACjD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UACrB,OAAO,YAAY,YAAY,QAAQ,IAAI,GACvC,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,IAAI,IAAI,GAC/C,SACJ,IAAI,CAAC,QACL,QAAQ,QAAQ,IAAI;QAIxB,uCAAuC;QACvC,IAAI,CAAC,oJAAA,CAAA,gBAAa,CAAC,aAAa,IAAI;YAClC,MAAM,oJAAA,CAAA,gBAAa,CAAC,UAAU;QAChC;QAGA,sFAAsF;QACtF,MAAM,cAAc,oJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC;QAC3C,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAGA,MAAM,oBAAoB;YACxB,SAAS;YACT,SAAS;YACT,UAAU;YACV,kBAAkB,oBAAoB;gBAAE,mBAAmB;gBAAO,mBAAmB;YAAK;YAC1F,aAAa,EAAE;YACf,eAAe,gBAAgB,aAAa,IAAI,EAAE;YAClD,aAAa,gBAAgB,WAAW,IAAI;YAC5C,gBAAgB,gBAAgB,cAAc,IAAI;YAClD,aAAa,gBAAgB,WAAW,IAAI,EAAE;YAC9C,uBAAuB,gBAAgB,qBAAqB,IAAI,EAAE;YAClE,UAAU,gBAAgB,QAAQ,IAAI,EAAE;YACxC,aAAa,gBAAgB,WAAW,IAAI;YAC5C,cAAc,gBAAgB,YAAY,IAAI;YAC9C,aAAa,gBAAgB,WAAW,IAAI;YAC5C,iBAAiB,gBAAgB,eAAe,IAAI;YACpD,aAAa,gBAAgB,WAAW;YACxC,gBAAgB;YAChB,WAAW;YACX,aAAa;YACb,UAAU;gBAAC;oBACT,UAAU;oBACV,aAAa,0BAA0B;gBACzC;aAAE;QACJ;QAEA,MAAM,SAAS,MAAM,YAAY,gBAAgB,CAAC,eAAe,CAAC;QAElE,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,MAAM,cAAc,OAAO,IAAI;QAE/B,MAAM,UAAyB;YAC7B,IAAI,IAAI,OAAO,WAAW;YAC1B,MAAM,MAAM,WAAW;YACvB,SAAS,YAAY,OAAO;YAC5B,UAAU,YAAY,QAAQ;YAC9B,QAAQ;YACR,UAAU,YAAY,QAAQ;YAC9B,aAAa,YAAY,WAAW;YACpC,aAAa,YAAY,WAAW,IAAI;YACxC,cAAc,YAAY,YAAY,IAAI;YAC1C,mDAAmD;YACnD,iBAAiB;YACjB,iBAAiB;YACjB,oBAAoB;YACpB,cAAc;QAChB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,2BACpB,OAAqB,EACrB,WAAmB,EACnB,WAAmB;IAEnB,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,+IAAA,CAAA,oBAAqB,AAAD,EAAE;YACzC,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,WAAW;YACX,aAAa;QACf;QACA,OAAO;YAAE,UAAU,OAAO,QAAQ;QAAC;IACrC,EAAE,OAAO,OAAO;QACd,8DAA8D;QAC9D,MAAM,IAAI,MAAM,AAAC,MAAgB,OAAO;IAC1C;AACF;AAGO,eAAe,4BACpB,MAAc,EACd,UAA6B,EAC7B,iBAAgC,EAChC,eAAwB,EACxB,YAAiC,EACjC,WAAsC,EACtC,WAAwC;IAExC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,wBAAyB,AAAD,EAAE;YAC7C;YACA;YACA;YACA;YACA,cAAc,kBAAkB,eAAe;YAC/C;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,sEAAsE;QACtE,MAAM,IAAI,MAAM,AAAC,MAAgB,OAAO;IAC1C;AACF;AAEO,eAAe,6BACpB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,SAAwF,EACxF,YAA2B,EAC3B,qBAA8B,IAAI,EAClC,gBAA6E,EAC7E,oBAA6B,EAC7B,yBAAkC,IAAI,EACtC,mBAA4B,KAAK;IAOjC,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAgC,EAAE;IAExC,IAAI;QACF,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,sDAAsD;QACtD,IAAI;QACJ,IAAI,OAAO,cAAc,UAAU;YACjC,iBAAiB;QACnB,OAAO;YACL,wDAAwD;YACxD,MAAM,aAAa;gBAAC,UAAU,WAAW;aAAC;YAC1C,IAAI,UAAU,WAAW,IAAI,UAAU,WAAW,CAAC,IAAI,IAAI;gBACzD,WAAW,IAAI,CAAC,UAAU,WAAW,CAAC,IAAI;YAC5C;YACA,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,IAAI,IAAI;gBAC3D,WAAW,IAAI,CAAC,UAAU,YAAY,CAAC,IAAI;YAC7C;YACA,iBAAiB,WAAW,IAAI,CAAC;QACnC;QAGA,mFAAmF;QACnF,IAAI;QAEJ,IAAI;YAEF,SAAS,MAAM,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE;gBACpC;gBACA;gBACA;gBACA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;YACF;QAGF,EAAE,OAAO,eAAe;YAEtB,IAAI;gBACF,MAAM,EAAE,kCAAkC,EAAE,GAAG;gBAE/C,SAAS,MAAM,mCAAmC;oBAChD;oBACA;oBACA;oBACA,WAAW;oBACX;oBACA;oBACA;gBACF;YAEF,EAAE,OAAO,aAAa;gBAEpB,MAAM,EAAE,0CAA0C,EAAE,GAAG;gBAEvD,SAAS,MAAM,2CAA2C;oBACxD;oBACA;oBACA;oBACA,WAAW;oBACX;oBACA;oBACA;gBACF;YAEF;QACF;QAGA,OAAO;YACL,UAAU,OAAO,QAAQ;YACzB,cAAc,OAAO,YAAY;YACjC,qBAAqB,OAAO,mBAAmB;YAC/C,gBAAgB,OAAO,cAAc;QACvC;IAGF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,AAAC,MAAgB,OAAO;IAC1C;AACF;AAMO,eAAe,6BACpB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,SAAiB,EACjB,YAA0B,EAC1B,gBAGC,EACD,oBAA6B;IAE7B,IAAI;QACF,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAGA,MAAM,EAAE,0CAA0C,EAAE,GAAG;QAEvD,MAAM,SAAS,MAAM,2CAA2C;YAC9D;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAGA,OAAO;YACL;YACA,UAAU,OAAO,QAAQ;YACzB,SAAS;YACT,UAAU,EAAE;QACd;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,mCACpB,OAAqB,EACrB,QAAkB,EAClB,gBAA6E,EAC7E,cAAwB,EAAE,EAC1B,oBAA6B,IAAI,EACjC,yBAAkC,IAAI,EACtC,mBAA4B,KAAK;IAEjC,IAAI;QAEF,yDAAyD;QACzD,IAAI,kBAA8B,EAAE;QAEpC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,0BAA0B;YAC1B,KAAK,MAAM,cAAc,YAAa;gBACpC,MAAM,WAAW,8IAAA,CAAA,mBAAgB,CAAC,WAAW,CAAC;gBAC9C,IAAI,UAAU;oBACZ,gBAAgB,IAAI,CAAC;oBACrB,MAAM,8IAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,YAAY;gBAChD;YACF;QACF,OAAO;YACL,+CAA+C;YAC/C,MAAM,kBAAkB,8IAAA,CAAA,mBAAgB,CAAC,kBAAkB;YAE3D,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;YACtE,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;YAEvE,iCAAiC;YACjC,kBAAkB;mBAAI;mBAAsB,mBAAmB,KAAK,CAAC,GAAG;aAAG;YAE3E,mCAAmC;YACnC,KAAK,MAAM,YAAY,gBAAiB;gBACtC,MAAM,8IAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE;YACjD;QACF;QAGA,8BAA8B;QAC9B,MAAM,WAAW,MAAM,sBAAsB,SAAS,UAAU;QAEhE,sDAAsD;QACtD,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QAEA,sFAAsF;QAEtF,IAAI,gBAAgB,MAAM,KAAK,GAAG,CAClC,OAAO,CACP;QAEA,6CAA6C;QAC7C,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACtE,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAEvE,4DAA4D;QAC5D,IAAI,oBAA0F;YAC5F,aAAa,SAAS,WAAW,IAAI;YACrC,aAAa,SAAS,WAAW;YACjC,cAAc,SAAS,YAAY;QACrC;QACA,IAAI,kBAAkB,SAAS,OAAO;QAEtC,4CAA4C;QAC5C,MAAM,uBAAuB,gBAC1B,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,EAAE,YAAY,CAAC,IAAI,IACjD,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,EACzC,IAAI,CAAC;QAER,wDAAwD;QACxD,MAAM,0BAA0B,kBAC7B,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,CAAC,YAAY,CAAC,IAAI,IAC1E,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,WAAW,CAAC,YAAY,EAAE,EACrD,IAAI,CAAC;QAER,sDAAsD;QACtD,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,MAAM,kBAAkB,iBAAiB,CAAC,EAAE;YAE5C,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,EAAE;gBAC/B,IAAI,gBAAgB,WAAW,CAAC,QAAQ,EAAE;oBACxC,kBAAkB,WAAW,GAAG,gBAAgB,WAAW,CAAC,QAAQ;gBACtE;gBAEA,IAAI,gBAAgB,WAAW,CAAC,OAAO,EAAE;oBACvC,kBAAkB,gBAAgB,WAAW,CAAC,OAAO;gBACvD;gBAEA,qCAAqC;gBACrC,IAAI,gBAAgB,WAAW,CAAC,GAAG,EAAE;oBACnC,kBAAkB,YAAY,GAAG,gBAAgB,WAAW,CAAC,GAAG;gBAClE;YACF;QACF;QAEA,iDAAiD;QACjD,MAAM,mBAAmB,mBAAmB,OAAO,CAAC,CAAA,WAClD,SAAS,UAAU,CAAC,MAAM,CAAC,CAAA,YAAa,UAAU,MAAM;QAG1D,mCAAmC;QACnC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAChE,IAAI,sBAAsB,QAAQ,WAAW,IAAI;QACjD,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,wBAAwB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI;YACtE,IAAI,uBAAuB;gBACzB,sBAAsB;YACxB;QACF;QAEA,2BAA2B;QAC3B,MAAM,kBAAkB;YAAC;YAAsB;SAAwB,CACpE,MAAM,CAAC,SACP,IAAI,CAAC;QAER,iDAAiD;QACjD,MAAM,iBAAiB,MAAM,6BAC3B,QAAQ,YAAY,IAAI,YACxB,SAAS,WAAW,IACpB,qBACA,mBACA,SACA,MACA,kBACA,mBAAmB,WACnB,wBACA;QAGF,8CAA8C;QAC9C,MAAM,eAA8B;YAClC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU;gBAAC;oBACT,UAAU;oBACV,UAAU,eAAe,QAAQ;gBACnC;aAAE;YACF,SAAS,gBAAgB,MAAM,GAAG,IAC9B,GAAG,gBAAgB,8BAA8B,EAAE,gBAAgB,MAAM,CAAC,UAAU,EAAE,gBAAgB,MAAM,KAAK,IAAI,MAAM,GAAG,WAAW,EAAE,eAAe,YAAY,CAAC,IAAI,CAAC,GAC5K,GAAG,gBAAgB,oDAAoD,EAAE,eAAe,YAAY,CAAC,IAAI,CAAC;YAC9G,MAAM,IAAI,OAAO,WAAW;YAC5B,wBAAwB;YACxB,UAAU;gBACR,GAAG,SAAS,QAAQ;gBACpB,qBAAqB,gBAAgB,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC7C,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,MAAM,EAAE,IAAI;wBACZ,UAAU,EAAE,QAAQ;oBACtB,CAAC;gBACD,kBAAkB,iBAAiB,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC3C,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,OAAO,EAAE,KAAK;wBACd,UAAU,EAAE,QAAQ;oBACtB,CAAC;YACH;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,AAAC,MAAgB,OAAO;IAC1C;AACF;;;IArkBsB;IAuFA;IA4HA;IAqBA;IA0BA;IA8GA;IA6CA;;AA7ZA,+OAAA;AAuFA,+OAAA;AA4HA,+OAAA;AAqBA,+OAAA;AA0BA,+OAAA;AA8GA,+OAAA;AA6CA,+OAAA", "debugId": null}}, {"offset": {"line": 4675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-2.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 2.0 Service - Next-Generation AI Content Creation\r\n * Uses Gemini 2.5 Flash Image Preview for enhanced content generation\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport OpenAI from 'openai';\r\nimport type { BrandProfile, Platform } from '@/lib/types';\r\n\r\n// Initialize AI clients\r\nconst ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);\r\nconst openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });\r\n\r\n// Revo 2.0 uses Gemini 2.5 Flash Image Preview (same as Revo 1.0 but with enhanced prompting)\r\nconst REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\nexport interface Revo20GenerationOptions {\r\n  businessType: string;\r\n  platform: Platform;\r\n  visualStyle?: 'modern' | 'minimalist' | 'bold' | 'elegant' | 'playful' | 'professional';\r\n  imageText?: string;\r\n  brandProfile: BrandProfile;\r\n  aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n  includePeopleInDesigns?: boolean;\r\n  useLocalLanguage?: boolean;\r\n}\r\n\r\nexport interface Revo20GenerationResult {\r\n  imageUrl: string;\r\n  model: string;\r\n  qualityScore: number;\r\n  processingTime: number;\r\n  enhancementsApplied: string[];\r\n  caption: string;\r\n  hashtags: string[];\r\n}\r\n\r\n/**\r\n * Generate enhanced creative concept using GPT-4\r\n */\r\nasync function generateCreativeConcept(options: Revo20GenerationOptions): Promise<any> {\r\n  const { businessType, platform, brandProfile, visualStyle = 'modern' } = options;\r\n\r\n  const prompt = `You are a world-class creative director specializing in ${businessType} businesses. \r\nCreate an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.\r\n\r\nBusiness Context:\r\n- Type: ${businessType}\r\n- Platform: ${platform}\r\n- Style: ${visualStyle}\r\n- Location: ${brandProfile.location || 'Global'}\r\n- Brand: ${brandProfile.businessName || businessType}\r\n\r\nCreate a concept that:\r\n1. Feels authentic and locally relevant\r\n2. Uses relatable human experiences\r\n3. Connects emotionally with the target audience\r\n4. Incorporates cultural nuances naturally\r\n5. Avoids generic corporate messaging\r\n\r\nReturn your response in this exact JSON format:\r\n{\r\n  \"concept\": \"Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)\",\r\n  \"catchwords\": [\"word1\", \"word2\", \"word3\", \"word4\", \"word5\"],\r\n  \"visualDirection\": \"Authentic visual direction that feels real and community-focused (2-3 sentences)\",\r\n  \"designElements\": [\"element1\", \"element2\", \"element3\", \"element4\"],\r\n  \"colorSuggestions\": [\"#color1\", \"#color2\", \"#color3\"],\r\n  \"moodKeywords\": [\"mood1\", \"mood2\", \"mood3\", \"mood4\"],\r\n  \"targetEmotions\": [\"emotion1\", \"emotion2\", \"emotion3\"]\r\n}`;\r\n\r\n  const response = await openai.chat.completions.create({\r\n    model: 'gpt-4o',\r\n    messages: [{ role: 'user', content: prompt }],\r\n    temperature: 0.8,\r\n    max_tokens: 1000\r\n  });\r\n\r\n  try {\r\n    const content = response.choices[0].message.content || '{}';\r\n    // Remove markdown code blocks if present\r\n    const cleanContent = content.replace(/```json\\n?/g, '').replace(/```\\n?/g, '').trim();\r\n    return JSON.parse(cleanContent);\r\n  } catch (error) {\r\n    return {\r\n      concept: `Professional ${businessType} content for ${platform}`,\r\n      catchwords: ['quality', 'professional', 'trusted', 'local', 'expert'],\r\n      visualDirection: 'Clean, professional design with modern aesthetics',\r\n      designElements: ['clean typography', 'professional imagery', 'brand colors', 'modern layout'],\r\n      colorSuggestions: ['#2563eb', '#1f2937', '#f8fafc'],\r\n      moodKeywords: ['professional', 'trustworthy', 'modern', 'clean'],\r\n      targetEmotions: ['trust', 'confidence', 'reliability']\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with Revo 2.0 (Gemini 2.5 Flash Image Preview)\r\n */\r\nexport async function generateWithRevo20(options: Revo20GenerationOptions): Promise<Revo20GenerationResult> {\r\n  const startTime = Date.now();\r\n\r\n  try {\r\n    // Step 1: Generate creative concept\r\n    const concept = await generateCreativeConcept(options);\r\n\r\n    // Step 2: Build enhanced prompt\r\n    const enhancedPrompt = buildEnhancedPrompt(options, concept);\r\n\r\n    // Step 3: Generate image with Gemini 2.5 Flash Image Preview\r\n    const imageResult = await generateImageWithGemini(enhancedPrompt, options);\r\n\r\n    // Step 4: Generate caption and hashtags\r\n    const contentResult = await generateCaptionAndHashtags(options, concept);\r\n\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    return {\r\n      imageUrl: imageResult.imageUrl,\r\n      model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',\r\n      qualityScore: 9.2,\r\n      processingTime,\r\n      enhancementsApplied: [\r\n        'Creative concept generation',\r\n        'Enhanced prompt engineering',\r\n        'Brand consistency optimization',\r\n        'Platform-specific formatting',\r\n        'Cultural relevance integration'\r\n      ],\r\n      caption: contentResult.caption,\r\n      hashtags: contentResult.hashtags\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build enhanced prompt for Revo 2.0\r\n */\r\nfunction buildEnhancedPrompt(options: Revo20GenerationOptions, concept: any): string {\r\n  const { businessType, platform, brandProfile, aspectRatio = '1:1', visualStyle = 'modern' } = options;\r\n\r\n  return `Create a high-quality, professional ${businessType} design for ${platform}.\r\n\r\nCREATIVE CONCEPT: ${concept.concept}\r\n\r\nVISUAL DIRECTION: ${concept.visualDirection}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Style: ${visualStyle}, premium quality\r\n- Aspect Ratio: ${aspectRatio}\r\n- Platform: ${platform} optimized\r\n- Business: ${brandProfile.businessName || businessType}\r\n- Location: ${brandProfile.location || 'Professional setting'}\r\n\r\nDESIGN ELEMENTS:\r\n${concept.designElements.map((element: string) => `- ${element}`).join('\\n')}\r\n\r\nMOOD & EMOTIONS:\r\n- Target emotions: ${concept.targetEmotions.join(', ')}\r\n- Mood keywords: ${concept.moodKeywords.join(', ')}\r\n\r\nBRAND INTEGRATION:\r\n- Colors: ${brandProfile.primaryColor ? `Primary: ${brandProfile.primaryColor}, Accent: ${brandProfile.accentColor}, Background: ${brandProfile.backgroundColor}` : concept.colorSuggestions.join(', ')}\r\n- Business name: ${brandProfile.businessName || businessType}\r\n- Logo: ${brandProfile.logoDataUrl ? 'Include provided brand logo prominently' : 'No logo provided'}\r\n- Professional, trustworthy appearance\r\n\r\nQUALITY STANDARDS:\r\n- Ultra-high resolution and clarity\r\n- Professional composition\r\n- Perfect typography and text rendering\r\n- MAXIMUM 3 COLORS ONLY (use brand colors if provided)\r\n- NO LINES: no decorative lines, borders, dividers, or linear elements\r\n- Platform-optimized dimensions\r\n- Brand consistency throughout\r\n- Clean, minimalist design with 50%+ white space\r\n\r\nCreate a stunning, professional design that captures the essence of this ${businessType} business.`;\r\n}\r\n\r\n/**\r\n * Generate image using Gemini 2.5 Flash Image Preview with logo support\r\n */\r\nasync function generateImageWithGemini(prompt: string, options: Revo20GenerationOptions): Promise<{ imageUrl: string }> {\r\n  const maxRetries = 3;\r\n  let lastError: any;\r\n\r\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n    try {\r\n\r\n      const model = ai.getGenerativeModel({\r\n        model: REVO_2_0_MODEL,\r\n        generationConfig: {\r\n          temperature: 0.7,\r\n          topP: 0.9,\r\n          topK: 40,\r\n          maxOutputTokens: 2048,\r\n        },\r\n      });\r\n\r\n      // Prepare the generation request with logo if available\r\n      const generationParts = [\r\n        'You are an expert graphic designer using Gemini 2.5 Flash Image Preview. Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.',\r\n        prompt\r\n      ];\r\n\r\n      // If logo is provided, include it in the generation\r\n      if (options.brandProfile.logoDataUrl) {\r\n\r\n        // Extract the base64 data and mime type from the data URL\r\n        const logoMatch = options.brandProfile.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);\r\n        if (logoMatch) {\r\n          const [, mimeType, base64Data] = logoMatch;\r\n\r\n          generationParts.push({\r\n            inlineData: {\r\n              data: base64Data,\r\n              mimeType: mimeType\r\n            }\r\n          });\r\n\r\n          // Update the prompt to reference the provided logo\r\n          const logoPrompt = `\\n\\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;\r\n          generationParts[1] = prompt + logoPrompt;\r\n        } else {\r\n        }\r\n      }\r\n\r\n      const result = await model.generateContent(generationParts);\r\n      const response = await result.response;\r\n\r\n\r\n      // Extract image data from Gemini response (same as Revo 1.0)\r\n      const parts = response.candidates?.[0]?.content?.parts || [];\r\n      let imageUrl = '';\r\n\r\n      for (const part of parts) {\r\n        if (part.inlineData) {\r\n          const imageData = part.inlineData.data;\r\n          const mimeType = part.inlineData.mimeType;\r\n          imageUrl = `data:${mimeType};base64,${imageData}`;\r\n          break;\r\n        }\r\n      }\r\n\r\n      if (!imageUrl) {\r\n        throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');\r\n      }\r\n\r\n      return { imageUrl };\r\n\r\n    } catch (error: any) {\r\n      lastError = error;\r\n\r\n      if (attempt === maxRetries) {\r\n        break;\r\n      }\r\n\r\n      const waitTime = Math.pow(2, attempt) * 1000;\r\n      await new Promise(resolve => setTimeout(resolve, waitTime));\r\n    }\r\n  }\r\n\r\n  throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);\r\n}\r\n\r\n/**\r\n * Generate caption and hashtags with AI-powered contextual generation\r\n */\r\nasync function generateCaptionAndHashtags(options: Revo20GenerationOptions, concept: any): Promise<{ caption: string; hashtags: string[] }> {\r\n  const { businessType, platform, brandProfile } = options;\r\n\r\n  const prompt = `Create engaging ${platform} content for a ${businessType} business.\r\n\r\nBusiness Details:\r\n- Name: ${brandProfile.businessName || businessType}\r\n- Type: ${businessType}\r\n- Location: ${brandProfile.location || 'Local area'}\r\n- Concept: ${concept.concept}\r\n- Catchwords: ${concept.catchwords.join(', ')}\r\n\r\nCreate:\r\n1. A catchy, engaging caption (2-3 sentences max) that incorporates the concept and catchwords naturally\r\n2. 10 highly relevant, specific hashtags that are:\r\n   - Specific to this business and location\r\n   - Mix of business-specific, location-based, industry-relevant, and platform-optimized\r\n   - Avoid generic hashtags like #business, #professional, #quality, #local\r\n   - Discoverable and relevant to the target audience\r\n   - Appropriate for ${platform}\r\n\r\nMake the content authentic, locally relevant, and engaging for ${platform}.\r\n\r\nFormat as JSON:\r\n{\r\n  \"caption\": \"Your engaging caption here\",\r\n  \"hashtags\": [\"#SpecificHashtag1\", \"#LocationBasedHashtag\", \"#IndustryRelevant\", ...]\r\n}`;\r\n\r\n  const response = await openai.chat.completions.create({\r\n    model: 'gpt-4o',\r\n    messages: [{ role: 'user', content: prompt }],\r\n    temperature: 0.7,\r\n    max_tokens: 600\r\n  });\r\n\r\n  try {\r\n    let responseContent = response.choices[0].message.content || '{}';\r\n\r\n    // Remove markdown code blocks if present\r\n    responseContent = responseContent.replace(/```json\\s*|\\s*```/g, '').trim();\r\n\r\n    const result = JSON.parse(responseContent);\r\n\r\n    // Validate the response\r\n    if (result.caption && Array.isArray(result.hashtags) && result.hashtags.length > 0) {\r\n      return {\r\n        caption: result.caption,\r\n        hashtags: result.hashtags.slice(0, 10) // Ensure max 10 hashtags\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to parse AI content response:', error);\r\n  }\r\n\r\n  // Fallback with contextual generation (no hardcoded placeholders)\r\n  return generateContextualFallback(businessType, brandProfile, platform, concept);\r\n}\r\n\r\n/**\r\n * Generate contextual fallback content without hardcoded placeholders\r\n */\r\nfunction generateContextualFallback(\r\n  businessType: string,\r\n  brandProfile: BrandProfile,\r\n  platform: string,\r\n  concept: any\r\n): { caption: string; hashtags: string[] } {\r\n  const businessName = brandProfile.businessName || businessType;\r\n  const location = brandProfile.location || 'your area';\r\n\r\n  // Generate contextual caption\r\n  const caption = `${concept.catchwords[0] || 'Discover'} what makes ${businessName} special in ${location}! ${concept.concept || 'Experience the difference with our exceptional service.'}`;\r\n\r\n  // Generate contextual hashtags\r\n  const hashtags: string[] = [];\r\n\r\n  // Business-specific\r\n  hashtags.push(`#${businessName.replace(/\\s+/g, '')}`);\r\n  hashtags.push(`#${businessType.replace(/\\s+/g, '')}Business`);\r\n\r\n  // Location-based\r\n  const locationParts = location.split(',').map(part => part.trim());\r\n  locationParts.forEach(part => {\r\n    if (part.length > 2) {\r\n      hashtags.push(`#${part.replace(/\\s+/g, '')}`);\r\n    }\r\n  });\r\n\r\n  // Platform-specific contextual\r\n  if (platform === 'instagram') {\r\n    hashtags.push('#InstagramContent', '#VisualStory');\r\n  } else if (platform === 'facebook') {\r\n    hashtags.push('#FacebookPost', '#CommunityBusiness');\r\n  } else if (platform === 'linkedin') {\r\n    hashtags.push('#LinkedInBusiness', '#ProfessionalServices');\r\n  } else if (platform === 'tiktok') {\r\n    hashtags.push('#TikTokBusiness', '#CreativeContent');\r\n  }\r\n\r\n  // Add current date context\r\n  const today = new Date();\r\n  const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n  hashtags.push(`#${dayName}Vibes`);\r\n\r\n  return {\r\n    caption,\r\n    hashtags: [...new Set(hashtags)].slice(0, 10) // Remove duplicates and limit to 10\r\n  };\r\n}\r\n\r\n/**\r\n * Test Revo 2.0 availability\r\n */\r\nexport async function testRevo20Availability(): Promise<boolean> {\r\n  try {\r\n\r\n    const model = ai.getGenerativeModel({ model: REVO_2_0_MODEL });\r\n    const response = await model.generateContent('Create a simple test image with the text \"Revo 2.0 Test\" on a modern gradient background');\r\n\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let hasImage = false;\r\n\r\n    for (const part of parts) {\r\n      if (part.inlineData) {\r\n        hasImage = true;\r\n      }\r\n    }\r\n\r\n    if (hasImage) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;;;AAGA,wBAAwB;AACxB,MAAM,KAAK,IAAI,8JAAA,CAAA,qBAAkB,CAAC,QAAQ,GAAG,CAAC,cAAc;AAC5D,MAAM,SAAS,IAAI,sKAAA,CAAA,UAAM,CAAC;IAAE,QAAQ,QAAQ,GAAG,CAAC,cAAc;AAAE;AAEhE,8FAA8F;AAC9F,MAAM,iBAAiB;AAuBvB;;CAEC,GACD,eAAe,wBAAwB,OAAgC;IACrE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,QAAQ,EAAE,GAAG;IAEzE,MAAM,SAAS,CAAC,wDAAwD,EAAE,aAAa;2DAC9B,EAAE,SAAS;;;QAG9D,EAAE,aAAa;YACX,EAAE,SAAS;SACd,EAAE,YAAY;YACX,EAAE,aAAa,QAAQ,IAAI,SAAS;SACvC,EAAE,aAAa,YAAY,IAAI,aAAa;;;;;;;;;;;;;;;;;;CAkBpD,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;QACvD,yCAAyC;QACzC,MAAM,eAAe,QAAQ,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;QACnF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,UAAU;YAC/D,YAAY;gBAAC;gBAAW;gBAAgB;gBAAW;gBAAS;aAAS;YACrE,iBAAiB;YACjB,gBAAgB;gBAAC;gBAAoB;gBAAwB;gBAAgB;aAAgB;YAC7F,kBAAkB;gBAAC;gBAAW;gBAAW;aAAU;YACnD,cAAc;gBAAC;gBAAgB;gBAAe;gBAAU;aAAQ;YAChE,gBAAgB;gBAAC;gBAAS;gBAAc;aAAc;QACxD;IACF;AACF;AAKO,eAAe,mBAAmB,OAAgC;IACvE,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,oCAAoC;QACpC,MAAM,UAAU,MAAM,wBAAwB;QAE9C,gCAAgC;QAChC,MAAM,iBAAiB,oBAAoB,SAAS;QAEpD,6DAA6D;QAC7D,MAAM,cAAc,MAAM,wBAAwB,gBAAgB;QAElE,wCAAwC;QACxC,MAAM,gBAAgB,MAAM,2BAA2B,SAAS;QAEhE,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,OAAO;YACP,cAAc;YACd;YACA,qBAAqB;gBACnB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS,cAAc,OAAO;YAC9B,UAAU,cAAc,QAAQ;QAClC;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3G;AACF;AAEA;;CAEC,GACD,SAAS,oBAAoB,OAAgC,EAAE,OAAY;IACzE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE,cAAc,QAAQ,EAAE,GAAG;IAE9F,OAAO,CAAC,oCAAoC,EAAE,aAAa,YAAY,EAAE,SAAS;;kBAElE,EAAE,QAAQ,OAAO,CAAC;;kBAElB,EAAE,QAAQ,eAAe,CAAC;;;SAGnC,EAAE,YAAY;gBACP,EAAE,YAAY;YAClB,EAAE,SAAS;YACX,EAAE,aAAa,YAAY,IAAI,aAAa;YAC5C,EAAE,aAAa,QAAQ,IAAI,uBAAuB;;;AAG9D,EAAE,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,UAAoB,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM;;;mBAG1D,EAAE,QAAQ,cAAc,CAAC,IAAI,CAAC,MAAM;iBACtC,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;;;UAGzC,EAAE,aAAa,YAAY,GAAG,CAAC,SAAS,EAAE,aAAa,YAAY,CAAC,UAAU,EAAE,aAAa,WAAW,CAAC,cAAc,EAAE,aAAa,eAAe,EAAE,GAAG,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM;iBACvL,EAAE,aAAa,YAAY,IAAI,aAAa;QACrD,EAAE,aAAa,WAAW,GAAG,4CAA4C,mBAAmB;;;;;;;;;;;;;yEAa3B,EAAE,aAAa,UAAU,CAAC;AACnG;AAEA;;CAEC,GACD,eAAe,wBAAwB,MAAc,EAAE,OAAgC;IACrF,MAAM,aAAa;IACnB,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;gBAClC,OAAO;gBACP,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;YACF;YAEA,wDAAwD;YACxD,MAAM,kBAAkB;gBACtB;gBACA;aACD;YAED,oDAAoD;YACpD,IAAI,QAAQ,YAAY,CAAC,WAAW,EAAE;gBAEpC,0DAA0D;gBAC1D,MAAM,YAAY,QAAQ,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;gBACzD,IAAI,WAAW;oBACb,MAAM,GAAG,UAAU,WAAW,GAAG;oBAEjC,gBAAgB,IAAI,CAAC;wBACnB,YAAY;4BACV,MAAM;4BACN,UAAU;wBACZ;oBACF;oBAEA,mDAAmD;oBACnD,MAAM,aAAa,CAAC,6MAA6M,CAAC;oBAClO,eAAe,CAAC,EAAE,GAAG,SAAS;gBAChC,OAAO,CACP;YACF;YAEA,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;YAGtC,6DAA6D;YAC7D,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;YAC5D,IAAI,WAAW;YAEf,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,UAAU,EAAE;oBACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;oBACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;oBACzC,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;oBACjD;gBACF;YACF;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;gBAAE;YAAS;QAEpB,EAAE,OAAO,OAAY;YACnB,YAAY;YAEZ,IAAI,YAAY,YAAY;gBAC1B;YACF;YAEA,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,WAAW;YACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,WAAW,WAAW,EAAE,WAAW,WAAW,iBAAiB;AACrH;AAEA;;CAEC,GACD,eAAe,2BAA2B,OAAgC,EAAE,OAAY;IACtF,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG;IAEjD,MAAM,SAAS,CAAC,gBAAgB,EAAE,SAAS,eAAe,EAAE,aAAa;;;QAGnE,EAAE,aAAa,YAAY,IAAI,aAAa;QAC5C,EAAE,aAAa;YACX,EAAE,aAAa,QAAQ,IAAI,aAAa;WACzC,EAAE,QAAQ,OAAO,CAAC;cACf,EAAE,QAAQ,UAAU,CAAC,IAAI,CAAC,MAAM;;;;;;;;;qBASzB,EAAE,SAAS;;+DAE+B,EAAE,SAAS;;;;;;CAMzE,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,IAAI,kBAAkB,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;QAE7D,yCAAyC;QACzC,kBAAkB,gBAAgB,OAAO,CAAC,sBAAsB,IAAI,IAAI;QAExE,MAAM,SAAS,KAAK,KAAK,CAAC;QAE1B,wBAAwB;QACxB,IAAI,OAAO,OAAO,IAAI,MAAM,OAAO,CAAC,OAAO,QAAQ,KAAK,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;YAClF,OAAO;gBACL,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,yBAAyB;YAClE;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,wCAAwC;IACvD;IAEA,kEAAkE;IAClE,OAAO,2BAA2B,cAAc,cAAc,UAAU;AAC1E;AAEA;;CAEC,GACD,SAAS,2BACP,YAAoB,EACpB,YAA0B,EAC1B,QAAgB,EAChB,OAAY;IAEZ,MAAM,eAAe,aAAa,YAAY,IAAI;IAClD,MAAM,WAAW,aAAa,QAAQ,IAAI;IAE1C,8BAA8B;IAC9B,MAAM,UAAU,GAAG,QAAQ,UAAU,CAAC,EAAE,IAAI,WAAW,YAAY,EAAE,aAAa,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,OAAO,IAAI,2DAA2D;IAE3L,+BAA+B;IAC/B,MAAM,WAAqB,EAAE;IAE7B,oBAAoB;IACpB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;IACpD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;IAE5D,iBAAiB;IACjB,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC/D,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,KAAK;QAC9C;IACF;IAEA,+BAA+B;IAC/B,IAAI,aAAa,aAAa;QAC5B,SAAS,IAAI,CAAC,qBAAqB;IACrC,OAAO,IAAI,aAAa,YAAY;QAClC,SAAS,IAAI,CAAC,iBAAiB;IACjC,OAAO,IAAI,aAAa,YAAY;QAClC,SAAS,IAAI,CAAC,qBAAqB;IACrC,OAAO,IAAI,aAAa,UAAU;QAChC,SAAS,IAAI,CAAC,mBAAmB;IACnC;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,UAAU,MAAM,kBAAkB,CAAC,SAAS;QAAE,SAAS;IAAO;IACpE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC;IAEhC,OAAO;QACL;QACA,UAAU;eAAI,IAAI,IAAI;SAAU,CAAC,KAAK,CAAC,GAAG,IAAI,oCAAoC;IACpF;AACF;AAKO,eAAe;IACpB,IAAI;QAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,WAAW,MAAM,MAAM,eAAe,CAAC;QAE7C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,UAAU;YACZ,OAAO;QACT,OAAO;YACL,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 5046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions/revo-2-actions.ts"], "sourcesContent": ["'use server';\r\n\r\n/**\r\n * Revo 2.0 Server Actions\r\n * Next-generation content creation with Gemini 2.5 Flash Image Preview\r\n */\r\n\r\nimport { generateWithRevo20, testRevo20Availability, type Revo20GenerationOptions } from '@/ai/revo-2.0-service';\r\nimport type { BrandProfile, Platform, BrandConsistencyPreferences, GeneratedPost } from '@/lib/types';\r\n\r\n/**\r\n * Generate content with Revo 2.0 (Gemini 2.5 Flash Image Preview)\r\n */\r\nexport async function generateRevo2ContentAction(\r\n  brandProfile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency: BrandConsistencyPreferences,\r\n  prompt?: string,\r\n  options?: {\r\n    aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n    visualStyle?: 'modern' | 'minimalist' | 'bold' | 'elegant' | 'playful' | 'professional';\r\n    includePeopleInDesigns?: boolean;\r\n    useLocalLanguage?: boolean;\r\n  }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n\r\n    // Prepare Revo 2.0 generation options\r\n    const revo2Options: Revo20GenerationOptions = {\r\n      businessType: brandProfile.businessType || 'Business',\r\n      platform,\r\n      visualStyle: options?.visualStyle || 'modern',\r\n      imageText: prompt || `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,\r\n      brandProfile,\r\n      aspectRatio: options?.aspectRatio || '1:1',\r\n      includePeopleInDesigns: options?.includePeopleInDesigns || false,\r\n      useLocalLanguage: options?.useLocalLanguage || false\r\n    };\r\n\r\n    // Generate with Revo 2.0\r\n    const result = await generateWithRevo20(revo2Options);\r\n\r\n    // Convert to GeneratedPost format\r\n    const generatedPost: GeneratedPost = {\r\n      id: `revo2-${Date.now()}`,\r\n      date: new Date().toISOString(),\r\n      platform: platform.toLowerCase(),\r\n      postType: 'post',\r\n      imageUrl: result.imageUrl,\r\n      content: result.caption,\r\n      hashtags: result.hashtags,\r\n      status: 'generated',\r\n      variants: [\r\n        {\r\n          platform: platform.toLowerCase(),\r\n          imageUrl: result.imageUrl\r\n        }\r\n      ],\r\n      metadata: {\r\n        model: result.model,\r\n        qualityScore: result.qualityScore,\r\n        processingTime: result.processingTime,\r\n        enhancementsApplied: result.enhancementsApplied\r\n      }\r\n    };\r\n\r\n    return generatedPost;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 2.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate creative asset with Revo 2.0\r\n */\r\nexport async function generateRevo2CreativeAssetAction(\r\n  brandProfile: BrandProfile,\r\n  platform: Platform,\r\n  prompt: string,\r\n  options?: {\r\n    aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n    visualStyle?: 'modern' | 'minimalist' | 'bold' | 'elegant' | 'playful' | 'professional';\r\n    includePeopleInDesigns?: boolean;\r\n  }\r\n): Promise<{\r\n  success: boolean;\r\n  imageUrl?: string;\r\n  model?: string;\r\n  qualityScore?: number;\r\n  processingTime?: number;\r\n  enhancementsApplied?: string[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n\r\n    const revo2Options: Revo20GenerationOptions = {\r\n      businessType: brandProfile.businessType || 'Business',\r\n      platform,\r\n      visualStyle: options?.visualStyle || 'modern',\r\n      imageText: prompt,\r\n      brandProfile,\r\n      aspectRatio: options?.aspectRatio || '1:1',\r\n      includePeopleInDesigns: options?.includePeopleInDesigns || false,\r\n      useLocalLanguage: false\r\n    };\r\n\r\n    const result = await generateWithRevo20(revo2Options);\r\n\r\n    return {\r\n      success: true,\r\n      imageUrl: result.imageUrl,\r\n      model: result.model,\r\n      qualityScore: result.qualityScore,\r\n      processingTime: result.processingTime,\r\n      enhancementsApplied: result.enhancementsApplied\r\n    };\r\n\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get Revo 2.0 capabilities\r\n */\r\nexport async function getRevo2CapabilitiesAction(): Promise<{\r\n  name: string;\r\n  description: string;\r\n  features: string[];\r\n  supportedPlatforms: string[];\r\n  qualityRange: string;\r\n  status: string;\r\n}> {\r\n  return {\r\n    name: 'Revo 2.0',\r\n    description: 'Next-generation AI content creation with Gemini 2.5 Flash Image Preview',\r\n    features: [\r\n      'Gemini 2.5 Flash Image Preview integration',\r\n      'Multi-aspect ratio support (1:1, 16:9, 9:16, 21:9, 4:5)',\r\n      'Advanced style control (6 styles)',\r\n      'Professional mood settings',\r\n      'Brand color integration',\r\n      'Platform-optimized generation',\r\n      'Enhanced prompt engineering',\r\n      'Ultra-high quality output',\r\n      'Smart caption generation',\r\n      'Intelligent hashtag strategy'\r\n    ],\r\n    supportedPlatforms: [\r\n      'Instagram',\r\n      'Facebook',\r\n      'Twitter',\r\n      'LinkedIn'\r\n    ],\r\n    qualityRange: '8.0-10.0/10',\r\n    status: 'Next-Generation'\r\n  };\r\n}\r\n\r\n/**\r\n * Test Revo 2.0 availability\r\n */\r\nexport async function testRevo2AvailabilityAction(): Promise<{\r\n  available: boolean;\r\n  model: string;\r\n  message: string;\r\n}> {\r\n  try {\r\n    const isAvailable = await testRevo20Availability();\r\n    \r\n    return {\r\n      available: isAvailable,\r\n      model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',\r\n      message: isAvailable \r\n        ? 'Revo 2.0 is available and ready!' \r\n        : 'Revo 2.0 is not available. Check API key and model access.'\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      available: false,\r\n      model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',\r\n      message: `Revo 2.0 test failed: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;CAGC,GAED;;;;;AAMO,eAAe,2BACpB,YAA0B,EAC1B,QAAkB,EAClB,gBAA6C,EAC7C,MAAe,EACf,OAKC;IAED,IAAI;QAEF,sCAAsC;QACtC,MAAM,eAAwC;YAC5C,cAAc,aAAa,YAAY,IAAI;YAC3C;YACA,aAAa,SAAS,eAAe;YACrC,WAAW,UAAU,GAAG,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,kBAAkB,CAAC;YAClG;YACA,aAAa,SAAS,eAAe;YACrC,wBAAwB,SAAS,0BAA0B;YAC3D,kBAAkB,SAAS,oBAAoB;QACjD;QAEA,yBAAyB;QACzB,MAAM,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE;QAExC,kCAAkC;QAClC,MAAM,gBAA+B;YACnC,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,IAAI,OAAO,WAAW;YAC5B,UAAU,SAAS,WAAW;YAC9B,UAAU;YACV,UAAU,OAAO,QAAQ;YACzB,SAAS,OAAO,OAAO;YACvB,UAAU,OAAO,QAAQ;YACzB,QAAQ;YACR,UAAU;gBACR;oBACE,UAAU,SAAS,WAAW;oBAC9B,UAAU,OAAO,QAAQ;gBAC3B;aACD;YACD,UAAU;gBACR,OAAO,OAAO,KAAK;gBACnB,cAAc,OAAO,YAAY;gBACjC,gBAAgB,OAAO,cAAc;gBACrC,qBAAqB,OAAO,mBAAmB;YACjD;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,iCACpB,YAA0B,EAC1B,QAAkB,EAClB,MAAc,EACd,OAIC;IAUD,IAAI;QAEF,MAAM,eAAwC;YAC5C,cAAc,aAAa,YAAY,IAAI;YAC3C;YACA,aAAa,SAAS,eAAe;YACrC,WAAW;YACX;YACA,aAAa,SAAS,eAAe;YACrC,wBAAwB,SAAS,0BAA0B;YAC3D,kBAAkB;QACpB;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE;QAExC,OAAO;YACL,SAAS;YACT,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,cAAc,OAAO,YAAY;YACjC,gBAAgB,OAAO,cAAc;YACrC,qBAAqB,OAAO,mBAAmB;QACjD;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAKO,eAAe;IAQpB,OAAO;QACL,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,QAAQ;IACV;AACF;AAKO,eAAe;IAKpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,sIAAA,CAAA,yBAAsB,AAAD;QAE/C,OAAO;YACL,WAAW;YACX,OAAO;YACP,SAAS,cACL,qCACA;QACN;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,WAAW;YACX,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC9F;IACF;AACF;;;IA/KsB;IA+DA;IAqDA;IAqCA;;AAzJA,+OAAA;AA+DA,+OAAA;AAqDA,+OAAA;AAqCA,+OAAA", "debugId": null}}, {"offset": {"line": 5192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/.next-internal/server/app/_not-found/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {generateCreativeAssetAction as '7f5d637b281692c3f27909f9e0d661b29f958881f8'} from 'ACTIONS_MODULE0'\nexport {generateEnhancedDesignAction as '7f7ba9144001954daaaa5cea5934ab233d76c6e00d'} from 'ACTIONS_MODULE0'\nexport {generateRevo2CreativeAssetAction as '789496ee83d803e1e9644583e4a6f21523a907741c'} from 'ACTIONS_MODULE1'\nexport {generateContentAction as '78a2cc90f0c309202520d68173137d83d08ea32806'} from 'ACTIONS_MODULE0'\nexport {generateContentWithArtifactsAction as '7f184a7753a1c29638132401afe2bdafb4cd96f602'} from 'ACTIONS_MODULE0'\nexport {generateVideoContentAction as '7002a50d713c2964d62133b2af4480664176bd8291'} from 'ACTIONS_MODULE0'\nexport {analyzeBrandAction as '60e429b11b311c0b871a717d8e7e036936b64edea0'} from 'ACTIONS_MODULE0'\nexport {generateGeminiHDDesignAction as '7f1495e5c9392c71a0638d8ee09c874aef3fff06f4'} from 'ACTIONS_MODULE0'\nexport {generateCreativeAsset as '409d8d4d6ee48a33e913d651c38aa0005eeae0dac6'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AAEA;AAMA", "debugId": null}}]}