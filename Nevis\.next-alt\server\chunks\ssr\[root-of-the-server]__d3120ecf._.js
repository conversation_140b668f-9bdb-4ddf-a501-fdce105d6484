module.exports = {

"[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Capabilities Configuration
 * Defines what each model version can do
 */ __turbopack_context__.s({
    "capabilityMatrix": (()=>capabilityMatrix),
    "featureAvailability": (()=>featureAvailability),
    "getCapabilityLevel": (()=>getCapabilityLevel),
    "getMaxQualityForPlatform": (()=>getMaxQualityForPlatform),
    "getModelsByFeature": (()=>getModelsByFeature),
    "getPlatformCapabilities": (()=>getPlatformCapabilities),
    "getSupportedAspectRatios": (()=>getSupportedAspectRatios),
    "hasCapability": (()=>hasCapability),
    "hasFeature": (()=>hasFeature),
    "modelCapabilities": (()=>modelCapabilities),
    "platformCapabilities": (()=>platformCapabilities)
});
const modelCapabilities = {
    'revo-1.0': {
        // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: false,
        aspectRatios: [
            '1:1'
        ],
        maxQuality: 9,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        perfectTextRendering: true,
        highResolution: true // NEW: 2048x2048 support
    },
    'revo-1.5': {
        // Enhanced model with advanced features
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16'
        ],
        maxQuality: 8,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true // Real-time context and trends
    },
    'revo-2.0': {
        // Premium Next-Gen AI model
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16',
            '4:3',
            '3:4'
        ],
        maxQuality: 10,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        characterConsistency: true,
        intelligentEditing: true,
        multimodalReasoning: true // NEW: Advanced visual context understanding
    }
};
const capabilityMatrix = {
    contentGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    designGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    videoGeneration: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'none'
    },
    artifactSupport: {
        'revo-1.0': 'none',
        'revo-1.5': 'full',
        'revo-2.0': 'premium'
    },
    brandConsistency: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'advanced',
        'revo-2.0': 'perfect'
    },
    characterConsistency: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    },
    intelligentEditing: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    }
};
const featureAvailability = {
    // Content features
    hashtagGeneration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    catchyWords: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    subheadlines: [
        'revo-1.5',
        'revo-2.0'
    ],
    callToAction: [
        'revo-1.5',
        'revo-2.0'
    ],
    contentVariants: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Design features
    logoIntegration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    brandColors: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    designExamples: [
        'revo-1.5',
        'revo-2.0'
    ],
    textOverlay: [
        'revo-1.5',
        'revo-2.0'
    ],
    multipleAspectRatios: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Advanced features
    realTimeContext: [
        'revo-1.5',
        'revo-2.0'
    ],
    trendingTopics: [
        'revo-1.5',
        'revo-2.0'
    ],
    marketIntelligence: [
        'revo-1.5',
        'revo-2.0'
    ],
    competitorAnalysis: [
        'revo-2.0'
    ],
    // Revo 2.0 exclusive features
    characterConsistency: [
        'revo-2.0'
    ],
    intelligentEditing: [
        'revo-2.0'
    ],
    inpainting: [
        'revo-2.0'
    ],
    outpainting: [
        'revo-2.0'
    ],
    multimodalReasoning: [
        'revo-2.0'
    ],
    // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)
    perfectTextRendering: [
        'revo-1.0',
        'revo-2.0'
    ],
    highResolution: [
        'revo-1.0',
        'revo-2.0'
    ],
    // Artifact features
    artifactReference: [
        'revo-1.5'
    ],
    exactUseArtifacts: [
        'revo-1.5'
    ],
    textOverlayArtifacts: [
        'revo-1.5'
    ]
};
const platformCapabilities = {
    Instagram: {
        'revo-1.0': {
            aspectRatios: [
                '1:1'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'hashtags'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '1:1',
                '9:16'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'hashtags',
                'stories',
                'reels-ready'
            ]
        }
    },
    Facebook: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'page-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'page-posts',
                'stories'
            ]
        }
    },
    Twitter: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'tweets'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'tweets',
                'threads'
            ]
        }
    },
    LinkedIn: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'professional-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'professional-posts',
                'articles'
            ]
        }
    }
};
function hasCapability(modelId, capability) {
    return modelCapabilities[modelId][capability];
}
function getCapabilityLevel(modelId, capability) {
    return capabilityMatrix[capability][modelId];
}
function hasFeature(modelId, feature) {
    return featureAvailability[feature].includes(modelId);
}
function getModelsByFeature(feature) {
    return [
        ...featureAvailability[feature]
    ];
}
function getPlatformCapabilities(modelId, platform) {
    return platformCapabilities[platform]?.[modelId] || null;
}
function getMaxQualityForPlatform(modelId, platform) {
    const platformCaps = getPlatformCapabilities(modelId, platform);
    return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;
}
function getSupportedAspectRatios(modelId, platform) {
    if (platform) {
        const platformCaps = getPlatformCapabilities(modelId, platform);
        return platformCaps?.aspectRatios ? [
            ...platformCaps.aspectRatios
        ] : [
            ...modelCapabilities[modelId].aspectRatios
        ];
    }
    return [
        ...modelCapabilities[modelId].aspectRatios
    ];
}
}}),
"[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Pricing Configuration
 * Defines credit costs and pricing tiers for each model
 */ __turbopack_context__.s({
    "creditPackages": (()=>creditPackages),
    "getAllPricing": (()=>getAllPricing),
    "getCheapestModel": (()=>getCheapestModel),
    "getModelPricing": (()=>getModelPricing),
    "getModelsByTier": (()=>getModelsByTier),
    "getMostExpensiveModel": (()=>getMostExpensiveModel),
    "modelPricing": (()=>modelPricing),
    "pricingDisplay": (()=>pricingDisplay),
    "pricingTiers": (()=>pricingTiers),
    "usageCalculations": (()=>usageCalculations)
});
const modelPricing = {
    'revo-1.0': {
        creditsPerGeneration: 1.5,
        creditsPerDesign: 1.5,
        creditsPerVideo: 0,
        tier: 'enhanced' // Upgraded from basic
    },
    'revo-1.5': {
        creditsPerGeneration: 2,
        creditsPerDesign: 2,
        creditsPerVideo: 0,
        tier: 'premium'
    },
    'revo-2.0': {
        creditsPerGeneration: 5,
        creditsPerDesign: 5,
        creditsPerVideo: 0,
        tier: 'premium'
    }
};
const pricingTiers = {
    basic: {
        name: 'Basic',
        description: 'Reliable and cost-effective',
        maxCreditsPerGeneration: 2,
        features: [
            'Standard quality generation',
            'Basic brand consistency',
            'Core platform support',
            'Standard processing speed'
        ],
        recommendedFor: [
            'Small businesses',
            'Personal brands',
            'Budget-conscious users',
            'Basic content needs'
        ]
    },
    premium: {
        name: 'Premium',
        description: 'Enhanced features and quality',
        maxCreditsPerGeneration: 10,
        features: [
            'Enhanced quality generation',
            'Advanced brand consistency',
            'Full platform support',
            'Artifact integration',
            'Real-time context',
            'Trending topics',
            'Multiple aspect ratios'
        ],
        recommendedFor: [
            'Growing businesses',
            'Marketing agencies',
            'Content creators',
            'Professional brands'
        ]
    },
    enterprise: {
        name: 'Enterprise',
        description: 'Maximum quality and features',
        maxCreditsPerGeneration: 20,
        features: [
            'Premium quality generation',
            '4K resolution support',
            'Perfect text rendering',
            'Advanced style controls',
            'Priority processing',
            'Dedicated support',
            'Custom integrations'
        ],
        recommendedFor: [
            'Large enterprises',
            'Premium brands',
            'High-volume users',
            'Quality-focused campaigns'
        ]
    }
};
const creditPackages = {
    starter: {
        name: 'Starter Pack',
        credits: 50,
        price: 9.99,
        pricePerCredit: 0.20,
        bestFor: 'revo-1.0',
        estimatedGenerations: {
            'revo-1.0': 50,
            'revo-1.5': 25,
            'imagen-4': 5
        }
    },
    professional: {
        name: 'Professional Pack',
        credits: 200,
        price: 29.99,
        pricePerCredit: 0.15,
        bestFor: 'revo-1.5',
        estimatedGenerations: {
            'revo-1.0': 200,
            'revo-1.5': 100,
            'imagen-4': 20
        }
    },
    business: {
        name: 'Business Pack',
        credits: 500,
        price: 59.99,
        pricePerCredit: 0.12,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 500,
            'revo-1.5': 250,
            'imagen-4': 50
        }
    },
    enterprise: {
        name: 'Enterprise Pack',
        credits: 1000,
        price: 99.99,
        pricePerCredit: 0.10,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 1000,
            'revo-1.5': 500,
            'revo-2.0': 200,
            'imagen-4': 100
        }
    }
};
const usageCalculations = {
    // Calculate cost for a specific generation request
    calculateGenerationCost (modelId, type = 'content') {
        const pricing = modelPricing[modelId];
        switch(type){
            case 'content':
                return pricing.creditsPerGeneration;
            case 'design':
                return pricing.creditsPerDesign;
            case 'video':
                return pricing.creditsPerVideo || 0;
            default:
                return pricing.creditsPerGeneration;
        }
    },
    // Calculate total cost for multiple generations
    calculateBatchCost (requests) {
        return requests.reduce((total, request)=>{
            return total + this.calculateGenerationCost(request.modelId, request.type);
        }, 0);
    },
    // Estimate monthly cost based on usage patterns
    estimateMonthlyCost (usage) {
        const pricing = modelPricing[usage.modelId];
        const dailyCost = usage.generationsPerDay * pricing.creditsPerGeneration + usage.designsPerDay * pricing.creditsPerDesign + (usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0);
        const monthlyCost = dailyCost * 30;
        // Recommend package based on monthly cost
        let recommendedPackage = 'starter';
        if (monthlyCost > 400) recommendedPackage = 'enterprise';
        else if (monthlyCost > 150) recommendedPackage = 'business';
        else if (monthlyCost > 50) recommendedPackage = 'professional';
        return {
            dailyCost,
            monthlyCost,
            recommendedPackage
        };
    },
    // Check if user has enough credits for a request
    canAfford (userCredits, modelId, type = 'content') {
        const cost = this.calculateGenerationCost(modelId, type);
        return userCredits >= cost;
    },
    // Get the best model within budget
    getBestModelForBudget (availableCredits, type = 'content') {
        const affordableModels = [];
        for (const [modelId, pricing] of Object.entries(modelPricing)){
            const cost = type === 'content' ? pricing.creditsPerGeneration : type === 'design' ? pricing.creditsPerDesign : pricing.creditsPerVideo || 0;
            if (cost <= availableCredits && cost > 0) {
                affordableModels.push(modelId);
            }
        }
        // Sort by quality (higher credit cost usually means higher quality)
        return affordableModels.sort((a, b)=>{
            const costA = this.calculateGenerationCost(a, type);
            const costB = this.calculateGenerationCost(b, type);
            return costB - costA; // Descending order (highest quality first)
        });
    }
};
const pricingDisplay = {
    // Format credits for display
    formatCredits (credits) {
        if (credits >= 1000) {
            return `${(credits / 1000).toFixed(1)}K`;
        }
        return credits.toString();
    },
    // Format price for display
    formatPrice (price) {
        return `$${price.toFixed(2)}`;
    },
    // Get pricing tier info
    getTierInfo (modelId) {
        const pricing = modelPricing[modelId];
        return pricingTiers[pricing.tier];
    },
    // Get cost comparison between models
    compareCosts (modelA, modelB) {
        const costA = modelPricing[modelA].creditsPerGeneration;
        const costB = modelPricing[modelB].creditsPerGeneration;
        const difference = Math.abs(costA - costB);
        const percentDifference = (difference / Math.min(costA, costB) * 100).toFixed(0);
        return {
            cheaper: costA < costB ? modelA : modelB,
            moreExpensive: costA > costB ? modelA : modelB,
            difference,
            percentDifference: `${percentDifference}%`,
            ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`
        };
    },
    // Get value proposition for each model
    getValueProposition (modelId) {
        const pricing = modelPricing[modelId];
        const tierInfo = pricingTiers[pricing.tier];
        return {
            model: modelId,
            tier: pricing.tier,
            creditsPerGeneration: pricing.creditsPerGeneration,
            valueScore: tierInfo.features.length / pricing.creditsPerGeneration,
            description: tierInfo.description,
            bestFor: tierInfo.recommendedFor
        };
    }
};
function getModelPricing(modelId) {
    return modelPricing[modelId];
}
function getAllPricing() {
    return modelPricing;
}
function getModelsByTier(tier) {
    return Object.entries(modelPricing).filter(([_, pricing])=>pricing.tier === tier).map(([modelId])=>modelId);
}
function getCheapestModel() {
    return Object.entries(modelPricing).reduce((cheapest, [modelId, pricing])=>{
        const currentCheapest = modelPricing[cheapest];
        return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ? modelId : cheapest;
    }, 'revo-1.0');
}
function getMostExpensiveModel() {
    return Object.entries(modelPricing).reduce((mostExpensive, [modelId, pricing])=>{
        const currentMostExpensive = modelPricing[mostExpensive];
        return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ? modelId : mostExpensive;
    }, 'revo-1.0');
}
}}),
"[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Configurations
 * Centralized configuration for all Revo model versions
 */ __turbopack_context__.s({
    "compareModels": (()=>compareModels),
    "getAllModelConfigs": (()=>getAllModelConfigs),
    "getLatestModels": (()=>getLatestModels),
    "getModelConfig": (()=>getModelConfig),
    "getModelForBudget": (()=>getModelForBudget),
    "getModelsByStatus": (()=>getModelsByStatus),
    "getModelsByTier": (()=>getModelsByTier),
    "getRecommendedModel": (()=>getRecommendedModel),
    "modelConfigs": (()=>modelConfigs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)");
;
;
// Base configurations for different AI services
const baseConfigs = {
    'gemini-2.0': {
        aiService: 'gemini-2.0',
        fallbackServices: [
            'gemini-2.5',
            'openai'
        ],
        maxRetries: 3,
        timeout: 30000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 85,
            enhancementLevel: 5
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    },
    'gemini-2.5': {
        aiService: 'gemini-2.5',
        fallbackServices: [
            'gemini-2.0',
            'openai'
        ],
        maxRetries: 2,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 90,
            enhancementLevel: 7
        },
        promptSettings: {
            temperature: 0.8,
            maxTokens: 4096,
            topP: 0.95,
            topK: 50
        }
    },
    'openai': {
        aiService: 'openai',
        fallbackServices: [
            'gemini-2.5',
            'gemini-2.0'
        ],
        maxRetries: 3,
        timeout: 35000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 88,
            enhancementLevel: 6
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 3000,
            topP: 0.9
        }
    },
    'gemini-2.5-flash-image': {
        aiService: 'gemini-2.5-flash-image',
        fallbackServices: [
            'imagen-4',
            'gemini-2.5'
        ],
        maxRetries: 3,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '2048x2048',
            compressionLevel: 95,
            enhancementLevel: 8 // Reduced for cleaner designs (was 10)
        },
        promptSettings: {
            temperature: 0.4,
            maxTokens: 4096,
            topP: 0.7,
            topK: 30 // Fewer creative choices for consistency (was 60)
        }
    }
};
const modelConfigs = {
    'revo-1.0': {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        version: '1.0.0',
        description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',
        longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',
        icon: 'Zap',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.0'],
        features: [
            'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',
            '1:1 Images with High Resolution',
            'Core Features',
            'Proven Performance',
            'Multi-platform Support',
            'Enhanced Brand Consistency',
            'Perfect Text Rendering',
            'High-Resolution Output (2048x2048)'
        ],
        releaseDate: '2024-01-15',
        lastUpdated: '2025-01-27'
    },
    'revo-1.5': {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        version: '1.5.0',
        description: 'Enhanced Model - Advanced Features',
        longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',
        icon: 'Sparkles',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.5'],
        config: {
            ...baseConfigs['gemini-2.5'],
            qualitySettings: {
                ...baseConfigs['gemini-2.5'].qualitySettings,
                enhancementLevel: 8
            }
        },
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.5'],
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations',
            'Professional Templates',
            'Advanced Brand Integration',
            'Real-time Context',
            'Trending Topics Integration'
        ],
        releaseDate: '2024-06-20',
        lastUpdated: '2024-12-15'
    },
    'revo-2.0': {
        id: 'revo-2.0',
        name: 'Revo 2.0',
        version: '2.0.0',
        description: 'Next-Gen Model - Advanced AI with native image generation',
        longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',
        icon: 'Rocket',
        badge: 'Next-Gen',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-2.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-2.0'],
        features: [
            'Next-Gen AI Engine',
            'Native Image Generation',
            'Character Consistency',
            'Intelligent Editing',
            'Inpainting & Outpainting',
            'Multimodal Reasoning',
            'All Aspect Ratios',
            'Perfect Brand Consistency'
        ],
        releaseDate: '2025-01-27',
        lastUpdated: '2025-01-27'
    }
};
function getModelConfig(modelId) {
    const config = modelConfigs[modelId];
    if (!config) {
        throw new Error(`Model configuration not found for: ${modelId}`);
    }
    return config;
}
function getAllModelConfigs() {
    return Object.values(modelConfigs);
}
function getModelsByStatus(status) {
    return getAllModelConfigs().filter((model)=>model.status === status);
}
function getModelsByTier(tier) {
    return getAllModelConfigs().filter((model)=>model.pricing.tier === tier);
}
function getLatestModels() {
    return getAllModelConfigs().sort((a, b)=>new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()).slice(0, 3);
}
function getRecommendedModel() {
    // Return Revo 1.5 as the recommended balanced option
    return modelConfigs['revo-1.5'];
}
function getModelForBudget(maxCredits) {
    return getAllModelConfigs().filter((model)=>model.pricing.creditsPerGeneration <= maxCredits).sort((a, b)=>a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);
}
function compareModels(modelA, modelB) {
    const configA = getModelConfig(modelA);
    const configB = getModelConfig(modelB);
    return {
        quality: {
            a: configA.capabilities.maxQuality,
            b: configB.capabilities.maxQuality,
            winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB
        },
        cost: {
            a: configA.pricing.creditsPerGeneration,
            b: configB.pricing.creditsPerGeneration,
            winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB
        },
        features: {
            a: configA.features.length,
            b: configB.features.length,
            winner: configA.features.length > configB.features.length ? modelA : modelB
        },
        status: {
            a: configA.status,
            b: configB.status,
            recommendation: configA.status === 'stable' || configB.status === 'stable' ? configA.status === 'stable' ? modelA : modelB : modelA
        }
    };
}
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:https [external] (node:https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:https", () => require("node:https"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:process [external] (node:process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:process", () => require("node:process"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:path [external] (node:path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:path", () => require("node:path"));

module.exports = mod;
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[project]/src/services/weather.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/weather.ts
__turbopack_context__.s({
    "getWeather": (()=>getWeather)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
;
const API_KEY = process.env.OPENWEATHERMAP_API_KEY;
const BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
async function getWeather(location) {
    if (!API_KEY || API_KEY === 'YOUR_OPENWEATHERMAP_API_KEY' || API_KEY.length < 20) {
        return null;
    }
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(`${BASE_URL}?q=${location}&appid=${API_KEY}&units=imperial`);
        if (!response.ok) {
            // Return null to allow the flow to continue without weather data
            return `Could not retrieve weather information due to an API error (Status: ${response.status})`;
        }
        const data = await response.json();
        if (data && data.weather && data.main) {
            const description = data.weather[0].description;
            const temp = Math.round(data.main.temp);
            return `${description} with a temperature of ${temp}°F`;
        }
        return null;
    } catch (error) {
        return null;
    }
}
}}),
"[project]/src/services/events.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/events.ts
__turbopack_context__.s({
    "getEvents": (()=>getEvents)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/add.mjs [app-rsc] (ecmascript)");
;
;
const API_KEY = process.env.EVENTBRITE_PRIVATE_TOKEN;
const BASE_URL = 'https://www.eventbriteapi.com/v3/events/search/';
async function getEvents(location, date) {
    if (!API_KEY || API_KEY === 'YOUR_EVENTBRITE_PRIVATE_TOKEN' || API_KEY.length < 10) {
        return null;
    }
    // Eventbrite is more flexible with location strings.
    const city = location.split(',')[0].trim();
    // Search for events starting from today up to one week from now to get more results
    const startDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd'T'HH:mm:ss'Z'");
    const endDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(date, {
        days: 7
    }), "yyyy-MM-dd'T'HH:mm:ss'Z'");
    try {
        const url = `${BASE_URL}?location.address=${city}&start_date.range_start=${startDate}&start_date.range_end=${endDate}&sort_by=date`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(url, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Accept': 'application/json'
            }
        });
        if (!response.ok) {
            const errorBody = await response.text();
            // Return null to allow the flow to continue without event data
            return `Could not retrieve local event information due to an API error (Status: ${response.status}).`;
        }
        const data = await response.json();
        if (data.events && data.events.length > 0) {
            const eventNames = data.events.slice(0, 5).map((event)=>event.name.text);
            return `local events happening soon include: ${eventNames.join(', ')}`;
        } else {
            return 'no major local events found on Eventbrite for the upcoming week';
        }
    } catch (error) {
        return null;
    }
}
}}),
"[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f274d1b42f64738f0b078f9e26b59975d67c6de99":"getWeatherTool","7fa7aa60011491c575cae15b62d3e7832a383a5811":"getEventsTool"},"",""] */ __turbopack_context__.s({
    "getEventsTool": (()=>getEventsTool),
    "getWeatherTool": (()=>getWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview Defines Genkit tools for fetching local weather and event data.
 * This allows the AI to dynamically decide when to pull in local information
 * to make social media posts more relevant and timely.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/weather.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/events.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
const getWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getWeather',
    description: 'Gets the current weather for a specific location. Use this to make posts more relevant to the current conditions.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    const weather = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeather"])(input.location);
    return weather || 'Could not retrieve weather information.';
});
const getEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEvents',
    description: 'Finds local events happening on or after the current date for a specific location. Use this to create timely posts about local happenings.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    // Tools will always be called with the current date
    const events = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEvents"])(input.location, new Date());
    return events || 'Could not retrieve local event information.';
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getWeatherTool,
    getEventsTool
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getWeatherTool, "7f274d1b42f64738f0b078f9e26b59975d67c6de99", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getEventsTool, "7fa7aa60011491c575cae15b62d3e7832a383a5811", null);
}}),
"[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced Local Data Tools - Events and Weather Integration
 * 
 * This module provides real-time local events and weather data
 * for contextually aware content generation.
 */ __turbopack_context__.s({
    "getEnhancedEventsTool": (()=>getEnhancedEventsTool),
    "getEnhancedWeatherTool": (()=>getEnhancedWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const getEnhancedEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedEvents',
    description: 'Fetch local events from Eventbrite API that are relevant to the business type and location',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for events (city, country or coordinates)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to filter relevant events'),
        radius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('25km').describe('Search radius for events'),
        timeframe: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('this_week').describe('Time period: today, this_week, this_month')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        venue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        is_free: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean(),
        relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number()
    }))
}, async (input)=>{
    try {
        if (!process.env.EVENTBRITE_API_KEY) {
            return getEventsFallback(input.location, input.businessType);
        }
        // Convert location to coordinates if needed
        const locationQuery = await geocodeLocation(input.location);
        // Build Eventbrite API request
        const params = new URLSearchParams({
            'location.address': input.location,
            'location.within': input.radius,
            'start_date.range_start': getDateRange(input.timeframe).start,
            'start_date.range_end': getDateRange(input.timeframe).end,
            'sort_by': 'relevance',
            'page_size': '20',
            'expand': 'venue,category'
        });
        const response = await fetch(`https://www.eventbriteapi.com/v3/events/search/?${params}`, {
            headers: {
                'Authorization': `Bearer ${process.env.EVENTBRITE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Eventbrite API error: ${response.status}`);
        }
        const data = await response.json();
        // Process and filter events by business relevance
        const relevantEvents = processEventbriteEvents(data.events || [], input.businessType);
        return relevantEvents.slice(0, 10);
    } catch (error) {
        return getEventsFallback(input.location, input.businessType);
    }
});
const getEnhancedWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedWeather',
    description: 'Fetch current weather and forecast with business context and content opportunities',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for weather (city, country)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to provide relevant weather context'),
        includeForecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional().default(false).describe('Include 5-day forecast')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        humidity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        feels_like: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        forecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional()
    })
}, async (input)=>{
    try {
        if (!process.env.OPENWEATHER_API_KEY) {
            return getWeatherFallback(input.location, input.businessType);
        }
        // Current weather
        const currentParams = new URLSearchParams({
            q: input.location,
            appid: process.env.OPENWEATHER_API_KEY,
            units: 'metric'
        });
        const currentResponse = await fetch(`https://api.openweathermap.org/data/2.5/weather?${currentParams}`);
        if (!currentResponse.ok) {
            throw new Error(`OpenWeather API error: ${currentResponse.status}`);
        }
        const currentData = await currentResponse.json();
        // Process weather data with business context
        const weatherContext = processWeatherData(currentData, input.businessType);
        // Get forecast if requested
        if (input.includeForecast) {
            const forecastParams = new URLSearchParams({
                q: input.location,
                appid: process.env.OPENWEATHER_API_KEY,
                units: 'metric'
            });
            const forecastResponse = await fetch(`https://api.openweathermap.org/data/2.5/forecast?${forecastParams}`);
            if (forecastResponse.ok) {
                const forecastData = await forecastResponse.json();
                weatherContext.forecast = processForecastData(forecastData, input.businessType);
            }
        }
        return weatherContext;
    } catch (error) {
        return getWeatherFallback(input.location, input.businessType);
    }
});
/**
 * Helper functions
 */ async function geocodeLocation(location) {
    try {
        if (!process.env.OPENWEATHER_API_KEY) return null;
        const params = new URLSearchParams({
            q: location,
            limit: '1',
            appid: process.env.OPENWEATHER_API_KEY
        });
        const response = await fetch(`https://api.openweathermap.org/geo/1.0/direct?${params}`);
        if (response.ok) {
            const data = await response.json();
            if (data.length > 0) {
                return {
                    lat: data[0].lat,
                    lon: data[0].lon
                };
            }
        }
    } catch (error) {}
    return null;
}
function getDateRange(timeframe) {
    const now = new Date();
    const start = new Date(now);
    let end = new Date(now);
    switch(timeframe){
        case 'today':
            end.setDate(end.getDate() + 1);
            break;
        case 'this_week':
            end.setDate(end.getDate() + 7);
            break;
        case 'this_month':
            end.setMonth(end.getMonth() + 1);
            break;
        default:
            end.setDate(end.getDate() + 7);
    }
    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}
function processEventbriteEvents(events, businessType) {
    return events.map((event)=>{
        const relevanceScore = calculateEventRelevance(event, businessType);
        return {
            name: event.name?.text || 'Unnamed Event',
            description: event.description?.text?.substring(0, 200) || '',
            start_date: event.start?.local || event.start?.utc || '',
            end_date: event.end?.local || event.end?.utc,
            venue: event.venue?.name || 'Online Event',
            category: event.category?.name || 'General',
            url: event.url,
            is_free: event.is_free || false,
            relevance_score: relevanceScore
        };
    }).filter((event)=>event.relevance_score >= 5).sort((a, b)=>b.relevance_score - a.relevance_score);
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventDescription = (event.description?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventDescription.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    // Free events get slight bonus for broader appeal
    if (event.is_free) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'cryptocurrency',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant',
            'hospitality'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition',
            'sports'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation',
            'startup'
        ],
        'beauty': [
            'beauty',
            'cosmetics',
            'skincare',
            'wellness',
            'spa',
            'fashion'
        ],
        'retail': [
            'retail',
            'shopping',
            'ecommerce',
            'business',
            'sales',
            'marketing'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
function processWeatherData(weatherData, businessType) {
    const temperature = Math.round(weatherData.main.temp);
    const condition = weatherData.weather[0].main;
    const description = weatherData.weather[0].description;
    return {
        temperature,
        condition,
        description,
        humidity: weatherData.main.humidity,
        feels_like: Math.round(weatherData.main.feels_like),
        location: weatherData.name,
        content_opportunities: generateWeatherContentOpportunities(condition, temperature, businessType),
        business_impact: generateBusinessWeatherImpact(condition, temperature, businessType)
    };
}
function processForecastData(forecastData, businessType) {
    const dailyForecasts = forecastData.list.filter((_, index)=>index % 8 === 0).slice(0, 5);
    return dailyForecasts.map((forecast)=>({
            date: new Date(forecast.dt * 1000).toLocaleDateString(),
            temperature: Math.round(forecast.main.temp),
            condition: forecast.weather[0].main,
            business_opportunity: generateBusinessWeatherImpact(forecast.weather[0].main, forecast.main.temp, businessType)
        }));
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    // Business-specific weather opportunities
    const businessOpportunities = getBusinessWeatherOpportunities(businessType, condition, temperature);
    opportunities.push(...businessOpportunities);
    return opportunities;
}
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'retail': {
            'sunny': 'Great shopping weather, people are out and about',
            'rain': 'Perfect time for online shopping promotions',
            'hot': 'Opportunity to promote summer collections and cooling products',
            'cold': 'Ideal for promoting warm clothing and comfort items'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['retail'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function getBusinessWeatherOpportunities(businessType, condition, temperature) {
    // Business-specific weather content opportunities
    const opportunities = [];
    if (businessType.toLowerCase().includes('restaurant')) {
        if (condition === 'sunny') opportunities.push('Outdoor dining promotion', 'Fresh seasonal menu highlight');
        if (condition === 'rain') opportunities.push('Cozy indoor atmosphere', 'Comfort food specials');
    }
    if (businessType.toLowerCase().includes('fitness')) {
        if (condition === 'sunny') opportunities.push('Outdoor workout motivation', 'Vitamin D benefits');
        if (temperature > 25) opportunities.push('Hydration importance', 'Summer fitness tips');
    }
    return opportunities;
}
// Fallback functions
function getEventsFallback(location, businessType) {
    return [
        {
            name: `${businessType} networking event in ${location}`,
            description: `Local networking opportunity for ${businessType} professionals`,
            start_date: new Date(Date.now() + 86400000 * 3).toISOString(),
            venue: `${location} Business Center`,
            category: 'Business & Professional',
            is_free: true,
            relevance_score: 8
        }
    ];
}
function getWeatherFallback(location, businessType) {
    return {
        temperature: 22,
        condition: 'Clear',
        description: 'clear sky',
        humidity: 60,
        feels_like: 24,
        location: location,
        content_opportunities: [
            'Pleasant weather content opportunity',
            'Comfortable conditions messaging'
        ],
        business_impact: 'Current weather conditions are favorable for business activities'
    };
}
}}),
"[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced AI Content Generation Prompt
 * 
 * This prompt integrates trending topics, competitor analysis, cultural optimization,
 * human-like content generation, and traffic-driving strategies.
 */ __turbopack_context__.s({
    "ADVANCED_AI_PROMPT": (()=>ADVANCED_AI_PROMPT)
});
const ADVANCED_AI_PROMPT = `You are an elite social media strategist, cultural anthropologist, and viral content creator with deep expertise in the {{{businessType}}} industry.

Your mission is to create content that:
🎯 Captures trending conversations and cultural moments
🚀 Drives maximum traffic and business results
🤝 Feels authentically human and culturally sensitive
💡 Differentiates from competitors strategically
📈 Optimizes for platform-specific viral potential
🌤️ Integrates current weather and local events naturally
🎪 Leverages local happenings for timely relevance
🌍 Uses ENGLISH ONLY for all content generation

BUSINESS INTELLIGENCE:
- Industry: {{{businessType}}}
- Location: {{{location}}}
- Brand Voice: {{{writingTone}}}
- Content Themes: {{{contentThemes}}}
- Day: {{{dayOfWeek}}}
- Date: {{{currentDate}}}
{{#if platform}}- Primary Platform: {{{platform}}}{{/if}}
{{#if services}}- Services/Products: {{{services}}}{{/if}}
{{#if targetAudience}}- Target Audience: {{{targetAudience}}}{{/if}}
{{#if keyFeatures}}- Key Features: {{{keyFeatures}}}{{/if}}
{{#if competitiveAdvantages}}- Competitive Edge: {{{competitiveAdvantages}}}{{/if}}
{{#if contentVariation}}- Content Approach: {{{contentVariation}}} (MANDATORY: Use this specific approach for content generation){{/if}}

TRENDING TOPICS INTEGRATION:
Research and incorporate current trending topics relevant to:
- {{{businessType}}} industry developments
- {{{location}}} local events and cultural moments
- Platform-specific trending hashtags and conversations
- Seasonal relevance and timely opportunities
- News events that connect to your business value

COMPETITOR DIFFERENTIATION STRATEGY:
Analyze and differentiate from typical competitor content by:
- Avoiding generic industry messaging
- Finding unique angles on common topics
- Highlighting authentic personal/business stories
- Focusing on underserved audience needs
- Creating content gaps competitors miss
- Using authentic local cultural connections

CONTENT DIVERSITY ENFORCEMENT:
CRITICAL: Each post must be completely different from previous generations:
- Use different opening hooks (question, statement, story, statistic, quote)
- Vary content structure (problem-solution, story-lesson, tip-benefit, behind-scenes)
- Alternate between different emotional tones (inspiring, educational, entertaining, personal)
- Change content length and paragraph structure significantly
- Use different call-to-action styles (direct, subtle, question-based, action-oriented)
- Vary hashtag themes and combinations
- Never repeat the same content pattern or messaging approach

{{#if contentVariation}}
MANDATORY CONTENT VARIATION APPROACH - {{{contentVariation}}}:

Use the following approach based on the content variation specified:
- For "trending_hook": Start with a trending topic or viral conversation, connect the trend to your business naturally, use current social media language and references, include trending hashtags and phrases
- For "story_driven": Begin with a compelling personal or customer story, use narrative structure with beginning, middle, end, include emotional elements and relatable characters, end with a meaningful lesson or takeaway
- For "educational_tip": Lead with valuable, actionable advice, use numbered lists or step-by-step format, position your business as the expert solution, include "did you know" or "pro tip" elements
- For "behind_scenes": Show the human side of your business, include process, preparation, or team moments, use authentic, unpolished language, create connection through transparency
- For "question_engagement": Start with a thought-provoking question, encourage audience participation and responses, use polls, "this or that," or opinion requests, build community through conversation
- For "statistic_driven": Lead with surprising or compelling statistics, use data to support your business value, include industry insights and research, position your business as data-informed
- For "personal_insight": Share personal experiences or observations, use first-person perspective and authentic voice, include lessons learned or mistakes made, connect personal growth to business value
- For "industry_contrarian": Challenge common industry assumptions, present alternative viewpoints respectfully, use "unpopular opinion" or "hot take" framing, support contrarian views with evidence
- For "local_cultural": Reference local events, landmarks, or culture, use location-specific language and references, connect to community values and traditions, show deep local understanding
- For "seasonal_relevance": Connect to current season, weather, or holidays, use timely references and seasonal language, align business offerings with seasonal needs
- For "problem_solution": Identify a specific customer pain point, agitate the problem to create urgency, present your business as the clear solution, use before/after or transformation language
- For "inspiration_motivation": Use uplifting, motivational language, include inspirational quotes or mantras, focus on transformation and possibility, connect inspiration to business outcomes

Apply the specific approach for the {{{contentVariation}}} variation throughout your content generation.
{{/if}}

CULTURAL & LOCATION OPTIMIZATION:
For {{{location}}}, incorporate:
- Local cultural nuances and values
- Regional language preferences and expressions
- Community customs and social norms
- Seasonal and cultural calendar awareness
- Local landmarks, events, and references
- Respectful acknowledgment of cultural diversity

INTELLIGENT CONTEXT USAGE:
{{#if contextInstructions}}
CONTEXT INSTRUCTIONS FOR THIS SPECIFIC POST:
{{{contextInstructions}}}

Follow these instructions precisely - they are based on expert analysis of what information is relevant for this specific business type and location.
{{/if}}

WEATHER & EVENTS INTEGRATION:
{{#if selectedWeather}}
- Current weather: {{{selectedWeather.temperature}}}°C, {{{selectedWeather.condition}}}
- Business impact: {{{selectedWeather.business_impact}}}
- Content opportunities: {{{selectedWeather.content_opportunities}}}
{{/if}}

{{#if selectedEvents}}
- Relevant local events:
{{#each selectedEvents}}
  * {{{this.name}}} ({{{this.category}}}) - {{{this.start_date}}}
{{/each}}
{{/if}}

Use this information ONLY if the context instructions indicate it's relevant for this business type.

HUMAN-LIKE AUTHENTICITY MARKERS:
Make content feel genuinely human by:
- Using conversational, imperfect language
- Including personal experiences and observations
- Showing vulnerability and learning moments
- Using specific details over generic statements
- Adding natural speech patterns and contractions
- Including time-specific references (today, this morning)
- Expressing genuine emotions and reactions

TRAFFIC-DRIVING OPTIMIZATION:
Maximize engagement and traffic through:
- Curiosity gaps that demand attention
- Shareability factors that encourage spreading
- Conversion triggers that drive action
- Social proof elements that build trust
- Interactive elements that boost engagement
- Viral hooks that capture trending conversations

ADVANCED COPYWRITING FRAMEWORKS:
1. **AIDA Framework**: Attention → Interest → Desire → Action
2. **PAS Framework**: Problem → Agitation → Solution  
3. **Storytelling Arc**: Setup → Conflict → Resolution → Lesson
4. **Social Proof Stack**: Testimonial → Statistics → Authority → Community
5. **Curiosity Loop**: Hook → Tension → Payoff → Next Hook

PSYCHOLOGICAL TRIGGERS FOR MAXIMUM ENGAGEMENT:
✅ **Urgency & Scarcity**: Time-sensitive opportunities
✅ **Social Proof**: Community validation and testimonials
✅ **FOMO**: Exclusive access and insider information
✅ **Curiosity Gaps**: Intriguing questions and reveals
✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy
✅ **Authority**: Expert insights and industry knowledge
✅ **Reciprocity**: Valuable tips and free insights
✅ **Tribal Identity**: Community belonging and shared values

CONTENT GENERATION REQUIREMENTS:

Generate a comprehensive social media post with these components:

1. **CAPTION (content)**:
   - Start with a trending topic hook or cultural moment
   - Use authentic, conversational human language
   - Include competitor differentiation naturally
   - Apply psychological triggers strategically
   - Incorporate local cultural references appropriately
   - End with traffic-driving call-to-action
   - Length optimized for platform and engagement
   - Feel like it was written by a real person, not AI

2. **CATCHY WORDS (catchyWords)**:
   - Create relevant, business-focused catchy words (max 5 words)
   - MUST be directly related to the specific business services/products
   - Use clear, professional language that matches the business type
   - Focus on the business value proposition or key service
   - Avoid generic phrases like "Banking Made Easy" or random financial terms
   - Examples: For a restaurant: "Fresh Daily Specials", For a gym: "Transform Your Body", For a salon: "Expert Hair Care"
   - Required for ALL posts - this is the main visual text
   - Optimize for visual impact and business relevance

3. **SUBHEADLINE (subheadline)** - OPTIONAL:
   - Add only when it would make the post more effective
   - Maximum 14 words
   - Use your marketing expertise to decide when needed
   - Should complement the catchy words and enhance the message
   - Examples: When explaining a complex service, highlighting a special offer, or providing context
   - Skip if the catchy words and caption are sufficient

4. **CALL TO ACTION (callToAction)** - OPTIONAL:
   - Add only when it would drive better engagement or conversions
   - Use your marketing expertise to decide when needed
   - Should be specific and actionable
   - Examples: "Book Now", "Call Today", "Visit Us", "Learn More", "Get Started"
   - Skip if the post is more about awareness or engagement rather than direct action

5. **HASHTAGS**:
   - Mix trending hashtags with niche industry tags
   - Include location-specific and cultural hashtags
   - Balance high-competition and low-competition tags
   - Ensure cultural sensitivity and appropriateness
   - Optimize quantity for platform (Instagram: 20-30, LinkedIn: 3-5, etc.)

6. **CONTENT VARIANTS (contentVariants)**:
   Generate 2-3 alternative approaches:

   **Variant 1 - Trending Topic Angle**:
   - Hook into current trending conversation
   - Connect trend to business value naturally
   - Use viral content patterns
   - Include shareability factors

   **Variant 2 - Cultural Connection Angle**:
   - Start with local cultural reference
   - Show deep community understanding
   - Use location-specific language naturally
   - Build authentic local connections

   **Variant 3 - Competitor Differentiation Angle**:
   - Address common industry pain points differently
   - Highlight unique business approach
   - Use contrarian but respectful positioning
   - Show authentic expertise and experience

   For each variant, provide:
   - The alternative caption content
   - The strategic approach used
   - Why this variant will drive traffic and engagement
   - Cultural sensitivity considerations

QUALITY STANDARDS:
- Every word serves engagement or conversion purpose
- Content feels authentically human, never robotic
- Cultural references are respectful and accurate
- Trending topics are naturally integrated, not forced
- Competitor differentiation is subtle but clear
- Traffic-driving elements are seamlessly woven in
- Platform optimization is invisible but effective
- Local cultural nuances are appropriately honored

TRAFFIC & CONVERSION OPTIMIZATION:
- Include clear value proposition for audience
- Create multiple engagement touchpoints
- Use psychological triggers ethically
- Provide shareable insights or entertainment
- Include conversion pathway (comment, DM, visit, etc.)
- Optimize for algorithm preferences
- Encourage community building and return visits

WEBSITE REFERENCE GUIDELINES:
{{#if websiteUrl}}
- Website available for CTAs: {{{websiteUrl}}} (use clean format without https:// or www.)
- Only include website when CTA specifically calls for it (e.g., "check us out online", "visit our site")
- Don't force website into every post - use contextually when it makes sense
- Examples: "Visit us online", "Check our website", "Learn more at [clean-url]"
{{else}}
- No website URL provided - focus on other CTAs (DM, call, visit location)
{{/if}}

LANGUAGE REQUIREMENTS:
🌍 TEXT CLARITY: Generate clear, readable text
{{#if useLocalLanguage}}
- You may use local language text when 100% certain of spelling, meaning, and cultural appropriateness
- Mix local language with English naturally (1-2 local words maximum per text element)
- Only use commonly known local words that add cultural connection to {{{location}}}
- When uncertain about local language accuracy, use English instead
- Better to use clear English than incorrect or garbled local language
{{else}}
- USE ONLY ENGLISH for all text content (captions, hashtags, call-to-actions)
- Do not use any local language words or phrases
- Keep all text elements in clear, professional English
- Focus on universal messaging that works across all markets
{{/if}}
- Do NOT use corrupted, gibberish, or unreadable character sequences
- Do NOT use random symbols or malformed text
- Ensure all text is properly formatted and legible
- Avoid character encoding issues or text corruption
- All text must be clear and professional
- Prevent any garbled or nonsensical character combinations

Your response MUST be a valid JSON object that conforms to the output schema.
Focus on creating content that real humans will love, share, and act upon.`;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[project]/src/services/rss-feed-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * RSS Feed Service for Trending Content & Social Media Insights
 * Fetches and parses RSS feeds to extract trending topics, keywords, and themes
 */ __turbopack_context__.s({
    "RSSFeedService": (()=>RSSFeedService),
    "rssService": (()=>rssService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/xml2js/lib/xml2js.js [app-rsc] (ecmascript)");
;
class RSSFeedService {
    cache = new Map();
    cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000;
    feedUrls = {
        // Social Media & Marketing Trends
        socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,
        socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,
        bufferBlog: process.env.RSS_BUFFER_BLOG,
        hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,
        sproutSocial: process.env.RSS_SPROUT_SOCIAL,
        laterBlog: process.env.RSS_LATER_BLOG,
        // Trending Topics & News
        googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,
        redditPopular: process.env.RSS_REDDIT_POPULAR,
        buzzfeed: process.env.RSS_BUZZFEED,
        twitterTrending: process.env.RSS_TWITTER_TRENDING,
        // Business & Marketing
        hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,
        contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,
        marketingProfs: process.env.RSS_MARKETING_PROFS,
        marketingLand: process.env.RSS_MARKETING_LAND,
        neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,
        // Industry News
        techCrunch: process.env.RSS_TECHCRUNCH,
        mashable: process.env.RSS_MASHABLE,
        theVerge: process.env.RSS_THE_VERGE,
        wired: process.env.RSS_WIRED,
        // Platform-Specific
        instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,
        facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,
        linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,
        youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,
        tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,
        // Analytics & Data
        googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,
        hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,
        // Design & Creative
        canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,
        adobeBlog: process.env.RSS_ADOBE_BLOG,
        creativeBloq: process.env.RSS_CREATIVE_BLOQ,
        // Seasonal & Events
        eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG
    };
    /**
   * Fetch and parse a single RSS feed
   */ async fetchRSSFeed(url, sourceName) {
        try {
            // Check cache first
            const cached = this.cache.get(url);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Nevis-AI-Content-Generator/1.0'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const xmlData = await response.text();
            const parsed = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseStringPromise"])(xmlData);
            const articles = [];
            const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];
            const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');
            for (const item of items.slice(0, maxArticles)){
                const article = {
                    title: this.extractText(item.title),
                    description: this.extractText(item.description || item.summary),
                    link: this.extractText(item.link || item.id),
                    pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),
                    category: this.extractText(item.category),
                    keywords: this.extractKeywords(this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)),
                    source: sourceName
                };
                articles.push(article);
            }
            // Cache the results
            this.cache.set(url, {
                data: articles,
                timestamp: Date.now()
            });
            return articles;
        } catch (error) {
            return [];
        }
    }
    /**
   * Extract text content from RSS item fields
   */ extractText(field) {
        if (!field) return '';
        if (typeof field === 'string') return field;
        if (Array.isArray(field) && field.length > 0) {
            return typeof field[0] === 'string' ? field[0] : field[0]._ || '';
        }
        if (typeof field === 'object' && field._) return field._;
        return '';
    }
    /**
   * Extract keywords from text content
   */ extractKeywords(text) {
        if (!text) return [];
        // Remove HTML tags and normalize text
        const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
        // Extract meaningful words (3+ characters, not common stop words)
        const stopWords = new Set([
            'the',
            'and',
            'for',
            'are',
            'but',
            'not',
            'you',
            'all',
            'can',
            'had',
            'her',
            'was',
            'one',
            'our',
            'out',
            'day',
            'get',
            'has',
            'him',
            'his',
            'how',
            'its',
            'may',
            'new',
            'now',
            'old',
            'see',
            'two',
            'who',
            'boy',
            'did',
            'she',
            'use',
            'way',
            'will',
            'with'
        ]);
        const words = cleanText.split(' ').filter((word)=>word.length >= 3 && !stopWords.has(word)).slice(0, 10); // Limit to top 10 keywords per article
        return Array.from(new Set(words)); // Remove duplicates
    }
    /**
   * Fetch all RSS feeds and return trending data
   */ async getTrendingData() {
        const allArticles = [];
        const fetchPromises = [];
        // Fetch all feeds concurrently
        for (const [sourceName, url] of Object.entries(this.feedUrls)){
            if (url) {
                fetchPromises.push(this.fetchRSSFeed(url, sourceName));
            }
        }
        const results = await Promise.allSettled(fetchPromises);
        // Collect all successful results
        results.forEach((result)=>{
            if (result.status === 'fulfilled') {
                allArticles.push(...result.value);
            }
        });
        // Sort articles by publication date (newest first)
        allArticles.sort((a, b)=>b.pubDate.getTime() - a.pubDate.getTime());
        // Extract trending keywords and topics
        const allKeywords = [];
        const allTopics = [];
        const allThemes = [];
        allArticles.forEach((article)=>{
            allKeywords.push(...article.keywords);
            if (article.title) allTopics.push(article.title);
            if (article.category) allThemes.push(article.category);
        });
        // Count frequency and get top items
        const keywordCounts = this.getTopItems(allKeywords, 50);
        const topicCounts = this.getTopItems(allTopics, 30);
        const themeCounts = this.getTopItems(allThemes, 20);
        // 🚀 ENHANCED: Generate hashtag analytics
        const hashtagAnalytics = this.generateHashtagAnalytics(allArticles, keywordCounts);
        return {
            keywords: keywordCounts,
            hashtags: keywordCounts.map((keyword)=>`#${keyword.replace(/\s+/g, '')}`),
            topics: topicCounts,
            themes: themeCounts,
            articles: allArticles.slice(0, 100),
            lastUpdated: new Date(),
            hashtagAnalytics
        };
    }
    /**
   * Get top items by frequency
   */ getTopItems(items, limit) {
        const counts = new Map();
        items.forEach((item)=>{
            const normalized = item.toLowerCase().trim();
            if (normalized.length >= 3) {
                counts.set(normalized, (counts.get(normalized) || 0) + 1);
            }
        });
        return Array.from(counts.entries()).sort((a, b)=>b[1] - a[1]).slice(0, limit).map(([item])=>item);
    }
    /**
   * Get trending keywords for a specific category
   */ async getTrendingKeywordsByCategory(category) {
        const trendingData = await this.getTrendingData();
        const categoryFeeds = {
            social: [
                'socialMediaToday',
                'socialMediaExaminer',
                'bufferBlog',
                'hootsuiteBlogs'
            ],
            business: [
                'hubspotMarketing',
                'contentMarketingInstitute',
                'marketingProfs'
            ],
            tech: [
                'techCrunch',
                'theVerge',
                'wired'
            ],
            design: [
                'canvaDesignSchool',
                'adobeBlog',
                'creativeBloq'
            ]
        };
        const categoryArticles = trendingData.articles.filter((article)=>categoryFeeds[category].includes(article.source));
        const keywords = [];
        categoryArticles.forEach((article)=>keywords.push(...article.keywords));
        return this.getTopItems(keywords, 20);
    }
    /**
   * 🚀 ENHANCED: Generate comprehensive hashtag analytics from RSS data
   */ generateHashtagAnalytics(articles, keywords) {
        const hashtagFrequency = new Map();
        const hashtagsByCategory = new Map();
        const hashtagsByLocation = new Map();
        const hashtagsByIndustry = new Map();
        const hashtagSentiment = new Map();
        // Process articles for hashtag analytics
        articles.forEach((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            // Extract hashtags from content
            const hashtags = content.match(/#[a-zA-Z0-9_]+/g) || [];
            // Generate hashtags from keywords
            const keywordHashtags = keywords.filter((keyword)=>content.includes(keyword.toLowerCase())).map((keyword)=>`#${keyword.replace(/\s+/g, '')}`);
            const allHashtags = [
                ...hashtags,
                ...keywordHashtags
            ];
            allHashtags.forEach((hashtag)=>{
                // Count frequency
                hashtagFrequency.set(hashtag, (hashtagFrequency.get(hashtag) || 0) + 1);
                // Categorize by article category
                if (article.category) {
                    if (!hashtagsByCategory.has(article.category)) {
                        hashtagsByCategory.set(article.category, new Set());
                    }
                    hashtagsByCategory.get(article.category).add(hashtag);
                }
                // Categorize by source (as industry proxy)
                if (article.source) {
                    const industry = this.mapSourceToIndustry(article.source);
                    if (!hashtagsByIndustry.has(industry)) {
                        hashtagsByIndustry.set(industry, new Set());
                    }
                    hashtagsByIndustry.get(industry).add(hashtag);
                }
                // Basic sentiment analysis
                if (!hashtagSentiment.has(hashtag)) {
                    hashtagSentiment.set(hashtag, this.analyzeSentiment(content));
                }
            });
        });
        // Calculate trending hashtags with momentum
        const trending = Array.from(hashtagFrequency.entries()).sort(([, a], [, b])=>b - a).slice(0, 20).map(([hashtag, frequency])=>({
                hashtag,
                frequency,
                momentum: this.calculateMomentum(hashtag, articles)
            }));
        // Convert sets to arrays for the final result
        const byCategory = {};
        hashtagsByCategory.forEach((hashtags, category)=>{
            byCategory[category] = Array.from(hashtags).slice(0, 10);
        });
        const byLocation = {};
        // Location analysis would require more sophisticated processing
        // For now, we'll use a simple approach
        byLocation['global'] = trending.slice(0, 10).map((t)=>t.hashtag);
        const byIndustry = {};
        hashtagsByIndustry.forEach((hashtags, industry)=>{
            byIndustry[industry] = Array.from(hashtags).slice(0, 8);
        });
        const sentiment = {};
        hashtagSentiment.forEach((sent, hashtag)=>{
            sentiment[hashtag] = sent;
        });
        return {
            trending,
            byCategory,
            byLocation,
            byIndustry,
            sentiment
        };
    }
    /**
   * Map RSS source to industry category
   */ mapSourceToIndustry(source) {
        const industryMap = {
            'socialMediaToday': 'social_media',
            'socialMediaExaminer': 'social_media',
            'bufferBlog': 'social_media',
            'hootsuiteBlogs': 'social_media',
            'hubspotMarketing': 'marketing',
            'contentMarketingInstitute': 'marketing',
            'marketingProfs': 'marketing',
            'techCrunch': 'technology',
            'theVerge': 'technology',
            'wired': 'technology',
            'canvaDesignSchool': 'design',
            'adobeBlog': 'design',
            'creativeBloq': 'design'
        };
        return industryMap[source] || 'general';
    }
    /**
   * Calculate hashtag momentum based on recent usage
   */ calculateMomentum(hashtag, articles) {
        const now = Date.now();
        const recentArticles = articles.filter((article)=>{
            const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished <= 24;
        });
        const oldArticles = articles.filter((article)=>{
            const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished > 24 && hoursSincePublished <= 72;
        });
        const recentMentions = recentArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        const oldMentions = oldArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        if (recentMentions > oldMentions * 1.5) return 'rising';
        if (recentMentions < oldMentions * 0.5) return 'declining';
        return 'stable';
    }
    /**
   * Basic sentiment analysis for hashtags
   */ analyzeSentiment(content) {
        const positiveWords = [
            'amazing',
            'awesome',
            'great',
            'excellent',
            'fantastic',
            'wonderful',
            'love',
            'best',
            'perfect',
            'incredible',
            'outstanding',
            'brilliant',
            'success',
            'win',
            'achieve',
            'growth',
            'improve',
            'boost'
        ];
        const negativeWords = [
            'bad',
            'terrible',
            'awful',
            'horrible',
            'worst',
            'hate',
            'fail',
            'problem',
            'issue',
            'crisis',
            'decline',
            'drop',
            'loss',
            'damage',
            'risk',
            'threat',
            'concern',
            'worry'
        ];
        const words = content.toLowerCase().split(/\s+/);
        const positiveCount = words.filter((word)=>positiveWords.includes(word)).length;
        const negativeCount = words.filter((word)=>negativeWords.includes(word)).length;
        if (positiveCount > negativeCount) return 'positive';
        if (negativeCount > positiveCount) return 'negative';
        return 'neutral';
    }
}
const rssService = new RSSFeedService();
}}),
"[project]/src/ai/trending-content-enhancer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Content Enhancer
 * Integrates RSS feed data to enhance content generation with trending topics
 */ __turbopack_context__.s({
    "TrendingContentEnhancer": (()=>TrendingContentEnhancer),
    "trendingEnhancer": (()=>trendingEnhancer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-rsc] (ecmascript)");
;
class TrendingContentEnhancer {
    trendingCache = null;
    lastCacheUpdate = 0;
    cacheTimeout = 30 * 60 * 1000;
    /**
   * Get fresh trending data with caching
   */ async getTrendingData() {
        const now = Date.now();
        if (this.trendingCache && now - this.lastCacheUpdate < this.cacheTimeout) {
            return this.trendingCache;
        }
        this.trendingCache = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
        this.lastCacheUpdate = now;
        return this.trendingCache;
    }
    /**
   * Get trending enhancement data for content generation
   */ async getTrendingEnhancement(context = {}) {
        try {
            const trendingData = await this.getTrendingData();
            // Filter and prioritize based on context
            const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);
            const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);
            // Generate hashtags from trending keywords
            const hashtags = this.generateHashtags(relevantKeywords, context);
            // Extract seasonal themes
            const seasonalThemes = this.extractSeasonalThemes(trendingData);
            // Extract industry-specific buzz
            const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);
            return {
                keywords: relevantKeywords.slice(0, 15),
                topics: relevantTopics.slice(0, 10),
                hashtags: hashtags.slice(0, 10),
                seasonalThemes: seasonalThemes.slice(0, 5),
                industryBuzz: industryBuzz.slice(0, 8)
            };
        } catch (error) {
            // Return contextual fallback data based on current context
            return this.generateContextualFallback(context);
        }
    }
    /**
   * Filter keywords based on context relevance
   */ filterKeywordsByContext(keywords, context) {
        const platformKeywords = {
            instagram: [
                'visual',
                'photo',
                'story',
                'reel',
                'aesthetic',
                'lifestyle'
            ],
            facebook: [
                'community',
                'share',
                'connect',
                'family',
                'local',
                'event'
            ],
            twitter: [
                'news',
                'update',
                'breaking',
                'discussion',
                'opinion',
                'thread'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'industry',
                'networking',
                'leadership'
            ],
            tiktok: [
                'viral',
                'trend',
                'challenge',
                'creative',
                'fun',
                'entertainment'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'design',
                'home',
                'style'
            ]
        };
        const businessKeywords = {
            restaurant: [
                'food',
                'dining',
                'menu',
                'chef',
                'cuisine',
                'taste',
                'fresh'
            ],
            retail: [
                'shopping',
                'sale',
                'fashion',
                'style',
                'product',
                'deal',
                'new'
            ],
            fitness: [
                'health',
                'workout',
                'training',
                'wellness',
                'strength',
                'motivation'
            ],
            beauty: [
                'skincare',
                'makeup',
                'beauty',
                'glow',
                'treatment',
                'style'
            ],
            tech: [
                'innovation',
                'digital',
                'technology',
                'software',
                'app',
                'solution'
            ],
            healthcare: [
                'health',
                'wellness',
                'care',
                'treatment',
                'medical',
                'patient'
            ]
        };
        let filtered = [
            ...keywords
        ];
        // Boost platform-relevant keywords
        if (context.platform && platformKeywords[context.platform]) {
            const platformBoost = platformKeywords[context.platform];
            filtered = filtered.sort((a, b)=>{
                const aBoost = platformBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = platformBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        // Boost business-relevant keywords
        if (context.businessType && businessKeywords[context.businessType]) {
            const businessBoost = businessKeywords[context.businessType];
            filtered = filtered.sort((a, b)=>{
                const aBoost = businessBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = businessBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        return filtered;
    }
    /**
   * Filter topics based on context relevance
   */ filterTopicsByContext(topics, context) {
        // Remove topics that are too generic or not suitable for social media
        const filtered = topics.filter((topic)=>{
            const lower = topic.toLowerCase();
            return !lower.includes('error') && !lower.includes('404') && !lower.includes('page not found') && lower.length > 10 && lower.length < 100;
        });
        return filtered;
    }
    /**
   * Generate relevant hashtags from keywords
   */ generateHashtags(keywords, context) {
        const hashtags = [];
        // Convert keywords to hashtags
        keywords.forEach((keyword)=>{
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        });
        // Add platform-specific hashtags
        const platformHashtags = {
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#picoftheday'
            ],
            facebook: [
                '#community',
                '#local',
                '#share',
                '#connect'
            ],
            twitter: [
                '#news',
                '#update',
                '#discussion',
                '#trending'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#career',
                '#networking'
            ],
            tiktok: [
                '#fyp',
                '#viral',
                '#trending',
                '#foryou'
            ],
            pinterest: [
                '#inspiration',
                '#ideas',
                '#diy',
                '#style'
            ]
        };
        if (context.platform && platformHashtags[context.platform]) {
            hashtags.push(...platformHashtags[context.platform]);
        }
        // Remove duplicates and return
        return Array.from(new Set(hashtags));
    }
    /**
   * Extract seasonal themes from trending data
   */ extractSeasonalThemes(trendingData) {
        const currentMonth = new Date().getMonth();
        const seasonalKeywords = {
            0: [
                'new year',
                'resolution',
                'fresh start',
                'winter'
            ],
            1: [
                'valentine',
                'love',
                'romance',
                'winter'
            ],
            2: [
                'spring',
                'march madness',
                'renewal',
                'growth'
            ],
            3: [
                'easter',
                'spring',
                'bloom',
                'fresh'
            ],
            4: [
                'mother\'s day',
                'spring',
                'flowers',
                'celebration'
            ],
            5: [
                'summer',
                'graduation',
                'father\'s day',
                'vacation'
            ],
            6: [
                'summer',
                'july 4th',
                'independence',
                'freedom'
            ],
            7: [
                'summer',
                'vacation',
                'back to school',
                'preparation'
            ],
            8: [
                'back to school',
                'fall',
                'autumn',
                'harvest'
            ],
            9: [
                'halloween',
                'october',
                'spooky',
                'fall'
            ],
            10: [
                'thanksgiving',
                'gratitude',
                'family',
                'harvest'
            ],
            11: [
                'christmas',
                'holiday',
                'winter',
                'celebration'
            ]
        };
        const currentSeasonalKeywords = seasonalKeywords[currentMonth] || [];
        const seasonalThemes = trendingData.keywords.filter((keyword)=>currentSeasonalKeywords.some((seasonal)=>keyword.toLowerCase().includes(seasonal.toLowerCase())));
        return seasonalThemes;
    }
    /**
   * Extract industry-specific buzz from trending data
   */ extractIndustryBuzz(trendingData, businessType) {
        if (!businessType) return [];
        const industryKeywords = {
            restaurant: [
                'food',
                'dining',
                'chef',
                'cuisine',
                'recipe',
                'restaurant',
                'menu'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'product',
                'brand',
                'sale',
                'deal'
            ],
            fitness: [
                'fitness',
                'workout',
                'health',
                'gym',
                'training',
                'wellness',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'cosmetics',
                'treatment',
                'spa'
            ],
            tech: [
                'technology',
                'tech',
                'digital',
                'software',
                'app',
                'innovation',
                'ai'
            ],
            healthcare: [
                'health',
                'medical',
                'healthcare',
                'wellness',
                'treatment',
                'care'
            ]
        };
        const relevantKeywords = industryKeywords[businessType] || [];
        const industryBuzz = trendingData.keywords.filter((keyword)=>relevantKeywords.some((industry)=>keyword.toLowerCase().includes(industry.toLowerCase())));
        return industryBuzz;
    }
    /**
   * Generate contextual fallback data without hardcoded placeholders
   */ generateContextualFallback(context) {
        const today = new Date();
        const currentMonth = today.toLocaleDateString('en-US', {
            month: 'long'
        });
        const currentDay = today.toLocaleDateString('en-US', {
            weekday: 'long'
        });
        // Generate contextual keywords based on business type and current date
        const keywords = [];
        if (context.businessType) {
            keywords.push(`${context.businessType} services`, `${context.businessType} solutions`);
        }
        keywords.push(`${currentDay} motivation`, `${currentMonth} opportunities`);
        // Generate contextual topics
        const topics = [];
        if (context.businessType) {
            topics.push(`${context.businessType} industry insights`);
        }
        if (context.location) {
            topics.push(`${context.location} business community`);
        }
        topics.push(`${currentMonth} business trends`);
        // Generate contextual hashtags
        const hashtags = [];
        if (context.businessType) {
            hashtags.push(`#${context.businessType.replace(/\s+/g, '')}Business`);
        }
        if (context.location) {
            const locationParts = context.location.split(',').map((part)=>part.trim());
            locationParts.forEach((part)=>{
                if (part.length > 2) {
                    hashtags.push(`#${part.replace(/\s+/g, '')}`);
                }
            });
        }
        hashtags.push(`#${currentDay}Motivation`, `#${currentMonth}${today.getFullYear()}`);
        // Generate seasonal themes
        const seasonalThemes = [];
        const month = today.getMonth();
        if (month >= 2 && month <= 4) seasonalThemes.push('Spring renewal', 'Fresh starts');
        else if (month >= 5 && month <= 7) seasonalThemes.push('Summer energy', 'Outdoor activities');
        else if (month >= 8 && month <= 10) seasonalThemes.push('Autumn preparation', 'Harvest season');
        else seasonalThemes.push('Winter planning', 'Year-end reflection');
        return {
            keywords: keywords.slice(0, 15),
            topics: topics.slice(0, 10),
            hashtags: hashtags.slice(0, 10),
            seasonalThemes: seasonalThemes.slice(0, 5),
            industryBuzz: context.businessType ? [
                `${context.businessType} innovation`
            ] : []
        };
    }
    /**
   * Get trending prompt enhancement for AI content generation
   */ async getTrendingPromptEnhancement(context = {}) {
        const enhancement = await this.getTrendingEnhancement(context);
        const promptParts = [];
        if (enhancement.keywords.length > 0) {
            promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);
        }
        if (enhancement.seasonalThemes.length > 0) {
            promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);
        }
        if (enhancement.industryBuzz.length > 0) {
            promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);
        }
        if (enhancement.hashtags.length > 0) {
            promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);
        }
        return promptParts.join('\n');
    }
}
const trendingEnhancer = new TrendingContentEnhancer();
}}),
"[project]/src/ai/advanced-trending-hashtag-analyzer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Trending Hashtag Analyzer
 * Analyzes RSS feeds and trending data to extract the most relevant hashtags
 * with sophisticated contextual understanding and business relevance scoring
 */ __turbopack_context__.s({
    "AdvancedTrendingHashtagAnalyzer": (()=>AdvancedTrendingHashtagAnalyzer),
    "advancedHashtagAnalyzer": (()=>advancedHashtagAnalyzer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-rsc] (ecmascript)");
;
;
class AdvancedTrendingHashtagAnalyzer {
    cache = new Map();
    cacheTimeout = 15 * 60 * 1000;
    /**
   * Analyze trending data and generate advanced hashtag strategy
   */ async analyzeHashtagTrends(context) {
        const cacheKey = this.generateCacheKey(context);
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        try {
            // Get comprehensive trending data
            const [trendingData, enhancementData] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rssService"].getTrendingData(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                    businessType: context.businessType,
                    location: context.location,
                    platform: context.platform,
                    targetAudience: context.targetAudience
                })
            ]);
            // Extract and analyze hashtags from multiple sources
            const hashtagAnalyses = await this.extractAndAnalyzeHashtags(trendingData, enhancementData, context);
            // Categorize hashtags by type and relevance
            const strategy = this.categorizeHashtags(hashtagAnalyses, context);
            // Cache the results
            this.cache.set(cacheKey, {
                data: strategy,
                timestamp: Date.now()
            });
            return strategy;
        } catch (error) {
            return this.getFallbackStrategy(context);
        }
    }
    /**
   * Extract hashtags from RSS articles and trending data
   */ async extractAndAnalyzeHashtags(trendingData, enhancementData, context) {
        const hashtagMap = new Map();
        // Process RSS articles for hashtag extraction
        for (const article of trendingData.articles){
            const extractedHashtags = this.extractHashtagsFromArticle(article, context);
            for (const hashtag of extractedHashtags){
                if (hashtagMap.has(hashtag)) {
                    const existing = hashtagMap.get(hashtag);
                    existing.sources.push(article.source);
                    existing.trendingScore += 1;
                } else {
                    hashtagMap.set(hashtag, {
                        hashtag,
                        relevanceScore: this.calculateRelevanceScore(hashtag, context),
                        trendingScore: 1,
                        businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                        platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                        locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                        engagementPotential: this.calculateEngagementPotential(hashtag),
                        sources: [
                            article.source
                        ],
                        momentum: this.calculateMomentum(hashtag, trendingData),
                        category: this.categorizeHashtag(hashtag, context)
                    });
                }
            }
        }
        // Add hashtags from trending enhancement data
        for (const hashtag of enhancementData.hashtags){
            if (hashtagMap.has(hashtag)) {
                const existing = hashtagMap.get(hashtag);
                existing.trendingScore += 2; // Enhancement data gets higher weight
                existing.sources.push('trending_enhancer');
            } else {
                hashtagMap.set(hashtag, {
                    hashtag,
                    relevanceScore: this.calculateRelevanceScore(hashtag, context),
                    trendingScore: 2,
                    businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                    platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                    locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                    engagementPotential: this.calculateEngagementPotential(hashtag),
                    sources: [
                        'trending_enhancer'
                    ],
                    momentum: 'rising',
                    category: this.categorizeHashtag(hashtag, context)
                });
            }
        }
        // Add business-specific trending hashtags
        const businessHashtags = this.generateBusinessTrendingHashtags(context);
        for (const hashtag of businessHashtags){
            if (!hashtagMap.has(hashtag)) {
                hashtagMap.set(hashtag, {
                    hashtag,
                    relevanceScore: this.calculateRelevanceScore(hashtag, context),
                    trendingScore: 1,
                    businessRelevance: 10,
                    platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                    locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                    engagementPotential: this.calculateEngagementPotential(hashtag),
                    sources: [
                        'business_generator'
                    ],
                    momentum: 'stable',
                    category: 'business'
                });
            }
        }
        return Array.from(hashtagMap.values());
    }
    /**
   * Extract hashtags from article content
   */ extractHashtagsFromArticle(article, context) {
        const hashtags = [];
        const content = `${article.title} ${article.description}`.toLowerCase();
        // Extract existing hashtags
        const hashtagMatches = content.match(/#[a-zA-Z0-9_]+/g) || [];
        hashtags.push(...hashtagMatches);
        // Generate hashtags from keywords
        const keywords = article.keywords || [];
        for (const keyword of keywords){
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        }
        // Generate contextual hashtags based on content relevance
        const contextualHashtags = this.generateContextualHashtags(content, context);
        hashtags.push(...contextualHashtags);
        return Array.from(new Set(hashtags));
    }
    /**
   * Generate contextual hashtags based on content analysis
   */ generateContextualHashtags(content, context) {
        const hashtags = [];
        // Business type relevance
        if (content.includes(context.businessType.toLowerCase())) {
            hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
        }
        // Location relevance
        if (content.includes(context.location.toLowerCase())) {
            hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
        }
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        for (const keyword of industryKeywords){
            if (content.includes(keyword.toLowerCase())) {
                hashtags.push(`#${keyword.replace(/\s+/g, '')}`);
            }
        }
        return hashtags;
    }
    /**
   * Calculate relevance score for a hashtag
   */ calculateRelevanceScore(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase();
        // Business type relevance
        if (hashtagLower.includes(context.businessType.toLowerCase())) score += 5;
        // Location relevance
        if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) score += 4;
        // Service relevance
        if (context.services) {
            const services = context.services.toLowerCase().split(/[,\s]+/);
            for (const service of services){
                if (hashtagLower.includes(service)) score += 3;
            }
        }
        // Platform optimization
        score += this.calculatePlatformOptimization(hashtag, context.platform);
        return Math.min(score, 10); // Cap at 10
    }
    /**
   * Calculate business relevance score
   */ calculateBusinessRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase();
        // Direct business name match
        if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\s+/g, ''))) score += 10;
        // Business type match
        if (hashtagLower.includes(context.businessType.toLowerCase())) score += 8;
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        for (const keyword of industryKeywords){
            if (hashtagLower.includes(keyword.toLowerCase())) score += 6;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate platform optimization score
   */ calculatePlatformOptimization(hashtag, platform) {
        const platformHashtags = {
            instagram: [
                'instagood',
                'photooftheday',
                'instadaily',
                'reels',
                'igers'
            ],
            facebook: [
                'community',
                'local',
                'share',
                'connect',
                'family'
            ],
            twitter: [
                'news',
                'update',
                'discussion',
                'trending',
                'breaking'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'networking',
                'industry'
            ],
            tiktok: [
                'fyp',
                'viral',
                'trending',
                'foryou',
                'dance'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'style',
                'design'
            ]
        };
        const platformSpecific = platformHashtags[platform.toLowerCase()] || [];
        const hashtagLower = hashtag.toLowerCase();
        for (const specific of platformSpecific){
            if (hashtagLower.includes(specific)) return 8;
        }
        return 2; // Base score for any hashtag
    }
    /**
   * Calculate location relevance score
   */ calculateLocationRelevance(hashtag, location) {
        const hashtagLower = hashtag.toLowerCase();
        const locationLower = location.toLowerCase();
        if (hashtagLower.includes(locationLower.replace(/\s+/g, ''))) return 10;
        if (hashtagLower.includes('local') || hashtagLower.includes('community')) return 6;
        // Check for city/state/country keywords
        const locationParts = location.split(/[,\s]+/);
        for (const part of locationParts){
            if (part.length > 2 && hashtagLower.includes(part.toLowerCase())) return 8;
        }
        return 1;
    }
    /**
   * Calculate engagement potential score
   */ calculateEngagementPotential(hashtag) {
        const highEngagementKeywords = [
            'viral',
            'trending',
            'amazing',
            'incredible',
            'awesome',
            'beautiful',
            'love',
            'best',
            'new',
            'hot',
            'popular',
            'top',
            'must',
            'perfect'
        ];
        const hashtagLower = hashtag.toLowerCase();
        for (const keyword of highEngagementKeywords){
            if (hashtagLower.includes(keyword)) return 9;
        }
        // Length-based scoring (shorter hashtags often perform better)
        if (hashtag.length <= 10) return 7;
        if (hashtag.length <= 15) return 5;
        return 3;
    }
    /**
   * Calculate momentum for hashtag trends
   */ calculateMomentum(hashtag, trendingData) {
        // Simple momentum calculation based on recency and frequency
        const recentArticles = trendingData.articles.filter((article)=>{
            const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished <= 24;
        });
        const hashtagMentions = recentArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        if (hashtagMentions >= 3) return 'rising';
        if (hashtagMentions >= 1) return 'stable';
        return 'declining';
    }
    /**
   * Categorize hashtag by type
   */ categorizeHashtag(hashtag, context) {
        const hashtagLower = hashtag.toLowerCase();
        if (hashtagLower.includes('viral') || hashtagLower.includes('trending')) return 'viral';
        if (hashtagLower.includes(context.businessType.toLowerCase())) return 'business';
        if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) return 'location';
        if (hashtagLower.includes('season') || hashtagLower.includes('holiday')) return 'seasonal';
        if (this.isNicheHashtag(hashtag, context)) return 'niche';
        return 'trending';
    }
    /**
   * Check if hashtag is niche-specific
   */ isNicheHashtag(hashtag, context) {
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const hashtagLower = hashtag.toLowerCase();
        return industryKeywords.some((keyword)=>hashtagLower.includes(keyword.toLowerCase()));
    }
    /**
   * Get industry-specific keywords
   */ getIndustryKeywords(businessType) {
        const industryMap = {
            restaurant: [
                'food',
                'dining',
                'cuisine',
                'chef',
                'menu',
                'delicious',
                'taste'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'sale',
                'deals',
                'boutique'
            ],
            healthcare: [
                'health',
                'wellness',
                'medical',
                'care',
                'treatment',
                'doctor'
            ],
            fitness: [
                'workout',
                'gym',
                'fitness',
                'health',
                'training',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'salon',
                'spa',
                'treatment'
            ],
            technology: [
                'tech',
                'digital',
                'innovation',
                'software',
                'app',
                'online'
            ],
            education: [
                'learning',
                'education',
                'training',
                'course',
                'skill',
                'knowledge'
            ],
            automotive: [
                'car',
                'auto',
                'vehicle',
                'repair',
                'service',
                'maintenance'
            ],
            realestate: [
                'property',
                'home',
                'house',
                'real estate',
                'investment'
            ],
            legal: [
                'law',
                'legal',
                'attorney',
                'lawyer',
                'justice',
                'rights'
            ]
        };
        return industryMap[businessType.toLowerCase()] || [
            'business',
            'service',
            'professional'
        ];
    }
    /**
   * Generate business-specific trending hashtags
   */ generateBusinessTrendingHashtags(context) {
        const hashtags = [];
        // Business name hashtag
        hashtags.push(`#${context.businessName.replace(/[^a-zA-Z0-9]/g, '')}`);
        // Business type hashtag
        hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
        // Location hashtag
        hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
        // Industry-specific hashtags
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        hashtags.push(...industryKeywords.slice(0, 3).map((keyword)=>`#${keyword.replace(/\s+/g, '')}`));
        return hashtags;
    }
    /**
   * Categorize hashtags into strategy groups
   */ categorizeHashtags(analyses, context) {
        // Sort by overall relevance score
        const sortedAnalyses = analyses.sort((a, b)=>{
            const scoreA = (a.relevanceScore + a.trendingScore + a.businessRelevance + a.engagementPotential) / 4;
            const scoreB = (b.relevanceScore + b.trendingScore + b.businessRelevance + b.engagementPotential) / 4;
            return scoreB - scoreA;
        });
        const topTrending = sortedAnalyses.filter((a)=>a.category === 'trending' || a.category === 'viral').slice(0, 8);
        const businessOptimized = sortedAnalyses.filter((a)=>a.businessRelevance >= 6).slice(0, 6);
        const locationSpecific = sortedAnalyses.filter((a)=>a.locationRelevance >= 6).slice(0, 4);
        const platformNative = sortedAnalyses.filter((a)=>a.platformOptimization >= 6).slice(0, 5);
        const emergingTrends = sortedAnalyses.filter((a)=>a.momentum === 'rising').slice(0, 6);
        // Create final recommendations (top 15 hashtags)
        const finalRecommendations = this.createFinalRecommendations(topTrending, businessOptimized, locationSpecific, platformNative, emergingTrends);
        return {
            topTrending,
            businessOptimized,
            locationSpecific,
            platformNative,
            emergingTrends,
            finalRecommendations
        };
    }
    /**
   * Create final hashtag recommendations
   */ createFinalRecommendations(topTrending, businessOptimized, locationSpecific, platformNative, emergingTrends) {
        const recommendations = new Set();
        // Add top performers from each category
        topTrending.slice(0, 4).forEach((h)=>recommendations.add(h.hashtag));
        businessOptimized.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        locationSpecific.slice(0, 2).forEach((h)=>recommendations.add(h.hashtag));
        platformNative.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        emergingTrends.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        return Array.from(recommendations).slice(0, 15);
    }
    /**
   * Generate cache key for analysis context
   */ generateCacheKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}-${context.businessName}`.toLowerCase();
    }
    /**
   * Get fallback strategy when analysis fails
   */ getFallbackStrategy(context) {
        const fallbackHashtags = [
            '#trending',
            '#viral',
            '#business',
            '#local',
            '#community',
            `#${context.businessType.replace(/\s+/g, '')}`,
            `#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`,
            '#quality',
            '#professional',
            '#service'
        ];
        const fallbackAnalyses = fallbackHashtags.map((hashtag)=>({
                hashtag,
                relevanceScore: 5,
                trendingScore: 3,
                businessRelevance: 5,
                platformOptimization: 4,
                locationRelevance: 3,
                engagementPotential: 5,
                sources: [
                    'fallback'
                ],
                momentum: 'stable',
                category: 'trending'
            }));
        return {
            topTrending: fallbackAnalyses.slice(0, 4),
            businessOptimized: fallbackAnalyses.slice(0, 3),
            locationSpecific: fallbackAnalyses.slice(0, 2),
            platformNative: fallbackAnalyses.slice(0, 3),
            emergingTrends: fallbackAnalyses.slice(0, 3),
            finalRecommendations: fallbackHashtags
        };
    }
}
const advancedHashtagAnalyzer = new AdvancedTrendingHashtagAnalyzer();
}}),
"[project]/src/ai/realtime-hashtag-scorer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Real-time Hashtag Relevance Scoring System
 * Advanced scoring algorithm that evaluates hashtag relevance based on
 * RSS trends, business context, location, platform, and engagement potential
 */ __turbopack_context__.s({
    "RealtimeHashtagScorer": (()=>RealtimeHashtagScorer),
    "realtimeHashtagScorer": (()=>realtimeHashtagScorer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-rsc] (ecmascript)");
;
class RealtimeHashtagScorer {
    scoreCache = new Map();
    cacheTimeout = 10 * 60 * 1000;
    /**
   * Score a single hashtag with comprehensive analysis
   */ async scoreHashtag(hashtag, context) {
        const cacheKey = `${hashtag}-${this.generateContextKey(context)}`;
        const cached = this.scoreCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.score;
        }
        try {
            // Get trending data for analysis
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
            // Calculate individual score components
            const breakdown = {
                trendingScore: await this.calculateTrendingScore(hashtag, trendingData),
                businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                locationRelevance: this.calculateLocationRelevance(hashtag, context),
                platformOptimization: this.calculatePlatformOptimization(hashtag, context),
                engagementPotential: this.calculateEngagementPotential(hashtag, context),
                temporalRelevance: this.calculateTemporalRelevance(hashtag, context),
                competitorAnalysis: await this.calculateCompetitorAnalysis(hashtag, context, trendingData),
                semanticRelevance: this.calculateSemanticRelevance(hashtag, context)
            };
            // Calculate weighted total score
            const totalScore = this.calculateWeightedScore(breakdown, context);
            // Determine confidence level
            const confidence = this.calculateConfidence(breakdown, trendingData);
            // Generate recommendation
            const recommendation = this.generateRecommendation(totalScore, confidence);
            // Generate reasoning
            const reasoning = this.generateReasoning(breakdown, context);
            const score = {
                hashtag,
                totalScore,
                breakdown,
                confidence,
                recommendation,
                reasoning
            };
            // Cache the result
            this.scoreCache.set(cacheKey, {
                score,
                timestamp: Date.now()
            });
            return score;
        } catch (error) {
            return this.getFallbackScore(hashtag, context);
        }
    }
    /**
   * Score multiple hashtags and return sorted by relevance
   */ async scoreHashtags(hashtags, context) {
        const scores = await Promise.all(hashtags.map((hashtag)=>this.scoreHashtag(hashtag, context)));
        return scores.sort((a, b)=>b.totalScore - a.totalScore);
    }
    /**
   * Calculate trending score based on RSS data
   */ async calculateTrendingScore(hashtag, trendingData) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Check direct mentions in RSS articles
        const mentionCount = trendingData.articles.filter((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            return content.includes(hashtagLower);
        }).length;
        // Score based on mention frequency
        if (mentionCount >= 5) score += 10;
        else if (mentionCount >= 3) score += 8;
        else if (mentionCount >= 1) score += 6;
        else score += 2;
        // Check keyword relevance in trending topics
        const keywordRelevance = trendingData.keywords.filter((keyword)=>keyword.toLowerCase().includes(hashtagLower) || hashtagLower.includes(keyword.toLowerCase())).length;
        score += Math.min(keywordRelevance * 2, 4);
        // Recency bonus (newer articles get higher weight)
        const recentMentions = trendingData.articles.filter((article)=>{
            const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);
            const content = `${article.title} ${article.description}`.toLowerCase();
            return hoursSincePublished <= 6 && content.includes(hashtagLower);
        }).length;
        if (recentMentions > 0) score += 2;
        return Math.min(score, 10);
    }
    /**
   * Calculate business relevance score
   */ calculateBusinessRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Direct business name match
        if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\s+/g, ''))) {
            score += 10;
        }
        // Business type relevance
        if (hashtagLower.includes(context.businessType.toLowerCase())) {
            score += 8;
        }
        // Services/expertise relevance
        if (context.services) {
            const services = context.services.toLowerCase().split(/[,\s]+/);
            const serviceMatches = services.filter((service)=>hashtagLower.includes(service) || service.includes(hashtagLower)).length;
            score += Math.min(serviceMatches * 3, 6);
        }
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const industryMatches = industryKeywords.filter((keyword)=>hashtagLower.includes(keyword.toLowerCase())).length;
        score += Math.min(industryMatches * 2, 4);
        return Math.min(score, 10);
    }
    /**
   * Calculate location relevance score
   */ calculateLocationRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const locationLower = context.location.toLowerCase();
        // Direct location match
        if (hashtagLower.includes(locationLower.replace(/\s+/g, ''))) {
            score += 10;
        }
        // Location parts (city, state, country)
        const locationParts = context.location.split(/[,\s]+/).filter((part)=>part.length > 2);
        const locationMatches = locationParts.filter((part)=>hashtagLower.includes(part.toLowerCase())).length;
        score += Math.min(locationMatches * 4, 8);
        // Local/community keywords
        const localKeywords = [
            'local',
            'community',
            'neighborhood',
            'area',
            'town',
            'city'
        ];
        if (localKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 6;
        }
        // Regional keywords
        const regionalKeywords = [
            'regional',
            'metro',
            'downtown',
            'uptown',
            'district'
        ];
        if (regionalKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 4;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate platform optimization score
   */ calculatePlatformOptimization(hashtag, context) {
        const platformHashtags = {
            instagram: {
                high: [
                    'instagood',
                    'photooftheday',
                    'instadaily',
                    'reels',
                    'igers',
                    'instamood'
                ],
                medium: [
                    'picoftheday',
                    'instapic',
                    'instalike',
                    'followme',
                    'instagramhub'
                ]
            },
            facebook: {
                high: [
                    'community',
                    'local',
                    'share',
                    'connect',
                    'family',
                    'friends'
                ],
                medium: [
                    'like',
                    'follow',
                    'page',
                    'group',
                    'event'
                ]
            },
            twitter: {
                high: [
                    'news',
                    'update',
                    'discussion',
                    'trending',
                    'breaking',
                    'thread'
                ],
                medium: [
                    'tweet',
                    'retweet',
                    'follow',
                    'hashtag',
                    'viral'
                ]
            },
            linkedin: {
                high: [
                    'professional',
                    'business',
                    'career',
                    'networking',
                    'industry',
                    'leadership'
                ],
                medium: [
                    'job',
                    'work',
                    'corporate',
                    'company',
                    'team'
                ]
            },
            tiktok: {
                high: [
                    'fyp',
                    'viral',
                    'trending',
                    'foryou',
                    'dance',
                    'challenge'
                ],
                medium: [
                    'tiktok',
                    'video',
                    'funny',
                    'entertainment',
                    'music'
                ]
            },
            pinterest: {
                high: [
                    'inspiration',
                    'ideas',
                    'diy',
                    'style',
                    'design',
                    'home'
                ],
                medium: [
                    'pinterest',
                    'pin',
                    'board',
                    'creative',
                    'art'
                ]
            }
        };
        const platform = context.platform.toLowerCase();
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const platformData = platformHashtags[platform];
        if (!platformData) return 5; // Default score for unknown platforms
        // Check high-value platform hashtags
        if (platformData.high.some((tag)=>hashtagLower.includes(tag))) {
            return 10;
        }
        // Check medium-value platform hashtags
        if (platformData.medium.some((tag)=>hashtagLower.includes(tag))) {
            return 7;
        }
        // Platform-specific length optimization
        const optimalLengths = {
            instagram: {
                min: 5,
                max: 20
            },
            twitter: {
                min: 3,
                max: 15
            },
            tiktok: {
                min: 3,
                max: 12
            },
            linkedin: {
                min: 8,
                max: 25
            },
            facebook: {
                min: 5,
                max: 18
            },
            pinterest: {
                min: 6,
                max: 22
            }
        };
        const lengthData = optimalLengths[platform];
        if (lengthData && hashtag.length >= lengthData.min && hashtag.length <= lengthData.max) {
            return 6;
        }
        return 3; // Base score
    }
    /**
   * Calculate engagement potential score
   */ calculateEngagementPotential(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // High-engagement keywords
        const highEngagementKeywords = [
            'viral',
            'trending',
            'amazing',
            'incredible',
            'awesome',
            'beautiful',
            'love',
            'best',
            'new',
            'hot',
            'popular',
            'top',
            'must',
            'perfect',
            'exclusive',
            'limited',
            'special',
            'unique',
            'rare'
        ];
        if (highEngagementKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 9;
        }
        // Emotional keywords
        const emotionalKeywords = [
            'happy',
            'excited',
            'proud',
            'grateful',
            'blessed',
            'inspired',
            'motivated',
            'passionate',
            'thrilled',
            'delighted'
        ];
        if (emotionalKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 7;
        }
        // Action keywords
        const actionKeywords = [
            'discover',
            'explore',
            'experience',
            'try',
            'learn',
            'create',
            'build',
            'grow',
            'achieve',
            'succeed'
        ];
        if (actionKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 6;
        }
        // Length-based scoring (optimal hashtag lengths)
        if (hashtag.length >= 6 && hashtag.length <= 15) {
            score += 5;
        } else if (hashtag.length >= 4 && hashtag.length <= 20) {
            score += 3;
        } else {
            score += 1;
        }
        // Avoid overly generic hashtags
        const genericHashtags = [
            'good',
            'nice',
            'cool',
            'great',
            'ok',
            'fine'
        ];
        if (genericHashtags.some((generic)=>hashtagLower === generic)) {
            score -= 3;
        }
        return Math.min(Math.max(score, 0), 10);
    }
    /**
   * Calculate temporal relevance score
   */ calculateTemporalRelevance(hashtag, context) {
        let score = 5; // Base score
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const now = new Date();
        const currentHour = context.timeOfDay || now.getHours();
        const currentDay = context.dayOfWeek || now.getDay();
        // Time-of-day relevance
        const timeKeywords = {
            morning: [
                'morning',
                'breakfast',
                'coffee',
                'start',
                'fresh'
            ],
            afternoon: [
                'lunch',
                'afternoon',
                'work',
                'business',
                'professional'
            ],
            evening: [
                'dinner',
                'evening',
                'relax',
                'unwind',
                'family'
            ],
            night: [
                'night',
                'late',
                'weekend',
                'party',
                'fun'
            ]
        };
        let timeCategory = 'morning';
        if (currentHour >= 12 && currentHour < 17) timeCategory = 'afternoon';
        else if (currentHour >= 17 && currentHour < 21) timeCategory = 'evening';
        else if (currentHour >= 21 || currentHour < 6) timeCategory = 'night';
        if (timeKeywords[timeCategory].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 3;
        }
        // Day-of-week relevance
        const dayKeywords = {
            weekday: [
                'work',
                'business',
                'professional',
                'office',
                'meeting'
            ],
            weekend: [
                'weekend',
                'fun',
                'relax',
                'family',
                'leisure',
                'party'
            ]
        };
        const isWeekend = currentDay === 0 || currentDay === 6;
        const dayCategory = isWeekend ? 'weekend' : 'weekday';
        if (dayKeywords[dayCategory].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 2;
        }
        // Seasonal relevance (basic implementation)
        const month = now.getMonth();
        const seasonalKeywords = {
            spring: [
                'spring',
                'fresh',
                'new',
                'bloom',
                'growth'
            ],
            summer: [
                'summer',
                'hot',
                'vacation',
                'beach',
                'outdoor'
            ],
            fall: [
                'fall',
                'autumn',
                'harvest',
                'cozy',
                'warm'
            ],
            winter: [
                'winter',
                'cold',
                'holiday',
                'celebration',
                'indoor'
            ]
        };
        let season = 'spring';
        if (month >= 5 && month <= 7) season = 'summer';
        else if (month >= 8 && month <= 10) season = 'fall';
        else if (month >= 11 || month <= 1) season = 'winter';
        if (seasonalKeywords[season].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 2;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate competitor analysis score
   */ async calculateCompetitorAnalysis(hashtag, context, trendingData) {
        let score = 5; // Base score
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Analyze if competitors in the same industry are using this hashtag
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const competitorMentions = trendingData.articles.filter((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            return industryKeywords.some((keyword)=>content.includes(keyword.toLowerCase())) && content.includes(hashtagLower);
        }).length;
        // Score based on competitor usage
        if (competitorMentions >= 3) {
            score += 4; // High competitor usage indicates relevance
        } else if (competitorMentions >= 1) {
            score += 2; // Some competitor usage
        }
        // Check for oversaturation (too many competitors using the same hashtag)
        if (competitorMentions >= 10) {
            score -= 2; // Penalty for oversaturated hashtags
        }
        return Math.min(Math.max(score, 0), 10);
    }
    /**
   * Calculate semantic relevance score
   */ calculateSemanticRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Content relevance (if post content is provided)
        if (context.postContent) {
            const contentLower = context.postContent.toLowerCase();
            const contentWords = contentLower.split(/\s+/);
            // Direct word match
            if (contentWords.some((word)=>word.includes(hashtagLower) || hashtagLower.includes(word))) {
                score += 8;
            }
            // Semantic similarity (basic implementation)
            const semanticKeywords = this.extractSemanticKeywords(context.postContent);
            if (semanticKeywords.some((keyword)=>hashtagLower.includes(keyword) || keyword.includes(hashtagLower))) {
                score += 6;
            }
        }
        // Target audience relevance
        if (context.targetAudience) {
            const audienceKeywords = context.targetAudience.toLowerCase().split(/[,\s]+/);
            if (audienceKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
                score += 5;
            }
        }
        // Industry semantic relevance
        const industrySemantics = this.getIndustrySemantics(context.businessType);
        if (industrySemantics.some((semantic)=>hashtagLower.includes(semantic.toLowerCase()))) {
            score += 4;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate weighted total score
   */ calculateWeightedScore(breakdown, context) {
        // Weights can be adjusted based on business priorities
        const weights = {
            trendingScore: 0.25,
            businessRelevance: 0.20,
            engagementPotential: 0.15,
            platformOptimization: 0.12,
            locationRelevance: 0.10,
            semanticRelevance: 0.08,
            temporalRelevance: 0.06,
            competitorAnalysis: 0.04 // Competitive intelligence
        };
        let totalScore = 0;
        totalScore += breakdown.trendingScore * weights.trendingScore;
        totalScore += breakdown.businessRelevance * weights.businessRelevance;
        totalScore += breakdown.engagementPotential * weights.engagementPotential;
        totalScore += breakdown.platformOptimization * weights.platformOptimization;
        totalScore += breakdown.locationRelevance * weights.locationRelevance;
        totalScore += breakdown.semanticRelevance * weights.semanticRelevance;
        totalScore += breakdown.temporalRelevance * weights.temporalRelevance;
        totalScore += breakdown.competitorAnalysis * weights.competitorAnalysis;
        return Math.round(totalScore * 10) / 10; // Round to 1 decimal place
    }
    /**
   * Calculate confidence in the score
   */ calculateConfidence(breakdown, trendingData) {
        let confidence = 0;
        let factors = 0;
        // RSS data quality factor
        if (trendingData.articles.length >= 10) {
            confidence += 0.3;
            factors++;
        } else if (trendingData.articles.length >= 5) {
            confidence += 0.2;
            factors++;
        }
        // Score consistency factor
        const scores = Object.values(breakdown);
        const avgScore = scores.reduce((sum, score)=>sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score)=>sum + Math.pow(score - avgScore, 2), 0) / scores.length;
        if (variance < 4) {
            confidence += 0.3;
            factors++;
        } else if (variance < 9) {
            confidence += 0.2;
            factors++;
        }
        // High-scoring factors
        const highScores = scores.filter((score)=>score >= 7).length;
        if (highScores >= 4) {
            confidence += 0.4;
            factors++;
        } else if (highScores >= 2) {
            confidence += 0.3;
            factors++;
        }
        return factors > 0 ? Math.min(confidence, 1) : 0.5;
    }
    /**
   * Generate recommendation based on score and confidence
   */ generateRecommendation(totalScore, confidence) {
        if (totalScore >= 8 && confidence >= 0.7) return 'high';
        if (totalScore >= 6 && confidence >= 0.5) return 'medium';
        if (totalScore >= 4) return 'low';
        return 'avoid';
    }
    /**
   * Generate reasoning for the score
   */ generateReasoning(breakdown, context) {
        const reasoning = [];
        if (breakdown.trendingScore >= 8) {
            reasoning.push('Highly trending in RSS feeds and news sources');
        } else if (breakdown.trendingScore >= 6) {
            reasoning.push('Moderately trending in current news cycle');
        }
        if (breakdown.businessRelevance >= 8) {
            reasoning.push('Highly relevant to your business type and services');
        } else if (breakdown.businessRelevance >= 6) {
            reasoning.push('Good business relevance for your industry');
        }
        if (breakdown.engagementPotential >= 8) {
            reasoning.push('High potential for user engagement and interaction');
        }
        if (breakdown.platformOptimization >= 8) {
            reasoning.push(`Optimized for ${context.platform} platform algorithms`);
        }
        if (breakdown.locationRelevance >= 8) {
            reasoning.push('Strong local/geographic relevance');
        }
        if (breakdown.competitorAnalysis >= 7) {
            reasoning.push('Successfully used by industry competitors');
        }
        if (reasoning.length === 0) {
            reasoning.push('Basic hashtag with standard performance potential');
        }
        return reasoning;
    }
    /**
   * Get industry-specific keywords
   */ getIndustryKeywords(businessType) {
        const industryMap = {
            restaurant: [
                'food',
                'dining',
                'cuisine',
                'chef',
                'menu',
                'delicious',
                'taste',
                'recipe'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'sale',
                'deals',
                'boutique',
                'store',
                'brand'
            ],
            healthcare: [
                'health',
                'wellness',
                'medical',
                'care',
                'treatment',
                'doctor',
                'patient'
            ],
            fitness: [
                'workout',
                'gym',
                'fitness',
                'health',
                'training',
                'exercise',
                'strength'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'salon',
                'spa',
                'treatment',
                'cosmetics'
            ],
            technology: [
                'tech',
                'digital',
                'innovation',
                'software',
                'app',
                'online',
                'data'
            ],
            education: [
                'learning',
                'education',
                'training',
                'course',
                'skill',
                'knowledge',
                'teach'
            ],
            automotive: [
                'car',
                'auto',
                'vehicle',
                'repair',
                'service',
                'maintenance',
                'drive'
            ],
            realestate: [
                'property',
                'home',
                'house',
                'real estate',
                'investment',
                'buy',
                'sell'
            ],
            legal: [
                'law',
                'legal',
                'attorney',
                'lawyer',
                'justice',
                'rights',
                'court'
            ]
        };
        return industryMap[businessType.toLowerCase()] || [
            'business',
            'service',
            'professional',
            'quality'
        ];
    }
    /**
   * Get industry semantic keywords
   */ getIndustrySemantics(businessType) {
        const semanticMap = {
            restaurant: [
                'culinary',
                'gastronomy',
                'hospitality',
                'ambiance',
                'flavor'
            ],
            retail: [
                'merchandise',
                'consumer',
                'lifestyle',
                'trend',
                'collection'
            ],
            healthcare: [
                'therapeutic',
                'diagnosis',
                'prevention',
                'recovery',
                'healing'
            ],
            fitness: [
                'performance',
                'endurance',
                'transformation',
                'motivation',
                'results'
            ],
            beauty: [
                'aesthetic',
                'enhancement',
                'rejuvenation',
                'glamour',
                'confidence'
            ],
            technology: [
                'automation',
                'efficiency',
                'connectivity',
                'intelligence',
                'solution'
            ],
            education: [
                'development',
                'growth',
                'achievement',
                'mastery',
                'expertise'
            ],
            automotive: [
                'performance',
                'reliability',
                'maintenance',
                'transportation',
                'mobility'
            ],
            realestate: [
                'investment',
                'location',
                'value',
                'opportunity',
                'lifestyle'
            ],
            legal: [
                'advocacy',
                'representation',
                'protection',
                'resolution',
                'compliance'
            ]
        };
        return semanticMap[businessType.toLowerCase()] || [
            'excellence',
            'quality',
            'service',
            'professional'
        ];
    }
    /**
   * Extract semantic keywords from content
   */ extractSemanticKeywords(content) {
        // Simple keyword extraction (can be enhanced with NLP)
        const words = content.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((word)=>word.length > 3);
        // Remove common stop words
        const stopWords = [
            'this',
            'that',
            'with',
            'have',
            'will',
            'from',
            'they',
            'been',
            'were',
            'said'
        ];
        return words.filter((word)=>!stopWords.includes(word));
    }
    /**
   * Generate context key for caching
   */ generateContextKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}`.toLowerCase();
    }
    /**
   * Get fallback score when analysis fails
   */ getFallbackScore(hashtag, context) {
        return {
            hashtag,
            totalScore: 5.0,
            breakdown: {
                trendingScore: 5,
                businessRelevance: 5,
                locationRelevance: 5,
                platformOptimization: 5,
                engagementPotential: 5,
                temporalRelevance: 5,
                competitorAnalysis: 5,
                semanticRelevance: 5
            },
            confidence: 0.3,
            recommendation: 'medium',
            reasoning: [
                'Fallback scoring due to analysis error'
            ]
        };
    }
}
const realtimeHashtagScorer = new RealtimeHashtagScorer();
}}),
"[project]/src/ai/intelligent-hashtag-mixer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Intelligent Hashtag Mixing Algorithm
 * Advanced algorithm that combines trending RSS hashtags with business-specific tags
 * for optimal reach and relevance using machine learning-inspired scoring
 */ __turbopack_context__.s({
    "IntelligentHashtagMixer": (()=>IntelligentHashtagMixer),
    "intelligentHashtagMixer": (()=>intelligentHashtagMixer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$realtime$2d$hashtag$2d$scorer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/realtime-hashtag-scorer.ts [app-rsc] (ecmascript)");
;
class IntelligentHashtagMixer {
    mixingCache = new Map();
    cacheTimeout = 20 * 60 * 1000;
    /**
   * Create intelligent hashtag mix using advanced algorithms
   */ async createIntelligentMix(advancedStrategy, viralStrategy, context) {
        const cacheKey = this.generateCacheKey(context);
        const cached = this.mixingCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.strategy;
        }
        try {
            // 🧠 STEP 1: Score all available hashtags
            const allHashtags = this.collectAllHashtags(advancedStrategy, viralStrategy);
            const scoredHashtags = await this.scoreAllHashtags(allHashtags, context);
            // 🎯 STEP 2: Apply intelligent mixing algorithm
            const mixedHashtags = this.applyMixingAlgorithm(scoredHashtags, context);
            // 📊 STEP 3: Analyze the final mix
            const analytics = this.analyzeMix(mixedHashtags, advancedStrategy, context);
            // 🏗️ STEP 4: Structure the final strategy
            const strategy = this.structureFinalStrategy(mixedHashtags, analytics);
            // Cache the result
            this.mixingCache.set(cacheKey, {
                strategy,
                timestamp: Date.now()
            });
            return strategy;
        } catch (error) {
            return this.getFallbackMix(context);
        }
    }
    /**
   * Collect all hashtags from different sources
   */ collectAllHashtags(advancedStrategy, viralStrategy) {
        const hashtags = [];
        // Advanced strategy hashtags (highest priority)
        advancedStrategy.finalRecommendations.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'advanced_rss',
                priority: 10
            });
        });
        advancedStrategy.topTrending.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_trending',
                priority: 9
            });
        });
        advancedStrategy.emergingTrends.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_emerging',
                priority: 8
            });
        });
        advancedStrategy.businessOptimized.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_business',
                priority: 8
            });
        });
        // Viral strategy hashtags (medium priority)
        viralStrategy.trending.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_trending',
                priority: 7
            });
        });
        viralStrategy.viral.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_engagement',
                priority: 7
            });
        });
        viralStrategy.niche.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_niche',
                priority: 6
            });
        });
        viralStrategy.location.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_location',
                priority: 6
            });
        });
        viralStrategy.platform.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_platform',
                priority: 5
            });
        });
        // Remove duplicates while preserving highest priority
        const uniqueHashtags = new Map();
        hashtags.forEach((item)=>{
            const existing = uniqueHashtags.get(item.hashtag);
            if (!existing || item.priority > existing.priority) {
                uniqueHashtags.set(item.hashtag, item);
            }
        });
        return Array.from(uniqueHashtags.values());
    }
    /**
   * Score all hashtags using the realtime scorer
   */ async scoreAllHashtags(hashtags, context) {
        const scoringContext = {
            businessType: context.businessType,
            businessName: context.businessName,
            location: context.location,
            platform: context.platform,
            postContent: context.postContent,
            targetAudience: context.targetAudience,
            services: context.services
        };
        const scoredHashtags = await Promise.all(hashtags.map(async (item)=>({
                ...item,
                score: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$realtime$2d$hashtag$2d$scorer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["realtimeHashtagScorer"].scoreHashtag(item.hashtag, scoringContext)
            })));
        return scoredHashtags;
    }
    /**
   * Apply intelligent mixing algorithm based on context priority
   */ applyMixingAlgorithm(scoredHashtags, context) {
        return scoredHashtags.map((item)=>{
            let finalScore = 0;
            // Base score from realtime scorer
            finalScore += item.score.totalScore * 0.4;
            // Priority bonus from source
            finalScore += item.priority * 0.2;
            // Context-specific adjustments
            switch(context.priority){
                case 'reach':
                    finalScore += item.score.breakdown.trendingScore * 0.3;
                    finalScore += item.score.breakdown.engagementPotential * 0.1;
                    break;
                case 'relevance':
                    finalScore += item.score.breakdown.businessRelevance * 0.3;
                    finalScore += item.score.breakdown.semanticRelevance * 0.1;
                    break;
                case 'engagement':
                    finalScore += item.score.breakdown.engagementPotential * 0.3;
                    finalScore += item.score.breakdown.platformOptimization * 0.1;
                    break;
                case 'balanced':
                default:
                    finalScore += (item.score.breakdown.trendingScore + item.score.breakdown.businessRelevance + item.score.breakdown.engagementPotential) * 0.1;
                    break;
            }
            // RSS weight adjustment
            if (item.source.includes('rss')) {
                finalScore += finalScore * context.rssWeight * 0.2;
            }
            // Business weight adjustment
            if (item.source.includes('business') || item.source.includes('niche')) {
                finalScore += finalScore * context.businessWeight * 0.2;
            }
            // Confidence bonus
            finalScore += item.score.confidence * 2;
            return {
                ...item,
                finalScore: Math.round(finalScore * 10) / 10
            };
        }).sort((a, b)=>b.finalScore - a.finalScore);
    }
    /**
   * Analyze the quality of the final mix
   */ analyzeMix(mixedHashtags, advancedStrategy, context) {
        const top15 = mixedHashtags.slice(0, 15);
        // Calculate RSS influence
        const rssHashtags = top15.filter((item)=>item.source.includes('rss')).length;
        const rssInfluence = Math.round(rssHashtags / 15 * 100);
        // Calculate average business relevance
        const avgBusinessRelevance = top15.reduce((sum, item)=>sum + item.score.breakdown.businessRelevance, 0) / 15;
        // Calculate average trending score
        const avgTrendingScore = top15.reduce((sum, item)=>sum + item.score.breakdown.trendingScore, 0) / 15;
        // Calculate diversity score
        const sources = new Set(top15.map((item)=>item.source));
        const diversityScore = Math.min(sources.size / 6 * 10, 10); // Max 6 different sources
        // Calculate overall confidence
        const avgConfidence = top15.reduce((sum, item)=>sum + item.score.confidence, 0) / 15;
        const confidenceLevel = Math.round(avgConfidence * 10);
        // Determine mixing strategy description
        const mixingStrategy = this.describeMixingStrategy(context, rssInfluence, avgBusinessRelevance);
        return {
            rssInfluence,
            businessRelevance: Math.round(avgBusinessRelevance * 10) / 10,
            trendingScore: Math.round(avgTrendingScore * 10) / 10,
            diversityScore: Math.round(diversityScore * 10) / 10,
            confidenceLevel,
            mixingStrategy
        };
    }
    /**
   * Structure the final hashtag strategy
   */ structureFinalStrategy(mixedHashtags, analytics) {
        const top15 = mixedHashtags.slice(0, 15);
        return {
            primary: top15.slice(0, 5).map((item)=>item.hashtag),
            secondary: top15.slice(5, 10).map((item)=>item.hashtag),
            tertiary: top15.slice(10, 15).map((item)=>item.hashtag),
            final: top15.map((item)=>item.hashtag),
            analytics
        };
    }
    /**
   * Describe the mixing strategy used
   */ describeMixingStrategy(context, rssInfluence, businessRelevance) {
        let strategy = `${context.priority.charAt(0).toUpperCase() + context.priority.slice(1)}-focused mixing`;
        if (rssInfluence >= 70) {
            strategy += ' with heavy RSS trending emphasis';
        } else if (rssInfluence >= 40) {
            strategy += ' with balanced RSS integration';
        } else {
            strategy += ' with minimal RSS influence';
        }
        if (businessRelevance >= 8) {
            strategy += ' and high business relevance';
        } else if (businessRelevance >= 6) {
            strategy += ' and moderate business relevance';
        } else {
            strategy += ' and broad market appeal';
        }
        return strategy;
    }
    /**
   * Generate cache key for mixing context
   */ generateCacheKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}-${context.priority}-${context.rssWeight}-${context.businessWeight}`.toLowerCase();
    }
    /**
   * Get fallback mix when algorithm fails
   */ getFallbackMix(context) {
        const fallbackHashtags = [
            '#trending',
            '#viral',
            `#${context.businessType.replace(/\s+/g, '')}`,
            '#local',
            '#community',
            '#business',
            '#quality',
            '#professional',
            '#service',
            '#new',
            '#amazing',
            '#best',
            '#popular',
            '#love',
            '#today'
        ];
        return {
            primary: fallbackHashtags.slice(0, 5),
            secondary: fallbackHashtags.slice(5, 10),
            tertiary: fallbackHashtags.slice(10, 15),
            final: fallbackHashtags,
            analytics: {
                rssInfluence: 0,
                businessRelevance: 5.0,
                trendingScore: 3.0,
                diversityScore: 4.0,
                confidenceLevel: 3,
                mixingStrategy: 'Fallback strategy due to algorithm failure'
            }
        };
    }
    /**
   * Get optimal mixing weights based on business type and platform
   */ getOptimalWeights(businessType, platform) {
        // Platform-specific weights
        const platformWeights = {
            instagram: {
                rssWeight: 0.7,
                businessWeight: 0.6
            },
            tiktok: {
                rssWeight: 0.8,
                businessWeight: 0.4
            },
            twitter: {
                rssWeight: 0.9,
                businessWeight: 0.5
            },
            linkedin: {
                rssWeight: 0.6,
                businessWeight: 0.8
            },
            facebook: {
                rssWeight: 0.5,
                businessWeight: 0.7
            },
            pinterest: {
                rssWeight: 0.6,
                businessWeight: 0.6
            }
        };
        // Business type adjustments
        const businessAdjustments = {
            restaurant: {
                rssBoost: 0.1,
                businessBoost: 0.2
            },
            retail: {
                rssBoost: 0.2,
                businessBoost: 0.1
            },
            healthcare: {
                rssBoost: 0.0,
                businessBoost: 0.3
            },
            technology: {
                rssBoost: 0.3,
                businessBoost: 0.1
            },
            fitness: {
                rssBoost: 0.2,
                businessBoost: 0.2
            },
            beauty: {
                rssBoost: 0.2,
                businessBoost: 0.1
            }
        };
        const platformWeight = platformWeights[platform.toLowerCase()] || {
            rssWeight: 0.6,
            businessWeight: 0.6
        };
        const businessAdj = businessAdjustments[businessType.toLowerCase()] || {
            rssBoost: 0.1,
            businessBoost: 0.1
        };
        return {
            rssWeight: Math.min(platformWeight.rssWeight + businessAdj.rssBoost, 1.0),
            businessWeight: Math.min(platformWeight.businessWeight + businessAdj.businessBoost, 1.0)
        };
    }
}
const intelligentHashtagMixer = new IntelligentHashtagMixer();
}}),
"[project]/src/ai/hashtag-performance-tracker.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Hashtag Performance Tracking and Learning System
 * Tracks hashtag performance and learns from successful combinations
 * to improve future hashtag generation with machine learning insights
 */ __turbopack_context__.s({
    "HashtagPerformanceTracker": (()=>HashtagPerformanceTracker),
    "hashtagPerformanceTracker": (()=>hashtagPerformanceTracker)
});
class HashtagPerformanceTracker {
    performanceData = new Map();
    combinationData = new Map();
    postHistory = [];
    storageKey = 'hashtag_performance_data';
    combinationStorageKey = 'hashtag_combination_data';
    postHistoryKey = 'post_performance_history';
    constructor(){
        this.loadPerformanceData();
    }
    /**
   * Track performance of a post with its hashtags
   */ trackPostPerformance(postData) {
        // Store post data
        this.postHistory.push(postData);
        // Update individual hashtag performance
        postData.hashtags.forEach((hashtag)=>{
            this.updateHashtagPerformance(hashtag, postData);
        });
        // Update combination performance
        this.updateCombinationPerformance(postData);
        // Save to storage
        this.savePerformanceData();
    }
    /**
   * Get performance insights for hashtag optimization
   */ getPerformanceInsights(businessType, platform, location) {
        const filteredData = this.filterPerformanceData(businessType, platform, location);
        return {
            topPerformingHashtags: this.getTopPerformingHashtags(filteredData),
            bestCombinations: this.getBestCombinations(businessType, platform, location),
            platformInsights: this.getPlatformInsights(),
            businessTypeInsights: this.getBusinessTypeInsights(),
            temporalInsights: this.getTemporalInsights(),
            learningRecommendations: this.generateLearningRecommendations(filteredData)
        };
    }
    /**
   * Get hashtag recommendations based on learning
   */ getLearnedRecommendations(businessType, platform, location, count = 10) {
        const recommendations = [];
        // Get hashtags that performed well for similar contexts
        const contextualData = Array.from(this.performanceData.values()).filter((data)=>{
            const businessMatch = data.businessTypes[businessType]?.avgEngagement > 0;
            const platformMatch = data.platforms[platform]?.avgEngagement > 0;
            const locationMatch = data.locations[location]?.avgEngagement > 0;
            return businessMatch || platformMatch || locationMatch;
        }).sort((a, b)=>b.averageEngagement - a.averageEngagement);
        contextualData.slice(0, count).forEach((data)=>{
            let confidence = 0;
            let reason = '';
            // Calculate confidence based on performance and context match
            if (data.businessTypes[businessType]) {
                confidence += 0.4 * (data.businessTypes[businessType].avgEngagement / 100);
                reason += `Strong performance in ${businessType} (${data.businessTypes[businessType].avgEngagement.toFixed(1)} avg engagement). `;
            }
            if (data.platforms[platform]) {
                confidence += 0.3 * (data.platforms[platform].avgEngagement / 100);
                reason += `Good ${platform} performance (${data.platforms[platform].avgEngagement.toFixed(1)} avg). `;
            }
            if (data.locations[location]) {
                confidence += 0.2 * (data.locations[location].avgEngagement / 100);
                reason += `Local relevance in ${location}. `;
            }
            confidence += 0.1 * data.successRate;
            reason += `${data.successRate.toFixed(1)}% success rate over ${data.usageCount} uses.`;
            recommendations.push({
                hashtag: data.hashtag,
                confidence: Math.min(confidence, 1),
                reason: reason.trim()
            });
        });
        return recommendations.sort((a, b)=>b.confidence - a.confidence);
    }
    /**
   * Update individual hashtag performance
   */ updateHashtagPerformance(hashtag, postData) {
        let data = this.performanceData.get(hashtag);
        if (!data) {
            data = {
                hashtag,
                usageCount: 0,
                totalEngagement: 0,
                averageEngagement: 0,
                platforms: {},
                businessTypes: {},
                locations: {},
                timePatterns: {
                    hourly: {},
                    daily: {},
                    monthly: {}
                },
                lastUsed: postData.timestamp,
                firstUsed: postData.timestamp,
                trendingScore: 0,
                successRate: 0
            };
        }
        // Update basic metrics
        data.usageCount++;
        data.totalEngagement += postData.engagement.total;
        data.averageEngagement = data.totalEngagement / data.usageCount;
        data.lastUsed = postData.timestamp;
        // Update platform performance
        if (!data.platforms[postData.platform]) {
            data.platforms[postData.platform] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.platforms[postData.platform].usage++;
        data.platforms[postData.platform].engagement += postData.engagement.total;
        data.platforms[postData.platform].avgEngagement = data.platforms[postData.platform].engagement / data.platforms[postData.platform].usage;
        // Update business type performance
        if (!data.businessTypes[postData.businessType]) {
            data.businessTypes[postData.businessType] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.businessTypes[postData.businessType].usage++;
        data.businessTypes[postData.businessType].engagement += postData.engagement.total;
        data.businessTypes[postData.businessType].avgEngagement = data.businessTypes[postData.businessType].engagement / data.businessTypes[postData.businessType].usage;
        // Update location performance
        if (!data.locations[postData.location]) {
            data.locations[postData.location] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.locations[postData.location].usage++;
        data.locations[postData.location].engagement += postData.engagement.total;
        data.locations[postData.location].avgEngagement = data.locations[postData.location].engagement / data.locations[postData.location].usage;
        // Update time patterns
        const hour = postData.timestamp.getHours();
        const day = postData.timestamp.getDay();
        const month = postData.timestamp.getMonth();
        if (!data.timePatterns.hourly[hour]) {
            data.timePatterns.hourly[hour] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.hourly[hour].usage++;
        data.timePatterns.hourly[hour].engagement += postData.engagement.total;
        if (!data.timePatterns.daily[day]) {
            data.timePatterns.daily[day] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.daily[day].usage++;
        data.timePatterns.daily[day].engagement += postData.engagement.total;
        if (!data.timePatterns.monthly[month]) {
            data.timePatterns.monthly[month] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.monthly[month].usage++;
        data.timePatterns.monthly[month].engagement += postData.engagement.total;
        // Update success rate
        const successfulPosts = this.postHistory.filter((post)=>post.hashtags.includes(hashtag) && post.success).length;
        data.successRate = successfulPosts / data.usageCount * 100;
        this.performanceData.set(hashtag, data);
    }
    /**
   * Update combination performance
   */ updateCombinationPerformance(postData) {
        const combinationKey = postData.hashtags.sort().join('|');
        let data = this.combinationData.get(combinationKey);
        if (!data) {
            data = {
                combination: postData.hashtags.sort(),
                usageCount: 0,
                totalEngagement: 0,
                averageEngagement: 0,
                successRate: 0,
                businessType: postData.businessType,
                platform: postData.platform,
                location: postData.location,
                lastUsed: postData.timestamp,
                performanceScore: 0
            };
        }
        data.usageCount++;
        data.totalEngagement += postData.engagement.total;
        data.averageEngagement = data.totalEngagement / data.usageCount;
        data.lastUsed = postData.timestamp;
        // Calculate success rate for this combination
        const combinationPosts = this.postHistory.filter((post)=>post.hashtags.sort().join('|') === combinationKey);
        const successfulCombinationPosts = combinationPosts.filter((post)=>post.success);
        data.successRate = successfulCombinationPosts.length / combinationPosts.length * 100;
        // Calculate performance score (weighted average of engagement and success rate)
        data.performanceScore = data.averageEngagement * 0.7 + data.successRate * 0.3;
        this.combinationData.set(combinationKey, data);
    }
    /**
   * Get top performing hashtags
   */ getTopPerformingHashtags(data) {
        return data.filter((d)=>d.usageCount >= 3) // Minimum usage for reliability
        .sort((a, b)=>b.averageEngagement - a.averageEngagement).slice(0, 20).map((d)=>({
                hashtag: d.hashtag,
                avgEngagement: d.averageEngagement,
                successRate: d.successRate,
                recommendationStrength: this.getRecommendationStrength(d)
            }));
    }
    /**
   * Get best hashtag combinations
   */ getBestCombinations(businessType, platform, location) {
        return Array.from(this.combinationData.values()).filter((data)=>{
            if (businessType && data.businessType !== businessType) return false;
            if (platform && data.platform !== platform) return false;
            if (location && data.location !== location) return false;
            return data.usageCount >= 2;
        }).sort((a, b)=>b.performanceScore - a.performanceScore).slice(0, 10).map((data)=>({
                hashtags: data.combination,
                avgEngagement: data.averageEngagement,
                successRate: data.successRate,
                context: `${data.businessType} on ${data.platform} in ${data.location}`
            }));
    }
    /**
   * Get platform-specific insights
   */ getPlatformInsights() {
        const insights = {};
        // Group data by platform
        const platformData = {};
        this.performanceData.forEach((data)=>{
            Object.keys(data.platforms).forEach((platform)=>{
                if (!platformData[platform]) platformData[platform] = [];
                platformData[platform].push(data);
            });
        });
        // Generate insights for each platform
        Object.entries(platformData).forEach(([platform, data])=>{
            const sortedData = data.filter((d)=>d.platforms[platform].usage >= 2).sort((a, b)=>b.platforms[platform].avgEngagement - a.platforms[platform].avgEngagement);
            insights[platform] = {
                bestHashtags: sortedData.slice(0, 10).map((d)=>d.hashtag),
                avgEngagement: sortedData.reduce((sum, d)=>sum + d.platforms[platform].avgEngagement, 0) / sortedData.length,
                optimalCount: this.calculateOptimalHashtagCount(platform)
            };
        });
        return insights;
    }
    /**
   * Get business type insights
   */ getBusinessTypeInsights() {
        const insights = {};
        // Group data by business type
        const businessData = {};
        this.performanceData.forEach((data)=>{
            Object.keys(data.businessTypes).forEach((businessType)=>{
                if (!businessData[businessType]) businessData[businessType] = [];
                businessData[businessType].push(data);
            });
        });
        // Generate insights for each business type
        Object.entries(businessData).forEach(([businessType, data])=>{
            const sortedData = data.filter((d)=>d.businessTypes[businessType].usage >= 2).sort((a, b)=>b.businessTypes[businessType].avgEngagement - a.businessTypes[businessType].avgEngagement);
            insights[businessType] = {
                bestHashtags: sortedData.slice(0, 8).map((d)=>d.hashtag),
                avgEngagement: sortedData.reduce((sum, d)=>sum + d.businessTypes[businessType].avgEngagement, 0) / sortedData.length,
                successPatterns: this.identifySuccessPatterns(businessType)
            };
        });
        return insights;
    }
    /**
   * Get temporal insights
   */ getTemporalInsights() {
        // Analyze best posting times
        const timePerformance = [];
        for(let day = 0; day < 7; day++){
            for(let hour = 0; hour < 24; hour++){
                const posts = this.postHistory.filter((post)=>post.timestamp.getDay() === day && post.timestamp.getHours() === hour);
                if (posts.length >= 3) {
                    const avgEngagement = posts.reduce((sum, post)=>sum + post.engagement.total, 0) / posts.length;
                    timePerformance.push({
                        hour,
                        day,
                        performance: avgEngagement
                    });
                }
            }
        }
        const bestTimes = timePerformance.sort((a, b)=>b.performance - a.performance).slice(0, 10);
        // Seasonal trends (simplified)
        const seasonalTrends = {
            spring: this.getSeasonalHashtags([
                2,
                3,
                4
            ]),
            summer: this.getSeasonalHashtags([
                5,
                6,
                7
            ]),
            fall: this.getSeasonalHashtags([
                8,
                9,
                10
            ]),
            winter: this.getSeasonalHashtags([
                11,
                0,
                1
            ])
        };
        return {
            bestTimes,
            seasonalTrends
        };
    }
    /**
   * Generate learning recommendations
   */ generateLearningRecommendations(data) {
        const recommendations = [];
        // Analyze performance patterns
        const highPerformers = data.filter((d)=>d.averageEngagement > 50 && d.successRate > 70);
        const lowPerformers = data.filter((d)=>d.averageEngagement < 10 || d.successRate < 30);
        if (highPerformers.length > 0) {
            recommendations.push(`Focus on high-performing hashtags like ${highPerformers.slice(0, 3).map((d)=>d.hashtag).join(', ')}`);
        }
        if (lowPerformers.length > 5) {
            recommendations.push(`Consider replacing underperforming hashtags: ${lowPerformers.slice(0, 3).map((d)=>d.hashtag).join(', ')}`);
        }
        // Platform-specific recommendations
        const platformPerformance = this.analyzePlatformPerformance();
        Object.entries(platformPerformance).forEach(([platform, perf])=>{
            if (perf.avgEngagement > 0) {
                recommendations.push(`${platform} performs best with ${perf.optimalCount} hashtags, focus on ${perf.topHashtag}`);
            }
        });
        return recommendations;
    }
    /**
   * Helper methods
   */ filterPerformanceData(businessType, platform, location) {
        return Array.from(this.performanceData.values()).filter((data)=>{
            if (businessType && !data.businessTypes[businessType]) return false;
            if (platform && !data.platforms[platform]) return false;
            if (location && !data.locations[location]) return false;
            return true;
        });
    }
    getRecommendationStrength(data) {
        if (data.averageEngagement > 50 && data.successRate > 70) return 'high';
        if (data.averageEngagement > 20 && data.successRate > 50) return 'medium';
        return 'low';
    }
    calculateOptimalHashtagCount(platform) {
        const platformPosts = this.postHistory.filter((post)=>post.platform === platform);
        const countPerformance = {};
        platformPosts.forEach((post)=>{
            const count = post.hashtags.length;
            if (!countPerformance[count]) countPerformance[count] = [];
            countPerformance[count].push(post.engagement.total);
        });
        let bestCount = 10; // Default
        let bestAvg = 0;
        Object.entries(countPerformance).forEach(([count, engagements])=>{
            if (engagements.length >= 3) {
                const avg = engagements.reduce((sum, eng)=>sum + eng, 0) / engagements.length;
                if (avg > bestAvg) {
                    bestAvg = avg;
                    bestCount = parseInt(count);
                }
            }
        });
        return bestCount;
    }
    identifySuccessPatterns(businessType) {
        const patterns = [];
        const businessPosts = this.postHistory.filter((post)=>post.businessType === businessType && post.success);
        // Analyze common hashtag patterns in successful posts
        const hashtagFrequency = {};
        businessPosts.forEach((post)=>{
            post.hashtags.forEach((hashtag)=>{
                hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;
            });
        });
        const commonHashtags = Object.entries(hashtagFrequency).filter(([, count])=>count >= 3).sort(([, a], [, b])=>b - a).slice(0, 5).map(([hashtag])=>hashtag);
        if (commonHashtags.length > 0) {
            patterns.push(`Common successful hashtags: ${commonHashtags.join(', ')}`);
        }
        return patterns;
    }
    getSeasonalHashtags(months) {
        const seasonalPosts = this.postHistory.filter((post)=>months.includes(post.timestamp.getMonth()));
        const hashtagFrequency = {};
        seasonalPosts.forEach((post)=>{
            post.hashtags.forEach((hashtag)=>{
                hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;
            });
        });
        return Object.entries(hashtagFrequency).sort(([, a], [, b])=>b - a).slice(0, 10).map(([hashtag])=>hashtag);
    }
    analyzePlatformPerformance() {
        const analysis = {};
        // Group by platform
        const platformGroups = {};
        this.postHistory.forEach((post)=>{
            if (!platformGroups[post.platform]) platformGroups[post.platform] = [];
            platformGroups[post.platform].push(post);
        });
        Object.entries(platformGroups).forEach(([platform, posts])=>{
            const avgEngagement = posts.reduce((sum, post)=>sum + post.engagement.total, 0) / posts.length;
            const optimalCount = this.calculateOptimalHashtagCount(platform);
            // Find top hashtag for this platform
            const hashtagPerf = {};
            posts.forEach((post)=>{
                post.hashtags.forEach((hashtag)=>{
                    if (!hashtagPerf[hashtag]) hashtagPerf[hashtag] = [];
                    hashtagPerf[hashtag].push(post.engagement.total);
                });
            });
            const topHashtag = Object.entries(hashtagPerf).filter(([, engagements])=>engagements.length >= 2).map(([hashtag, engagements])=>({
                    hashtag,
                    avg: engagements.reduce((sum, eng)=>sum + eng, 0) / engagements.length
                })).sort((a, b)=>b.avg - a.avg)[0]?.hashtag || '';
            analysis[platform] = {
                avgEngagement,
                optimalCount,
                topHashtag
            };
        });
        return analysis;
    }
    /**
   * Storage methods
   */ loadPerformanceData() {
        try {
            const performanceData = localStorage.getItem(this.storageKey);
            if (performanceData) {
                const parsed = JSON.parse(performanceData);
                this.performanceData = new Map(Object.entries(parsed));
            }
            const combinationData = localStorage.getItem(this.combinationStorageKey);
            if (combinationData) {
                const parsed = JSON.parse(combinationData);
                this.combinationData = new Map(Object.entries(parsed));
            }
            const postHistory = localStorage.getItem(this.postHistoryKey);
            if (postHistory) {
                this.postHistory = JSON.parse(postHistory).map((post)=>({
                        ...post,
                        timestamp: new Date(post.timestamp)
                    }));
            }
        } catch (error) {
        // Initialize with empty data if loading fails
        }
    }
    savePerformanceData() {
        try {
            // Save performance data
            const performanceObj = Object.fromEntries(this.performanceData);
            localStorage.setItem(this.storageKey, JSON.stringify(performanceObj));
            // Save combination data
            const combinationObj = Object.fromEntries(this.combinationData);
            localStorage.setItem(this.combinationStorageKey, JSON.stringify(combinationObj));
            // Save post history (keep only last 1000 posts)
            const recentHistory = this.postHistory.slice(-1000);
            localStorage.setItem(this.postHistoryKey, JSON.stringify(recentHistory));
        } catch (error) {
        // Handle storage errors gracefully
        }
    }
    /**
   * Clear all performance data (for testing or reset)
   */ clearPerformanceData() {
        this.performanceData.clear();
        this.combinationData.clear();
        this.postHistory = [];
        localStorage.removeItem(this.storageKey);
        localStorage.removeItem(this.combinationStorageKey);
        localStorage.removeItem(this.postHistoryKey);
    }
}
const hashtagPerformanceTracker = new HashtagPerformanceTracker();
}}),
"[project]/src/ai/viral-hashtag-engine.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Viral Hashtag Engine - Real-time trending hashtag generation
 * Integrates with RSS feeds and trending data to generate viral hashtags
 * Enhanced with Advanced Trending Hashtag Analyzer for superior relevance
 */ __turbopack_context__.s({
    "ViralHashtagEngine": (()=>ViralHashtagEngine),
    "viralHashtagEngine": (()=>viralHashtagEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$trending$2d$hashtag$2d$analyzer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/advanced-trending-hashtag-analyzer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/intelligent-hashtag-mixer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/hashtag-performance-tracker.ts [app-rsc] (ecmascript)");
;
;
;
;
class ViralHashtagEngine {
    /**
   * Generate viral hashtag strategy using advanced RSS analysis and real-time trending data
   */ async generateViralHashtags(businessType, businessName, location, platform, services, targetAudience) {
        try {
            // 🚀 ENHANCED: Use Advanced Hashtag Analyzer for superior RSS integration
            const analysisContext = {
                businessType,
                businessName,
                location,
                platform,
                services,
                targetAudience
            };
            // Get advanced hashtag analysis with RSS integration
            const advancedAnalysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$trending$2d$hashtag$2d$analyzer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["advancedHashtagAnalyzer"].analyzeHashtagTrends(analysisContext);
            // Get traditional trending data as backup
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                businessType,
                location,
                platform,
                targetAudience
            });
            // 🔥 ENHANCED: Generate hashtag categories using advanced analysis
            const trending = this.extractTrendingFromAnalysis(advancedAnalysis, trendingData);
            const viral = this.getEnhancedViralHashtags(businessType, platform, advancedAnalysis);
            const niche = this.getEnhancedNicheHashtags(businessType, services, advancedAnalysis);
            const location_tags = this.getEnhancedLocationHashtags(location, advancedAnalysis);
            const community = this.getCommunityHashtags(businessType, targetAudience);
            const seasonal = this.getSeasonalHashtags();
            const platform_tags = this.getEnhancedPlatformHashtags(platform, advancedAnalysis);
            // 🧠 ENHANCED: Use Intelligent Hashtag Mixer for optimal combination
            const mixingContext = {
                businessType,
                businessName,
                location,
                platform,
                postContent: undefined,
                targetAudience,
                services,
                priority: 'balanced',
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["intelligentHashtagMixer"].getOptimalWeights(businessType, platform)
            };
            // Create the current strategy for mixing
            const currentStrategy = {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total: [] // Will be filled by mixer
            };
            // Apply intelligent mixing
            const intelligentMix = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["intelligentHashtagMixer"].createIntelligentMix(advancedAnalysis, currentStrategy, mixingContext);
            // 🧠 ENHANCED: Get learned recommendations from performance tracking
            const learnedRecommendations = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getLearnedRecommendations(businessType, platform, location, 5);
            // 📊 Get performance insights for improvement suggestions
            const performanceInsights = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getPerformanceInsights(businessType, platform, location);
            // 🎯 Integrate learned recommendations with intelligent mix
            const enhancedTotal = this.integrateLearnedRecommendations(intelligentMix.final, learnedRecommendations, performanceInsights);
            // Use the enhanced hashtags as the final total
            const total = enhancedTotal;
            // Calculate confidence score based on RSS data quality
            const confidenceScore = this.calculateConfidenceScore(advancedAnalysis);
            return {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total,
                analytics: {
                    topPerformers: advancedAnalysis.finalRecommendations.slice(0, 5),
                    emergingTrends: advancedAnalysis.emergingTrends.map((t)=>t.hashtag).slice(0, 3),
                    businessOptimized: advancedAnalysis.businessOptimized.map((b)=>b.hashtag).slice(0, 3),
                    rssSourced: this.extractRSSSourcedHashtags(advancedAnalysis),
                    confidenceScore,
                    // Include intelligent mixing analytics
                    mixingStrategy: {
                        rssInfluence: intelligentMix.analytics.rssInfluence,
                        businessRelevance: intelligentMix.analytics.businessRelevance,
                        trendingScore: intelligentMix.analytics.trendingScore,
                        diversityScore: intelligentMix.analytics.diversityScore,
                        confidenceLevel: intelligentMix.analytics.confidenceLevel,
                        algorithm: intelligentMix.analytics.mixingStrategy
                    },
                    // Include performance learning insights
                    learningInsights: {
                        learnedRecommendations,
                        historicalPerformance: this.calculateHistoricalPerformance(performanceInsights),
                        improvementSuggestions: performanceInsights.learningRecommendations
                    }
                }
            };
        } catch (error) {
            return this.getFallbackHashtags(businessType, location, platform);
        }
    }
    /**
   * Extract trending hashtags from advanced analysis
   */ extractTrendingFromAnalysis(advancedAnalysis, fallbackData) {
        // Prioritize RSS-sourced trending hashtags
        const rssHashtags = advancedAnalysis.topTrending.filter((analysis)=>analysis.sources.some((source)=>source !== 'business_generator' && source !== 'fallback')).map((analysis)=>analysis.hashtag);
        // Add high-scoring emerging trends
        const emergingHashtags = advancedAnalysis.emergingTrends.filter((analysis)=>analysis.trendingScore >= 3).map((analysis)=>analysis.hashtag);
        // Combine with fallback data if needed
        const combined = [
            ...rssHashtags,
            ...emergingHashtags,
            ...fallbackData.hashtags
        ];
        // Remove duplicates and return top trending
        return Array.from(new Set(combined)).slice(0, 8);
    }
    /**
   * Get enhanced viral hashtags using RSS analysis
   */ getEnhancedViralHashtags(businessType, platform, advancedAnalysis) {
        // Get traditional viral hashtags
        const traditionalViral = this.getViralHashtags(businessType, platform);
        // Add high-engagement hashtags from RSS analysis
        const rssViral = advancedAnalysis.topTrending.filter((analysis)=>analysis.engagementPotential >= 7).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssViral.slice(0, 4),
            ...traditionalViral.slice(0, 3)
        ];
        return Array.from(new Set(combined)).slice(0, 7);
    }
    /**
   * Get enhanced niche hashtags using business analysis
   */ getEnhancedNicheHashtags(businessType, services, advancedAnalysis) {
        // Get traditional niche hashtags
        const traditionalNiche = this.getNicheHashtags(businessType, services);
        // Add business-optimized hashtags from RSS analysis
        const rssNiche = advancedAnalysis.businessOptimized.filter((analysis)=>analysis.businessRelevance >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssNiche.slice(0, 3),
            ...traditionalNiche.slice(0, 3)
        ];
        return Array.from(new Set(combined)).slice(0, 6);
    }
    /**
   * Get enhanced location hashtags using location analysis
   */ getEnhancedLocationHashtags(location, advancedAnalysis) {
        // Get traditional location hashtags
        const traditionalLocation = this.getLocationHashtags(location);
        // Add location-specific hashtags from RSS analysis
        const rssLocation = advancedAnalysis.locationSpecific.filter((analysis)=>analysis.locationRelevance >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssLocation.slice(0, 2),
            ...traditionalLocation.slice(0, 2)
        ];
        return Array.from(new Set(combined)).slice(0, 4);
    }
    /**
   * Get enhanced platform hashtags using platform analysis
   */ getEnhancedPlatformHashtags(platform, advancedAnalysis) {
        // Get traditional platform hashtags
        const traditionalPlatform = this.getPlatformHashtags(platform);
        // Add platform-optimized hashtags from RSS analysis
        const rssPlatform = advancedAnalysis.platformNative.filter((analysis)=>analysis.platformOptimization >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssPlatform.slice(0, 2),
            ...traditionalPlatform.slice(0, 2)
        ];
        return Array.from(new Set(combined)).slice(0, 4);
    }
    /**
   * Intelligent hashtag mixing algorithm
   */ intelligentHashtagMixing(hashtags, advancedAnalysis) {
        // Create a scoring system for hashtag selection
        const hashtagScores = new Map();
        // Score each hashtag based on multiple factors
        hashtags.forEach((hashtag)=>{
            let score = 0;
            // Find analysis for this hashtag
            const analysis = this.findHashtagAnalysis(hashtag, advancedAnalysis);
            if (analysis) {
                score += analysis.relevanceScore * 0.3;
                score += analysis.trendingScore * 0.25;
                score += analysis.businessRelevance * 0.2;
                score += analysis.engagementPotential * 0.15;
                score += analysis.platformOptimization * 0.1;
            } else {
                // Default score for hashtags not in analysis
                score = 5;
            }
            hashtagScores.set(hashtag, score);
        });
        // Sort by score and return top hashtags
        const sortedHashtags = Array.from(hashtagScores.entries()).sort(([, scoreA], [, scoreB])=>scoreB - scoreA).map(([hashtag])=>hashtag);
        return sortedHashtags.slice(0, 15);
    }
    /**
   * Find hashtag analysis in advanced analysis results
   */ findHashtagAnalysis(hashtag, advancedAnalysis) {
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        return allAnalyses.find((analysis)=>analysis.hashtag === hashtag);
    }
    /**
   * Calculate confidence score based on RSS data quality
   */ calculateConfidenceScore(advancedAnalysis) {
        let score = 0;
        let factors = 0;
        // Factor 1: Number of RSS sources
        const rssSourceCount = this.countRSSSources(advancedAnalysis);
        if (rssSourceCount > 0) {
            score += Math.min(rssSourceCount * 2, 10);
            factors++;
        }
        // Factor 2: Quality of trending data
        const trendingQuality = advancedAnalysis.topTrending.length > 0 ? 8 : 3;
        score += trendingQuality;
        factors++;
        // Factor 3: Business relevance coverage
        const businessCoverage = advancedAnalysis.businessOptimized.length >= 3 ? 9 : 5;
        score += businessCoverage;
        factors++;
        // Factor 4: Emerging trends availability
        const emergingTrends = advancedAnalysis.emergingTrends.length > 0 ? 7 : 4;
        score += emergingTrends;
        factors++;
        return factors > 0 ? Math.round(score / factors) : 5;
    }
    /**
   * Count RSS sources in analysis
   */ countRSSSources(advancedAnalysis) {
        const sources = new Set();
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        allAnalyses.forEach((analysis)=>{
            analysis.sources.forEach((source)=>{
                if (source !== 'business_generator' && source !== 'fallback') {
                    sources.add(source);
                }
            });
        });
        return sources.size;
    }
    /**
   * Extract RSS-sourced hashtags
   */ extractRSSSourcedHashtags(advancedAnalysis) {
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        return allAnalyses.filter((analysis)=>analysis.sources.some((source)=>source !== 'business_generator' && source !== 'fallback')).map((analysis)=>analysis.hashtag).slice(0, 8);
    }
    /**
   * Get high-engagement viral hashtags
   */ getViralHashtags(businessType, platform) {
        const viralHashtags = {
            general: [
                '#viral',
                '#trending',
                '#fyp',
                '#explore',
                '#discover',
                '#amazing',
                '#incredible',
                '#mustsee'
            ],
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#reels',
                '#explorepage'
            ],
            tiktok: [
                '#fyp',
                '#foryou',
                '#viral',
                '#trending',
                '#foryoupage'
            ],
            facebook: [
                '#viral',
                '#share',
                '#community',
                '#local',
                '#trending'
            ],
            twitter: [
                '#trending',
                '#viral',
                '#breaking',
                '#news',
                '#update'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#networking',
                '#career',
                '#industry'
            ]
        };
        const general = viralHashtags.general.sort(()=>0.5 - Math.random()).slice(0, 4);
        const platformSpecific = viralHashtags[platform.toLowerCase()] || [];
        return [
            ...general,
            ...platformSpecific.slice(0, 3)
        ];
    }
    /**
   * Get business-specific niche hashtags
   */ getNicheHashtags(businessType, services) {
        const nicheMap = {
            restaurant: [
                '#foodie',
                '#delicious',
                '#freshfood',
                '#localeats',
                '#foodlover',
                '#tasty',
                '#chef',
                '#dining'
            ],
            bakery: [
                '#freshbaked',
                '#artisan',
                '#homemade',
                '#bakery',
                '#pastry',
                '#bread',
                '#dessert',
                '#sweet'
            ],
            fitness: [
                '#fitness',
                '#workout',
                '#health',
                '#gym',
                '#strong',
                '#motivation',
                '#fitlife',
                '#training'
            ],
            beauty: [
                '#beauty',
                '#skincare',
                '#makeup',
                '#glam',
                '#selfcare',
                '#beautiful',
                '#style',
                '#cosmetics'
            ],
            tech: [
                '#tech',
                '#innovation',
                '#digital',
                '#software',
                '#technology',
                '#startup',
                '#coding',
                '#ai'
            ],
            retail: [
                '#shopping',
                '#fashion',
                '#style',
                '#sale',
                '#newcollection',
                '#boutique',
                '#trendy',
                '#deals'
            ]
        };
        const baseNiche = nicheMap[businessType.toLowerCase()] || [
            '#business',
            '#service',
            '#quality',
            '#professional'
        ];
        // Add service-specific hashtags if provided
        if (services) {
            const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            baseNiche.push(...serviceHashtags);
        }
        return baseNiche.slice(0, 6);
    }
    /**
   * Get location-based hashtags
   */ getLocationHashtags(location) {
        const locationParts = location.split(',').map((part)=>part.trim());
        const hashtags = [];
        locationParts.forEach((part)=>{
            const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '');
            if (cleanLocation.length > 2) {
                hashtags.push(`#${cleanLocation.toLowerCase()}`);
            }
        });
        // Add generic location hashtags
        hashtags.push('#local', '#community', '#neighborhood');
        return hashtags.slice(0, 5);
    }
    /**
   * Get community engagement hashtags
   */ getCommunityHashtags(businessType, targetAudience) {
        const communityHashtags = [
            '#community',
            '#local',
            '#support',
            '#family',
            '#friends',
            '#together',
            '#love'
        ];
        if (targetAudience) {
            const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            communityHashtags.push(...audienceHashtags);
        }
        return communityHashtags.slice(0, 5);
    }
    /**
   * Get seasonal/timely hashtags
   */ getSeasonalHashtags() {
        const now = new Date();
        const month = now.getMonth();
        const day = now.getDate();
        // Seasonal hashtags based on current time
        const seasonal = {
            0: [
                '#newyear',
                '#january',
                '#fresh',
                '#newbeginnings'
            ],
            1: [
                '#february',
                '#love',
                '#valentine',
                '#winter'
            ],
            2: [
                '#march',
                '#spring',
                '#fresh',
                '#bloom'
            ],
            3: [
                '#april',
                '#spring',
                '#easter',
                '#renewal'
            ],
            4: [
                '#may',
                '#spring',
                '#mothers',
                '#bloom'
            ],
            5: [
                '#june',
                '#summer',
                '#fathers',
                '#sunshine'
            ],
            6: [
                '#july',
                '#summer',
                '#vacation',
                '#hot'
            ],
            7: [
                '#august',
                '#summer',
                '#vacation',
                '#sunny'
            ],
            8: [
                '#september',
                '#fall',
                '#autumn',
                '#backtoschool'
            ],
            9: [
                '#october',
                '#fall',
                '#halloween',
                '#autumn'
            ],
            10: [
                '#november',
                '#thanksgiving',
                '#grateful',
                '#fall'
            ],
            11: [
                '#december',
                '#christmas',
                '#holiday',
                '#winter'
            ] // December
        };
        return seasonal[month] || [
            '#today',
            '#now',
            '#current'
        ];
    }
    /**
   * Get platform-specific hashtags
   */ getPlatformHashtags(platform) {
        const platformHashtags = {
            instagram: [
                '#instagram',
                '#insta',
                '#ig'
            ],
            facebook: [
                '#facebook',
                '#fb',
                '#social'
            ],
            twitter: [
                '#twitter',
                '#tweet',
                '#x'
            ],
            linkedin: [
                '#linkedin',
                '#professional',
                '#business'
            ],
            tiktok: [
                '#tiktok',
                '#tt',
                '#video'
            ]
        };
        return platformHashtags[platform.toLowerCase()] || [
            '#social',
            '#media'
        ];
    }
    /**
   * Get business-relevant trending hashtags
   */ getBusinessTrendingHashtags(businessType, platform) {
        // This would integrate with real trending APIs in production
        const trendingByBusiness = {
            restaurant: [
                '#foodtrends',
                '#eats2024',
                '#localfood',
                '#foodie'
            ],
            fitness: [
                '#fitness2024',
                '#healthtrends',
                '#workout',
                '#wellness'
            ],
            beauty: [
                '#beautytrends',
                '#skincare2024',
                '#makeup',
                '#selfcare'
            ],
            tech: [
                '#tech2024',
                '#innovation',
                '#ai',
                '#digital'
            ],
            retail: [
                '#fashion2024',
                '#shopping',
                '#style',
                '#trends'
            ]
        };
        return trendingByBusiness[businessType.toLowerCase()] || [
            '#trending',
            '#popular',
            '#new'
        ];
    }
    /**
   * Optimize hashtag selection for maximum virality
   */ optimizeForVirality(hashtags) {
        // Remove duplicates
        const unique = Array.from(new Set(hashtags));
        // Sort by estimated engagement potential (simplified scoring)
        const scored = unique.map((tag)=>({
                tag,
                score: this.calculateViralScore(tag)
            }));
        scored.sort((a, b)=>b.score - a.score);
        return scored.slice(0, 15).map((item)=>item.tag);
    }
    /**
   * Calculate viral potential score for a hashtag
   */ calculateViralScore(hashtag) {
        let score = 0;
        // High-engagement keywords get bonus points
        const viralKeywords = [
            'viral',
            'trending',
            'fyp',
            'explore',
            'amazing',
            'incredible'
        ];
        if (viralKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 10;
        }
        // Platform-specific hashtags get bonus
        const platformKeywords = [
            'instagram',
            'tiktok',
            'reels',
            'story'
        ];
        if (platformKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 5;
        }
        // Local hashtags get moderate bonus
        const localKeywords = [
            'local',
            'community',
            'neighborhood'
        ];
        if (localKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 3;
        }
        // Length penalty (very long hashtags perform worse)
        if (hashtag.length > 20) score -= 2;
        if (hashtag.length > 30) score -= 5;
        return score + Math.random(); // Add randomness for variety
    }
    /**
   * Enhanced fallback hashtags when trending data fails
   */ getFallbackHashtags(businessType, location, platform) {
        const fallbackTotal = [
            '#trending',
            '#viral',
            `#${businessType.replace(/\s+/g, '')}`,
            '#local',
            '#community',
            '#amazing',
            '#quality',
            '#professional',
            '#popular',
            '#new',
            '#support',
            '#service',
            `#${platform.toLowerCase()}`,
            '#today',
            '#love'
        ];
        return {
            trending: [
                '#trending',
                '#viral',
                '#popular',
                '#new'
            ],
            viral: [
                '#amazing',
                '#incredible',
                '#mustsee',
                '#wow'
            ],
            niche: [
                `#${businessType.replace(/\s+/g, '')}`,
                '#quality',
                '#professional',
                '#service'
            ],
            location: [
                '#local',
                '#community',
                `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`
            ],
            community: [
                '#community',
                '#support',
                '#family',
                '#love'
            ],
            seasonal: [
                '#today',
                '#now'
            ],
            platform: [
                `#${platform.toLowerCase()}`
            ],
            total: fallbackTotal,
            analytics: {
                topPerformers: fallbackTotal.slice(0, 5),
                emergingTrends: [
                    '#trending',
                    '#viral',
                    '#new'
                ],
                businessOptimized: [
                    `#${businessType.replace(/\s+/g, '')}`,
                    '#quality',
                    '#professional'
                ],
                rssSourced: [],
                confidenceScore: 3 // Low confidence for fallback
            }
        };
    }
    /**
   * 🧠 ENHANCED: Integrate learned recommendations with intelligent mix
   */ integrateLearnedRecommendations(intelligentMix, learnedRecommendations, performanceInsights) {
        const enhancedHashtags = [
            ...intelligentMix
        ];
        // Replace low-confidence hashtags with high-confidence learned recommendations
        const highConfidenceRecommendations = learnedRecommendations.filter((rec)=>rec.confidence >= 0.7);
        if (highConfidenceRecommendations.length > 0) {
            // Find hashtags in the mix that might be replaced
            const replaceableIndices = [];
            // Look for hashtags that aren't in the top performers
            const topPerformers = performanceInsights.topPerformingHashtags.map((h)=>h.hashtag);
            enhancedHashtags.forEach((hashtag, index)=>{
                if (!topPerformers.includes(hashtag) && index >= 10) {
                    replaceableIndices.push(index);
                }
            });
            // Replace up to 3 hashtags with learned recommendations
            const replacementCount = Math.min(highConfidenceRecommendations.length, replaceableIndices.length, 3);
            for(let i = 0; i < replacementCount; i++){
                const indexToReplace = replaceableIndices[i];
                const recommendation = highConfidenceRecommendations[i];
                // Only replace if the recommendation isn't already in the mix
                if (!enhancedHashtags.includes(recommendation.hashtag)) {
                    enhancedHashtags[indexToReplace] = recommendation.hashtag;
                }
            }
        }
        return enhancedHashtags;
    }
    /**
   * Calculate historical performance score
   */ calculateHistoricalPerformance(performanceInsights) {
        if (!performanceInsights.topPerformingHashtags.length) return 0;
        const avgEngagement = performanceInsights.topPerformingHashtags.reduce((sum, hashtag)=>sum + hashtag.avgEngagement, 0) / performanceInsights.topPerformingHashtags.length;
        const avgSuccessRate = performanceInsights.topPerformingHashtags.reduce((sum, hashtag)=>sum + hashtag.successRate, 0) / performanceInsights.topPerformingHashtags.length;
        // Weighted score: 70% engagement, 30% success rate
        return Math.round((avgEngagement * 0.7 + avgSuccessRate * 0.3) * 10) / 10;
    }
    /**
   * 📊 ENHANCED: Method to track hashtag performance after post creation
   */ trackHashtagPerformance(hashtags, platform, businessType, location, engagement, success = false) {
        const postData = {
            postId: `post_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            hashtags,
            platform,
            businessType,
            location,
            timestamp: new Date(),
            engagement,
            success
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].trackPostPerformance(postData);
    }
    /**
   * 📈 Get performance insights for hashtag optimization
   */ getHashtagPerformanceInsights(businessType, platform, location) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getPerformanceInsights(businessType, platform, location);
    }
    /**
   * 🎯 Get learned hashtag recommendations
   */ getLearnedHashtagRecommendations(businessType, platform, location, count = 10) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getLearnedRecommendations(businessType, platform, location, count);
    }
}
const viralHashtagEngine = new ViralHashtagEngine();
}}),
"[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Real-Time Trends Integration System
 * 
 * This module integrates multiple real-time trending topic sources
 * and provides a unified interface for getting current trends.
 */ __turbopack_context__.s({
    "TRENDING_CONFIG": (()=>TRENDING_CONFIG),
    "fetchCurrentNews": (()=>fetchCurrentNews),
    "fetchGoogleTrends": (()=>fetchGoogleTrends),
    "fetchLocalContext": (()=>fetchLocalContext),
    "fetchRedditTrends": (()=>fetchRedditTrends),
    "fetchTwitterTrends": (()=>fetchTwitterTrends)
});
const TRENDING_CONFIG = {
    sources: {
        googleTrends: {
            name: 'Google Trends RSS',
            enabled: process.env.GOOGLE_TRENDS_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://trends.google.com/trends/trendingsearches/daily/rss',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        twitterApi: {
            name: 'Twitter API v1.1',
            enabled: false,
            apiKey: process.env.TWITTER_BEARER_TOKEN,
            baseUrl: 'https://api.twitter.com/1.1',
            rateLimitPerHour: 300
        },
        newsApi: {
            name: 'News API',
            enabled: false,
            apiKey: process.env.NEWS_API_KEY,
            baseUrl: 'https://newsapi.org/v2',
            rateLimitPerHour: 1000
        },
        redditApi: {
            name: 'Reddit RSS',
            enabled: process.env.REDDIT_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://www.reddit.com',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        youtubeApi: {
            name: 'YouTube Data API',
            enabled: !!process.env.YOUTUBE_API_KEY,
            apiKey: process.env.YOUTUBE_API_KEY,
            baseUrl: 'https://www.googleapis.com/youtube/v3',
            rateLimitPerHour: 10000
        },
        eventbriteApi: {
            name: 'Eventbrite API',
            enabled: !!process.env.EVENTBRITE_API_KEY,
            apiKey: process.env.EVENTBRITE_API_KEY,
            baseUrl: 'https://www.eventbriteapi.com/v3',
            rateLimitPerHour: 1000
        },
        openWeatherApi: {
            name: 'OpenWeather API',
            enabled: !!process.env.OPENWEATHER_API_KEY,
            apiKey: process.env.OPENWEATHER_API_KEY,
            baseUrl: 'https://api.openweathermap.org/data/2.5',
            rateLimitPerHour: 1000
        }
    },
    fallbackToStatic: true,
    cacheTimeMinutes: 30
};
async function fetchGoogleTrends(location, category) {
    if (!TRENDING_CONFIG.sources.googleTrends.enabled) {
        return getGoogleTrendsFallback(location, category);
    }
    try {
        // Import RSS integration
        const { fetchGoogleTrendsRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Google Trends
        const geoCode = getGoogleTrendsGeoCode(location);
        const trends = await fetchGoogleTrendsRSS(geoCode, category);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'google_trends_rss'
            }));
    } catch (error) {
        return getGoogleTrendsFallback(location, category);
    }
}
async function fetchTwitterTrends(location, businessType) {
    if (!TRENDING_CONFIG.sources.twitterApi.enabled) {
        return getTwitterTrendsFallback(location, businessType);
    }
    try {
        const woeid = getTwitterWOEID(location);
        // Use Twitter API v2 trending topics endpoint
        const response = await fetch(`${TRENDING_CONFIG.sources.twitterApi.baseUrl}/trends/place.json?id=${woeid}`, {
            headers: {
                'Authorization': `Bearer ${TRENDING_CONFIG.sources.twitterApi.apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'TrendingTopicsBot/2.0'
            }
        });
        if (!response.ok) {
            throw new Error(`Twitter API error: ${response.status}`);
        }
        const data = await response.json();
        // Process Twitter trends data
        return processTwitterTrendsData(data, businessType);
    } catch (error) {
        return getTwitterTrendsFallback(location, businessType);
    }
}
async function fetchCurrentNews(location, businessType, category) {
    if (!TRENDING_CONFIG.sources.newsApi.enabled) {
        return getNewsFallback(location, businessType, category);
    }
    try {
        const params = new URLSearchParams({
            country: getNewsApiCountryCode(location),
            category: category || 'business',
            pageSize: '10',
            apiKey: TRENDING_CONFIG.sources.newsApi.apiKey
        });
        const response = await fetch(`${TRENDING_CONFIG.sources.newsApi.baseUrl}/top-headlines?${params}`);
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`News API error: ${response.status}`);
        }
        const data = await response.json();
        // Process news data
        return processNewsData(data, businessType);
    } catch (error) {
        return getNewsFallback(location, businessType, category);
    }
}
async function fetchRedditTrends(businessType, platform) {
    if (!TRENDING_CONFIG.sources.redditApi.enabled) {
        return getRedditTrendsFallback(businessType, platform);
    }
    try {
        // Import RSS integration
        const { fetchRedditRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Reddit trends
        const trends = await fetchRedditRSS(businessType);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'reddit_rss'
            }));
    } catch (error) {
        return getRedditTrendsFallback(businessType, platform);
    }
}
/**
 * Helper functions for processing API data
 */ function processGoogleTrendsData(data, location, category) {
    // Process Google Trends API response
    return [
        {
            topic: `Trending in ${location}`,
            source: 'google_trends',
            relevanceScore: 9,
            category: category || 'general',
            timeframe: 'now',
            engagement_potential: 'high'
        }
    ];
}
function processTwitterTrendsData(data, businessType) {
    // Process Twitter API response
    if (data && data[0] && data[0].trends) {
        return data[0].trends.slice(0, 10).map((trend)=>({
                topic: trend.name,
                source: 'twitter',
                relevanceScore: trend.tweet_volume ? Math.min(10, Math.log10(trend.tweet_volume)) : 5,
                category: 'social',
                timeframe: 'now',
                engagement_potential: trend.tweet_volume > 10000 ? 'high' : 'medium'
            }));
    }
    return [];
}
function processNewsData(data, businessType) {
    // Process News API response
    if (data && data.articles) {
        return data.articles.slice(0, 8).map((article)=>({
                topic: article.title,
                source: 'news',
                relevanceScore: 8,
                category: 'news',
                timeframe: 'today',
                engagement_potential: 'high',
                business_angle: `How this relates to ${businessType} industry`
            }));
    }
    return [];
}
function processRedditData(data, subreddit) {
    // Process Reddit API response
    if (data && data.data && data.data.children) {
        return data.data.children.slice(0, 5).map((post)=>({
                topic: post.data.title,
                source: 'reddit',
                relevanceScore: Math.min(10, post.data.score / 100),
                category: 'community',
                timeframe: 'today',
                engagement_potential: post.data.score > 1000 ? 'high' : 'medium',
                subreddit: subreddit
            }));
    }
    return [];
}
/**
 * Helper functions for API parameters
 */ function getGoogleTrendsGeoCode(location) {
    const geoMap = {
        'kenya': 'KE',
        'united states': 'US',
        'nairobi': 'KE',
        'new york': 'US-NY',
        'london': 'GB-ENG'
    };
    return geoMap[location.toLowerCase()] || 'US';
}
function getTwitterWOEID(location) {
    const woeidMap = {
        'kenya': '23424863',
        'united states': '23424977',
        'nairobi': '1528488',
        'new york': '2459115',
        'london': '44418'
    };
    return woeidMap[location.toLowerCase()] || '1'; // Worldwide
}
function getNewsApiCountryCode(location) {
    const countryMap = {
        'kenya': 'ke',
        'united states': 'us',
        'nairobi': 'ke',
        'new york': 'us',
        'london': 'gb'
    };
    return countryMap[location.toLowerCase()] || 'us';
}
function getRelevantSubreddits(businessType) {
    const subredditMap = {
        'financial technology software': [
            'fintech',
            'personalfinance',
            'investing',
            'entrepreneur'
        ],
        'restaurant': [
            'food',
            'recipes',
            'restaurantowners',
            'smallbusiness'
        ],
        'fitness': [
            'fitness',
            'bodybuilding',
            'nutrition',
            'personaltrainer'
        ],
        'technology': [
            'technology',
            'programming',
            'startups',
            'artificial'
        ]
    };
    return subredditMap[businessType.toLowerCase()] || [
        'business',
        'entrepreneur'
    ];
}
/**
 * Fallback functions when APIs are not available
 */ function getGoogleTrendsFallback(location, category) {
    return [
        {
            topic: `Local business trends in ${location}`,
            source: 'fallback',
            relevanceScore: 7,
            category: category || 'business',
            timeframe: 'week',
            engagement_potential: 'medium'
        }
    ];
}
function getTwitterTrendsFallback(location, businessType) {
    return [
        {
            topic: '#MondayMotivation',
            source: 'fallback',
            relevanceScore: 6,
            category: 'social',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getNewsFallback(location, businessType, category) {
    return [
        {
            topic: `${businessType} industry updates`,
            source: 'fallback',
            relevanceScore: 6,
            category: 'news',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getRedditTrendsFallback(businessType, platform) {
    return [
        {
            topic: `${businessType} community discussions`,
            source: 'fallback',
            relevanceScore: 5,
            category: 'community',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
async function fetchLocalContext(location, businessType) {
    const context = {};
    try {
        // Fetch weather context
        if (TRENDING_CONFIG.sources.openWeatherApi.enabled) {
            const params = new URLSearchParams({
                q: location,
                appid: TRENDING_CONFIG.sources.openWeatherApi.apiKey,
                units: 'metric'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.openWeatherApi.baseUrl}/weather?${params}`);
            if (response.ok) {
                const weatherData = await response.json();
                context.weather = {
                    temperature: Math.round(weatherData.main.temp),
                    condition: weatherData.weather[0].main,
                    business_impact: generateBusinessWeatherImpact(weatherData.weather[0].main, weatherData.main.temp, businessType),
                    content_opportunities: generateWeatherContentOpportunities(weatherData.weather[0].main, weatherData.main.temp, businessType)
                };
            }
        }
        // Fetch events context
        if (TRENDING_CONFIG.sources.eventbriteApi.enabled) {
            const params = new URLSearchParams({
                'location.address': location,
                'location.within': '25km',
                'start_date.range_start': new Date().toISOString(),
                'start_date.range_end': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                'sort_by': 'relevance',
                'page_size': '10'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.eventbriteApi.baseUrl}/events/search/?${params}`, {
                headers: {
                    'Authorization': `Bearer ${TRENDING_CONFIG.sources.eventbriteApi.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.ok) {
                const eventsData = await response.json();
                context.events = (eventsData.events || []).slice(0, 5).map((event)=>({
                        name: event.name?.text || 'Event',
                        category: event.category?.name || 'General',
                        relevance_score: calculateEventRelevance(event, businessType),
                        start_date: event.start?.local || event.start?.utc
                    }));
            }
        }
    } catch (error) {}
    return context;
}
// Helper functions for weather and events
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'financial technology software': {
            'sunny': 'Great weather for outdoor meetings and client visits',
            'rain': 'Perfect time for indoor productivity and digital solutions',
            'hot': 'Ideal for promoting mobile solutions and remote services',
            'cold': 'Good time for cozy indoor planning and financial reviews'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['restaurant'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    return opportunities;
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
}}),
"[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Topics and Market Intelligence System
 *
 * This module provides real-time trending topics, competitor analysis,
 * and market intelligence for content optimization.
 */ __turbopack_context__.s({
    "generateCompetitorInsights": (()=>generateCompetitorInsights),
    "generateCulturalContext": (()=>generateCulturalContext),
    "generateMarketIntelligence": (()=>generateMarketIntelligence),
    "generateRealTimeTrendingTopics": (()=>generateRealTimeTrendingTopics),
    "generateStaticTrendingTopics": (()=>generateStaticTrendingTopics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
;
async function generateRealTimeTrendingTopics(businessType, location, platform = 'general') {
    try {
        // Fetch from working real-time sources (temporarily disable failing APIs)
        const [googleTrends, redditTrends] = await Promise.allSettled([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchGoogleTrends"])(location, businessType),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchRedditTrends"])(businessType, platform)
        ]);
        // Temporarily disable failing APIs until we fix them
        const twitterTrends = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const currentNews = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const allTrends = [];
        // Process Google Trends
        if (googleTrends.status === 'fulfilled') {
            allTrends.push(...googleTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process Twitter Trends
        if (twitterTrends.status === 'fulfilled') {
            allTrends.push(...twitterTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process News
        if (currentNews.status === 'fulfilled') {
            allTrends.push(...currentNews.value.map((news)=>({
                    topic: news.topic,
                    relevanceScore: news.relevanceScore,
                    platform: platform,
                    category: news.category,
                    timeframe: news.timeframe,
                    engagement_potential: news.engagement_potential
                })));
        } else {}
        // Process Reddit Trends
        if (redditTrends.status === 'fulfilled') {
            allTrends.push(...redditTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        } else {}
        // If we have real-time trends, use them
        if (allTrends.length > 0) {
            return allTrends.sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 10);
        }
        // Fallback to static trends
        return generateStaticTrendingTopics(businessType, location, platform);
    } catch (error) {
        return generateStaticTrendingTopics(businessType, location, platform);
    }
}
function generateStaticTrendingTopics(businessType, location, platform = 'general') {
    const businessTrends = {
        'restaurant': [
            {
                topic: 'Sustainable dining trends',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Local food festivals',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Plant-based menu innovations',
                relevanceScore: 7,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'month',
                engagement_potential: 'medium'
            }
        ],
        'fitness': [
            {
                topic: 'New Year fitness resolutions',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Mental health and exercise',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Home workout equipment trends',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'technology': [
            {
                topic: 'AI in business automation',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Cybersecurity awareness',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'technology',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Remote work productivity tools',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'beauty': [
            {
                topic: 'Clean beauty movement',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Skincare for different seasons',
                relevanceScore: 8,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Sustainable beauty packaging',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'financial technology software': [
            {
                topic: 'Digital banking adoption in Africa',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Financial inclusion initiatives',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Mobile payment security',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ]
    };
    // Get base trends for business type
    const baseTrends = businessTrends[businessType.toLowerCase()] || businessTrends['technology'];
    // Add location-specific trends
    const locationTrends = generateLocationTrends(location);
    // Combine and filter by platform
    const allTrends = [
        ...baseTrends,
        ...locationTrends
    ];
    return allTrends.filter((trend)=>trend.platform === platform || trend.platform === 'general').sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 5);
}
/**
 * Generates location-specific trending topics
 */ function generateLocationTrends(location) {
    const locationMap = {
        'nairobi': [
            {
                topic: 'Nairobi tech hub growth',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Kenyan startup ecosystem',
                relevanceScore: 7,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'medium'
            }
        ],
        'new york': [
            {
                topic: 'NYC small business support',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ],
        'london': [
            {
                topic: 'London fintech innovation',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [];
}
function generateCompetitorInsights(businessType, location, services) {
    const competitorStrategies = {
        'financial technology software': [
            {
                competitor_name: 'Traditional Banks',
                content_gap: 'Lack of educational content about digital banking benefits',
                differentiation_opportunity: 'Focus on simplicity and accessibility for everyday users',
                successful_strategy: 'Trust-building through security messaging',
                avoid_strategy: 'Overly technical jargon that confuses users'
            },
            {
                competitor_name: 'Other Fintech Apps',
                content_gap: 'Limited focus on local market needs and culture',
                differentiation_opportunity: 'Emphasize local partnerships and community impact',
                successful_strategy: 'User testimonials and success stories',
                avoid_strategy: 'Generic global messaging without local relevance'
            }
        ],
        'restaurant': [
            {
                competitor_name: 'Chain Restaurants',
                content_gap: 'Lack of personal connection and local community focus',
                differentiation_opportunity: 'Highlight local sourcing, chef personality, and community involvement',
                successful_strategy: 'Behind-the-scenes content and food preparation videos',
                avoid_strategy: 'Generic food photos without story or context'
            }
        ],
        'fitness': [
            {
                competitor_name: 'Large Gym Chains',
                content_gap: 'Impersonal approach and lack of individual attention',
                differentiation_opportunity: 'Focus on personal transformation stories and community support',
                successful_strategy: 'Client success stories and progress tracking',
                avoid_strategy: 'Intimidating fitness content that discourages beginners'
            }
        ]
    };
    return competitorStrategies[businessType.toLowerCase()] || [
        {
            competitor_name: 'Industry Leaders',
            content_gap: 'Generic messaging without personal touch',
            differentiation_opportunity: 'Focus on authentic storytelling and customer relationships',
            successful_strategy: 'Educational content that provides real value',
            avoid_strategy: 'Overly promotional content without substance'
        }
    ];
}
function generateCulturalContext(location) {
    const culturalMap = {
        'nairobi, kenya': {
            location: 'Nairobi, Kenya',
            cultural_nuances: [
                'Ubuntu philosophy - community and interconnectedness',
                'Respect for elders and traditional values',
                'Entrepreneurial spirit and innovation mindset',
                'Multilingual communication (English, Swahili, local languages)'
            ],
            local_customs: [
                'Harambee - community cooperation and fundraising',
                'Greeting customs and respect protocols',
                'Religious diversity and tolerance',
                'Family-centered decision making'
            ],
            language_preferences: [
                'Mix of English and Swahili phrases',
                'Respectful and formal tone in business contexts',
                'Storytelling and proverb usage',
                'Community-focused language'
            ],
            seasonal_relevance: [
                'Rainy seasons (March-May, October-December)',
                'School calendar considerations',
                'Agricultural seasons and harvest times',
                'Holiday seasons and celebrations'
            ],
            local_events: [
                'Nairobi Innovation Week',
                'Kenya Music Festival',
                'Nairobi Restaurant Week',
                'Local cultural festivals'
            ]
        }
    };
    const locationKey = location.toLowerCase();
    return culturalMap[locationKey] || {
        location: location,
        cultural_nuances: [
            'Local community values',
            'Regional business customs'
        ],
        local_customs: [
            'Local traditions',
            'Community practices'
        ],
        language_preferences: [
            'Local language nuances',
            'Regional communication styles'
        ],
        seasonal_relevance: [
            'Local seasons',
            'Regional events'
        ],
        local_events: [
            'Local festivals',
            'Community gatherings'
        ]
    };
}
function generateMarketIntelligence(businessType, location, platform, services) {
    return {
        trending_topics: generateStaticTrendingTopics(businessType, location, platform),
        competitor_insights: generateCompetitorInsights(businessType, location, services),
        cultural_context: generateCulturalContext(location),
        viral_content_patterns: [
            'Behind-the-scenes authentic moments',
            'User-generated content and testimonials',
            'Educational content that solves problems',
            'Emotional storytelling with clear outcomes',
            'Interactive content that encourages participation'
        ],
        engagement_triggers: [
            'Ask questions that require personal responses',
            'Share relatable struggles and solutions',
            'Use local references and cultural touchpoints',
            'Create content that people want to share with friends',
            'Provide exclusive insights or early access'
        ]
    };
}
}}),
"[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Intelligent Context Selector
 * 
 * This module acts like a local expert who knows what information
 * is relevant for each business type, location, and content context.
 * It intelligently selects which data to use and which to ignore.
 */ __turbopack_context__.s({
    "filterContextData": (()=>filterContextData),
    "selectRelevantContext": (()=>selectRelevantContext)
});
function selectRelevantContext(businessType, location, platform, contentThemes, dayOfWeek) {
    const businessKey = businessType.toLowerCase();
    const locationKey = location.toLowerCase();
    const isWeekend = dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday';
    return {
        weather: analyzeWeatherRelevance(businessKey, locationKey, platform, isWeekend),
        events: analyzeEventsRelevance(businessKey, locationKey, platform, isWeekend),
        trends: analyzeTrendsRelevance(businessKey, locationKey, platform),
        cultural: analyzeCulturalRelevance(businessKey, locationKey, platform)
    };
}
/**
 * Determines if weather information is relevant for this business/location
 */ function analyzeWeatherRelevance(businessType, location, platform, isWeekend) {
    // High weather relevance businesses
    const weatherSensitiveBusinesses = [
        'restaurant',
        'cafe',
        'food',
        'dining',
        'fitness',
        'gym',
        'sports',
        'outdoor',
        'retail',
        'shopping',
        'fashion',
        'tourism',
        'travel',
        'hotel',
        'construction',
        'agriculture',
        'delivery',
        'transportation'
    ];
    // Medium weather relevance
    const moderateWeatherBusinesses = [
        'beauty',
        'spa',
        'wellness',
        'entertainment',
        'events',
        'real estate',
        'automotive'
    ];
    // Low/No weather relevance
    const weatherIndependentBusinesses = [
        'financial technology software',
        'fintech',
        'banking',
        'software',
        'technology',
        'saas',
        'consulting',
        'legal',
        'accounting',
        'insurance',
        'healthcare',
        'education',
        'digital marketing',
        'design'
    ];
    // Check business type relevance
    const isHighRelevance = weatherSensitiveBusinesses.some((type)=>businessType.includes(type));
    const isMediumRelevance = moderateWeatherBusinesses.some((type)=>businessType.includes(type));
    const isLowRelevance = weatherIndependentBusinesses.some((type)=>businessType.includes(type));
    // Location-based adjustments
    const isExtremeWeatherLocation = location.includes('nairobi') || location.includes('kenya') || location.includes('tropical');
    if (isHighRelevance) {
        return {
            useWeather: true,
            relevanceReason: `${businessType} customers are highly influenced by weather conditions`,
            priority: 'high'
        };
    }
    if (isMediumRelevance) {
        return {
            useWeather: true,
            relevanceReason: `Weather can impact ${businessType} customer behavior`,
            priority: 'medium'
        };
    }
    if (isLowRelevance) {
        return {
            useWeather: false,
            relevanceReason: `${businessType} operates independently of weather conditions`,
            priority: 'ignore'
        };
    }
    // Default case
    return {
        useWeather: isExtremeWeatherLocation,
        relevanceReason: isExtremeWeatherLocation ? 'Local weather is culturally significant' : 'Weather has minimal business impact',
        priority: isExtremeWeatherLocation ? 'low' : 'ignore'
    };
}
/**
 * Determines if local events are relevant for this business/location
 */ function analyzeEventsRelevance(businessType, location, platform, isWeekend) {
    // Always relevant for networking/community businesses
    const networkingBusinesses = [
        'consulting',
        'marketing',
        'business services',
        'financial technology software',
        'fintech',
        'real estate',
        'insurance',
        'legal'
    ];
    // Event-dependent businesses
    const eventDependentBusinesses = [
        'restaurant',
        'entertainment',
        'retail',
        'fitness',
        'beauty',
        'tourism'
    ];
    // B2B vs B2C consideration
    const isB2B = networkingBusinesses.some((type)=>businessType.includes(type)) || businessType.includes('software') || businessType.includes('technology');
    const isB2C = eventDependentBusinesses.some((type)=>businessType.includes(type));
    // Relevant event types based on business
    let eventTypes = [];
    if (isB2B) {
        eventTypes = [
            'business',
            'networking',
            'conference',
            'workshop',
            'professional'
        ];
    }
    if (isB2C) {
        eventTypes = [
            'community',
            'festival',
            'entertainment',
            'cultural',
            'local'
        ];
    }
    // Location-based event culture
    const isEventCentricLocation = location.includes('nairobi') || location.includes('new york') || location.includes('london');
    if (isB2B && isEventCentricLocation) {
        return {
            useEvents: true,
            relevanceReason: `${businessType} benefits from professional networking events`,
            priority: 'high',
            eventTypes
        };
    }
    if (isB2C) {
        return {
            useEvents: true,
            relevanceReason: `Local events drive foot traffic for ${businessType}`,
            priority: 'medium',
            eventTypes
        };
    }
    return {
        useEvents: isEventCentricLocation,
        relevanceReason: isEventCentricLocation ? 'Local events show community engagement' : 'Events have minimal business relevance',
        priority: isEventCentricLocation ? 'low' : 'ignore',
        eventTypes: [
            'community'
        ]
    };
}
/**
 * Determines trending topics relevance
 */ function analyzeTrendsRelevance(businessType, location, platform) {
    // Always use trends for social media businesses
    const trendDependentBusinesses = [
        'marketing',
        'social media',
        'content',
        'entertainment',
        'fashion',
        'beauty',
        'technology',
        'startup'
    ];
    // Industry-specific trend types
    let trendTypes = [];
    if (businessType.includes('technology') || businessType.includes('fintech')) {
        trendTypes = [
            'technology',
            'business',
            'innovation',
            'startup'
        ];
    } else if (businessType.includes('restaurant') || businessType.includes('food')) {
        trendTypes = [
            'food',
            'lifestyle',
            'local',
            'cultural'
        ];
    } else if (businessType.includes('fitness')) {
        trendTypes = [
            'health',
            'wellness',
            'lifestyle',
            'sports'
        ];
    } else {
        trendTypes = [
            'business',
            'local',
            'community'
        ];
    }
    const isTrendSensitive = trendDependentBusinesses.some((type)=>businessType.includes(type));
    // Platform consideration
    const isSocialPlatform = platform === 'instagram' || platform === 'twitter';
    return {
        useTrends: true,
        relevanceReason: isTrendSensitive ? `${businessType} thrives on current trends and conversations` : 'Trending topics increase content relevance and engagement',
        priority: isTrendSensitive ? 'high' : 'medium',
        trendTypes
    };
}
/**
 * Determines cultural context relevance
 */ function analyzeCulturalRelevance(businessType, location, platform) {
    // Always high relevance for local businesses
    const localBusinesses = [
        'restaurant',
        'retail',
        'fitness',
        'beauty',
        'real estate',
        'healthcare',
        'education'
    ];
    // Cultural elements to emphasize
    let culturalElements = [];
    if (location.includes('nairobi') || location.includes('kenya')) {
        culturalElements = [
            'ubuntu philosophy',
            'harambee spirit',
            'swahili expressions',
            'community values'
        ];
    } else if (location.includes('new york')) {
        culturalElements = [
            'diversity',
            'hustle culture',
            'innovation',
            'fast-paced lifestyle'
        ];
    } else if (location.includes('london')) {
        culturalElements = [
            'tradition',
            'multiculturalism',
            'business etiquette',
            'dry humor'
        ];
    } else {
        culturalElements = [
            'local customs',
            'community values',
            'regional preferences'
        ];
    }
    const isLocalBusiness = localBusinesses.some((type)=>businessType.includes(type));
    const isInternationalLocation = !location.includes('united states');
    return {
        useCultural: true,
        relevanceReason: isLocalBusiness ? `Local ${businessType} must connect with community culture` : 'Cultural awareness builds authentic connections',
        priority: isLocalBusiness || isInternationalLocation ? 'high' : 'medium',
        culturalElements
    };
}
function filterContextData(relevance, availableData) {
    const result = {
        contextInstructions: generateContextInstructions(relevance)
    };
    // Filter weather data
    if (relevance.weather.useWeather && availableData.weather) {
        result.selectedWeather = availableData.weather;
    }
    // Filter events data
    if (relevance.events.useEvents && availableData.events) {
        result.selectedEvents = availableData.events.filter((event)=>relevance.events.eventTypes.some((type)=>event.category?.toLowerCase().includes(type) || event.name?.toLowerCase().includes(type))).slice(0, relevance.events.priority === 'high' ? 3 : 1);
    }
    // Filter trends data
    if (relevance.trends.useTrends && availableData.trends) {
        result.selectedTrends = availableData.trends.filter((trend)=>relevance.trends.trendTypes.some((type)=>trend.category?.toLowerCase().includes(type) || trend.topic?.toLowerCase().includes(type))).slice(0, relevance.trends.priority === 'high' ? 5 : 3);
    }
    // Filter cultural data
    if (relevance.cultural.useCultural && availableData.cultural) {
        result.selectedCultural = {
            ...availableData.cultural,
            cultural_nuances: availableData.cultural.cultural_nuances?.filter((nuance)=>relevance.cultural.culturalElements.some((element)=>nuance.toLowerCase().includes(element.toLowerCase()))).slice(0, 3)
        };
    }
    return result;
}
/**
 * Generates context-specific instructions for the AI
 */ function generateContextInstructions(relevance) {
    const instructions = [];
    if (relevance.weather.useWeather) {
        if (relevance.weather.priority === 'high') {
            instructions.push('WEATHER: Integrate weather naturally as it significantly impacts customer behavior');
        } else if (relevance.weather.priority === 'medium') {
            instructions.push('WEATHER: Mention weather subtly if it adds value to the message');
        }
    } else {
        instructions.push('WEATHER: Ignore weather data - not relevant for this business type');
    }
    if (relevance.events.useEvents) {
        if (relevance.events.priority === 'high') {
            instructions.push('EVENTS: Highlight relevant local events as key business opportunities');
        } else {
            instructions.push('EVENTS: Reference events only if they add community connection value');
        }
    } else {
        instructions.push('EVENTS: Skip event references - focus on core business value');
    }
    if (relevance.trends.priority === 'high') {
        instructions.push('TRENDS: Lead with trending topics to maximize engagement and relevance');
    } else {
        instructions.push('TRENDS: Use trends subtly to add contemporary relevance');
    }
    if (relevance.cultural.priority === 'high') {
        instructions.push('CULTURE: Deeply integrate local cultural elements for authentic connection');
    } else {
        instructions.push('CULTURE: Include respectful cultural awareness without overdoing it');
    }
    return instructions.join('\n');
}
}}),
"[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Human-Like Content Generation System
 * 
 * This module provides techniques to make AI-generated content
 * feel authentic, human, and engaging while avoiding AI detection.
 */ __turbopack_context__.s({
    "generateContentOptimization": (()=>generateContentOptimization),
    "generateHumanizationTechniques": (()=>generateHumanizationTechniques),
    "generateTrafficDrivingElements": (()=>generateTrafficDrivingElements)
});
function generateHumanizationTechniques(businessType, brandVoice, location) {
    const basePersonality = getPersonalityMarkers(brandVoice);
    const industryAuthenticity = getIndustryAuthenticity(businessType);
    const locationConversation = getLocationConversation(location);
    return {
        personality_markers: [
            ...basePersonality,
            'Use first-person perspective occasionally',
            'Include personal opinions and preferences',
            'Show vulnerability and learning moments',
            'Express genuine excitement about successes'
        ],
        authenticity_elements: [
            ...industryAuthenticity,
            'Share behind-the-scenes moments',
            'Admit mistakes and lessons learned',
            'Use specific details instead of generalities',
            'Reference real experiences and observations',
            'Include time-specific references (today, this morning, etc.)'
        ],
        conversational_patterns: [
            ...locationConversation,
            'Start sentences with "You know what?"',
            'Use rhetorical questions naturally',
            'Include conversational fillers like "honestly" or "actually"',
            'Break up long thoughts with shorter sentences',
            'Use contractions (we\'re, don\'t, can\'t) naturally'
        ],
        storytelling_devices: [
            'Start with "I remember when..." or "Last week..."',
            'Use the "But here\'s the thing..." transition',
            'Include dialogue: "My customer said..."',
            'Paint vivid scenes with sensory details',
            'End with unexpected insights or realizations'
        ],
        emotional_connectors: [
            'Share moments of doubt and breakthrough',
            'Express genuine gratitude to customers',
            'Show empathy for customer struggles',
            'Celebrate small wins with enthusiasm',
            'Use emotional language that resonates'
        ],
        imperfection_markers: [
            'Occasional typos that feel natural (but not distracting)',
            'Slightly informal grammar in casual contexts',
            'Stream-of-consciousness moments',
            'Self-corrections: "Actually, let me rephrase that..."',
            'Honest admissions: "I\'m still figuring this out..."'
        ]
    };
}
function generateTrafficDrivingElements(businessType, platform, targetAudience) {
    return {
        viral_hooks: [
            'Controversial but respectful opinions',
            'Surprising industry statistics',
            'Before/after transformations',
            'Myth-busting content',
            'Exclusive behind-the-scenes reveals',
            'Timely reactions to trending topics',
            'Unexpected collaborations or partnerships'
        ],
        engagement_magnets: [
            'Fill-in-the-blank questions',
            'This or that choices',
            'Caption this photo challenges',
            'Share your experience prompts',
            'Prediction requests',
            'Opinion polls and surveys',
            'Challenge participation invites'
        ],
        conversion_triggers: [
            'Limited-time offers with urgency',
            'Exclusive access for followers',
            'Free valuable resources',
            'Personal consultation offers',
            'Early bird opportunities',
            'Member-only benefits',
            'Referral incentives'
        ],
        shareability_factors: [
            'Relatable everyday struggles',
            'Inspirational success stories',
            'Useful tips people want to save',
            'Funny observations about the industry',
            'Heartwarming customer stories',
            'Educational content that teaches',
            'Content that makes people look smart for sharing'
        ],
        curiosity_gaps: [
            'The one thing nobody tells you about...',
            'What happened next will surprise you...',
            'The secret that changed everything...',
            'Why everyone is wrong about...',
            'The mistake I made that taught me...',
            'What I wish I knew before...',
            'The truth about... that nobody talks about'
        ],
        social_proof_elements: [
            'Customer testimonials and reviews',
            'User-generated content features',
            'Industry recognition and awards',
            'Media mentions and press coverage',
            'Collaboration with respected figures',
            'Community size and engagement stats',
            'Success metrics and achievements'
        ]
    };
}
/**
 * Gets personality markers based on brand voice
 */ function getPersonalityMarkers(brandVoice) {
    const voiceMap = {
        'friendly': [
            'Use warm, welcoming language',
            'Include friendly greetings and sign-offs',
            'Show genuine interest in followers',
            'Use inclusive language that brings people together'
        ],
        'professional': [
            'Maintain expertise while being approachable',
            'Use industry knowledge to build authority',
            'Balance formal tone with personal touches',
            'Show competence through specific examples'
        ],
        'casual': [
            'Use everyday language and slang appropriately',
            'Be relaxed and conversational',
            'Include humor and light-hearted moments',
            'Feel like talking to a friend'
        ],
        'innovative': [
            'Show forward-thinking perspectives',
            'Challenge conventional wisdom respectfully',
            'Share cutting-edge insights',
            'Express excitement about new possibilities'
        ]
    };
    // Extract key words from brand voice description
    const lowerVoice = brandVoice.toLowerCase();
    for (const [key, markers] of Object.entries(voiceMap)){
        if (lowerVoice.includes(key)) {
            return markers;
        }
    }
    return voiceMap['friendly']; // Default fallback
}
/**
 * Gets industry-specific authenticity elements
 */ function getIndustryAuthenticity(businessType) {
    const industryMap = {
        'restaurant': [
            'Share cooking failures and successes',
            'Talk about ingredient sourcing stories',
            'Mention customer reactions and feedback',
            'Describe the sensory experience of food'
        ],
        'fitness': [
            'Share personal workout struggles',
            'Admit to having off days',
            'Celebrate client progress genuinely',
            'Talk about the mental health benefits'
        ],
        'technology': [
            'Explain complex concepts simply',
            'Share debugging stories and solutions',
            'Admit when technology isn\'t perfect',
            'Focus on human impact of technology'
        ],
        'financial technology software': [
            'Share stories about financial inclusion impact',
            'Explain complex financial concepts simply',
            'Highlight real customer success stories',
            'Address common financial fears and concerns',
            'Show the human side of financial technology'
        ],
        'beauty': [
            'Share makeup fails and learning moments',
            'Talk about skin struggles and solutions',
            'Celebrate diverse beauty standards',
            'Share product testing experiences'
        ]
    };
    return industryMap[businessType.toLowerCase()] || [
        'Share real customer interactions',
        'Talk about daily business challenges',
        'Celebrate small business wins',
        'Show the human side of your industry'
    ];
}
/**
 * Gets location-specific conversational patterns
 */ function getLocationConversation(location) {
    const locationMap = {
        'nairobi': [
            'Use occasional Swahili phrases naturally',
            'Reference local landmarks and experiences',
            'Include community-focused language',
            'Show respect for local customs and values'
        ],
        'new york': [
            'Use direct, fast-paced communication',
            'Reference city experiences and culture',
            'Include diverse perspectives',
            'Show hustle and ambition'
        ],
        'london': [
            'Use British expressions naturally',
            'Include dry humor appropriately',
            'Reference local culture and experiences',
            'Maintain polite but direct communication'
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [
        'Use local expressions and references',
        'Include regional cultural touchpoints',
        'Show understanding of local context',
        'Connect with community values'
    ];
}
function generateContentOptimization(platform, businessType, timeOfDay = 'morning') {
    const platformStrategies = {
        'instagram': {
            posting_strategy: [
                'Use high-quality visuals as primary hook',
                'Write captions that encourage saves and shares',
                'Include clear call-to-actions in stories',
                'Use relevant hashtags strategically'
            ],
            engagement_timing: [
                'Post when your audience is most active',
                'Respond to comments within first hour',
                'Use stories for real-time engagement',
                'Go live during peak audience times'
            ]
        },
        'linkedin': {
            posting_strategy: [
                'Lead with valuable insights or questions',
                'Use professional but personal tone',
                'Include industry-relevant hashtags',
                'Share thought leadership content'
            ],
            engagement_timing: [
                'Post during business hours for B2B',
                'Engage with comments professionally',
                'Share in relevant LinkedIn groups',
                'Connect with commenters personally'
            ]
        },
        'twitter': {
            posting_strategy: [
                'Use trending hashtags when relevant',
                'Create tweetable quotes and insights',
                'Engage in real-time conversations',
                'Share quick tips and observations'
            ],
            engagement_timing: [
                'Tweet during peak conversation times',
                'Respond quickly to mentions',
                'Join trending conversations',
                'Retweet with thoughtful comments'
            ]
        },
        'facebook': {
            posting_strategy: [
                'Create community-focused content',
                'Use longer-form storytelling',
                'Encourage group discussions',
                'Share local community content'
            ],
            engagement_timing: [
                'Post when your community is online',
                'Respond to all comments personally',
                'Share in relevant Facebook groups',
                'Use Facebook events for promotion'
            ]
        }
    };
    const strategy = platformStrategies[platform.toLowerCase()] || platformStrategies['instagram'];
    return {
        ...strategy,
        content_mix: [
            '60% educational/valuable content',
            '20% behind-the-scenes/personal',
            '15% promotional/business',
            '5% trending/entertainment'
        ],
        performance_indicators: [
            'Comments and meaningful engagement',
            'Saves and shares over likes',
            'Profile visits and follows',
            'Website clicks and conversions',
            'Direct messages and inquiries'
        ]
    };
}
}}),
"[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Trends Integration System
 * 
 * Keeps design generation current with latest visual trends and best practices
 */ __turbopack_context__.s({
    "DesignTrendsSchema": (()=>DesignTrendsSchema),
    "generateTrendInstructions": (()=>generateTrendInstructions),
    "getCachedDesignTrends": (()=>getCachedDesignTrends),
    "getCurrentDesignTrends": (()=>getCurrentDesignTrends)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignTrendsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    currentTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Name of the design trend'),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Description of the trend'),
        applicability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('How applicable this trend is to the business type'),
        implementation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to implement this trend in the design'),
        examples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Visual examples or descriptions of the trend')
    })).describe('Current relevant design trends'),
    colorTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        palette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending color palette in hex format'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood of trending colors'),
        application: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to apply these colors effectively')
    }),
    typographyTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        styles: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending typography styles'),
        pairings: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Popular font pairings'),
        treatments: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Special text treatments and effects')
    }),
    layoutTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        compositions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending layout compositions'),
        spacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Current spacing and whitespace trends'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Visual hierarchy trends')
    }),
    platformSpecific: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Instagram-specific design trends'),
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Facebook-specific design trends'),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Twitter/X-specific design trends'),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('LinkedIn-specific design trends')
    })
});
// Design trends analysis prompt
const designTrendsPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignTrends',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            industry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignTrendsSchema
    },
    prompt: `You are a leading design trend analyst with deep knowledge of current visual design trends, social media best practices, and industry-specific design patterns.

Analyze and provide current design trends relevant to:
- Business Type: {{businessType}}
- Platform: {{platform}}
- Target Audience: {{targetAudience}}
- Industry: {{industry}}

Focus on trends that are:
1. Currently popular and effective (2024-2025)
2. Relevant to the specific business type and platform
3. Proven to drive engagement and conversions
4. Accessible and implementable in AI-generated designs

Provide specific, actionable trend insights that can be directly applied to design generation.`
});
async function getCurrentDesignTrends(businessType, platform, targetAudience, industry) {
    try {
        // For now, return fallback trends to avoid API issues
        // This provides current, relevant trends while the system is being tested
        return getFallbackTrends(businessType, platform);
    } catch (error) {
        // Return fallback trends
        return getFallbackTrends(businessType, platform);
    }
}
function generateTrendInstructions(trends, platform) {
    const platformTrends = trends.platformSpecific[platform] || [];
    const highApplicabilityTrends = trends.currentTrends.filter((t)=>t.applicability === 'high');
    return `
**CURRENT DESIGN TRENDS INTEGRATION:**

**High-Priority Trends to Incorporate:**
${highApplicabilityTrends.map((trend)=>`
- **${trend.name}**: ${trend.description}
  Implementation: ${trend.implementation}`).join('\n')}

**Color Trends:**
- Trending Palette: ${trends.colorTrends.palette.join(', ')}
- Mood: ${trends.colorTrends.mood}
- Application: ${trends.colorTrends.application}

**Typography Trends:**
- Styles: ${trends.typographyTrends.styles.join(', ')}
- Popular Pairings: ${trends.typographyTrends.pairings.join(', ')}
- Special Treatments: ${trends.typographyTrends.treatments.join(', ')}

**Layout Trends:**
- Compositions: ${trends.layoutTrends.compositions.join(', ')}
- Spacing: ${trends.layoutTrends.spacing}
- Hierarchy: ${trends.layoutTrends.hierarchy}

**Platform-Specific Trends (${platform}):**
${platformTrends.map((trend)=>`- ${trend}`).join('\n')}

**TREND APPLICATION GUIDELINES:**
- Incorporate 2-3 relevant trends maximum to avoid overwhelming the design
- Ensure trends align with brand personality and business goals
- Prioritize trends that enhance readability and user experience
- Balance trendy elements with timeless design principles
`;
}
/**
 * Fallback trends when API fails
 */ function getFallbackTrends(businessType, platform) {
    return {
        currentTrends: [
            {
                name: "Bold Typography",
                description: "Large, impactful typography that commands attention",
                applicability: "high",
                implementation: "Use oversized headlines with strong contrast",
                examples: [
                    "Large sans-serif headers",
                    "Bold statement text",
                    "Typography as hero element"
                ]
            },
            {
                name: "Minimalist Design",
                description: "Clean, uncluttered designs with plenty of white space",
                applicability: "high",
                implementation: "Focus on essential elements, generous spacing, simple color palette",
                examples: [
                    "Clean layouts",
                    "Minimal color schemes",
                    "Focused messaging"
                ]
            },
            {
                name: "Authentic Photography",
                description: "Real, unposed photography over stock imagery",
                applicability: "medium",
                implementation: "Use candid, lifestyle photography that feels genuine",
                examples: [
                    "Behind-the-scenes shots",
                    "Real customer photos",
                    "Lifestyle imagery"
                ]
            }
        ],
        colorTrends: {
            palette: [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7"
            ],
            mood: "Vibrant yet calming, optimistic and approachable",
            application: "Use as accent colors against neutral backgrounds for maximum impact"
        },
        typographyTrends: {
            styles: [
                "Bold sans-serif",
                "Modern serif",
                "Custom lettering"
            ],
            pairings: [
                "Bold header + clean body",
                "Serif headline + sans-serif body"
            ],
            treatments: [
                "Gradient text",
                "Outlined text",
                "Text with shadows"
            ]
        },
        layoutTrends: {
            compositions: [
                "Asymmetrical balance",
                "Grid-based layouts",
                "Centered focal points"
            ],
            spacing: "Generous white space with intentional breathing room",
            hierarchy: "Clear size differentiation with strong contrast"
        },
        platformSpecific: {
            instagram: [
                "Square and vertical formats",
                "Story-friendly designs",
                "Carousel-optimized layouts"
            ],
            facebook: [
                "Horizontal emphasis",
                "Video-first approach",
                "Community-focused messaging"
            ],
            twitter: [
                "High contrast for timeline",
                "Text-heavy designs",
                "Trending hashtag integration"
            ],
            linkedin: [
                "Professional aesthetics",
                "Data visualization",
                "Thought leadership focus"
            ]
        }
    };
}
/**
 * Caches trends to avoid excessive API calls
 * Reduced cache duration and added randomization to prevent repetitive designs
 */ const trendsCache = new Map();
const CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours (reduced from 24 hours)
const MAX_USAGE_COUNT = 5; // Force refresh after 5 uses to add variety
async function getCachedDesignTrends(businessType, platform, targetAudience, industry) {
    // Add randomization to cache key to create more variety
    const hourOfDay = new Date().getHours();
    const randomSeed = Math.floor(hourOfDay / 2); // Changes every 2 hours
    const cacheKey = `${businessType}-${platform}-${targetAudience}-${industry}-${randomSeed}`;
    const cached = trendsCache.get(cacheKey);
    // Check if cache is valid and not overused
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION && cached.usageCount < MAX_USAGE_COUNT) {
        cached.usageCount++;
        return cached.trends;
    }
    const trends = await getCurrentDesignTrends(businessType, platform, targetAudience, industry);
    trendsCache.set(cacheKey, {
        trends,
        timestamp: Date.now(),
        usageCount: 1
    });
    return trends;
}
}}),
"[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Performance Analytics System
 * 
 * Tracks design performance, learns from successful patterns, and optimizes future generations
 */ __turbopack_context__.s({
    "DesignPerformanceSchema": (()=>DesignPerformanceSchema),
    "exportAnalyticsData": (()=>exportAnalyticsData),
    "generatePerformanceOptimizedInstructions": (()=>generatePerformanceOptimizedInstructions),
    "getPerformanceInsights": (()=>getPerformanceInsights),
    "getTopPerformingDesigns": (()=>getTopPerformingDesigns),
    "recordDesignGeneration": (()=>recordDesignGeneration),
    "updateDesignPerformance": (()=>updateDesignPerformance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const DesignPerformanceSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    designId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    generatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].date(),
    metrics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        qualityScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        engagementPrediction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        brandAlignmentScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        trendRelevance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10)
    }),
    designElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        businessDNA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }),
    performance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        actualEngagement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        clickThroughRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        conversionRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        brandRecall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        userFeedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(5).optional()
    }).optional(),
    improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional()
});
// In-memory storage for design analytics (in production, use a database)
const designAnalytics = new Map();
const performancePatterns = new Map();
function recordDesignGeneration(designId, businessType, platform, visualStyle, qualityScore, designElements, predictions) {
    const record = {
        designId,
        businessType,
        platform,
        visualStyle,
        generatedAt: new Date(),
        metrics: {
            qualityScore,
            engagementPrediction: predictions.engagement,
            brandAlignmentScore: predictions.brandAlignment,
            technicalQuality: predictions.technicalQuality,
            trendRelevance: predictions.trendRelevance
        },
        designElements,
        tags: [
            businessType,
            platform,
            visualStyle
        ]
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
function updateDesignPerformance(designId, actualMetrics) {
    const record = designAnalytics.get(designId);
    if (!record) return;
    record.performance = {
        ...record.performance,
        ...actualMetrics
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
/**
 * Analyzes performance patterns to improve future designs
 */ function updatePerformancePatterns(record) {
    const patternKey = `${record.businessType}-${record.platform}-${record.visualStyle}`;
    if (!performancePatterns.has(patternKey)) {
        performancePatterns.set(patternKey, {
            count: 0,
            avgQuality: 0,
            avgEngagement: 0,
            successfulElements: new Map(),
            commonIssues: new Map(),
            bestPractices: []
        });
    }
    const pattern = performancePatterns.get(patternKey);
    pattern.count += 1;
    // Update averages
    pattern.avgQuality = (pattern.avgQuality * (pattern.count - 1) + record.metrics.qualityScore) / pattern.count;
    pattern.avgEngagement = (pattern.avgEngagement * (pattern.count - 1) + record.metrics.engagementPrediction) / pattern.count;
    // Track successful elements
    if (record.metrics.qualityScore >= 8) {
        record.designElements.trends.forEach((trend)=>{
            const count = pattern.successfulElements.get(trend) || 0;
            pattern.successfulElements.set(trend, count + 1);
        });
    }
    // Track common issues
    if (record.improvements) {
        record.improvements.forEach((issue)=>{
            const count = pattern.commonIssues.get(issue) || 0;
            pattern.commonIssues.set(issue, count + 1);
        });
    }
    performancePatterns.set(patternKey, pattern);
}
function getPerformanceInsights(businessType, platform, visualStyle) {
    const patternKey = visualStyle ? `${businessType}-${platform}-${visualStyle}` : `${businessType}-${platform}`;
    const pattern = performancePatterns.get(patternKey);
    if (!pattern) {
        return {
            averageQuality: 0,
            averageEngagement: 0,
            topSuccessfulElements: [],
            commonIssues: [],
            recommendations: [
                'Insufficient data for insights'
            ],
            sampleSize: 0
        };
    }
    // Get top successful elements
    const topElements = Array.from(pattern.successfulElements.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([element])=>element);
    // Get common issues
    const topIssues = Array.from(pattern.commonIssues.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 3).map(([issue])=>issue);
    // Generate recommendations
    const recommendations = generateRecommendations(pattern, topElements, topIssues);
    return {
        averageQuality: Math.round(pattern.avgQuality * 10) / 10,
        averageEngagement: Math.round(pattern.avgEngagement * 10) / 10,
        topSuccessfulElements: topElements,
        commonIssues: topIssues,
        recommendations,
        sampleSize: pattern.count
    };
}
/**
 * Generates actionable recommendations based on performance data
 */ function generateRecommendations(pattern, successfulElements, commonIssues) {
    const recommendations = [];
    // Quality-based recommendations
    if (pattern.avgQuality < 7) {
        recommendations.push('Focus on improving overall design quality through better composition and typography');
    }
    // Engagement-based recommendations
    if (pattern.avgEngagement < 7) {
        recommendations.push('Incorporate more attention-grabbing elements and bold visual choices');
    }
    // Element-based recommendations
    if (successfulElements.length > 0) {
        recommendations.push(`Continue using successful elements: ${successfulElements.slice(0, 3).join(', ')}`);
    }
    // Issue-based recommendations
    if (commonIssues.length > 0) {
        recommendations.push(`Address common issues: ${commonIssues.slice(0, 2).join(', ')}`);
    }
    // Sample size recommendations
    if (pattern.count < 10) {
        recommendations.push('Generate more designs to improve insights accuracy');
    }
    return recommendations;
}
function getTopPerformingDesigns(businessType, platform, limit = 10) {
    let designs = Array.from(designAnalytics.values());
    // Filter by business type and platform if specified
    if (businessType) {
        designs = designs.filter((d)=>d.businessType === businessType);
    }
    if (platform) {
        designs = designs.filter((d)=>d.platform === platform);
    }
    // Sort by quality score and engagement prediction
    designs.sort((a, b)=>{
        const scoreA = (a.metrics.qualityScore + a.metrics.engagementPrediction) / 2;
        const scoreB = (b.metrics.qualityScore + b.metrics.engagementPrediction) / 2;
        return scoreB - scoreA;
    });
    return designs.slice(0, limit);
}
function generatePerformanceOptimizedInstructions(businessType, platform, visualStyle) {
    const insights = getPerformanceInsights(businessType, platform, visualStyle);
    if (insights.sampleSize === 0) {
        return ''; // No data available
    }
    let instructions = `\n**PERFORMANCE-OPTIMIZED DESIGN INSTRUCTIONS:**\n`;
    if (insights.topSuccessfulElements.length > 0) {
        instructions += `**Proven Successful Elements (${insights.sampleSize} designs analyzed):**\n`;
        insights.topSuccessfulElements.forEach((element)=>{
            instructions += `- Incorporate: ${element}\n`;
        });
    }
    if (insights.commonIssues.length > 0) {
        instructions += `\n**Avoid Common Issues:**\n`;
        insights.commonIssues.forEach((issue)=>{
            instructions += `- Prevent: ${issue}\n`;
        });
    }
    if (insights.recommendations.length > 0) {
        instructions += `\n**Performance Recommendations:**\n`;
        insights.recommendations.forEach((rec)=>{
            instructions += `- ${rec}\n`;
        });
    }
    instructions += `\n**Performance Benchmarks:**\n`;
    instructions += `- Target Quality Score: ${Math.max(insights.averageQuality + 0.5, 8)}/10\n`;
    instructions += `- Target Engagement: ${Math.max(insights.averageEngagement + 0.5, 8)}/10\n`;
    return instructions;
}
function exportAnalyticsData() {
    const designs = Array.from(designAnalytics.values());
    const patterns = Array.from(performancePatterns.entries()).map(([key, data])=>({
            key,
            data
        }));
    // Calculate summary statistics
    const totalDesigns = designs.length;
    const averageQuality = designs.reduce((sum, d)=>sum + d.metrics.qualityScore, 0) / totalDesigns;
    const businessTypeCounts = designs.reduce((acc, d)=>{
        acc[d.businessType] = (acc[d.businessType] || 0) + 1;
        return acc;
    }, {});
    const platformCounts = designs.reduce((acc, d)=>{
        acc[d.platform] = (acc[d.platform] || 0) + 1;
        return acc;
    }, {});
    const topBusinessTypes = Object.entries(businessTypeCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([type])=>type);
    const topPlatforms = Object.entries(platformCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([platform])=>platform);
    return {
        designs,
        patterns,
        summary: {
            totalDesigns,
            averageQuality: Math.round(averageQuality * 10) / 10,
            topBusinessTypes,
            topPlatforms
        }
    };
}
}}),
"[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40fa5f6cc8ddd662f9b3af59ed25e5677c98c250e5":"generatePostFromProfile","7f066f1a1f04ec45ead6bae4889653f4f2c8ba9635":"generatePostFromProfileFlow"},"",""] */ __turbopack_context__.s({
    "generatePostFromProfile": (()=>generatePostFromProfile),
    "generatePostFromProfileFlow": (()=>generatePostFromProfileFlow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview This file defines a Genkit flow for generating a daily social media post.
 *
 * It takes into account business type, location, brand voice, current weather, and local events to create engaging content.
 * @exports generatePostFromProfile - The main function to generate a post.
 * @exports GeneratePostFromProfileInput - The input type for the generation flow.
 * @exports GeneratePostFromProfileOutput - The output type for the generation flow.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/viral-hashtag-engine.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)");
// Enhanced design system temporarily disabled - will be re-enabled after module resolution
// import { generateEnhancedDesignPrompt, generateDesignEnhancements, validateDesignQuality } from '@/ai/utils/enhanced-design-generator';
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
// Clean design system implemented inline for immediate use
// 7 Different Design Template Styles for Variety - STRONG VISUAL DIFFERENTIATION
const DESIGN_TEMPLATES = [
    {
        name: "Motivational Quote",
        style: "WATERCOLOR BACKGROUND - NO ILLUSTRATIONS",
        description: "MANDATORY: Soft watercolor wash background in pastels (pink/blue/purple). NO illustrations, NO graphics, NO icons. Only elegant typography on watercolor texture.",
        elements: [
            "watercolor texture background",
            "script font headlines",
            "minimal text-only design"
        ],
        forbidden: [
            "illustrations",
            "graphics",
            "icons",
            "people",
            "objects",
            "geometric shapes"
        ]
    },
    {
        name: "Behind the Brand",
        style: "CUSTOM ILLUSTRATIONS ONLY",
        description: "MANDATORY: Hand-drawn style illustrations of business elements. Illustrated style with warm storytelling visuals.",
        elements: [
            "custom illustrations",
            "hand-drawn style",
            "storytelling visuals"
        ],
        forbidden: [
            "photos",
            "watercolor",
            "geometric shapes",
            "minimal design"
        ]
    },
    {
        name: "Engagement Post",
        style: "SPLIT PHOTO COLLAGE - NO ILLUSTRATIONS",
        description: "MANDATORY: Split screen layout with real photos on each side. 'This or That' style with bold text overlay. NO illustrations allowed.",
        elements: [
            "split screen layout",
            "real photographs",
            "comparison design",
            "bold overlay text"
        ],
        forbidden: [
            "illustrations",
            "watercolor",
            "single image",
            "minimal design"
        ]
    },
    {
        name: "Promotional Highlight",
        style: "BOLD TYPOGRAPHY FOCUS - MINIMAL GRAPHICS",
        description: "MANDATORY: Typography-driven design with clean background. Focus on text hierarchy and product showcase. Minimal geometric accents only.",
        elements: [
            "large typography",
            "text hierarchy",
            "clean background",
            "minimal geometric accents"
        ],
        forbidden: [
            "illustrations",
            "watercolor",
            "complex graphics",
            "busy backgrounds"
        ]
    },
    {
        name: "Fun/Trending",
        style: "MEME TEMPLATE - WHITE BACKGROUND",
        description: "MANDATORY: Clean white background with meme-style text placement. Simple, humorous layout with minimal visual elements.",
        elements: [
            "white background",
            "meme-style text",
            "simple layout",
            "humorous approach"
        ],
        forbidden: [
            "illustrations",
            "watercolor",
            "complex graphics",
            "busy designs",
            "multiple colors"
        ]
    },
    {
        name: "Customer Love",
        style: "POLAROID FRAME - RETRO PHOTO STYLE",
        description: "MANDATORY: Polaroid photo frame design with retro styling. Testimonial presentation with vintage photo aesthetic.",
        elements: [
            "polaroid frame",
            "retro photo style",
            "vintage aesthetic",
            "testimonial layout"
        ],
        forbidden: [
            "illustrations",
            "watercolor",
            "modern design",
            "minimal layout"
        ]
    },
    {
        name: "Creativity + Brand Values",
        style: "MIXED MEDIA ARTISTIC - WATERCOLOR + ELEMENTS",
        description: "MANDATORY: Watercolor splash background combined with artistic mixed media elements. Creative inspiration with artistic flair.",
        elements: [
            "watercolor splash",
            "mixed media",
            "artistic elements",
            "creative inspiration"
        ],
        forbidden: [
            "pure illustrations",
            "clean minimal",
            "geometric only",
            "photo-based"
        ]
    }
];
;
;
;
;
const GeneratePostFromProfileInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The type of business (e.g., restaurant, salon).'),
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The location of the business (city, state).'),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The visual style of the brand (e.g., modern, vintage).'),
    writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The brand voice of the business.'),
    contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The content themes of the business.'),
    logoDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe("The business logo as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."),
    designExamples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe("Array of design example data URIs to use as style reference for generating similar designs."),
    dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The day of the week for the post.'),
    currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The current date for the post.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    })).describe('An array of platform and aspect ratio variants to generate.'),
    primaryColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The primary brand color in HSL format.'),
    accentColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The accent brand color in HSL format.'),
    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The background brand color in HSL format.'),
    // New detailed fields for richer content
    services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key services or products.'),
    targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A description of the target audience.'),
    keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key features or selling points.'),
    competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of competitive advantages.'),
    // Brand consistency preferences
    brandConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        strictConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional(),
        followBrandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional()
    }).optional().describe('Brand consistency preferences for content generation.'),
    // Enhanced brand context
    websiteUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The business website URL for additional context.'),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Detailed business description for better content context.'),
    contactInfo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
    }).optional().describe('Contact information for business context.'),
    socialMedia: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
    }).optional().describe('Social media handles for cross-platform consistency.'),
    // Language preferences
    useLocalLanguage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional().describe('Whether to use local language in content generation (default: false).')
});
const GeneratePostFromProfileOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
    catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
    subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
    callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
    hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
    contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
        approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
        rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
    })).optional().describe('Alternative caption variants for A/B testing.'),
    hashtagAnalysis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending hashtags for reach.'),
        niche: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Industry-specific hashtags.'),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Location-based hashtags.'),
        community: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Community engagement hashtags.')
    }).optional().describe('Categorized hashtag strategy.'),
    marketIntelligence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending_topics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            topic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevanceScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            engagement_potential: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Current trending topics relevant to the business.'),
        competitor_insights: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            competitor_name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_gap: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            differentiation_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Competitor analysis and differentiation opportunities.'),
        cultural_context: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            cultural_nuances: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
            local_customs: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).describe('Cultural and location-specific context.'),
        viral_patterns: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Content patterns that drive viral engagement.'),
        engagement_triggers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Psychological triggers for maximum engagement.')
    }).optional().describe('Advanced market intelligence and optimization data.'),
    localContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        weather: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).optional().describe('Current weather context and business opportunities.'),
        events: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional().describe('Relevant local events for content integration.')
    }).optional().describe('Local context including weather and events.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }))
});
// Export function moved to end of file after flow definition
/**
 * Combines catchy words, subheadline, and call to action into a single text for image overlay
 */ function combineTextComponents(catchyWords, subheadline, callToAction) {
    const components = [
        catchyWords
    ];
    if (subheadline && subheadline.trim()) {
        components.push(subheadline.trim());
    }
    if (callToAction && callToAction.trim()) {
        components.push(callToAction.trim());
    }
    return components.join('\n');
}
// Define the enhanced text generation prompt
const enhancedTextGenPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'enhancedGeneratePostTextPrompt',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            contentVariation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            contextInstructions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            selectedWeather: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedEvents: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            selectedCultural: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].any().optional(),
            useLocalLanguage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional()
        })
    },
    output: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
            catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
            subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
            callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
            hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
            contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
                content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
                approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
                rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
            })).describe('2-3 alternative caption variants for A/B testing.')
        })
    },
    tools: [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEventsTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedEventsTool"]
    ],
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ADVANCED_AI_PROMPT"]
});
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
// Helper function to generate an image for a single variant with ENHANCED design principles
async function generateImageForVariant(variant, input, textOutput) {
    // TEMPORARILY DISABLED - Enhanced design system will be re-enabled after module resolution
    /*
  const enhancedDesignInput = {
    businessType: input.businessType,
    platform: variant.platform,
    visualStyle: input.visualStyle,
    primaryColor: input.primaryColor,
    accentColor: input.accentColor,
    backgroundColor: input.backgroundColor,
    imageText: textOutput.imageText,
    businessName: input.businessName,
    logoDataUrl: input.logoDataUrl,
    designExamples: input.designExamples,
    qualityLevel: 'premium' as const
  };

  const designEnhancements = generateDesignEnhancements(enhancedDesignInput);

  const enhancedPrompt = generateEnhancedDesignPrompt(enhancedDesignInput);
  */ // Determine consistency level based on preferences first
    const isStrictConsistency = input.brandConsistency?.strictConsistency ?? false;
    const followBrandColors = input.brandConsistency?.followBrandColors ?? true;
    // STRICT 3-color maximum with brand color enforcement
    const colorInstructions = followBrandColors ? `
  **MAXIMUM 3 COLORS ONLY - BRAND COLORS MANDATORY:**
  - Primary Color: ${input.primaryColor} - DOMINANT color (60-70% of design)
  - Accent Color: ${input.accentColor} - HIGHLIGHT color (20-30% of design)
  - Background Color: ${input.backgroundColor} - BASE color (10-20% of design)

  **ABSOLUTE COLOR LIMITS - NO EXCEPTIONS:**
  - MAXIMUM 3 colors total in entire design
  - ONLY use these exact 3 brand colors - NO additional colors
  - NO 4th, 5th, or more colors allowed
  - NO random colors, NO complementary colors, NO decorative colors
  - NO gradients using non-brand colors
  - Text: Use high contrast white or black only when needed
  - FORBIDDEN: Any design with more than 3 colors total
  ` : `
  **MAXIMUM 3 COLORS TOTAL:**
  - Primary: ${input.primaryColor} - DOMINANT (60-70%)
  - Accent: ${input.accentColor} - HIGHLIGHT (20-30%)
  - Background: ${input.backgroundColor} - BASE (10-20%)
  - ABSOLUTE LIMIT: 3 colors maximum in entire design
  `;
    // Get platform-specific guidelines
    const platformGuidelines = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"][variant.platform] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"].instagram;
    // Get business-specific design DNA
    const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][input.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
    // Get current design trends
    let trendInstructions = '';
    try {
        const trends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedDesignTrends"])(input.businessType, variant.platform, input.targetAudience, input.businessType);
        trendInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrendInstructions"])(trends, variant.platform);
    } catch (error) {}
    // Get performance-optimized instructions
    const performanceInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePerformanceOptimizedInstructions"])(input.businessType, variant.platform, input.visualStyle);
    // Enhanced brand context for better design generation
    const businessContext = `
  **BUSINESS PROFILE:**
  - Name: ${input.businessName || 'Business'}
  - Type: ${input.businessType}
  - Location: ${input.location}
  - Description: ${input.description || 'Professional business'}
  ${input.services ? `- Services: ${input.services.split('\n').slice(0, 3).join(', ')}` : ''}
  ${input.targetAudience ? `- Target Audience: ${input.targetAudience}` : ''}
  ${input.websiteUrl ? `- Website: ${input.websiteUrl}` : ''}
  `;
    // Select random design template for variety
    const selectedTemplate1 = DESIGN_TEMPLATES[Math.floor(Math.random() * DESIGN_TEMPLATES.length)];
    // Generate visual variation approach for diversity
    const visualVariations = [
        'minimalist_clean',
        'bold_dynamic',
        'elegant_sophisticated',
        'playful_creative',
        'modern_geometric',
        'organic_natural',
        'industrial_urban',
        'artistic_abstract',
        'photographic_realistic',
        'illustrated_stylized',
        'gradient_colorful',
        'monochrome_accent'
    ];
    const selectedVisualVariation = visualVariations[Math.floor(Math.random() * visualVariations.length)];
    // TEMPLATE-BASED DESIGN APPROACH - Using selected template style
    let imagePrompt = `Create a ${selectedTemplate1.style.toUpperCase()} social media design following the "${selectedTemplate1.name}" template approach.

**BUSINESS:** ${input.businessType}
**PLATFORM:** ${variant.platform}
**ASPECT RATIO:** ${variant.aspectRatio}
**MESSAGE:** "${combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction)}"

**MANDATORY TEMPLATE STYLE:** ${selectedTemplate1.name}
**MANDATORY TEMPLATE DESCRIPTION:** ${selectedTemplate1.description}
**REQUIRED ELEMENTS:** ${selectedTemplate1.elements.join(', ')}
**ABSOLUTELY FORBIDDEN:** ${selectedTemplate1.forbidden.join(', ')}

**CRITICAL: You MUST follow the template style exactly. Do NOT default to illustrations if the template specifies otherwise.**

**STRICT REQUIREMENTS - FOLLOW EXACTLY:**
- Use ONLY 3 visual elements maximum: logo, main text, one simple accent
- 50%+ of the design must be white/empty space
- Single, clean sans-serif font family only
- MAXIMUM 3 COLORS TOTAL in entire design
- NO LINES: no decorative lines, borders, dividers, or linear elements
- No decorative elements, shapes, or complex backgrounds
- High contrast for perfect readability
- Generous margins and spacing throughout
- One clear focal point only

**MAXIMUM 3 COLORS ONLY - BRAND COLORS:**
- Primary Color: ${input.primaryColor} (DOMINANT - 60-70% of design)
- Accent Color: ${input.accentColor} (HIGHLIGHTS - 20-30% of design)
- Background Color: ${input.backgroundColor} (BASE - 10-20% of design)
- ABSOLUTE LIMIT: These 3 colors only - NO 4th color allowed
- FORBIDDEN: Any design using more than 3 colors total
- FORBIDDEN: Additional colors, random colors, decorative colors

**MANDATORY DESIGN APPROACH - ${selectedTemplate1.name.toUpperCase()}:**
- MUST follow ${selectedTemplate1.style} aesthetic - NO EXCEPTIONS
- MUST incorporate: ${selectedTemplate1.elements.join(', ')}
- ABSOLUTELY FORBIDDEN: ${selectedTemplate1.forbidden.join(', ')}
- ${selectedTemplate1.description}
- DO NOT default to illustration style unless template specifically requires it
- Template requirements override all other design preferences

**FORBIDDEN ELEMENTS:**
❌ Multiple competing focal points
❌ Decorative shapes or ornaments
❌ Complex backgrounds or textures
❌ Multiple font families
❌ MORE THAN 3 COLORS TOTAL in the design
❌ Any 4th, 5th, or additional colors beyond the 3 brand colors
❌ Random colors, rainbow colors, complementary colors
❌ Gradients using non-brand colors
❌ ALL LINES: decorative lines, border lines, divider lines, underlines
❌ Frame lines, geometric lines, separator lines, outline borders
❌ Any linear elements or line-based decorations
❌ Cramped spacing
❌ Overlapping elements
❌ Busy compositions
❌ Multiple graphics or icons
❌ Patterns or textures

**MANDATORY SIMPLICITY:**
- Single clear message
- Generous white space (50%+ empty)
- Maximum 3 visual elements
- High contrast readability
- Professional, uncluttered appearance

**TEMPLATE ENFORCEMENT:**
- If template says "WATERCOLOR" → Use watercolor texture, NOT illustrations
- If template says "SPLIT PHOTO" → Use real photos split screen, NOT illustrations
- If template says "MEME TEMPLATE" → Use white background with simple text, NOT illustrations
- If template says "POLAROID" → Use retro photo frame style, NOT illustrations
- If template says "TYPOGRAPHY FOCUS" → Focus on text design, NOT illustrations

**FINAL INSTRUCTION:** Create a ${selectedTemplate1.style} design following the ${selectedTemplate1.name} template using MAXIMUM 3 COLORS ONLY and NO LINES. STRICTLY follow template requirements - do NOT default to illustration style. ABSOLUTE LIMITS: 3 colors maximum, NO lines, FOLLOW TEMPLATE EXACTLY - NO EXCEPTIONS.`;
    // Add brand colors section if colors are available
    if (input.brandProfile?.colors?.primary || input.brandProfile?.colors?.accent || input.brandProfile?.colors?.background) {
        const primaryColor = input.brandProfile.colors.primary || 'default';
        const accentColor = input.brandProfile.colors.accent || 'default';
        const backgroundColor = input.brandProfile.colors.background || 'default';
        imagePrompt += '\n - Brand Colors: The brand\'s color palette is: Primary HSL(' + primaryColor + '), Accent HSL(' + accentColor + '), Background HSL(' + backgroundColor + '). Please use these colors in the design.';
    }
    // Intelligent design examples processing
    let designDNA = '';
    let selectedExamples = [];
    if (input.designExamples && input.designExamples.length > 0) {
        try {
            // Analyze design examples for intelligent processing
            const analyses = [];
            for (const example of input.designExamples.slice(0, 5)){
                try {
                    const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, input.businessType, variant.platform, input.visualStyle + " design for " + textOutput.imageText);
                    analyses.push(analysis);
                } catch (error) {}
            }
            if (analyses.length > 0) {
                // Extract design DNA from analyzed examples
                designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                // Select optimal examples based on analysis
                selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(input.designExamples, analyses, textOutput.imageText, variant.platform, isStrictConsistency ? 3 : 1);
            } else {
                // Fallback to original logic if analysis fails
                selectedExamples = isStrictConsistency ? input.designExamples : [
                    input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
                ];
            }
        } catch (error) {
            selectedExamples = isStrictConsistency ? input.designExamples : [
                input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
            ];
        }
    }
    // Add design consistency instructions based on analysis
    if (isStrictConsistency) {
        imagePrompt += "\n ** STRICT STYLE REFERENCE:**\n" + "Use the provided design examples as strict style reference. Closely match the visual aesthetic, color scheme, typography, layout patterns, and overall design approach of the reference designs. Create content that looks very similar to the uploaded examples while incorporating the new text and subject matter.\n\n" + designDNA;
    } else {
        imagePrompt += "\n ** STYLE INSPIRATION:**\n" + "Use the provided design examples as loose inspiration for the overall aesthetic and mood, but feel free to create more varied and creative designs while maintaining the brand essence.\n\n" + designDNA;
    }
    // Add clean design consistency instruction
    imagePrompt += "\n\n** CLEAN DESIGN CONSISTENCY:** Maintain the same clean, minimal approach for all designs. Focus on clarity and simplicity rather than variation. Each design should feel part of a cohesive, professional brand family.\n\n" + "** SIMPLICITY REQUIREMENT:** Every design must prioritize:\n" + "- Single clear message\n" + "- Generous white space (50%+ empty)\n" + "- Maximum 3 visual elements\n" + "- High contrast readability\n" + "- Professional, uncluttered appearance\n\n" + "** GENERATION ID:** " + Date.now() + "_clean_minimal - Clean design approach";
    // Build prompt parts array - Enhanced system temporarily disabled
    const promptParts = [
        {
            text: imagePrompt
        }
    ];
    // Enhanced logo integration with analysis
    if (input.logoDataUrl) {
        // Add logo analysis instructions to the prompt
        const logoInstructions = "\n\n** CRITICAL LOGO USAGE REQUIREMENTS:**\n" + "🚨 MANDATORY: You MUST use the uploaded brand logo image provided below. DO NOT create, generate, or design a new logo.\n\n" + "** LOGO INTEGRATION REQUIREMENTS:**\n" + "- Use ONLY the provided logo image - never create or generate a new logo\n" + "- The uploaded logo is the official brand logo and must be used exactly as provided\n" + "- Incorporate the provided logo naturally and prominently into the design\n" + "- Ensure logo is clearly visible and properly sized for the platform (minimum 10% of design area)\n" + "- Maintain logo's original proportions and readability - do not distort or modify the logo\n" + "- Position logo strategically: " + (platformGuidelines.logoPlacement || 'Place logo prominently in corner or integrated into layout') + "\n" + "- Ensure sufficient contrast between logo and background (minimum 4.5:1 ratio)\n" + "- For " + variant.platform + ": Logo should be clearly visible and recognizable\n\n" + "** BRAND CONSISTENCY WITH UPLOADED LOGO:**\n" + "- Extract and use colors from the provided logo for the overall color scheme\n" + "- Match the design style to complement the logo's aesthetic and personality\n" + "- Ensure visual harmony between the uploaded logo and all design elements\n" + "- The logo is the primary brand identifier - treat it as the most important visual element";
        // Add additional logo placement instructions
        const logoPlacementInstructions = "\n\n** LOGO PLACEMENT PRIORITY:**\n" + "- Logo visibility is more important than other design elements\n" + "- If space is limited, reduce other elements to ensure logo prominence\n" + "- Logo should be one of the first things viewers notice in the design";
        // Update the main prompt with logo instructions
        promptParts[0].text += logoInstructions + logoPlacementInstructions;
        // Add logo as media with high priority
        promptParts.push({
            media: {
                url: input.logoDataUrl,
                contentType: getMimeTypeFromDataURI(input.logoDataUrl)
            }
        });
    } else {}
    // Add selected design examples
    selectedExamples.forEach((example)=>{
        promptParts.push({
            media: {
                url: example,
                contentType: getMimeTypeFromDataURI(example)
            }
        });
    });
    // Generate initial design
    let finalImageUrl = '';
    let attempts = 0;
    const maxAttempts = 2; // Limit attempts to avoid excessive API calls
    while(attempts < maxAttempts){
        attempts++;
        try {
            const { media } = await generateWithRetry({
                model: 'googleai/gemini-2.0-flash-preview-image-generation',
                prompt: promptParts,
                config: {
                    responseModalities: [
                        'TEXT',
                        'IMAGE'
                    ]
                }
            });
            let imageUrl = media?.url ?? '';
            if (!imageUrl) {
                throw new Error('No image generated');
            }
            // Apply aspect ratio correction for non-square platforms
            const { cropImageFromUrl, needsAspectRatioCorrection } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            if (needsAspectRatioCorrection(variant.platform)) {
                try {
                    imageUrl = await cropImageFromUrl(imageUrl, variant.platform);
                } catch (cropError) {
                // Continue with original image if cropping fails
                }
            }
            // ENHANCED Quality validation for first attempt
            if (attempts === 1) {
                try {
                    // Standard quality assessment (Enhanced validation temporarily disabled)
                    const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.businessType, variant.platform, input.visualStyle, followBrandColors && input.primaryColor ? colorInstructions : undefined, "Create engaging design for: " + textOutput.catchyWords);
                    // If quality is acceptable, use this design
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 7)) {
                        finalImageUrl = imageUrl;
                        break;
                    }
                    // If quality is poor and we have attempts left, try to improve
                    if (attempts < maxAttempts) {
                        // Add improvement instructions to prompt
                        const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                        const improvedPrompt = imagePrompt + "\n\n" + improvementInstructions;
                        promptParts[0] = {
                            text: improvedPrompt
                        };
                        continue;
                    } else {
                        // Use the design even if quality is subpar (better than nothing)
                        finalImageUrl = imageUrl;
                        break;
                    }
                } catch (qualityError) {
                    finalImageUrl = imageUrl;
                    break;
                }
            } else {
                // For subsequent attempts, use the result
                finalImageUrl = imageUrl;
                break;
            }
        } catch (error) {
            if (attempts === maxAttempts) {
                throw error;
            }
        }
    }
    // Record design generation for analytics
    if (finalImageUrl) {
        try {
            const designId = "design_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["recordDesignGeneration"])(designId, input.businessType, variant.platform, input.visualStyle, 9.2, {
                colorPalette: input.primaryColor ? [
                    input.primaryColor,
                    input.accentColor,
                    input.backgroundColor
                ].filter(Boolean) : [],
                typography: 'Modern social media optimized',
                composition: variant.aspectRatio,
                trends: selectedExamples.length > 0 ? [
                    'design-examples-based'
                ] : [
                    'ai-generated'
                ],
                businessDNA: businessDNA.substring(0, 100) // Truncate for storage
            }, {
                engagement: 8,
                brandAlignment: followBrandColors ? 9 : 7,
                technicalQuality: 8,
                trendRelevance: trendInstructions ? 8 : 6
            });
        } catch (analyticsError) {}
    }
    return {
        platform: variant.platform,
        imageUrl: finalImageUrl
    };
}
const generatePostFromProfileFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generatePostFromProfileFlow',
    inputSchema: GeneratePostFromProfileInputSchema,
    outputSchema: GeneratePostFromProfileOutputSchema
}, async (input)=>{
    // Determine the primary platform for optimization
    const primaryPlatform = input.variants[0]?.platform || 'instagram';
    // Generate unique content variation approach to ensure diversity
    const contentVariations = [
        'trending_hook',
        'story_driven',
        'educational_tip',
        'behind_scenes',
        'question_engagement',
        'statistic_driven',
        'personal_insight',
        'industry_contrarian',
        'local_cultural',
        'seasonal_relevance',
        'problem_solution',
        'inspiration_motivation'
    ];
    const selectedVariation = contentVariations[Math.floor(Math.random() * contentVariations.length)];
    // Step 1: Intelligent Context Analysis - Determine what information is relevant
    const contextRelevance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectRelevantContext"])(input.businessType, input.location, primaryPlatform, input.contentThemes, input.dayOfWeek);
    // Step 2: Fetch Real-Time Trending Topics (always useful)
    const realTimeTrends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRealTimeTrendingTopics"])(input.businessType, input.location, primaryPlatform);
    // Step 3: Fetch Local Context (Weather + Events) - but filter intelligently
    const rawLocalContext = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchLocalContext"])(input.location, input.businessType);
    // Step 4: Generate Market Intelligence for Advanced Content
    const marketIntelligence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateMarketIntelligence"])(input.businessType, input.location, primaryPlatform, input.services);
    // Step 5: Intelligently Filter Context Data
    const filteredContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filterContextData"])(contextRelevance, {
        weather: rawLocalContext.weather,
        events: rawLocalContext.events,
        trends: realTimeTrends,
        cultural: marketIntelligence.cultural_context
    });
    // Enhance market intelligence with filtered real-time trends
    marketIntelligence.trending_topics = [
        ...(filteredContext.selectedTrends || []).slice(0, 3),
        ...marketIntelligence.trending_topics.slice(0, 2)
    ];
    // Step 6: Generate Human-like Content Techniques
    const humanizationTechniques = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateHumanizationTechniques"])(input.businessType, input.writingTone, input.location);
    // Step 7: Generate Traffic-Driving Elements
    const trafficElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrafficDrivingElements"])(input.businessType, primaryPlatform, input.targetAudience);
    // Step 8: Generate Enhanced Text Content with Intelligent Context
    const { output: textOutput } = await enhancedTextGenPrompt({
        businessType: input.businessType,
        location: input.location,
        writingTone: input.writingTone,
        contentThemes: input.contentThemes,
        dayOfWeek: input.dayOfWeek,
        currentDate: input.currentDate,
        platform: primaryPlatform,
        services: input.services,
        targetAudience: input.targetAudience,
        keyFeatures: input.keyFeatures,
        competitiveAdvantages: input.competitiveAdvantages,
        // Add intelligent context instructions
        contextInstructions: filteredContext.contextInstructions,
        selectedWeather: filteredContext.selectedWeather,
        selectedEvents: filteredContext.selectedEvents,
        selectedTrends: filteredContext.selectedTrends,
        selectedCultural: filteredContext.selectedCultural,
        // Add content variation for diversity
        contentVariation: selectedVariation,
        // Template-specific content guidance
        designTemplate: selectedTemplate.name,
        templateStyle: selectedTemplate.style,
        templateDescription: selectedTemplate.description,
        // Language preferences
        useLocalLanguage: input.useLocalLanguage || false
    });
    if (!textOutput) {
        throw new Error('Failed to generate advanced AI post content.');
    }
    // 🚀 ENHANCED: Generate Strategic Hashtag Analysis using Advanced RSS-Integrated System
    const viralHashtagStrategy = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(input.businessType, input.businessName || input.businessType, input.location, primaryPlatform, input.services, input.targetAudience);
    // Step 10: Generate Image for each variant in parallel
    const imagePromises = input.variants.map((variant)=>generateImageForVariant(variant, input, textOutput));
    const variants = await Promise.all(imagePromises);
    // Step 11: Combine text components for image overlay
    const combinedImageText = combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction);
    // 🎯 ENHANCED: Use Advanced RSS-Integrated Hashtags (exactly 10 hashtags)
    const finalHashtags = viralHashtagStrategy.total.slice(0, 10); // Use the intelligently mixed hashtags
    // Step 13: Combine results with intelligently selected context
    return {
        content: textOutput.content,
        catchyWords: textOutput.catchyWords,
        subheadline: textOutput.subheadline,
        callToAction: textOutput.callToAction,
        hashtags: finalHashtags.join(' '),
        contentVariants: textOutput.contentVariants,
        hashtagAnalysis: {
            trending: viralHashtagStrategy.trending,
            viral: viralHashtagStrategy.viral,
            niche: viralHashtagStrategy.niche,
            location: viralHashtagStrategy.location,
            community: viralHashtagStrategy.community,
            platform: viralHashtagStrategy.platform,
            seasonal: viralHashtagStrategy.seasonal,
            analytics: viralHashtagStrategy.analytics // Include advanced analytics
        },
        // Advanced AI features metadata (for future UI display)
        marketIntelligence: {
            trending_topics: marketIntelligence.trending_topics.slice(0, 3),
            competitor_insights: marketIntelligence.competitor_insights.slice(0, 2),
            cultural_context: marketIntelligence.cultural_context,
            viral_patterns: marketIntelligence.viral_content_patterns.slice(0, 3),
            engagement_triggers: marketIntelligence.engagement_triggers.slice(0, 3)
        },
        // Intelligently selected local context
        localContext: {
            weather: filteredContext.selectedWeather,
            events: filteredContext.selectedEvents,
            contextRelevance: {
                weather: contextRelevance.weather.priority,
                events: contextRelevance.events.priority,
                weatherReason: contextRelevance.weather.relevanceReason,
                eventsReason: contextRelevance.events.relevanceReason
            }
        },
        variants
    };
});
async function generatePostFromProfile(input) {
    return generatePostFromProfileFlow(input);
}
;
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generatePostFromProfile,
    generatePostFromProfileFlow
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generatePostFromProfile, "40fa5f6cc8ddd662f9b3af59ed25e5677c98c250e5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generatePostFromProfileFlow, "7f066f1a1f04ec45ead6bae4889653f4f2c8ba9635", null);
}}),
"[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Content Generator
 * Enhanced content generation with advanced features
 */ __turbopack_context__.s({
    "Revo15ContentGenerator": (()=>Revo15ContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)");
;
class Revo15ContentGenerator {
    modelId = 'revo-1.5';
    /**
   * Generate enhanced content using Revo 1.5 specifications
   */ async generateContent(request) {
        const startTime = Date.now();
        try {
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid content generation request for Revo 1.5');
            }
            // Prepare enhanced generation parameters
            const generationParams = this.prepareEnhancedGenerationParams(request);
            // Generate content with enhanced features
            const postDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePostFromProfile"])(generationParams);
            // Create the enhanced generated post
            const generatedPost = {
                id: new Date().toISOString(),
                date: new Date().toISOString(),
                content: postDetails.content,
                hashtags: postDetails.hashtags,
                status: 'generated',
                variants: postDetails.variants,
                catchyWords: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                // Enhanced features for Revo 1.5
                contentVariants: postDetails.contentVariants,
                hashtagAnalysis: postDetails.hashtagAnalysis,
                marketIntelligence: postDetails.marketIntelligence,
                localContext: postDetails.localContext,
                metadata: {
                    modelId: this.modelId,
                    modelVersion: '1.5.0',
                    generationType: 'enhanced',
                    processingTime: Date.now() - startTime,
                    qualityLevel: 'enhanced',
                    enhancedFeatures: this.getAppliedEnhancements(request),
                    artifactsUsed: request.artifactIds?.length || 0
                }
            };
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateEnhancedQualityScore(generatedPost);
            return {
                success: true,
                data: generatedPost,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 2,
                    enhancementsApplied: [
                        'enhanced-ai-engine',
                        'real-time-context',
                        'trending-topics',
                        'advanced-prompting',
                        'quality-optimization',
                        ...request.artifactIds?.length ? [
                            'artifact-integration'
                        ] : []
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Validate content generation request for Revo 1.5
   */ validateRequest(request) {
        // Check required fields
        if (!request.profile || !request.platform) {
            return false;
        }
        // Check if profile has minimum required information
        if (!request.profile.businessType || !request.profile.businessName) {
            return false;
        }
        // Revo 1.5 supports artifacts - validate if provided
        if (request.artifactIds && request.artifactIds.length > 5) {}
        return true;
    }
    /**
   * Prepare enhanced generation parameters for Revo 1.5
   */ prepareEnhancedGenerationParams(request) {
        const { profile, platform, brandConsistency } = request;
        const today = new Date();
        // Enhanced parameter preparation with more sophisticated processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        return {
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: brandConsistency?.strictConsistency ? profile.designExamples || [] : [],
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek: today.toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: today.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            variants: [
                {
                    platform: platform,
                    aspectRatio: this.getOptimalAspectRatio(platform)
                }
            ],
            services: servicesString,
            targetAudience: profile.targetAudience,
            keyFeatures: keyFeaturesString,
            competitiveAdvantages: competitiveAdvantagesString,
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            },
            // Revo 1.5 enhanced features
            modelConstraints: {
                maxComplexity: 'enhanced',
                enhancedFeatures: true,
                realTimeContext: true,
                trendingTopics: true,
                artifactSupport: true,
                advancedPrompting: true,
                qualityLevel: 'enhanced'
            },
            // Artifact integration
            artifactIds: request.artifactIds?.slice(0, 5) || [],
            customInstructions: request.customInstructions
        };
    }
    /**
   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)
   */ getOptimalAspectRatio(platform) {
        switch(platform){
            case 'Instagram':
                return '1:1'; // Square for feed, can also do 9:16 for stories
            case 'Facebook':
                return '16:9'; // Landscape for better engagement
            case 'Twitter':
                return '16:9'; // Landscape works well
            case 'LinkedIn':
                return '16:9'; // Professional landscape format
            default:
                return '1:1';
        }
    }
    /**
   * Calculate enhanced quality score for generated content
   */ calculateEnhancedQualityScore(post) {
        let score = 6; // Higher base score for Revo 1.5
        // Content quality checks
        if (post.content && post.content.length > 50) score += 0.5;
        if (post.content && post.content.length > 150) score += 0.5;
        // Enhanced content features
        if (post.subheadline && post.subheadline.trim().length > 0) score += 0.5;
        if (post.callToAction && post.callToAction.trim().length > 0) score += 0.5;
        // Hashtag quality and analysis
        if (post.hashtags && post.hashtags.length >= 8) score += 0.5;
        if (post.hashtagAnalysis) score += 0.5;
        // Advanced features
        if (post.contentVariants && post.contentVariants.length > 0) score += 0.5;
        if (post.marketIntelligence) score += 0.5;
        if (post.localContext) score += 0.5;
        // Image generation success
        if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {
            score += 0.5;
        }
        // Cap at 10
        return Math.min(score, 10);
    }
    /**
   * Get applied enhancements for this generation
   */ getAppliedEnhancements(request) {
        const enhancements = [
            'enhanced-ai-engine',
            'advanced-prompting'
        ];
        if (request.artifactIds && request.artifactIds.length > 0) {
            enhancements.push('artifact-integration');
        }
        if (request.profile.location) {
            enhancements.push('local-context', 'real-time-context');
        }
        enhancements.push('trending-topics', 'quality-optimization', 'brand-consistency-advanced');
        return enhancements;
    }
    /**
   * Health check for enhanced content generator
   */ async healthCheck() {
        try {
            // Check if we can access enhanced AI services
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'content',
            capabilities: [
                'Enhanced content generation',
                'Real-time context integration',
                'Trending topics analysis',
                'Advanced brand consistency',
                'Artifact support (up to 5)',
                'Content variants generation',
                'Hashtag analysis',
                'Market intelligence',
                'Local context optimization'
            ],
            limitations: [
                'Higher credit cost (2x)',
                'Longer processing times',
                'Requires more system resources'
            ],
            averageProcessingTime: '20-30 seconds',
            qualityRange: '7-9/10',
            costPerGeneration: 2,
            enhancedFeatures: this.getEnhancedFeaturesList()
        };
    }
    getEnhancedFeaturesList() {
        return {
            realTimeContext: true,
            trendingTopics: true,
            artifactSupport: true,
            contentVariants: true,
            hashtagAnalysis: true,
            marketIntelligence: true,
            localOptimization: true,
            advancedPrompting: true,
            qualityOptimization: true
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Design Generator
 * Enhanced design generation with advanced features
 */ __turbopack_context__.s({
    "Revo15DesignGenerator": (()=>Revo15DesignGenerator)
});
class Revo15DesignGenerator {
    modelId = 'revo-1.5';
    /**
   * Generate enhanced design using Revo 1.5 specifications
   */ async generateDesign(request) {
        const startTime = Date.now();
        try {
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid design generation request for Revo 1.5');
            }
            // Generate enhanced design using Gemini 2.5 or fallback
            const designResult = await this.generateEnhancedDesign(request);
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateEnhancedQualityScore(designResult);
            return {
                success: true,
                data: designResult,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 2,
                    enhancementsApplied: [
                        'enhanced-ai-engine',
                        'advanced-styling',
                        'brand-consistency-advanced',
                        'multi-aspect-ratio',
                        'quality-optimization',
                        ...request.artifactInstructions ? [
                            'artifact-integration'
                        ] : []
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Generate enhanced design using Revo 1.5 two-step process
   */ async generateEnhancedDesign(request) {
        try {
            // Try Revo 1.5 enhanced two-step design generation first
            const { generateRevo15EnhancedDesign } = await __turbopack_context__.r("[project]/src/ai/revo-1.5-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                // Enhanced text combination for Revo 1.5
                const components = [
                    request.imageText.catchyWords
                ];
                if (request.imageText.subheadline) {
                    components.push(request.imageText.subheadline);
                }
                if (request.imageText.callToAction) {
                    components.push(request.imageText.callToAction);
                }
                imageText = components.join('\n');
            }
            // Generate enhanced design using two-step process
            const result = await generateRevo15EnhancedDesign({
                businessType: request.businessType,
                platform: request.platform,
                visualStyle: request.visualStyle,
                imageText,
                brandProfile: request.brandProfile,
                brandConsistency: request.brandConsistency,
                artifactInstructions: request.artifactInstructions,
                includePeopleInDesigns: true,
                useLocalLanguage: false
            });
            return {
                platform: request.platform,
                imageUrl: result.imageUrl,
                caption: imageText,
                hashtags: []
            };
        } catch (error) {
            // Fallback to original enhanced design
            return this.generateOriginalEnhancedDesign(request);
        }
    }
    /**
   * Original enhanced design generation (fallback for two-step process)
   */ async generateOriginalEnhancedDesign(request) {
        try {
            // Try original enhanced design generation
            const { generateEnhancedDesign } = await __turbopack_context__.r("[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                // Enhanced text combination for Revo 1.5
                const components = [
                    request.imageText.catchyWords
                ];
                if (request.imageText.subheadline) {
                    components.push(request.imageText.subheadline);
                }
                if (request.imageText.callToAction) {
                    components.push(request.imageText.callToAction);
                }
                imageText = components.join('\n');
            }
            // Generate enhanced design
            const result = await generateEnhancedDesign({
                businessType: request.businessType,
                platform: request.platform,
                visualStyle: request.visualStyle,
                imageText,
                brandProfile: request.brandProfile,
                brandConsistency: request.brandConsistency,
                artifactInstructions: request.artifactInstructions
            });
            return {
                platform: request.platform,
                imageUrl: result.imageUrl,
                caption: imageText,
                hashtags: []
            };
        } catch (error) {
            // Fallback to basic generation
            return this.generateFallbackDesign(request);
        }
    }
    /**
   * Basic fallback design generation
   */ async generateFallbackDesign(request) {
        try {
            const { generatePostFromProfile } = await __turbopack_context__.r("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                imageText = request.imageText.catchyWords;
                if (request.imageText.subheadline) {
                    imageText += '\n' + request.imageText.subheadline;
                }
            }
            // Create generation parameters
            const generationParams = {
                businessType: request.businessType,
                location: request.brandProfile.location || '',
                writingTone: request.brandProfile.writingTone || 'professional',
                contentThemes: request.brandProfile.contentThemes || '',
                visualStyle: request.visualStyle,
                logoDataUrl: request.brandProfile.logoDataUrl,
                designExamples: request.brandConsistency?.strictConsistency ? request.brandProfile.designExamples || [] : [],
                primaryColor: request.brandProfile.primaryColor,
                accentColor: request.brandProfile.accentColor,
                backgroundColor: request.brandProfile.backgroundColor,
                dayOfWeek: new Date().toLocaleDateString('en-US', {
                    weekday: 'long'
                }),
                currentDate: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }),
                variants: [
                    {
                        platform: request.platform,
                        aspectRatio: this.getOptimalAspectRatio(request.platform)
                    }
                ],
                services: '',
                targetAudience: request.brandProfile.targetAudience || '',
                keyFeatures: '',
                competitiveAdvantages: '',
                brandConsistency: request.brandConsistency || {
                    strictConsistency: false,
                    followBrandColors: true
                }
            };
            const result = await generatePostFromProfile(generationParams);
            if (result.variants && result.variants.length > 0) {
                return {
                    ...result.variants[0],
                    caption: imageText,
                    hashtags: result.hashtags || []
                };
            }
            // Final fallback
            return {
                platform: request.platform,
                imageUrl: '',
                caption: imageText,
                hashtags: []
            };
        } catch (error) {
            return {
                platform: request.platform,
                imageUrl: '',
                caption: typeof request.imageText === 'string' ? request.imageText : request.imageText.catchyWords,
                hashtags: []
            };
        }
    }
    /**
   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)
   */ getOptimalAspectRatio(platform) {
        switch(platform){
            case 'Instagram':
                return '1:1'; // Can also support 9:16 for stories
            case 'Facebook':
                return '16:9';
            case 'Twitter':
                return '16:9';
            case 'LinkedIn':
                return '16:9';
            default:
                return '1:1';
        }
    }
    /**
   * Validate design generation request for Revo 1.5
   */ validateRequest(request) {
        // Check required fields
        if (!request.businessType || !request.platform || !request.brandProfile) {
            return false;
        }
        // Check image text
        if (!request.imageText) {
            return false;
        }
        // Revo 1.5 supports multiple aspect ratios and artifacts
        return true;
    }
    /**
   * Calculate enhanced quality score for generated design
   */ calculateEnhancedQualityScore(variant) {
        let score = 6; // Higher base score for Revo 1.5
        // Image generation success
        if (variant.imageUrl && variant.imageUrl.length > 0) {
            score += 2;
        }
        // Caption quality
        if (variant.caption && variant.caption.length > 10) {
            score += 0.5;
        }
        if (variant.caption && variant.caption.length > 50) {
            score += 0.5;
        }
        // Hashtags presence and quality
        if (variant.hashtags && variant.hashtags.length > 0) {
            score += 0.5;
        }
        if (variant.hashtags && variant.hashtags.length >= 5) {
            score += 0.5;
        }
        // Platform optimization
        if (variant.platform) {
            score += 0.5;
        }
        // Revo 1.5 can achieve higher quality scores
        return Math.min(score, 9);
    }
    /**
   * Health check for enhanced design generator
   */ async healthCheck() {
        try {
            // Check if we can access enhanced AI services
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'design',
            capabilities: [
                'Two-step enhanced design process',
                'Gemini 2.5 Flash strategic planning',
                'Gemini 2.5 Flash Image Preview generation',
                'Premium image generation quality',
                'Multiple aspect ratios (1:1, 16:9, 9:16)',
                'Advanced brand integration',
                'Artifact support',
                'Superior text overlay',
                'Advanced color harmony',
                'Layout optimization',
                'Platform-specific optimization',
                'Strategic design planning',
                'Professional visual depth'
            ],
            limitations: [
                'Higher credit cost (2x)',
                'Longer processing times (two-step process)',
                'Requires more system resources',
                'Requires both Gemini models available'
            ],
            supportedPlatforms: [
                'Instagram',
                'Facebook',
                'Twitter',
                'LinkedIn'
            ],
            supportedAspectRatios: [
                '1:1',
                '16:9',
                '9:16'
            ],
            averageProcessingTime: '25-45 seconds',
            qualityRange: '8-9.8/10',
            costPerGeneration: 2,
            resolution: '1024x1024 to 2048x2048',
            enhancedFeatures: {
                twoStepProcess: true,
                strategicPlanning: true,
                premiumGeneration: true,
                multipleAspectRatios: true,
                artifactSupport: true,
                advancedStyling: true,
                brandConsistencyAdvanced: true,
                qualityOptimization: true,
                textOverlayAdvanced: true,
                gemini25FlashPlanning: true,
                gemini25FlashImagePreview: true
            },
            models: {
                planning: 'gemini-2.5-flash',
                generation: 'gemini-2.5-flash-image-preview'
            }
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.5 Model Implementation
 * Enhanced Model - Advanced Features
 */ __turbopack_context__.s({
    "Revo15Implementation": (()=>Revo15Implementation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
;
;
;
class Revo15Implementation {
    model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModelConfig"])('revo-1.5');
    contentGenerator;
    designGenerator;
    constructor(){
        this.contentGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15ContentGenerator"]();
        this.designGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15DesignGenerator"]();
    }
    /**
   * Check if the model is available and ready to use
   */ async isAvailable() {
        try {
            // Check if Gemini 2.5 (preferred) or fallback services are available
            const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
            // Revo 1.5 needs at least one advanced AI service
            return hasGeminiKey || hasOpenAIKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Validate a generation request for this model
   */ validateRequest(request) {
        try {
            // Basic validation
            if (!request || !request.modelId) {
                return false;
            }
            // Check if this is the correct model
            if (request.modelId !== 'revo-1.5') {
                return false;
            }
            // Content generation validation
            if ('profile' in request) {
                const contentRequest = request;
                return !!(contentRequest.profile && contentRequest.platform && contentRequest.profile.businessType);
            }
            // Design generation validation
            if ('businessType' in request) {
                const designRequest = request;
                return !!(designRequest.businessType && designRequest.platform && designRequest.visualStyle && designRequest.brandProfile);
            }
            return false;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get model-specific information
   */ getModelInfo() {
        return {
            id: this.model.id,
            name: this.model.name,
            version: this.model.version,
            description: this.model.description,
            status: this.model.status,
            capabilities: this.model.capabilities,
            pricing: this.model.pricing,
            features: this.model.features,
            strengths: [
                'Advanced AI engine with superior capabilities',
                'Enhanced content generation algorithms',
                'Superior quality control and consistency',
                'Professional design generation',
                'Improved brand integration',
                'Real-time context and trending topics',
                'Full artifact support',
                'Multiple aspect ratios'
            ],
            limitations: [
                'Higher credit cost than Revo 1.0',
                'Longer processing times',
                'No video generation (coming in 2.0)',
                'Requires more system resources'
            ],
            bestUseCases: [
                'Growing businesses',
                'Marketing agencies',
                'Content creators',
                'Professional brands',
                'Users wanting enhanced quality',
                'Artifact-based workflows',
                'Multi-platform campaigns'
            ]
        };
    }
    /**
   * Get performance metrics for this model
   */ async getPerformanceMetrics() {
        return {
            modelId: this.model.id,
            averageProcessingTime: 25000,
            successRate: 0.92,
            averageQualityScore: 8.1,
            costEfficiency: 'medium',
            reliability: 'very good',
            userSatisfaction: 4.4,
            lastUpdated: new Date().toISOString()
        };
    }
    /**
   * Health check for this specific model
   */ async healthCheck() {
        try {
            const isAvailable = await this.isAvailable();
            const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;
            const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;
            const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;
            return {
                healthy,
                details: {
                    modelAvailable: isAvailable,
                    contentGenerator: contentGeneratorHealthy,
                    designGenerator: designGeneratorHealthy,
                    enhancedFeaturesEnabled: true,
                    artifactSupportEnabled: true,
                    realTimeContextEnabled: true,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            return {
                healthy: false,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    /**
   * Get enhanced features specific to Revo 1.5
   */ getEnhancedFeatures() {
        return {
            artifactSupport: {
                enabled: true,
                supportedTypes: [
                    'image',
                    'text',
                    'reference'
                ],
                maxArtifacts: 5,
                features: [
                    'exact-use',
                    'reference',
                    'text-overlay'
                ]
            },
            realTimeContext: {
                enabled: true,
                features: [
                    'weather',
                    'events',
                    'trending-topics',
                    'local-optimization'
                ]
            },
            advancedDesign: {
                enabled: true,
                aspectRatios: [
                    '1:1',
                    '16:9',
                    '9:16'
                ],
                qualityEnhancements: [
                    'color-harmony',
                    'layout-optimization',
                    'brand-consistency'
                ],
                textOverlay: 'advanced'
            },
            contentEnhancements: {
                enabled: true,
                features: [
                    'content-variants',
                    'hashtag-analysis',
                    'market-intelligence'
                ],
                qualityLevel: 'enhanced'
            }
        };
    }
}
;
;
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo15ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15ContentGenerator"]),
    "Revo15DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo15DesignGenerator"]),
    "Revo15Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Revo15Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo15ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15ContentGenerator"]),
    "Revo15DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15DesignGenerator"]),
    "Revo15Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo15Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$5$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d3120ecf._.js.map