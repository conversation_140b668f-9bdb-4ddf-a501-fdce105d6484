module.exports = {

"[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__f2e5f150._.js",
  "server/chunks/node_modules_e9c30f59._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)");
    });
});
}}),

};