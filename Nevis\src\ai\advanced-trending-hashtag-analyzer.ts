/**
 * Advanced Trending Hashtag Analyzer
 * Analyzes RSS feeds and trending data to extract the most relevant hashtags
 * with sophisticated contextual understanding and business relevance scoring
 */

import { rssService, TrendingData, RSSArticle } from '../services/rss-feed-service';
import { trendingEnhancer } from './trending-content-enhancer';

export interface HashtagAnalysis {
  hashtag: string;
  relevanceScore: number;
  trendingScore: number;
  businessRelevance: number;
  platformOptimization: number;
  locationRelevance: number;
  engagementPotential: number;
  sources: string[];
  momentum: 'rising' | 'stable' | 'declining';
  category: 'trending' | 'viral' | 'niche' | 'location' | 'business' | 'seasonal';
}

export interface AdvancedHashtagStrategy {
  topTrending: HashtagAnalysis[];
  businessOptimized: HashtagAnalysis[];
  locationSpecific: HashtagAnalysis[];
  platformNative: HashtagAnalysis[];
  emergingTrends: HashtagAnalysis[];
  finalRecommendations: string[];
}

export interface AnalysisContext {
  businessType: string;
  businessName: string;
  location: string;
  platform: string;
  services?: string;
  targetAudience?: string;
  postContent?: string;
  industry?: string;
}

export class AdvancedTrendingHashtagAnalyzer {
  private cache: Map<string, { data: AdvancedHashtagStrategy; timestamp: number }> = new Map();
  private readonly cacheTimeout = 15 * 60 * 1000; // 15 minutes

  /**
   * Analyze trending data and generate advanced hashtag strategy
   */
  public async analyzeHashtagTrends(context: AnalysisContext): Promise<AdvancedHashtagStrategy> {
    const cacheKey = this.generateCacheKey(context);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    try {
      // Get comprehensive trending data
      const [trendingData, enhancementData] = await Promise.all([
        rssService.getTrendingData(),
        trendingEnhancer.getTrendingEnhancement({
          businessType: context.businessType,
          location: context.location,
          platform: context.platform,
          targetAudience: context.targetAudience
        })
      ]);

      // Extract and analyze hashtags from multiple sources
      const hashtagAnalyses = await this.extractAndAnalyzeHashtags(trendingData, enhancementData, context);

      // Categorize hashtags by type and relevance
      const strategy = this.categorizeHashtags(hashtagAnalyses, context);

      // Cache the results
      this.cache.set(cacheKey, { data: strategy, timestamp: Date.now() });

      return strategy;

    } catch (error) {
      return this.getFallbackStrategy(context);
    }
  }

  /**
   * Extract hashtags from RSS articles and trending data
   */
  private async extractAndAnalyzeHashtags(
    trendingData: TrendingData,
    enhancementData: any,
    context: AnalysisContext
  ): Promise<HashtagAnalysis[]> {
    const hashtagMap = new Map<string, HashtagAnalysis>();

    // Process RSS articles for hashtag extraction
    for (const article of trendingData.articles) {
      const extractedHashtags = this.extractHashtagsFromArticle(article, context);
      
      for (const hashtag of extractedHashtags) {
        if (hashtagMap.has(hashtag)) {
          const existing = hashtagMap.get(hashtag)!;
          existing.sources.push(article.source);
          existing.trendingScore += 1;
        } else {
          hashtagMap.set(hashtag, {
            hashtag,
            relevanceScore: this.calculateRelevanceScore(hashtag, context),
            trendingScore: 1,
            businessRelevance: this.calculateBusinessRelevance(hashtag, context),
            platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
            locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
            engagementPotential: this.calculateEngagementPotential(hashtag),
            sources: [article.source],
            momentum: this.calculateMomentum(hashtag, trendingData),
            category: this.categorizeHashtag(hashtag, context)
          });
        }
      }
    }

    // Add hashtags from trending enhancement data
    for (const hashtag of enhancementData.hashtags) {
      if (hashtagMap.has(hashtag)) {
        const existing = hashtagMap.get(hashtag)!;
        existing.trendingScore += 2; // Enhancement data gets higher weight
        existing.sources.push('trending_enhancer');
      } else {
        hashtagMap.set(hashtag, {
          hashtag,
          relevanceScore: this.calculateRelevanceScore(hashtag, context),
          trendingScore: 2,
          businessRelevance: this.calculateBusinessRelevance(hashtag, context),
          platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
          locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
          engagementPotential: this.calculateEngagementPotential(hashtag),
          sources: ['trending_enhancer'],
          momentum: 'rising',
          category: this.categorizeHashtag(hashtag, context)
        });
      }
    }

    // Add business-specific trending hashtags
    const businessHashtags = this.generateBusinessTrendingHashtags(context);
    for (const hashtag of businessHashtags) {
      if (!hashtagMap.has(hashtag)) {
        hashtagMap.set(hashtag, {
          hashtag,
          relevanceScore: this.calculateRelevanceScore(hashtag, context),
          trendingScore: 1,
          businessRelevance: 10, // High business relevance
          platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
          locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
          engagementPotential: this.calculateEngagementPotential(hashtag),
          sources: ['business_generator'],
          momentum: 'stable',
          category: 'business'
        });
      }
    }

    return Array.from(hashtagMap.values());
  }

  /**
   * Extract hashtags from article content
   */
  private extractHashtagsFromArticle(article: RSSArticle, context: AnalysisContext): string[] {
    const hashtags: string[] = [];
    const content = `${article.title} ${article.description}`.toLowerCase();

    // Extract existing hashtags
    const hashtagMatches = content.match(/#[a-zA-Z0-9_]+/g) || [];
    hashtags.push(...hashtagMatches);

    // Generate hashtags from keywords
    const keywords = article.keywords || [];
    for (const keyword of keywords) {
      const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
        hashtags.push(`#${cleanKeyword}`);
      }
    }

    // Generate contextual hashtags based on content relevance
    const contextualHashtags = this.generateContextualHashtags(content, context);
    hashtags.push(...contextualHashtags);

    return Array.from(new Set(hashtags));
  }

  /**
   * Generate contextual hashtags based on content analysis
   */
  private generateContextualHashtags(content: string, context: AnalysisContext): string[] {
    const hashtags: string[] = [];

    // Business type relevance
    if (content.includes(context.businessType.toLowerCase())) {
      hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
    }

    // Location relevance
    if (content.includes(context.location.toLowerCase())) {
      hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
    }

    // Industry keywords
    const industryKeywords = this.getIndustryKeywords(context.businessType);
    for (const keyword of industryKeywords) {
      if (content.includes(keyword.toLowerCase())) {
        hashtags.push(`#${keyword.replace(/\s+/g, '')}`);
      }
    }

    return hashtags;
  }

  /**
   * Calculate relevance score for a hashtag
   */
  private calculateRelevanceScore(hashtag: string, context: AnalysisContext): number {
    let score = 0;
    const hashtagLower = hashtag.toLowerCase();

    // Business type relevance
    if (hashtagLower.includes(context.businessType.toLowerCase())) score += 5;
    
    // Location relevance
    if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) score += 4;
    
    // Service relevance
    if (context.services) {
      const services = context.services.toLowerCase().split(/[,\s]+/);
      for (const service of services) {
        if (hashtagLower.includes(service)) score += 3;
      }
    }

    // Platform optimization
    score += this.calculatePlatformOptimization(hashtag, context.platform);

    return Math.min(score, 10); // Cap at 10
  }

  /**
   * Calculate business relevance score
   */
  private calculateBusinessRelevance(hashtag: string, context: AnalysisContext): number {
    let score = 0;
    const hashtagLower = hashtag.toLowerCase();

    // Direct business name match
    if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\s+/g, ''))) score += 10;
    
    // Business type match
    if (hashtagLower.includes(context.businessType.toLowerCase())) score += 8;
    
    // Industry keywords
    const industryKeywords = this.getIndustryKeywords(context.businessType);
    for (const keyword of industryKeywords) {
      if (hashtagLower.includes(keyword.toLowerCase())) score += 6;
    }

    return Math.min(score, 10);
  }

  /**
   * Calculate platform optimization score
   */
  private calculatePlatformOptimization(hashtag: string, platform: string): number {
    const platformHashtags = {
      instagram: ['instagood', 'photooftheday', 'instadaily', 'reels', 'igers'],
      facebook: ['community', 'local', 'share', 'connect', 'family'],
      twitter: ['news', 'update', 'discussion', 'trending', 'breaking'],
      linkedin: ['professional', 'business', 'career', 'networking', 'industry'],
      tiktok: ['fyp', 'viral', 'trending', 'foryou', 'dance'],
      pinterest: ['inspiration', 'ideas', 'diy', 'style', 'design']
    };

    const platformSpecific = platformHashtags[platform.toLowerCase() as keyof typeof platformHashtags] || [];
    const hashtagLower = hashtag.toLowerCase();

    for (const specific of platformSpecific) {
      if (hashtagLower.includes(specific)) return 8;
    }

    return 2; // Base score for any hashtag
  }

  /**
   * Calculate location relevance score
   */
  private calculateLocationRelevance(hashtag: string, location: string): number {
    const hashtagLower = hashtag.toLowerCase();
    const locationLower = location.toLowerCase();

    if (hashtagLower.includes(locationLower.replace(/\s+/g, ''))) return 10;
    if (hashtagLower.includes('local') || hashtagLower.includes('community')) return 6;
    
    // Check for city/state/country keywords
    const locationParts = location.split(/[,\s]+/);
    for (const part of locationParts) {
      if (part.length > 2 && hashtagLower.includes(part.toLowerCase())) return 8;
    }

    return 1;
  }

  /**
   * Calculate engagement potential score
   */
  private calculateEngagementPotential(hashtag: string): number {
    const highEngagementKeywords = [
      'viral', 'trending', 'amazing', 'incredible', 'awesome', 'beautiful',
      'love', 'best', 'new', 'hot', 'popular', 'top', 'must', 'perfect'
    ];

    const hashtagLower = hashtag.toLowerCase();
    
    for (const keyword of highEngagementKeywords) {
      if (hashtagLower.includes(keyword)) return 9;
    }

    // Length-based scoring (shorter hashtags often perform better)
    if (hashtag.length <= 10) return 7;
    if (hashtag.length <= 15) return 5;
    return 3;
  }

  /**
   * Calculate momentum for hashtag trends
   */
  private calculateMomentum(hashtag: string, trendingData: TrendingData): 'rising' | 'stable' | 'declining' {
    // Simple momentum calculation based on recency and frequency
    const recentArticles = trendingData.articles
      .filter(article => {
        const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);
        return hoursSincePublished <= 24;
      });

    const hashtagMentions = recentArticles.filter(article => 
      `${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())
    ).length;

    if (hashtagMentions >= 3) return 'rising';
    if (hashtagMentions >= 1) return 'stable';
    return 'declining';
  }

  /**
   * Categorize hashtag by type
   */
  private categorizeHashtag(hashtag: string, context: AnalysisContext): HashtagAnalysis['category'] {
    const hashtagLower = hashtag.toLowerCase();

    if (hashtagLower.includes('viral') || hashtagLower.includes('trending')) return 'viral';
    if (hashtagLower.includes(context.businessType.toLowerCase())) return 'business';
    if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) return 'location';
    if (hashtagLower.includes('season') || hashtagLower.includes('holiday')) return 'seasonal';
    if (this.isNicheHashtag(hashtag, context)) return 'niche';
    
    return 'trending';
  }

  /**
   * Check if hashtag is niche-specific
   */
  private isNicheHashtag(hashtag: string, context: AnalysisContext): boolean {
    const industryKeywords = this.getIndustryKeywords(context.businessType);
    const hashtagLower = hashtag.toLowerCase();

    return industryKeywords.some(keyword => 
      hashtagLower.includes(keyword.toLowerCase())
    );
  }

  /**
   * Get industry-specific keywords
   */
  private getIndustryKeywords(businessType: string): string[] {
    const industryMap: Record<string, string[]> = {
      restaurant: ['food', 'dining', 'cuisine', 'chef', 'menu', 'delicious', 'taste'],
      retail: ['shopping', 'fashion', 'style', 'sale', 'deals', 'boutique'],
      healthcare: ['health', 'wellness', 'medical', 'care', 'treatment', 'doctor'],
      fitness: ['workout', 'gym', 'fitness', 'health', 'training', 'exercise'],
      beauty: ['beauty', 'skincare', 'makeup', 'salon', 'spa', 'treatment'],
      technology: ['tech', 'digital', 'innovation', 'software', 'app', 'online'],
      education: ['learning', 'education', 'training', 'course', 'skill', 'knowledge'],
      automotive: ['car', 'auto', 'vehicle', 'repair', 'service', 'maintenance'],
      realestate: ['property', 'home', 'house', 'real estate', 'investment'],
      legal: ['law', 'legal', 'attorney', 'lawyer', 'justice', 'rights']
    };

    return industryMap[businessType.toLowerCase()] || ['business', 'service', 'professional'];
  }

  /**
   * Generate business-specific trending hashtags
   */
  private generateBusinessTrendingHashtags(context: AnalysisContext): string[] {
    const hashtags: string[] = [];

    // Business name hashtag
    hashtags.push(`#${context.businessName.replace(/[^a-zA-Z0-9]/g, '')}`);
    
    // Business type hashtag
    hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
    
    // Location hashtag
    hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
    
    // Industry-specific hashtags
    const industryKeywords = this.getIndustryKeywords(context.businessType);
    hashtags.push(...industryKeywords.slice(0, 3).map(keyword => `#${keyword.replace(/\s+/g, '')}`));

    return hashtags;
  }

  /**
   * Categorize hashtags into strategy groups
   */
  private categorizeHashtags(analyses: HashtagAnalysis[], context: AnalysisContext): AdvancedHashtagStrategy {
    // Sort by overall relevance score
    const sortedAnalyses = analyses.sort((a, b) => {
      const scoreA = (a.relevanceScore + a.trendingScore + a.businessRelevance + a.engagementPotential) / 4;
      const scoreB = (b.relevanceScore + b.trendingScore + b.businessRelevance + b.engagementPotential) / 4;
      return scoreB - scoreA;
    });

    const topTrending = sortedAnalyses
      .filter(a => a.category === 'trending' || a.category === 'viral')
      .slice(0, 8);

    const businessOptimized = sortedAnalyses
      .filter(a => a.businessRelevance >= 6)
      .slice(0, 6);

    const locationSpecific = sortedAnalyses
      .filter(a => a.locationRelevance >= 6)
      .slice(0, 4);

    const platformNative = sortedAnalyses
      .filter(a => a.platformOptimization >= 6)
      .slice(0, 5);

    const emergingTrends = sortedAnalyses
      .filter(a => a.momentum === 'rising')
      .slice(0, 6);

    // Create final recommendations (top 15 hashtags)
    const finalRecommendations = this.createFinalRecommendations(
      topTrending,
      businessOptimized,
      locationSpecific,
      platformNative,
      emergingTrends
    );

    return {
      topTrending,
      businessOptimized,
      locationSpecific,
      platformNative,
      emergingTrends,
      finalRecommendations
    };
  }

  /**
   * Create final hashtag recommendations
   */
  private createFinalRecommendations(
    topTrending: HashtagAnalysis[],
    businessOptimized: HashtagAnalysis[],
    locationSpecific: HashtagAnalysis[],
    platformNative: HashtagAnalysis[],
    emergingTrends: HashtagAnalysis[]
  ): string[] {
    const recommendations = new Set<string>();

    // Add top performers from each category
    topTrending.slice(0, 4).forEach(h => recommendations.add(h.hashtag));
    businessOptimized.slice(0, 3).forEach(h => recommendations.add(h.hashtag));
    locationSpecific.slice(0, 2).forEach(h => recommendations.add(h.hashtag));
    platformNative.slice(0, 3).forEach(h => recommendations.add(h.hashtag));
    emergingTrends.slice(0, 3).forEach(h => recommendations.add(h.hashtag));

    return Array.from(recommendations).slice(0, 15);
  }

  /**
   * Generate cache key for analysis context
   */
  private generateCacheKey(context: AnalysisContext): string {
    return `${context.businessType}-${context.location}-${context.platform}-${context.businessName}`.toLowerCase();
  }

  /**
   * Get fallback strategy when analysis fails
   */
  private getFallbackStrategy(context: AnalysisContext): AdvancedHashtagStrategy {
    const fallbackHashtags = [
      '#trending', '#viral', '#business', '#local', '#community',
      `#${context.businessType.replace(/\s+/g, '')}`,
      `#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`,
      '#quality', '#professional', '#service'
    ];

    const fallbackAnalyses: HashtagAnalysis[] = fallbackHashtags.map(hashtag => ({
      hashtag,
      relevanceScore: 5,
      trendingScore: 3,
      businessRelevance: 5,
      platformOptimization: 4,
      locationRelevance: 3,
      engagementPotential: 5,
      sources: ['fallback'],
      momentum: 'stable' as const,
      category: 'trending' as const
    }));

    return {
      topTrending: fallbackAnalyses.slice(0, 4),
      businessOptimized: fallbackAnalyses.slice(0, 3),
      locationSpecific: fallbackAnalyses.slice(0, 2),
      platformNative: fallbackAnalyses.slice(0, 3),
      emergingTrends: fallbackAnalyses.slice(0, 3),
      finalRecommendations: fallbackHashtags
    };
  }
}

// Export singleton instance
export const advancedHashtagAnalyzer = new AdvancedTrendingHashtagAnalyzer();
