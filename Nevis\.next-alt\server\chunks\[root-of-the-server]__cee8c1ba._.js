module.exports = {

"[externals]/perf_hooks [external] (perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("perf_hooks", () => require("perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/async_hooks [external] (async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("async_hooks", () => require("async_hooks"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/require-in-the-middle [external] (require-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("require-in-the-middle", () => require("require-in-the-middle"));

module.exports = mod;
}}),
"[externals]/import-in-the-middle [external] (import-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("import-in-the-middle", () => require("import-in-the-middle"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/http2 [external] (http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http2", () => require("http2"));

module.exports = mod;
}}),
"[externals]/dns [external] (dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("dns", () => require("dns"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/express [external] (express, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("express", () => require("express"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/ai/genkit.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ai": (()=>ai)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/genkit.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-route] (ecmascript) <locals>");
;
;
// Get API key from environment variables
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
if (!apiKey) {}
const ai = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["genkit"])({
    plugins: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["googleAI"])({
            apiKey
        })
    ],
    model: 'googleai/gemini-2.0-flash'
});
}}),
"[project]/src/ai/prompts/advanced-design-prompts.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Design Generation Prompts
 * 
 * Professional-grade prompts incorporating design principles, composition rules,
 * typography best practices, color theory, and modern design trends.
 */ __turbopack_context__.s({
    "ADVANCED_DESIGN_PRINCIPLES": (()=>ADVANCED_DESIGN_PRINCIPLES),
    "BUSINESS_TYPE_DESIGN_DNA": (()=>BUSINESS_TYPE_DESIGN_DNA),
    "PLATFORM_SPECIFIC_GUIDELINES": (()=>PLATFORM_SPECIFIC_GUIDELINES),
    "QUALITY_ENHANCEMENT_INSTRUCTIONS": (()=>QUALITY_ENHANCEMENT_INSTRUCTIONS)
});
const ADVANCED_DESIGN_PRINCIPLES = `
**COMPOSITION & VISUAL HIERARCHY:**
- Apply the Rule of Thirds: Position key elements along the grid lines or intersections
- Create clear visual hierarchy using size, contrast, and positioning
- Establish a strong focal point that draws the eye immediately
- Use negative space strategically to create breathing room and emphasis
- Balance elements using symmetrical or asymmetrical composition
- Guide the viewer's eye through the design with leading lines and flow

**TYPOGRAPHY EXCELLENCE:**
- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)
- Use maximum 2-3 font families with strong contrast between them
- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)
- Apply proper letter spacing, line height, and text alignment
- Scale typography appropriately for the platform and viewing distance
- Use typography as a design element, not just information delivery

**COLOR THEORY & HARMONY:**
- Apply color psychology appropriate to the business type and message
- Use complementary colors for high contrast and attention
- Apply analogous colors for harmony and cohesion
- Implement triadic color schemes for vibrant, balanced designs
- Ensure sufficient contrast between text and background
- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent

**MODERN DESIGN TRENDS:**
- Embrace minimalism with purposeful use of white space
- Use bold, geometric shapes and clean lines
- Apply subtle gradients and depth effects when appropriate
- Incorporate authentic, diverse photography when using people
- Use consistent border radius and spacing throughout
- Apply subtle shadows and depth for modern dimensionality
`;
const PLATFORM_SPECIFIC_GUIDELINES = {
    instagram: `
**INSTAGRAM OPTIMIZATION:**
- Design for mobile-first viewing with bold, clear elements
- Use high contrast colors that pop on small screens
- Keep text large and readable (minimum 24px equivalent)
- Center important elements for square crop compatibility
- Use Instagram's native color palette trends
- Design for both feed and story formats
- Optimize for thumb-stopping power in fast scroll feeds
- Logo placement: Bottom right corner or integrated naturally into design
- Ensure logo is visible but doesn't overwhelm the main content
`,
    facebook: `
**FACEBOOK OPTIMIZATION:**
- Design for both desktop and mobile viewing
- Use Facebook blue (#1877F2) strategically for CTAs
- Optimize for news feed algorithm preferences
- Include clear value proposition in visual hierarchy
- Design for engagement and shareability
- Use authentic, relatable imagery
- Optimize for both organic and paid placement
- Logo placement: Top left or bottom right corner for brand recognition
- Ensure logo works well in both desktop and mobile formats
`,
    twitter: `
**TWITTER/X OPTIMIZATION:**
- Design for rapid consumption and high engagement
- Use bold, contrasting colors that stand out in timeline
- Keep text minimal and impactful
- Design for retweet and quote tweet functionality
- Use trending visual styles and memes appropriately
- Optimize for both light and dark mode viewing
- Create thumb-stopping visuals for fast-scrolling feeds
- Logo placement: Small, subtle placement that doesn't interfere with content
- Ensure logo is readable in both light and dark modes
`,
    linkedin: `
**LINKEDIN OPTIMIZATION:**
- Use professional, business-appropriate color schemes
- Apply corporate design standards and clean aesthetics
- Include clear value proposition for business audience
- Use professional photography and imagery
- Design for thought leadership and expertise positioning
- Apply subtle, sophisticated design elements
- Optimize for professional networking context
- Logo placement: Prominent placement for brand authority and recognition
- Ensure logo conveys professionalism and trustworthiness
`
};
const BUSINESS_TYPE_DESIGN_DNA = {
    restaurant: `
**RESTAURANT DESIGN DNA:**
- Use warm, appetizing colors (reds, oranges, warm yellows)
- Include high-quality food photography with proper lighting
- Apply rustic or modern clean aesthetics based on restaurant type
- Use food-focused typography (script for upscale, bold sans for casual)
- Include appetite-triggering visual elements
- Apply golden hour lighting effects for food imagery
- Use complementary colors that enhance food appeal
- Show diverse people enjoying meals in authentic, social settings
- Include cultural food elements that reflect local cuisine traditions
- Display chefs, staff, and customers from the local community
- Use table settings and dining environments that feel culturally authentic
`,
    fitness: `
**FITNESS DESIGN DNA:**
- Use energetic, motivational color schemes (bright blues, oranges, greens)
- Include dynamic action shots and movement
- Apply bold, strong typography with impact
- Use high-contrast designs for motivation and energy
- Include progress and achievement visual metaphors
- Apply athletic and performance-focused imagery
- Use inspiring and empowering visual language
- Show diverse athletes and fitness enthusiasts in action
- Include people of different body types, ages, and fitness levels
- Display authentic workout environments and community settings
- Use culturally relevant sports and fitness activities for the region
`,
    beauty: `
**BEAUTY DESIGN DNA:**
- Use sophisticated, elegant color palettes (pastels, metallics)
- Include high-quality beauty photography with perfect lighting
- Apply clean, minimalist aesthetics with luxury touches
- Use elegant, refined typography
- Include aspirational and transformational imagery
- Apply soft, flattering lighting effects
- Use premium and luxurious visual elements
- Show diverse models representing different skin tones, ages, and beauty standards
- Include authentic beauty routines and self-care moments
- Display culturally relevant beauty practices and aesthetics
- Use inclusive representation that celebrates natural beauty diversity
`,
    tech: `
**TECH DESIGN DNA (CANVA-QUALITY):**
- Use sophisticated, professional color schemes (modern blues, elegant grays, clean whites)
- Include polished, well-designed layouts with strategic geometric elements and refined shapes
- Apply professional business visual metaphors with premium stock photography quality
- Use modern, bold typography with clear hierarchy (multiple font weights and sizes)
- Include high-quality business imagery: professional office spaces, authentic workplace scenarios
- Apply elegant design effects: subtle gradients, refined shadows, tasteful borders
- Use trustworthy and sophisticated visual language that matches premium Canva templates
- Show diverse tech professionals in polished, well-lit business environments
- Include people using technology in professional, aspirational business contexts
- Display modern office spaces, premium remote work setups, and sophisticated business environments
- Use strategic design elements: elegant shapes, professional patterns, refined layouts
- Create designs that look intentionally crafted and professionally designed
- FOCUS: Premium stock photography quality, sophisticated layouts, Canva-level polish
`,
    ecommerce: `
**E-COMMERCE DESIGN DNA:**
- Use conversion-focused color schemes (trust blues, urgency reds, success greens)
- Include high-quality product photography with lifestyle context
- Apply clean, scannable layouts with clear hierarchy
- Use action-oriented typography and compelling CTAs
- Include social proof and trust signals
- Apply mobile-first responsive design principles
- Use persuasive and benefit-focused visual language
- Show diverse customers using products in real-life situations
- Include authentic unboxing and product experience moments
- Display culturally relevant usage scenarios and lifestyle contexts
`,
    healthcare: `
**HEALTHCARE DESIGN DNA:**
- Use calming, trustworthy color palettes (soft blues, greens, whites)
- Include professional medical imagery with human warmth
- Apply clean, accessible design with clear information hierarchy
- Use readable, professional typography
- Include caring and compassionate visual elements
- Apply medical accuracy with approachable aesthetics
- Use reassuring and professional visual language
- Show diverse healthcare professionals and patients
- Include authentic care moments and medical environments
- Display culturally sensitive healthcare interactions and settings
`,
    education: `
**EDUCATION DESIGN DNA:**
- Use inspiring, growth-focused color schemes (blues, greens, warm oranges)
- Include diverse learning environments and educational moments
- Apply organized, structured layouts with clear learning paths
- Use friendly, accessible typography
- Include knowledge and achievement visual metaphors
- Apply bright, optimistic design elements
- Use encouraging and empowering visual language
- Show students and educators from diverse backgrounds
- Include authentic classroom and learning environments
- Display culturally relevant educational practices and settings
`,
    default: `
**UNIVERSAL DESIGN DNA:**
- Use brand-appropriate color psychology
- Include authentic, high-quality imagery
- Apply clean, professional aesthetics
- Use readable, accessible typography
- Include relevant industry visual metaphors
- Apply consistent brand visual language
- Use trustworthy and professional design elements
- Show diverse people in authentic, relevant contexts
- Include culturally appropriate imagery and design elements
- Display real human connections and authentic moments
`
};
const QUALITY_ENHANCEMENT_INSTRUCTIONS = `
**DESIGN QUALITY STANDARDS:**
- Ensure all text is perfectly readable with sufficient contrast
- Apply consistent spacing and alignment throughout
- Use high-resolution imagery without pixelation or artifacts
- Maintain visual balance and proper proportions
- Ensure brand elements are prominently but naturally integrated
- Apply professional color grading and visual polish
- Create designs that work across different screen sizes
- Ensure accessibility compliance for color contrast and readability

**TECHNICAL EXCELLENCE:**
- Generate crisp, high-resolution images suitable for social media
- Apply proper aspect ratios for platform requirements
- Ensure text overlay is perfectly positioned and readable
- Use consistent visual style throughout the design
- Apply professional lighting and shadow effects
- Ensure logo integration feels natural and branded
- Create designs that maintain quality when compressed for social media
`;
}}),
"[project]/src/ai/utils/design-analysis.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Analysis Utilities
 * 
 * Intelligent analysis and processing of design examples for better AI generation
 */ __turbopack_context__.s({
    "DesignAnalysisSchema": (()=>DesignAnalysisSchema),
    "analyzeDesignExample": (()=>analyzeDesignExample),
    "extractDesignDNA": (()=>extractDesignDNA),
    "selectOptimalDesignExamples": (()=>selectOptimalDesignExamples)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
;
;
const DesignAnalysisSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        primary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Primary color in hex format'),
        secondary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Secondary color in hex format'),
        accent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Accent color in hex format'),
        colorHarmony: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'complementary',
            'analogous',
            'triadic',
            'monochromatic',
            'split-complementary'
        ]).describe('Type of color harmony used'),
        colorMood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood conveyed by the color scheme')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        layout: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'centered',
            'left-aligned',
            'right-aligned',
            'asymmetrical',
            'grid-based'
        ]).describe('Primary layout structure'),
        visualHierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('How visual hierarchy is established'),
        focalPoint: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Primary focal point and how it\'s created'),
        balance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'symmetrical',
            'asymmetrical',
            'radial'
        ]).describe('Type of visual balance'),
        whitespace: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'minimal',
            'moderate',
            'generous'
        ]).describe('Use of negative space')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        primaryFont: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Primary font style/category'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Typographic hierarchy structure'),
        textTreatment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Special text treatments or effects'),
        readability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'stylized'
        ]).describe('Text readability level')
    }),
    style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        aesthetic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Emotional mood and feeling'),
        sophistication: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'casual',
            'professional',
            'luxury',
            'playful'
        ]).describe('Level of sophistication'),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Current design trends incorporated')
    }),
    effectiveness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        attention: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),
        clarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Message clarity (1-10)'),
        brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand alignment strength (1-10)'),
        platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform optimization (1-10)')
    })
});
// Design analysis prompt
const designAnalysisPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignExample',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignAnalysisSchema
    },
    prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.

Analyze the provided design image and extract detailed insights about its design elements and effectiveness.

Business Context: {{businessType}}
Platform: {{platform}}
Context: {{designContext}}

Provide a comprehensive analysis covering:

1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact
2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space
3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment
4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation
5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization

Be specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`
});
async function analyzeDesignExample(designImageUrl, businessType, platform, context) {
    try {
        // For now, return a mock analysis to avoid API issues
        // This can be replaced with actual AI analysis once the prompt system is stable
        return {
            colorPalette: {
                primary: '#FF6B6B',
                secondary: '#4ECDC4',
                accent: '#45B7D1',
                colorHarmony: 'complementary',
                colorMood: 'Energetic and modern'
            },
            composition: {
                layout: 'centered',
                visualHierarchy: 'Clear size-based hierarchy with strong focal point',
                focalPoint: 'Central logo and headline combination',
                balance: 'symmetrical',
                whitespace: 'moderate'
            },
            typography: {
                primaryFont: 'Modern sans-serif',
                hierarchy: 'Large headline, medium subtext, small details',
                textTreatment: 'Bold headlines with subtle shadows',
                readability: 'high'
            },
            style: {
                aesthetic: 'Modern minimalist',
                mood: 'Professional and approachable',
                sophistication: 'professional',
                trends: [
                    'Bold typography',
                    'Minimalist design',
                    'High contrast'
                ]
            },
            effectiveness: {
                attention: 8,
                clarity: 9,
                brandAlignment: 8,
                platformOptimization: 7
            }
        };
    } catch (error) {
        throw new Error('Failed to analyze design example');
    }
}
function selectOptimalDesignExamples(designExamples, analyses, contentType, platform, maxExamples = 3) {
    if (!analyses.length || !designExamples.length) {
        return designExamples.slice(0, maxExamples);
    }
    // Score each design based on relevance and effectiveness
    const scoredExamples = designExamples.map((example, index)=>{
        const analysis = analyses[index];
        if (!analysis) return {
            example,
            score: 0
        };
        let score = 0;
        // Weight effectiveness metrics
        score += analysis.effectiveness.attention * 0.3;
        score += analysis.effectiveness.clarity * 0.25;
        score += analysis.effectiveness.brandAlignment * 0.25;
        score += analysis.effectiveness.platformOptimization * 0.2;
        // Bonus for sophisticated designs
        if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {
            score += 1;
        }
        // Bonus for modern trends
        score += analysis.style.trends.length * 0.5;
        return {
            example,
            score,
            analysis
        };
    });
    // Sort by score and return top examples
    return scoredExamples.sort((a, b)=>b.score - a.score).slice(0, maxExamples).map((item)=>item.example);
}
function extractDesignDNA(analyses) {
    if (!analyses.length) return '';
    const commonElements = {
        colors: analyses.map((a)=>a.colorPalette.colorHarmony),
        layouts: analyses.map((a)=>a.composition.layout),
        aesthetics: analyses.map((a)=>a.style.aesthetic),
        moods: analyses.map((a)=>a.style.mood)
    };
    // Find most common elements
    const mostCommonColor = getMostCommon(commonElements.colors);
    const mostCommonLayout = getMostCommon(commonElements.layouts);
    const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);
    const mostCommonMood = getMostCommon(commonElements.moods);
    return `
**EXTRACTED DESIGN DNA:**
- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes
- **Layout Pattern**: Favors ${mostCommonLayout} compositions
- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach
- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout
- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation
- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure
`;
}
/**
 * Helper function to find most common element in array
 */ function getMostCommon(arr) {
    const counts = arr.reduce((acc, item)=>{
        acc[item] = (acc[item] || 0) + 1;
        return acc;
    }, {});
    return Object.entries(counts).reduce((a, b)=>counts[a[0]] > counts[b[0]] ? a : b)[0];
}
}}),
"[project]/src/ai/utils/design-quality.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Quality Validation and Enhancement
 * 
 * System for validating, scoring, and iteratively improving generated designs
 */ __turbopack_context__.s({
    "DesignQualitySchema": (()=>DesignQualitySchema),
    "assessDesignQuality": (()=>assessDesignQuality),
    "calculateWeightedScore": (()=>calculateWeightedScore),
    "generateImprovementPrompt": (()=>generateImprovementPrompt),
    "meetsQualityStandards": (()=>meetsQualityStandards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
;
;
const DesignQualitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    overall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Overall design quality score (1-10)'),
        grade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'A+',
            'A',
            'B+',
            'B',
            'C+',
            'C',
            'D',
            'F'
        ]).describe('Letter grade for design quality'),
        summary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Brief summary of design strengths and weaknesses')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Composition and layout quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on composition'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested composition improvements')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Typography quality and readability (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on typography'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested typography improvements')
    }),
    colorDesign: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Color usage and harmony (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on color choices'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested color improvements')
    }),
    brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on brand alignment'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested brand alignment improvements')
    }),
    platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform-specific optimization (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on platform optimization'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested platform optimization improvements')
    }),
    technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Technical execution quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on technical aspects'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested technical improvements')
    }),
    recommendedActions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
        priority: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('Priority level of the action'),
        action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Specific action to take'),
        expectedImpact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('Expected impact of the action')
    })).describe('Prioritized list of recommended improvements')
});
// Design quality assessment prompt
const designQualityPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'assessDesignQuality',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string(),
            visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string(),
            brandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designGoals: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignQualitySchema
    },
    prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.

Evaluate the provided design image with the highest professional standards.

**Context:**
- Business Type: {{businessType}}
- Platform: {{platform}}
- Visual Style Goal: {{visualStyle}}
- Brand Colors: {{brandColors}}
- Design Goals: {{designGoals}}

**Assessment Criteria:**

1. **Composition & Layout** (25%):
   - Visual hierarchy and flow
   - Balance and proportion
   - Use of negative space
   - Rule of thirds application
   - Focal point effectiveness

2. **Typography** (20%):
   - Readability and legibility
   - Hierarchy and contrast
   - Font choice appropriateness
   - Text positioning and spacing
   - Accessibility compliance

3. **Color Design** (20%):
   - Color harmony and theory
   - Brand color integration
   - Contrast and accessibility
   - Psychological impact
   - Platform appropriateness

4. **Brand Alignment** (15%):
   - Brand consistency
   - Logo integration
   - Visual style adherence
   - Brand personality expression
   - Professional presentation

5. **Platform Optimization** (10%):
   - Platform-specific best practices
   - Mobile optimization
   - Engagement potential
   - Algorithm friendliness
   - Format appropriateness

6. **Technical Quality** (10%):
   - Image resolution and clarity
   - Professional finish
   - Technical execution
   - Scalability
   - Print/digital readiness

Provide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`
});
async function assessDesignQuality(designImageUrl, businessType, platform, visualStyle, brandColors, designGoals) {
    try {
        // For now, return a mock quality assessment to avoid API issues
        // This provides realistic quality scores while the system is being tested
        const baseScore = 7 + Math.random() * 2; // Random score between 7-9
        return {
            overall: {
                score: Math.round(baseScore * 10) / 10,
                grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',
                summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`
            },
            composition: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Strong visual hierarchy with balanced composition',
                improvements: baseScore < 8 ? [
                    'Improve focal point clarity',
                    'Enhance visual balance'
                ] : []
            },
            typography: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Clear, readable typography with appropriate hierarchy',
                improvements: baseScore < 8 ? [
                    'Increase text contrast',
                    'Improve font pairing'
                ] : []
            },
            colorDesign: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',
                improvements: baseScore < 8 ? [
                    'Enhance color harmony',
                    'Improve contrast ratios'
                ] : []
            },
            brandAlignment: {
                score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,
                feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',
                improvements: !brandColors ? [
                    'Integrate brand elements',
                    'Improve brand consistency'
                ] : []
            },
            platformOptimization: {
                score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,
                feedback: `Well optimized for ${platform} format and audience`,
                improvements: baseScore < 8 ? [
                    'Optimize for mobile viewing',
                    'Improve platform-specific elements'
                ] : []
            },
            technicalQuality: {
                score: Math.round((baseScore + 0.2) * 10) / 10,
                feedback: 'High resolution with professional finish',
                improvements: baseScore < 8 ? [
                    'Improve image resolution',
                    'Enhance visual polish'
                ] : []
            },
            recommendedActions: [
                {
                    priority: baseScore < 7.5 ? 'high' : 'medium',
                    action: 'Enhance visual impact through stronger focal points',
                    expectedImpact: 'Improved attention and engagement'
                },
                {
                    priority: 'medium',
                    action: 'Optimize typography for better readability',
                    expectedImpact: 'Clearer message communication'
                }
            ].filter((action)=>baseScore < 8.5 || action.priority === 'medium')
        };
    } catch (error) {
        throw new Error('Failed to assess design quality');
    }
}
function generateImprovementPrompt(quality) {
    const highPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'high').map((action)=>action.action);
    const mediumPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'medium').map((action)=>action.action);
    let improvementPrompt = `
**DESIGN IMPROVEMENT INSTRUCTIONS:**

Based on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):

**CRITICAL IMPROVEMENTS (High Priority):**
${highPriorityActions.map((action)=>`- ${action}`).join('\n')}

**RECOMMENDED ENHANCEMENTS (Medium Priority):**
${mediumPriorityActions.map((action)=>`- ${action}`).join('\n')}

**SPECIFIC AREA FEEDBACK:**
`;
    if (quality.composition.score < 7) {
        improvementPrompt += `
**Composition Issues to Address:**
${quality.composition.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.typography.score < 7) {
        improvementPrompt += `
**Typography Issues to Address:**
${quality.typography.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.colorDesign.score < 7) {
        improvementPrompt += `
**Color Design Issues to Address:**
${quality.colorDesign.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.brandAlignment.score < 7) {
        improvementPrompt += `
**Brand Alignment Issues to Address:**
${quality.brandAlignment.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    return improvementPrompt;
}
function meetsQualityStandards(quality, minimumScore = 7) {
    return quality.overall.score >= minimumScore && quality.composition.score >= minimumScore - 1 && quality.typography.score >= minimumScore - 1 && quality.brandAlignment.score >= minimumScore - 1;
}
function calculateWeightedScore(quality) {
    const weights = {
        composition: 0.25,
        typography: 0.20,
        colorDesign: 0.20,
        brandAlignment: 0.15,
        platformOptimization: 0.10,
        technicalQuality: 0.10
    };
    return quality.composition.score * weights.composition + quality.typography.score * weights.typography + quality.colorDesign.score * weights.colorDesign + quality.brandAlignment.score * weights.brandAlignment + quality.platformOptimization.score * weights.platformOptimization + quality.technicalQuality.score * weights.technicalQuality;
}
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
/* __next_internal_action_entry_do_not_use__ [{"409d8d4d6ee48a33e913d651c38aa0005eeae0dac6":"generateCreativeAsset"},"",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-route] (ecmascript)");
/**
 * @fileOverview A Genkit flow for generating a creative asset (image or video)
 * based on a user's prompt, an optional reference image, and brand profile settings.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
// Define the input schema for the creative asset generation flow.
const CreativeAssetInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('The main text prompt describing the desired asset.'),
    outputType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
        'image',
        'video'
    ]).describe('The type of asset to generate.'),
    referenceAssetUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('An optional reference image or video as a data URI.'),
    useBrandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].boolean().describe('Whether to apply the brand profile.'),
    brandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].custom().nullable().describe('The brand profile object.'),
    maskDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),
    aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
        '16:9',
        '9:16'
    ]).optional().describe('The aspect ratio for video generation.'),
    preferredModel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Preferred model for generation (e.g., gemini-2.5-flash-image-preview).')
});
// Define the output schema for the creative asset generation flow.
const CreativeAssetOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated image, if applicable.'),
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated video, if applicable.'),
    aiExplanation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().describe('A brief explanation from the AI about what it created.')
});
async function generateCreativeAsset(input) {
    return generateCreativeAssetFlow(input);
}
/**
 * Helper function to download video and convert to data URI
 */ async function videoToDataURI(videoPart) {
    if (!videoPart.media || !videoPart.media.url) {
        throw new Error('Media URL not found in video part.');
    }
    const fetch = (await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    const videoDownloadResponse = await fetch(`${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`);
    if (!videoDownloadResponse.ok) {
        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);
    }
    const videoBuffer = await videoDownloadResponse.arrayBuffer();
    const base64Video = Buffer.from(videoBuffer).toString('base64');
    const contentType = videoPart.media.contentType || 'video/mp4';
    return `data:${contentType};base64,${base64Video}`;
}
/**
 * Extracts text in quotes and the remaining prompt.
 */ const extractQuotedText = (prompt)=>{
    const quoteRegex = /"([^"]*)"/;
    const match = prompt.match(quoteRegex);
    if (match) {
        return {
            imageText: match[1],
            remainingPrompt: prompt.replace(quoteRegex, '').trim()
        };
    }
    return {
        imageText: null,
        remainingPrompt: prompt
    };
};
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
/**
 * The core Genkit flow for generating a creative asset.
 */ const generateCreativeAssetFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generateCreativeAssetFlow',
    inputSchema: CreativeAssetInputSchema,
    outputSchema: CreativeAssetOutputSchema
}, async (input)=>{
    const promptParts = [];
    let textPrompt = '';
    const { imageText, remainingPrompt } = extractQuotedText(input.prompt);
    if (input.maskDataUrl && input.referenceAssetUrl) {
        // This is an inpainting request.
        textPrompt = `You are an expert image editor performing a precise inpainting task.
You will be given an original image, a mask, and a text prompt.
Your task is to modify the original image *only* in the areas designated by the black region of the mask.
The rest of the image must remain absolutely unchanged.
If the prompt is a "remove" or "delete" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.
The user's instruction for the masked area is: "${remainingPrompt}".
Recreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;
        promptParts.push({
            text: textPrompt
        });
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
        promptParts.push({
            media: {
                url: input.maskDataUrl,
                contentType: getMimeTypeFromDataURI(input.maskDataUrl)
            }
        });
    } else if (input.referenceAssetUrl) {
        // This is a generation prompt with a reference asset (image or video).
        let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.
Your task is to generate a new asset that is inspired by the reference asset and follows the new instructions.

Your primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.
Analyze the user's prompt for common editing terminology and apply it creatively. For example:
- If asked to "change the background," intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.
- If asked to "make the logo bigger" or "change the text color," perform those specific edits while maintaining the overall composition.
- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.

The user's instruction is: "${remainingPrompt}"`;
        if (imageText) {
            referencePrompt += `\n\n**Explicit Text Overlay:** The user has provided specific text in quotes: "${imageText}". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`;
        }
        if (input.outputType === 'video') {
            referencePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (imageText) {
                referencePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
        }
        if (input.useBrandProfile && input.brandProfile) {
            const bp = input.brandProfile;
            let brandGuidelines = '\n\n**Brand Guidelines:**';
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
                brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`;
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`;
            }
            referencePrompt += brandGuidelines;
        }
        textPrompt = referencePrompt;
        if (textPrompt) {
            promptParts.push({
                text: textPrompt
            });
        }
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
    } else if (input.useBrandProfile && input.brandProfile) {
        // This is a new, on-brand asset generation with advanced design principles.
        const bp = input.brandProfile;
        // Get business-specific design DNA
        const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][bp.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
        let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.

BUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})
CONTENT: "${remainingPrompt}"
STYLE: ${bp.visualStyle}, modern, clean, professional

FORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}

BRAND COLORS (use prominently):
${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}
${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}
${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}

REQUIREMENTS:
- High-quality, professional design
- ${bp.visualStyle} aesthetic
- Clean, modern layout
- Perfect for ${bp.businessType} business
- Brand colors prominently featured
- Professional social media appearance`;
        // Intelligent design examples processing
        let designDNA = '';
        let selectedExamples = [];
        if (bp.designExamples && bp.designExamples.length > 0) {
            try {
                // Analyze design examples for intelligent processing
                const analyses = [];
                for (const example of bp.designExamples.slice(0, 3)){
                    try {
                        const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, bp.businessType, 'creative-studio', `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`);
                        analyses.push(analysis);
                    } catch (error) {}
                }
                if (analyses.length > 0) {
                    // Extract design DNA from analyzed examples
                    designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                    // Select optimal examples based on analysis
                    selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(bp.designExamples, analyses, remainingPrompt, 'creative-studio', 2);
                } else {
                    selectedExamples = bp.designExamples.slice(0, 2);
                }
            } catch (error) {
                selectedExamples = bp.designExamples.slice(0, 2);
            }
            onBrandPrompt += `\n**STYLE REFERENCE:**
Use the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.

${designDNA}`;
        }
        if (input.outputType === 'image') {
            onBrandPrompt += `\n- **Text Overlay Requirements:** ${imageText ? `
                  * Display this EXACT text: "${imageText}"
                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters
                  * Make text LARGE and BOLD for mobile readability
                  * Apply high contrast (minimum 4.5:1 ratio) between text and background
                  * Add text shadows, outlines, or semi-transparent backgrounds for readability
                  * Position text using rule of thirds for optimal composition
                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;
            onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;
            onBrandPrompt += `\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            }
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            onBrandPrompt += `\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                onBrandPrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                onBrandPrompt += `\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Brand Identity:** Create a design that represents the brand identity and style.`;
            }
            // Add selected design examples as reference
            selectedExamples.forEach((designExample)=>{
                promptParts.push({
                    media: {
                        url: designExample,
                        contentType: getMimeTypeFromDataURI(designExample)
                    }
                });
            });
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    } else {
        // This is a new, un-branded, creative prompt.
        let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: "${remainingPrompt}".

⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:
- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)
- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes
- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small
- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness
- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions
- SHARP DETAILS: Crystal-clear textures, no blur or artifacts
- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows
- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance
- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;
        if (input.outputType === 'image' && imageText) {
            creativePrompt += `

🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨

⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:
- NEVER add "Flex Your Finances" or any financial terms
- NEVER add "Payroll Banking Simplified" or banking phrases
- NEVER add "Banking Made Easy" or similar taglines
- NEVER add company descriptions or service explanations
- NEVER add marketing copy or promotional text
- NEVER add placeholder text or sample content
- NEVER create fake headlines or taglines
- NEVER add descriptive text about the business
- NEVER add ANY text except what is specified below

🎯 ONLY THIS TEXT IS ALLOWED: "${imageText}"
🎯 REPEAT: ONLY THIS TEXT: "${imageText}"
🎯 NO OTHER TEXT PERMITTED: "${imageText}"

🌍 ENGLISH ONLY REQUIREMENT:
- ALL text must be in clear, readable English
- NO foreign languages (Arabic, Chinese, Hindi, etc.)
- NO special characters, symbols, or corrupted text
- NO accents or diacritical marks

Overlay ONLY the following text onto the asset: "${imageText}".
DO NOT ADD ANY OTHER TEXT.
Ensure the text is readable and well-composed.`;
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            creativePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                creativePrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                creativePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    }
    const aiExplanationPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].definePrompt({
        name: 'creativeAssetExplanationPrompt',
        prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: "I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo."`
    });
    const explanationResult = await aiExplanationPrompt();
    try {
        if (input.outputType === 'image') {
            // Generate image with quality validation
            let finalImageUrl = null;
            let attempts = 0;
            const maxAttempts = 2;
            while(attempts < maxAttempts && !finalImageUrl){
                attempts++;
                // Determine which model to use based on preferred model parameter
                let modelToUse = 'googleai/gemini-2.0-flash-preview-image-generation'; // Default
                if (input.preferredModel) {
                    // Map Gemini model names to Genkit model identifiers
                    const modelMapping = {
                        'gemini-2.5-flash-image-preview': 'googleai/gemini-2.5-flash-image-preview',
                        'gemini-2.0-flash-preview-image-generation': 'googleai/gemini-2.0-flash-preview-image-generation',
                        'gemini-2.5-flash': 'googleai/gemini-2.5-flash'
                    };
                    modelToUse = modelMapping[input.preferredModel] || modelToUse;
                }
                const { media } = await generateWithRetry({
                    model: modelToUse,
                    prompt: promptParts,
                    config: {
                        responseModalities: [
                            'TEXT',
                            'IMAGE'
                        ]
                    }
                });
                let imageUrl = media?.url ?? null;
                if (!imageUrl) {
                    if (attempts === maxAttempts) {
                        throw new Error('Failed to generate image');
                    }
                    continue;
                }
                // Apply aspect ratio correction if needed
                if (input.aspectRatio && input.aspectRatio !== '1:1') {
                    try {
                        const { cropImageFromUrl } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                        // Map aspect ratio to platform for cropping
                        const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' : input.aspectRatio === '9:16' ? 'story' : 'instagram';
                        imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);
                    } catch (cropError) {
                    // Continue with original image if cropping fails
                    }
                }
                // Quality validation for brand profile designs
                if (input.useBrandProfile && input.brandProfile && attempts === 1) {
                    try {
                        const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.brandProfile.businessType, 'creative-studio', input.brandProfile.visualStyle, undefined, `Creative asset: ${remainingPrompt}`);
                        // If quality is acceptable, use this design
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 6)) {
                            finalImageUrl = imageUrl;
                            break;
                        }
                        // If quality is poor and we have attempts left, try to improve
                        if (attempts < maxAttempts) {
                            // Add improvement instructions to prompt
                            const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                            const improvedPrompt = `${promptParts[0].text}\n\n${improvementInstructions}`;
                            promptParts[0] = {
                                text: improvedPrompt
                            };
                            continue;
                        } else {
                            finalImageUrl = imageUrl;
                            break;
                        }
                    } catch (qualityError) {
                        finalImageUrl = imageUrl;
                        break;
                    }
                } else {
                    finalImageUrl = imageUrl;
                    break;
                }
            }
            return {
                imageUrl: finalImageUrl,
                videoUrl: null,
                aiExplanation: explanationResult.output ?? "Here is the generated image based on your prompt."
            };
        } else {
            const isVertical = input.aspectRatio === '9:16';
            const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';
            const config = {};
            if (isVertical) {
                config.aspectRatio = '9:16';
                config.durationSeconds = 8;
            }
            const result = await generateWithRetry({
                model,
                prompt: promptParts,
                config
            });
            let operation = result.operation;
            if (!operation) {
                throw new Error('The video generation process did not start correctly. Please try again.');
            }
            // Poll for completion
            while(!operation.done){
                await new Promise((resolve)=>setTimeout(resolve, 5000)); // wait 5s
                operation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ai"].checkOperation(operation);
            }
            if (operation.error) {
                throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);
            }
            const videoPart = operation.output?.message?.content.find((p)=>!!p.media);
            if (!videoPart || !videoPart.media) {
                throw new Error('Video generation completed, but the final video file could not be found.');
            }
            const videoDataUrl = await videoToDataURI(videoPart);
            return {
                imageUrl: null,
                videoUrl: videoDataUrl,
                aiExplanation: explanationResult.output ?? "Here is the generated video based on your prompt."
            };
        }
    } catch (e) {
        // Ensure a user-friendly error is thrown
        const message = e.message || "An unknown error occurred during asset generation.";
        throw new Error(message);
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateCreativeAsset
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(generateCreativeAsset, "409d8d4d6ee48a33e913d651c38aa0005eeae0dac6", null);
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cee8c1ba._.js.map