{"version": 3, "sources": [], "sections": [{"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/genkit.ts"], "sourcesContent": ["import { genkit } from 'genkit';\r\nimport { googleAI } from '@genkit-ai/googleai';\r\n\r\n// Get API key from environment variables\r\nconst apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\nexport const ai = genkit({\r\n  plugins: [googleAI({ apiKey })],\r\n  model: 'googleai/gemini-2.0-flash', // Back to working Gemini 2.0 Flash\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEA,yCAAyC;AACzC,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,oBAAoB;AAE3G,IAAI,CAAC,QAAQ,CACb;AAEO,MAAM,KAAK,CAAA,GAAA,yIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE;YAAE;QAAO;KAAG;IAC/B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/advanced-design-prompts.ts"], "sourcesContent": ["/**\r\n * Advanced Design Generation Prompts\r\n * \r\n * Professional-grade prompts incorporating design principles, composition rules,\r\n * typography best practices, color theory, and modern design trends.\r\n */\r\n\r\nexport const ADVANCED_DESIGN_PRINCIPLES = `\r\n**COMPOSITION & VISUAL HIERARCHY:**\r\n- Apply the Rule of Thirds: Position key elements along the grid lines or intersections\r\n- Create clear visual hierarchy using size, contrast, and positioning\r\n- Establish a strong focal point that draws the eye immediately\r\n- Use negative space strategically to create breathing room and emphasis\r\n- Balance elements using symmetrical or asymmetrical composition\r\n- Guide the viewer's eye through the design with leading lines and flow\r\n\r\n**TYPOGRAPHY EXCELLENCE:**\r\n- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)\r\n- Use maximum 2-3 font families with strong contrast between them\r\n- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)\r\n- Apply proper letter spacing, line height, and text alignment\r\n- Scale typography appropriately for the platform and viewing distance\r\n- Use typography as a design element, not just information delivery\r\n\r\n**COLOR THEORY & HARMONY:**\r\n- Apply color psychology appropriate to the business type and message\r\n- Use complementary colors for high contrast and attention\r\n- Apply analogous colors for harmony and cohesion\r\n- Implement triadic color schemes for vibrant, balanced designs\r\n- Ensure sufficient contrast between text and background\r\n- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent\r\n\r\n**MODERN DESIGN TRENDS:**\r\n- Embrace minimalism with purposeful use of white space\r\n- Use bold, geometric shapes and clean lines\r\n- Apply subtle gradients and depth effects when appropriate\r\n- Incorporate authentic, diverse photography when using people\r\n- Use consistent border radius and spacing throughout\r\n- Apply subtle shadows and depth for modern dimensionality\r\n`;\r\n\r\nexport const PLATFORM_SPECIFIC_GUIDELINES = {\r\n  instagram: `\r\n**INSTAGRAM OPTIMIZATION:**\r\n- Design for mobile-first viewing with bold, clear elements\r\n- Use high contrast colors that pop on small screens\r\n- Keep text large and readable (minimum 24px equivalent)\r\n- Center important elements for square crop compatibility\r\n- Use Instagram's native color palette trends\r\n- Design for both feed and story formats\r\n- Optimize for thumb-stopping power in fast scroll feeds\r\n- Logo placement: Bottom right corner or integrated naturally into design\r\n- Ensure logo is visible but doesn't overwhelm the main content\r\n`,\r\n\r\n  facebook: `\r\n**FACEBOOK OPTIMIZATION:**\r\n- Design for both desktop and mobile viewing\r\n- Use Facebook blue (#1877F2) strategically for CTAs\r\n- Optimize for news feed algorithm preferences\r\n- Include clear value proposition in visual hierarchy\r\n- Design for engagement and shareability\r\n- Use authentic, relatable imagery\r\n- Optimize for both organic and paid placement\r\n- Logo placement: Top left or bottom right corner for brand recognition\r\n- Ensure logo works well in both desktop and mobile formats\r\n`,\r\n\r\n  twitter: `\r\n**TWITTER/X OPTIMIZATION:**\r\n- Design for rapid consumption and high engagement\r\n- Use bold, contrasting colors that stand out in timeline\r\n- Keep text minimal and impactful\r\n- Design for retweet and quote tweet functionality\r\n- Use trending visual styles and memes appropriately\r\n- Optimize for both light and dark mode viewing\r\n- Create thumb-stopping visuals for fast-scrolling feeds\r\n- Logo placement: Small, subtle placement that doesn't interfere with content\r\n- Ensure logo is readable in both light and dark modes\r\n`,\r\n\r\n  linkedin: `\r\n**LINKEDIN OPTIMIZATION:**\r\n- Use professional, business-appropriate color schemes\r\n- Apply corporate design standards and clean aesthetics\r\n- Include clear value proposition for business audience\r\n- Use professional photography and imagery\r\n- Design for thought leadership and expertise positioning\r\n- Apply subtle, sophisticated design elements\r\n- Optimize for professional networking context\r\n- Logo placement: Prominent placement for brand authority and recognition\r\n- Ensure logo conveys professionalism and trustworthiness\r\n`\r\n};\r\n\r\nexport const BUSINESS_TYPE_DESIGN_DNA = {\r\n  restaurant: `\r\n**RESTAURANT DESIGN DNA:**\r\n- Use warm, appetizing colors (reds, oranges, warm yellows)\r\n- Include high-quality food photography with proper lighting\r\n- Apply rustic or modern clean aesthetics based on restaurant type\r\n- Use food-focused typography (script for upscale, bold sans for casual)\r\n- Include appetite-triggering visual elements\r\n- Apply golden hour lighting effects for food imagery\r\n- Use complementary colors that enhance food appeal\r\n- Show diverse people enjoying meals in authentic, social settings\r\n- Include cultural food elements that reflect local cuisine traditions\r\n- Display chefs, staff, and customers from the local community\r\n- Use table settings and dining environments that feel culturally authentic\r\n`,\r\n\r\n  fitness: `\r\n**FITNESS DESIGN DNA:**\r\n- Use energetic, motivational color schemes (bright blues, oranges, greens)\r\n- Include dynamic action shots and movement\r\n- Apply bold, strong typography with impact\r\n- Use high-contrast designs for motivation and energy\r\n- Include progress and achievement visual metaphors\r\n- Apply athletic and performance-focused imagery\r\n- Use inspiring and empowering visual language\r\n- Show diverse athletes and fitness enthusiasts in action\r\n- Include people of different body types, ages, and fitness levels\r\n- Display authentic workout environments and community settings\r\n- Use culturally relevant sports and fitness activities for the region\r\n`,\r\n\r\n  beauty: `\r\n**BEAUTY DESIGN DNA:**\r\n- Use sophisticated, elegant color palettes (pastels, metallics)\r\n- Include high-quality beauty photography with perfect lighting\r\n- Apply clean, minimalist aesthetics with luxury touches\r\n- Use elegant, refined typography\r\n- Include aspirational and transformational imagery\r\n- Apply soft, flattering lighting effects\r\n- Use premium and luxurious visual elements\r\n- Show diverse models representing different skin tones, ages, and beauty standards\r\n- Include authentic beauty routines and self-care moments\r\n- Display culturally relevant beauty practices and aesthetics\r\n- Use inclusive representation that celebrates natural beauty diversity\r\n`,\r\n\r\n  tech: `\r\n**TECH DESIGN DNA (CANVA-QUALITY):**\r\n- Use sophisticated, professional color schemes (modern blues, elegant grays, clean whites)\r\n- Include polished, well-designed layouts with strategic geometric elements and refined shapes\r\n- Apply professional business visual metaphors with premium stock photography quality\r\n- Use modern, bold typography with clear hierarchy (multiple font weights and sizes)\r\n- Include high-quality business imagery: professional office spaces, authentic workplace scenarios\r\n- Apply elegant design effects: subtle gradients, refined shadows, tasteful borders\r\n- Use trustworthy and sophisticated visual language that matches premium Canva templates\r\n- Show diverse tech professionals in polished, well-lit business environments\r\n- Include people using technology in professional, aspirational business contexts\r\n- Display modern office spaces, premium remote work setups, and sophisticated business environments\r\n- Use strategic design elements: elegant shapes, professional patterns, refined layouts\r\n- Create designs that look intentionally crafted and professionally designed\r\n- FOCUS: Premium stock photography quality, sophisticated layouts, Canva-level polish\r\n`,\r\n\r\n  ecommerce: `\r\n**E-COMMERCE DESIGN DNA:**\r\n- Use conversion-focused color schemes (trust blues, urgency reds, success greens)\r\n- Include high-quality product photography with lifestyle context\r\n- Apply clean, scannable layouts with clear hierarchy\r\n- Use action-oriented typography and compelling CTAs\r\n- Include social proof and trust signals\r\n- Apply mobile-first responsive design principles\r\n- Use persuasive and benefit-focused visual language\r\n- Show diverse customers using products in real-life situations\r\n- Include authentic unboxing and product experience moments\r\n- Display culturally relevant usage scenarios and lifestyle contexts\r\n`,\r\n\r\n  healthcare: `\r\n**HEALTHCARE DESIGN DNA:**\r\n- Use calming, trustworthy color palettes (soft blues, greens, whites)\r\n- Include professional medical imagery with human warmth\r\n- Apply clean, accessible design with clear information hierarchy\r\n- Use readable, professional typography\r\n- Include caring and compassionate visual elements\r\n- Apply medical accuracy with approachable aesthetics\r\n- Use reassuring and professional visual language\r\n- Show diverse healthcare professionals and patients\r\n- Include authentic care moments and medical environments\r\n- Display culturally sensitive healthcare interactions and settings\r\n`,\r\n\r\n  education: `\r\n**EDUCATION DESIGN DNA:**\r\n- Use inspiring, growth-focused color schemes (blues, greens, warm oranges)\r\n- Include diverse learning environments and educational moments\r\n- Apply organized, structured layouts with clear learning paths\r\n- Use friendly, accessible typography\r\n- Include knowledge and achievement visual metaphors\r\n- Apply bright, optimistic design elements\r\n- Use encouraging and empowering visual language\r\n- Show students and educators from diverse backgrounds\r\n- Include authentic classroom and learning environments\r\n- Display culturally relevant educational practices and settings\r\n`,\r\n\r\n  default: `\r\n**UNIVERSAL DESIGN DNA:**\r\n- Use brand-appropriate color psychology\r\n- Include authentic, high-quality imagery\r\n- Apply clean, professional aesthetics\r\n- Use readable, accessible typography\r\n- Include relevant industry visual metaphors\r\n- Apply consistent brand visual language\r\n- Use trustworthy and professional design elements\r\n- Show diverse people in authentic, relevant contexts\r\n- Include culturally appropriate imagery and design elements\r\n- Display real human connections and authentic moments\r\n`\r\n};\r\n\r\nexport const QUALITY_ENHANCEMENT_INSTRUCTIONS = `\r\n**DESIGN QUALITY STANDARDS:**\r\n- Ensure all text is perfectly readable with sufficient contrast\r\n- Apply consistent spacing and alignment throughout\r\n- Use high-resolution imagery without pixelation or artifacts\r\n- Maintain visual balance and proper proportions\r\n- Ensure brand elements are prominently but naturally integrated\r\n- Apply professional color grading and visual polish\r\n- Create designs that work across different screen sizes\r\n- Ensure accessibility compliance for color contrast and readability\r\n\r\n**TECHNICAL EXCELLENCE:**\r\n- Generate crisp, high-resolution images suitable for social media\r\n- Apply proper aspect ratios for platform requirements\r\n- Ensure text overlay is perfectly positioned and readable\r\n- Use consistent visual style throughout the design\r\n- Apply professional lighting and shadow effects\r\n- Ensure logo integration feels natural and branded\r\n- Create designs that maintain quality when compressed for social media\r\n`;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AAEM,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC3C,CAAC;AAEM,MAAM,+BAA+B;IAC1C,WAAW,CAAC;;;;;;;;;;;AAWd,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;AAWZ,CAAC;IAEC,UAAU,CAAC;;;;;;;;;;;AAWb,CAAC;AACD;AAEO,MAAM,2BAA2B;IACtC,YAAY,CAAC;;;;;;;;;;;;;AAaf,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;;AAaZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;;;;;AAaX,CAAC;IAEC,MAAM,CAAC;;;;;;;;;;;;;;;AAeT,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,YAAY,CAAC;;;;;;;;;;;;AAYf,CAAC;IAEC,WAAW,CAAC;;;;;;;;;;;;AAYd,CAAC;IAEC,SAAS,CAAC;;;;;;;;;;;;AAYZ,CAAC;AACD;AAEO,MAAM,mCAAmC,CAAC;;;;;;;;;;;;;;;;;;;AAmBjD,CAAC", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-analysis.ts"], "sourcesContent": ["/**\r\n * Design Analysis Utilities\r\n * \r\n * Intelligent analysis and processing of design examples for better AI generation\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design analysis results\r\nexport const DesignAnalysisSchema = z.object({\r\n  colorPalette: z.object({\r\n    primary: z.string().describe('Primary color in hex format'),\r\n    secondary: z.string().describe('Secondary color in hex format'),\r\n    accent: z.string().describe('Accent color in hex format'),\r\n    colorHarmony: z.enum(['complementary', 'analogous', 'triadic', 'monochromatic', 'split-complementary']).describe('Type of color harmony used'),\r\n    colorMood: z.string().describe('Overall mood conveyed by the color scheme')\r\n  }),\r\n  composition: z.object({\r\n    layout: z.enum(['centered', 'left-aligned', 'right-aligned', 'asymmetrical', 'grid-based']).describe('Primary layout structure'),\r\n    visualHierarchy: z.string().describe('How visual hierarchy is established'),\r\n    focalPoint: z.string().describe('Primary focal point and how it\\'s created'),\r\n    balance: z.enum(['symmetrical', 'asymmetrical', 'radial']).describe('Type of visual balance'),\r\n    whitespace: z.enum(['minimal', 'moderate', 'generous']).describe('Use of negative space')\r\n  }),\r\n  typography: z.object({\r\n    primaryFont: z.string().describe('Primary font style/category'),\r\n    hierarchy: z.string().describe('Typographic hierarchy structure'),\r\n    textTreatment: z.string().describe('Special text treatments or effects'),\r\n    readability: z.enum(['high', 'medium', 'stylized']).describe('Text readability level')\r\n  }),\r\n  style: z.object({\r\n    aesthetic: z.string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),\r\n    mood: z.string().describe('Emotional mood and feeling'),\r\n    sophistication: z.enum(['casual', 'professional', 'luxury', 'playful']).describe('Level of sophistication'),\r\n    trends: z.array(z.string()).describe('Current design trends incorporated')\r\n  }),\r\n  effectiveness: z.object({\r\n    attention: z.number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),\r\n    clarity: z.number().min(1).max(10).describe('Message clarity (1-10)'),\r\n    brandAlignment: z.number().min(1).max(10).describe('Brand alignment strength (1-10)'),\r\n    platformOptimization: z.number().min(1).max(10).describe('Platform optimization (1-10)')\r\n  })\r\n});\r\n\r\nexport type DesignAnalysis = z.infer<typeof DesignAnalysisSchema>;\r\n\r\n// Design analysis prompt\r\nconst designAnalysisPrompt = ai.definePrompt({\r\n  name: 'analyzeDesignExample',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string().optional(),\r\n      designContext: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignAnalysisSchema\r\n  },\r\n  prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.\r\n\r\nAnalyze the provided design image and extract detailed insights about its design elements and effectiveness.\r\n\r\nBusiness Context: {{businessType}}\r\nPlatform: {{platform}}\r\nContext: {{designContext}}\r\n\r\nProvide a comprehensive analysis covering:\r\n\r\n1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact\r\n2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space\r\n3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment\r\n4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation\r\n5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization\r\n\r\nBe specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`\r\n});\r\n\r\n/**\r\n * Analyzes a design example to extract key design elements and patterns\r\n */\r\nexport async function analyzeDesignExample(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform?: string,\r\n  context?: string\r\n): Promise<DesignAnalysis> {\r\n  try {\r\n    // For now, return a mock analysis to avoid API issues\r\n    // This can be replaced with actual AI analysis once the prompt system is stable\r\n    return {\r\n      colorPalette: {\r\n        primary: '#FF6B6B',\r\n        secondary: '#4ECDC4',\r\n        accent: '#45B7D1',\r\n        colorHarmony: 'complementary',\r\n        colorMood: 'Energetic and modern'\r\n      },\r\n      composition: {\r\n        layout: 'centered',\r\n        visualHierarchy: 'Clear size-based hierarchy with strong focal point',\r\n        focalPoint: 'Central logo and headline combination',\r\n        balance: 'symmetrical',\r\n        whitespace: 'moderate'\r\n      },\r\n      typography: {\r\n        primaryFont: 'Modern sans-serif',\r\n        hierarchy: 'Large headline, medium subtext, small details',\r\n        textTreatment: 'Bold headlines with subtle shadows',\r\n        readability: 'high'\r\n      },\r\n      style: {\r\n        aesthetic: 'Modern minimalist',\r\n        mood: 'Professional and approachable',\r\n        sophistication: 'professional',\r\n        trends: ['Bold typography', 'Minimalist design', 'High contrast']\r\n      },\r\n      effectiveness: {\r\n        attention: 8,\r\n        clarity: 9,\r\n        brandAlignment: 8,\r\n        platformOptimization: 7\r\n      }\r\n    };\r\n  } catch (error) {\r\n    throw new Error('Failed to analyze design example');\r\n  }\r\n}\r\n\r\n/**\r\n * Selects the best design examples based on content type and platform\r\n */\r\nexport function selectOptimalDesignExamples(\r\n  designExamples: string[],\r\n  analyses: DesignAnalysis[],\r\n  contentType: string,\r\n  platform: string,\r\n  maxExamples: number = 3\r\n): string[] {\r\n  if (!analyses.length || !designExamples.length) {\r\n    return designExamples.slice(0, maxExamples);\r\n  }\r\n\r\n  // Score each design based on relevance and effectiveness\r\n  const scoredExamples = designExamples.map((example, index) => {\r\n    const analysis = analyses[index];\r\n    if (!analysis) return { example, score: 0 };\r\n\r\n    let score = 0;\r\n\r\n    // Weight effectiveness metrics\r\n    score += analysis.effectiveness.attention * 0.3;\r\n    score += analysis.effectiveness.clarity * 0.25;\r\n    score += analysis.effectiveness.brandAlignment * 0.25;\r\n    score += analysis.effectiveness.platformOptimization * 0.2;\r\n\r\n    // Bonus for sophisticated designs\r\n    if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {\r\n      score += 1;\r\n    }\r\n\r\n    // Bonus for modern trends\r\n    score += analysis.style.trends.length * 0.5;\r\n\r\n    return { example, score, analysis };\r\n  });\r\n\r\n  // Sort by score and return top examples\r\n  return scoredExamples\r\n    .sort((a, b) => b.score - a.score)\r\n    .slice(0, maxExamples)\r\n    .map(item => item.example);\r\n}\r\n\r\n/**\r\n * Generates design DNA from analyzed examples\r\n */\r\nexport function extractDesignDNA(analyses: DesignAnalysis[]): string {\r\n  if (!analyses.length) return '';\r\n\r\n  const commonElements = {\r\n    colors: analyses.map(a => a.colorPalette.colorHarmony),\r\n    layouts: analyses.map(a => a.composition.layout),\r\n    aesthetics: analyses.map(a => a.style.aesthetic),\r\n    moods: analyses.map(a => a.style.mood)\r\n  };\r\n\r\n  // Find most common elements\r\n  const mostCommonColor = getMostCommon(commonElements.colors);\r\n  const mostCommonLayout = getMostCommon(commonElements.layouts);\r\n  const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);\r\n  const mostCommonMood = getMostCommon(commonElements.moods);\r\n\r\n  return `\r\n**EXTRACTED DESIGN DNA:**\r\n- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes\r\n- **Layout Pattern**: Favors ${mostCommonLayout} compositions\r\n- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach\r\n- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout\r\n- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation\r\n- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure\r\n`;\r\n}\r\n\r\n/**\r\n * Helper function to find most common element in array\r\n */\r\nfunction getMostCommon<T>(arr: T[]): T {\r\n  const counts = arr.reduce((acc, item) => {\r\n    acc[item as string] = (acc[item as string] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n\r\n  return Object.entries(counts).reduce((a, b) => counts[a[0]] > counts[b[0]] ? a : b)[0] as T;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AACA;;;AAGO,MAAM,uBAAuB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,cAAc,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAiB;YAAa;YAAW;YAAiB;SAAsB,EAAE,QAAQ,CAAC;QACjH,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,QAAQ,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAY;YAAgB;YAAiB;YAAgB;SAAa,EAAE,QAAQ,CAAC;QACrG,iBAAiB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACrC,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,SAAS,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAgB;SAAS,EAAE,QAAQ,CAAC;QACpE,YAAY,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAW;YAAY;SAAW,EAAE,QAAQ,CAAC;IACnE;IACA,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACjC,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACnC,aAAa,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAW,EAAE,QAAQ,CAAC;IAC/D;IACA,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC/B,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,gBAAgB,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAgB;YAAU;SAAU,EAAE,QAAQ,CAAC;QACjF,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACvC;IACA,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACtB,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC9C,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC5C,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QACnD,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;IAC3D;AACF;AAIA,yBAAyB;AACzB,MAAM,uBAAuB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC3C,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACpC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;iHAgBsG,CAAC;AAClH;AAKO,eAAe,qBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAiB,EACjB,OAAgB;IAEhB,IAAI;QACF,sDAAsD;QACtD,gFAAgF;QAChF,OAAO;YACL,cAAc;gBACZ,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,cAAc;gBACd,WAAW;YACb;YACA,aAAa;gBACX,QAAQ;gBACR,iBAAiB;gBACjB,YAAY;gBACZ,SAAS;gBACT,YAAY;YACd;YACA,YAAY;gBACV,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,aAAa;YACf;YACA,OAAO;gBACL,WAAW;gBACX,MAAM;gBACN,gBAAgB;gBAChB,QAAQ;oBAAC;oBAAmB;oBAAqB;iBAAgB;YACnE;YACA,eAAe;gBACb,WAAW;gBACX,SAAS;gBACT,gBAAgB;gBAChB,sBAAsB;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,4BACd,cAAwB,EACxB,QAA0B,EAC1B,WAAmB,EACnB,QAAgB,EAChB,cAAsB,CAAC;IAEvB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,eAAe,MAAM,EAAE;QAC9C,OAAO,eAAe,KAAK,CAAC,GAAG;IACjC;IAEA,yDAAyD;IACzD,MAAM,iBAAiB,eAAe,GAAG,CAAC,CAAC,SAAS;QAClD,MAAM,WAAW,QAAQ,CAAC,MAAM;QAChC,IAAI,CAAC,UAAU,OAAO;YAAE;YAAS,OAAO;QAAE;QAE1C,IAAI,QAAQ;QAEZ,+BAA+B;QAC/B,SAAS,SAAS,aAAa,CAAC,SAAS,GAAG;QAC5C,SAAS,SAAS,aAAa,CAAC,OAAO,GAAG;QAC1C,SAAS,SAAS,aAAa,CAAC,cAAc,GAAG;QACjD,SAAS,SAAS,aAAa,CAAC,oBAAoB,GAAG;QAEvD,kCAAkC;QAClC,IAAI,SAAS,KAAK,CAAC,cAAc,KAAK,kBAAkB,SAAS,KAAK,CAAC,cAAc,KAAK,UAAU;YAClG,SAAS;QACX;QAEA,0BAA0B;QAC1B,SAAS,SAAS,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG;QAExC,OAAO;YAAE;YAAS;YAAO;QAAS;IACpC;IAEA,wCAAwC;IACxC,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;AAC7B;AAKO,SAAS,iBAAiB,QAA0B;IACzD,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO;IAE7B,MAAM,iBAAiB;QACrB,QAAQ,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,YAAY;QACrD,SAAS,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,CAAC,MAAM;QAC/C,YAAY,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,SAAS;QAC/C,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,IAAI;IACvC;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,cAAc,eAAe,MAAM;IAC3D,MAAM,mBAAmB,cAAc,eAAe,OAAO;IAC7D,MAAM,sBAAsB,cAAc,eAAe,UAAU;IACnE,MAAM,iBAAiB,cAAc,eAAe,KAAK;IAEzD,OAAO,CAAC;;oCAE0B,EAAE,gBAAgB;6BACzB,EAAE,iBAAiB;kCACd,EAAE,oBAAoB;gCACxB,EAAE,eAAe;6BACpB,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,eAAe;2BACtC,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,UAAU;AAC/D,CAAC;AACD;AAEA;;CAEC,GACD,SAAS,cAAiB,GAAQ;IAChC,MAAM,SAAS,IAAI,MAAM,CAAC,CAAC,KAAK;QAC9B,GAAG,CAAC,KAAe,GAAG,CAAC,GAAG,CAAC,KAAe,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;AACxF", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-quality.ts"], "sourcesContent": ["/**\r\n * Design Quality Validation and Enhancement\r\n * \r\n * System for validating, scoring, and iteratively improving generated designs\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design quality assessment\r\nexport const DesignQualitySchema = z.object({\r\n  overall: z.object({\r\n    score: z.number().min(1).max(10).describe('Overall design quality score (1-10)'),\r\n    grade: z.enum(['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F']).describe('Letter grade for design quality'),\r\n    summary: z.string().describe('Brief summary of design strengths and weaknesses')\r\n  }),\r\n  composition: z.object({\r\n    score: z.number().min(1).max(10).describe('Composition and layout quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on composition'),\r\n    improvements: z.array(z.string()).describe('Suggested composition improvements')\r\n  }),\r\n  typography: z.object({\r\n    score: z.number().min(1).max(10).describe('Typography quality and readability (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on typography'),\r\n    improvements: z.array(z.string()).describe('Suggested typography improvements')\r\n  }),\r\n  colorDesign: z.object({\r\n    score: z.number().min(1).max(10).describe('Color usage and harmony (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on color choices'),\r\n    improvements: z.array(z.string()).describe('Suggested color improvements')\r\n  }),\r\n  brandAlignment: z.object({\r\n    score: z.number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on brand alignment'),\r\n    improvements: z.array(z.string()).describe('Suggested brand alignment improvements')\r\n  }),\r\n  platformOptimization: z.object({\r\n    score: z.number().min(1).max(10).describe('Platform-specific optimization (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on platform optimization'),\r\n    improvements: z.array(z.string()).describe('Suggested platform optimization improvements')\r\n  }),\r\n  technicalQuality: z.object({\r\n    score: z.number().min(1).max(10).describe('Technical execution quality (1-10)'),\r\n    feedback: z.string().describe('Specific feedback on technical aspects'),\r\n    improvements: z.array(z.string()).describe('Suggested technical improvements')\r\n  }),\r\n  recommendedActions: z.array(z.object({\r\n    priority: z.enum(['high', 'medium', 'low']).describe('Priority level of the action'),\r\n    action: z.string().describe('Specific action to take'),\r\n    expectedImpact: z.string().describe('Expected impact of the action')\r\n  })).describe('Prioritized list of recommended improvements')\r\n});\r\n\r\nexport type DesignQuality = z.infer<typeof DesignQualitySchema>;\r\n\r\n// Design quality assessment prompt\r\nconst designQualityPrompt = ai.definePrompt({\r\n  name: 'assessDesignQuality',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string(),\r\n      visualStyle: z.string(),\r\n      brandColors: z.string().optional(),\r\n      designGoals: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignQualitySchema\r\n  },\r\n  prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.\r\n\r\nEvaluate the provided design image with the highest professional standards.\r\n\r\n**Context:**\r\n- Business Type: {{businessType}}\r\n- Platform: {{platform}}\r\n- Visual Style Goal: {{visualStyle}}\r\n- Brand Colors: {{brandColors}}\r\n- Design Goals: {{designGoals}}\r\n\r\n**Assessment Criteria:**\r\n\r\n1. **Composition & Layout** (25%):\r\n   - Visual hierarchy and flow\r\n   - Balance and proportion\r\n   - Use of negative space\r\n   - Rule of thirds application\r\n   - Focal point effectiveness\r\n\r\n2. **Typography** (20%):\r\n   - Readability and legibility\r\n   - Hierarchy and contrast\r\n   - Font choice appropriateness\r\n   - Text positioning and spacing\r\n   - Accessibility compliance\r\n\r\n3. **Color Design** (20%):\r\n   - Color harmony and theory\r\n   - Brand color integration\r\n   - Contrast and accessibility\r\n   - Psychological impact\r\n   - Platform appropriateness\r\n\r\n4. **Brand Alignment** (15%):\r\n   - Brand consistency\r\n   - Logo integration\r\n   - Visual style adherence\r\n   - Brand personality expression\r\n   - Professional presentation\r\n\r\n5. **Platform Optimization** (10%):\r\n   - Platform-specific best practices\r\n   - Mobile optimization\r\n   - Engagement potential\r\n   - Algorithm friendliness\r\n   - Format appropriateness\r\n\r\n6. **Technical Quality** (10%):\r\n   - Image resolution and clarity\r\n   - Professional finish\r\n   - Technical execution\r\n   - Scalability\r\n   - Print/digital readiness\r\n\r\nProvide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`\r\n});\r\n\r\n/**\r\n * Assesses the quality of a generated design\r\n */\r\nexport async function assessDesignQuality(\r\n  designImageUrl: string,\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  brandColors?: string,\r\n  designGoals?: string\r\n): Promise<DesignQuality> {\r\n  try {\r\n    // For now, return a mock quality assessment to avoid API issues\r\n    // This provides realistic quality scores while the system is being tested\r\n    const baseScore = 7 + Math.random() * 2; // Random score between 7-9\r\n\r\n    return {\r\n      overall: {\r\n        score: Math.round(baseScore * 10) / 10,\r\n        grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',\r\n        summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`\r\n      },\r\n      composition: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Strong visual hierarchy with balanced composition',\r\n        improvements: baseScore < 8 ? ['Improve focal point clarity', 'Enhance visual balance'] : []\r\n      },\r\n      typography: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: 'Clear, readable typography with appropriate hierarchy',\r\n        improvements: baseScore < 8 ? ['Increase text contrast', 'Improve font pairing'] : []\r\n      },\r\n      colorDesign: {\r\n        score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',\r\n        improvements: baseScore < 8 ? ['Enhance color harmony', 'Improve contrast ratios'] : []\r\n      },\r\n      brandAlignment: {\r\n        score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,\r\n        feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',\r\n        improvements: !brandColors ? ['Integrate brand elements', 'Improve brand consistency'] : []\r\n      },\r\n      platformOptimization: {\r\n        score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,\r\n        feedback: `Well optimized for ${platform} format and audience`,\r\n        improvements: baseScore < 8 ? ['Optimize for mobile viewing', 'Improve platform-specific elements'] : []\r\n      },\r\n      technicalQuality: {\r\n        score: Math.round((baseScore + 0.2) * 10) / 10,\r\n        feedback: 'High resolution with professional finish',\r\n        improvements: baseScore < 8 ? ['Improve image resolution', 'Enhance visual polish'] : []\r\n      },\r\n      recommendedActions: [\r\n        {\r\n          priority: baseScore < 7.5 ? 'high' : 'medium',\r\n          action: 'Enhance visual impact through stronger focal points',\r\n          expectedImpact: 'Improved attention and engagement'\r\n        },\r\n        {\r\n          priority: 'medium',\r\n          action: 'Optimize typography for better readability',\r\n          expectedImpact: 'Clearer message communication'\r\n        }\r\n      ].filter(action => baseScore < 8.5 || action.priority === 'medium')\r\n    };\r\n  } catch (error) {\r\n    throw new Error('Failed to assess design quality');\r\n  }\r\n}\r\n\r\n/**\r\n * Generates improvement suggestions based on quality assessment\r\n */\r\nexport function generateImprovementPrompt(quality: DesignQuality): string {\r\n  const highPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'high')\r\n    .map(action => action.action);\r\n\r\n  const mediumPriorityActions = quality.recommendedActions\r\n    .filter(action => action.priority === 'medium')\r\n    .map(action => action.action);\r\n\r\n  let improvementPrompt = `\r\n**DESIGN IMPROVEMENT INSTRUCTIONS:**\r\n\r\nBased on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):\r\n\r\n**CRITICAL IMPROVEMENTS (High Priority):**\r\n${highPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**RECOMMENDED ENHANCEMENTS (Medium Priority):**\r\n${mediumPriorityActions.map(action => `- ${action}`).join('\\n')}\r\n\r\n**SPECIFIC AREA FEEDBACK:**\r\n`;\r\n\r\n  if (quality.composition.score < 7) {\r\n    improvementPrompt += `\r\n**Composition Issues to Address:**\r\n${quality.composition.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.typography.score < 7) {\r\n    improvementPrompt += `\r\n**Typography Issues to Address:**\r\n${quality.typography.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.colorDesign.score < 7) {\r\n    improvementPrompt += `\r\n**Color Design Issues to Address:**\r\n${quality.colorDesign.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  if (quality.brandAlignment.score < 7) {\r\n    improvementPrompt += `\r\n**Brand Alignment Issues to Address:**\r\n${quality.brandAlignment.improvements.map(imp => `- ${imp}`).join('\\n')}\r\n`;\r\n  }\r\n\r\n  return improvementPrompt;\r\n}\r\n\r\n/**\r\n * Determines if a design meets quality standards\r\n */\r\nexport function meetsQualityStandards(quality: DesignQuality, minimumScore: number = 7): boolean {\r\n  return quality.overall.score >= minimumScore &&\r\n    quality.composition.score >= minimumScore - 1 &&\r\n    quality.typography.score >= minimumScore - 1 &&\r\n    quality.brandAlignment.score >= minimumScore - 1;\r\n}\r\n\r\n/**\r\n * Calculates weighted quality score\r\n */\r\nexport function calculateWeightedScore(quality: DesignQuality): number {\r\n  const weights = {\r\n    composition: 0.25,\r\n    typography: 0.20,\r\n    colorDesign: 0.20,\r\n    brandAlignment: 0.15,\r\n    platformOptimization: 0.10,\r\n    technicalQuality: 0.10\r\n  };\r\n\r\n  return (\r\n    quality.composition.score * weights.composition +\r\n    quality.typography.score * weights.typography +\r\n    quality.colorDesign.score * weights.colorDesign +\r\n    quality.brandAlignment.score * weights.brandAlignment +\r\n    quality.platformOptimization.score * weights.platformOptimization +\r\n    quality.technicalQuality.score * weights.technicalQuality\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AAED;AACA;;;AAGO,MAAM,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,OAAO,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAM;YAAK;YAAM;YAAK;YAAM;YAAK;YAAK;SAAI,EAAE,QAAQ,CAAC;QACpE,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC7B,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ,CAAC;QAC1C,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC7C;IACA,oBAAoB,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnC,UAAU,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAM,EAAE,QAAQ,CAAC;QACrD,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC5B,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACtC,IAAI,QAAQ,CAAC;AACf;AAIA,mCAAmC;AACnC,MAAM,sBAAsB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC1C,MAAM;IACN,OAAO;QACL,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAClC;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0GAuD+F,CAAC;AAC3G;AAKO,eAAe,oBACpB,cAAsB,EACtB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,WAAoB,EACpB,WAAoB;IAEpB,IAAI;QACF,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,YAAY,IAAI,KAAK,MAAM,KAAK,GAAG,2BAA2B;QAEpE,OAAO;YACL,SAAS;gBACP,OAAO,KAAK,KAAK,CAAC,YAAY,MAAM;gBACpC,OAAO,aAAa,MAAM,MAAM,aAAa,MAAM,OAAO;gBAC1D,SAAS,CAAC,aAAa,EAAE,YAAY,YAAY,EAAE,aAAa,2CAA2C,CAAC;YAC9G;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAyB,GAAG,EAAE;YAC9F;YACA,YAAY;gBACV,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA0B;iBAAuB,GAAG,EAAE;YACvF;YACA,aAAa;gBACX,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,cAAc,iCAAiC;gBACzD,cAAc,YAAY,IAAI;oBAAC;oBAAyB;iBAA0B,GAAG,EAAE;YACzF;YACA,gBAAgB;gBACd,OAAO,cAAc,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM,KAAK,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBACpG,UAAU,cAAc,wCAAwC;gBAChE,cAAc,CAAC,cAAc;oBAAC;oBAA4B;iBAA4B,GAAG,EAAE;YAC7F;YACA,sBAAsB;gBACpB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM;gBAC5D,UAAU,CAAC,mBAAmB,EAAE,SAAS,oBAAoB,CAAC;gBAC9D,cAAc,YAAY,IAAI;oBAAC;oBAA+B;iBAAqC,GAAG,EAAE;YAC1G;YACA,kBAAkB;gBAChB,OAAO,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,MAAM;gBAC5C,UAAU;gBACV,cAAc,YAAY,IAAI;oBAAC;oBAA4B;iBAAwB,GAAG,EAAE;YAC1F;YACA,oBAAoB;gBAClB;oBACE,UAAU,YAAY,MAAM,SAAS;oBACrC,QAAQ;oBACR,gBAAgB;gBAClB;gBACA;oBACE,UAAU;oBACV,QAAQ;oBACR,gBAAgB;gBAClB;aACD,CAAC,MAAM,CAAC,CAAA,SAAU,YAAY,OAAO,OAAO,QAAQ,KAAK;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,0BAA0B,OAAsB;IAC9D,MAAM,sBAAsB,QAAQ,kBAAkB,CACnD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,QACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,MAAM,wBAAwB,QAAQ,kBAAkB,CACrD,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,UACrC,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;IAE9B,IAAI,oBAAoB,CAAC;;;wDAG6B,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC;;;AAGpH,EAAE,oBAAoB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAG9D,EAAE,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM;;;AAGhE,CAAC;IAEC,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,UAAU,CAAC,KAAK,GAAG,GAAG;QAChC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACpE,CAAC;IACC;IAEA,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG;QACjC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACrE,CAAC;IACC;IAEA,IAAI,QAAQ,cAAc,CAAC,KAAK,GAAG,GAAG;QACpC,qBAAqB,CAAC;;AAE1B,EAAE,QAAQ,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACxE,CAAC;IACC;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,OAAsB,EAAE,eAAuB,CAAC;IACpF,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,gBAC9B,QAAQ,WAAW,CAAC,KAAK,IAAI,eAAe,KAC5C,QAAQ,UAAU,CAAC,KAAK,IAAI,eAAe,KAC3C,QAAQ,cAAc,CAAC,KAAK,IAAI,eAAe;AACnD;AAKO,SAAS,uBAAuB,OAAsB;IAC3D,MAAM,UAAU;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,OACE,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,UAAU,CAAC,KAAK,GAAG,QAAQ,UAAU,GAC7C,QAAQ,WAAW,CAAC,KAAK,GAAG,QAAQ,WAAW,GAC/C,QAAQ,cAAc,CAAC,KAAK,GAAG,QAAQ,cAAc,GACrD,QAAQ,oBAAoB,CAAC,KAAK,GAAG,QAAQ,oBAAoB,GACjE,QAAQ,gBAAgB,CAAC,KAAK,GAAG,QAAQ,gBAAgB;AAE7D", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-creative-asset.ts"], "sourcesContent": ["\r\n// src/ai/flows/generate-creative-asset.ts\r\n'use server';\r\n\r\n/**\r\n * @fileOverview A Genkit flow for generating a creative asset (image or video)\r\n * based on a user's prompt, an optional reference image, and brand profile settings.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport type { BrandProfile } from '@/lib/types';\r\nimport { MediaPart } from 'genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport {\r\n    ADVANCED_DESIGN_PRINCIPLES,\r\n    PLATFORM_SPECIFIC_GUIDELINES,\r\n    BUSINESS_TYPE_DESIGN_DNA,\r\n    QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\nimport {\r\n    analyzeDesignExample,\r\n    selectOptimalDesignExamples,\r\n    extractDesignDNA,\r\n    type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n    assessDesignQuality,\r\n    generateImprovementPrompt,\r\n    meetsQualityStandards,\r\n    type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\n\r\n// Define the input schema for the creative asset generation flow.\r\nconst CreativeAssetInputSchema = z.object({\r\n    prompt: z.string().describe('The main text prompt describing the desired asset.'),\r\n    outputType: z.enum(['image', 'video']).describe('The type of asset to generate.'),\r\n    referenceAssetUrl: z.string().nullable().describe('An optional reference image or video as a data URI.'),\r\n    useBrandProfile: z.boolean().describe('Whether to apply the brand profile.'),\r\n    brandProfile: z.custom<BrandProfile>().nullable().describe('The brand profile object.'),\r\n    maskDataUrl: z.string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),\r\n    aspectRatio: z.enum(['16:9', '9:16']).optional().describe('The aspect ratio for video generation.'),\r\n    preferredModel: z.string().optional().describe('Preferred model for generation (e.g., gemini-2.5-flash-image-preview).'),\r\n});\r\nexport type CreativeAssetInput = z.infer<typeof CreativeAssetInputSchema>;\r\n\r\n// Define the output schema for the creative asset generation flow.\r\nconst CreativeAssetOutputSchema = z.object({\r\n    imageUrl: z.string().nullable().describe('The data URI of the generated image, if applicable.'),\r\n    videoUrl: z.string().nullable().describe('The data URI of the generated video, if applicable.'),\r\n    aiExplanation: z.string().describe('A brief explanation from the AI about what it created.'),\r\n});\r\nexport type CreativeAsset = z.infer<typeof CreativeAssetOutputSchema>;\r\n\r\n/**\r\n * An exported wrapper function that calls the creative asset generation flow.\r\n * @param input - The input data for asset generation.\r\n * @returns A promise that resolves to the generated asset details.\r\n */\r\nexport async function generateCreativeAsset(input: CreativeAssetInput): Promise<CreativeAsset> {\r\n    return generateCreativeAssetFlow(input);\r\n}\r\n\r\n\r\n/**\r\n * Helper function to download video and convert to data URI\r\n */\r\nasync function videoToDataURI(videoPart: MediaPart): Promise<string> {\r\n    if (!videoPart.media || !videoPart.media.url) {\r\n        throw new Error('Media URL not found in video part.');\r\n    }\r\n\r\n    const fetch = (await import('node-fetch')).default;\r\n    const videoDownloadResponse = await fetch(\r\n        `${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`\r\n    );\r\n\r\n    if (!videoDownloadResponse.ok) {\r\n        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);\r\n    }\r\n\r\n    const videoBuffer = await videoDownloadResponse.arrayBuffer();\r\n    const base64Video = Buffer.from(videoBuffer).toString('base64');\r\n    const contentType = videoPart.media.contentType || 'video/mp4';\r\n\r\n    return `data:${contentType};base64,${base64Video}`;\r\n}\r\n\r\n/**\r\n * Extracts text in quotes and the remaining prompt.\r\n */\r\nconst extractQuotedText = (prompt: string): { imageText: string | null; remainingPrompt: string } => {\r\n    const quoteRegex = /\"([^\"]*)\"/;\r\n    const match = prompt.match(quoteRegex);\r\n    if (match) {\r\n        return {\r\n            imageText: match[1],\r\n            remainingPrompt: prompt.replace(quoteRegex, '').trim()\r\n        };\r\n    }\r\n    return {\r\n        imageText: null,\r\n        remainingPrompt: prompt\r\n    };\r\n};\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n    for (let i = 0; i < retries; i++) {\r\n        try {\r\n            const result = await ai.generate(request);\r\n            return result;\r\n        } catch (e: any) {\r\n            if (e.message && e.message.includes('503') && i < retries - 1) {\r\n                await new Promise(resolve => setTimeout(resolve, delay));\r\n            } else {\r\n                if (e.message && e.message.includes('503')) {\r\n                    throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n                }\r\n                if (e.message && e.message.includes('429')) {\r\n                    throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n                }\r\n                throw e; // Rethrow other errors immediately\r\n            }\r\n        }\r\n    }\r\n    // This line should not be reachable if retries are configured, but as a fallback:\r\n    throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n    const match = dataURI.match(/^data:(.*?);/);\r\n    return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n\r\n/**\r\n * The core Genkit flow for generating a creative asset.\r\n */\r\nconst generateCreativeAssetFlow = ai.defineFlow(\r\n    {\r\n        name: 'generateCreativeAssetFlow',\r\n        inputSchema: CreativeAssetInputSchema,\r\n        outputSchema: CreativeAssetOutputSchema,\r\n    },\r\n    async (input) => {\r\n        const promptParts: (string | { text: string } | { media: { url: string; contentType?: string } })[] = [];\r\n        let textPrompt = '';\r\n\r\n        const { imageText, remainingPrompt } = extractQuotedText(input.prompt);\r\n\r\n        if (input.maskDataUrl && input.referenceAssetUrl) {\r\n            // This is an inpainting request.\r\n            textPrompt = `You are an expert image editor performing a precise inpainting task.\r\nYou will be given an original image, a mask, and a text prompt.\r\nYour task is to modify the original image *only* in the areas designated by the black region of the mask.\r\nThe rest of the image must remain absolutely unchanged.\r\nIf the prompt is a \"remove\" or \"delete\" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.\r\nThe user's instruction for the masked area is: \"${remainingPrompt}\".\r\nRecreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;\r\n\r\n            promptParts.push({ text: textPrompt });\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n            promptParts.push({ media: { url: input.maskDataUrl, contentType: getMimeTypeFromDataURI(input.maskDataUrl) } });\r\n\r\n        } else if (input.referenceAssetUrl) {\r\n            // This is a generation prompt with a reference asset (image or video).\r\n            let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.\r\nYour task is to generate a new asset that is inspired by the reference asset and follows the new instructions.\r\n\r\nYour primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.\r\nAnalyze the user's prompt for common editing terminology and apply it creatively. For example:\r\n- If asked to \"change the background,\" intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.\r\n- If asked to \"make the logo bigger\" or \"change the text color,\" perform those specific edits while maintaining the overall composition.\r\n- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.\r\n\r\nThe user's instruction is: \"${remainingPrompt}\"`;\r\n\r\n            if (imageText) {\r\n                referencePrompt += `\\n\\n**Explicit Text Overlay:** The user has provided specific text in quotes: \"${imageText}\". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`\r\n            }\r\n\r\n            if (input.outputType === 'video') {\r\n                referencePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (imageText) {\r\n                    referencePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n            }\r\n\r\n            if (input.useBrandProfile && input.brandProfile) {\r\n                const bp = input.brandProfile;\r\n                let brandGuidelines = '\\n\\n**Brand Guidelines:**';\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                    brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`\r\n                }\r\n                referencePrompt += brandGuidelines;\r\n            }\r\n\r\n            textPrompt = referencePrompt;\r\n            if (textPrompt) {\r\n                promptParts.push({ text: textPrompt });\r\n            }\r\n            promptParts.push({ media: { url: input.referenceAssetUrl, contentType: getMimeTypeFromDataURI(input.referenceAssetUrl) } });\r\n\r\n        } else if (input.useBrandProfile && input.brandProfile) {\r\n            // This is a new, on-brand asset generation with advanced design principles.\r\n            const bp = input.brandProfile;\r\n\r\n            // Get business-specific design DNA\r\n            const businessDNA = BUSINESS_TYPE_DESIGN_DNA[bp.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n            let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.\r\n\r\nBUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})\r\nCONTENT: \"${remainingPrompt}\"\r\nSTYLE: ${bp.visualStyle}, modern, clean, professional\r\n\r\nFORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}\r\n\r\nBRAND COLORS (use prominently):\r\n${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}\r\n${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}\r\n${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}\r\n\r\nREQUIREMENTS:\r\n- High-quality, professional design\r\n- ${bp.visualStyle} aesthetic\r\n- Clean, modern layout\r\n- Perfect for ${bp.businessType} business\r\n- Brand colors prominently featured\r\n- Professional social media appearance`;\r\n\r\n            // Intelligent design examples processing\r\n            let designDNA = '';\r\n            let selectedExamples: string[] = [];\r\n\r\n            if (bp.designExamples && bp.designExamples.length > 0) {\r\n                try {\r\n                    // Analyze design examples for intelligent processing\r\n                    const analyses: DesignAnalysis[] = [];\r\n                    for (const example of bp.designExamples.slice(0, 3)) { // Limit for performance\r\n                        try {\r\n                            const analysis = await analyzeDesignExample(\r\n                                example,\r\n                                bp.businessType,\r\n                                'creative-studio',\r\n                                `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`\r\n                            );\r\n                            analyses.push(analysis);\r\n                        } catch (error) {\r\n                        }\r\n                    }\r\n\r\n                    if (analyses.length > 0) {\r\n                        // Extract design DNA from analyzed examples\r\n                        designDNA = extractDesignDNA(analyses);\r\n\r\n                        // Select optimal examples based on analysis\r\n                        selectedExamples = selectOptimalDesignExamples(\r\n                            bp.designExamples,\r\n                            analyses,\r\n                            remainingPrompt,\r\n                            'creative-studio',\r\n                            2\r\n                        );\r\n                    } else {\r\n                        selectedExamples = bp.designExamples.slice(0, 2);\r\n                    }\r\n                } catch (error) {\r\n                    selectedExamples = bp.designExamples.slice(0, 2);\r\n                }\r\n\r\n                onBrandPrompt += `\\n**STYLE REFERENCE:**\r\nUse the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.\r\n\r\n${designDNA}`;\r\n            }\r\n\r\n            if (input.outputType === 'image') {\r\n                onBrandPrompt += `\\n- **Text Overlay Requirements:** ${imageText ? `\r\n                  * Display this EXACT text: \"${imageText}\"\r\n                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters\r\n                  * Make text LARGE and BOLD for mobile readability\r\n                  * Apply high contrast (minimum 4.5:1 ratio) between text and background\r\n                  * Add text shadows, outlines, or semi-transparent backgrounds for readability\r\n                  * Position text using rule of thirds for optimal composition\r\n                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;\r\n                onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;\r\n                onBrandPrompt += `\\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;\r\n\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                }\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                onBrandPrompt += `\\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    onBrandPrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    onBrandPrompt += `\\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`\r\n                }\r\n                if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;\r\n                    promptParts.push({ media: { url: bp.logoDataUrl, contentType: getMimeTypeFromDataURI(bp.logoDataUrl) } });\r\n                } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {\r\n                    onBrandPrompt += `\\n- **Brand Identity:** Create a design that represents the brand identity and style.`;\r\n                }\r\n\r\n                // Add selected design examples as reference\r\n                selectedExamples.forEach(designExample => {\r\n                    promptParts.push({ media: { url: designExample, contentType: getMimeTypeFromDataURI(designExample) } });\r\n                });\r\n\r\n                textPrompt = onBrandPrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        } else {\r\n            // This is a new, un-branded, creative prompt.\r\n            let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: \"${remainingPrompt}\".\r\n\r\n⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;\r\n\r\n            if (input.outputType === 'image' && imageText) {\r\n                creativePrompt += `\r\n\r\n🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨\r\n\r\n⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:\r\n- NEVER add \"Flex Your Finances\" or any financial terms\r\n- NEVER add \"Payroll Banking Simplified\" or banking phrases\r\n- NEVER add \"Banking Made Easy\" or similar taglines\r\n- NEVER add company descriptions or service explanations\r\n- NEVER add marketing copy or promotional text\r\n- NEVER add placeholder text or sample content\r\n- NEVER create fake headlines or taglines\r\n- NEVER add descriptive text about the business\r\n- NEVER add ANY text except what is specified below\r\n\r\n🎯 ONLY THIS TEXT IS ALLOWED: \"${imageText}\"\r\n🎯 REPEAT: ONLY THIS TEXT: \"${imageText}\"\r\n🎯 NO OTHER TEXT PERMITTED: \"${imageText}\"\r\n\r\n🌍 ENGLISH ONLY REQUIREMENT:\r\n- ALL text must be in clear, readable English\r\n- NO foreign languages (Arabic, Chinese, Hindi, etc.)\r\n- NO special characters, symbols, or corrupted text\r\n- NO accents or diacritical marks\r\n\r\nOverlay ONLY the following text onto the asset: \"${imageText}\".\r\nDO NOT ADD ANY OTHER TEXT.\r\nEnsure the text is readable and well-composed.`\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            } else { // Video\r\n                creativePrompt += `\\n\\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;\r\n                if (input.aspectRatio === '16:9') {\r\n                    creativePrompt += ' The video should have relevant sound.';\r\n                }\r\n                if (imageText) {\r\n                    creativePrompt += `\\n\\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: \"${imageText}\". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;\r\n                }\r\n                textPrompt = creativePrompt;\r\n                if (textPrompt) {\r\n                    promptParts.unshift({ text: textPrompt });\r\n                }\r\n            }\r\n        }\r\n\r\n        const aiExplanationPrompt = ai.definePrompt({\r\n            name: 'creativeAssetExplanationPrompt',\r\n            prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: \"I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo.\"`\r\n        });\r\n\r\n        const explanationResult = await aiExplanationPrompt();\r\n\r\n        try {\r\n            if (input.outputType === 'image') {\r\n                // Generate image with quality validation\r\n                let finalImageUrl: string | null = null;\r\n                let attempts = 0;\r\n                const maxAttempts = 2;\r\n\r\n                while (attempts < maxAttempts && !finalImageUrl) {\r\n                    attempts++;\r\n\r\n                    // Determine which model to use based on preferred model parameter\r\n                    let modelToUse = 'googleai/gemini-2.0-flash-preview-image-generation'; // Default\r\n\r\n                    if (input.preferredModel) {\r\n                        // Map Gemini model names to Genkit model identifiers\r\n                        const modelMapping: Record<string, string> = {\r\n                            'gemini-2.5-flash-image-preview': 'googleai/gemini-2.5-flash-image-preview',\r\n                            'gemini-2.0-flash-preview-image-generation': 'googleai/gemini-2.0-flash-preview-image-generation',\r\n                            'gemini-2.5-flash': 'googleai/gemini-2.5-flash'\r\n                        };\r\n\r\n                        modelToUse = modelMapping[input.preferredModel] || modelToUse;\r\n                    }\r\n\r\n                    const { media } = await generateWithRetry({\r\n                        model: modelToUse,\r\n                        prompt: promptParts,\r\n                        config: {\r\n                            responseModalities: ['TEXT', 'IMAGE'],\r\n                        },\r\n                    });\r\n\r\n                    let imageUrl = media?.url ?? null;\r\n                    if (!imageUrl) {\r\n                        if (attempts === maxAttempts) {\r\n                            throw new Error('Failed to generate image');\r\n                        }\r\n                        continue;\r\n                    }\r\n\r\n                    // Apply aspect ratio correction if needed\r\n                    if (input.aspectRatio && input.aspectRatio !== '1:1') {\r\n                        try {\r\n                            const { cropImageFromUrl } = await import('@/lib/image-processing');\r\n                            // Map aspect ratio to platform for cropping\r\n                            const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' :\r\n                                input.aspectRatio === '9:16' ? 'story' : 'instagram';\r\n                            imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);\r\n                        } catch (cropError) {\r\n                            // Continue with original image if cropping fails\r\n                        }\r\n                    }\r\n\r\n                    // Quality validation for brand profile designs\r\n                    if (input.useBrandProfile && input.brandProfile && attempts === 1) {\r\n                        try {\r\n                            const quality = await assessDesignQuality(\r\n                                imageUrl,\r\n                                input.brandProfile.businessType,\r\n                                'creative-studio',\r\n                                input.brandProfile.visualStyle,\r\n                                undefined,\r\n                                `Creative asset: ${remainingPrompt}`\r\n                            );\r\n\r\n                            // If quality is acceptable, use this design\r\n                            if (meetsQualityStandards(quality, 6)) { // Slightly lower threshold for creative assets\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n\r\n                            // If quality is poor and we have attempts left, try to improve\r\n                            if (attempts < maxAttempts) {\r\n\r\n                                // Add improvement instructions to prompt\r\n                                const improvementInstructions = generateImprovementPrompt(quality);\r\n                                const improvedPrompt = `${promptParts[0].text}\\n\\n${improvementInstructions}`;\r\n                                promptParts[0] = { text: improvedPrompt };\r\n                                continue;\r\n                            } else {\r\n                                finalImageUrl = imageUrl;\r\n                                break;\r\n                            }\r\n                        } catch (qualityError) {\r\n                            finalImageUrl = imageUrl;\r\n                            break;\r\n                        }\r\n                    } else {\r\n                        finalImageUrl = imageUrl;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                return {\r\n                    imageUrl: finalImageUrl,\r\n                    videoUrl: null,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated image based on your prompt.\"\r\n                };\r\n            } else { // Video generation\r\n                const isVertical = input.aspectRatio === '9:16';\r\n\r\n                const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';\r\n                const config: Record<string, any> = {};\r\n                if (isVertical) {\r\n                    config.aspectRatio = '9:16';\r\n                    config.durationSeconds = 8;\r\n                }\r\n\r\n                const result = await generateWithRetry({\r\n                    model,\r\n                    prompt: promptParts,\r\n                    config,\r\n                });\r\n\r\n                let operation = result.operation;\r\n\r\n                if (!operation) {\r\n                    throw new Error('The video generation process did not start correctly. Please try again.');\r\n                }\r\n\r\n                // Poll for completion\r\n                while (!operation.done) {\r\n                    await new Promise(resolve => setTimeout(resolve, 5000)); // wait 5s\r\n                    operation = await ai.checkOperation(operation);\r\n                }\r\n\r\n                if (operation.error) {\r\n                    throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);\r\n                }\r\n\r\n                const videoPart = operation.output?.message?.content.find(p => !!p.media);\r\n                if (!videoPart || !videoPart.media) {\r\n                    throw new Error('Video generation completed, but the final video file could not be found.');\r\n                }\r\n\r\n                const videoDataUrl = await videoToDataURI(videoPart);\r\n\r\n                return {\r\n                    imageUrl: null,\r\n                    videoUrl: videoDataUrl,\r\n                    aiExplanation: explanationResult.output ?? \"Here is the generated video based on your prompt.\"\r\n                };\r\n            }\r\n        } catch (e: any) {\r\n            // Ensure a user-friendly error is thrown\r\n            const message = e.message || \"An unknown error occurred during asset generation.\";\r\n            throw new Error(message);\r\n        }\r\n    }\r\n);\r\n\r\n\r\n"], "names": [], "mappings": "AACA,0CAA0C;;;;;;AAG1C;;;CAGC,GAED;AACA;AAIA;AAMA;AAMA;;;;;;;;;AAOA,kEAAkE;AAClE,MAAM,2BAA2B,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,YAAY,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAQ,EAAE,QAAQ,CAAC;IAChD,mBAAmB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAClD,iBAAiB,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;IACtC,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAiB,QAAQ,GAAG,QAAQ,CAAC;IAC3D,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvD,aAAa,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;KAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAC1D,gBAAgB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACnD;AAGA,mEAAmE;AACnE,MAAM,4BAA4B,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,eAAe,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACvC;AAQO,eAAe,sBAAsB,KAAyB;IACjE,OAAO,0BAA0B;AACrC;AAGA;;CAEC,GACD,eAAe,eAAe,SAAoB;IAC9C,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG,EAAE;QAC1C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,QAAQ,CAAC,+IAA0B,EAAE,OAAO;IAClD,MAAM,wBAAwB,MAAM,MAChC,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;IAG9D,IAAI,CAAC,sBAAsB,EAAE,EAAE;QAC3B,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,sBAAsB,UAAU,EAAE;IACnF;IAEA,MAAM,cAAc,MAAM,sBAAsB,WAAW;IAC3D,MAAM,cAAc,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;IACtD,MAAM,cAAc,UAAU,KAAK,CAAC,WAAW,IAAI;IAEnD,OAAO,CAAC,KAAK,EAAE,YAAY,QAAQ,EAAE,aAAa;AACtD;AAEA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACvB,MAAM,aAAa;IACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,OAAO;QACP,OAAO;YACH,WAAW,KAAK,CAAC,EAAE;YACnB,iBAAiB,OAAO,OAAO,CAAC,YAAY,IAAI,IAAI;QACxD;IACJ;IACA,OAAO;QACH,WAAW;QACX,iBAAiB;IACrB;AACJ;AAEA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAChF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAC9B,IAAI;YACA,MAAM,SAAS,MAAM,qHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACX,EAAE,OAAO,GAAQ;YACb,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACrD,OAAO;gBACH,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACxC,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACxC,MAAM,IAAI,MAAM;gBACpB;gBACA,MAAM,GAAG,mCAAmC;YAChD;QACJ;IACJ;IACA,kFAAkF;IAClF,MAAM,IAAI,MAAM;AACpB;AAEA,MAAM,yBAAyB,CAAC;IAC5B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG,4BAA4B,sBAAsB;AAChF;AAGA;;CAEC,GACD,MAAM,4BAA4B,qHAAA,CAAA,KAAE,CAAC,UAAU,CAC3C;IACI,MAAM;IACN,aAAa;IACb,cAAc;AAClB,GACA,OAAO;IACH,MAAM,cAAgG,EAAE;IACxG,IAAI,aAAa;IAEjB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,kBAAkB,MAAM,MAAM;IAErE,IAAI,MAAM,WAAW,IAAI,MAAM,iBAAiB,EAAE;QAC9C,iCAAiC;QACjC,aAAa,CAAC;;;;;gDAKsB,EAAE,gBAAgB;+KAC6G,CAAC;QAEpK,YAAY,IAAI,CAAC;YAAE,MAAM;QAAW;QACpC,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,iBAAiB;gBAAE,aAAa,uBAAuB,MAAM,iBAAiB;YAAE;QAAE;QACzH,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,WAAW;gBAAE,aAAa,uBAAuB,MAAM,WAAW;YAAE;QAAE;IAEjH,OAAO,IAAI,MAAM,iBAAiB,EAAE;QAChC,uEAAuE;QACvE,IAAI,kBAAkB,CAAC;;;;;;;;;4BASP,EAAE,gBAAgB,CAAC,CAAC;QAEpC,IAAI,WAAW;YACX,mBAAmB,CAAC,+EAA+E,EAAE,UAAU,sIAAsI,CAAC;QAC1P;QAEA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,mBAAmB,CAAC,uQAAuQ,CAAC;YAC5R,IAAI,WAAW;gBACX,mBAAmB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC/P;QACJ;QAEA,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,EAAE;YAC7C,MAAM,KAAK,MAAM,YAAY;YAC7B,IAAI,kBAAkB;YAEtB,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;gBACvG,mBAAmB,CAAC,2EAA2E,CAAC;YACpG,OAAO,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBACnE,mBAAmB,CAAC,gGAAgG,CAAC;YACzH;YACA,mBAAmB;QACvB;QAEA,aAAa;QACb,IAAI,YAAY;YACZ,YAAY,IAAI,CAAC;gBAAE,MAAM;YAAW;QACxC;QACA,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK,MAAM,iBAAiB;gBAAE,aAAa,uBAAuB,MAAM,iBAAiB;YAAE;QAAE;IAE7H,OAAO,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,EAAE;QACpD,4EAA4E;QAC5E,MAAM,KAAK,MAAM,YAAY;QAE7B,mCAAmC;QACnC,MAAM,cAAc,uJAAA,CAAA,2BAAwB,CAAC,GAAG,YAAY,CAA0C,IAAI,uJAAA,CAAA,2BAAwB,CAAC,OAAO;QAE1I,IAAI,gBAAgB,CAAC,6CAA6C,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,IAAI,gBAAgB;;UAEjI,EAAE,GAAG,YAAY,IAAI,wBAAwB,EAAE,EAAE,GAAG,YAAY,CAAC;UACjE,EAAE,gBAAgB;OACrB,EAAE,GAAG,WAAW,CAAC;;QAEhB,EAAE,MAAM,WAAW,GAAG,GAAG,MAAM,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB;;;AAGxF,EAAE,GAAG,YAAY,GAAG,CAAC,WAAW,EAAE,GAAG,YAAY,EAAE,GAAG,GAAG;AACzD,EAAE,GAAG,WAAW,GAAG,CAAC,UAAU,EAAE,GAAG,WAAW,EAAE,GAAG,GAAG;AACtD,EAAE,GAAG,eAAe,GAAG,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,GAAG,GAAG;;;;EAIhE,EAAE,GAAG,WAAW,CAAC;;cAEL,EAAE,GAAG,YAAY,CAAC;;sCAEM,CAAC;QAE3B,yCAAyC;QACzC,IAAI,YAAY;QAChB,IAAI,mBAA6B,EAAE;QAEnC,IAAI,GAAG,cAAc,IAAI,GAAG,cAAc,CAAC,MAAM,GAAG,GAAG;YACnD,IAAI;gBACA,qDAAqD;gBACrD,MAAM,WAA6B,EAAE;gBACrC,KAAK,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAI;oBACjD,IAAI;wBACA,MAAM,WAAW,MAAM,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EACtC,SACA,GAAG,YAAY,EACf,mBACA,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,iBAAiB;wBAElE,SAAS,IAAI,CAAC;oBAClB,EAAE,OAAO,OAAO,CAChB;gBACJ;gBAEA,IAAI,SAAS,MAAM,GAAG,GAAG;oBACrB,4CAA4C;oBAC5C,YAAY,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE;oBAE7B,4CAA4C;oBAC5C,mBAAmB,CAAA,GAAA,0IAAA,CAAA,8BAA2B,AAAD,EACzC,GAAG,cAAc,EACjB,UACA,iBACA,mBACA;gBAER,OAAO;oBACH,mBAAmB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG;gBAClD;YACJ,EAAE,OAAO,OAAO;gBACZ,mBAAmB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG;YAClD;YAEA,iBAAiB,CAAC;;;AAGlC,EAAE,WAAW;QACD;QAEA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,iBAAiB,CAAC,mCAAmC,EAAE,YAAY,CAAC;8CACtC,EAAE,UAAU;;;;;;wEAMc,CAAC,GAAG,yCAAyC;YACrG,iBAAiB,CAAC,8IAA8I,CAAC;YACjK,iBAAiB,CAAC,qJAAqJ,CAAC;YAExK,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;YAC3G;YACA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ,OAAO;YACH,iBAAiB,CAAC,uQAAuQ,CAAC;YAC1R,IAAI,MAAM,WAAW,KAAK,QAAQ;gBAC9B,iBAAiB;YACrB;YACA,IAAI,WAAW;gBACX,iBAAiB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC7P;YACA,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBAC7D,iBAAiB,CAAC,uFAAuF,CAAC;gBAC1G,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK,GAAG,WAAW;wBAAE,aAAa,uBAAuB,GAAG,WAAW;oBAAE;gBAAE;YAC3G,OAAO,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,kBAAkB;gBACnE,iBAAiB,CAAC,qFAAqF,CAAC;YAC5G;YAEA,4CAA4C;YAC5C,iBAAiB,OAAO,CAAC,CAAA;gBACrB,YAAY,IAAI,CAAC;oBAAE,OAAO;wBAAE,KAAK;wBAAe,aAAa,uBAAuB;oBAAe;gBAAE;YACzG;YAEA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ;IACJ,OAAO;QACH,8CAA8C;QAC9C,IAAI,iBAAiB,CAAC,4IAA4I,EAAE,MAAM,UAAU,CAAC,sCAAsC,EAAE,gBAAgB;;;;;;;;;;;4FAW7J,CAAC;QAEjF,IAAI,MAAM,UAAU,KAAK,WAAW,WAAW;YAC3C,kBAAkB,CAAC;;;;;;;;;;;;;;;+BAeJ,EAAE,UAAU;4BACf,EAAE,UAAU;6BACX,EAAE,UAAU;;;;;;;;iDAQQ,EAAE,UAAU;;8CAEf,CAAC;YAC/B,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ,OAAO;YACH,kBAAkB,CAAC,uQAAuQ,CAAC;YAC3R,IAAI,MAAM,WAAW,KAAK,QAAQ;gBAC9B,kBAAkB;YACtB;YACA,IAAI,WAAW;gBACX,kBAAkB,CAAC,qGAAqG,EAAE,UAAU,qHAAqH,CAAC;YAC9P;YACA,aAAa;YACb,IAAI,YAAY;gBACZ,YAAY,OAAO,CAAC;oBAAE,MAAM;gBAAW;YAC3C;QACJ;IACJ;IAEA,MAAM,sBAAsB,qHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;QACxC,MAAM;QACN,QAAQ,CAAC,uBAAuB,EAAE,MAAM,UAAU,CAAC,8LAA8L,CAAC;IACtP;IAEA,MAAM,oBAAoB,MAAM;IAEhC,IAAI;QACA,IAAI,MAAM,UAAU,KAAK,SAAS;YAC9B,yCAAyC;YACzC,IAAI,gBAA+B;YACnC,IAAI,WAAW;YACf,MAAM,cAAc;YAEpB,MAAO,WAAW,eAAe,CAAC,cAAe;gBAC7C;gBAEA,kEAAkE;gBAClE,IAAI,aAAa,sDAAsD,UAAU;gBAEjF,IAAI,MAAM,cAAc,EAAE;oBACtB,qDAAqD;oBACrD,MAAM,eAAuC;wBACzC,kCAAkC;wBAClC,6CAA6C;wBAC7C,oBAAoB;oBACxB;oBAEA,aAAa,YAAY,CAAC,MAAM,cAAc,CAAC,IAAI;gBACvD;gBAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;oBACtC,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACJ,oBAAoB;4BAAC;4BAAQ;yBAAQ;oBACzC;gBACJ;gBAEA,IAAI,WAAW,OAAO,OAAO;gBAC7B,IAAI,CAAC,UAAU;oBACX,IAAI,aAAa,aAAa;wBAC1B,MAAM,IAAI,MAAM;oBACpB;oBACA;gBACJ;gBAEA,0CAA0C;gBAC1C,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,KAAK,OAAO;oBAClD,IAAI;wBACA,MAAM,EAAE,gBAAgB,EAAE,GAAG;wBAC7B,4CAA4C;wBAC5C,MAAM,sBAAsB,MAAM,WAAW,KAAK,SAAS,aACvD,MAAM,WAAW,KAAK,SAAS,UAAU;wBAC7C,WAAW,MAAM,iBAAiB,UAAU;oBAChD,EAAE,OAAO,WAAW;oBAChB,iDAAiD;oBACrD;gBACJ;gBAEA,+CAA+C;gBAC/C,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY,IAAI,aAAa,GAAG;oBAC/D,IAAI;wBACA,MAAM,UAAU,MAAM,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD,EACpC,UACA,MAAM,YAAY,CAAC,YAAY,EAC/B,mBACA,MAAM,YAAY,CAAC,WAAW,EAC9B,WACA,CAAC,gBAAgB,EAAE,iBAAiB;wBAGxC,4CAA4C;wBAC5C,IAAI,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,IAAI;4BACnC,gBAAgB;4BAChB;wBACJ;wBAEA,+DAA+D;wBAC/D,IAAI,WAAW,aAAa;4BAExB,yCAAyC;4BACzC,MAAM,0BAA0B,CAAA,GAAA,yIAAA,CAAA,4BAAyB,AAAD,EAAE;4BAC1D,MAAM,iBAAiB,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,yBAAyB;4BAC7E,WAAW,CAAC,EAAE,GAAG;gCAAE,MAAM;4BAAe;4BACxC;wBACJ,OAAO;4BACH,gBAAgB;4BAChB;wBACJ;oBACJ,EAAE,OAAO,cAAc;wBACnB,gBAAgB;wBAChB;oBACJ;gBACJ,OAAO;oBACH,gBAAgB;oBAChB;gBACJ;YACJ;YAEA,OAAO;gBACH,UAAU;gBACV,UAAU;gBACV,eAAe,kBAAkB,MAAM,IAAI;YAC/C;QACJ,OAAO;YACH,MAAM,aAAa,MAAM,WAAW,KAAK;YAEzC,MAAM,QAAQ,aAAa,kCAAkC;YAC7D,MAAM,SAA8B,CAAC;YACrC,IAAI,YAAY;gBACZ,OAAO,WAAW,GAAG;gBACrB,OAAO,eAAe,GAAG;YAC7B;YAEA,MAAM,SAAS,MAAM,kBAAkB;gBACnC;gBACA,QAAQ;gBACR;YACJ;YAEA,IAAI,YAAY,OAAO,SAAS;YAEhC,IAAI,CAAC,WAAW;gBACZ,MAAM,IAAI,MAAM;YACpB;YAEA,sBAAsB;YACtB,MAAO,CAAC,UAAU,IAAI,CAAE;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,UAAU;gBACnE,YAAY,MAAM,qHAAA,CAAA,KAAE,CAAC,cAAc,CAAC;YACxC;YAEA,IAAI,UAAU,KAAK,EAAE;gBACjB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC5F;YAEA,MAAM,YAAY,UAAU,MAAM,EAAE,SAAS,QAAQ,KAAK,CAAA,IAAK,CAAC,CAAC,EAAE,KAAK;YACxE,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,EAAE;gBAChC,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,eAAe,MAAM,eAAe;YAE1C,OAAO;gBACH,UAAU;gBACV,UAAU;gBACV,eAAe,kBAAkB,MAAM,IAAI;YAC/C;QACJ;IACJ,EAAE,OAAO,GAAQ;QACb,yCAAyC;QACzC,MAAM,UAAU,EAAE,OAAO,IAAI;QAC7B,MAAM,IAAI,MAAM;IACpB;AACJ;;;IAvekB;;AAAA,iPAAA", "debugId": null}}]}