module.exports = {

"[project]/.next-internal/server/app/api/advanced-content/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/ai/models/versions/revo-1.0/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Configuration
 * Model-specific configuration and constants
 */ __turbopack_context__.s({
    "getPerformanceBenchmark": (()=>getPerformanceBenchmark),
    "getPromptTemplate": (()=>getPromptTemplate),
    "getRevo10Config": (()=>getRevo10Config),
    "isFeatureEnabled": (()=>isFeatureEnabled),
    "revo10Config": (()=>revo10Config),
    "revo10Constants": (()=>revo10Constants),
    "revo10Metrics": (()=>revo10Metrics),
    "revo10Prompts": (()=>revo10Prompts),
    "revo10Validation": (()=>revo10Validation),
    "shouldAlert": (()=>shouldAlert),
    "validateRequest": (()=>validateRequest)
});
const revo10Config = {
    aiService: 'gemini-2.5-flash-image-preview',
    fallbackServices: [
        'gemini-2.5',
        'gemini-2.0',
        'openai'
    ],
    maxRetries: 3,
    timeout: 45000,
    qualitySettings: {
        imageResolution: '2048x2048',
        compressionLevel: 95,
        enhancementLevel: 7 // Reduced for cleaner designs (was 10)
    },
    promptSettings: {
        temperature: 0.3,
        maxTokens: 4096,
        topP: 0.6,
        topK: 25 // Fewer creative choices for consistency (was 100)
    }
};
const revo10Constants = {
    // Model identification
    MODEL_ID: 'revo-1.0',
    MODEL_NAME: 'Revo 1.0',
    MODEL_VERSION: '1.0.0',
    // Capabilities
    SUPPORTED_ASPECT_RATIOS: [
        '1:1'
    ],
    SUPPORTED_PLATFORMS: [
        'Instagram',
        'Facebook',
        'Twitter',
        'LinkedIn'
    ],
    MAX_QUALITY_SCORE: 9.0,
    // Performance targets
    TARGET_PROCESSING_TIME: 30000,
    TARGET_SUCCESS_RATE: 0.97,
    TARGET_QUALITY_SCORE: 8.5,
    // Resource limits
    MAX_CONTENT_LENGTH: 2000,
    MAX_HASHTAGS: 15,
    MAX_IMAGE_SIZE: 2048,
    // Feature flags
    FEATURES: {
        ARTIFACTS_SUPPORT: false,
        REAL_TIME_CONTEXT: true,
        TRENDING_TOPICS: true,
        MULTIPLE_ASPECT_RATIOS: false,
        VIDEO_GENERATION: false,
        ADVANCED_PROMPTING: true,
        ENHANCED_DESIGN: true,
        PERFECT_TEXT_RENDERING: true,
        HIGH_RESOLUTION: true,
        NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability
    },
    // Pricing
    CREDITS_PER_GENERATION: 1.5,
    CREDITS_PER_DESIGN: 1.5,
    TIER: 'enhanced' // Upgraded from basic
};
const revo10Prompts = {
    // Content generation prompts
    CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.
Your expertise spans viral content creation, brand storytelling, and audience engagement optimization.

Your capabilities include:
- **Deep Local Market Knowledge**: Understanding of local business environment, competition, and market trends
- **Industry-Specific Insights**: 20+ years of experience across various industries
- **Community Connection**: Deep understanding of local culture, values, and business needs
- **Market Dynamics**: Knowledge of local economic conditions, competitive landscape, and business opportunities

When creating content:
- Write like a real industry professional, not AI
- Use local market insights and industry knowledge naturally
- Incorporate local phrases and community language authentically
- Share real, relatable stories that connect with the local community
- Position as the local expert with deep industry knowledge
- Focus on local relevance and community impact
- Use conversational, human language that builds trust and authority

Your mission is to create content that sounds like it's written by a real industry professional with deep local expertise - not generic marketing copy. Every post should demonstrate your local market knowledge and industry authority.`,
    CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:
Business: {businessName}
Type: {businessType}
Platform: {platform}
Tone: {writingTone}
Location: {location}

Brand Information:
- Primary Color: {primaryColor}
- Visual Style: {visualStyle}
- Target Audience: {targetAudience}
- Services: {services}
- Key Features: {keyFeatures}
- Competitive Advantages: {competitiveAdvantages}
- Content Themes: {contentThemes}

Requirements:
- Create engaging, professional content that reflects the business's unique value proposition
- Incorporate services and key features naturally into the content
- Highlight competitive advantages when relevant
- Include relevant hashtags (5-15) that align with content themes
- Generate catchy words for the image that capture the brand essence
- Ensure platform-appropriate formatting and tone
- Maintain brand consistency with colors and visual style
- Use only clean, readable text (no special characters, symbols, or garbled text)
- Generate content in proper English with correct spelling and grammar
- Avoid any corrupted or unreadable character sequences
- Make the content location-specific and culturally relevant when appropriate`,
    // Design generation prompts
    DESIGN_SYSTEM_PROMPT: `You are a world-class graphic designer who creates 7 completely different types of social media designs, each with their own unique visual language and style. You have deep expertise in multiple industries and understand how to create designs that rival the best brands in the world.

Your design philosophy:
- Create designs that are VISUALLY APPEALING and engaging
- Each design type should look completely different from the others
- Focus on style-specific authenticity (watercolor should look like real watercolor, meme-style should look like a real meme)
- Make designs that look like something from successful, popular brands
- **CRITICAL: Make designs look like a human designer created them, not AI**
- **CRITICAL: Each design type must have its own unique visual identity**
- **IMPORTANT: Keep local/cultural elements subtle and natural, not overwhelming**
- **NEW: Understand the business industry and create designs that rival world-class brands**

When creating designs:
- Start with the specific style requirements for the chosen design type
- Use style-appropriate elements, colors, and typography
- Focus on visual impact and engagement
- Create designs people want to interact with
- Use current design trends that work for the specific style
- **MOST IMPORTANT: Make each design type genuinely unique and different**
- **SECOND MOST IMPORTANT: Make it look human-made, not AI-generated**
- **NEW: Study industry benchmarks and create designs that match world-class quality**

CRITICAL: You are a human designer who understands that each design type should look completely different. A watercolor quote should look nothing like a meme-style post. A split photo collage should look nothing like a branded poster. Each style must have its own visual language and approach.

**HUMAN DESIGN APPROACH:**
- Add slight imperfections and asymmetry (humans aren't perfect)
- Use natural spacing and proportions
- Avoid overly symmetrical, geometric perfection
- Make it feel organic and handcrafted
- Focus on the design style first, local elements second

**INDUSTRY INTELLIGENCE INTEGRATION:**
- Study and understand the business industry context
- Learn from world-class brands in the same industry
- Incorporate industry-specific design trends and best practices
- Create designs that feel authentic to the industry while being creative
- Match the quality and sophistication of industry leaders

Focus on creating designs that are both beautiful and engaging while maintaining the unique characteristics of each design type, looking genuinely human-made, and rivaling world-class industry standards.`,
    DESIGN_USER_PROMPT_TEMPLATE: `Create a world-class, human-made 2048x2048 social media design that people will actually want to engage with:

BUSINESS CONTEXT:
- Business: {businessName}
- Industry: {businessType}
- Platform: {platform}
- Target Message: {imageText}

DESIGN REQUIREMENTS:
- Create a design that's VISUALLY APPEALING and engaging
- Focus on the specific design style requirements
- Make it look like a human designer created it, not AI
- Keep local/cultural elements subtle and natural, not overwhelming
- Focus on the design style first, local elements second
- **NEW: Study industry benchmarks and create designs that rival world-class brands**

KEY DESIGN PRINCIPLES:
1. **HUMAN-MADE FIRST** - Make it look like a skilled human designer created it
2. **STYLE AUTHENTICITY** - Follow the specific style requirements exactly
3. **VISUAL UNIQUENESS** - Make this look completely different from other design types
4. **NATURAL IMPERFECTIONS** - Add slight asymmetry, natural spacing, organic feel
5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative
6. **INDUSTRY EXCELLENCE** - Match the quality of world-class brands in the industry

INDUSTRY INTELLIGENCE INTEGRATION:
- Study and understand the {businessType} industry context
- Learn from world-class brands in the same industry
- Incorporate industry-specific design trends and best practices
- Create designs that feel authentic to the industry while being creative
- Match the quality and sophistication of industry leaders

WHAT TO AVOID:
- Overly perfect, symmetrical, AI-generated looking designs
- Forced cultural elements that feel stereotypical
- Generic, template-like designs
- Overly complex or busy layouts
- Poor contrast or readability
- Designs that don't match industry quality standards

WHAT TO INCLUDE:
- Style-specific elements that match the chosen design type
- Unique visual approach for the specific style
- Subtle local touches that feel natural, not forced
- Human imperfections - slight asymmetry, natural spacing, organic feel
- Style-appropriate typography and layout
- Industry-specific design elements and quality standards

TECHNICAL REQUIREMENTS:
- Resolution: 2048x2048 pixels
- Format: Square (1:1)
- Text must be readable on mobile
- Logo integration should look natural

🎨 GOAL: Create a world-class design that looks genuinely human-made, follows the specific style requirements, feels unique and engaging, and rivals the quality of industry leaders. Focus on the design style first, add subtle local touches naturally, make it look like a skilled human designer created it, and ensure it matches world-class industry standards.`,
    // Error messages
    ERROR_MESSAGES: {
        GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',
        DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',
        INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',
        SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',
        TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',
        QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'
    }
};
const revo10Validation = {
    // Content validation
    content: {
        minLength: 10,
        maxLength: 2000,
        requiredFields: [
            'businessType',
            'platform',
            'businessName'
        ],
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Design validation
    design: {
        requiredFields: [
            'businessType',
            'platform',
            'visualStyle',
            'imageText'
        ],
        supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,
        maxImageTextLength: 200,
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Brand profile validation
    brandProfile: {
        requiredFields: [
            'businessType',
            'businessName'
        ],
        optionalFields: [
            'location',
            'writingTone',
            'visualStyle',
            'primaryColor',
            'accentColor',
            'backgroundColor',
            'logoDataUrl',
            'targetAudience'
        ]
    }
};
const revo10Metrics = {
    // Expected performance benchmarks
    BENCHMARKS: {
        processingTime: {
            target: 30000,
            acceptable: 40000,
            maximum: 60000 // 60 seconds (upgraded from 45s)
        },
        qualityScore: {
            minimum: 7.0,
            target: 8.5,
            maximum: 9.0 // Upgraded from 7.5
        },
        successRate: {
            minimum: 0.95,
            target: 0.97,
            maximum: 0.99 // Upgraded from 98%
        }
    },
    // Monitoring thresholds
    ALERTS: {
        processingTimeHigh: 45000,
        qualityScoreLow: 7.5,
        successRateLow: 0.95,
        errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)
    }
};
function getRevo10Config() {
    return revo10Config;
}
function isFeatureEnabled(feature) {
    return revo10Constants.FEATURES[feature];
}
function getPromptTemplate(type, templateName) {
    if (type === 'content') {
        return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;
    } else if (type === 'design') {
        return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;
    }
    throw new Error(`Unknown prompt template: ${type}/${templateName}`);
}
function validateRequest(type, request) {
    const errors = [];
    const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;
    // Check required fields
    for (const field of validation.requiredFields){
        if (!request[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    // Check platform support
    if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {
        errors.push(`Unsupported platform: ${request.platform}`);
    }
    // Design-specific validation
    if (type === 'design') {
        if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {
            errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function getPerformanceBenchmark(metric) {
    return revo10Metrics.BENCHMARKS[metric];
}
function shouldAlert(metric, value) {
    const alerts = revo10Metrics.ALERTS;
    switch(metric){
        case 'processingTime':
            return value > alerts.processingTimeHigh;
        case 'qualityScore':
            return value < alerts.qualityScoreLow;
        case 'successRate':
            return value < alerts.successRateLow;
        case 'errorRate':
            return value > alerts.errorRateHigh;
        default:
            return false;
    }
}
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * RSS Feed Service for Trending Content & Social Media Insights
 * Fetches and parses RSS feeds to extract trending topics, keywords, and themes
 */ __turbopack_context__.s({
    "RSSFeedService": (()=>RSSFeedService),
    "rssService": (()=>rssService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/xml2js/lib/xml2js.js [app-route] (ecmascript)");
;
class RSSFeedService {
    cache = new Map();
    cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000;
    feedUrls = {
        // Social Media & Marketing Trends
        socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,
        socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,
        bufferBlog: process.env.RSS_BUFFER_BLOG,
        hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,
        sproutSocial: process.env.RSS_SPROUT_SOCIAL,
        laterBlog: process.env.RSS_LATER_BLOG,
        // Trending Topics & News
        googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,
        redditPopular: process.env.RSS_REDDIT_POPULAR,
        buzzfeed: process.env.RSS_BUZZFEED,
        twitterTrending: process.env.RSS_TWITTER_TRENDING,
        // Business & Marketing
        hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,
        contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,
        marketingProfs: process.env.RSS_MARKETING_PROFS,
        marketingLand: process.env.RSS_MARKETING_LAND,
        neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,
        // Industry News
        techCrunch: process.env.RSS_TECHCRUNCH,
        mashable: process.env.RSS_MASHABLE,
        theVerge: process.env.RSS_THE_VERGE,
        wired: process.env.RSS_WIRED,
        // Platform-Specific
        instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,
        facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,
        linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,
        youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,
        tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,
        // Analytics & Data
        googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,
        hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,
        // Design & Creative
        canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,
        adobeBlog: process.env.RSS_ADOBE_BLOG,
        creativeBloq: process.env.RSS_CREATIVE_BLOQ,
        // Seasonal & Events
        eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG
    };
    /**
   * Fetch and parse a single RSS feed
   */ async fetchRSSFeed(url, sourceName) {
        try {
            // Check cache first
            const cached = this.cache.get(url);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Nevis-AI-Content-Generator/1.0'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const xmlData = await response.text();
            const parsed = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseStringPromise"])(xmlData);
            const articles = [];
            const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];
            const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');
            for (const item of items.slice(0, maxArticles)){
                const article = {
                    title: this.extractText(item.title),
                    description: this.extractText(item.description || item.summary),
                    link: this.extractText(item.link || item.id),
                    pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),
                    category: this.extractText(item.category),
                    keywords: this.extractKeywords(this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)),
                    source: sourceName
                };
                articles.push(article);
            }
            // Cache the results
            this.cache.set(url, {
                data: articles,
                timestamp: Date.now()
            });
            return articles;
        } catch (error) {
            return [];
        }
    }
    /**
   * Extract text content from RSS item fields
   */ extractText(field) {
        if (!field) return '';
        if (typeof field === 'string') return field;
        if (Array.isArray(field) && field.length > 0) {
            return typeof field[0] === 'string' ? field[0] : field[0]._ || '';
        }
        if (typeof field === 'object' && field._) return field._;
        return '';
    }
    /**
   * Extract keywords from text content
   */ extractKeywords(text) {
        if (!text) return [];
        // Remove HTML tags and normalize text
        const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
        // Extract meaningful words (3+ characters, not common stop words)
        const stopWords = new Set([
            'the',
            'and',
            'for',
            'are',
            'but',
            'not',
            'you',
            'all',
            'can',
            'had',
            'her',
            'was',
            'one',
            'our',
            'out',
            'day',
            'get',
            'has',
            'him',
            'his',
            'how',
            'its',
            'may',
            'new',
            'now',
            'old',
            'see',
            'two',
            'who',
            'boy',
            'did',
            'she',
            'use',
            'way',
            'will',
            'with'
        ]);
        const words = cleanText.split(' ').filter((word)=>word.length >= 3 && !stopWords.has(word)).slice(0, 10); // Limit to top 10 keywords per article
        return Array.from(new Set(words)); // Remove duplicates
    }
    /**
   * Fetch all RSS feeds and return trending data
   */ async getTrendingData() {
        const allArticles = [];
        const fetchPromises = [];
        // Fetch all feeds concurrently
        for (const [sourceName, url] of Object.entries(this.feedUrls)){
            if (url) {
                fetchPromises.push(this.fetchRSSFeed(url, sourceName));
            }
        }
        const results = await Promise.allSettled(fetchPromises);
        // Collect all successful results
        results.forEach((result)=>{
            if (result.status === 'fulfilled') {
                allArticles.push(...result.value);
            }
        });
        // Sort articles by publication date (newest first)
        allArticles.sort((a, b)=>b.pubDate.getTime() - a.pubDate.getTime());
        // Extract trending keywords and topics
        const allKeywords = [];
        const allTopics = [];
        const allThemes = [];
        allArticles.forEach((article)=>{
            allKeywords.push(...article.keywords);
            if (article.title) allTopics.push(article.title);
            if (article.category) allThemes.push(article.category);
        });
        // Count frequency and get top items
        const keywordCounts = this.getTopItems(allKeywords, 50);
        const topicCounts = this.getTopItems(allTopics, 30);
        const themeCounts = this.getTopItems(allThemes, 20);
        return {
            keywords: keywordCounts,
            topics: topicCounts,
            themes: themeCounts,
            articles: allArticles.slice(0, 100),
            lastUpdated: new Date()
        };
    }
    /**
   * Get top items by frequency
   */ getTopItems(items, limit) {
        const counts = new Map();
        items.forEach((item)=>{
            const normalized = item.toLowerCase().trim();
            if (normalized.length >= 3) {
                counts.set(normalized, (counts.get(normalized) || 0) + 1);
            }
        });
        return Array.from(counts.entries()).sort((a, b)=>b[1] - a[1]).slice(0, limit).map(([item])=>item);
    }
    /**
   * Get trending keywords for a specific category
   */ async getTrendingKeywordsByCategory(category) {
        const trendingData = await this.getTrendingData();
        const categoryFeeds = {
            social: [
                'socialMediaToday',
                'socialMediaExaminer',
                'bufferBlog',
                'hootsuiteBlogs'
            ],
            business: [
                'hubspotMarketing',
                'contentMarketingInstitute',
                'marketingProfs'
            ],
            tech: [
                'techCrunch',
                'theVerge',
                'wired'
            ],
            design: [
                'canvaDesignSchool',
                'adobeBlog',
                'creativeBloq'
            ]
        };
        const categoryArticles = trendingData.articles.filter((article)=>categoryFeeds[category].includes(article.source));
        const keywords = [];
        categoryArticles.forEach((article)=>keywords.push(...article.keywords));
        return this.getTopItems(keywords, 20);
    }
}
const rssService = new RSSFeedService();
}}),
"[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Content Enhancer
 * Integrates RSS feed data to enhance content generation with trending topics
 */ __turbopack_context__.s({
    "TrendingContentEnhancer": (()=>TrendingContentEnhancer),
    "trendingEnhancer": (()=>trendingEnhancer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)");
;
class TrendingContentEnhancer {
    trendingCache = null;
    lastCacheUpdate = 0;
    cacheTimeout = 30 * 60 * 1000;
    /**
   * Get fresh trending data with caching
   */ async getTrendingData() {
        const now = Date.now();
        if (this.trendingCache && now - this.lastCacheUpdate < this.cacheTimeout) {
            return this.trendingCache;
        }
        this.trendingCache = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
        this.lastCacheUpdate = now;
        return this.trendingCache;
    }
    /**
   * Get trending enhancement data for content generation
   */ async getTrendingEnhancement(context = {}) {
        try {
            const trendingData = await this.getTrendingData();
            // Filter and prioritize based on context
            const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);
            const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);
            // Generate hashtags from trending keywords
            const hashtags = this.generateHashtags(relevantKeywords, context);
            // Extract seasonal themes
            const seasonalThemes = this.extractSeasonalThemes(trendingData);
            // Extract industry-specific buzz
            const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);
            return {
                keywords: relevantKeywords.slice(0, 15),
                topics: relevantTopics.slice(0, 10),
                hashtags: hashtags.slice(0, 10),
                seasonalThemes: seasonalThemes.slice(0, 5),
                industryBuzz: industryBuzz.slice(0, 8)
            };
        } catch (error) {
            // Return fallback data
            return {
                keywords: [
                    'trending',
                    'viral',
                    'popular',
                    'latest',
                    'new'
                ],
                topics: [
                    'social media trends',
                    'digital marketing',
                    'content creation'
                ],
                hashtags: [
                    '#trending',
                    '#viral',
                    '#socialmedia',
                    '#marketing'
                ],
                seasonalThemes: [],
                industryBuzz: []
            };
        }
    }
    /**
   * Filter keywords based on context relevance
   */ filterKeywordsByContext(keywords, context) {
        const platformKeywords = {
            instagram: [
                'visual',
                'photo',
                'story',
                'reel',
                'aesthetic',
                'lifestyle'
            ],
            facebook: [
                'community',
                'share',
                'connect',
                'family',
                'local',
                'event'
            ],
            twitter: [
                'news',
                'update',
                'breaking',
                'discussion',
                'opinion',
                'thread'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'industry',
                'networking',
                'leadership'
            ],
            tiktok: [
                'viral',
                'trend',
                'challenge',
                'creative',
                'fun',
                'entertainment'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'design',
                'home',
                'style'
            ]
        };
        const businessKeywords = {
            restaurant: [
                'food',
                'dining',
                'menu',
                'chef',
                'cuisine',
                'taste',
                'fresh'
            ],
            retail: [
                'shopping',
                'sale',
                'fashion',
                'style',
                'product',
                'deal',
                'new'
            ],
            fitness: [
                'health',
                'workout',
                'training',
                'wellness',
                'strength',
                'motivation'
            ],
            beauty: [
                'skincare',
                'makeup',
                'beauty',
                'glow',
                'treatment',
                'style'
            ],
            tech: [
                'innovation',
                'digital',
                'technology',
                'software',
                'app',
                'solution'
            ],
            healthcare: [
                'health',
                'wellness',
                'care',
                'treatment',
                'medical',
                'patient'
            ]
        };
        let filtered = [
            ...keywords
        ];
        // Boost platform-relevant keywords
        if (context.platform && platformKeywords[context.platform]) {
            const platformBoost = platformKeywords[context.platform];
            filtered = filtered.sort((a, b)=>{
                const aBoost = platformBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = platformBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        // Boost business-relevant keywords
        if (context.businessType && businessKeywords[context.businessType]) {
            const businessBoost = businessKeywords[context.businessType];
            filtered = filtered.sort((a, b)=>{
                const aBoost = businessBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = businessBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        return filtered;
    }
    /**
   * Filter topics based on context relevance
   */ filterTopicsByContext(topics, context) {
        // Remove topics that are too generic or not suitable for social media
        const filtered = topics.filter((topic)=>{
            const lower = topic.toLowerCase();
            return !lower.includes('error') && !lower.includes('404') && !lower.includes('page not found') && lower.length > 10 && lower.length < 100;
        });
        return filtered;
    }
    /**
   * Generate relevant hashtags from keywords
   */ generateHashtags(keywords, context) {
        const hashtags = [];
        // Convert keywords to hashtags
        keywords.forEach((keyword)=>{
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        });
        // Add platform-specific hashtags
        const platformHashtags = {
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#picoftheday'
            ],
            facebook: [
                '#community',
                '#local',
                '#share',
                '#connect'
            ],
            twitter: [
                '#news',
                '#update',
                '#discussion',
                '#trending'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#career',
                '#networking'
            ],
            tiktok: [
                '#fyp',
                '#viral',
                '#trending',
                '#foryou'
            ],
            pinterest: [
                '#inspiration',
                '#ideas',
                '#diy',
                '#style'
            ]
        };
        if (context.platform && platformHashtags[context.platform]) {
            hashtags.push(...platformHashtags[context.platform]);
        }
        // Remove duplicates and return
        return Array.from(new Set(hashtags));
    }
    /**
   * Extract seasonal themes from trending data
   */ extractSeasonalThemes(trendingData) {
        const currentMonth = new Date().getMonth();
        const seasonalKeywords = {
            0: [
                'new year',
                'resolution',
                'fresh start',
                'winter'
            ],
            1: [
                'valentine',
                'love',
                'romance',
                'winter'
            ],
            2: [
                'spring',
                'march madness',
                'renewal',
                'growth'
            ],
            3: [
                'easter',
                'spring',
                'bloom',
                'fresh'
            ],
            4: [
                'mother\'s day',
                'spring',
                'flowers',
                'celebration'
            ],
            5: [
                'summer',
                'graduation',
                'father\'s day',
                'vacation'
            ],
            6: [
                'summer',
                'july 4th',
                'independence',
                'freedom'
            ],
            7: [
                'summer',
                'vacation',
                'back to school',
                'preparation'
            ],
            8: [
                'back to school',
                'fall',
                'autumn',
                'harvest'
            ],
            9: [
                'halloween',
                'october',
                'spooky',
                'fall'
            ],
            10: [
                'thanksgiving',
                'gratitude',
                'family',
                'harvest'
            ],
            11: [
                'christmas',
                'holiday',
                'winter',
                'celebration'
            ]
        };
        const currentSeasonalKeywords = seasonalKeywords[currentMonth] || [];
        const seasonalThemes = trendingData.keywords.filter((keyword)=>currentSeasonalKeywords.some((seasonal)=>keyword.toLowerCase().includes(seasonal.toLowerCase())));
        return seasonalThemes;
    }
    /**
   * Extract industry-specific buzz from trending data
   */ extractIndustryBuzz(trendingData, businessType) {
        if (!businessType) return [];
        const industryKeywords = {
            restaurant: [
                'food',
                'dining',
                'chef',
                'cuisine',
                'recipe',
                'restaurant',
                'menu'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'product',
                'brand',
                'sale',
                'deal'
            ],
            fitness: [
                'fitness',
                'workout',
                'health',
                'gym',
                'training',
                'wellness',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'cosmetics',
                'treatment',
                'spa'
            ],
            tech: [
                'technology',
                'tech',
                'digital',
                'software',
                'app',
                'innovation',
                'ai'
            ],
            healthcare: [
                'health',
                'medical',
                'healthcare',
                'wellness',
                'treatment',
                'care'
            ]
        };
        const relevantKeywords = industryKeywords[businessType] || [];
        const industryBuzz = trendingData.keywords.filter((keyword)=>relevantKeywords.some((industry)=>keyword.toLowerCase().includes(industry.toLowerCase())));
        return industryBuzz;
    }
    /**
   * Get trending prompt enhancement for AI content generation
   */ async getTrendingPromptEnhancement(context = {}) {
        const enhancement = await this.getTrendingEnhancement(context);
        const promptParts = [];
        if (enhancement.keywords.length > 0) {
            promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);
        }
        if (enhancement.seasonalThemes.length > 0) {
            promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);
        }
        if (enhancement.industryBuzz.length > 0) {
            promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);
        }
        if (enhancement.hashtags.length > 0) {
            promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);
        }
        return promptParts.join('\n');
    }
}
const trendingEnhancer = new TrendingContentEnhancer();
}}),
"[project]/src/ai/regional-communication-engine.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Regional Communication Engine
 * Deep understanding of how people actually communicate, advertise, and connect in different regions
 */ __turbopack_context__.s({
    "RegionalCommunicationEngine": (()=>RegionalCommunicationEngine),
    "regionalEngine": (()=>regionalEngine)
});
class RegionalCommunicationEngine {
    regionalProfiles = new Map();
    constructor(){
        this.initializeRegionalProfiles();
    }
    initializeRegionalProfiles() {
        // KENYA - Nairobi and surrounding areas
        this.regionalProfiles.set('kenya', {
            region: 'Kenya',
            country: 'Kenya',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'high',
                humorStyle: [
                    'witty',
                    'playful',
                    'community-based',
                    'storytelling'
                ],
                persuasionTactics: [
                    'community benefit',
                    'family value',
                    'quality emphasis',
                    'local pride'
                ],
                attentionGrabbers: [
                    'Eh!',
                    'Sawa sawa!',
                    'Mambo!',
                    'Poa!',
                    'Uko ready?'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Community-Centered',
                    approach: 'Emphasize how the business serves the local community',
                    examples: [
                        'Serving our Nairobi family with love',
                        'Your neighborhood spot for authentic taste',
                        'Where Kenyans come together'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'facebook',
                        'whatsapp',
                        'instagram'
                    ]
                },
                {
                    type: 'Quality & Freshness',
                    approach: 'Highlight freshness, quality, and authentic preparation',
                    examples: [
                        'Fresh from the kitchen to your table',
                        'Made with love, served with pride',
                        'Authentic taste that reminds you of home'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'facebook'
                    ]
                },
                {
                    type: 'Swahili Integration',
                    approach: 'Natural mix of English and Swahili that feels authentic',
                    examples: [
                        'Chakula kizuri, bei nzuri!',
                        'Karibu for the best experience',
                        'Tupo hapa for you always'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'all'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'Mambo!',
                    'Sasa!',
                    'Niaje!',
                    'Poa!',
                    'Karibu!'
                ],
                excitement: [
                    'Poa kabisa!',
                    'Sawa sawa!',
                    'Fiti!',
                    'Bomba!',
                    'Noma!'
                ],
                approval: [
                    'Safi!',
                    'Poa!',
                    'Nzuri!',
                    'Fiti kabisa!',
                    'Bomba sana!'
                ],
                emphasis: [
                    'kabisa',
                    'sana',
                    'mzuri',
                    'noma',
                    'fiti'
                ],
                callToAction: [
                    'Njoo uone!',
                    'Karibu!',
                    'Tupatane!',
                    'Uko ready?',
                    'Twende!'
                ],
                endingPhrases: [
                    'Tutaonana!',
                    'Karibu tena!',
                    'Asante sana!',
                    'Mungu akubariki!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Community Connection',
                    importance: 'critical',
                    description: 'Kenyans value businesses that feel like part of the community',
                    doAndDonts: {
                        do: [
                            'Reference local landmarks',
                            'Use "our community" language',
                            'Show family values'
                        ],
                        dont: [
                            'Sound too corporate',
                            'Ignore local customs',
                            'Be overly formal'
                        ]
                    }
                },
                {
                    aspect: 'Language Mixing',
                    importance: 'important',
                    description: 'Natural mixing of English and Swahili is expected and appreciated',
                    doAndDonts: {
                        do: [
                            'Mix languages naturally',
                            'Use common Swahili phrases',
                            'Keep it conversational'
                        ],
                        dont: [
                            'Force Swahili if unsure',
                            'Use formal Swahili only',
                            'Ignore English speakers'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Family-owned and operated',
                    'Serving the community for [X] years',
                    'Made with love by local hands',
                    'Your neighbors you can trust'
                ],
                valuePropositions: [
                    'Fresh ingredients sourced locally',
                    'Authentic recipes passed down generations',
                    'Fair prices for quality food',
                    'A place where everyone is family'
                ],
                communityConnection: [
                    'Part of the Nairobi family',
                    'Supporting local farmers and suppliers',
                    'Where neighbors become friends',
                    'Celebrating our Kenyan heritage'
                ],
                localReferences: [
                    'Just off [local road/landmark]',
                    'Near [well-known local spot]',
                    'In the heart of [neighborhood]',
                    'Where locals have been coming for years'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'WhatsApp',
                    'Facebook',
                    'Instagram',
                    'TikTok'
                ],
                contentPreferences: [
                    'food photos',
                    'behind-the-scenes',
                    'customer testimonials',
                    'community events'
                ],
                engagementStyle: 'High interaction, lots of comments and shares, community-focused',
                hashtagUsage: 'Mix of English and Swahili hashtags, location-based tags',
                visualPreferences: [
                    'bright colors',
                    'authentic moments',
                    'people enjoying food',
                    'local settings'
                ]
            }
        });
        // NIGERIA - Lagos and surrounding areas
        this.regionalProfiles.set('nigeria', {
            region: 'Nigeria',
            country: 'Nigeria',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'high',
                humorStyle: [
                    'energetic',
                    'bold',
                    'confident',
                    'community-pride'
                ],
                persuasionTactics: [
                    'quality emphasis',
                    'value for money',
                    'social status',
                    'community respect'
                ],
                attentionGrabbers: [
                    'Oya!',
                    'See this one!',
                    'No be small thing!',
                    'This one sweet die!'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Bold & Confident',
                    approach: 'Strong, confident statements about quality and value',
                    examples: [
                        'The best in Lagos, no cap!',
                        'Quality wey go shock you!',
                        'This one na correct business!'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'twitter',
                        'facebook'
                    ]
                },
                {
                    type: 'Value Emphasis',
                    approach: 'Highlight exceptional value and quality for the price',
                    examples: [
                        'Quality food, affordable price',
                        'Where your money get value',
                        'Premium taste, pocket-friendly price'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'all'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'How far?',
                    'Wetin dey happen?',
                    'Oya!',
                    'My guy!'
                ],
                excitement: [
                    'E sweet die!',
                    'This one correct!',
                    'Na fire!',
                    'Too much!'
                ],
                approval: [
                    'Correct!',
                    'Na so!',
                    'Perfect!',
                    'E good die!'
                ],
                emphasis: [
                    'die',
                    'well well',
                    'proper',
                    'correct'
                ],
                callToAction: [
                    'Come try am!',
                    'Oya come!',
                    'Make you taste am!',
                    'No waste time!'
                ],
                endingPhrases: [
                    'See you soon!',
                    'We dey wait for you!',
                    'Come back again!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Confidence & Quality',
                    importance: 'critical',
                    description: 'Nigerians appreciate confident, bold statements about quality',
                    doAndDonts: {
                        do: [
                            'Be confident about your quality',
                            'Use bold language',
                            'Emphasize value'
                        ],
                        dont: [
                            'Be too modest',
                            'Undersell your quality',
                            'Sound uncertain'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Tested and trusted',
                    'Lagos people choice',
                    'Quality wey you fit trust',
                    'We no dey disappoint'
                ],
                valuePropositions: [
                    'Best quality for your money',
                    'Fresh ingredients, authentic taste',
                    'Where quality meets affordability',
                    'Premium service, reasonable price'
                ],
                communityConnection: [
                    'Proudly Nigerian',
                    'Serving Lagos with pride',
                    'Your neighborhood favorite',
                    'Where Lagos people gather'
                ],
                localReferences: [
                    'For Lagos Island',
                    'Victoria Island area',
                    'Mainland favorite',
                    'Ikeja corridor'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'Instagram',
                    'Twitter',
                    'WhatsApp',
                    'Facebook'
                ],
                contentPreferences: [
                    'food videos',
                    'customer reactions',
                    'quality showcases',
                    'value demonstrations'
                ],
                engagementStyle: 'High energy, lots of reactions, sharing culture',
                hashtagUsage: 'Mix of English and Pidgin, location tags, trending topics',
                visualPreferences: [
                    'vibrant colors',
                    'appetizing close-ups',
                    'happy customers',
                    'quality focus'
                ]
            }
        });
        // SOUTH AFRICA - Johannesburg/Cape Town
        this.regionalProfiles.set('south_africa', {
            region: 'South Africa',
            country: 'South Africa',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'medium',
                humorStyle: [
                    'laid-back',
                    'friendly',
                    'inclusive',
                    'warm'
                ],
                persuasionTactics: [
                    'quality focus',
                    'local pride',
                    'community value',
                    'authentic experience'
                ],
                attentionGrabbers: [
                    'Howzit!',
                    'Check this out!',
                    'Lekker!',
                    'Sharp!'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Lekker & Local',
                    approach: 'Emphasize local flavor and authentic South African experience',
                    examples: [
                        'Proper lekker food, hey!',
                        'Authentic South African taste',
                        'Made with love in Mzansi'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'facebook'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'Howzit!',
                    'Sharp!',
                    'Sawubona!',
                    'Hey!'
                ],
                excitement: [
                    'Lekker!',
                    'Sharp sharp!',
                    'Eish!',
                    'Awesome!'
                ],
                approval: [
                    'Lekker!',
                    'Sharp!',
                    'Cool!',
                    'Nice one!'
                ],
                emphasis: [
                    'proper',
                    'lekker',
                    'sharp',
                    'hey'
                ],
                callToAction: [
                    'Come check us out!',
                    'Pop in!',
                    'Give us a try!'
                ],
                endingPhrases: [
                    'Cheers!',
                    'See you now!',
                    'Sharp!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Rainbow Nation Unity',
                    importance: 'important',
                    description: 'Inclusive language that welcomes all South Africans',
                    doAndDonts: {
                        do: [
                            'Be inclusive',
                            'Celebrate diversity',
                            'Use local terms naturally'
                        ],
                        dont: [
                            'Exclude any group',
                            'Be overly formal',
                            'Ignore local culture'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Proudly South African',
                    'Local family business',
                    'Trusted by locals',
                    'Authentic Mzansi experience'
                ],
                valuePropositions: [
                    'Lekker food, fair prices',
                    'Authentic local flavors',
                    'Quality you can trust',
                    'Where everyone is welcome'
                ],
                communityConnection: [
                    'Part of the local community',
                    'Supporting local suppliers',
                    'Where neighbors meet',
                    'Celebrating our heritage'
                ],
                localReferences: [
                    'In the heart of [area]',
                    'Your local [business type]',
                    'Joburg favorite',
                    'Cape Town gem'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'Facebook',
                    'Instagram',
                    'WhatsApp',
                    'Twitter'
                ],
                contentPreferences: [
                    'local culture',
                    'food heritage',
                    'community events',
                    'authentic moments'
                ],
                engagementStyle: 'Friendly, inclusive, community-focused',
                hashtagUsage: 'Local slang mixed with English, location-based',
                visualPreferences: [
                    'natural lighting',
                    'authentic settings',
                    'diverse people',
                    'local culture'
                ]
            }
        });
    // Add more regions as needed...
    }
    /**
   * Get regional communication profile
   */ getRegionalProfile(location) {
        const locationLower = location.toLowerCase();
        // Kenya detection
        if (locationLower.includes('kenya') || locationLower.includes('nairobi') || locationLower.includes('mombasa') || locationLower.includes('kisumu')) {
            return this.regionalProfiles.get('kenya');
        }
        // Nigeria detection
        if (locationLower.includes('nigeria') || locationLower.includes('lagos') || locationLower.includes('abuja') || locationLower.includes('kano')) {
            return this.regionalProfiles.get('nigeria');
        }
        // South Africa detection
        if (locationLower.includes('south africa') || locationLower.includes('johannesburg') || locationLower.includes('cape town') || locationLower.includes('durban')) {
            return this.regionalProfiles.get('south_africa');
        }
        return null;
    }
    /**
   * Generate regionally authentic content
   */ generateRegionalContent(businessType, businessName, location, contentType = 'headline') {
        const profile = this.getRegionalProfile(location);
        if (!profile) {
            return this.generateGenericContent(businessType, businessName, contentType);
        }
        switch(contentType){
            case 'headline':
                return this.generateRegionalHeadline(businessType, businessName, profile);
            case 'subheadline':
                return this.generateRegionalSubheadline(businessType, businessName, profile);
            case 'caption':
                return this.generateRegionalCaption(businessType, businessName, profile);
            case 'cta':
                return this.generateRegionalCTA(businessType, businessName, profile);
            default:
                return this.generateRegionalHeadline(businessType, businessName, profile);
        }
    }
    generateRegionalHeadline(businessType, businessName, profile) {
        const { localSlang, advertisingPatterns, businessCommunication } = profile;
        // Get relevant advertising pattern
        const relevantPattern = advertisingPatterns.find((p)=>p.effectiveness === 'high') || advertisingPatterns[0];
        // Create meaningful headlines that tell a story
        const meaningfulTemplates = [
            `What makes ${businessName} different in ${profile.region}?`,
            `The ${profile.region.toLowerCase()} secret everyone's talking about`,
            `Why ${businessName} is ${profile.region}'s best kept secret`,
            `${this.getRandomElement(businessCommunication.valuePropositions)} - ${businessName}`,
            `Discover what makes ${businessName} special`,
            `${businessName}: ${this.getRandomElement(businessCommunication.trustBuilders)}`
        ];
        // Add local flavor to meaningful content
        const selectedTemplate = this.getRandomElement(meaningfulTemplates);
        // Enhance with local expressions where appropriate
        if (Math.random() > 0.6) {
            const localTouch = this.getRandomElement(localSlang.excitement);
            return `${selectedTemplate} ${localTouch}`;
        }
        return selectedTemplate;
    }
    generateRegionalSubheadline(businessType, businessName, profile) {
        const { localSlang, businessCommunication } = profile;
        // Create meaningful subheadlines that provide context
        const meaningfulTemplates = [
            `${this.getRandomElement(businessCommunication.valuePropositions)} you can trust`,
            `Authentic ${businessType.toLowerCase()} with a local touch`,
            `Where tradition meets innovation`,
            `${this.getRandomElement(businessCommunication.trustBuilders)} since day one`,
            `Bringing ${profile.region}'s finest to your table`,
            `More than just ${businessType.toLowerCase()} - it's an experience`,
            `Crafted with care, served with pride`,
            `Your neighborhood's favorite gathering place`,
            `Quality ingredients, time-tested recipes`,
            `Where every customer becomes family`
        ];
        // Occasionally add local flair
        const baseSubheadline = this.getRandomElement(meaningfulTemplates);
        if (Math.random() > 0.8) {
            const localEmphasis = this.getRandomElement(localSlang.emphasis);
            return `${localEmphasis} ${baseSubheadline.toLowerCase()}`;
        }
        return baseSubheadline;
    }
    generateRegionalCaption(businessType, businessName, profile) {
        const { localSlang, businessCommunication, culturalNuances } = profile;
        // Create meaningful story-driven captions
        const storyTemplates = [
            {
                opening: `Ever wondered what makes ${businessName} stand out?`,
                story: `We've been ${this.getRandomElement(businessCommunication.trustBuilders)} for years, bringing you ${this.getRandomElement(businessCommunication.valuePropositions)}. Our secret? We understand what ${profile.region} truly values.`,
                proof: `From our carefully selected ingredients to our time-tested recipes, every detail matters. That's why we're ${this.getRandomElement(businessCommunication.communityConnection)}.`,
                action: `Ready to taste the difference? ${this.getRandomElement(localSlang.callToAction)}`
            },
            {
                opening: `Here's what makes ${businessName} special in ${profile.region}:`,
                story: `✨ ${this.getRandomElement(businessCommunication.valuePropositions)}\n✨ ${this.getRandomElement(businessCommunication.trustBuilders)}\n✨ ${this.getRandomElement(businessCommunication.communityConnection)}`,
                proof: `We don't just serve ${businessType.toLowerCase()} - we create experiences that bring people together. That's the ${profile.region} way!`,
                action: `Come see for yourself why locals choose us. ${this.getRandomElement(localSlang.callToAction)}`
            },
            {
                opening: `The story behind ${businessName}:`,
                story: `We started with a simple mission: to be ${this.getRandomElement(businessCommunication.trustBuilders)} while delivering ${this.getRandomElement(businessCommunication.valuePropositions)}.`,
                proof: `Today, we're proud to be ${this.getRandomElement(businessCommunication.communityConnection)}, serving authentic ${businessType.toLowerCase()} that reflects our heritage and values.`,
                action: `Join our growing family! ${this.getRandomElement(localSlang.callToAction)}`
            }
        ];
        const selectedStory = this.getRandomElement(storyTemplates);
        // Add local greeting and closing
        const greeting = Math.random() > 0.7 ? `${this.getRandomElement(localSlang.greetings)} ` : '';
        const excitement = Math.random() > 0.5 ? ` ${this.getRandomElement(localSlang.excitement)}` : '';
        const ending = this.getRandomElement(localSlang.endingPhrases);
        return `${greeting}${selectedStory.opening}

${selectedStory.story}

${selectedStory.proof}${excitement}

${selectedStory.action}

${ending}`;
    }
    generateRegionalCTA(businessType, businessName, profile) {
        const { localSlang, businessCommunication } = profile;
        // Create meaningful CTAs that provide clear value
        const meaningfulCTAs = [
            `Taste the difference at ${businessName}`,
            `Experience authentic ${businessType.toLowerCase()} today`,
            `Join our community of satisfied customers`,
            `Discover why locals choose ${businessName}`,
            `Book your table and taste the tradition`,
            `Visit us and see what makes us special`,
            `Come for the food, stay for the experience`,
            `Your next favorite meal awaits`,
            `Ready to become part of our family?`,
            `Let us show you what quality means`
        ];
        // Add local CTAs with context
        const localCTAs = localSlang.callToAction.map((cta)=>{
            if (Math.random() > 0.5) {
                return `${cta} - ${this.getRandomElement(businessCommunication.valuePropositions)}`;
            }
            return cta;
        });
        const allCTAs = [
            ...meaningfulCTAs,
            ...localCTAs
        ];
        return this.getRandomElement(allCTAs);
    }
    generateGenericContent(businessType, businessName, contentType) {
        // Fallback for unsupported regions
        const templates = {
            headline: `Experience the best at ${businessName}`,
            subheadline: `Quality ${businessType.toLowerCase()} you can trust`,
            caption: `Welcome to ${businessName}! We're committed to providing you with exceptional ${businessType} services. Visit us today!`,
            cta: `Visit ${businessName} today!`
        };
        return templates[contentType] || templates.headline;
    }
    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)] || array[0];
    }
    /**
   * Get regional hashtags
   */ getRegionalHashtags(location, businessType) {
        const profile = this.getRegionalProfile(location);
        if (!profile) {
            return [
                `#${businessType}`,
                `#local`,
                `#quality`
            ];
        }
        const hashtags = [];
        // Add location-based hashtags
        if (location.toLowerCase().includes('nairobi')) {
            hashtags.push('#NairobiEats', '#KenyanFood', '#NairobiLife', '#254Food');
        } else if (location.toLowerCase().includes('lagos')) {
            hashtags.push('#LagosEats', '#NaijaFood', '#LagosLife', '#9jaFood');
        } else if (location.toLowerCase().includes('johannesburg')) {
            hashtags.push('#JoziEats', '#SouthAfricanFood', '#MzansiFood', '#JHBLife');
        }
        // Add business type hashtags with local flavor
        hashtags.push(`#${businessType}`, '#LocalBusiness', '#CommunityFavorite');
        return hashtags;
    }
}
const regionalEngine = new RegionalCommunicationEngine();
}}),
"[project]/src/ai/advanced-content-generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Content Generator
 * Deep business understanding, cultural awareness, and competitive analysis
 */ __turbopack_context__.s({
    "AdvancedContentGenerator": (()=>AdvancedContentGenerator),
    "advancedContentGenerator": (()=>advancedContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/regional-communication-engine.ts [app-route] (ecmascript)");
;
;
class AdvancedContentGenerator {
    businessIntelligence = new Map();
    culturalDatabase = new Map();
    performanceHistory = new Map();
    constructor(){
        this.initializeCulturalDatabase();
        this.initializeBusinessIntelligence();
    }
    /**
   * Initialize cultural database with local knowledge
   */ initializeCulturalDatabase() {
        const cultures = {
            'United States': {
                primaryLanguage: 'English',
                localPhrases: [
                    'awesome',
                    'amazing',
                    'game-changer',
                    'must-have',
                    'life-changing'
                ],
                culturalValues: [
                    'innovation',
                    'convenience',
                    'quality',
                    'value',
                    'authenticity'
                ],
                localEvents: [
                    'Black Friday',
                    'Super Bowl',
                    'Memorial Day',
                    'Labor Day'
                ],
                communicationStyle: 'direct, enthusiastic, benefit-focused',
                localInfluencers: [
                    'lifestyle',
                    'fitness',
                    'food',
                    'tech',
                    'business'
                ]
            },
            'United Kingdom': {
                primaryLanguage: 'English',
                localPhrases: [
                    'brilliant',
                    'fantastic',
                    'proper',
                    'lovely',
                    'spot on'
                ],
                culturalValues: [
                    'tradition',
                    'quality',
                    'reliability',
                    'heritage',
                    'craftsmanship'
                ],
                localEvents: [
                    'Boxing Day',
                    'Bank Holiday',
                    'Wimbledon',
                    'Royal events'
                ],
                communicationStyle: 'polite, understated, witty',
                localInfluencers: [
                    'lifestyle',
                    'fashion',
                    'food',
                    'travel',
                    'culture'
                ]
            },
            'Canada': {
                primaryLanguage: 'English',
                localPhrases: [
                    'eh',
                    'beauty',
                    'fantastic',
                    'wonderful',
                    'great'
                ],
                culturalValues: [
                    'friendliness',
                    'inclusivity',
                    'nature',
                    'community',
                    'sustainability'
                ],
                localEvents: [
                    'Canada Day',
                    'Victoria Day',
                    'Thanksgiving',
                    'Winter Olympics'
                ],
                communicationStyle: 'friendly, inclusive, nature-focused',
                localInfluencers: [
                    'outdoor',
                    'lifestyle',
                    'food',
                    'wellness',
                    'community'
                ]
            },
            'Australia': {
                primaryLanguage: 'English',
                localPhrases: [
                    'mate',
                    'fair dinkum',
                    'ripper',
                    'bonzer',
                    'ace'
                ],
                culturalValues: [
                    'laid-back',
                    'outdoor lifestyle',
                    'mateship',
                    'adventure',
                    'authenticity'
                ],
                localEvents: [
                    'Australia Day',
                    'Melbourne Cup',
                    'ANZAC Day',
                    'AFL Grand Final'
                ],
                communicationStyle: 'casual, friendly, straightforward',
                localInfluencers: [
                    'outdoor',
                    'fitness',
                    'food',
                    'travel',
                    'lifestyle'
                ]
            }
        };
        Object.entries(cultures).forEach(([location, context])=>{
            this.culturalDatabase.set(location, context);
        });
    }
    /**
   * Initialize business intelligence database
   */ initializeBusinessIntelligence() {
        const businessTypes = {
            restaurant: {
                industryKeywords: [
                    'fresh',
                    'delicious',
                    'authentic',
                    'homemade',
                    'seasonal',
                    'local',
                    'chef-crafted'
                ],
                businessStrengths: [
                    'taste',
                    'atmosphere',
                    'service',
                    'ingredients',
                    'experience'
                ],
                targetEmotions: [
                    'hunger',
                    'comfort',
                    'satisfaction',
                    'joy',
                    'nostalgia'
                ],
                valuePropositions: [
                    'quality ingredients',
                    'unique flavors',
                    'memorable experience',
                    'value for money'
                ],
                localRelevance: [
                    'neighborhood favorite',
                    'local ingredients',
                    'community gathering'
                ],
                seasonalOpportunities: [
                    'seasonal menu',
                    'holiday specials',
                    'summer patio',
                    'winter comfort'
                ]
            },
            retail: {
                industryKeywords: [
                    'trendy',
                    'stylish',
                    'affordable',
                    'quality',
                    'exclusive',
                    'limited',
                    'new arrival'
                ],
                businessStrengths: [
                    'selection',
                    'price',
                    'quality',
                    'customer service',
                    'convenience'
                ],
                targetEmotions: [
                    'desire',
                    'confidence',
                    'satisfaction',
                    'excitement',
                    'belonging'
                ],
                valuePropositions: [
                    'best prices',
                    'latest trends',
                    'quality guarantee',
                    'exclusive access'
                ],
                localRelevance: [
                    'local fashion',
                    'community style',
                    'neighborhood store'
                ],
                seasonalOpportunities: [
                    'seasonal collections',
                    'holiday sales',
                    'back-to-school',
                    'summer styles'
                ]
            },
            fitness: {
                industryKeywords: [
                    'strong',
                    'healthy',
                    'fit',
                    'transformation',
                    'results',
                    'energy',
                    'powerful'
                ],
                businessStrengths: [
                    'expertise',
                    'results',
                    'community',
                    'equipment',
                    'motivation'
                ],
                targetEmotions: [
                    'motivation',
                    'confidence',
                    'achievement',
                    'energy',
                    'determination'
                ],
                valuePropositions: [
                    'proven results',
                    'expert guidance',
                    'supportive community',
                    'flexible schedules'
                ],
                localRelevance: [
                    'neighborhood gym',
                    'local fitness community',
                    'accessible location'
                ],
                seasonalOpportunities: [
                    'New Year resolutions',
                    'summer body',
                    'holiday fitness',
                    'spring training'
                ]
            },
            beauty: {
                industryKeywords: [
                    'glowing',
                    'radiant',
                    'beautiful',
                    'flawless',
                    'natural',
                    'luxurious',
                    'rejuvenating'
                ],
                businessStrengths: [
                    'expertise',
                    'products',
                    'results',
                    'relaxation',
                    'personalization'
                ],
                targetEmotions: [
                    'confidence',
                    'relaxation',
                    'beauty',
                    'self-care',
                    'transformation'
                ],
                valuePropositions: [
                    'expert care',
                    'premium products',
                    'personalized service',
                    'lasting results'
                ],
                localRelevance: [
                    'trusted local salon',
                    'community beauty expert',
                    'neighborhood favorite'
                ],
                seasonalOpportunities: [
                    'bridal season',
                    'holiday glam',
                    'summer skin',
                    'winter care'
                ]
            }
        };
        Object.entries(businessTypes).forEach(([type, intelligence])=>{
            this.businessIntelligence.set(type, intelligence);
        });
    }
    /**
   * Analyze business and context for content generation
   */ async analyzeBusinessContext(profile) {
        // Get business intelligence
        const businessIntelligence = this.businessIntelligence.get(profile.businessType) || {
            industryKeywords: [],
            businessStrengths: [],
            targetEmotions: [],
            valuePropositions: [],
            localRelevance: [],
            seasonalOpportunities: []
        };
        // Get cultural context
        const culturalContext = this.culturalDatabase.get(profile.location) || {
            primaryLanguage: 'English',
            localPhrases: [],
            culturalValues: [],
            localEvents: [],
            communicationStyle: 'friendly, professional',
            localInfluencers: []
        };
        // Get trending insights
        const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
            businessType: profile.businessType,
            location: profile.location,
            targetAudience: profile.targetAudience
        });
        const trendingInsights = {
            currentTrends: trendingData.keywords,
            viralPatterns: trendingData.topics,
            platformSpecificTrends: trendingData.hashtags,
            seasonalTrends: trendingData.seasonalThemes,
            emergingTopics: trendingData.industryBuzz
        };
        // Analyze competitors (simulated for now)
        const competitiveAnalysis = {
            industryBenchmarks: this.generateIndustryBenchmarks(profile.businessType),
            competitorStrategies: this.analyzeCompetitorStrategies(profile.competitors),
            marketGaps: this.identifyMarketGaps(profile.businessType),
            differentiators: profile.uniqueSellingPoints,
            performanceTargets: this.setPerformanceTargets(profile.businessType)
        };
        return {
            businessIntelligence,
            culturalContext,
            competitiveAnalysis,
            trendingInsights
        };
    }
    /**
   * Generate highly engaging content based on analysis
   */ async generateEngagingContent(profile, platform, contentType = 'promotional') {
        const analysis = await this.analyzeBusinessContext(profile);
        // Generate content components
        const headline = await this.generateCatchyHeadline(profile, analysis, platform, contentType);
        const subheadline = await this.generateSubheadline(profile, analysis, headline);
        const caption = await this.generateEngagingCaption(profile, analysis, headline, platform);
        const cta = await this.generateCompellingCTA(profile, analysis, platform, contentType);
        const hashtags = await this.generateStrategicHashtags(profile, analysis, platform);
        const post = {
            headline,
            subheadline,
            caption,
            cta,
            hashtags,
            platform
        };
        // Store for performance tracking
        this.storePostForAnalysis(profile.businessName, post);
        return post;
    }
    /**
   * Generate catchy, business-specific headlines with regional authenticity
   */ async generateCatchyHeadline(profile, analysis, platform, contentType) {
        const { businessIntelligence, culturalContext, trendingInsights } = analysis;
        // Try regional communication first for authentic local content
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            const regionalHeadline = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'headline');
            // Enhance with trending elements if available
            if (trendingInsights.currentTrends.length > 0) {
                const trendingElement = this.getRandomElement(trendingInsights.currentTrends);
                return `${regionalHeadline} - ${trendingElement}`;
            }
            return regionalHeadline;
        }
        // Fallback to original method for unsupported regions
        // Combine business strengths with trending topics
        const powerWords = [
            ...businessIntelligence.industryKeywords,
            ...culturalContext.localPhrases
        ];
        const trendingWords = trendingInsights.currentTrends.slice(0, 5);
        // Create headline templates based on content type
        const templates = {
            promotional: [
                `${this.getRandomElement(powerWords)} ${profile.businessName} ${this.getRandomElement(businessIntelligence.valuePropositions)}`,
                `${this.getRandomElement(culturalContext.localPhrases)} ${this.getRandomElement(trendingWords)} at ${profile.businessName}`,
                `${profile.businessName}: ${this.getRandomElement(businessIntelligence.businessStrengths)} that ${this.getRandomElement(businessIntelligence.targetEmotions)}`
            ],
            educational: [
                `${this.getRandomElement(trendingWords)} secrets from ${profile.businessName}`,
                `Why ${profile.businessName} ${this.getRandomElement(businessIntelligence.businessStrengths)} matters`,
                `The ${this.getRandomElement(powerWords)} guide to ${this.getRandomElement(businessIntelligence.industryKeywords)}`
            ],
            entertaining: [
                `${this.getRandomElement(culturalContext.localPhrases)}! ${profile.businessName} ${this.getRandomElement(trendingWords)}`,
                `${profile.businessName} + ${this.getRandomElement(trendingWords)} = ${this.getRandomElement(powerWords)}`,
                `When ${this.getRandomElement(businessIntelligence.targetEmotions)} meets ${profile.businessName}`
            ],
            seasonal: [
                `${this.getRandomElement(trendingInsights.seasonalTrends)} ${this.getRandomElement(powerWords)} at ${profile.businessName}`,
                `${profile.businessName}'s ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,
                `${this.getRandomElement(culturalContext.localEvents)} special: ${this.getRandomElement(businessIntelligence.valuePropositions)}`
            ]
        };
        const selectedTemplate = this.getRandomElement(templates[contentType]);
        return this.capitalizeWords(selectedTemplate);
    }
    /**
   * Generate supporting subheadlines
   */ async generateSubheadline(profile, analysis, headline) {
        const { businessIntelligence, culturalContext } = analysis;
        const supportingElements = [
            ...businessIntelligence.valuePropositions,
            ...businessIntelligence.localRelevance,
            ...culturalContext.culturalValues
        ];
        const templates = [
            `${this.getRandomElement(supportingElements)} in ${profile.location}`,
            `${this.getRandomElement(businessIntelligence.businessStrengths)} you can trust`,
            `${this.getRandomElement(culturalContext.localPhrases)} experience awaits`
        ];
        return this.getRandomElement(templates);
    }
    /**
   * Generate engaging, culturally-aware captions with regional authenticity
   */ async generateEngagingCaption(profile, analysis, headline, platform) {
        const { businessIntelligence, culturalContext, trendingInsights } = analysis;
        // Try regional communication first for authentic local content
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'caption');
        }
        // Fallback to original method for unsupported regions
        // Platform-specific caption styles
        const platformStyles = {
            instagram: 'visual, lifestyle-focused, emoji-rich',
            facebook: 'community-focused, conversational, story-driven',
            twitter: 'concise, witty, trending-aware',
            linkedin: 'professional, value-driven, industry-focused',
            tiktok: 'trendy, fun, challenge-oriented'
        };
        const captionElements = [
            `At ${profile.businessName}, we believe ${this.getRandomElement(businessIntelligence.valuePropositions)}.`,
            `Our ${this.getRandomElement(businessIntelligence.businessStrengths)} brings ${this.getRandomElement(businessIntelligence.targetEmotions)} to ${profile.location}.`,
            `${this.getRandomElement(culturalContext.localPhrases)}! ${this.getRandomElement(trendingInsights.currentTrends)} meets ${this.getRandomElement(businessIntelligence.industryKeywords)}.`,
            `Join our ${profile.location} community for ${this.getRandomElement(businessIntelligence.localRelevance)}.`
        ];
        return captionElements.slice(0, 2).join(' ');
    }
    /**
   * Generate compelling CTAs with regional authenticity
   */ async generateCompellingCTA(profile, analysis, platform, contentType) {
        const { businessIntelligence, culturalContext } = analysis;
        // Try regional communication first for authentic local CTAs
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'cta');
        }
        // Fallback to original method for unsupported regions
        const ctaTemplates = {
            promotional: [
                `Visit ${profile.businessName} today!`,
                `Experience ${this.getRandomElement(businessIntelligence.businessStrengths)} now`,
                `Book your ${this.getRandomElement(businessIntelligence.targetEmotions)} experience`
            ],
            educational: [
                `Learn more at ${profile.businessName}`,
                `Discover the ${this.getRandomElement(businessIntelligence.industryKeywords)} difference`,
                `Get expert advice from ${profile.businessName}`
            ],
            entertaining: [
                `Join the fun at ${profile.businessName}!`,
                `Share your ${profile.businessName} experience`,
                `Tag a friend who needs this!`
            ],
            seasonal: [
                `Don't miss our ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,
                `Limited time at ${profile.businessName}`,
                `Celebrate with ${profile.businessName}`
            ]
        };
        return this.getRandomElement(ctaTemplates[contentType]);
    }
    /**
   * Generate strategic hashtags with regional authenticity
   */ async generateStrategicHashtags(profile, analysis, platform) {
        const { businessIntelligence, trendingInsights } = analysis;
        // Try regional hashtags first for authentic local content
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            const regionalHashtags = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalHashtags(profile.location, profile.businessType);
            // Add business-specific hashtags
            const businessHashtags = [
                `#${profile.businessName.replace(/\s+/g, '')}`,
                `#${profile.businessType}`
            ];
            // Combine regional and business hashtags
            const combinedHashtags = [
                ...regionalHashtags,
                ...businessHashtags
            ];
            // Add some trending hashtags if available
            trendingInsights.platformSpecificTrends.slice(0, 2).forEach((hashtag)=>{
                if (!combinedHashtags.includes(hashtag)) {
                    combinedHashtags.push(hashtag);
                }
            });
            return combinedHashtags.slice(0, 10);
        }
        // Fallback to original method for unsupported regions
        const hashtags = [];
        // Business-specific hashtags
        hashtags.push(`#${profile.businessName.replace(/\s+/g, '')}`);
        hashtags.push(`#${profile.businessType}`);
        hashtags.push(`#${profile.location.replace(/\s+/g, '')}`);
        // Industry hashtags
        businessIntelligence.industryKeywords.slice(0, 3).forEach((keyword)=>{
            hashtags.push(`#${keyword.replace(/\s+/g, '')}`);
        });
        // Trending hashtags
        trendingInsights.platformSpecificTrends.slice(0, 4).forEach((hashtag)=>{
            if (!hashtags.includes(hashtag)) {
                hashtags.push(hashtag);
            }
        });
        // Platform-specific hashtags
        const platformHashtags = {
            instagram: [
                '#instagood',
                '#photooftheday'
            ],
            facebook: [
                '#community',
                '#local'
            ],
            twitter: [
                '#trending',
                '#news'
            ],
            linkedin: [
                '#business',
                '#professional'
            ],
            tiktok: [
                '#fyp',
                '#viral'
            ]
        };
        if (platformHashtags[platform]) {
            hashtags.push(...platformHashtags[platform]);
        }
        return hashtags.slice(0, 10); // Limit to 10 hashtags
    }
    /**
   * Generate regional subheadlines
   */ generateRegionalSubheadline(profile, regionalProfile, context) {
        const { businessCommunication, localSlang } = regionalProfile;
        const templates = [
            `${this.getRandomElement(businessCommunication.valuePropositions)} in ${profile.location}`,
            `${this.getRandomElement(businessCommunication.trustBuilders)} - ${this.getRandomElement(localSlang.approval)}`,
            `${this.getRandomElement(businessCommunication.communityConnection)} ${this.getRandomElement(localSlang.emphasis)}`
        ];
        return this.getRandomElement(templates);
    }
    // Helper methods
    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)] || array[0];
    }
    capitalizeWords(str) {
        return str.replace(/\b\w/g, (l)=>l.toUpperCase());
    }
    generateIndustryBenchmarks(businessType) {
        return [
            `${businessType} industry standard`,
            'market leader performance',
            'customer satisfaction benchmark'
        ];
    }
    analyzeCompetitorStrategies(competitors) {
        return competitors.map((comp)=>`${comp} strategy analysis`);
    }
    identifyMarketGaps(businessType) {
        return [
            `${businessType} market opportunity`,
            'underserved customer segment',
            'innovation gap'
        ];
    }
    setPerformanceTargets(businessType) {
        return [
            'high engagement rate',
            'increased brand awareness',
            'customer acquisition'
        ];
    }
    storePostForAnalysis(businessName, post) {
        const existing = this.performanceHistory.get(businessName) || [];
        existing.push(post);
        this.performanceHistory.set(businessName, existing.slice(-50)); // Keep last 50 posts
    }
}
const advancedContentGenerator = new AdvancedContentGenerator();
}}),
"[project]/src/ai/content-performance-analyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Content Performance Analyzer
 * Benchmarks against industry standards and continuously improves content quality
 */ __turbopack_context__.s({
    "ContentPerformanceAnalyzer": (()=>ContentPerformanceAnalyzer),
    "performanceAnalyzer": (()=>performanceAnalyzer)
});
class ContentPerformanceAnalyzer {
    industryBenchmarks = new Map();
    performanceHistory = new Map();
    contentPatterns = new Map();
    constructor(){
        this.initializeIndustryBenchmarks();
        this.initializeSuccessPatterns();
    }
    /**
   * Initialize industry benchmarks for different business types
   */ initializeIndustryBenchmarks() {
        const benchmarks = {
            restaurant: [
                {
                    businessType: 'restaurant',
                    platform: 'instagram',
                    averageEngagement: 3.2,
                    topPerformerEngagement: 8.5,
                    averageReach: 15.4,
                    bestPractices: [
                        'High-quality food photography',
                        'Behind-the-scenes content',
                        'Customer testimonials',
                        'Seasonal menu highlights',
                        'Local ingredient stories'
                    ],
                    commonMistakes: [
                        'Poor lighting in photos',
                        'Generic captions',
                        'Inconsistent posting',
                        'Ignoring local trends',
                        'Over-promotional content'
                    ],
                    successPatterns: [
                        'Food close-ups with natural lighting',
                        'Stories about ingredients and preparation',
                        'Customer experience highlights',
                        'Local community involvement',
                        'Seasonal and trending ingredients'
                    ]
                },
                {
                    businessType: 'restaurant',
                    platform: 'facebook',
                    averageEngagement: 2.8,
                    topPerformerEngagement: 6.2,
                    averageReach: 12.1,
                    bestPractices: [
                        'Community engagement',
                        'Event announcements',
                        'Customer reviews sharing',
                        'Local partnerships',
                        'Family-friendly content'
                    ],
                    commonMistakes: [
                        'Posting only promotional content',
                        'Ignoring customer comments',
                        'Not leveraging local events',
                        'Generic stock photos',
                        'Inconsistent brand voice'
                    ],
                    successPatterns: [
                        'Community event participation',
                        'Customer story sharing',
                        'Local ingredient sourcing stories',
                        'Family dining experiences',
                        'Seasonal celebration posts'
                    ]
                }
            ],
            retail: [
                {
                    businessType: 'retail',
                    platform: 'instagram',
                    averageEngagement: 2.9,
                    topPerformerEngagement: 7.8,
                    averageReach: 18.2,
                    bestPractices: [
                        'Product styling and flat lays',
                        'User-generated content',
                        'Trend-focused content',
                        'Behind-the-brand stories',
                        'Seasonal collections'
                    ],
                    commonMistakes: [
                        'Product-only posts',
                        'Poor product photography',
                        'Ignoring fashion trends',
                        'Not showcasing versatility',
                        'Generic product descriptions'
                    ],
                    successPatterns: [
                        'Lifestyle product integration',
                        'Trend-forward styling',
                        'Customer styling examples',
                        'Seasonal fashion guides',
                        'Sustainable fashion stories'
                    ]
                }
            ],
            fitness: [
                {
                    businessType: 'fitness',
                    platform: 'instagram',
                    averageEngagement: 4.1,
                    topPerformerEngagement: 9.3,
                    averageReach: 16.7,
                    bestPractices: [
                        'Transformation stories',
                        'Workout demonstrations',
                        'Motivational content',
                        'Community challenges',
                        'Expert tips and advice'
                    ],
                    commonMistakes: [
                        'Intimidating content for beginners',
                        'Only showing perfect bodies',
                        'Generic motivational quotes',
                        'Not addressing different fitness levels',
                        'Ignoring mental health aspects'
                    ],
                    successPatterns: [
                        'Inclusive fitness content',
                        'Real transformation journeys',
                        'Beginner-friendly workouts',
                        'Mental health and fitness connection',
                        'Community support stories'
                    ]
                }
            ],
            beauty: [
                {
                    businessType: 'beauty',
                    platform: 'instagram',
                    averageEngagement: 3.7,
                    topPerformerEngagement: 8.9,
                    averageReach: 14.3,
                    bestPractices: [
                        'Before/after transformations',
                        'Tutorial content',
                        'Product demonstrations',
                        'Skin care education',
                        'Inclusive beauty content'
                    ],
                    commonMistakes: [
                        'Over-filtered photos',
                        'Not showing diverse skin types',
                        'Generic beauty tips',
                        'Ignoring skincare science',
                        'Not addressing common concerns'
                    ],
                    successPatterns: [
                        'Natural beauty enhancement',
                        'Educational skincare content',
                        'Diverse model representation',
                        'Seasonal beauty tips',
                        'Self-care and confidence building'
                    ]
                }
            ]
        };
        Object.entries(benchmarks).forEach(([businessType, benchmarkArray])=>{
            this.industryBenchmarks.set(businessType, benchmarkArray);
        });
    }
    /**
   * Initialize success patterns for content optimization
   */ initializeSuccessPatterns() {
        const patterns = {
            'high-engagement-headlines': [
                'Question-based headlines that spark curiosity',
                'Numbers and statistics in headlines',
                'Emotional trigger words',
                'Local references and community connection',
                'Trending topic integration',
                'Problem-solution format',
                'Exclusive or limited-time offers',
                'Behind-the-scenes insights'
            ],
            'effective-captions': [
                'Storytelling approach',
                'Personal anecdotes and experiences',
                'Call-to-action integration',
                'Community questions and engagement',
                'Educational value provision',
                'Emotional connection building',
                'Local culture and language integration',
                'Trending hashtag utilization'
            ],
            'compelling-ctas': [
                'Action-oriented language',
                'Urgency and scarcity elements',
                'Clear value proposition',
                'Personalized messaging',
                'Community-focused calls',
                'Experience-based invitations',
                'Social proof integration',
                'Local relevance emphasis'
            ]
        };
        Object.entries(patterns).forEach(([category, patternList])=>{
            this.contentPatterns.set(category, patternList);
        });
    }
    /**
   * Analyze content performance against industry benchmarks
   */ analyzePerformance(post, profile, actualMetrics) {
        const benchmarks = this.industryBenchmarks.get(profile.businessType) || [];
        const platformBenchmark = benchmarks.find((b)=>b.platform === post.platform);
        if (!platformBenchmark) {
            return this.generateGenericOptimization();
        }
        // Analyze content elements
        const headlineAnalysis = this.analyzeHeadline(post.headline, platformBenchmark);
        const captionAnalysis = this.analyzeCaption(post.caption, platformBenchmark);
        const ctaAnalysis = this.analyzeCTA(post.cta, platformBenchmark);
        const hashtagAnalysis = this.analyzeHashtags(post.hashtags, platformBenchmark);
        // Generate optimization recommendations
        const optimization = {
            strengths: [
                ...headlineAnalysis.strengths,
                ...captionAnalysis.strengths,
                ...ctaAnalysis.strengths,
                ...hashtagAnalysis.strengths
            ],
            improvements: [
                ...headlineAnalysis.improvements,
                ...captionAnalysis.improvements,
                ...ctaAnalysis.improvements,
                ...hashtagAnalysis.improvements
            ],
            recommendations: this.generateRecommendations(platformBenchmark, profile),
            nextIterationFocus: this.identifyNextIterationFocus(platformBenchmark, profile),
            competitiveAdvantages: this.identifyCompetitiveAdvantages(platformBenchmark, profile)
        };
        return optimization;
    }
    /**
   * Analyze headline effectiveness
   */ analyzeHeadline(headline, benchmark) {
        const strengths = [];
        const improvements = [];
        // Check for success patterns
        const successPatterns = this.contentPatterns.get('high-engagement-headlines') || [];
        if (headline.includes('?')) {
            strengths.push('Uses question format to engage audience');
        } else {
            improvements.push('Consider using questions to increase engagement');
        }
        if (/\d+/.test(headline)) {
            strengths.push('Includes numbers for credibility');
        } else {
            improvements.push('Consider adding specific numbers or statistics');
        }
        if (headline.length > 10 && headline.length < 60) {
            strengths.push('Optimal headline length for platform');
        } else {
            improvements.push('Adjust headline length for better readability');
        }
        // Check for emotional triggers
        const emotionalWords = [
            'amazing',
            'incredible',
            'exclusive',
            'limited',
            'secret',
            'proven'
        ];
        if (emotionalWords.some((word)=>headline.toLowerCase().includes(word))) {
            strengths.push('Uses emotional trigger words');
        } else {
            improvements.push('Add emotional trigger words to increase appeal');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze caption effectiveness
   */ analyzeCaption(caption, benchmark) {
        const strengths = [];
        const improvements = [];
        if (caption.length > 50 && caption.length < 300) {
            strengths.push('Optimal caption length for engagement');
        } else {
            improvements.push('Adjust caption length for better engagement');
        }
        // Check for storytelling elements
        if (caption.includes('we') || caption.includes('our') || caption.includes('story')) {
            strengths.push('Uses storytelling approach');
        } else {
            improvements.push('Add storytelling elements to create connection');
        }
        // Check for community engagement
        if (caption.includes('?') || caption.includes('comment') || caption.includes('share')) {
            strengths.push('Encourages community engagement');
        } else {
            improvements.push('Add questions or engagement prompts');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze CTA effectiveness
   */ analyzeCTA(cta, benchmark) {
        const strengths = [];
        const improvements = [];
        const actionWords = [
            'visit',
            'book',
            'call',
            'order',
            'try',
            'discover',
            'experience'
        ];
        if (actionWords.some((word)=>cta.toLowerCase().includes(word))) {
            strengths.push('Uses strong action words');
        } else {
            improvements.push('Use more compelling action words');
        }
        if (cta.length > 5 && cta.length < 50) {
            strengths.push('Appropriate CTA length');
        } else {
            improvements.push('Optimize CTA length for clarity');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze hashtag strategy
   */ analyzeHashtags(hashtags, benchmark) {
        const strengths = [];
        const improvements = [];
        if (hashtags.length >= 5 && hashtags.length <= 10) {
            strengths.push('Optimal number of hashtags');
        } else {
            improvements.push('Adjust hashtag count for better reach');
        }
        // Check for mix of popular and niche hashtags
        const hasPopular = hashtags.some((tag)=>tag.includes('trending') || tag.includes('viral'));
        const hasNiche = hashtags.some((tag)=>tag.length > 15);
        if (hasPopular && hasNiche) {
            strengths.push('Good mix of popular and niche hashtags');
        } else {
            improvements.push('Balance popular and niche hashtags for better reach');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Generate specific recommendations based on benchmarks
   */ generateRecommendations(benchmark, profile) {
        const recommendations = [];
        // Add benchmark-specific recommendations
        benchmark.bestPractices.forEach((practice)=>{
            recommendations.push(`Implement: ${practice}`);
        });
        // Add business-specific recommendations
        recommendations.push(`Leverage ${profile.location} local culture and events`);
        recommendations.push(`Highlight unique selling points: ${profile.uniqueSellingPoints.join(', ')}`);
        recommendations.push(`Target ${profile.targetAudience} with personalized messaging`);
        return recommendations.slice(0, 8); // Limit to top 8 recommendations
    }
    /**
   * Identify focus areas for next iteration
   */ identifyNextIterationFocus(benchmark, profile) {
        const focus = [];
        // Focus on top-performing patterns
        benchmark.successPatterns.forEach((pattern)=>{
            focus.push(`Enhance: ${pattern}`);
        });
        // Avoid common mistakes
        benchmark.commonMistakes.forEach((mistake)=>{
            focus.push(`Avoid: ${mistake}`);
        });
        return focus.slice(0, 6); // Limit to top 6 focus areas
    }
    /**
   * Identify competitive advantages
   */ identifyCompetitiveAdvantages(benchmark, profile) {
        const advantages = [];
        // Business-specific advantages
        profile.uniqueSellingPoints.forEach((usp)=>{
            advantages.push(`Unique advantage: ${usp}`);
        });
        // Location-based advantages
        advantages.push(`Local market expertise in ${profile.location}`);
        advantages.push(`Community connection and trust`);
        advantages.push(`Cultural understanding and relevance`);
        return advantages.slice(0, 5); // Limit to top 5 advantages
    }
    /**
   * Generate generic optimization for unknown business types
   */ generateGenericOptimization() {
        return {
            strengths: [
                'Content created with business context'
            ],
            improvements: [
                'Add industry-specific benchmarks',
                'Enhance local relevance'
            ],
            recommendations: [
                'Research industry best practices',
                'Analyze competitor content'
            ],
            nextIterationFocus: [
                'Improve targeting',
                'Enhance engagement'
            ],
            competitiveAdvantages: [
                'Personalized approach',
                'Local market focus'
            ]
        };
    }
    /**
   * Track performance over time for continuous improvement
   */ trackPerformance(businessName, metrics) {
        const history = this.performanceHistory.get(businessName) || [];
        history.push(metrics);
        this.performanceHistory.set(businessName, history.slice(-20)); // Keep last 20 records
    }
    /**
   * Get performance trends for a business
   */ getPerformanceTrends(businessName) {
        const history = this.performanceHistory.get(businessName) || [];
        if (history.length < 2) {
            return {
                trend: 'stable',
                averageScore: history[0]?.overallScore || 0,
                bestPerformingContent: []
            };
        }
        const recent = history.slice(-5);
        const older = history.slice(-10, -5);
        const recentAvg = recent.reduce((sum, m)=>sum + m.overallScore, 0) / recent.length;
        const olderAvg = older.reduce((sum, m)=>sum + m.overallScore, 0) / older.length;
        let trend = 'stable';
        if (recentAvg > olderAvg + 0.5) trend = 'improving';
        else if (recentAvg < olderAvg - 0.5) trend = 'declining';
        const averageScore = history.reduce((sum, m)=>sum + m.overallScore, 0) / history.length;
        return {
            trend,
            averageScore,
            bestPerformingContent: [
                'High-engagement headlines',
                'Community-focused content',
                'Local relevance'
            ]
        };
    }
}
const performanceAnalyzer = new ContentPerformanceAnalyzer();
}}),
"[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Viral Hashtag Engine - Real-time trending hashtag generation
 * Integrates with RSS feeds and trending data to generate viral hashtags
 */ __turbopack_context__.s({
    "ViralHashtagEngine": (()=>ViralHashtagEngine),
    "viralHashtagEngine": (()=>viralHashtagEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
;
class ViralHashtagEngine {
    /**
   * Generate viral hashtag strategy using real-time trending data
   */ async generateViralHashtags(businessType, businessName, location, platform, services, targetAudience) {
        try {
            // Get trending data from RSS feeds and trending enhancer
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                businessType,
                location,
                platform,
                targetAudience
            });
            // Generate different hashtag categories
            const trending = await this.getTrendingHashtags(trendingData, businessType, platform);
            const viral = this.getViralHashtags(businessType, platform);
            const niche = this.getNicheHashtags(businessType, services);
            const location_tags = this.getLocationHashtags(location);
            const community = this.getCommunityHashtags(businessType, targetAudience);
            const seasonal = this.getSeasonalHashtags();
            const platform_tags = this.getPlatformHashtags(platform);
            // Combine and optimize for virality
            const total = this.optimizeForVirality([
                ...trending.slice(0, 4),
                ...viral.slice(0, 3),
                ...niche.slice(0, 2),
                ...location_tags.slice(0, 2),
                ...community.slice(0, 2),
                ...seasonal.slice(0, 1),
                ...platform_tags.slice(0, 1)
            ]);
            return {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total
            };
        } catch (error) {
            return this.getFallbackHashtags(businessType, location, platform);
        }
    }
    /**
   * Get trending hashtags from RSS data
   */ async getTrendingHashtags(trendingData, businessType, platform) {
        const hashtags = [
            ...trendingData.hashtags
        ];
        // Add business-relevant trending hashtags
        const businessTrending = this.getBusinessTrendingHashtags(businessType, platform);
        hashtags.push(...businessTrending);
        // Remove duplicates and return top trending
        return Array.from(new Set(hashtags)).slice(0, 8);
    }
    /**
   * Get high-engagement viral hashtags
   */ getViralHashtags(businessType, platform) {
        const viralHashtags = {
            general: [
                '#viral',
                '#trending',
                '#fyp',
                '#explore',
                '#discover',
                '#amazing',
                '#incredible',
                '#mustsee'
            ],
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#reels',
                '#explorepage'
            ],
            tiktok: [
                '#fyp',
                '#foryou',
                '#viral',
                '#trending',
                '#foryoupage'
            ],
            facebook: [
                '#viral',
                '#share',
                '#community',
                '#local',
                '#trending'
            ],
            twitter: [
                '#trending',
                '#viral',
                '#breaking',
                '#news',
                '#update'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#networking',
                '#career',
                '#industry'
            ]
        };
        const general = viralHashtags.general.sort(()=>0.5 - Math.random()).slice(0, 4);
        const platformSpecific = viralHashtags[platform.toLowerCase()] || [];
        return [
            ...general,
            ...platformSpecific.slice(0, 3)
        ];
    }
    /**
   * Get business-specific niche hashtags
   */ getNicheHashtags(businessType, services) {
        const nicheMap = {
            restaurant: [
                '#foodie',
                '#delicious',
                '#freshfood',
                '#localeats',
                '#foodlover',
                '#tasty',
                '#chef',
                '#dining'
            ],
            bakery: [
                '#freshbaked',
                '#artisan',
                '#homemade',
                '#bakery',
                '#pastry',
                '#bread',
                '#dessert',
                '#sweet'
            ],
            fitness: [
                '#fitness',
                '#workout',
                '#health',
                '#gym',
                '#strong',
                '#motivation',
                '#fitlife',
                '#training'
            ],
            beauty: [
                '#beauty',
                '#skincare',
                '#makeup',
                '#glam',
                '#selfcare',
                '#beautiful',
                '#style',
                '#cosmetics'
            ],
            tech: [
                '#tech',
                '#innovation',
                '#digital',
                '#software',
                '#technology',
                '#startup',
                '#coding',
                '#ai'
            ],
            retail: [
                '#shopping',
                '#fashion',
                '#style',
                '#sale',
                '#newcollection',
                '#boutique',
                '#trendy',
                '#deals'
            ]
        };
        const baseNiche = nicheMap[businessType.toLowerCase()] || [
            '#business',
            '#service',
            '#quality',
            '#professional'
        ];
        // Add service-specific hashtags if provided
        if (services) {
            const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            baseNiche.push(...serviceHashtags);
        }
        return baseNiche.slice(0, 6);
    }
    /**
   * Get location-based hashtags
   */ getLocationHashtags(location) {
        const locationParts = location.split(',').map((part)=>part.trim());
        const hashtags = [];
        locationParts.forEach((part)=>{
            const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '');
            if (cleanLocation.length > 2) {
                hashtags.push(`#${cleanLocation.toLowerCase()}`);
            }
        });
        // Add generic location hashtags
        hashtags.push('#local', '#community', '#neighborhood');
        return hashtags.slice(0, 5);
    }
    /**
   * Get community engagement hashtags
   */ getCommunityHashtags(businessType, targetAudience) {
        const communityHashtags = [
            '#community',
            '#local',
            '#support',
            '#family',
            '#friends',
            '#together',
            '#love'
        ];
        if (targetAudience) {
            const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            communityHashtags.push(...audienceHashtags);
        }
        return communityHashtags.slice(0, 5);
    }
    /**
   * Get seasonal/timely hashtags
   */ getSeasonalHashtags() {
        const now = new Date();
        const month = now.getMonth();
        const day = now.getDate();
        // Seasonal hashtags based on current time
        const seasonal = {
            0: [
                '#newyear',
                '#january',
                '#fresh',
                '#newbeginnings'
            ],
            1: [
                '#february',
                '#love',
                '#valentine',
                '#winter'
            ],
            2: [
                '#march',
                '#spring',
                '#fresh',
                '#bloom'
            ],
            3: [
                '#april',
                '#spring',
                '#easter',
                '#renewal'
            ],
            4: [
                '#may',
                '#spring',
                '#mothers',
                '#bloom'
            ],
            5: [
                '#june',
                '#summer',
                '#fathers',
                '#sunshine'
            ],
            6: [
                '#july',
                '#summer',
                '#vacation',
                '#hot'
            ],
            7: [
                '#august',
                '#summer',
                '#vacation',
                '#sunny'
            ],
            8: [
                '#september',
                '#fall',
                '#autumn',
                '#backtoschool'
            ],
            9: [
                '#october',
                '#fall',
                '#halloween',
                '#autumn'
            ],
            10: [
                '#november',
                '#thanksgiving',
                '#grateful',
                '#fall'
            ],
            11: [
                '#december',
                '#christmas',
                '#holiday',
                '#winter'
            ] // December
        };
        return seasonal[month] || [
            '#today',
            '#now',
            '#current'
        ];
    }
    /**
   * Get platform-specific hashtags
   */ getPlatformHashtags(platform) {
        const platformHashtags = {
            instagram: [
                '#instagram',
                '#insta',
                '#ig'
            ],
            facebook: [
                '#facebook',
                '#fb',
                '#social'
            ],
            twitter: [
                '#twitter',
                '#tweet',
                '#x'
            ],
            linkedin: [
                '#linkedin',
                '#professional',
                '#business'
            ],
            tiktok: [
                '#tiktok',
                '#tt',
                '#video'
            ]
        };
        return platformHashtags[platform.toLowerCase()] || [
            '#social',
            '#media'
        ];
    }
    /**
   * Get business-relevant trending hashtags
   */ getBusinessTrendingHashtags(businessType, platform) {
        // This would integrate with real trending APIs in production
        const trendingByBusiness = {
            restaurant: [
                '#foodtrends',
                '#eats2024',
                '#localfood',
                '#foodie'
            ],
            fitness: [
                '#fitness2024',
                '#healthtrends',
                '#workout',
                '#wellness'
            ],
            beauty: [
                '#beautytrends',
                '#skincare2024',
                '#makeup',
                '#selfcare'
            ],
            tech: [
                '#tech2024',
                '#innovation',
                '#ai',
                '#digital'
            ],
            retail: [
                '#fashion2024',
                '#shopping',
                '#style',
                '#trends'
            ]
        };
        return trendingByBusiness[businessType.toLowerCase()] || [
            '#trending',
            '#popular',
            '#new'
        ];
    }
    /**
   * Optimize hashtag selection for maximum virality
   */ optimizeForVirality(hashtags) {
        // Remove duplicates
        const unique = Array.from(new Set(hashtags));
        // Sort by estimated engagement potential (simplified scoring)
        const scored = unique.map((tag)=>({
                tag,
                score: this.calculateViralScore(tag)
            }));
        scored.sort((a, b)=>b.score - a.score);
        return scored.slice(0, 15).map((item)=>item.tag);
    }
    /**
   * Calculate viral potential score for a hashtag
   */ calculateViralScore(hashtag) {
        let score = 0;
        // High-engagement keywords get bonus points
        const viralKeywords = [
            'viral',
            'trending',
            'fyp',
            'explore',
            'amazing',
            'incredible'
        ];
        if (viralKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 10;
        }
        // Platform-specific hashtags get bonus
        const platformKeywords = [
            'instagram',
            'tiktok',
            'reels',
            'story'
        ];
        if (platformKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 5;
        }
        // Local hashtags get moderate bonus
        const localKeywords = [
            'local',
            'community',
            'neighborhood'
        ];
        if (localKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 3;
        }
        // Length penalty (very long hashtags perform worse)
        if (hashtag.length > 20) score -= 2;
        if (hashtag.length > 30) score -= 5;
        return score + Math.random(); // Add randomness for variety
    }
    /**
   * Fallback hashtags when trending data fails
   */ getFallbackHashtags(businessType, location, platform) {
        return {
            trending: [
                '#trending',
                '#viral',
                '#popular',
                '#new'
            ],
            viral: [
                '#amazing',
                '#incredible',
                '#mustsee',
                '#wow'
            ],
            niche: [
                `#${businessType}`,
                '#quality',
                '#professional',
                '#service'
            ],
            location: [
                '#local',
                '#community',
                `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`
            ],
            community: [
                '#community',
                '#support',
                '#family',
                '#love'
            ],
            seasonal: [
                '#today',
                '#now'
            ],
            platform: [
                `#${platform.toLowerCase()}`
            ],
            total: [
                '#trending',
                '#viral',
                `#${businessType}`,
                '#local',
                '#community',
                '#amazing',
                '#quality',
                '#professional',
                '#popular',
                '#new',
                '#support',
                '#service',
                `#${platform.toLowerCase()}`,
                '#today',
                '#love'
            ]
        };
    }
}
const viralHashtagEngine = new ViralHashtagEngine();
}}),
"[project]/src/ai/dynamic-cta-generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Dynamic CTA Generator - Creates compelling, conversion-focused call-to-actions
 * Uses AI and business intelligence to generate CTAs that drive action
 */ __turbopack_context__.s({
    "DynamicCTAGenerator": (()=>DynamicCTAGenerator),
    "dynamicCTAGenerator": (()=>dynamicCTAGenerator)
});
class DynamicCTAGenerator {
    /**
   * Generate dynamic, conversion-focused CTA
   */ async generateDynamicCTA(businessName, businessType, location, platform, contentGoal = 'engagement', services, targetAudience) {
        // Select optimal CTA style based on business context
        const ctaStyle = this.selectOptimalCTAStyle(businessType, platform, contentGoal);
        // Generate primary CTA
        const primary = this.generateCTAByStyle(ctaStyle, businessName, businessType, location, platform, services);
        // Generate alternatives for A/B testing
        const alternatives = this.generateAlternativeCTAs(businessName, businessType, location, platform, services);
        // Get reasoning for CTA choice
        const reasoning = this.getCTAReasoning(ctaStyle, businessType, platform);
        return {
            primary,
            alternatives,
            style: ctaStyle,
            reasoning,
            platform
        };
    }
    /**
   * Select optimal CTA style based on business context
   */ selectOptimalCTAStyle(businessType, platform, contentGoal) {
        const businessCTAMap = {
            restaurant: [
                'URGENCY',
                'INVITATION',
                'SENSORY',
                'LOCAL_REFERENCE'
            ],
            bakery: [
                'SENSORY',
                'URGENCY',
                'INVITATION',
                'COMMUNITY'
            ],
            fitness: [
                'CHALLENGE',
                'BENEFIT_FOCUSED',
                'MOTIVATION',
                'PERSONAL'
            ],
            beauty: [
                'TRANSFORMATION',
                'CONFIDENCE',
                'EXCLUSIVE',
                'PERSONAL'
            ],
            retail: [
                'URGENCY',
                'EXCLUSIVE',
                'BENEFIT_FOCUSED',
                'DISCOVERY'
            ],
            tech: [
                'INNOVATION',
                'BENEFIT_FOCUSED',
                'CURIOSITY',
                'PROFESSIONAL'
            ],
            service: [
                'DIRECT_ACTION',
                'BENEFIT_FOCUSED',
                'TRUST',
                'LOCAL_REFERENCE'
            ]
        };
        const platformCTAMap = {
            instagram: [
                'VISUAL',
                'DISCOVERY',
                'COMMUNITY',
                'INVITATION'
            ],
            facebook: [
                'COMMUNITY',
                'LOCAL_REFERENCE',
                'INVITATION',
                'SHARE'
            ],
            twitter: [
                'URGENCY',
                'CURIOSITY',
                'DIRECT_ACTION',
                'TRENDING'
            ],
            linkedin: [
                'PROFESSIONAL',
                'BENEFIT_FOCUSED',
                'NETWORKING',
                'EXPERTISE'
            ],
            tiktok: [
                'CHALLENGE',
                'TRENDING',
                'VIRAL',
                'FUN'
            ]
        };
        const goalCTAMap = {
            engagement: [
                'CURIOSITY',
                'COMMUNITY',
                'INVITATION',
                'QUESTION'
            ],
            conversion: [
                'URGENCY',
                'BENEFIT_FOCUSED',
                'EXCLUSIVE',
                'DIRECT_ACTION'
            ],
            awareness: [
                'DISCOVERY',
                'CURIOSITY',
                'SHARE',
                'VIRAL'
            ],
            retention: [
                'COMMUNITY',
                'LOYALTY',
                'PERSONAL',
                'APPRECIATION'
            ]
        };
        // Get possible styles from each category
        const businessStyles = businessCTAMap[businessType.toLowerCase()] || [
            'DIRECT_ACTION',
            'BENEFIT_FOCUSED'
        ];
        const platformStyles = platformCTAMap[platform.toLowerCase()] || [
            'DIRECT_ACTION'
        ];
        const goalStyles = goalCTAMap[contentGoal.toLowerCase()] || [
            'ENGAGEMENT'
        ];
        // Find intersection or pick best match
        const allStyles = [
            ...businessStyles,
            ...platformStyles,
            ...goalStyles
        ];
        const styleCounts = allStyles.reduce((acc, style)=>{
            acc[style] = (acc[style] || 0) + 1;
            return acc;
        }, {});
        // Return style with highest count (most relevant)
        const bestStyle = Object.entries(styleCounts).sort(([, a], [, b])=>b - a)[0][0];
        return bestStyle;
    }
    /**
   * Generate CTA based on selected style
   */ generateCTAByStyle(style, businessName, businessType, location, platform, services) {
        const timestamp = Date.now();
        const variation = timestamp % 4; // 4 variations per style
        const ctaTemplates = {
            URGENCY: [
                `Book now - limited spots!`,
                `Don't wait - call today!`,
                `Limited time offer - act fast!`,
                `Only a few left - grab yours!`
            ],
            INVITATION: [
                `Come experience the difference!`,
                `Visit us this weekend!`,
                `Join our community today!`,
                `See what everyone's talking about!`
            ],
            CHALLENGE: [
                `Try to find better - we dare you!`,
                `Challenge yourself today!`,
                `Beat this quality anywhere!`,
                `Prove us wrong - we're confident!`
            ],
            BENEFIT_FOCUSED: [
                `Get more for your money!`,
                `Save time and hassle!`,
                `Double your results!`,
                `Feel the difference immediately!`
            ],
            COMMUNITY: [
                `Join the ${location} family!`,
                `Be part of something special!`,
                `Connect with your neighbors!`,
                `Share the love with friends!`
            ],
            CURIOSITY: [
                `Discover what makes us different!`,
                `Find out why locals choose us!`,
                `See what the buzz is about!`,
                `Uncover ${location}'s best kept secret!`
            ],
            LOCAL_REFERENCE: [
                `${location}'s favorite spot awaits!`,
                `Proudly serving ${location} families!`,
                `Your neighborhood ${businessType}!`,
                `Where ${location} locals go!`
            ],
            SENSORY: [
                `Taste the difference today!`,
                `Experience pure quality!`,
                `Feel the freshness!`,
                `Savor every moment!`
            ],
            EXCLUSIVE: [
                `VIP treatment awaits you!`,
                `Exclusive access - members only!`,
                `Premium experience guaranteed!`,
                `Elite service, just for you!`
            ],
            DIRECT_ACTION: [
                `Call us now!`,
                `Book your appointment!`,
                `Order online today!`,
                `Get started immediately!`
            ]
        };
        const templates = ctaTemplates[style] || ctaTemplates.DIRECT_ACTION;
        let cta = templates[variation];
        // Personalize with business name or location when appropriate
        if (Math.random() > 0.5 && !cta.includes(businessName) && !cta.includes(location)) {
            const personalizations = [
                `at ${businessName}`,
                `with ${businessName}`,
                `- ${businessName}`,
                `@ ${businessName}`
            ];
            const personalization = personalizations[variation % personalizations.length];
            cta = `${cta.replace('!', '')} ${personalization}!`;
        }
        return cta;
    }
    /**
   * Generate alternative CTAs for A/B testing
   */ generateAlternativeCTAs(businessName, businessType, location, platform, services) {
        const alternativeStyles = [
            'URGENCY',
            'INVITATION',
            'BENEFIT_FOCUSED',
            'COMMUNITY',
            'CURIOSITY'
        ];
        const alternatives = [];
        alternativeStyles.forEach((style)=>{
            const cta = this.generateCTAByStyle(style, businessName, businessType, location, platform, services);
            alternatives.push(cta);
        });
        // Remove duplicates and return top 3
        return Array.from(new Set(alternatives)).slice(0, 3);
    }
    /**
   * Get reasoning for CTA choice
   */ getCTAReasoning(style, businessType, platform) {
        const reasoningMap = {
            URGENCY: `Creates immediate action through scarcity and time pressure, effective for ${businessType} conversions`,
            INVITATION: `Builds welcoming community feeling, perfect for local ${businessType} businesses`,
            CHALLENGE: `Engages competitive spirit and confidence, great for ${businessType} differentiation`,
            BENEFIT_FOCUSED: `Highlights clear value proposition, drives ${businessType} decision-making`,
            COMMUNITY: `Leverages local connection and belonging, ideal for neighborhood ${businessType}`,
            CURIOSITY: `Sparks interest and discovery, effective for ${platform} engagement`,
            LOCAL_REFERENCE: `Emphasizes local pride and familiarity, builds ${businessType} trust`,
            SENSORY: `Appeals to emotional and physical experience, perfect for ${businessType}`,
            EXCLUSIVE: `Creates premium positioning and special treatment feeling`,
            DIRECT_ACTION: `Clear, straightforward instruction that drives immediate response`
        };
        return reasoningMap[style] || `Optimized for ${businessType} on ${platform} to drive engagement and conversions`;
    }
    /**
   * Generate platform-optimized CTA
   */ generatePlatformOptimizedCTA(baseCTA, platform) {
        const platformOptimizations = {
            instagram: (cta)=>`${cta} 📸✨`,
            facebook: (cta)=>`${cta} Share with friends! 👥`,
            twitter: (cta)=>`${cta} #${new Date().getFullYear()}`,
            linkedin: (cta)=>`${cta} Connect with us professionally.`,
            tiktok: (cta)=>`${cta} 🔥💯`
        };
        const optimizer = platformOptimizations[platform.toLowerCase()];
        return optimizer ? optimizer(baseCTA) : baseCTA;
    }
    /**
   * Generate time-sensitive CTA
   */ generateTimeSensitiveCTA(businessType, location) {
        const now = new Date();
        const hour = now.getHours();
        const day = now.getDay();
        const isWeekend = day === 0 || day === 6;
        if (hour < 11) {
            return `Start your day right - visit us now!`;
        } else if (hour < 14) {
            return `Perfect lunch break spot - come by!`;
        } else if (hour < 17) {
            return `Afternoon pick-me-up awaits!`;
        } else if (hour < 20) {
            return `End your day on a high note!`;
        } else {
            return `Evening treat - you deserve it!`;
        }
    }
    /**
   * Generate seasonal CTA
   */ generateSeasonalCTA(businessType, location) {
        const now = new Date();
        const month = now.getMonth();
        const seasonalCTAs = {
            0: [
                `New Year, new experiences - try us!`,
                `Start 2024 right with us!`
            ],
            1: [
                `Warm up with us this February!`,
                `Love is in the air - visit us!`
            ],
            2: [
                `Spring into action - book now!`,
                `Fresh start, fresh experience!`
            ],
            3: [
                `April showers bring May flowers - and great service!`,
                `Spring special awaits!`
            ],
            4: [
                `May we serve you today?`,
                `Mother's Day special - treat her!`
            ],
            5: [
                `Summer starts here - join us!`,
                `Father's Day celebration awaits!`
            ],
            6: [
                `Beat the heat with us!`,
                `Summer vibes, great service!`
            ],
            7: [
                `August special - don't miss out!`,
                `Late summer treat awaits!`
            ],
            8: [
                `Back to school, back to us!`,
                `Fall into great service!`
            ],
            9: [
                `October surprise awaits you!`,
                `Halloween special - spooktacular!`
            ],
            10: [
                `Thanksgiving gratitude - visit us!`,
                `Give thanks for great service!`
            ],
            11: [
                `Holiday magic awaits you!`,
                `Christmas special - ho ho ho!`
            ] // December
        };
        const monthCTAs = seasonalCTAs[month] || [
            `Visit us today!`,
            `Experience the difference!`
        ];
        return monthCTAs[Math.floor(Math.random() * monthCTAs.length)];
    }
}
const dynamicCTAGenerator = new DynamicCTAGenerator();
}}),
"[project]/src/ai/creative-enhancement.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced Business Intelligence & Strategic Content Generation System
 * Replaces generic templates with business-specific insights and strategic planning
 */ __turbopack_context__.s({
    "AntiRepetitionSystem": (()=>AntiRepetitionSystem),
    "BUSINESS_INTELLIGENCE_SYSTEM": (()=>BUSINESS_INTELLIGENCE_SYSTEM),
    "CONTENT_VARIATION_ENGINE": (()=>CONTENT_VARIATION_ENGINE),
    "CREATIVE_PROMPT_SYSTEM": (()=>CREATIVE_PROMPT_SYSTEM),
    "StrategicContentPlanner": (()=>StrategicContentPlanner),
    "analyzeBusinessContext": (()=>analyzeBusinessContext),
    "enhanceDesignCreativity": (()=>enhanceDesignCreativity),
    "generateBusinessSpecificCaption": (()=>generateBusinessSpecificCaption),
    "generateBusinessSpecificHeadline": (()=>generateBusinessSpecificHeadline),
    "generateBusinessSpecificSubheadline": (()=>generateBusinessSpecificSubheadline),
    "generateCreativeCTA": (()=>generateCreativeCTA),
    "generateCreativeHeadline": (()=>generateCreativeHeadline),
    "generateCreativeSeed": (()=>generateCreativeSeed),
    "generateCreativeSubheadline": (()=>generateCreativeSubheadline),
    "generateUnifiedContent": (()=>generateUnifiedContent),
    "getRandomElement": (()=>getRandomElement),
    "getRandomElements": (()=>getRandomElements)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$dynamic$2d$cta$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/dynamic-cta-generator.ts [app-route] (ecmascript)");
;
;
;
// Word Repetition Removal Function - Fixes issues like "buy now now pay later"
function removeWordRepetitions(text) {
    // Simple approach: split by spaces and check for consecutive duplicate words
    const words = text.split(/\s+/);
    const cleanedWords = [];
    for(let i = 0; i < words.length; i++){
        const currentWord = words[i];
        const previousWord = cleanedWords[cleanedWords.length - 1];
        // Skip if current word is the same as previous word (case-insensitive)
        // Only for actual words (not empty strings)
        if (currentWord && previousWord && currentWord.toLowerCase() === previousWord.toLowerCase() && currentWord.trim().length > 0) {
            continue; // Skip this duplicate word
        }
        cleanedWords.push(currentWord);
    }
    return cleanedWords.join(' ');
}
// Shared AI initialization to avoid duplicate variable names
function initializeAI() {
    const geminiApiKey = process.env.GOOGLE_AI_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.GEMINI_API_KEY;
    const genAI = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](geminiApiKey);
    // Use the same model as Revo 1.0 service for consistency
    return genAI.getGenerativeModel({
        model: 'gemini-2.5-flash-image-preview',
        generationConfig: {
            temperature: 0.9,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 4096
        }
    });
}
// Dynamic approach instructions to force variety
function getApproachInstructions(approach, businessName, location, creativityBoost) {
    switch(approach){
        case 'DIRECT_BENEFIT':
            return `HEADLINES: Lead with specific benefit. Example: "8g Protein Per Cookie" SUBHEADLINES: Expand with business details. Example: "Finally snacks that fuel kids properly - made fresh daily in ${location}" CAPTIONS: Full benefit story with RSS/business data.`;
        case 'SOCIAL_PROOF':
            return `HEADLINES: Reference community adoption. Example: "200+ ${location} Families Agree" SUBHEADLINES: Add business specifics. Example: "Our protein cookies beat sugar crashes every time" CAPTIONS: Full social proof story with testimonials and business intelligence.`;
        case 'PROBLEM_SOLUTION':
            return `HEADLINES: State the problem. Example: "Sugar Crashes Ruining Snacktime" SUBHEADLINES: Present solution. Example: "${businessName}'s protein cookies keep energy steady for hours" CAPTIONS: Full problem-solution narrative with business details.`;
        case 'LOCAL_INSIDER':
            return `HEADLINES: Use local insider knowledge. Example: "${location} Parents Secret Weapon" SUBHEADLINES: Add business insider details. Example: "What 500+ local families know about our cookies" CAPTIONS: Full insider story with local references and business intelligence.`;
        case 'URGENCY_SCARCITY':
            return `HEADLINES: Create real urgency. Example: "Only 50 Packs Left" SUBHEADLINES: Add business context. Example: "This week's batch selling faster than expected" CAPTIONS: Full urgency story with business details and RSS trends.`;
        case 'QUESTION_HOOK':
            return `HEADLINES: Ask specific question. Example: "Tired of Sugar Crashes?" SUBHEADLINES: Hint at solution. Example: "${businessName} has the protein-packed answer parents love" CAPTIONS: Full question-answer story with business intelligence.`;
        case 'STATISTIC_LEAD':
            return `HEADLINES: Lead with business statistic. Example: "95% Same-Day Fix Rate" SUBHEADLINES: Add context. Example: "Our certified technicians solve most issues within hours" CAPTIONS: Full statistic story with business details and proof.`;
        case 'STORY_ANGLE':
            return `HEADLINES: Start story hook. Example: "Local Baker's Secret Recipe" SUBHEADLINES: Continue story. Example: "Three generations of ${location} families can't be wrong" CAPTIONS: Full story with business history and customer experiences.`;
        case 'COMPARISON':
            return `HEADLINES: Set up comparison. Example: "Better Than Downtown Options" SUBHEADLINES: Specify difference. Example: "Same quality, half the price, right in ${location}" CAPTIONS: Full comparison with business advantages and local benefits.`;
        case 'NEWS_TREND':
            return `HEADLINES: Connect to current news/trends. Example: "Holiday Rush Solution Found" SUBHEADLINES: Add business connection. Example: "${businessName} handles your busiest season stress-free" CAPTIONS: Full trend connection with RSS data and business solutions.`;
        default:
            return `Create unique content that could only apply to ${businessName} in ${location}. Be specific and authentic.`;
    }
}
// Dynamic CTA style instructions to force variety
function getCtaStyleInstructions(style, businessName, location) {
    switch(style){
        case 'DIRECT_ACTION':
            return `Use action verbs specific to the business. Example: "Grab your protein cookies today" NOT generic "Shop now".`;
        case 'INVITATION':
            return `Sound like a personal invitation from ${businessName}. Example: "Come taste what ${location} is talking about" NOT generic invites.`;
        case 'CHALLENGE':
            return `Challenge the customer to try something. Example: "Find better cookies - we dare you" NOT generic challenges.`;
        case 'BENEFIT_FOCUSED':
            return `Lead with the specific benefit. Example: "Get 8g protein per bite" NOT generic benefits.`;
        case 'COMMUNITY':
            return `Reference the ${location} community. Example: "Join 200+ ${location} families" NOT generic community language.`;
        case 'URGENCY':
            return `Create real urgency with specifics. Example: "Only 50 left this week" NOT generic "limited time".`;
        case 'CURIOSITY':
            return `Make them curious about something specific. Example: "See why kids ask for seconds" NOT generic curiosity.`;
        case 'LOCAL_REFERENCE':
            return `Use actual ${location} references. Example: "Better than Main Street bakery" NOT generic local references.`;
        case 'PERSONAL':
            return `Sound personal and direct. Example: "Your kids will thank you" NOT generic personal language.`;
        case 'EXCLUSIVE':
            return `Make it feel exclusive to ${businessName}. Example: "Only at ${businessName}" NOT generic exclusivity.`;
        default:
            return `Create a unique CTA that could only apply to ${businessName} in ${location}.`;
    }
}
const BUSINESS_INTELLIGENCE_SYSTEM = {
    industryInsights: {
        'restaurant': {
            trends: [
                'farm-to-table',
                'fusion cuisine',
                'sustainable dining',
                'local ingredients',
                'chef-driven menus',
                'wine pairing',
                'seasonal specialties'
            ],
            challenges: [
                'food costs',
                'staff retention',
                'customer loyalty',
                'online reviews',
                'seasonal fluctuations',
                'competition from chains'
            ],
            opportunities: [
                'private dining',
                'catering services',
                'cooking classes',
                'wine tastings',
                'local partnerships'
            ],
            uniqueValue: [
                'chef expertise',
                'local sourcing',
                'authentic recipes',
                'atmosphere',
                'service quality'
            ],
            customerPainPoints: [
                'long wait times',
                'expensive prices',
                'limited options',
                'poor service',
                'generic food'
            ],
            successMetrics: [
                'repeat customers',
                'online reviews',
                'word-of-mouth',
                'reservations',
                'social media engagement'
            ],
            localCompetition: [
                'chain restaurants',
                'fast food',
                'other local restaurants',
                'food trucks',
                'delivery services'
            ],
            seasonalOpportunities: [
                'summer outdoor dining',
                'winter comfort food',
                'holiday specials',
                'local events',
                'weather-based menus'
            ]
        },
        'technology': {
            trends: [
                'AI integration',
                'automation',
                'cloud solutions',
                'cybersecurity',
                'digital transformation',
                'remote work tools'
            ],
            challenges: [
                'rapid change',
                'skill gaps',
                'security',
                'scalability',
                'competition',
                'client retention'
            ],
            opportunities: [
                'consulting services',
                'custom development',
                'training programs',
                'maintenance contracts',
                'upgrades'
            ],
            uniqueValue: [
                'technical expertise',
                'local support',
                'custom solutions',
                'ongoing partnership',
                'industry knowledge'
            ],
            customerPainPoints: [
                'complex technology',
                'high costs',
                'poor support',
                'outdated systems',
                'security concerns'
            ],
            successMetrics: [
                'client satisfaction',
                'project completion',
                'referrals',
                'long-term contracts',
                'technical outcomes'
            ],
            localCompetition: [
                'large tech companies',
                'freelancers',
                'other local tech firms',
                'national chains',
                'online services'
            ],
            seasonalOpportunities: [
                'year-end planning',
                'tax season',
                'new fiscal year',
                'back-to-school',
                'holiday e-commerce'
            ]
        },
        'healthcare': {
            trends: [
                'telemedicine',
                'preventive care',
                'patient experience',
                'digital health',
                'personalized medicine',
                'wellness focus'
            ],
            challenges: [
                'regulations',
                'patient trust',
                'technology adoption',
                'insurance complexity',
                'staff shortages'
            ],
            opportunities: [
                'preventive programs',
                'specialized services',
                'wellness coaching',
                'community outreach',
                'partnerships'
            ],
            uniqueValue: [
                'medical expertise',
                'personalized care',
                'local accessibility',
                'trusted relationships',
                'comprehensive services'
            ],
            customerPainPoints: [
                'long wait times',
                'high costs',
                'complex insurance',
                'poor communication',
                'impersonal care'
            ],
            successMetrics: [
                'patient outcomes',
                'satisfaction scores',
                'referrals',
                'community trust',
                'health improvements'
            ],
            localCompetition: [
                'hospitals',
                'other clinics',
                'urgent care centers',
                'specialists',
                'online health services'
            ],
            seasonalOpportunities: [
                'flu season',
                'summer wellness',
                'back-to-school checkups',
                'holiday stress',
                'new year resolutions'
            ]
        },
        'fitness': {
            trends: [
                'home workouts',
                'personal training',
                'group classes',
                'mind-body connection',
                'nutrition integration',
                'wearable tech'
            ],
            challenges: [
                'member retention',
                'seasonal fluctuations',
                'competition',
                'staff turnover',
                'facility costs'
            ],
            opportunities: [
                'online programs',
                'corporate wellness',
                'specialized training',
                'nutrition coaching',
                'community events'
            ],
            uniqueValue: [
                'expert trainers',
                'community atmosphere',
                'personalized programs',
                'convenient location',
                'proven results'
            ],
            customerPainPoints: [
                'lack of motivation',
                'time constraints',
                'intimidation',
                'poor results',
                'expensive memberships'
            ],
            successMetrics: [
                'member retention',
                'goal achievement',
                'referrals',
                'class attendance',
                'community engagement'
            ],
            localCompetition: [
                'other gyms',
                'home equipment',
                'online programs',
                'personal trainers',
                'sports clubs'
            ],
            seasonalOpportunities: [
                'new year resolutions',
                'summer body prep',
                'holiday fitness',
                'back-to-school',
                'weather-based activities'
            ]
        },
        'retail': {
            trends: [
                'omnichannel',
                'personalization',
                'sustainability',
                'local sourcing',
                'experiential shopping',
                'community focus'
            ],
            challenges: [
                'online competition',
                'inventory management',
                'customer experience',
                'seasonal sales',
                'staff training'
            ],
            opportunities: [
                'online presence',
                'local partnerships',
                'loyalty programs',
                'events',
                'personal shopping'
            ],
            uniqueValue: [
                'curated selection',
                'personal service',
                'local knowledge',
                'quality products',
                'community connection'
            ],
            customerPainPoints: [
                'limited selection',
                'high prices',
                'poor service',
                'inconvenient hours',
                'lack of expertise'
            ],
            successMetrics: [
                'sales growth',
                'customer loyalty',
                'repeat purchases',
                'word-of-mouth',
                'community engagement'
            ],
            localCompetition: [
                'online retailers',
                'big box stores',
                'other local shops',
                'malls',
                'direct sales'
            ],
            seasonalOpportunities: [
                'holiday shopping',
                'back-to-school',
                'summer sales',
                'seasonal products',
                'local events'
            ]
        },
        'real-estate': {
            trends: [
                'virtual tours',
                'digital marketing',
                'local expertise',
                'investment focus',
                'sustainability',
                'smart homes'
            ],
            challenges: [
                'market fluctuations',
                'competition',
                'client acquisition',
                'market knowledge',
                'technology adoption'
            ],
            opportunities: [
                'investment properties',
                'property management',
                'consulting services',
                'local partnerships',
                'specialized markets'
            ],
            uniqueValue: [
                'local expertise',
                'market knowledge',
                'personal service',
                'proven track record',
                'community connections'
            ],
            customerPainPoints: [
                'high fees',
                'poor communication',
                'lack of expertise',
                'market uncertainty',
                'slow process'
            ],
            successMetrics: [
                'sales volume',
                'client satisfaction',
                'referrals',
                'market share',
                'repeat clients'
            ],
            localCompetition: [
                'other agents',
                'online platforms',
                'national companies',
                'for-sale-by-owner',
                'investors'
            ],
            seasonalOpportunities: [
                'spring market',
                'summer families',
                'fall investors',
                'winter deals',
                'holiday moves'
            ]
        },
        'automotive': {
            trends: [
                'electric vehicles',
                'digital services',
                'sustainability',
                'convenience',
                'technology integration',
                'online sales'
            ],
            challenges: [
                'parts shortages',
                'technician shortage',
                'technology changes',
                'competition',
                'customer expectations'
            ],
            opportunities: [
                'EV services',
                'mobile repair',
                'fleet services',
                'specialized repairs',
                'maintenance programs'
            ],
            uniqueValue: [
                'expert technicians',
                'honest service',
                'convenient location',
                'quality parts',
                'warranty support'
            ],
            customerPainPoints: [
                'expensive repairs',
                'poor service',
                'unreliable work',
                'long wait times',
                'hidden costs'
            ],
            successMetrics: [
                'customer satisfaction',
                'repeat business',
                'referrals',
                'online reviews',
                'service quality'
            ],
            localCompetition: [
                'dealerships',
                'other repair shops',
                'chain stores',
                'mobile services',
                'online parts'
            ],
            seasonalOpportunities: [
                'winter preparation',
                'summer road trips',
                'back-to-school',
                'holiday travel',
                'seasonal maintenance'
            ]
        },
        'beauty': {
            trends: [
                'clean beauty',
                'personalization',
                'sustainability',
                'wellness integration',
                'technology',
                'inclusivity'
            ],
            challenges: [
                'product costs',
                'staff retention',
                'trend changes',
                'competition',
                'client retention'
            ],
            opportunities: [
                'online services',
                'product sales',
                'membership programs',
                'events',
                'corporate services'
            ],
            uniqueValue: [
                'expert stylists',
                'quality products',
                'personalized service',
                'convenient location',
                'trend knowledge'
            ],
            customerPainPoints: [
                'high costs',
                'poor results',
                'long appointments',
                'limited availability',
                'product damage'
            ],
            successMetrics: [
                'client retention',
                'referrals',
                'online reviews',
                'service quality',
                'product sales'
            ],
            localCompetition: [
                'salons',
                'chain stores',
                'mobile services',
                'online products',
                'other beauty professionals'
            ],
            seasonalOpportunities: [
                'wedding season',
                'holiday parties',
                'summer styles',
                'back-to-school',
                'special events'
            ]
        }
    },
    audiencePsychology: {
        motivations: [
            'success',
            'security',
            'convenience',
            'status',
            'belonging',
            'growth',
            'health',
            'savings',
            'recognition'
        ],
        painPoints: [
            'time constraints',
            'budget concerns',
            'trust issues',
            'complexity',
            'uncertainty',
            'frustration',
            'stress'
        ],
        aspirations: [
            'better life',
            'success',
            'recognition',
            'peace of mind',
            'efficiency',
            'independence',
            'security'
        ],
        communication: [
            'clear benefits',
            'social proof',
            'emotional connection',
            'practical value',
            'expertise',
            'trust'
        ]
    },
    // New: Strategic Content Planning
    contentStrategy: {
        'awareness': {
            goal: 'Introduce business and build recognition',
            approach: 'Educational, informative, community-focused',
            contentTypes: [
                'industry insights',
                'local news',
                'educational tips',
                'community stories'
            ],
            emotionalTone: 'helpful, informative, community-minded'
        },
        'consideration': {
            goal: 'Build trust and demonstrate expertise',
            approach: 'Problem-solving, expertise demonstration, social proof',
            contentTypes: [
                'case studies',
                'expert tips',
                'customer stories',
                'industry knowledge'
            ],
            emotionalTone: 'expert, trustworthy, helpful'
        },
        'conversion': {
            goal: 'Drive action and sales',
            approach: 'Urgency, offers, clear benefits, strong CTAs',
            contentTypes: [
                'special offers',
                'limited time deals',
                'clear benefits',
                'action-oriented content'
            ],
            emotionalTone: 'urgent, compelling, confident'
        },
        'retention': {
            goal: 'Keep existing customers engaged',
            approach: 'Value-added content, community building, ongoing support',
            contentTypes: [
                'loyalty programs',
                'exclusive content',
                'community events',
                'ongoing value'
            ],
            emotionalTone: 'appreciative, supportive, community-focused'
        }
    }
};
class StrategicContentPlanner {
    static generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness') {
        const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
        const strategy = BUSINESS_INTELLIGENCE_SYSTEM.contentStrategy[contentGoal];
        // Analyze business strengths and opportunities
        const businessStrengths = this.analyzeBusinessStrengths(businessDetails, industry);
        const marketOpportunities = this.identifyMarketOpportunities(industry, location);
        const customerPainPoints = this.mapCustomerPainPoints(industry, businessStrengths);
        return {
            strategy: strategy,
            businessType: businessType,
            businessStrengths,
            marketOpportunities,
            customerPainPoints,
            contentAngle: this.determineContentAngle(contentGoal, businessStrengths, marketOpportunities),
            emotionalHook: this.selectEmotionalHook(contentGoal, customerPainPoints),
            valueProposition: this.craftValueProposition(businessStrengths, customerPainPoints),
            localRelevance: this.createLocalRelevance(location, industry, businessDetails)
        };
    }
    static analyzeBusinessStrengths(businessDetails, industry) {
        const strengths = [];
        if (businessDetails.experience) strengths.push(`${businessDetails.experience} years of experience`);
        if (businessDetails.expertise) strengths.push(`specialized in ${businessDetails.expertise}`);
        if (businessDetails.awards) strengths.push(`award-winning ${businessDetails.awards}`);
        if (businessDetails.certifications) strengths.push(`certified in ${businessDetails.certifications}`);
        if (businessDetails.uniqueServices) strengths.push(`unique ${businessDetails.uniqueServices} services`);
        // Add industry-specific strengths
        strengths.push(...industry.uniqueValue.slice(0, 3));
        return strengths;
    }
    static identifyMarketOpportunities(industry, location) {
        return industry.seasonalOpportunities.map((opportunity)=>`${opportunity} in ${location}`).slice(0, 3);
    }
    static mapCustomerPainPoints(industry, businessStrengths) {
        return industry.customerPainPoints.filter((painPoint)=>businessStrengths.some((strength)=>strength.toLowerCase().includes(painPoint.toLowerCase().replace(/\s+/g, '')))).slice(0, 3);
    }
    static determineContentAngle(contentGoal, businessStrengths, marketOpportunities) {
        const angles = {
            'awareness': [
                'educational',
                'community',
                'industry insights'
            ],
            'consideration': [
                'problem-solving',
                'expertise',
                'social proof'
            ],
            'conversion': [
                'benefits',
                'offers',
                'urgency'
            ],
            'retention': [
                'value-added',
                'community',
                'exclusive'
            ]
        };
        return angles[contentGoal] || angles['awareness'];
    }
    static selectEmotionalHook(contentGoal, customerPainPoints) {
        const hooks = {
            'awareness': [
                'curiosity',
                'community pride',
                'local knowledge'
            ],
            'consideration': [
                'frustration relief',
                'trust building',
                'expertise recognition'
            ],
            'conversion': [
                'urgency',
                'excitement',
                'confidence'
            ],
            'retention': [
                'appreciation',
                'belonging',
                'exclusive access'
            ]
        };
        return hooks[contentGoal] || hooks['awareness'];
    }
    static craftValueProposition(businessStrengths, customerPainPoints) {
        if (businessStrengths.length === 0 || customerPainPoints.length === 0) {
            return 'Quality service and expertise';
        }
        const strength = businessStrengths[0];
        const painPoint = customerPainPoints[0];
        return `We solve ${painPoint} with ${strength}`;
    }
    static createLocalRelevance(location, industry, businessDetails) {
        return {
            localMarket: `${location} market insights`,
            communityConnection: `${location} community focus`,
            localCompetition: `Understanding ${location} competition`,
            localOpportunities: industry.seasonalOpportunities.map((opp)=>`${opp} in ${location}`)
        };
    }
}
async function generateBusinessSpecificHeadline(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal);
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create dynamic AI prompt for headline generation with RSS trends and regional marketing intelligence
    const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];
    const trendingHashtags = trendingData?.hashtags?.slice(0, 3) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    const prompt = `You are a brilliant local marketing expert who deeply understands ${location} culture, language, and market dynamics. You stay updated with current trends and know exactly how businesses in ${location} market themselves successfully.

BUSINESS INTELLIGENCE:
- Business: ${businessName} (${businessType})
- Location: ${location}
- Experience: ${businessDetails.experience || 'established business'}
- Specialties: ${businessDetails.expertise || businessDetails.services || 'professional services'}
- Target Market: ${businessDetails.targetAudience || 'local community'}
- Marketing Goal: ${contentGoal}

CURRENT TRENDING CONTEXT (Use these insights to make content relevant):
- Trending Keywords: ${trendingKeywords.join(', ') || 'quality, authentic, local, fresh, community'}
- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Tone: ${regionalLanguage}

LOCAL MARKET INTELLIGENCE:
- Industry Trends: ${industry.trends.slice(0, 3).join(', ')}
- Competitive Advantages: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market Opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- How locals in ${location} talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

REGIONAL MARKETING STRATEGY:
You understand that in ${location}, people respond to ${marketingStyle} marketing. Use the trending keywords naturally and speak like locals do. Create content that feels authentic to ${location} culture and current market trends.

CONVERSION PSYCHOLOGY REQUIREMENTS:
- Maximum 5 words that trigger immediate desire to try/buy
- Use psychological triggers: scarcity, exclusivity, curiosity, FOMO
- Create urgency and desire - make people think "I NEED this NOW"
- Sound like a successful local marketer who knows conversion psychology
- Incorporate trending elements naturally (don't force them)
- Use language patterns that drive action in ${location}
- Focus on what makes people instantly want to experience ${businessName}
- Create curiosity gaps that make people want to know more

CONVERSION-FOCUSED EXAMPLES (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):
- "Secret Recipe Finally Revealed" (curiosity + exclusivity)
- "Last Batch This Week" (scarcity + urgency)
- "Addictive Flavors Warning Inside" (intrigue + benefit)
- "Hidden Gem Locals Obsess" (social proof + exclusivity)
- "Revolutionary Taste Experience Awaits" (innovation + anticipation)

CRITICAL ANTI-REPETITION INSTRUCTIONS:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

IMPORTANT: Generate ONLY ONE headline, not multiple options or lists.
Do NOT write "Here are headlines" or provide multiple choices.
Generate ONE unique headline that makes people instantly want to try ${businessName}. Focus on conversion, not just awareness.
Make it so specific to ${businessName} in ${location} that it could never be used for another business.`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original content that has never been generated before.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(prompt + uniqueContext);
        let headline = result.response.text().trim();
        // Post-process to remove word repetitions
        headline = removeWordRepetitions(headline);
        // Add randomization to approach and emotional impact to ensure variety
        const approaches = [
            'strategic',
            'creative',
            'authentic',
            'bold',
            'community-focused',
            'innovative'
        ];
        const emotions = [
            'engaging',
            'inspiring',
            'trustworthy',
            'exciting',
            'confident',
            'welcoming'
        ];
        return {
            headline: headline,
            approach: approaches[Math.floor(Math.random() * approaches.length)],
            emotionalImpact: emotions[Math.floor(Math.random() * emotions.length)]
        };
    } catch (error) {
        // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback
        try {
            const simplifiedHeadlinePrompt = `Create a unique 5-word headline for ${businessName}, a ${businessType} in ${location}.

Make it:
- Conversion-focused (makes people want to try it NOW)
- Different from typical marketing words
- Uses psychological triggers like scarcity, urgency, or exclusivity
- Locally relevant to ${location}

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before

Just return the headline, nothing else.`;
            // Add unique generation context to headline retry as well
            const headlineRetryContext = `\n\nUNIQUE HEADLINE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
      This headline retry must be completely different and avoid repetitive patterns.`;
            const retryResult = await model.generateContent(simplifiedHeadlinePrompt + headlineRetryContext);
            const retryHeadline = retryResult.response.text().trim();
            return {
                headline: retryHeadline,
                approach: 'ai-retry-generated',
                emotionalImpact: 'conversion-focused'
            };
        } catch (retryError) {
            // EMERGENCY AI GENERATION - Ultra Simple Prompt
            try {
                const emergencyPrompt = `Write a catchy 5-word headline for ${businessName} in ${location}. Make it unique and compelling.

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before`;
                // Add unique generation context to emergency headline generation as well
                const headlineEmergencyContext = `\n\nUNIQUE HEADLINE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
        This emergency headline must be completely different and avoid repetitive patterns.`;
                const emergencyResult = await model.generateContent(emergencyPrompt + headlineEmergencyContext);
                const emergencyHeadline = emergencyResult.response.text().trim();
                return {
                    headline: emergencyHeadline,
                    approach: 'emergency-ai-generated',
                    emotionalImpact: 'unique-ai-created'
                };
            } catch (emergencyError) {
                // LAST RESORT: Generate with current timestamp for uniqueness
                const timestamp = Date.now();
                const uniqueId = timestamp % 1000;
                const emergencyHeadlines = [
                    `${businessName} ${location} Experience`,
                    `Exclusive ${businessType} ${location}`,
                    `${location}'s Premium ${businessType}`,
                    `Limited ${businessName} Access`,
                    `Secret ${businessType} Discovery`
                ];
                return {
                    headline: emergencyHeadlines[uniqueId % emergencyHeadlines.length] + ` #${uniqueId}`,
                    approach: 'timestamp-unique',
                    emotionalImpact: 'emergency-fallback'
                };
            }
        }
    }
}
async function generateBusinessSpecificSubheadline(businessType, businessName, location, businessDetails, headline, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create marketing-focused AI prompt for subheadline generation with trending intelligence
    const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    const prompt = `You are a skilled local marketer creating a subheadline for ${businessName} that will make people in ${location} want to visit immediately. You understand current trends and local marketing patterns.

MARKETING CONTEXT:
- Main Headline: "${headline}"
- Business: ${businessName} (${businessType})
- Location: ${location}
- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}
- Target Market: Local ${location} residents and visitors
- Marketing Goal: ${contentGoal}

CURRENT TRENDING INTELLIGENCE:
- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Patterns: ${regionalLanguage}
- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

LOCAL MARKET INTELLIGENCE:
- What locals value: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- Industry trends: ${industry.trends.slice(0, 2).join(', ')}

REGIONAL MARKETING STRATEGY:
Create a subheadline that makes locals think "I need to try this place!" Use trending keywords naturally and speak like successful marketers in ${location} do. Focus on what makes ${businessName} irresistible to people in ${location}.

CONVERSION-FOCUSED SUBHEADLINE REQUIREMENTS:
- Maximum 14 words that trigger immediate action and desire
- Use psychological triggers: social proof, scarcity, exclusivity, urgency
- Create FOMO (Fear of Missing Out) - make people think they'll regret not trying
- Include specific benefits that answer "What's in it for me?"
- Use action-oriented language that drives immediate response
- Build on the headline's promise with compelling reasons to act NOW
- Sound like a successful conversion-focused marketer in ${location}
- Should make the offer irresistible and create urgency to visit/buy

Examples of effective ${location} subheadlines (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):
${getLocalMarketingExamples(location, businessType).split('\n').map((line)=>line.replace('- "', '- "').replace('"', '" (subheadline style)')).slice(0, 3).join('\n')}

CRITICAL ANTI-REPETITION INSTRUCTIONS FOR SUBHEADLINES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula
❌ Make it specific to ${businessName}'s actual services and features

Generate ONLY the subheadline text, nothing else.
Make it so specific to ${businessName} in ${location} that it could never be used for another business.`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This subheadline generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original subheadlines that have never been generated before.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(prompt + uniqueContext);
        let subheadline = result.response.text().trim();
        // Post-process to remove word repetitions
        subheadline = removeWordRepetitions(subheadline);
        // Add randomization to framework and benefit
        const frameworks = [
            'benefit-focused',
            'problem-solving',
            'community-centered',
            'expertise-driven',
            'results-oriented'
        ];
        const benefits = industry.uniqueValue.concat([
            'exceptional service',
            'local expertise',
            'proven results'
        ]);
        return {
            subheadline: subheadline,
            framework: frameworks[Math.floor(Math.random() * frameworks.length)],
            benefit: benefits[Math.floor(Math.random() * benefits.length)]
        };
    } catch (error) {
        // Marketing-focused fallback with enhanced randomization
        const timestamp = Date.now();
        const randomSeed = Math.floor(Math.random() * 1000) + timestamp;
        const variation = randomSeed % 16;
        const experienceWords = [
            'authentic',
            'fresh',
            'handcrafted',
            'traditional',
            'artisan',
            'premium',
            'local',
            'quality'
        ];
        const actionWords = [
            'discover',
            'experience',
            'taste',
            'enjoy',
            'savor',
            'explore',
            'try',
            'visit'
        ];
        const benefitPhrases = [
            'where quality meets tradition',
            'crafted with care daily',
            'your local favorite since day one',
            'bringing authentic flavors to life',
            'where every detail matters',
            'made fresh, served with pride',
            'your neighborhood gem awaits',
            'experience the difference'
        ];
        const marketingSubheadlines = [
            `${experienceWords[variation % experienceWords.length]} ${businessType} ${actionWords[(variation + 1) % actionWords.length]} in ${location}`,
            `${benefitPhrases[variation % benefitPhrases.length]}`,
            `${actionWords[variation % actionWords.length]} ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType} in ${location}`,
            `where ${location} locals ${actionWords[(variation + 3) % actionWords.length]} ${experienceWords[variation % experienceWords.length]} ${businessType}`,
            `${experienceWords[variation % experienceWords.length]} ingredients, ${experienceWords[(variation + 1) % experienceWords.length]} results`,
            `serving ${location} with ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType}`,
            `your go-to spot for ${experienceWords[variation % experienceWords.length]} ${businessType}`,
            `${benefitPhrases[(variation + 4) % benefitPhrases.length]}`,
            `bringing ${experienceWords[(variation + 3) % experienceWords.length]} ${businessType} to ${location}`,
            `${location}'s most ${experienceWords[(variation + 4) % experienceWords.length]} ${businessType} experience`,
            `${experienceWords[(variation + 5) % experienceWords.length]} ${businessType} crafted for ${location}`,
            `where ${experienceWords[variation % experienceWords.length]} meets ${experienceWords[(variation + 2) % experienceWords.length]}`,
            `${location} deserves ${experienceWords[(variation + 1) % experienceWords.length]} ${businessType}`,
            `creating ${experienceWords[(variation + 3) % experienceWords.length]} moments in ${location}`,
            `${experienceWords[(variation + 4) % experienceWords.length]} ${businessType}, ${experienceWords[(variation + 5) % experienceWords.length]} service`,
            `your ${location} destination for ${experienceWords[variation % experienceWords.length]} ${businessType}`
        ];
        return {
            subheadline: marketingSubheadlines[variation],
            framework: 'benefit-focused',
            benefit: experienceWords[variation % experienceWords.length]
        };
    }
}
async function generateUnifiedContent(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal);
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create marketing-focused AI prompt for unified content generation
    const trendingKeywords = trendingData?.keywords?.slice(0, 8) || [];
    const trendingHashtags = trendingData?.hashtags?.slice(0, 5) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    // INTELLIGENT APPROACH SELECTION - Let AI decide based on context
    const uniqueGenerationId = Date.now() + Math.floor(Math.random() * 1000);
    // DEBUG: Log what data we actually received
    // DYNAMIC ANTI-REPETITION SYSTEM - No hardcoded phrases
    const dynamicVariationSeed = uniqueGenerationId % 1000;
    const creativityBoost = Math.floor(Math.random() * 100) + dynamicVariationSeed;
    // FORCE DIFFERENT APPROACHES BASED ON GENERATION ID
    const approachStyles = [
        'DIRECT_BENEFIT',
        'SOCIAL_PROOF',
        'PROBLEM_SOLUTION',
        'LOCAL_INSIDER',
        'URGENCY_SCARCITY',
        'QUESTION_HOOK',
        'STATISTIC_LEAD',
        'STORY_ANGLE',
        'COMPARISON',
        'NEWS_TREND'
    ];
    const selectedApproach = approachStyles[creativityBoost % approachStyles.length];
    const uniquenessPrompt = `
MANDATORY APPROACH: ${selectedApproach}
You MUST use this specific approach - no other approach is allowed for this generation.

STRICT ANTI-REPETITION RULES:
❌ NEVER use "2025" or any year references like "2025's Best-Kept Secret"
❌ NEVER use "best-kept secret", "secret", "hidden gem", or mystery language
❌ NEVER use "chakula kizuri" - if using Swahili, use different phrases like "chakula bora", "vyakula vizuri", "lishe nzuri"
❌ NEVER use "Shop now via the link in our bio! Karibu!" - create completely unique CTAs
❌ NEVER use "Discover", "Experience", "Taste the", "Try our", "Indulge in"
❌ NEVER use formulaic patterns that sound like templates
❌ NEVER repeat the same opening words or sentence structures
❌ NEVER use "for your familia's delight" or similar repetitive family references
❌ AVOID any phrase that sounds like it could be copy-pasted to another business

APPROACH-SPECIFIC REQUIREMENTS (Apply to ALL components - headlines, subheadlines, captions):
${getApproachInstructions(selectedApproach, businessName, location, creativityBoost)}

CREATIVITY BOOST ${creativityBoost} CHALLENGE:
Create ALL COMPONENTS (headline, subheadline, caption) that are so unique and specific to ${businessName} in ${location} that they could NEVER be used for any other business. Use the actual business data, trending information, RSS feeds, local events, and business intelligence to create something genuinely original.

MANDATORY UNIQUENESS REQUIREMENTS:
- Each component must reference specific details about ${businessName}
- Headlines must connect to current events, trends, or local happenings
- Subheadlines must mention actual services, products, or business features
- Captions must tell a story specific to this business and location
- NO generic phrases that could apply to any ${businessType}
- NO template-like language patterns
- Every sentence must add unique value specific to ${businessName}

UNIFIED DATA INTEGRATION REQUIREMENTS:
- HEADLINES: Must incorporate RSS trends, current events, or seasonal opportunities
- SUBHEADLINES: Must reference specific business services, features, or intelligence data
- CAPTIONS: Must weave together all data sources into compelling marketing copy
- ALL COMPONENTS: Must tell the same story using the same data sources consistently

GENERATION UNIQUENESS ID: ${uniqueGenerationId}
Use this ID to ensure this content is completely different from any previous generation.
`;
    // FORCE DIFFERENT CTA STYLES
    const ctaStyles = [
        'DIRECT_ACTION',
        'INVITATION',
        'CHALLENGE',
        'BENEFIT_FOCUSED',
        'COMMUNITY',
        'URGENCY',
        'CURIOSITY',
        'LOCAL_REFERENCE',
        'PERSONAL',
        'EXCLUSIVE'
    ];
    const selectedCtaStyle = ctaStyles[creativityBoost % ctaStyles.length];
    const unifiedPrompt = `You are a conversion-focused social media marketer creating a COMPLETE UNIFIED CAMPAIGN for ${businessName} that will make people in ${location} take immediate action. You must create ALL components (headline, subheadline, caption, CTA, design direction) that work together as ONE cohesive message.

${uniquenessPrompt}

UNIFIED CAMPAIGN REQUIREMENTS:
- ALL components must tell the SAME STORY with consistent information
- Headline, subheadline, caption, and design must reinforce the SAME key message
- No contradictory information between components
- One unified theme that runs through everything
- Design direction must match the content tone and message
- All components should feel like they came from the same marketing campaign

MARKETING BRIEF:
- Business: ${businessName} (${businessType})
- Location: ${location}
- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}
- Target Market: Local ${location} residents and visitors
- Platform: ${platform}
- Marketing Goal: ${contentGoal}

CURRENT TRENDING INTELLIGENCE (From RSS feeds and market data):
- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community, experience, tradition, innovation'}
- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality #community #fresh'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Patterns: ${regionalLanguage}
- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

REAL BUSINESS INTELLIGENCE DATA:
${businessIntelligence ? `
- Business Strengths: ${businessIntelligence.businessStrengths?.join(', ') || 'quality service, customer satisfaction'}
- Value Propositions: ${businessIntelligence.valuePropositions?.join(', ') || 'exceptional quality, local expertise'}
- Target Emotions: ${businessIntelligence.targetEmotions?.join(', ') || 'trust, satisfaction, excitement'}
- Industry Keywords: ${businessIntelligence.industryKeywords?.join(', ') || 'professional, reliable, innovative'}
- Local Relevance: ${businessIntelligence.localRelevance?.join(', ') || 'community-focused, locally-owned'}
- Seasonal Opportunities: ${businessIntelligence.seasonalOpportunities?.join(', ') || 'year-round service'}
` : 'Use general business intelligence for this business type'}

LIVE RSS TRENDING DATA (Use this for ALL components - headlines, subheadlines, captions):
${trendingData ? `
- Current News Topics: ${trendingData.news?.slice(0, 3).map((n)=>n.title).join(', ') || 'No current news data'}
- Social Media Trends: ${trendingData.socialTrends?.slice(0, 3).join(', ') || 'No social trends data'}
- Local Events: ${trendingData.events?.slice(0, 2).map((e)=>e.name).join(', ') || 'No local events data'}
- Market Insights: ${trendingData.insights?.slice(0, 2).join(', ') || 'No market insights'}
` : 'No live RSS data available - use general market knowledge'}

HEADLINE GENERATION REQUIREMENTS (Use RSS data and business intelligence):
- HEADLINES must reference current trends, events, or news when relevant
- Connect ${businessName} to trending topics or local events naturally
- Use specific business services/features from business details
- Reference current market conditions or seasonal opportunities
- Make headlines feel current and timely, not generic
- Examples of RSS-integrated headlines:
  * "Local Food Festival Winner" (if there's a food event)
  * "Beat Holiday Rush Stress" (if trending topic is holiday stress)
  * "New Year Fitness Goals" (if trending topic is resolutions)
  * "Supply Chain Solution Found" (if news mentions supply issues)

SUBHEADLINE GENERATION REQUIREMENTS (Build on headline with business intelligence):
- SUBHEADLINES must expand on headline using specific business details
- Reference actual services, products, or unique features offered
- Use business intelligence data (industry trends, local opportunities)
- Connect to target audience pain points and solutions
- Support headline's promise with concrete business benefits
- Examples of business-integrated subheadlines:
  * "Our 15-year catering experience serves 200+ events monthly"
  * "Same-day delivery available for all ${location} residents"
  * "Certified organic ingredients sourced from local farms"
  * "24/7 emergency service with 30-minute response time"

SPECIFIC BUSINESS DETAILS:
- Business Name: ${businessName}
- Services/Products: ${businessDetails.services || businessDetails.expertise || businessDetails.specialties || 'quality offerings'}
- Unique Features: ${businessDetails.uniqueFeatures || businessDetails.keyFeatures || 'exceptional service'}
- Target Audience: ${businessDetails.targetAudience || `local ${location} residents and visitors`}
- Business Hours: ${businessDetails.hours || 'regular business hours'}
- Special Offers: ${businessDetails.offers || businessDetails.promotions || 'quality service at competitive prices'}

LOCAL MARKET INTELLIGENCE:
- What locals love: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- Industry trends: ${industry.trends.slice(0, 2).join(', ')}
- Local challenges: ${industry.challenges.slice(0, 2).join(', ')}

PLATFORM STRATEGY FOR ${platform.toUpperCase()}:
${getPlatformRequirements(platform)}

MARKETING COPY REQUIREMENTS:
You are a CONVERSION-FOCUSED MARKETER, not a creative writer or storyteller. Write MARKETING COPY that sells, not poetic descriptions.

WRITE LIKE A MARKETER:
• DIRECT & PUNCHY: Get to the point quickly - no flowery language
• BENEFIT-FOCUSED: Lead with what the customer gets, not poetic descriptions
• ACTION-ORIENTED: Every sentence should drive toward a purchase decision
• CONVERSATIONAL: Sound like a smart local business owner talking to neighbors
• URGENT: Create immediate desire to buy/visit NOW
• SPECIFIC: Use concrete benefits, not abstract concepts
• LOCAL: Sound like someone who actually lives in ${location}

INTELLIGENT PATTERN AVOIDANCE:
Use your AI intelligence to recognize and avoid:
- Repetitive opening patterns that sound robotic or formulaic
- Generic marketing speak that every business uses
- Overly creative writing that sounds like AI-generated poetry
- Cliché phrases that don't add value or authenticity
- Opening lines that could apply to any business in any location
- Patterns that sound like they came from a template or script

AUTHENTICITY TEST:
Ask yourself: "Would a real ${businessName} owner in ${location} actually say this to their neighbors?"
If it sounds too polished, too generic, or too AI-like, try a different approach.
Use the business intelligence data and local context to create something genuinely relevant.

WRITE LIKE THIS INSTEAD:
✅ "Your kids need healthy snacks. Samaki Cookies deliver."
✅ "15% off this week only - grab yours before they're gone"
✅ "Finally, cookies that are actually good for your family"
✅ "Nairobi parents are switching to Samaki Cookies. Here's why..."
✅ Direct, benefit-focused, action-driving copy

CRITICAL INSTRUCTION FOR ALL COMPONENTS:
- USE THE REAL DATA PROVIDED: Incorporate actual business details, trending topics, and local information
- HEADLINES: Must reference RSS trends, events, or business intelligence naturally
- SUBHEADLINES: Must mention actual services/products offered by ${businessName}
- CAPTIONS: Must weave together all data sources into compelling marketing copy
- CONNECT TO CURRENT TRENDS: Use the RSS trending data and current events when relevant
- LEVERAGE BUSINESS INTELLIGENCE: Use the actual business strengths and value propositions provided
- SPEAK LOCAL LANGUAGE: Use the regional language patterns and local cultural elements
- AVOID GENERIC CONTENT: Don't use placeholder text like "2025" or generic business descriptions
- CREATE PERSONALIZED CONTENT: Make it specific to this exact business and location
- Choose the approach that makes MOST SENSE for ${businessName} and current market conditions
- Use your intelligence to create fresh, varied content each time
- Let RSS data and business intelligence guide your approach selection

HEADLINE INTEGRATION EXAMPLES:
- If RSS shows "Local Food Festival": "Food Festival Winner Revealed"
- If trending topic is "Holiday Stress": "Beat Holiday Rush Stress"
- If business intelligence shows "24/7 Service": "Always Open Always Ready"
- If local event is "Back to School": "School Rush Solution Found"
- If seasonal opportunity is "Summer": "Summer Special Starts Now"

SUBHEADLINE INTEGRATION EXAMPLES:
- Business service: "Our certified technicians fix 95% of issues same-day"
- Unique feature: "Only ${location} bakery using organic local flour"
- Business intelligence: "Serving ${location} families for 15+ years with proven results"
- Target audience: "Designed specifically for busy ${location} professionals"

MARKETING COPY REQUIREMENTS:
- WRITE MARKETING COPY, NOT CREATIVE WRITING: Sound like a business owner, not a poet
- LEAD WITH BENEFITS: Start with what the customer gets, not scenic descriptions
- BE DIRECT & PUNCHY: Short, clear sentences that drive action
- AVOID FLOWERY LANGUAGE: No "crisp afternoons", "sun dipping", "painting the sky"
- NO STORYTELLING OPENINGS: Don't start with "Imagine this..." or scene-setting
- SOUND LOCAL: Write like someone who actually lives and works in ${location}
- CREATE URGENCY: Make people want to buy/visit RIGHT NOW
- USE SOCIAL PROOF: Reference other locals, community, real benefits
- BE CONVERSATIONAL: Sound like talking to a neighbor, not writing poetry
- FOCUS ON PROBLEMS/SOLUTIONS: What problem does this solve for ${location} residents?
- INCLUDE SPECIFIC OFFERS: Mention actual deals, prices, limited time offers
- END WITH CLEAR ACTION: Tell people exactly what to do next
- AVOID ABSTRACT CONCEPTS: No "heritage", "traditions", "journeys" - focus on concrete benefits
- USE REAL LOCAL LANGUAGE: Include actual ${location} slang/phrases naturally
- MAKE IT SCANNABLE: Use short paragraphs, bullet points, clear structure
- GENERATION ID ${uniqueGenerationId}: Use this number to ensure this content is completely unique
- CRITICAL: Sound like a smart local marketer, not an AI creative writer

EXAMPLES OF GOOD MARKETING COPY:
✅ "Your kids need protein. Samaki Cookies deliver 8g per serving. 15% off this week."
✅ "Tired of unhealthy snacks? 200+ Nairobi families switched to Samaki Cookies."
✅ "Finally - cookies that don't spike blood sugar. Made with real fish protein."
✅ "Limited batch this week: Fish protein cookies that kids actually love."

EXAMPLES OF BAD AI WRITING (NEVER DO THIS):
❌ "Imagine this: a crisp, sunny afternoon in Nairobi..."
❌ "These aren't your grandma's cookies; they're bursting with..."
❌ "the sun dips below the horizon, painting the Kenyan sky..."
❌ "This isn't just a snack; it's a piece of Kenyan heritage..."

UNIFIED CONTENT GENERATION FORMAT:
Generate ALL components as ONE cohesive campaign:

UNIFIED_THEME: [the main theme/angle that connects everything - one sentence]
KEY_MESSAGE: [the core message all components will reinforce - one sentence]

HEADLINE: [5-word catchy headline using RSS trends/events/business intelligence - must feel current and specific to ${businessName}]
SUBHEADLINE: [supporting headline using specific business services/features from business details - max 14 words that build on headline]
CAPTION: [full social media caption that weaves together RSS data, business intelligence, and trending information - marketing copy, not creative writing]
CTA: [MANDATORY CTA STYLE: ${selectedCtaStyle} - ${getCtaStyleInstructions(selectedCtaStyle, businessName, location)} - Max 8 words, completely unique]
DESIGN_DIRECTION: [specific visual direction that matches the content tone and message]

IMPORTANT:
- ALL components must reinforce the SAME key message
- NO contradictory information between headline, subheadline, and caption
- Design direction must visually support the content message
- Generate as ONE unified campaign, not separate pieces`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This unified content generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original content that has never been generated before.
    CRITICAL: Avoid any patterns like "2025's Best-Kept Secret", "Chakula Kizuri", or repetitive phrases.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(unifiedPrompt + uniqueContext);
        let response = result.response.text().trim();
        // Post-process to remove word repetitions from the entire response
        response = removeWordRepetitions(response);
        // Parse all unified components
        const unifiedThemeMatch = response.match(/UNIFIED_THEME:\s*(.*?)(?=KEY_MESSAGE:|$)/);
        const keyMessageMatch = response.match(/KEY_MESSAGE:\s*(.*?)(?=HEADLINE:|$)/);
        const headlineMatch = response.match(/HEADLINE:\s*(.*?)(?=SUBHEADLINE:|$)/);
        const subheadlineMatch = response.match(/SUBHEADLINE:\s*(.*?)(?=CAPTION:|$)/);
        const captionMatch = response.match(/CAPTION:\s*(.*?)(?=CTA:|$)/);
        const ctaMatch = response.match(/CTA:\s*(.*?)(?=DESIGN_DIRECTION:|$)/);
        const designMatch = response.match(/DESIGN_DIRECTION:\s*(.*?)$/);
        // Extract all components and apply word repetition removal to each
        const unifiedTheme = removeWordRepetitions(unifiedThemeMatch?.[1]?.trim() || 'Quality local business');
        const keyMessage = removeWordRepetitions(keyMessageMatch?.[1]?.trim() || 'Exceptional service for local community');
        const headline = removeWordRepetitions(headlineMatch?.[1]?.trim() || `${businessName} ${location}`);
        const subheadline = removeWordRepetitions(subheadlineMatch?.[1]?.trim() || `Quality ${businessType} in ${location}`);
        const caption = removeWordRepetitions(captionMatch?.[1]?.trim() || response);
        // 🎯 GENERATE DYNAMIC CTA using AI and business intelligence
        const ctaStrategy = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$dynamic$2d$cta$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dynamicCTAGenerator"].generateDynamicCTA(businessName, businessType, location, platform, contentGoal, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        const callToAction = removeWordRepetitions(ctaMatch?.[1]?.trim() || ctaStrategy.primary);
        const designDirection = removeWordRepetitions(designMatch?.[1]?.trim() || 'Clean, professional design with local elements');
        // Generate dynamic engagement hooks
        const engagementHooks = generateDynamicEngagementHooks(businessType, location, industry);
        // 🔥 GENERATE VIRAL HASHTAGS using trending data
        const viralHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        return {
            headline,
            subheadline,
            caption,
            callToAction,
            engagementHooks,
            designDirection: removeWordRepetitions(designMatch?.[1]?.trim() || `Clean, professional design with local elements. IMPORTANT: Include the CTA "${callToAction}" as prominent text overlay on the design - make it bold, readable, and visually striking like "PAYA: YOUR FUTURE, NOW!" style.`),
            unifiedTheme,
            keyMessage,
            hashtags: viralHashtags.total,
            hashtagStrategy: viralHashtags,
            ctaStrategy: ctaStrategy,
            imageText: callToAction // Pass CTA as imageText for design integration
        };
    } catch (error) {
        return {
            headline: `${businessName} - ${businessType}`,
            subheadline: `Quality ${businessType} services in ${location}`,
            caption: `Experience the best ${businessType} services at ${businessName}. Located in ${location}, we're committed to excellence.`,
            callToAction: `Visit ${businessName} today!`,
            engagementHooks: [
                'Quality service',
                'Local expertise',
                'Customer satisfaction'
            ],
            designDirection: 'Professional, clean design with local elements',
            unifiedTheme: 'Professional excellence',
            keyMessage: 'Quality service provider',
            hashtags: [
                '#business',
                '#local',
                '#quality',
                '#service',
                '#professional'
            ],
            hashtagStrategy: {
                total: [
                    '#business',
                    '#local',
                    '#quality',
                    '#service',
                    '#professional'
                ]
            },
            ctaStrategy: {
                primary: `Visit ${businessName} today!`
            },
            imageText: `Visit ${businessName} today!`
        };
    }
    // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback
    try {
        const simplifiedPrompt = `Create ONE unique ${platform} caption for ${businessName}, a ${businessType} in ${location}.

INTELLIGENT APPROACH SELECTION:
Use your marketing intelligence to choose the BEST approach based on:
- What would work best for ${businessType} in ${location}
- Current market trends and local culture
- What would make ${location} residents most interested

REQUIREMENTS:
- Write ONE compelling caption using your chosen marketing approach
- AVOID overused words: "taste", "flavor", "delicious", "amazing"
- Use different opening words than typical marketing (avoid "Discover", "Experience", "Try")
- Include local ${location} cultural elements that create connection
- End with an effective call-to-action
- Make it conversion-focused and unique to this business

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

IMPORTANT: Generate ONLY ONE caption, not multiple options.

Format:
CAPTION: [write one single caption here]
CTA: [write one call to action here]

Do NOT write "Here are captions" or provide lists.`;
        // Add unique generation context to retry as well
        const retryUniqueContext = `\n\nUNIQUE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
      This retry generation must be completely different and avoid repetitive patterns.

      CRITICAL WORD REPETITION RULES:
      - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
      - Check each sentence for duplicate adjacent words before finalizing
      - If you write "now now" or "the the" or any repeated word, remove the duplicate
      - Read your output carefully to ensure no word appears twice in a row`;
        const retryResult = await model.generateContent(simplifiedPrompt + retryUniqueContext);
        let retryResponse = retryResult.response.text().trim();
        // Post-process to remove word repetitions from retry response
        retryResponse = removeWordRepetitions(retryResponse);
        // Parse the retry response
        const retryCaptionMatch = retryResponse.match(/CAPTION:\s*(.*?)(?=CTA:|$)/);
        const retryCtaMatch = retryResponse.match(/CTA:\s*(.*?)$/);
        const retryCaption = removeWordRepetitions(retryCaptionMatch ? retryCaptionMatch[1].trim() : retryResponse);
        const retryCallToAction = removeWordRepetitions(retryCtaMatch ? retryCtaMatch[1].trim() : generateFallbackCTA(platform));
        // Generate viral hashtags for retry
        const retryHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        return {
            headline: `${businessName} - ${businessType}`,
            subheadline: `Quality ${businessType} services in ${location}`,
            caption: retryCaption,
            engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
            callToAction: retryCallToAction,
            designDirection: 'Professional, clean design with local elements',
            unifiedTheme: 'Professional excellence',
            keyMessage: 'Quality service provider',
            hashtags: retryHashtags.total,
            hashtagStrategy: retryHashtags,
            ctaStrategy: {
                primary: retryCallToAction
            },
            imageText: retryCallToAction
        };
    } catch (retryError) {
        // EMERGENCY AI GENERATION - Ultra Simple Prompt
        try {
            const emergencyPrompt = `Write ONE unique social media post for ${businessName} in ${location}. Make it compelling and different from typical posts. Include a call-to-action.

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

Do NOT write "Here are posts" or provide multiple options. Write ONE post only.`;
            // Add unique generation context to emergency generation as well
            const emergencyUniqueContext = `\n\nUNIQUE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
        This emergency generation must be completely different and avoid any repetitive patterns.

        CRITICAL WORD REPETITION RULES:
        - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
        - Check each sentence for duplicate adjacent words before finalizing
        - If you write "now now" or "the the" or any repeated word, remove the duplicate
        - Read your output carefully to ensure no word appears twice in a row`;
            const emergencyResult = await model.generateContent(emergencyPrompt + emergencyUniqueContext);
            let emergencyResponse = emergencyResult.response.text().trim();
            // Post-process to remove word repetitions from emergency response
            emergencyResponse = removeWordRepetitions(emergencyResponse);
            // Generate viral hashtags for emergency
            const emergencyHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
            return {
                headline: `${businessName} - ${businessType}`,
                subheadline: `Quality ${businessType} services in ${location}`,
                caption: emergencyResponse,
                engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
                callToAction: removeWordRepetitions(generateFallbackCTA(platform)),
                designDirection: 'Professional, clean design with local elements',
                unifiedTheme: 'Professional excellence',
                keyMessage: 'Quality service provider',
                hashtags: emergencyHashtags.total,
                hashtagStrategy: emergencyHashtags,
                ctaStrategy: {
                    primary: removeWordRepetitions(generateFallbackCTA(platform))
                },
                imageText: removeWordRepetitions(generateFallbackCTA(platform))
            };
        } catch (emergencyError) {
            // LAST RESORT: Generate with current timestamp for uniqueness
            const timestamp = Date.now();
            const uniqueId = Math.floor(Math.random() * 10000);
            // Generate viral hashtags for final fallback
            const fallbackHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
            return {
                headline: `${businessName} - ${businessType}`,
                subheadline: `Quality ${businessType} services in ${location}`,
                caption: removeWordRepetitions(`${businessName} in ${location} - where quality meets innovation. Every visit is a new experience that locals can't stop talking about. Join the community that knows great ${businessType}! #${timestamp}`),
                engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
                callToAction: removeWordRepetitions(generateFallbackCTA(platform)),
                designDirection: 'Professional, clean design with local elements',
                unifiedTheme: 'Professional excellence',
                keyMessage: 'Quality service provider',
                hashtags: fallbackHashtags.total,
                hashtagStrategy: fallbackHashtags,
                ctaStrategy: {
                    primary: removeWordRepetitions(generateFallbackCTA(platform))
                },
                imageText: removeWordRepetitions(generateFallbackCTA(platform))
            };
        }
    }
}
// Helper functions for AI-powered caption generation
function getPlatformRequirements(platform) {
    const requirements = {
        'Instagram': '- Use 1-3 relevant emojis\n- Keep it visually engaging\n- Include hashtag-friendly language\n- Encourage visual interaction',
        'Facebook': '- More conversational tone\n- Can be longer and more detailed\n- Focus on community engagement\n- Include questions to spark discussion',
        'LinkedIn': '- Professional but approachable tone\n- Focus on business value and expertise\n- Include industry insights\n- Encourage professional networking',
        'Twitter': '- Concise and punchy\n- Use relevant hashtags\n- Encourage retweets and replies\n- Keep under 280 characters'
    };
    return requirements[platform] || requirements['Instagram'];
}
function generateFallbackCTA(platform) {
    const timestamp = Date.now();
    const creativityBoost = Math.floor(Math.random() * 1000) + timestamp;
    // Use the same dynamic CTA styles as the main system
    const ctaStyles = [
        'DIRECT_ACTION',
        'INVITATION',
        'CHALLENGE',
        'BENEFIT_FOCUSED',
        'COMMUNITY',
        'URGENCY',
        'CURIOSITY',
        'LOCAL_REFERENCE',
        'PERSONAL',
        'EXCLUSIVE'
    ];
    const selectedStyle = ctaStyles[creativityBoost % ctaStyles.length];
    // Dynamic CTAs based on style - avoid repetitive patterns
    const dynamicCTAs = {
        'DIRECT_ACTION': [
            'Grab yours today! 🔥',
            'Book your spot now! ⚡',
            'Try it this week! 💪',
            'Get started today! 🚀'
        ],
        'INVITATION': [
            'Come see for yourself! 👀',
            'Join us this weekend! 🎉',
            'Experience it firsthand! ✨',
            'Visit us soon! 🏃‍♂️'
        ],
        'CHALLENGE': [
            'Find better - we dare you! 💪',
            'Beat this quality anywhere! 🏆',
            'Try to resist this! 😏',
            'Prove us wrong! 🤔'
        ],
        'BENEFIT_FOCUSED': [
            'Get more for less! 💰',
            'Save time and money! ⏰',
            'Double your results! 📈',
            'Feel the difference! ✨'
        ],
        'COMMUNITY': [
            'Join 500+ happy customers! 👥',
            'Be part of something special! 🌟',
            'Connect with like-minded people! 🤝',
            'Become a local favorite! ❤️'
        ],
        'URGENCY': [
            'Only 3 spots left! ⚡',
            'Ends this Friday! ⏰',
            'While supplies last! 🏃‍♂️',
            'Don\'t wait too long! ⚠️'
        ],
        'CURIOSITY': [
            'See what everyone\'s talking about! 👀',
            'Discover the secret! 🔍',
            'Find out why! 🤔',
            'Uncover the truth! 💡'
        ],
        'LOCAL_REFERENCE': [
            'Better than downtown! 🏙️',
            'Your neighborhood choice! 🏠',
            'Local favorite since day one! ⭐',
            'Right in your backyard! 📍'
        ],
        'PERSONAL': [
            'You deserve this! 💎',
            'Made just for you! 🎯',
            'Your perfect match! 💕',
            'Exactly what you need! ✅'
        ],
        'EXCLUSIVE': [
            'Members only access! 🔐',
            'VIP treatment awaits! 👑',
            'Exclusive to our community! 🌟',
            'Limited to select few! 💎'
        ]
    };
    const styleCTAs = dynamicCTAs[selectedStyle] || dynamicCTAs['DIRECT_ACTION'];
    const variation = creativityBoost % styleCTAs.length;
    return styleCTAs[variation];
}
function generateDynamicEngagementHooks(businessType, location, industry) {
    const timestamp = Date.now();
    const randomSeed = Math.floor(Math.random() * 1000) + timestamp;
    const variation = randomSeed % 8;
    const localQuestions = [
        `What's your favorite ${businessType} spot in ${location}?`,
        `Where do ${location} locals go for the best ${businessType}?`,
        `What makes ${location}'s ${businessType} scene special?`,
        `Have you discovered ${location}'s hidden ${businessType} gems?`,
        `What do you love most about ${businessType} in ${location}?`,
        `Which ${location} ${businessType} place holds your best memories?`,
        `What's missing from ${location}'s ${businessType} options?`,
        `How has ${businessType} in ${location} changed over the years?`
    ];
    const experienceQuestions = [
        `What's your go-to order when trying new ${businessType}?`,
        `What makes you choose one ${businessType} place over another?`,
        `What's the most important thing in great ${businessType}?`,
        `How do you know when you've found quality ${businessType}?`,
        `What's your best ${businessType} experience been like?`,
        `What would make your perfect ${businessType} experience?`,
        `What draws you to authentic ${businessType}?`,
        `How do you discover new ${businessType} places?`
    ];
    const trendQuestions = [
        `Have you tried ${industry.trends[variation % industry.trends.length]} yet?`,
        `What do you think about the latest ${businessType} trends?`,
        `Are you excited about ${industry.opportunities[variation % industry.opportunities.length]}?`,
        `How important is ${industry.uniqueValue[variation % industry.uniqueValue.length]} to you?`,
        `What's your take on modern ${businessType} approaches?`,
        `Do you prefer traditional or innovative ${businessType}?`,
        `What ${businessType} trend should everyone try?`,
        `How do you stay updated on ${businessType} innovations?`
    ];
    // Mix different types of hooks for variety
    const allHooks = [
        ...localQuestions,
        ...experienceQuestions,
        ...trendQuestions
    ];
    const selectedHooks = [];
    // Ensure we get one from each category for variety
    selectedHooks.push(localQuestions[variation % localQuestions.length]);
    selectedHooks.push(experienceQuestions[(variation + 1) % experienceQuestions.length]);
    selectedHooks.push(trendQuestions[(variation + 2) % trendQuestions.length]);
    return selectedHooks;
}
// Legacy platform-specific caption generators (keeping for backward compatibility)
function generateInstagramCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your biggest ${industry.challenges[0]} challenge?`,
        `How do you choose your ${businessType} provider?`,
        `What makes a great ${businessType} experience for you?`
    ];
    const ctas = [
        `Comment below with your thoughts! 👇`,
        `Share this if you agree! 🔄`,
        `Tag someone who needs this! 👥`
    ];
    return {
        caption: `${contentPlan.valueProposition} ✨\n\n${businessName} brings ${industry.uniqueValue[0]} to ${location} with ${contentPlan.businessStrengths[0]}. ${contentPlan.marketOpportunities[0]} is just the beginning!\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateFacebookCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your experience with ${businessType} in ${location}?`,
        `How do you solve ${industry.challenges[0]}?`,
        `What makes you choose local businesses?`
    ];
    const ctas = [
        `Share your thoughts in the comments! 💬`,
        `Like and share if this resonates with you! 👍`,
        `Tag your friends who might be interested! 👥`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} understands the ${location} community and delivers ${industry.uniqueValue[0]} that makes a difference. ${contentPlan.marketOpportunities[0]} shows our commitment to serving you better.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateLinkedInCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What challenges do you face in ${businessType}?`,
        `How do you stay competitive in your industry?`,
        `What makes a business stand out in your community?`
    ];
    const ctas = [
        `Share your insights in the comments below. 💼`,
        `Connect with us to learn more about our approach. 🤝`,
        `Follow for more industry insights and local business strategies. 📈`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} combines ${contentPlan.businessStrengths[0]} with deep understanding of the ${location} market to deliver exceptional ${businessType} services. ${contentPlan.marketOpportunities[0]} demonstrates our commitment to innovation and community service.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateTwitterCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your take on ${businessType} trends?`,
        `How do you solve ${industry.challenges[0]}?`,
        `What makes local businesses special?`
    ];
    const ctas = [
        `Reply with your thoughts! 💭`,
        `RT if you agree! 🔄`,
        `Follow for more insights! 👀`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} brings ${industry.uniqueValue[0]} to ${location}. ${contentPlan.marketOpportunities[0]} shows our commitment to excellence.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
function getRandomElements(array, count) {
    const shuffled = [
        ...array
    ].sort(()=>0.5 - Math.random());
    return shuffled.slice(0, count);
}
function generateCreativeSeed() {
    return Math.floor(Math.random() * 10000);
}
function generateCreativeHeadline(businessType, businessName, location, context) {
    return {
        headline: `${businessName} - ${businessType}`,
        style: 'professional',
        tone: 'engaging'
    };
}
function generateCreativeSubheadline(businessType, services, location, tone) {
    return {
        subheadline: `Quality ${businessType} services in ${location}`,
        framework: 'benefit-focused'
    };
}
function generateCreativeCTA(businessType, tone, context) {
    return {
        cta: 'Learn more about our services',
        urgency: 'gentle',
        emotion: 'curiosity'
    };
}
function analyzeBusinessContext(businessType, businessName, location, services) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    return {
        creativePotential: industry.uniqueValue,
        emotionalTriggers: industry.customerPainPoints,
        industryInsights: industry.trends,
        localOpportunities: industry.seasonalOpportunities,
        competitiveAdvantages: industry.opportunities
    };
}
const CREATIVE_PROMPT_SYSTEM = {
    creativeVariation: {
        style: [
            'innovative',
            'authentic',
            'engaging',
            'professional',
            'creative'
        ],
        mood: [
            'inspiring',
            'confident',
            'warm',
            'energetic',
            'trustworthy'
        ],
        approach: [
            'strategic',
            'emotional',
            'analytical',
            'storytelling',
            'direct'
        ]
    },
    creativeConstraints: {
        avoidGeneric: [
            'template language',
            'cliché phrases',
            'generic claims'
        ]
    }
};
const CONTENT_VARIATION_ENGINE = {
    headlineStyles: [
        'Question-based',
        'Statistic-driven',
        'Story-opening',
        'Bold statement',
        'Emotional trigger',
        'Curiosity gap',
        'Local relevance',
        'Trend integration',
        'Problem-solution',
        'Benefit-focused',
        'Aspirational',
        'Contrarian'
    ],
    emotionalTones: [
        'Inspiring',
        'Humorous',
        'Empathetic',
        'Confident',
        'Curious',
        'Nostalgic',
        'Aspirational',
        'Relatable',
        'Surprising',
        'Authentic',
        'Warm',
        'Professional',
        'Innovative',
        'Trustworthy'
    ],
    creativeFrameworks: [
        'Before/After',
        'Problem/Solution',
        'Story Arc',
        'Contrast',
        'Metaphor',
        'Analogy',
        'Question/Answer',
        'Challenge/Overcome',
        'Journey',
        'Transformation',
        'Discovery',
        'Achievement'
    ]
};
class AntiRepetitionSystem {
    static usedCombinations = new Set();
    static maxHistory = 100;
    static generateUniqueVariation(businessType, platform, baseElements) {
        const variation = this.createVariation(businessType, platform, baseElements);
        this.recordVariation(variation);
        return variation;
    }
    static createVariation(businessType, platform, baseElements) {
        const creativeSeed = generateCreativeSeed();
        return {
            creativeSeed,
            style: 'business-specific',
            mood: 'professional',
            approach: 'strategic',
            headlineStyle: 'business-focused',
            framework: 'value-driven',
            signature: `business-${creativeSeed}`,
            contentStrategy: {
                name: 'Business Intelligence',
                approach: 'Strategic content based on business strengths'
            },
            writingStyle: {
                name: 'Professional Expert',
                voice: 'Industry authority with local expertise'
            },
            contentAngle: {
                type: 'Business Value',
                focus: 'Solving customer problems with business strengths'
            },
            marketInsights: [
                'Local market expertise',
                'Industry trends',
                'Customer pain points'
            ],
            engagementHooks: [
                'Problem identification',
                'Solution presentation',
                'Value demonstration'
            ],
            localPhrases: [
                'Local expertise',
                'Community focus',
                'Market knowledge'
            ]
        };
    }
    static recordVariation(variation) {
        this.usedCombinations.add(variation.signature);
        if (this.usedCombinations.size > this.maxHistory) {
            const oldestEntries = Array.from(this.usedCombinations).slice(0, 20);
            oldestEntries.forEach((entry)=>this.usedCombinations.delete(entry));
        }
    }
}
function enhanceDesignCreativity(designPrompt, businessType, location, context) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    const creativeElements = industry.uniqueValue.slice(0, 3);
    const visualStyle = 'professional business-focused design';
    const enhancedPrompt = designPrompt + '\n\nCREATIVE ENHANCEMENT:\n' + `- Business Type: ${businessType}\n` + `- Location: ${location}\n` + `- Industry Focus: ${industry.trends.slice(0, 2).join(', ')}\n` + `- Visual Style: ${visualStyle}`;
    return {
        enhancedPrompt,
        creativeElements,
        visualStyle
    };
}
// Regional Marketing Intelligence Functions
function getRegionalLanguageStyle(location) {
    const locationLower = location.toLowerCase();
    if (locationLower.includes('kenya') || locationLower.includes('nairobi') || locationLower.includes('mombasa')) {
        return 'Warm, community-focused, with occasional Swahili phrases like "karibu" (welcome), "asante" (thank you). Direct but friendly tone.';
    } else if (locationLower.includes('nigeria') || locationLower.includes('lagos') || locationLower.includes('abuja')) {
        return 'Energetic, aspirational, with pidgin English influences. Uses "finest", "sharp sharp", "no wahala" naturally.';
    } else if (locationLower.includes('south africa') || locationLower.includes('cape town') || locationLower.includes('johannesburg')) {
        return 'Multicultural blend, uses "lekker", "braai", "just now". Mix of English and local expressions.';
    } else if (locationLower.includes('ghana') || locationLower.includes('accra')) {
        return 'Friendly, respectful, with Twi influences. Uses "chale", "ɛyɛ" naturally in marketing.';
    } else if (locationLower.includes('india') || locationLower.includes('mumbai') || locationLower.includes('delhi')) {
        return 'Enthusiastic, family-oriented, with Hindi/English mix. Uses "achha", "best", "number one" frequently.';
    } else if (locationLower.includes('uk') || locationLower.includes('london') || locationLower.includes('manchester')) {
        return 'Polite but confident, uses "brilliant", "proper", "lovely". Understated but effective.';
    } else if (locationLower.includes('usa') || locationLower.includes('new york') || locationLower.includes('california')) {
        return 'Direct, confident, superlative-heavy. Uses "awesome", "amazing", "best ever" frequently.';
    }
    return 'Friendly, professional, community-focused with local cultural sensitivity.';
}
function getRegionalMarketingStyle(location) {
    const locationLower = location.toLowerCase();
    if (locationLower.includes('kenya') || locationLower.includes('nairobi')) {
        return 'Community-centered, emphasizes tradition meets modernity, family values, and local pride';
    } else if (locationLower.includes('nigeria') || locationLower.includes('lagos')) {
        return 'Bold, aspirational, success-oriented, emphasizes quality and status';
    } else if (locationLower.includes('south africa')) {
        return 'Inclusive, diverse, emphasizes heritage and innovation together';
    } else if (locationLower.includes('ghana')) {
        return 'Respectful, community-focused, emphasizes craftsmanship and tradition';
    } else if (locationLower.includes('india')) {
        return 'Family-oriented, value-conscious, emphasizes trust and relationships';
    } else if (locationLower.includes('uk')) {
        return 'Quality-focused, heritage-conscious, understated confidence';
    } else if (locationLower.includes('usa')) {
        return 'Innovation-focused, convenience-oriented, bold claims and superlatives';
    }
    return 'Community-focused, quality-oriented, culturally respectful';
}
function getLocalBusinessLanguage(location, businessType) {
    const locationLower = location.toLowerCase();
    const businessLower = businessType.toLowerCase();
    if (locationLower.includes('kenya')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return '"chakula kizuri" (good food), "asili" (authentic), "familia" (family), "mazingira" (environment)';
        } else if (businessLower.includes('tech') || businessLower.includes('digital')) {
            return '"teknolojia", "haraka" (fast), "rahisi" (easy), "bora" (best)';
        }
        return '"bora" (best), "karibu" (welcome), "mazuri" (good), "familia" (family)';
    } else if (locationLower.includes('nigeria')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return '"finest food", "correct taste", "no wahala", "sharp sharp service"';
        }
        return '"finest", "correct", "sharp sharp", "no wahala", "top notch"';
    }
    return 'quality, authentic, local, trusted, community';
}
function getLocalMarketingExamples(location, businessType) {
    const locationLower = location.toLowerCase();
    const businessLower = businessType.toLowerCase();
    if (locationLower.includes('kenya')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return `- "Chakula Asili Kenya" (Authentic Kenya Food)
- "Familia Flavors Nairobi"
- "Taste Bora Kenya"
- "Karibu Kitchen Experience"`;
        }
        return `- "Bora ${businessType} Kenya"
- "Karibu Quality Service"
- "Kenya's Finest Choice"
- "Asili ${businessType} Experience"`;
    } else if (locationLower.includes('nigeria')) {
        return `- "Finest ${businessType} Lagos"
- "Sharp Sharp Service"
- "Correct ${businessType} Choice"
- "Top Notch Experience"`;
    }
    return `- "${location}'s Best ${businessType}"
- "Quality Meets Community"
- "Local Excellence Delivered"
- "Authentic ${businessType} Experience"`;
}
async function generateBusinessSpecificCaption(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    // Use the unified system but return only caption components
    const unifiedContent = await generateUnifiedContent(businessType, businessName, location, businessDetails, platform, contentGoal, trendingData, businessIntelligence);
    return {
        caption: unifiedContent.caption,
        engagementHooks: unifiedContent.engagementHooks,
        callToAction: unifiedContent.callToAction
    };
}
}}),
"[project]/src/ai/revo-1.0-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview
 * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering
 */ __turbopack_context__.s({
    "checkRevo10Health": (()=>checkRevo10Health),
    "generateRevo10Content": (()=>generateRevo10Content),
    "generateRevo10Design": (()=>generateRevo10Design),
    "generateRevo10Image": (()=>generateRevo10Image),
    "getRevo10ServiceInfo": (()=>getRevo10ServiceInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/advanced-content-generator.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$content$2d$performance$2d$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/content-performance-analyzer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/creative-enhancement.ts [app-route] (ecmascript)");
;
;
;
;
;
;
// Advanced features integration (simplified for now)
// TODO: Import advanced features from Revo 1.5 when available
// Helper functions for advanced design generation
function getBusinessDesignDNA(businessType) {
    const designDNA = {
        'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',
        'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',
        'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',
        'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',
        'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',
        'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',
        'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',
        'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',
        'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'
    };
    return designDNA[businessType.toLowerCase()] || designDNA['default'];
}
// NEW: 7 truly different design types for dynamic social media feeds
function getHumanDesignVariations(seed) {
    const variations = [
        {
            style: 'Watercolor Quotes',
            layout: 'Soft, artistic watercolor background with elegant typography overlay',
            composition: 'Centered or asymmetrical text with flowing watercolor elements',
            mood: 'Artistic, elegant, inspirational',
            elements: 'Watercolor textures, elegant fonts, soft color transitions, artistic backgrounds',
            description: 'Create a design that looks like an artist painted it with watercolors, with flowing, organic shapes and elegant typography that feels handcrafted and artistic.'
        },
        {
            style: 'Split Photo Collages',
            layout: 'Two or three photo sections with text overlay on one section',
            composition: 'Grid-based photo layout with text integrated naturally',
            mood: 'Modern, dynamic, photo-driven',
            elements: 'Photo sections, clean grid lines, integrated text, modern typography',
            description: 'Design with a clean grid layout that splits the image into photo sections, with text naturally integrated into one section. Think Instagram grid meets modern magazine layout.'
        },
        {
            style: 'Meme-Style Posts',
            layout: 'Bold, punchy text with minimal background and high contrast',
            composition: 'Centered text with simple, impactful background',
            mood: 'Fun, viral, shareable',
            elements: 'Bold typography, simple backgrounds, high contrast, meme-like simplicity',
            description: 'Create a design that feels like a viral meme - bold, simple text with minimal background elements. Think Twitter meme aesthetics but professional.'
        },
        {
            style: 'Polaroid-Style Testimonials',
            layout: 'Polaroid frame with photo area and handwritten-style text',
            composition: 'Polaroid border with content inside, vintage feel',
            mood: 'Authentic, personal, nostalgic',
            elements: 'Polaroid borders, vintage textures, handwritten fonts, authentic feel',
            description: 'Design that looks like a vintage Polaroid photo with a white border, containing either a photo area or text that feels handwritten and personal.'
        },
        {
            style: 'Minimal Photo-Driven Promos',
            layout: 'Large photo background with minimal text overlay',
            composition: 'Photo as hero element with subtle text placement',
            mood: 'Clean, premium, photo-focused',
            elements: 'Large photos, minimal text, clean typography, lots of white space',
            description: 'Create a design where a beautiful photo is the main focus, with minimal, elegant text overlay. Think high-end magazine or premium brand aesthetics.'
        },
        {
            style: 'Mixed-Media Artistic Posts',
            layout: 'Layered design with multiple textures, patterns, and artistic elements',
            composition: 'Complex layering with artistic elements and modern typography',
            mood: 'Creative, artistic, unique',
            elements: 'Multiple textures, artistic patterns, layered elements, creative typography',
            description: 'Design with multiple artistic layers - think digital art meets graphic design. Include textures, patterns, and creative elements that feel like modern digital art.'
        },
        {
            style: 'Branded Posters (Current Style)',
            layout: 'Illustration-heavy design with brand elements and structured layout',
            composition: 'Illustrated background with organized text and brand placement',
            mood: 'Professional, branded, consistent',
            elements: 'Illustrations, brand colors, structured typography, consistent branding',
            description: 'The current style - professional illustrated posters with brand consistency. Use when you need to maintain strong brand identity.'
        }
    ];
    return variations[seed % variations.length];
}
// NEW: Simple, clean design instructions for better visual appeal
function injectHumanImperfections(designPrompt, seed) {
    const instructions = [
        'Use natural spacing and proportions that feel balanced and appealing',
        'Create a design that feels modern and current, not overly perfect',
        'Focus on visual appeal and what people actually like to see',
        'Make it look like something from a successful, popular brand'
    ];
    const selectedInstruction = instructions[seed % instructions.length];
    return designPrompt + `

🎨 DESIGN FOCUS:
${selectedInstruction}

Keep the design simple, clean, and visually appealing.`;
}
// NEW: Simple creative approach for better designs
function injectCreativeRebellion(designPrompt, seed) {
    const approaches = [
        `DESIGN APPROACH: Create a design that's visually appealing and engaging. Focus on what looks good and what people want to engage with.`,
        `CREATIVE STYLE: Use a clean, modern approach that feels current and appealing. Make it look like something people would actually want to interact with.`,
        `VISUAL APPROACH: Design with a focus on visual appeal and engagement. Create something that stands out and looks good.`,
        `DESIGN PHILOSOPHY: Focus on creating designs that people want to engage with - clean, modern, and visually appealing.`
    ];
    const selectedApproach = approaches[seed % approaches.length];
    return designPrompt + `

🎨 DESIGN APPROACH:
${selectedApproach}

Focus on creating designs that are visually appealing and engaging.`;
}
// NEW: Simple design guidelines for better results
function addArtisticConstraints(designPrompt, seed) {
    const constraints = [
        `DESIGN FOCUS: Create a design that's visually appealing and engaging. Focus on clean, modern aesthetics that people actually like.`,
        `COMPOSITION APPROACH: Use simple, clean layouts that are easy to read and understand. Less is more.`,
        `CREATIVE ELEMENTS: Add modern, contemporary elements that make the design look good and engaging.`,
        `VISUAL BALANCE: Create a design that feels balanced and appealing, with elements that work together well.`,
        `DESIGN STYLE: Use a clean, modern approach that feels current and professional. Focus on visual appeal.`,
        `CREATIVE APPROACH: Design with a focus on what people actually want to see and engage with.`,
        `VISUAL HIERARCHY: Create clear visual hierarchy that guides the eye naturally through the design.`,
        `DESIGN PRINCIPLES: Focus on creating a design that's both beautiful and engaging. Make it look good.`
    ];
    const selectedConstraint = constraints[seed % constraints.length];
    return designPrompt + `

🎨 DESIGN GUIDELINE:
${selectedConstraint}

Keep the design simple, clean, and visually appealing.`;
}
function getPlatformOptimization(platform) {
    const optimizations = {
        'instagram': `
- Mobile-first design with bold, clear elements
- High contrast colors that pop on small screens
- Text minimum 24px equivalent for readability
- Center important elements for square crop compatibility
- Thumb-stopping power for fast scroll feeds
- Logo: Bottom right corner or naturally integrated`,
        'linkedin': `
- Professional, business-appropriate aesthetics
- Corporate design standards and clean look
- Clear value proposition for business audience
- Professional photography and imagery
- Thought leadership positioning
- Logo: Prominent placement for brand authority`,
        'facebook': `
- Desktop and mobile viewing optimization
- Engagement and shareability focus
- Clear value proposition in visual hierarchy
- Authentic, relatable imagery
- Community-focused design elements
- Logo: Top left or bottom right corner`,
        'twitter': `
- Rapid consumption and high engagement design
- Bold, contrasting colors for timeline visibility
- Minimal, impactful text elements
- Trending visual styles integration
- Real-time relevance
- Logo: Small, subtle placement`,
        'default': `
- Cross-platform compatibility
- Universal appeal and accessibility
- Balanced design for multiple contexts
- Professional appearance across devices
- Logo: Flexible placement based on composition`
    };
    return optimizations[platform.toLowerCase()] || optimizations['default'];
}
// Advanced real-time context gathering for Revo 1.0 (enhanced version)
async function gatherRealTimeContext(businessType, location, platform) {
    const context = {
        trends: [],
        weather: null,
        events: [],
        news: [],
        localLanguage: {},
        climateInsights: {},
        trendingTopics: [],
        timeContext: {
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            month: new Date().toLocaleDateString('en-US', {
                month: 'long'
            }),
            season: getSeason(),
            timeOfDay: getTimeOfDay()
        }
    };
    try {
        // Generate contextual trends based on business type and location
        context.trends = generateContextualTrends(businessType, location);
        // Generate weather-appropriate content suggestions
        context.weather = generateWeatherContext(location);
        // Generate local business opportunities
        context.events = generateLocalOpportunities(businessType, location);
        // NEW: Enhanced local language and cultural context
        context.localLanguage = generateLocalLanguageContext(location);
        // NEW: Advanced climate insights for business relevance
        context.climateInsights = generateClimateInsights(location, businessType);
        // NEW: Real-time trending topics (simulated for now, can be enhanced with actual APIs)
        context.trendingTopics = generateTrendingTopics(businessType, location, platform);
        // NEW: Local news and market insights
        context.news = generateLocalNewsContext(businessType, location);
        return context;
    } catch (error) {
        return context; // Return partial context
    }
}
// Advanced design enhancement functions
function shouldIncludePeopleInDesign(businessType, location, visualStyle) {
    const peopleBusinessTypes = [
        'restaurant',
        'fitness',
        'healthcare',
        'education',
        'retail',
        'hospitality',
        'beauty',
        'wellness',
        'consulting',
        'coaching',
        'real estate',
        'finance',
        'technology',
        'marketing',
        'events',
        'photography',
        'fashion'
    ];
    return peopleBusinessTypes.some((type)=>businessType.toLowerCase().includes(type) || visualStyle === 'lifestyle' || visualStyle === 'authentic');
}
function getLocalCulturalContext(location) {
    const culturalContexts = {
        'kenya': 'Subtle Kenyan elements: warm earth tones, natural textures, community feel',
        'nigeria': 'Subtle Nigerian elements: vibrant accents, natural patterns, community warmth',
        'south africa': 'Subtle South African elements: diverse representation, natural colors, community spirit',
        'ghana': 'Subtle Ghanaian elements: warm tones, natural textures, community connection',
        'uganda': 'Subtle Ugandan elements: natural colors, community feel, authentic representation',
        'tanzania': 'Subtle Tanzanian elements: coastal influences, natural textures, community warmth',
        'ethiopia': 'Subtle Ethiopian elements: natural earth tones, community connection, authentic feel',
        'rwanda': 'Subtle Rwandan elements: natural colors, community spirit, authentic representation',
        'default': 'Natural, authentic feel with subtle local elements that feel genuine, not forced'
    };
    const locationKey = location.toLowerCase();
    for (const [key, context] of Object.entries(culturalContexts)){
        if (locationKey.includes(key)) {
            return context;
        }
    }
    return culturalContexts['default'];
}
function getDesignVariations(seed) {
    const variations = [
        {
            style: 'Modern Minimalist',
            layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',
            composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',
            mood: 'Professional, clean, sophisticated',
            elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'
        },
        {
            style: 'Dynamic Action',
            layout: 'Diagonal composition with movement, multiple focal points, energetic flow',
            composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',
            mood: 'Energetic, exciting, forward-moving',
            elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'
        },
        {
            style: 'Lifestyle Authentic',
            layout: 'Natural, candid composition with real-world settings, human-centered design',
            composition: 'Environmental context, natural lighting, authentic moments captured',
            mood: 'Warm, relatable, trustworthy, human',
            elements: 'Natural lighting, authentic people, real environments, warm color tones'
        },
        {
            style: 'Corporate Professional',
            layout: 'Structured grid layout, balanced composition, formal presentation',
            composition: 'Symmetrical balance, clear hierarchy, professional spacing',
            mood: 'Trustworthy, established, reliable, premium',
            elements: 'Corporate colors, professional imagery, clean typography, structured layout'
        },
        {
            style: 'Creative Artistic',
            layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',
            composition: 'Creative angles, artistic overlays, unique visual treatments',
            mood: 'Creative, innovative, unique, inspiring',
            elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'
        },
        {
            style: 'Tech Innovation',
            layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',
            composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',
            mood: 'Innovative, cutting-edge, digital, forward-thinking',
            elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'
        },
        {
            style: 'Cultural Heritage',
            layout: 'Traditional patterns mixed with modern design, cultural elements integrated',
            composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',
            mood: 'Cultural, authentic, heritage-proud, modern-traditional',
            elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'
        },
        {
            style: 'Luxury Premium',
            layout: 'Elegant, sophisticated layout with premium materials and finishes',
            composition: 'Luxurious spacing, premium typography, elegant proportions',
            mood: 'Luxurious, premium, exclusive, sophisticated',
            elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'
        }
    ];
    return variations[seed % variations.length];
}
function getAdvancedPeopleInstructions(businessType, location) {
    const culturalContext = getLocalCulturalContext(location);
    return `
**ADVANCED PEOPLE INTEGRATION:**
- Include diverse, authentic people with PERFECT FACIAL FEATURES
- Complete faces, symmetrical features, natural expressions, professional poses
- Faces fully visible, well-lit, anatomically correct with no deformations
- Cultural Context: ${culturalContext}
- Show people in varied, engaging settings:
  * Professional environments (modern offices, studios, workshops)
  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)
  * Industry-specific contexts (${businessType} environments)
  * Cultural celebrations and modern community gatherings
  * Urban settings (co-working spaces, tech hubs, modern city life)
  * Traditional meets modern (cultural heritage with contemporary life)
- Ensure representation reflects local demographics and cultural values
- Show real people in natural, engaging situations that vary by design
- People should be actively engaged with the business/service context
- Use authentic expressions of joy, confidence, success, and community
- Include intergenerational representation when appropriate
- Show modern African/local fashion and styling
- Ensure people are central to the story, not just decorative elements`;
}
// NEW: Industry Intelligence System with World-Class Design Benchmarks
function getIndustryDesignIntelligence(businessType) {
    const industryIntelligence = {
        'restaurant': {
            name: 'Restaurant & Food Service',
            worldClassBrands: [
                'Noma',
                'Eleven Madison Park',
                'The French Laundry',
                'Osteria Francescana',
                'Gaggan'
            ],
            designBenchmarks: {
                visualStyle: 'Sophisticated, appetizing, experiential',
                colorPalettes: [
                    'Warm earth tones',
                    'Rich burgundies',
                    'Cream whites',
                    'Deep greens',
                    'Gold accents'
                ],
                typography: 'Elegant serifs, sophisticated sans-serifs, handwritten touches',
                imagery: 'Food photography, intimate dining scenes, chef portraits, ingredient close-ups',
                layout: 'Clean, spacious, food-focused, premium feel',
                creativeElements: [
                    'Food textures',
                    'Culinary tools',
                    'Seasonal ingredients',
                    'Dining atmosphere',
                    'Chef artistry'
                ]
            },
            creativityFrameworks: [
                'Culinary storytelling through visual narrative',
                'Seasonal and ingredient-driven design evolution',
                'Chef personality and restaurant atmosphere integration',
                'Food photography as art form',
                'Dining experience visualization'
            ],
            industryTrends: [
                'Farm-to-table aesthetics',
                'Minimalist plating influence',
                'Chef celebrity culture',
                'Sustainable dining',
                'Global fusion'
            ]
        },
        'technology': {
            name: 'Technology & Innovation',
            worldClassBrands: [
                'Apple',
                'Tesla',
                'SpaceX',
                'Google',
                'Microsoft',
                'Adobe'
            ],
            designBenchmarks: {
                visualStyle: 'Futuristic, clean, innovative, premium',
                colorPalettes: [
                    'Deep blues',
                    'Pure whites',
                    'Accent colors',
                    'Gradients',
                    'Neon highlights'
                ],
                typography: 'Modern sans-serifs, geometric precision, clean hierarchy',
                imagery: 'Abstract tech elements, clean interfaces, innovation concepts, premium materials',
                layout: 'Grid-based, clean lines, lots of white space, focused messaging',
                creativeElements: [
                    'Geometric shapes',
                    'Digital interfaces',
                    'Innovation metaphors',
                    'Premium materials',
                    'Future concepts'
                ]
            },
            creativityFrameworks: [
                'Technology as art and innovation',
                'Clean, premium aesthetic with bold innovation',
                'Future-focused visual storytelling',
                'Interface and product integration',
                'Innovation and progress visualization'
            ],
            industryTrends: [
                'AI integration',
                'Sustainable tech',
                'Minimalist interfaces',
                'Premium positioning',
                'Innovation focus'
            ]
        },
        'healthcare': {
            name: 'Healthcare & Wellness',
            worldClassBrands: [
                'Mayo Clinic',
                'Cleveland Clinic',
                'Johns Hopkins',
                'Stanford Health',
                'Cleveland Clinic'
            ],
            designBenchmarks: {
                visualStyle: 'Trustworthy, caring, professional, accessible',
                colorPalettes: [
                    'Calming blues',
                    'Soft greens',
                    'Warm whites',
                    'Accent colors',
                    'Professional tones'
                ],
                typography: 'Clean, readable fonts, professional hierarchy, accessible sizing',
                imagery: 'Caring professionals, modern facilities, wellness concepts, community health',
                layout: 'Clean, organized, easy to navigate, trustworthy appearance',
                creativeElements: [
                    'Medical symbols',
                    'Wellness imagery',
                    'Community health',
                    'Professional care',
                    'Modern facilities'
                ]
            },
            creativityFrameworks: [
                'Care and compassion through visual design',
                'Trust and professionalism building',
                'Wellness and health promotion',
                'Community health engagement',
                'Modern healthcare accessibility'
            ],
            industryTrends: [
                'Telehealth integration',
                'Patient-centered care',
                'Digital health',
                'Wellness focus',
                'Community health'
            ]
        },
        'fitness': {
            name: 'Fitness & Wellness',
            worldClassBrands: [
                'Peloton',
                'Nike',
                'Adidas',
                'Equinox',
                'Planet Fitness',
                'CrossFit'
            ],
            designBenchmarks: {
                visualStyle: 'Energetic, motivational, premium, inclusive',
                colorPalettes: [
                    'Bold reds',
                    'Energetic oranges',
                    'Motivational yellows',
                    'Strong blacks',
                    'Accent colors'
                ],
                typography: 'Bold, energetic fonts, motivational messaging, strong hierarchy',
                imagery: 'Action shots, diverse athletes, motivational scenes, fitness environments',
                layout: 'Dynamic, energetic, motivational, inclusive',
                creativeElements: [
                    'Movement lines',
                    'Athletic energy',
                    'Diversity representation',
                    'Motivational elements',
                    'Fitness environments'
                ]
            },
            creativityFrameworks: [
                'Energy and motivation through visual design',
                'Inclusive fitness for all',
                'Athletic achievement celebration',
                'Community and belonging',
                'Personal transformation stories'
            ],
            industryTrends: [
                'Digital fitness',
                'Inclusive representation',
                'Community building',
                'Personal transformation',
                'Wellness integration'
            ]
        },
        'finance': {
            name: 'Finance & Banking',
            worldClassBrands: [
                'Goldman Sachs',
                'JP Morgan',
                'Morgan Stanley',
                'BlackRock',
                'Visa',
                'Mastercard'
            ],
            designBenchmarks: {
                visualStyle: 'Trustworthy, sophisticated, stable, premium',
                colorPalettes: [
                    'Deep blues',
                    'Professional grays',
                    'Gold accents',
                    'Clean whites',
                    'Trustworthy tones'
                ],
                typography: 'Professional serifs, clean sans-serifs, authoritative hierarchy',
                imagery: 'Modern buildings, professional environments, growth concepts, stability symbols',
                layout: 'Structured, professional, trustworthy, premium',
                creativeElements: [
                    'Financial symbols',
                    'Growth metaphors',
                    'Stability elements',
                    'Professional environments',
                    'Premium materials'
                ]
            },
            creativityFrameworks: [
                'Trust and stability through design',
                'Sophistication and premium positioning',
                'Growth and progress visualization',
                'Professional excellence',
                'Financial security representation'
            ],
            industryTrends: [
                'Digital banking',
                'Fintech innovation',
                'Sustainable finance',
                'Personal finance',
                'Cryptocurrency'
            ]
        },
        'education': {
            name: 'Education & Learning',
            worldClassBrands: [
                'Harvard',
                'MIT',
                'Stanford',
                'Coursera',
                'Khan Academy',
                'Duolingo'
            ],
            designBenchmarks: {
                visualStyle: 'Inspiring, accessible, modern, engaging',
                colorPalettes: [
                    'Inspiring blues',
                    'Creative purples',
                    'Warm oranges',
                    'Growth greens',
                    'Accent colors'
                ],
                typography: 'Readable fonts, inspiring hierarchy, accessible design',
                imagery: 'Learning environments, diverse students, innovation concepts, growth metaphors',
                layout: 'Engaging, organized, inspiring, accessible',
                creativeElements: [
                    'Learning symbols',
                    'Growth metaphors',
                    'Innovation elements',
                    'Diversity representation',
                    'Knowledge visualization'
                ]
            },
            creativityFrameworks: [
                'Inspiration and learning through design',
                'Accessibility and inclusion',
                'Innovation and progress',
                'Community and collaboration',
                'Personal growth stories'
            ],
            industryTrends: [
                'Online learning',
                'Personalized education',
                'STEM focus',
                'Global accessibility',
                'Innovation in learning'
            ]
        },
        'retail': {
            name: 'Retail & E-commerce',
            worldClassBrands: [
                'Amazon',
                'Apple',
                'Nike',
                'IKEA',
                'Zara',
                'Uniqlo'
            ],
            designBenchmarks: {
                visualStyle: 'Attractive, commercial, engaging, conversion-focused',
                colorPalettes: [
                    'Brand colors',
                    'Attractive accents',
                    'Commercial tones',
                    'Engaging highlights'
                ],
                typography: 'Commercial fonts, conversion-focused messaging, attractive hierarchy',
                imagery: 'Product showcases, lifestyle integration, commercial appeal, brand personality',
                layout: 'Commercial, attractive, conversion-optimized, engaging',
                creativeElements: [
                    'Product elements',
                    'Lifestyle integration',
                    'Commercial appeal',
                    'Brand personality',
                    'Conversion elements'
                ]
            },
            creativityFrameworks: [
                'Commercial appeal and conversion',
                'Brand personality expression',
                'Lifestyle integration',
                'Product storytelling',
                'Customer engagement'
            ],
            industryTrends: [
                'E-commerce growth',
                'Personalization',
                'Sustainability',
                'Mobile commerce',
                'Social commerce'
            ]
        },
        'real estate': {
            name: 'Real Estate & Property',
            worldClassBrands: [
                'Sotheby\'s',
                'Christie\'s',
                'Douglas Elliman',
                'Compass',
                'Zillow'
            ],
            designBenchmarks: {
                visualStyle: 'Luxurious, aspirational, trustworthy, premium',
                colorPalettes: [
                    'Luxury golds',
                    'Sophisticated grays',
                    'Premium whites',
                    'Rich browns',
                    'Accent colors'
                ],
                typography: 'Luxury fonts, sophisticated hierarchy, premium appearance',
                imagery: 'Luxury properties, premium environments, aspirational lifestyles, professional service',
                layout: 'Luxurious, sophisticated, premium, aspirational',
                creativeElements: [
                    'Luxury elements',
                    'Premium materials',
                    'Aspirational lifestyles',
                    'Professional service',
                    'Property showcase'
                ]
            },
            creativityFrameworks: [
                'Luxury and aspiration through design',
                'Trust and professionalism',
                'Premium positioning',
                'Lifestyle visualization',
                'Property storytelling'
            ],
            industryTrends: [
                'Digital property viewing',
                'Sustainable properties',
                'Luxury market growth',
                'Technology integration',
                'Global investment'
            ]
        },
        'default': {
            name: 'Professional Services',
            worldClassBrands: [
                'McKinsey',
                'Bain',
                'BCG',
                'Deloitte',
                'PwC',
                'EY'
            ],
            designBenchmarks: {
                visualStyle: 'Professional, trustworthy, modern, sophisticated',
                colorPalettes: [
                    'Professional blues',
                    'Trustworthy grays',
                    'Modern accents',
                    'Clean whites'
                ],
                typography: 'Professional fonts, clean hierarchy, trustworthy appearance',
                imagery: 'Professional environments, modern offices, business concepts, trust symbols',
                layout: 'Professional, organized, trustworthy, modern',
                creativeElements: [
                    'Professional elements',
                    'Business concepts',
                    'Trust symbols',
                    'Modern environments',
                    'Success indicators'
                ]
            },
            creativityFrameworks: [
                'Professional excellence through design',
                'Trust and credibility building',
                'Modern sophistication',
                'Business success visualization',
                'Professional service representation'
            ],
            industryTrends: [
                'Digital transformation',
                'Remote work',
                'Sustainability',
                'Innovation focus',
                'Global expansion'
            ]
        }
    };
    return industryIntelligence[businessType.toLowerCase()] || industryIntelligence['default'];
}
// NEW: Enhanced Creativity System with Industry Intelligence
function getEnhancedCreativityFramework(businessType, designStyle, seed) {
    const industryIntel = getIndustryDesignIntelligence(businessType);
    const creativityFrameworks = [
        {
            name: 'World-Class Benchmarking',
            approach: `Study and emulate the design excellence of ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}`,
            focus: 'Premium positioning, industry best practices, sophisticated aesthetics',
            elements: industryIntel.designBenchmarks.creativeElements,
            description: `Create designs that rival the sophistication and quality of ${industryIntel.name} industry leaders`
        },
        {
            name: 'Industry Trend Integration',
            approach: `Incorporate current ${industryIntel.name} trends: ${industryIntel.industryTrends.slice(0, 3).join(', ')}`,
            focus: 'Modern relevance, industry innovation, forward-thinking design',
            elements: [
                'Trend elements',
                'Innovation concepts',
                'Modern aesthetics',
                'Industry relevance'
            ],
            description: 'Design that feels current and relevant to the industry while maintaining creativity'
        },
        {
            name: 'Creative Storytelling',
            approach: industryIntel.creativityFrameworks[seed % industryIntel.creativityFrameworks.length],
            focus: 'Narrative design, emotional connection, brand storytelling',
            elements: [
                'Story elements',
                'Emotional triggers',
                'Narrative flow',
                'Brand personality'
            ],
            description: 'Use visual design to tell compelling stories that connect with the audience'
        },
        {
            name: 'Innovation & Disruption',
            approach: 'Challenge industry conventions with creative innovation',
            focus: 'Breaking norms, creative disruption, unique positioning',
            elements: [
                'Innovation elements',
                'Disruptive concepts',
                'Unique approaches',
                'Creative risk-taking'
            ],
            description: 'Create designs that stand out by challenging industry conventions'
        },
        {
            name: 'Cultural & Global Fusion',
            approach: 'Blend local cultural elements with global industry standards',
            focus: 'Cultural authenticity, global relevance, unique positioning',
            elements: [
                'Cultural elements',
                'Global standards',
                'Local authenticity',
                'Fusion concepts'
            ],
            description: 'Create designs that feel both locally authentic and globally competitive'
        }
    ];
    return creativityFrameworks[seed % creativityFrameworks.length];
}
// NEW: Industry-Specific Design Enhancement
function enhanceDesignWithIndustryIntelligence(designPrompt, businessType, designStyle, seed) {
    const industryIntel = getIndustryDesignIntelligence(businessType);
    const creativityFramework = getEnhancedCreativityFramework(businessType, designStyle, seed);
    const industryEnhancement = `
🏭 INDUSTRY INTELLIGENCE INTEGRATION:
**Industry:** ${industryIntel.name}
**World-Class Benchmarks:** ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}
**Industry Visual Style:** ${industryIntel.designBenchmarks.visualStyle}
**Industry Color Palettes:** ${industryIntel.designBenchmarks.colorPalettes.slice(0, 3).join(', ')}
**Industry Typography:** ${industryIntel.designBenchmarks.typography}
**Industry Imagery:** ${industryIntel.designBenchmarks.imagery}
**Industry Layout:** ${industryIntel.designBenchmarks.layout}

🎨 CREATIVITY FRAMEWORK: ${creativityFramework.name}
**Approach:** ${creativityFramework.approach}
**Focus:** ${creativityFramework.focus}
**Creative Elements:** ${creativityFramework.elements.slice(0, 3).join(', ')}
**Description:** ${creativityFramework.description}

🚀 INDUSTRY TRENDS TO INCORPORATE:
${industryIntel.industryTrends.slice(0, 3).map((trend, i)=>`${i + 1}. ${trend}`).join('\n')}

🎯 DESIGN REQUIREMENTS:
- **Industry Benchmarking:** Create designs that rival ${industryIntel.name} industry leaders
- **Trend Integration:** Incorporate current industry trends naturally
- **Creative Innovation:** Use ${creativityFramework.name} approach for unique positioning
- **Quality Standards:** Match world-class design quality and sophistication
- **Industry Relevance:** Ensure design feels authentic to ${industryIntel.name} industry`;
    return designPrompt + industryEnhancement;
}
// NEW: Business Intelligence Engine - Local Marketing Expert System
function getBusinessIntelligenceEngine(businessType, location) {
    const businessIntelligence = {
        'restaurant': {
            name: 'Restaurant & Food Service',
            localExpertise: {
                experience: '25+ years in hospitality and culinary marketing',
                marketDynamics: [
                    'Seasonal menu optimization and local ingredient sourcing',
                    'Customer loyalty programs and repeat business strategies',
                    'Local competition analysis and unique positioning',
                    'Food trends and cultural preferences in the area',
                    'Pricing strategies for local market conditions'
                ],
                localPhrases: [
                    'Taste of [location]',
                    'Where locals eat',
                    'Fresh from our kitchen',
                    'Made with love',
                    'Family recipe',
                    'Local favorite',
                    'Chef\'s special',
                    'Daily fresh',
                    'Home-cooked taste',
                    'Local ingredients'
                ],
                contentStrategies: [
                    'Behind-the-scenes kitchen stories',
                    'Chef personality and cooking philosophy',
                    'Local ingredient sourcing stories',
                    'Customer testimonials and success stories',
                    'Seasonal menu highlights',
                    'Local food culture integration',
                    'Community involvement and events',
                    'Sustainability and local farming partnerships'
                ],
                engagementHooks: [
                    'Food memories and nostalgia',
                    'Local pride and community connection',
                    'Health and wellness benefits',
                    'Family traditions and gatherings',
                    'Adventure and trying new flavors',
                    'Social sharing and food photography',
                    'Exclusive offers and VIP experiences',
                    'Local events and celebrations'
                ]
            }
        },
        'technology': {
            name: 'Technology & Innovation',
            localExpertise: {
                experience: '22+ years in tech marketing and digital transformation',
                marketDynamics: [
                    'Local tech ecosystem and startup culture',
                    'Digital adoption rates in the region',
                    'Competitive landscape and innovation gaps',
                    'Local talent pool and skill development',
                    'Government tech initiatives and support'
                ],
                localPhrases: [
                    'Innovation hub',
                    'Digital transformation',
                    'Tech-forward solutions',
                    'Future-ready',
                    'Smart [location]',
                    'Digital innovation',
                    'Tech excellence',
                    'Innovation center',
                    'Digital leadership',
                    'Tech ecosystem'
                ],
                contentStrategies: [
                    'Local tech success stories',
                    'Innovation case studies',
                    'Digital transformation journeys',
                    'Tech talent development',
                    'Local startup ecosystem',
                    'Government tech partnerships',
                    'Digital skills training',
                    'Smart city initiatives'
                ],
                engagementHooks: [
                    'Career advancement and skill development',
                    'Innovation and future thinking',
                    'Local tech community building',
                    'Digital transformation success',
                    'Tech entrepreneurship',
                    'Smart city development',
                    'Digital inclusion',
                    'Tech for social good'
                ]
            }
        },
        'healthcare': {
            name: 'Healthcare & Wellness',
            localExpertise: {
                experience: '28+ years in healthcare marketing and patient care',
                marketDynamics: [
                    'Local health demographics and needs',
                    'Healthcare accessibility and insurance coverage',
                    'Competing healthcare providers and services',
                    'Local health trends and concerns',
                    'Community health initiatives and partnerships'
                ],
                localPhrases: [
                    'Your health, our priority',
                    'Caring for [location] families',
                    'Local healthcare excellence',
                    'Community health partner',
                    'Your wellness journey',
                    'Health close to home',
                    'Caring professionals',
                    'Local health experts',
                    'Community wellness',
                    'Health for everyone'
                ],
                contentStrategies: [
                    'Patient success stories and testimonials',
                    'Local health education and prevention',
                    'Community health initiatives',
                    'Healthcare professional spotlights',
                    'Local health trends and insights',
                    'Wellness tips and advice',
                    'Health technology integration',
                    'Community partnerships and events'
                ],
                engagementHooks: [
                    'Family health and wellness',
                    'Preventive care and early detection',
                    'Local health community',
                    'Professional healthcare expertise',
                    'Health technology innovation',
                    'Community health improvement',
                    'Patient-centered care',
                    'Health education and awareness'
                ]
            }
        },
        'fitness': {
            name: 'Fitness & Wellness',
            localExpertise: {
                experience: '24+ years in fitness marketing and community building',
                marketDynamics: [
                    'Local fitness culture and preferences',
                    'Competing gyms and fitness options',
                    'Seasonal fitness trends and activities',
                    'Local sports teams and community events',
                    'Health awareness and wellness trends'
                ],
                localPhrases: [
                    'Your fitness journey starts here',
                    'Stronger [location] community',
                    'Local fitness excellence',
                    'Your wellness partner',
                    'Fitness for everyone',
                    'Local strength',
                    'Community fitness',
                    'Your health transformation',
                    'Local fitness family',
                    'Wellness close to home'
                ],
                contentStrategies: [
                    'Member transformation stories',
                    'Local fitness challenges and events',
                    'Community fitness initiatives',
                    'Trainer spotlights and expertise',
                    'Local sports team partnerships',
                    'Seasonal fitness programs',
                    'Wellness education and tips',
                    'Community health partnerships'
                ],
                engagementHooks: [
                    'Personal transformation and goals',
                    'Community fitness challenges',
                    'Local sports pride',
                    'Health and wellness education',
                    'Fitness community building',
                    'Seasonal fitness motivation',
                    'Professional training expertise',
                    'Inclusive fitness for all'
                ]
            }
        },
        'finance': {
            name: 'Finance & Banking',
            localExpertise: {
                experience: '26+ years in financial services and local banking',
                marketDynamics: [
                    'Local economic conditions and growth',
                    'Competing financial institutions',
                    'Local business financing needs',
                    'Personal finance trends in the area',
                    'Community investment opportunities'
                ],
                localPhrases: [
                    'Your financial partner in [location]',
                    'Local financial expertise',
                    'Community banking excellence',
                    'Your financial future',
                    'Local financial solutions',
                    'Community financial partner',
                    'Your money, our care',
                    'Local financial guidance',
                    'Community wealth building',
                    'Financial security close to home'
                ],
                contentStrategies: [
                    'Local business success stories',
                    'Financial education and literacy',
                    'Community investment initiatives',
                    'Local economic insights',
                    'Personal finance success stories',
                    'Business financing solutions',
                    'Local financial trends',
                    'Community financial partnerships'
                ],
                engagementHooks: [
                    'Financial security and planning',
                    'Local business growth',
                    'Community economic development',
                    'Personal finance education',
                    'Investment opportunities',
                    'Business financing solutions',
                    'Local economic pride',
                    'Financial wellness for families'
                ]
            }
        },
        'education': {
            name: 'Education & Learning',
            localExpertise: {
                experience: '23+ years in educational marketing and community learning',
                marketDynamics: [
                    'Local education standards and performance',
                    'Competing educational institutions',
                    'Local learning needs and preferences',
                    'Community education initiatives',
                    'Employment and skill development needs'
                ],
                localPhrases: [
                    'Learning excellence in [location]',
                    'Your educational journey',
                    'Local learning excellence',
                    'Community education partner',
                    'Your learning success',
                    'Local educational leadership',
                    'Community learning center',
                    'Your knowledge partner',
                    'Local educational excellence',
                    'Learning close to home'
                ],
                contentStrategies: [
                    'Student success stories',
                    'Local educational achievements',
                    'Community learning initiatives',
                    'Educational innovation and technology',
                    'Local employment partnerships',
                    'Skill development programs',
                    'Community education events',
                    'Local learning trends'
                ],
                engagementHooks: [
                    'Personal growth and development',
                    'Career advancement opportunities',
                    'Local educational pride',
                    'Community learning initiatives',
                    'Innovation in education',
                    'Skill development and training',
                    'Local employment success',
                    'Educational excellence recognition'
                ]
            }
        },
        'retail': {
            name: 'Retail & E-commerce',
            localExpertise: {
                experience: '25+ years in retail marketing and customer experience',
                marketDynamics: [
                    'Local shopping preferences and trends',
                    'Competing retail options and malls',
                    'Local economic conditions and spending',
                    'Seasonal shopping patterns',
                    'Community shopping habits and events'
                ],
                localPhrases: [
                    'Your local shopping destination',
                    'Shopping excellence in [location]',
                    'Local retail leadership',
                    'Your shopping partner',
                    'Local retail excellence',
                    'Community shopping center',
                    'Your retail destination',
                    'Local shopping experience',
                    'Community retail partner',
                    'Shopping close to home'
                ],
                contentStrategies: [
                    'Local product highlights',
                    'Customer success stories',
                    'Community shopping events',
                    'Local brand partnerships',
                    'Seasonal shopping guides',
                    'Local shopping trends',
                    'Community retail initiatives',
                    'Local customer appreciation'
                ],
                engagementHooks: [
                    'Local product discovery',
                    'Community shopping events',
                    'Seasonal shopping excitement',
                    'Local brand support',
                    'Customer appreciation',
                    'Shopping convenience',
                    'Local retail pride',
                    'Community shopping experience'
                ]
            }
        },
        'real estate': {
            name: 'Real Estate & Property',
            localExpertise: {
                experience: '27+ years in real estate marketing and local property',
                marketDynamics: [
                    'Local property market conditions',
                    'Competing real estate agencies',
                    'Local property trends and values',
                    'Community development and growth',
                    'Local investment opportunities'
                ],
                localPhrases: [
                    'Your local real estate expert',
                    'Real estate excellence in [location]',
                    'Local property specialist',
                    'Your property partner',
                    'Local real estate leadership',
                    'Community property expert',
                    'Your real estate guide',
                    'Local property excellence',
                    'Community real estate partner',
                    'Property close to home'
                ],
                contentStrategies: [
                    'Local property success stories',
                    'Community development updates',
                    'Local property market insights',
                    'Property investment opportunities',
                    'Local neighborhood highlights',
                    'Community real estate events',
                    'Local property trends',
                    'Community property partnerships'
                ],
                engagementHooks: [
                    'Property investment opportunities',
                    'Local neighborhood pride',
                    'Community development',
                    'Property market insights',
                    'Local real estate success',
                    'Community property events',
                    'Property investment guidance',
                    'Local real estate expertise'
                ]
            }
        },
        'default': {
            name: 'Professional Services',
            localExpertise: {
                experience: '20+ years in professional services and local business',
                marketDynamics: [
                    'Local business environment and competition',
                    'Community business needs and trends',
                    'Local economic conditions',
                    'Business development opportunities',
                    'Community partnerships and networking'
                ],
                localPhrases: [
                    'Your local business partner',
                    'Professional excellence in [location]',
                    'Local business expertise',
                    'Your success partner',
                    'Local professional leadership',
                    'Community business partner',
                    'Your business guide',
                    'Local professional excellence',
                    'Community business expert',
                    'Success close to home'
                ],
                contentStrategies: [
                    'Local business success stories',
                    'Community business initiatives',
                    'Local business insights',
                    'Business development opportunities',
                    'Local business trends',
                    'Community business events',
                    'Local business partnerships',
                    'Community business support'
                ],
                engagementHooks: [
                    'Business growth and success',
                    'Local business community',
                    'Professional development',
                    'Business opportunities',
                    'Local business pride',
                    'Community business support',
                    'Business innovation',
                    'Local business expertise'
                ]
            }
        },
        'financial technology software': {
            name: 'Financial Technology Software',
            localExpertise: {
                experience: '15+ years in fintech and digital payments',
                marketDynamics: [
                    'Digital payment adoption rates in the region',
                    'Mobile banking and fintech competition',
                    'Financial inclusion and accessibility needs',
                    'Regulatory compliance and security requirements',
                    'Local banking partnerships and integrations'
                ],
                contentStrategies: [
                    'Digital financial innovation',
                    'Payment security and trust',
                    'Financial inclusion stories',
                    'Fintech industry insights',
                    'User experience excellence',
                    'Local market expansion',
                    'Partnership announcements',
                    'Technology advancement'
                ],
                engagementHooks: [
                    'Financial innovation',
                    'Digital payments',
                    'Financial inclusion',
                    'Secure transactions',
                    'Fintech solutions',
                    'Payment convenience',
                    'Financial empowerment',
                    'Digital banking'
                ]
            },
            localPhrases: [
                'Your digital payment partner',
                'Fintech innovation in [location]',
                'Digital financial solutions',
                'Your payment solution',
                'Financial technology excellence',
                'Digital banking for [location]',
                'Your fintech partner',
                'Payment innovation'
            ]
        },
        'default': {
            name: 'Professional Services',
            localExpertise: {
                experience: '20+ years in professional services',
                marketDynamics: [
                    'Local business environment and competition',
                    'Market trends and opportunities',
                    'Customer needs and preferences',
                    'Industry best practices and standards',
                    'Local economic conditions and growth'
                ],
                contentStrategies: [
                    'Professional excellence and expertise',
                    'Client success stories',
                    'Industry insights and trends',
                    'Local market knowledge',
                    'Service quality and reliability',
                    'Innovation and solutions',
                    'Community involvement',
                    'Professional development'
                ],
                engagementHooks: [
                    'Professional excellence',
                    'Client success',
                    'Industry expertise',
                    'Local market knowledge',
                    'Quality service',
                    'Innovation solutions',
                    'Community partnership',
                    'Professional growth'
                ]
            },
            localPhrases: [
                'Your local professional partner',
                'Excellence in [location]',
                'Local expertise you can trust',
                'Your success partner',
                'Professional solutions for [location]',
                'Local industry leadership',
                'Your trusted advisor',
                'Professional excellence'
            ]
        }
    };
    const result = businessIntelligence[businessType.toLowerCase()] || businessIntelligence['default'];
    return result;
}
// NEW: Dynamic Content Strategy Engine - Never Repetitive
function getDynamicContentStrategy(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const contentStrategies = [
        {
            name: 'Local Market Expert',
            approach: `Position as the ${businessIntel.name} expert in ${location} with ${businessIntel.localExpertise.experience}`,
            focus: 'Local expertise, community knowledge, market insights',
            hooks: businessIntel.localExpertise.engagementHooks.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'local expertise',
                'community focused',
                'trusted service',
                'proven results'
            ]).slice(0, 4),
            description: `Write like a ${businessIntel.localExpertise.experience} professional who knows ${location} inside and out`
        },
        {
            name: 'Community Storyteller',
            approach: `Share authentic stories about local ${businessIntel.name} success and community impact`,
            focus: 'Real stories, community connection, authentic experiences',
            hooks: businessIntel.localExpertise.engagementHooks.slice(4, 8),
            phrases: (businessIntel.localPhrases || [
                'community stories',
                'local success',
                'authentic experiences',
                'real results'
            ]).slice(4, 8),
            description: 'Share real, relatable stories that connect with the local community'
        },
        {
            name: 'Industry Innovator',
            approach: `Showcase cutting-edge ${businessIntel.name} solutions and industry leadership`,
            focus: 'Innovation, industry trends, competitive advantage',
            hooks: businessIntel.localExpertise.contentStrategies.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'innovative solutions',
                'industry leader',
                'cutting-edge',
                'advanced technology'
            ]).slice(0, 4),
            description: 'Position as an industry leader with innovative solutions and insights'
        },
        {
            name: 'Problem Solver',
            approach: `Address specific ${businessIntel.name} challenges that local businesses and people face`,
            focus: 'Problem identification, solution offering, value demonstration',
            hooks: businessIntel.localExpertise.marketDynamics.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'problem solver',
                'effective solutions',
                'proven results',
                'reliable service'
            ]).slice(0, 4),
            description: 'Identify and solve real problems that matter to the local community'
        },
        {
            name: 'Success Catalyst',
            approach: `Inspire and guide local ${businessIntel.name} success through proven strategies`,
            focus: 'Success stories, proven methods, inspirational guidance',
            hooks: businessIntel.localExpertise.contentStrategies.slice(4, 8),
            phrases: (businessIntel.localPhrases || [
                'success catalyst',
                'proven strategies',
                'inspiring results',
                'growth partner'
            ]).slice(4, 8),
            description: 'Inspire success through proven strategies and real results'
        }
    ];
    return contentStrategies[seed % contentStrategies.length];
}
// NEW: Human Writing Style Generator - Authentic, Engaging
function getHumanWritingStyle(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const writingStyles = [
        {
            name: 'Conversational Expert',
            tone: 'Friendly, knowledgeable, approachable',
            voice: `Like a ${businessIntel.localExpertise.experience} professional chatting with a friend over coffee`,
            characteristics: [
                'Use local phrases naturally',
                'Share personal insights and experiences',
                'Ask engaging questions',
                'Use conversational language',
                'Show genuine enthusiasm for the business'
            ],
            examples: [
                `"You know what I love about ${location}? The way our community..."`,
                `"After ${businessIntel.localExpertise.experience} in this industry, I've learned..."`,
                `"Here's something that always makes me smile about our business..."`
            ]
        },
        {
            name: 'Storytelling Mentor',
            tone: 'Inspirational, narrative, engaging',
            voice: 'Like sharing a compelling story that teaches and inspires',
            characteristics: [
                'Start with intriguing hooks',
                'Build narrative tension',
                'Include relatable characters',
                'End with meaningful insights',
                'Use vivid, descriptive language'
            ],
            examples: [
                `"Last week, something incredible happened that reminded me why..."`,
                `"I'll never forget the day when..."`,
                `"There's a story behind every success, and this one..."`
            ]
        },
        {
            name: 'Local Champion',
            tone: 'Proud, community-focused, authentic',
            voice: 'Like a proud local business owner celebrating community success',
            characteristics: [
                'Celebrate local achievements',
                'Use local pride and identity',
                'Highlight community connections',
                'Show genuine local love',
                'Connect business to community values'
            ],
            examples: [
                `"This is why I'm so proud to be part of the ${location} community..."`,
                `"Our ${location} neighbors never cease to amaze me..."`,
                `"There's something special about doing business in ${location}..."`
            ]
        },
        {
            name: 'Problem-Solving Partner',
            tone: 'Helpful, solution-oriented, trustworthy',
            voice: 'Like a trusted advisor helping solve real problems',
            characteristics: [
                'Identify real problems',
                'Offer practical solutions',
                'Show understanding and empathy',
                'Build trust through expertise',
                'Focus on customer benefit'
            ],
            examples: [
                `"I've noticed that many ${location} businesses struggle with..."`,
                `"Here's a solution that's worked for countless local businesses..."`,
                `"Let me share what I've learned about solving this common challenge..."`
            ]
        },
        {
            name: 'Success Celebrator',
            tone: 'Enthusiastic, celebratory, motivational',
            voice: 'Like celebrating wins and inspiring future success',
            characteristics: [
                'Celebrate achievements',
                'Share success stories',
                'Inspire future action',
                'Use positive, uplifting language',
                'Connect success to community'
            ],
            examples: [
                `"I'm thrilled to share some amazing news from our ${location} community..."`,
                `"This success story is exactly why I love ${businessIntel.name} in ${location}..."`,
                `"Let's celebrate this incredible achievement together..."`
            ]
        }
    ];
    return writingStyles[seed % writingStyles.length];
}
// NEW: Anti-Repetition Content Engine
function generateUniqueContentVariation(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const contentStrategy = getDynamicContentStrategy(businessType, location, seed);
    const writingStyle = getHumanWritingStyle(businessType, location, seed);
    // Generate unique content angle based on multiple factors
    const contentAngles = [
        {
            type: 'Local Insight',
            focus: `Share unique ${businessIntel.name} insights specific to ${location}`,
            examples: [
                `"What I've learned about ${businessIntel.name} in ${location} after ${businessIntel.localExpertise.experience}..."`,
                `"The ${businessIntel.name} landscape in ${location} is unique because..."`,
                `"Here's what makes ${location} special for ${businessIntel.name} businesses..."`
            ]
        },
        {
            type: 'Community Story',
            focus: `Tell a compelling story about local ${businessIntel.name} impact`,
            examples: [
                `"Last month, something incredible happened in our ${location} community..."`,
                `"I want to share a story that perfectly captures why we do what we do..."`,
                `"This is the kind of moment that makes ${businessIntel.name} in ${location} special..."`
            ]
        },
        {
            type: 'Industry Innovation',
            focus: `Showcase cutting-edge ${businessIntel.name} solutions`,
            examples: [
                `"We're excited to introduce something that's changing ${businessIntel.name} in ${location}..."`,
                `"Here's how we're innovating in the ${businessIntel.name} space..."`,
                `"This new approach is revolutionizing how we do ${businessIntel.name} in ${location}..."`
            ]
        },
        {
            type: 'Problem Solution',
            focus: `Address specific ${businessIntel.name} challenges in ${location}`,
            examples: [
                `"I've noticed that many ${location} businesses struggle with..."`,
                `"Here's a common challenge in ${businessIntel.name} and how we solve it..."`,
                `"Let me share what I've learned about overcoming this ${businessIntel.name} obstacle..."`
            ]
        },
        {
            type: 'Success Celebration',
            focus: `Celebrate local ${businessIntel.name} achievements`,
            examples: [
                `"I'm thrilled to share some amazing news from our ${location} community..."`,
                `"This success story is exactly why I love ${businessIntel.name} in ${location}..."`,
                `"Let's celebrate this incredible achievement together..."`
            ]
        }
    ];
    const selectedAngle = contentAngles[seed % contentAngles.length];
    return {
        contentStrategy: contentStrategy,
        writingStyle: writingStyle,
        contentAngle: selectedAngle,
        uniqueSignature: `${selectedAngle.type}-${contentStrategy.name}-${writingStyle.name}-${seed}`,
        localPhrases: (businessIntel.localPhrases || [
            'professional service',
            'quality results',
            'trusted expertise'
        ]).slice(0, 3),
        engagementHooks: businessIntel.localExpertise.engagementHooks.slice(0, 3),
        marketInsights: businessIntel.localExpertise.marketDynamics.slice(0, 2)
    };
}
// Helper functions for context generation
function getSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Fall';
    return 'Winter';
}
function getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'Morning';
    if (hour >= 12 && hour < 17) return 'Afternoon';
    if (hour >= 17 && hour < 21) return 'Evening';
    return 'Night';
}
function generateContextualTrends(businessType, location) {
    const trends = [
        {
            topic: `${businessType} innovation trends`,
            category: 'Industry',
            relevance: 'high'
        },
        {
            topic: `${location} business growth`,
            category: 'Local',
            relevance: 'high'
        },
        {
            topic: 'Digital transformation',
            category: 'Technology',
            relevance: 'medium'
        },
        {
            topic: 'Customer experience optimization',
            category: 'Business',
            relevance: 'high'
        },
        {
            topic: 'Sustainable business practices',
            category: 'Trends',
            relevance: 'medium'
        }
    ];
    return trends.slice(0, 3);
}
function generateWeatherContext(location) {
    // Simplified weather context based on location and season
    const season = getSeason();
    const contexts = {
        'Spring': {
            condition: 'Fresh and energizing',
            business_impact: 'New beginnings, growth opportunities',
            content_opportunities: 'Renewal, fresh starts, growth themes'
        },
        'Summer': {
            condition: 'Bright and active',
            business_impact: 'High energy, outdoor activities',
            content_opportunities: 'Vibrant colors, active lifestyle, summer solutions'
        },
        'Fall': {
            condition: 'Cozy and productive',
            business_impact: 'Planning, preparation, harvest',
            content_opportunities: 'Preparation, results, autumn themes'
        },
        'Winter': {
            condition: 'Focused and strategic',
            business_impact: 'Planning, reflection, indoor focus',
            content_opportunities: 'Planning, strategy, winter solutions'
        }
    };
    return {
        temperature: '22',
        condition: contexts[season].condition,
        business_impact: contexts[season].business_impact,
        content_opportunities: contexts[season].content_opportunities
    };
}
function generateLocalOpportunities(businessType, location) {
    const opportunities = [
        {
            name: `${location} Business Expo`,
            venue: 'Local Convention Center',
            relevance: 'networking'
        },
        {
            name: `${businessType} Innovation Summit`,
            venue: 'Business District',
            relevance: 'industry'
        },
        {
            name: 'Local Entrepreneur Meetup',
            venue: 'Community Center',
            relevance: 'community'
        }
    ];
    return opportunities.slice(0, 2);
}
// Get API keys (supporting both server-side and client-side)
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;
if (!apiKey) {}
// Initialize Google GenAI client with Revo 1.0 configuration
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](apiKey);
// Revo 1.0 uses Gemini 2.5 Flash Image Preview
const REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';
async function generateRevo10Content(input) {
    try {
        // Convert input to BusinessProfile for advanced analysis
        const businessProfile = {
            businessName: input.businessName,
            businessType: input.businessType,
            location: input.location,
            targetAudience: input.targetAudience,
            brandVoice: input.writingTone,
            uniqueSellingPoints: [
                input.competitiveAdvantages || 'Quality service'
            ],
            competitors: []
        };
        // 📊 GENERATE ADVANCED CONTENT WITH DEEP ANALYSIS
        const advancedContent = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["advancedContentGenerator"].generateEngagingContent(businessProfile, input.platform, 'promotional');
        // 🎯 GET TRENDING INSIGHTS FOR ENHANCED RELEVANCE
        const trendingEnhancement = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
            businessType: input.businessType,
            platform: input.platform,
            location: input.location,
            targetAudience: input.targetAudience
        });
        // 📈 ANALYZE PERFORMANCE FOR CONTINUOUS IMPROVEMENT
        const performanceAnalysis = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$content$2d$performance$2d$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["performanceAnalyzer"].analyzePerformance(advancedContent, businessProfile);
        // Extract hashtags from advanced content for use in business-specific generation
        const hashtags = advancedContent.hashtags;
        // Gather real-time context data (keeping existing functionality)
        const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the content generation prompt with enhanced brand context
        const contentPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Prompts"].CONTENT_USER_PROMPT_TEMPLATE.replace('{businessName}', input.businessName).replace('{businessType}', input.businessType).replace('{platform}', input.platform).replace('{writingTone}', input.writingTone).replace('{location}', input.location).replace('{primaryColor}', input.primaryColor || '#3B82F6').replace('{visualStyle}', input.visualStyle || 'modern').replace('{targetAudience}', input.targetAudience).replace('{services}', input.services || '').replace('{keyFeatures}', input.keyFeatures || '').replace('{competitiveAdvantages}', input.competitiveAdvantages || '').replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');
        // 🎨 CREATIVE CAPTION GENERATION: Apply creative enhancement system
        // NEW: Get business intelligence and local marketing expertise
        const businessIntel = getBusinessIntelligenceEngine(input.businessType, input.location);
        const randomSeed = Math.floor(Math.random() * 10000) + Date.now();
        const uniqueContentVariation = generateUniqueContentVariation(input.businessType, input.location, randomSeed % 1000);
        // 🎯 NEW: Generate business-specific content strategy
        const businessDetails = {
            experience: '5+ years',
            expertise: input.keyFeatures,
            services: input.services,
            location: input.location,
            targetAudience: input.targetAudience
        };
        // Generate strategic content plan based on business type and goals
        const contentPlan = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StrategicContentPlanner"].generateBusinessSpecificContent(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness' // Can be dynamic based on business goals
        );
        // 🎨 NEW: Generate business-specific headlines and subheadlines with AI
        const businessHeadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificHeadline"])(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness', trendingEnhancement, advancedContent);
        const businessSubheadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificSubheadline"])(input.businessType, input.businessName, input.location, businessDetails, businessHeadline.headline, 'awareness', trendingEnhancement, advancedContent);
        // 📝 NEW: Generate AI-powered business-specific caption
        const businessCaption = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificCaption"])(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness', trendingEnhancement, advancedContent);
        // 🎯 BUSINESS-SPECIFIC CAPTION GENERATION COMPLETE
        // 🎯 BUSINESS-SPECIFIC CONTENT GENERATION COMPLETE
        // 🎯 FINAL: Return business-specific content package
        const finalContent = {
            content: businessCaption.caption,
            headline: businessHeadline.headline,
            subheadline: businessSubheadline.subheadline,
            callToAction: businessCaption.callToAction,
            hashtags: hashtags,
            catchyWords: businessHeadline.headline,
            contentStrategy: contentPlan.strategy,
            businessStrengths: contentPlan.businessStrengths,
            marketOpportunities: contentPlan.marketOpportunities,
            valueProposition: contentPlan.valueProposition,
            platform: input.platform,
            businessType: input.businessType,
            location: input.location,
            realTimeContext: realTimeContext,
            creativeContext: {
                style: businessHeadline.approach,
                tone: businessHeadline.emotionalImpact,
                framework: businessSubheadline.framework,
                businessInsights: contentPlan,
                variation: uniqueContentVariation
            },
            // 🧠 BUSINESS INTELLIGENCE DATA
            businessIntelligence: {
                contentGoal: contentPlan.strategy.goal,
                businessStrengths: contentPlan.businessStrengths,
                marketOpportunities: contentPlan.marketOpportunities,
                customerPainPoints: contentPlan.customerPainPoints,
                valueProposition: contentPlan.valueProposition,
                localRelevance: contentPlan.localRelevance
            },
            variants: [
                {
                    platform: input.platform,
                    aspectRatio: '1:1',
                    imageUrl: '' // Will be generated separately
                }
            ],
            generatedAt: new Date().toISOString()
        };
        return finalContent;
    } catch (error) {
        throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Design(input) {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the design generation prompt
        const designPrompt = `Create a creative design concept for ${input.businessName} (${input.businessType}) that feels like it was imagined by a human designer.

Think like a creative professional who:
- Starts with a visual story or concept, not a template
- Uses colors and elements that feel chosen, not generated
- Creates layouts that feel natural and organic
- Thinks about the emotional impact, not just technical perfection

DESIGN BRIEF:
- Business: ${input.businessName} (${input.businessType})
- Platform: ${input.platform}
- Style: ${input.visualStyle}
- Colors: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}
- Message: ${input.imageText}

CREATIVE APPROACH:
- What's the visual story you want to tell?
- How can you make this feel authentic and engaging?
- What unexpected elements would make this memorable?
- How can you avoid looking like every other business post?

DESIGN DESCRIPTION:
Describe your creative concept in natural, designer language. Focus on the creative vision, mood, and story - not technical specifications. Make it sound like a designer explaining their idea to a client.

Remember: You're a creative human designer, not an AI. Think with imagination and artistic vision.`;
        const result = await model.generateContent([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Prompts"].DESIGN_SYSTEM_PROMPT,
            designPrompt
        ]);
        const response = await result.response;
        const design = response.text();
        return {
            design: design.trim(),
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Image(input) {
    try {
        // 🎨 CREATIVE ENHANCEMENT: Apply creative design system
        let creativeDesignEnhancement = '';
        if (input.creativeContext) {
            const designEnhancement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enhanceDesignCreativity"])(input.designDescription, input.businessType, input.location || 'Global', input.creativeContext);
            creativeDesignEnhancement = `
🎨 CREATIVE DESIGN ENHANCEMENT SYSTEM ACTIVATED:
${designEnhancement.enhancedPrompt}

CREATIVE VISUAL STYLE: ${designEnhancement.visualStyle}
CREATIVE ELEMENTS TO INCORPORATE: ${designEnhancement.creativeElements.join(', ')}
BUSINESS CREATIVE INSIGHTS: ${input.creativeContext.businessInsights?.creativePotential?.slice(0, 3).join(', ') || 'Professional excellence'}
EMOTIONAL DESIGN TONE: ${input.creativeContext.tone} with ${input.creativeContext.style} approach
CREATIVE FRAMEWORK: ${input.creativeContext.framework} storytelling structure

ANTI-GENERIC REQUIREMENTS:
- NO template-like designs or stock photo aesthetics
- NO boring business layouts or predictable compositions
- NO generic color schemes or uninspiring visual elements
- CREATE something memorable, unique, and emotionally engaging
- USE unexpected visual metaphors and creative storytelling
- INCORPORATE cultural elements naturally and authentically
- DESIGN with emotional intelligence and creative sophistication
`;
        }
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build advanced professional design prompt
        const brandInfo = input.location ? ` based in ${input.location}` : '';
        const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;
        const logoInstruction = input.logoDataUrl ? 'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' : 'Create professional design without logo overlay';
        // Prepare structured content display with hierarchy
        const contentStructure = [];
        if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): "${input.headline}"`);
        if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): "${input.subheadline}"`);
        if (input.callToAction) contentStructure.push(`CTA (Bold, action-oriented, prominent like "PAYA: YOUR FUTURE, NOW!" style): "${input.callToAction}"`);
        // 🎯 CTA PROMINENCE INSTRUCTIONS (like Paya example)
        const ctaInstructions = input.callToAction ? `

🎯 CRITICAL CTA DISPLAY REQUIREMENTS (LIKE PAYA EXAMPLE):
- The CTA "${input.callToAction}" MUST be displayed prominently on the design
- Make it BOLD, LARGE, and VISUALLY STRIKING like "PAYA: YOUR FUTURE, NOW!"
- Use high contrast colors to make the CTA stand out
- Position it prominently - top, center, or as a banner across the design
- Make the CTA text the MAIN FOCAL POINT of the design
- Use typography that commands attention - bold, modern, impactful
- Add visual elements (borders, backgrounds, highlights) to emphasize the CTA
- The CTA should be the FIRST thing people notice when they see the design
- Make it look like a professional marketing campaign CTA
- Ensure it's readable from mobile devices - minimum 32px equivalent font size
- EXAMPLE STYLE: Like "PAYA: YOUR FUTURE, NOW!" - bold, prominent, unmissable
    ` : '';
        // Get advanced design features
        const businessDesignDNA = getBusinessDesignDNA(input.businessType);
        const platformOptimization = getPlatformOptimization(input.platform);
        const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);
        const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';
        const culturalContext = getLocalCulturalContext(input.location || 'Global');
        // Generate human-like design variation for authentic, creative designs
        const designRandomSeed = Math.floor(Math.random() * 10000) + Date.now();
        const designSeed = designRandomSeed % 10000;
        const designVariations = getHumanDesignVariations(designSeed);
        // NEW: Get industry intelligence and creativity framework
        const industryIntel = getIndustryDesignIntelligence(input.businessType);
        const creativityFramework = getEnhancedCreativityFramework(input.businessType, designVariations.style, designSeed);
        let imagePrompt = `🎨 Create a ${designVariations.style.toLowerCase()} social media design for ${input.businessName} that looks completely different from typical business posts and feels genuinely human-made.

BUSINESS CONTEXT:
- Business: ${input.businessName} (${input.businessType})
- Platform: ${input.platform}
- Message: ${input.imageText}
- Location: ${input.location || 'Global'}

${ctaInstructions}

TEXT CONTENT TO DISPLAY:
${contentStructure.map((item)=>`- ${item}`).join('\n')}

DESIGN APPROACH:
- Create a design that's VISUALLY APPEALING and engaging
- Focus on the specific style: ${designVariations.style}
- Make it look genuinely different from other design types
- Each design type should have its own unique visual language
- **MOST IMPORTANT: Make it look like a human designer made it, not AI**
- **CRITICAL: Include ALL text content listed above in the design**

VISUAL STYLE:
- ${businessDesignDNA}
- ${platformOptimization}
- **SPECIFIC STYLE REQUIREMENTS: ${designVariations.description}**
- Use colors and elements that match this specific style
- Typography should match the style's mood and approach

🌍 SUBTLE LOCAL TOUCH (NOT OVERWHELMING):
- ${culturalContext}
- **Keep cultural elements subtle and natural - don't force them**
- Use local colors and textures naturally, not as obvious cultural markers
- Make it feel authentic to the location without being stereotypical
- Focus on the design style first, local elements second

DESIGN VARIATION:
**STYLE: ${designVariations.style}**
- Layout: ${designVariations.layout}
- Composition: ${designVariations.composition}
- Mood: ${designVariations.mood}
- Elements: ${designVariations.elements}

KEY DESIGN PRINCIPLES:
1. **STYLE-SPECIFIC APPROACH** - Follow the exact style requirements for ${designVariations.style}
2. **VISUAL UNIQUENESS** - Make this look completely different from other design types
3. **STYLE AUTHENTICITY** - If it's watercolor, make it look like real watercolor; if it's meme-style, make it look like a real meme
4. **HUMAN TOUCH** - Make it look like a human designer made it, not AI
5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative

WHAT TO AVOID:
- Overly complex layouts
- Too many competing elements
- Boring, generic business designs
- Poor contrast or readability
- Outdated design styles
- **MOST IMPORTANT: Don't make this look like the other design types - each should be genuinely unique**
- **AVOID: Overly perfect, symmetrical, AI-generated looking designs**
- **AVOID: Forced cultural elements that feel stereotypical**

WHAT TO INCLUDE:
- **Style-specific elements** that match ${designVariations.style}
- **Unique visual approach** for this specific style
- **Subtle local touches** that feel natural, not forced
- **Human imperfections** - slight asymmetry, natural spacing, organic feel
- **Style-appropriate typography** and layout

TECHNICAL REQUIREMENTS:
- Resolution: 2048x2048 pixels
- Format: Square (1:1)
- Text must be readable on mobile
- Logo integration should look natural

🎨 GOAL: Create a ${designVariations.style.toLowerCase()} design that looks completely different from other design types while feeling genuinely human-made. Focus on the specific style requirements, make it unique, and add subtle local touches without being overwhelming. The design should look like a skilled human designer created it, not AI.`;
        // NEW: Enhance with industry intelligence and creativity
        imagePrompt = enhanceDesignWithIndustryIntelligence(imagePrompt, input.businessType, designVariations.style, designSeed);
        // Inject multiple layers of human creativity to force AI out of its patterns
        imagePrompt = injectHumanImperfections(imagePrompt, designSeed);
        imagePrompt = injectCreativeRebellion(imagePrompt, designSeed);
        imagePrompt = addArtisticConstraints(imagePrompt, designSeed);
        if (input.creativeContext) {}
        // Prepare the generation request with logo if available
        const generationParts = [
            'You are a skilled graphic designer who creates visually appealing social media designs. Focus on creating designs that people actually want to engage with - clean, modern, and appealing. Keep it simple and focus on visual impact.',
            imagePrompt
        ];
        // If logo is provided, include it in the generation
        if (input.logoDataUrl) {
            // Extract the base64 data and mime type from the data URL
            const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);
            if (logoMatch) {
                const [, mimeType, base64Data] = logoMatch;
                generationParts.push({
                    inlineData: {
                        data: base64Data,
                        mimeType: mimeType
                    }
                });
                // Update the prompt to reference the provided logo
                const logoPrompt = `\n\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;
                generationParts[1] = imagePrompt + logoPrompt;
            } else {}
        }
        const result = await model.generateContent(generationParts);
        const response = await result.response;
        // Extract image data from Gemini response
        const parts = response.candidates?.[0]?.content?.parts || [];
        let imageUrl = '';
        for (const part of parts){
            if (part.inlineData) {
                const imageData = part.inlineData.data;
                const mimeType = part.inlineData.mimeType;
                imageUrl = `data:${mimeType};base64,${imageData}`;
                break;
            }
        }
        if (!imageUrl) {
            // Fallback: try to get text response if no image data
            const textResponse = response.text();
            throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');
        }
        return {
            imageUrl: imageUrl,
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function checkRevo10Health() {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL
        });
        const result = await model.generateContent('Hello');
        const response = await result.response;
        return {
            healthy: true,
            model: REVO_1_0_MODEL,
            response: response.text().substring(0, 50) + '...',
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return {
            healthy: false,
            model: REVO_1_0_MODEL,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        };
    }
}
function getRevo10ServiceInfo() {
    return {
        model: REVO_1_0_MODEL,
        version: '1.0.0',
        status: 'enhanced',
        aiService: 'gemini-2.5-flash-image-preview',
        capabilities: [
            'Enhanced content generation',
            'High-resolution image support (2048x2048)',
            'Perfect text rendering',
            'Advanced AI capabilities',
            'Enhanced brand consistency'
        ],
        pricing: {
            contentGeneration: 1.5,
            designGeneration: 1.5,
            tier: 'enhanced'
        },
        lastUpdated: '2025-01-27'
    };
}
// NEW: Enhanced local language and cultural context generator
function generateLocalLanguageContext(location) {
    const languageContexts = {
        'kenya': {
            primaryLanguage: 'Swahili & English',
            commonPhrases: [
                'Karibu',
                'Asante',
                'Jambo',
                'Mzuri sana'
            ],
            businessTerms: [
                'Biashara',
                'Mradi',
                'Kazi',
                'Ushirika'
            ],
            culturalNuances: 'Warm hospitality, community-first approach, respect for elders',
            marketingStyle: 'Personal, relationship-focused, community-oriented',
            localExpressions: [
                'Tuko pamoja',
                'Kazi yetu',
                'Jitihada zetu'
            ]
        },
        'nigeria': {
            primaryLanguage: 'English, Hausa, Yoruba, Igbo',
            commonPhrases: [
                'Oga',
                'Abeg',
                'Wetin dey happen',
                'How far'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Money',
                'Success'
            ],
            culturalNuances: 'Entrepreneurial spirit, networking culture, achievement focus',
            marketingStyle: 'Direct, motivational, success-oriented',
            localExpressions: [
                'No shaking',
                'I go do am',
                'We dey here'
            ]
        },
        'south africa': {
            primaryLanguage: 'English, Afrikaans, Zulu, Xhosa',
            commonPhrases: [
                'Howzit',
                'Lekker',
                'Ja',
                'Eish'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Diverse culture, innovation focus, global perspective',
            marketingStyle: 'Professional, inclusive, forward-thinking',
            localExpressions: [
                'Ubuntu',
                'Together we can',
                'Moving forward'
            ]
        },
        'ghana': {
            primaryLanguage: 'English, Twi, Ga, Ewe',
            commonPhrases: [
                'Akwaaba',
                'Medaase',
                'Yoo',
                'Chale'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Money',
                'Success'
            ],
            culturalNuances: 'Hospitality, respect, community values',
            marketingStyle: 'Warm, respectful, community-focused',
            localExpressions: [
                'Sankofa',
                'Unity in diversity',
                'Forward together'
            ]
        },
        'uganda': {
            primaryLanguage: 'English, Luganda, Runyankole',
            commonPhrases: [
                'Oli otya',
                'Webale',
                'Kale',
                'Nja'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Friendly, welcoming, community spirit',
            marketingStyle: 'Friendly, approachable, community-oriented',
            localExpressions: [
                'Tugende',
                'Together we grow',
                'Community first'
            ]
        },
        'tanzania': {
            primaryLanguage: 'Swahili & English',
            commonPhrases: [
                'Karibu',
                'Asante',
                'Jambo',
                'Mzuri'
            ],
            businessTerms: [
                'Biashara',
                'Kazi',
                'Mradi',
                'Ushirika'
            ],
            culturalNuances: 'Peaceful, community-focused, natural beauty appreciation',
            marketingStyle: 'Peaceful, natural, community-oriented',
            localExpressions: [
                'Uhuru na Umoja',
                'Peace and unity',
                'Natural beauty'
            ]
        },
        'ethiopia': {
            primaryLanguage: 'Amharic & English',
            commonPhrases: [
                'Selam',
                'Amesegenalu',
                'Endet',
                'Tena yistilign'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Ancient culture, hospitality, coffee culture',
            marketingStyle: 'Traditional, hospitable, culturally rich',
            localExpressions: [
                'Ethiopia first',
                'Coffee culture',
                'Ancient wisdom'
            ]
        },
        'rwanda': {
            primaryLanguage: 'Kinyarwanda, French & English',
            commonPhrases: [
                'Murakoze',
                'Amahoro',
                'Urugero',
                'Nta kibazo'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Innovation, cleanliness, community unity',
            marketingStyle: 'Innovative, clean, community-focused',
            localExpressions: [
                'Agaciro',
                'Dignity',
                'Unity and reconciliation'
            ]
        },
        'default': {
            primaryLanguage: 'English',
            commonPhrases: [
                'Hello',
                'Thank you',
                'Welcome',
                'Great'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Professional, friendly, community-oriented',
            marketingStyle: 'Professional, friendly, community-focused',
            localExpressions: [
                'Community first',
                'Quality service',
                'Local expertise'
            ]
        }
    };
    const locationKey = location.toLowerCase();
    for (const [key, context] of Object.entries(languageContexts)){
        if (locationKey.includes(key)) {
            return context;
        }
    }
    return languageContexts['default'];
}
// NEW: Advanced climate insights for business relevance
function generateClimateInsights(location, businessType) {
    const season = getSeason();
    const climateData = {
        'Spring': {
            businessImpact: 'Renewal and growth opportunities, seasonal business preparation',
            contentOpportunities: 'Fresh starts, new beginnings, seasonal preparation, growth themes',
            businessSuggestions: 'Launch new services, seasonal promotions, growth campaigns',
            localAdaptations: 'Spring cleaning services, seasonal menu changes, outdoor activities'
        },
        'Summer': {
            businessImpact: 'High energy and outdoor activities, peak business season',
            contentOpportunities: 'Vibrant colors, active lifestyle, summer solutions, outdoor themes',
            businessSuggestions: 'Summer specials, outdoor events, seasonal products',
            localAdaptations: 'Summer festivals, outdoor dining, seasonal services'
        },
        'Fall': {
            businessImpact: 'Planning and preparation, harvest and results focus',
            contentOpportunities: 'Preparation themes, results celebration, autumn aesthetics',
            businessSuggestions: 'Year-end planning, results showcase, preparation services',
            localAdaptations: 'Harvest celebrations, planning services, year-end reviews'
        },
        'Winter': {
            businessImpact: 'Strategic planning and indoor focus, reflection period',
            contentOpportunities: 'Planning themes, strategy focus, indoor solutions',
            businessSuggestions: 'Strategic planning, indoor services, year planning',
            localAdaptations: 'Indoor events, planning services, strategic consultations'
        }
    };
    // Add business-specific climate insights
    const businessClimateInsights = {
        'restaurant': {
            seasonalMenu: `${season} seasonal ingredients and dishes`,
            weatherAdaptation: `${season === 'Summer' ? 'Cooling beverages and light meals' : season === 'Winter' ? 'Warm comfort foods' : 'Seasonal specialties'}`,
            businessStrategy: `${season === 'Summer' ? 'Outdoor dining and seasonal menus' : 'Indoor comfort and seasonal specialties'}`
        },
        'fitness': {
            seasonalActivities: `${season === 'Summer' ? 'Outdoor workouts and water activities' : season === 'Winter' ? 'Indoor training and winter sports' : 'Seasonal fitness programs'}`,
            weatherAdaptation: `${season === 'Summer' ? 'Early morning and evening sessions' : 'Indoor and weather-appropriate activities'}`,
            businessStrategy: `${season === 'Summer' ? 'Outdoor fitness programs' : 'Indoor training focus'}`
        },
        'retail': {
            seasonalProducts: `${season} fashion and lifestyle products`,
            weatherAdaptation: `${season === 'Summer' ? 'Light clothing and outdoor gear' : season === 'Winter' ? 'Warm clothing and indoor items' : 'Seasonal essentials'}`,
            businessStrategy: `${season === 'Summer' ? 'Summer sales and outdoor products' : 'Seasonal collections and indoor focus'}`
        },
        'default': {
            seasonalFocus: `${season} business opportunities and seasonal services`,
            weatherAdaptation: `${season === 'Summer' ? 'Outdoor and seasonal services' : 'Indoor and year-round services'}`,
            businessStrategy: `${season} business strategies and seasonal promotions`
        }
    };
    const baseClimate = climateData[season];
    const businessClimate = businessClimateInsights[businessType.toLowerCase()] || businessClimateInsights['default'];
    return {
        season: season,
        businessImpact: baseClimate.businessImpact,
        contentOpportunities: baseClimate.contentOpportunities,
        businessSuggestions: baseClimate.businessSuggestions,
        localAdaptations: baseClimate.localAdaptations,
        businessSpecific: businessClimate,
        marketingAngle: `Leverage ${season.toLowerCase()} opportunities for ${businessType} business growth`
    };
}
// NEW: Real-time trending topics generator (can be enhanced with actual social media APIs)
function generateTrendingTopics(businessType, location, platform) {
    const platformTrends = {
        'Instagram': [
            {
                topic: 'Visual storytelling trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Authentic content creation',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Reels and short-form video',
                category: 'Format',
                relevance: 'medium'
            }
        ],
        'LinkedIn': [
            {
                topic: 'Professional networking trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Industry thought leadership',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Career development insights',
                category: 'Professional',
                relevance: 'medium'
            }
        ],
        'Facebook': [
            {
                topic: 'Community building strategies',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Local business networking',
                category: 'Community',
                relevance: 'high'
            },
            {
                topic: 'Family-friendly content',
                category: 'Content',
                relevance: 'medium'
            }
        ],
        'Twitter': [
            {
                topic: 'Real-time conversation trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Viral content strategies',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Trending hashtags',
                category: 'Engagement',
                relevance: 'medium'
            }
        ]
    };
    const businessTrends = {
        'restaurant': [
            {
                topic: 'Local food culture trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Sustainable dining practices',
                category: 'Trends',
                relevance: 'high'
            },
            {
                topic: 'Food delivery innovations',
                category: 'Technology',
                relevance: 'medium'
            }
        ],
        'technology': [
            {
                topic: 'AI and automation trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Digital transformation',
                category: 'Business',
                relevance: 'high'
            },
            {
                topic: 'Remote work solutions',
                category: 'Workplace',
                relevance: 'medium'
            }
        ],
        'healthcare': [
            {
                topic: 'Telehealth adoption',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Preventive healthcare',
                category: 'Wellness',
                relevance: 'high'
            },
            {
                topic: 'Mental health awareness',
                category: 'Health',
                relevance: 'medium'
            }
        ],
        'fitness': [
            {
                topic: 'Home workout trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Mental wellness integration',
                category: 'Wellness',
                relevance: 'high'
            },
            {
                topic: 'Community fitness challenges',
                category: 'Engagement',
                relevance: 'medium'
            }
        ],
        'finance': [
            {
                topic: 'Digital banking trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Financial literacy',
                category: 'Education',
                relevance: 'high'
            },
            {
                topic: 'Investment opportunities',
                category: 'Wealth',
                relevance: 'medium'
            }
        ],
        'education': [
            {
                topic: 'Online learning platforms',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Skill development trends',
                category: 'Learning',
                relevance: 'high'
            },
            {
                topic: 'Personalized education',
                category: 'Innovation',
                relevance: 'medium'
            }
        ],
        'retail': [
            {
                topic: 'E-commerce growth',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Omnichannel shopping',
                category: 'Customer',
                relevance: 'high'
            },
            {
                topic: 'Sustainable products',
                category: 'Trends',
                relevance: 'medium'
            }
        ],
        'real estate': [
            {
                topic: 'Virtual property tours',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Sustainable properties',
                category: 'Trends',
                relevance: 'high'
            },
            {
                topic: 'Investment opportunities',
                category: 'Market',
                relevance: 'medium'
            }
        ],
        'default': [
            {
                topic: 'Digital transformation trends',
                category: 'Business',
                relevance: 'high'
            },
            {
                topic: 'Customer experience optimization',
                category: 'Strategy',
                relevance: 'high'
            },
            {
                topic: 'Local business growth',
                category: 'Community',
                relevance: 'medium'
            }
        ]
    };
    const platformSpecific = platformTrends[platform] || platformTrends['Instagram'];
    const businessSpecific = businessTrends[businessType.toLowerCase()] || businessTrends['default'];
    const localTrends = [
        {
            topic: `${location} business growth`,
            category: 'Local',
            relevance: 'high'
        },
        {
            topic: `${location} community development`,
            category: 'Community',
            relevance: 'high'
        },
        {
            topic: `${location} economic trends`,
            category: 'Local',
            relevance: 'medium'
        }
    ];
    return [
        ...platformSpecific,
        ...businessSpecific,
        ...localTrends
    ].slice(0, 5);
}
// NEW: Local news and market insights generator
function generateLocalNewsContext(businessType, location) {
    const newsInsights = [
        {
            type: 'Local Market',
            headline: `${location} business environment update`,
            impact: 'Local market conditions affecting business opportunities',
            businessRelevance: 'Market positioning and strategic planning',
            contentAngle: 'Local market expertise and insights'
        },
        {
            type: 'Industry Trends',
            headline: `${businessType} industry developments in ${location}`,
            impact: 'Industry-specific opportunities and challenges',
            businessRelevance: 'Competitive positioning and service innovation',
            contentAngle: 'Industry leadership and local expertise'
        },
        {
            type: 'Community Events',
            headline: `${location} community and business events`,
            impact: 'Networking and community engagement opportunities',
            businessRelevance: 'Community involvement and local partnerships',
            contentAngle: 'Community connection and local engagement'
        },
        {
            type: 'Economic Update',
            headline: `${location} economic indicators and business climate`,
            impact: 'Business planning and investment decisions',
            businessRelevance: 'Strategic planning and market timing',
            contentAngle: 'Economic expertise and market insights'
        }
    ];
    return newsInsights.slice(0, 3);
}
}}),
"[project]/src/app/api/advanced-content/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Content Generation API
 * Tests the new advanced content generation system
 */ __turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-1.0-service.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        // Default test data if not provided
        const testData = {
            businessType: body.businessType || 'restaurant',
            businessName: body.businessName || 'Bella Vista Restaurant',
            location: body.location || 'New York, NY',
            platform: body.platform || 'instagram',
            writingTone: body.writingTone || 'friendly',
            contentThemes: body.contentThemes || [
                'food',
                'dining',
                'experience'
            ],
            targetAudience: body.targetAudience || 'food lovers and families',
            services: body.services || 'Fine dining, catering, private events',
            keyFeatures: body.keyFeatures || 'Fresh ingredients, authentic recipes, cozy atmosphere',
            competitiveAdvantages: body.competitiveAdvantages || 'Family-owned, locally sourced, 20+ years experience',
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: new Date().toLocaleDateString(),
            primaryColor: body.primaryColor || '#D97706',
            visualStyle: body.visualStyle || 'warm and inviting',
            ...body
        };
        // Generate advanced content
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateRevo10Content"])(testData);
        // Return enhanced response with analysis
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                // Core content
                headline: result.headline,
                subheadline: result.subheadline,
                caption: result.content,
                cta: result.callToAction,
                hashtags: result.hashtags,
                // Advanced intelligence insights
                intelligence: result.businessIntelligence,
                // Generation metadata
                metadata: {
                    businessName: testData.businessName,
                    businessType: testData.businessType,
                    location: testData.location,
                    platform: testData.platform,
                    generatedAt: new Date().toISOString(),
                    model: 'Revo 1.0 Enhanced',
                    aiService: 'gemini-2.5-flash-image-preview'
                },
                // Performance insights
                performance: {
                    hashtagCount: result.hashtags.length,
                    captionLength: result.content.length,
                    headlineLength: result.headline?.length || 0,
                    trendingKeywords: result.businessIntelligence?.trendingKeywords?.length || 0,
                    recommendations: result.businessIntelligence?.performanceRecommendations?.length || 0
                },
                // Quality metrics
                quality: {
                    hasHeadline: !!result.headline,
                    hasSubheadline: !!result.subheadline,
                    hasCTA: !!result.callToAction,
                    hashtagOptimized: result.hashtags.length >= 5 && result.hashtags.length <= 15,
                    captionOptimized: result.content.length >= 50 && result.content.length <= 500,
                    trendingIntegrated: (result.businessIntelligence?.trendingKeywords?.length || 0) > 0,
                    performanceAnalyzed: (result.businessIntelligence?.performanceRecommendations?.length || 0) > 0
                }
            }
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to generate advanced content',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const businessType = searchParams.get('businessType') || 'restaurant';
        const platform = searchParams.get('platform') || 'instagram';
        const location = searchParams.get('location') || 'New York, NY';
        // Quick test generation
        const testData = {
            businessType,
            businessName: `Test ${businessType.charAt(0).toUpperCase() + businessType.slice(1)}`,
            location,
            platform,
            writingTone: 'professional',
            contentThemes: [
                'quality',
                'service',
                'experience'
            ],
            targetAudience: 'local customers',
            services: 'Professional services',
            keyFeatures: 'Quality and reliability',
            competitiveAdvantages: 'Local expertise',
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: new Date().toLocaleDateString(),
            primaryColor: '#3B82F6',
            visualStyle: 'modern'
        };
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateRevo10Content"])(testData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Advanced content generation test successful',
            data: {
                headline: result.headline,
                caption: result.content.substring(0, 200) + '...',
                hashtagCount: result.hashtags.length,
                trendingKeywords: result.businessIntelligence?.trendingKeywords?.slice(0, 5) || [],
                recommendations: result.businessIntelligence?.performanceRecommendations?.slice(0, 3) || []
            }
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to test advanced content generation',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4f5ac329._.js.map