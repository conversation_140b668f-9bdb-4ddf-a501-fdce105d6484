module.exports = {

"[project]/node_modules/node-fetch/src/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_node-fetch_src_utils_multipart-parser_481f3d75.js",
  "server/chunks/node_modules_60bb1674._.js",
  "server/chunks/[root-of-the-server]__02243159._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/src/lib/image-processing.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__d9ed9975._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/image-processing.ts [app-route] (ecmascript)");
    });
});
}}),

};