/**
 * Revo 1.5 Generation API Route
 * Enhanced Model with Advanced Features and Artifact Support
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateRevo15EnhancedDesign } from '@/ai/revo-1.5-enhanced-design';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      businessType,
      platform,
      brandProfile,
      visualStyle,
      imageText,
      aspectRatio,
      includePeopleInDesigns,
      useLocalLanguage,
      artifactIds
    } = body;

    // Validate required fields
    if (!businessType || !platform || !brandProfile) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: businessType, platform, brandProfile'
      }, { status: 400 });
    }

    console.log('Revo 1.5 generation request:', {
      businessType,
      platform,
      visualStyle: visualStyle || 'modern',
      aspectRatio: aspectRatio || '1:1',
      artifactIds: artifactIds?.length || 0
    });

    // Prepare image text for Revo 1.5
    const finalImageText = imageText || `${brandProfile.businessName || businessType} - Premium Content`;

    // Generate content with Revo 1.5 Enhanced Design
    const result = await generateRevo15EnhancedDesign({
      businessType,
      platform: platform.toLowerCase(),
      visualStyle: visualStyle || 'modern',
      imageText: finalImageText,
      brandProfile: {
        businessName: brandProfile.businessName || businessType,
        businessType: brandProfile.businessType || businessType,
        location: brandProfile.location || 'Location',
        targetAudience: brandProfile.targetAudience || 'General',
        brandVoice: brandProfile.brandVoice || 'professional',
        contentThemes: brandProfile.contentThemes || [],
        services: brandProfile.services || [],
        keyFeatures: brandProfile.keyFeatures || [],
        competitiveAdvantages: brandProfile.competitiveAdvantages || [],
        visualStyle: visualStyle || 'modern',
        primaryColor: brandProfile.primaryColor || '#3B82F6',
        accentColor: brandProfile.accentColor || '#10B981',
        backgroundColor: brandProfile.backgroundColor || '#FFFFFF',
        logoUrl: brandProfile.logoUrl,
        writingTone: brandProfile.brandVoice || 'professional'
      },
      brandConsistency: {
        strictConsistency: false,
        followBrandColors: true
      },
      artifactInstructions: artifactIds?.map((id: string) => ({
        artifactId: id,
        usage: 'reference'
      })) || [],
      includePeopleInDesigns: includePeopleInDesigns || true,
      useLocalLanguage: useLocalLanguage || false
    });

    return NextResponse.json({
      success: true,
      imageUrl: result.imageUrl,
      model: 'Revo 1.5 (Enhanced with Gemini 2.5 Flash)',
      qualityScore: result.qualityScore || 8.8,
      processingTime: result.processingTime || '35s',
      enhancementsApplied: result.enhancementsApplied || [
        'Two-Step Design Process',
        'Advanced AI Engine',
        'Real-Time Context',
        'Trending Topics',
        'Artifact Integration',
        'Enhanced Quality Control'
      ],
      headline: result.headline || `${brandProfile.businessName || businessType} Excellence`,
      subheadline: result.subheadline || 'Premium quality and service',
      caption: result.caption || `Experience the best with ${brandProfile.businessName || businessType}. Quality you can trust, service you can rely on.`,
      cta: result.cta || 'Learn More',
      hashtags: result.hashtags || [
        `#${businessType.replace(/\s+/g, '')}`,
        '#Quality',
        '#Premium',
        '#Excellence',
        '#Professional'
      ],
      businessIntelligence: result.businessIntelligence || {
        contentGoal: 'Brand awareness and engagement',
        businessStrengths: ['Quality service', 'Professional approach'],
        marketOpportunities: ['Digital presence', 'Customer engagement'],
        valueProposition: 'Premium quality and reliable service'
      },
      artifactsUsed: artifactIds?.length || 0,
      message: 'Revo 1.5 content generated successfully'
    });

  } catch (error) {
    console.error('Revo 1.5 generation error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Revo 1.5 generation failed'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Revo 1.5 Generation API',
    description: 'Use POST method to generate content with Revo 1.5',
    requiredFields: ['businessType', 'platform', 'brandProfile'],
    optionalFields: ['visualStyle', 'imageText', 'aspectRatio', 'artifactIds'],
    model: 'Enhanced with Gemini 2.5 Flash',
    version: '1.5.0',
    status: 'enhanced',
    capabilities: [
      'Advanced AI engine with superior capabilities',
      'Enhanced content generation algorithms',
      'Superior quality control and consistency',
      'Professional design generation',
      'Real-time context and trending topics',
      'Full artifact support',
      'Multiple aspect ratios (1:1, 16:9, 9:16)',
      'Advanced text overlay'
    ],
    features: [
      'Two-step design process',
      'Artifact integration (up to 5 artifacts)',
      'Real-time context awareness',
      'Enhanced brand consistency',
      'Advanced prompting techniques',
      'Quality optimization algorithms'
    ],
    pricing: {
      creditsPerGeneration: 2,
      tier: 'enhanced'
    }
  });
}
