const CHUNK_PUBLIC_PATH = "server/app/api/generate-revo-1.5/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_ai_flows_generate-creative-asset_ts_2876c35f._.js");
runtime.loadChunk("server/chunks/node_modules_c34d8fcb._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__16872341._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/generate-revo-1.5/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-revo-1.5/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-revo-1.5/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
