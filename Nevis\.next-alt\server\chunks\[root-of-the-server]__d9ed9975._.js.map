{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/image-processing.ts"], "sourcesContent": ["/**\r\n * Image processing utilities for aspect ratio correction\r\n * Crops Gemini-generated 1024x1024 images to platform-specific aspect ratios\r\n * Server-side implementation using Sharp\r\n */\r\nimport sharp from 'sharp';\r\n\r\nexport interface CropOptions {\r\n  targetWidth?: number;\r\n  targetHeight?: number;\r\n  quality?: number;\r\n}\r\n\r\nexport interface PlatformDimensions {\r\n  width: number;\r\n  height: number;\r\n  aspectRatio: string;\r\n}\r\n\r\n/**\r\n * Get target dimensions for each platform\r\n */\r\nexport function getPlatformDimensions(platform: string): PlatformDimensions {\r\n  const platformLower = platform.toLowerCase();\r\n\r\n  // LinkedIn - 16:9 landscape (matches Revo 2.0 backend)\r\n  if (platformLower.includes('linkedin')) {\r\n    return {\r\n      width: 1200,\r\n      height: 675, // Standardized to match backend generation\r\n      aspectRatio: '16:9'\r\n    };\r\n  }\r\n\r\n  // Facebook - 16:9 landscape (matches Revo 2.0 backend)\r\n  if (platformLower.includes('facebook')) {\r\n    return {\r\n      width: 1200,\r\n      height: 675, // Standardized to match backend generation\r\n      aspectRatio: '16:9'\r\n    };\r\n  }\r\n\r\n  // Twitter - 16:9 landscape (matches Revo 2.0 backend)\r\n  if (platformLower.includes('twitter')) {\r\n    return {\r\n      width: 1200,\r\n      height: 675, // Standardized to match backend generation\r\n      aspectRatio: '16:9'\r\n    };\r\n  }\r\n\r\n  // Instagram Stories - 9:16 portrait\r\n  if (platformLower.includes('story') || platformLower.includes('reel')) {\r\n    return {\r\n      width: 1080,\r\n      height: 1920,\r\n      aspectRatio: '9:16'\r\n    };\r\n  }\r\n\r\n  // Instagram Feed - 1:1 square (keep original)\r\n  return {\r\n    width: 1024,\r\n    height: 1024,\r\n    aspectRatio: '1:1'\r\n  };\r\n}\r\n\r\n/**\r\n * Crop image from data URL to target aspect ratio using Sharp (server-side)\r\n */\r\nexport async function cropImageToAspectRatio(\r\n  imageDataUrl: string,\r\n  platform: string,\r\n  options: CropOptions = {}\r\n): Promise<string> {\r\n  try {\r\n\r\n    const dimensions = getPlatformDimensions(platform);\r\n\r\n    // If it's already square (Instagram), return as-is\r\n    if (dimensions.aspectRatio === '1:1') {\r\n      return imageDataUrl;\r\n    }\r\n\r\n    // Extract base64 data from data URL\r\n    const base64Data = imageDataUrl.split(',')[1];\r\n    if (!base64Data) {\r\n      throw new Error('Invalid data URL format');\r\n    }\r\n\r\n    const imageBuffer = Buffer.from(base64Data, 'base64');\r\n\r\n    // Get image metadata\r\n    const image = sharp(imageBuffer);\r\n    const metadata = await image.metadata();\r\n\r\n    if (!metadata.width || !metadata.height) {\r\n      throw new Error('Could not get image dimensions');\r\n    }\r\n\r\n\r\n    // Calculate crop dimensions from center\r\n    const sourceWidth = metadata.width;\r\n    const sourceHeight = metadata.height;\r\n\r\n    let cropWidth: number;\r\n    let cropHeight: number;\r\n    let left: number;\r\n    let top: number;\r\n\r\n    // No cropping - return original image\r\n    return imageDataUrl;\r\n\r\n\r\n    // Crop and resize the image using Sharp\r\n    const processedImageBuffer = await image\r\n      .extract({ left, top, width: cropWidth, height: cropHeight })\r\n      .resize(dimensions.width, dimensions.height)\r\n      .jpeg({ quality: options.quality || 95 })\r\n      .toBuffer();\r\n\r\n    // Convert back to data URL\r\n    const base64Image = processedImageBuffer.toString('base64');\r\n    const croppedDataUrl = `data:image/jpeg;base64,${base64Image}`;\r\n\r\n    return croppedDataUrl;\r\n\r\n  } catch (error) {\r\n    // Return original image if cropping fails\r\n    return imageDataUrl;\r\n  }\r\n}\r\n\r\n/**\r\n * Crop image from URL (fetches the image first) using Sharp (server-side)\r\n */\r\nexport async function cropImageFromUrl(\r\n  imageUrl: string,\r\n  platform: string,\r\n  options: CropOptions = {}\r\n): Promise<string> {\r\n  try {\r\n    // If it's already a data URL, use it directly\r\n    if (imageUrl.startsWith('data:')) {\r\n      return cropImageToAspectRatio(imageUrl, platform, options);\r\n    }\r\n\r\n\r\n    // Fetch the image\r\n    const response = await fetch(imageUrl);\r\n    if (!response.ok) {\r\n      throw new Error(`Failed to fetch image: ${response.statusText}`);\r\n    }\r\n\r\n    const arrayBuffer = await response.arrayBuffer();\r\n    const imageBuffer = Buffer.from(arrayBuffer);\r\n\r\n    // Get dimensions and check if cropping is needed\r\n    const dimensions = getPlatformDimensions(platform);\r\n    if (dimensions.aspectRatio === '1:1') {\r\n      // Convert to data URL and return\r\n      const base64 = imageBuffer.toString('base64');\r\n      const mimeType = response.headers.get('content-type') || 'image/jpeg';\r\n      return `data:${mimeType};base64,${base64}`;\r\n    }\r\n\r\n    // Process with Sharp directly from buffer\r\n    const image = sharp(imageBuffer);\r\n    const metadata = await image.metadata();\r\n\r\n    if (!metadata.width || !metadata.height) {\r\n      throw new Error('Could not get image dimensions');\r\n    }\r\n\r\n\r\n    // Calculate crop dimensions\r\n    const sourceWidth = metadata.width;\r\n    const sourceHeight = metadata.height;\r\n\r\n    let cropWidth: number;\r\n    let cropHeight: number;\r\n    let left: number;\r\n    let top: number;\r\n\r\n    // No cropping - return original image\r\n    const base64 = imageBuffer.toString('base64');\r\n    const mimeType = response.headers.get('content-type') || 'image/jpeg';\r\n    return `data:${mimeType};base64,${base64}`;\r\n\r\n\r\n    // Crop and resize\r\n    const processedImageBuffer = await image\r\n      .extract({ left, top, width: cropWidth, height: cropHeight })\r\n      .resize(dimensions.width, dimensions.height)\r\n      .jpeg({ quality: options.quality || 95 })\r\n      .toBuffer();\r\n\r\n    // Convert to data URL\r\n    const base64Image = processedImageBuffer.toString('base64');\r\n    const croppedDataUrl = `data:image/jpeg;base64,${base64Image}`;\r\n\r\n    return croppedDataUrl;\r\n\r\n  } catch (error) {\r\n    // Return original URL if processing fails\r\n    return imageUrl;\r\n  }\r\n}\r\n\r\n/**\r\n * Check if platform needs aspect ratio correction\r\n */\r\nexport function needsAspectRatioCorrection(platform: string): boolean {\r\n  const dimensions = getPlatformDimensions(platform);\r\n  return dimensions.aspectRatio !== '1:1';\r\n}\r\n\r\n/**\r\n * Get aspect ratio description for logging\r\n */\r\nexport function getAspectRatioDescription(platform: string): string {\r\n  const dimensions = getPlatformDimensions(platform);\r\n  return `${dimensions.width}x${dimensions.height} (${dimensions.aspectRatio})`;\r\n}\r\n\r\n/**\r\n * Validate data URL format\r\n */\r\nfunction validateDataUrl(dataUrl: string): boolean {\r\n  return dataUrl.startsWith('data:image/') && dataUrl.includes('base64,');\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AACD;;AAiBO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,gBAAgB,SAAS,WAAW;IAE1C,uDAAuD;IACvD,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO;YACL,OAAO;YACP,QAAQ;YACR,aAAa;QACf;IACF;IAEA,uDAAuD;IACvD,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO;YACL,OAAO;YACP,QAAQ;YACR,aAAa;QACf;IACF;IAEA,sDAAsD;IACtD,IAAI,cAAc,QAAQ,CAAC,YAAY;QACrC,OAAO;YACL,OAAO;YACP,QAAQ;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,SAAS;QACrE,OAAO;YACL,OAAO;YACP,QAAQ;YACR,aAAa;QACf;IACF;IAEA,8CAA8C;IAC9C,OAAO;QACL,OAAO;QACP,QAAQ;QACR,aAAa;IACf;AACF;AAKO,eAAe,uBACpB,YAAoB,EACpB,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QAEF,MAAM,aAAa,sBAAsB;QAEzC,mDAAmD;QACnD,IAAI,WAAW,WAAW,KAAK,OAAO;YACpC,OAAO;QACT;QAEA,oCAAoC;QACpC,MAAM,aAAa,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7C,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;QAE5C,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;QACpB,MAAM,WAAW,MAAM,MAAM,QAAQ;QAErC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,MAAM,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAGA,wCAAwC;QACxC,MAAM,cAAc,SAAS,KAAK;QAClC,MAAM,eAAe,SAAS,MAAM;QAEpC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,sCAAsC;QACtC,OAAO;;QAGP,wCAAwC;QACxC,MAAM;QAMN,2BAA2B;QAC3B,MAAM;QACN,MAAM;IAIR,EAAE,OAAO,OAAO;QACd,0CAA0C;QAC1C,OAAO;IACT;AACF;AAKO,eAAe,iBACpB,QAAgB,EAChB,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,8CAA8C;QAC9C,IAAI,SAAS,UAAU,CAAC,UAAU;YAChC,OAAO,uBAAuB,UAAU,UAAU;QACpD;QAGA,kBAAkB;QAClB,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,cAAc,MAAM,SAAS,WAAW;QAC9C,MAAM,cAAc,OAAO,IAAI,CAAC;QAEhC,iDAAiD;QACjD,MAAM,aAAa,sBAAsB;QACzC,IAAI,WAAW,WAAW,KAAK,OAAO;YACpC,iCAAiC;YACjC,MAAM,SAAS,YAAY,QAAQ,CAAC;YACpC,MAAM,WAAW,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACzD,OAAO,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,QAAQ;QAC5C;QAEA,0CAA0C;QAC1C,MAAM,QAAQ,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;QACpB,MAAM,WAAW,MAAM,MAAM,QAAQ;QAErC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,MAAM,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAGA,4BAA4B;QAC5B,MAAM,cAAc,SAAS,KAAK;QAClC,MAAM,eAAe,SAAS,MAAM;QAEpC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,sCAAsC;QACtC,MAAM,SAAS,YAAY,QAAQ,CAAC;QACpC,MAAM,WAAW,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;QACzD,OAAO,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,QAAQ;;QAG1C,kBAAkB;QAClB,MAAM;QAMN,sBAAsB;QACtB,MAAM;QACN,MAAM;IAIR,EAAE,OAAO,OAAO;QACd,0CAA0C;QAC1C,OAAO;IACT;AACF;AAKO,SAAS,2BAA2B,QAAgB;IACzD,MAAM,aAAa,sBAAsB;IACzC,OAAO,WAAW,WAAW,KAAK;AACpC;AAKO,SAAS,0BAA0B,QAAgB;IACxD,MAAM,aAAa,sBAAsB;IACzC,OAAO,GAAG,WAAW,KAAK,CAAC,CAAC,EAAE,WAAW,MAAM,CAAC,EAAE,EAAE,WAAW,WAAW,CAAC,CAAC,CAAC;AAC/E;AAEA;;CAEC,GACD,SAAS,gBAAgB,OAAe;IACtC,OAAO,QAAQ,UAAU,CAAC,kBAAkB,QAAQ,QAAQ,CAAC;AAC/D", "debugId": null}}]}