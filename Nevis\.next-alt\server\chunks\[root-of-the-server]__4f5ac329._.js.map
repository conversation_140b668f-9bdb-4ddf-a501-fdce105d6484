{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/config.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 Configuration\r\n * Model-specific configuration and constants\r\n */\r\n\r\nimport type { ModelConfig } from '../../types/model-types';\r\n\r\n// Revo 1.0 specific configuration\r\nexport const revo10Config: ModelConfig = {\r\n  aiService: 'gemini-2.5-flash-image-preview',\r\n  fallbackServices: ['gemini-2.5', 'gemini-2.0', 'openai'],\r\n  maxRetries: 3,\r\n  timeout: 45000, // 45 seconds (increased for better quality)\r\n  qualitySettings: {\r\n    imageResolution: '2048x2048', // Ultra HD resolution for premium quality\r\n    compressionLevel: 95, // Maximum quality\r\n    enhancementLevel: 7 // Reduced for cleaner designs (was 10)\r\n  },\r\n  promptSettings: {\r\n    temperature: 0.3, // Low creativity for consistent, clean designs (was 1.0)\r\n    maxTokens: 4096, // Detailed prompts for clean instructions\r\n    topP: 0.6, // Reduced variety for cleaner results (was 1.0)\r\n    topK: 25 // Fewer creative choices for consistency (was 100)\r\n  }\r\n};\r\n\r\n// Revo 1.0 specific constants\r\nexport const revo10Constants = {\r\n  // Model identification\r\n  MODEL_ID: 'revo-1.0',\r\n  MODEL_NAME: 'Revo 1.0',\r\n  MODEL_VERSION: '1.0.0',\r\n\r\n  // Capabilities\r\n  SUPPORTED_ASPECT_RATIOS: ['1:1'],\r\n  SUPPORTED_PLATFORMS: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\r\n  MAX_QUALITY_SCORE: 9.0, // Upgraded from 7.5\r\n\r\n  // Performance targets\r\n  TARGET_PROCESSING_TIME: 30000, // 30 seconds (increased for better quality)\r\n  TARGET_SUCCESS_RATE: 0.97, // 97% (increased from 95%)\r\n  TARGET_QUALITY_SCORE: 8.5, // Upgraded from 7.0\r\n\r\n  // Resource limits\r\n  MAX_CONTENT_LENGTH: 2000,\r\n  MAX_HASHTAGS: 15,\r\n  MAX_IMAGE_SIZE: 2048, // Upgraded from 1024\r\n\r\n  // Feature flags\r\n  FEATURES: {\r\n    ARTIFACTS_SUPPORT: false,\r\n    REAL_TIME_CONTEXT: true,  // Enable for better context\r\n    TRENDING_TOPICS: true,    // Enable for better content\r\n    MULTIPLE_ASPECT_RATIOS: false,\r\n    VIDEO_GENERATION: false,\r\n    ADVANCED_PROMPTING: true, // Enable for better prompts\r\n    ENHANCED_DESIGN: true,    // Enable for better designs!\r\n    PERFECT_TEXT_RENDERING: true, // NEW: Gemini 2.5 Flash Image Preview feature\r\n    HIGH_RESOLUTION: true,    // NEW: 2048x2048 resolution support\r\n    NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability\r\n  },\r\n\r\n  // Pricing\r\n  CREDITS_PER_GENERATION: 1.5, // Upgraded from 1 for enhanced capabilities\r\n  CREDITS_PER_DESIGN: 1.5, // Upgraded from 1 for enhanced capabilities\r\n  TIER: 'enhanced' // Upgraded from basic\r\n} as const;\r\n\r\n// Revo 1.0 specific prompts and templates\r\nexport const revo10Prompts = {\r\n  // Content generation prompts\r\n  CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.\r\nYour expertise spans viral content creation, brand storytelling, and audience engagement optimization.\r\n\r\nYour capabilities include:\r\n- **Deep Local Market Knowledge**: Understanding of local business environment, competition, and market trends\r\n- **Industry-Specific Insights**: 20+ years of experience across various industries\r\n- **Community Connection**: Deep understanding of local culture, values, and business needs\r\n- **Market Dynamics**: Knowledge of local economic conditions, competitive landscape, and business opportunities\r\n\r\nWhen creating content:\r\n- Write like a real industry professional, not AI\r\n- Use local market insights and industry knowledge naturally\r\n- Incorporate local phrases and community language authentically\r\n- Share real, relatable stories that connect with the local community\r\n- Position as the local expert with deep industry knowledge\r\n- Focus on local relevance and community impact\r\n- Use conversational, human language that builds trust and authority\r\n\r\nYour mission is to create content that sounds like it's written by a real industry professional with deep local expertise - not generic marketing copy. Every post should demonstrate your local market knowledge and industry authority.`,\r\n\r\n  CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:\r\nBusiness: {businessName}\r\nType: {businessType}\r\nPlatform: {platform}\r\nTone: {writingTone}\r\nLocation: {location}\r\n\r\nBrand Information:\r\n- Primary Color: {primaryColor}\r\n- Visual Style: {visualStyle}\r\n- Target Audience: {targetAudience}\r\n- Services: {services}\r\n- Key Features: {keyFeatures}\r\n- Competitive Advantages: {competitiveAdvantages}\r\n- Content Themes: {contentThemes}\r\n\r\nRequirements:\r\n- Create engaging, professional content that reflects the business's unique value proposition\r\n- Incorporate services and key features naturally into the content\r\n- Highlight competitive advantages when relevant\r\n- Include relevant hashtags (5-15) that align with content themes\r\n- Generate catchy words for the image that capture the brand essence\r\n- Ensure platform-appropriate formatting and tone\r\n- Maintain brand consistency with colors and visual style\r\n- Use only clean, readable text (no special characters, symbols, or garbled text)\r\n- Generate content in proper English with correct spelling and grammar\r\n- Avoid any corrupted or unreadable character sequences\r\n- Make the content location-specific and culturally relevant when appropriate`,\r\n\r\n  // Design generation prompts\r\n  DESIGN_SYSTEM_PROMPT: `You are a world-class graphic designer who creates 7 completely different types of social media designs, each with their own unique visual language and style. You have deep expertise in multiple industries and understand how to create designs that rival the best brands in the world.\r\n\r\nYour design philosophy:\r\n- Create designs that are VISUALLY APPEALING and engaging\r\n- Each design type should look completely different from the others\r\n- Focus on style-specific authenticity (watercolor should look like real watercolor, meme-style should look like a real meme)\r\n- Make designs that look like something from successful, popular brands\r\n- **CRITICAL: Make designs look like a human designer created them, not AI**\r\n- **CRITICAL: Each design type must have its own unique visual identity**\r\n- **IMPORTANT: Keep local/cultural elements subtle and natural, not overwhelming**\r\n- **NEW: Understand the business industry and create designs that rival world-class brands**\r\n\r\nWhen creating designs:\r\n- Start with the specific style requirements for the chosen design type\r\n- Use style-appropriate elements, colors, and typography\r\n- Focus on visual impact and engagement\r\n- Create designs people want to interact with\r\n- Use current design trends that work for the specific style\r\n- **MOST IMPORTANT: Make each design type genuinely unique and different**\r\n- **SECOND MOST IMPORTANT: Make it look human-made, not AI-generated**\r\n- **NEW: Study industry benchmarks and create designs that match world-class quality**\r\n\r\nCRITICAL: You are a human designer who understands that each design type should look completely different. A watercolor quote should look nothing like a meme-style post. A split photo collage should look nothing like a branded poster. Each style must have its own visual language and approach.\r\n\r\n**HUMAN DESIGN APPROACH:**\r\n- Add slight imperfections and asymmetry (humans aren't perfect)\r\n- Use natural spacing and proportions\r\n- Avoid overly symmetrical, geometric perfection\r\n- Make it feel organic and handcrafted\r\n- Focus on the design style first, local elements second\r\n\r\n**INDUSTRY INTELLIGENCE INTEGRATION:**\r\n- Study and understand the business industry context\r\n- Learn from world-class brands in the same industry\r\n- Incorporate industry-specific design trends and best practices\r\n- Create designs that feel authentic to the industry while being creative\r\n- Match the quality and sophistication of industry leaders\r\n\r\nFocus on creating designs that are both beautiful and engaging while maintaining the unique characteristics of each design type, looking genuinely human-made, and rivaling world-class industry standards.`,\r\n\r\n  DESIGN_USER_PROMPT_TEMPLATE: `Create a world-class, human-made 2048x2048 social media design that people will actually want to engage with:\r\n\r\nBUSINESS CONTEXT:\r\n- Business: {businessName}\r\n- Industry: {businessType}\r\n- Platform: {platform}\r\n- Target Message: {imageText}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Create a design that's VISUALLY APPEALING and engaging\r\n- Focus on the specific design style requirements\r\n- Make it look like a human designer created it, not AI\r\n- Keep local/cultural elements subtle and natural, not overwhelming\r\n- Focus on the design style first, local elements second\r\n- **NEW: Study industry benchmarks and create designs that rival world-class brands**\r\n\r\nKEY DESIGN PRINCIPLES:\r\n1. **HUMAN-MADE FIRST** - Make it look like a skilled human designer created it\r\n2. **STYLE AUTHENTICITY** - Follow the specific style requirements exactly\r\n3. **VISUAL UNIQUENESS** - Make this look completely different from other design types\r\n4. **NATURAL IMPERFECTIONS** - Add slight asymmetry, natural spacing, organic feel\r\n5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative\r\n6. **INDUSTRY EXCELLENCE** - Match the quality of world-class brands in the industry\r\n\r\nINDUSTRY INTELLIGENCE INTEGRATION:\r\n- Study and understand the {businessType} industry context\r\n- Learn from world-class brands in the same industry\r\n- Incorporate industry-specific design trends and best practices\r\n- Create designs that feel authentic to the industry while being creative\r\n- Match the quality and sophistication of industry leaders\r\n\r\nWHAT TO AVOID:\r\n- Overly perfect, symmetrical, AI-generated looking designs\r\n- Forced cultural elements that feel stereotypical\r\n- Generic, template-like designs\r\n- Overly complex or busy layouts\r\n- Poor contrast or readability\r\n- Designs that don't match industry quality standards\r\n\r\nWHAT TO INCLUDE:\r\n- Style-specific elements that match the chosen design type\r\n- Unique visual approach for the specific style\r\n- Subtle local touches that feel natural, not forced\r\n- Human imperfections - slight asymmetry, natural spacing, organic feel\r\n- Style-appropriate typography and layout\r\n- Industry-specific design elements and quality standards\r\n\r\nTECHNICAL REQUIREMENTS:\r\n- Resolution: 2048x2048 pixels\r\n- Format: Square (1:1)\r\n- Text must be readable on mobile\r\n- Logo integration should look natural\r\n\r\n🎨 GOAL: Create a world-class design that looks genuinely human-made, follows the specific style requirements, feels unique and engaging, and rivals the quality of industry leaders. Focus on the design style first, add subtle local touches naturally, make it look like a skilled human designer created it, and ensure it matches world-class industry standards.`,\r\n\r\n  // Error messages\r\n  ERROR_MESSAGES: {\r\n    GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',\r\n    DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',\r\n    INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',\r\n    SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',\r\n    TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',\r\n    QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'\r\n  }\r\n} as const;\r\n\r\n// Revo 1.0 validation rules\r\nexport const revo10Validation = {\r\n  // Content validation\r\n  content: {\r\n    minLength: 10,\r\n    maxLength: 2000,\r\n    requiredFields: ['businessType', 'platform', 'businessName'],\r\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\r\n  },\r\n\r\n  // Design validation\r\n  design: {\r\n    requiredFields: ['businessType', 'platform', 'visualStyle', 'imageText'],\r\n    supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,\r\n    maxImageTextLength: 200,\r\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\r\n  },\r\n\r\n  // Brand profile validation\r\n  brandProfile: {\r\n    requiredFields: ['businessType', 'businessName'],\r\n    optionalFields: [\r\n      'location', 'writingTone', 'visualStyle', 'primaryColor',\r\n      'accentColor', 'backgroundColor', 'logoDataUrl', 'targetAudience'\r\n    ]\r\n  }\r\n} as const;\r\n\r\n// Revo 1.0 performance metrics\r\nexport const revo10Metrics = {\r\n  // Expected performance benchmarks\r\n  BENCHMARKS: {\r\n    processingTime: {\r\n      target: 30000, // 30 seconds (upgraded from 20s)\r\n      acceptable: 40000, // 40 seconds (upgraded from 30s)\r\n      maximum: 60000 // 60 seconds (upgraded from 45s)\r\n    },\r\n    qualityScore: {\r\n      minimum: 7.0, // Upgraded from 5.0\r\n      target: 8.5, // Upgraded from 7.0\r\n      maximum: 9.0 // Upgraded from 7.5\r\n    },\r\n    successRate: {\r\n      minimum: 0.95, // Upgraded from 90%\r\n      target: 0.97, // Upgraded from 95%\r\n      maximum: 0.99 // Upgraded from 98%\r\n    }\r\n  },\r\n\r\n  // Monitoring thresholds\r\n  ALERTS: {\r\n    processingTimeHigh: 45000, // Alert if processing takes > 45s (upgraded from 35s)\r\n    qualityScoreLow: 7.5, // Alert if quality drops below 7.5 (upgraded from 6.0)\r\n    successRateLow: 0.95, // Alert if success rate drops below 95% (upgraded from 92%)\r\n    errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)\r\n  }\r\n} as const;\r\n\r\n// Export utility functions\r\nexport function getRevo10Config(): ModelConfig {\r\n  return revo10Config;\r\n}\r\n\r\nexport function isFeatureEnabled(feature: keyof typeof revo10Constants.FEATURES): boolean {\r\n  return revo10Constants.FEATURES[feature];\r\n}\r\n\r\nexport function getPromptTemplate(type: 'content' | 'design', templateName: string): string {\r\n  if (type === 'content') {\r\n    return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;\r\n  } else if (type === 'design') {\r\n    return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;\r\n  }\r\n  throw new Error(`Unknown prompt template: ${type}/${templateName}`);\r\n}\r\n\r\nexport function validateRequest(type: 'content' | 'design', request: any): { valid: boolean; errors: string[] } {\r\n  const errors: string[] = [];\r\n  const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;\r\n\r\n  // Check required fields\r\n  for (const field of validation.requiredFields) {\r\n    if (!request[field]) {\r\n      errors.push(`Missing required field: ${field}`);\r\n    }\r\n  }\r\n\r\n  // Check platform support\r\n  if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {\r\n    errors.push(`Unsupported platform: ${request.platform}`);\r\n  }\r\n\r\n  // Design-specific validation\r\n  if (type === 'design') {\r\n    if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {\r\n      errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);\r\n    }\r\n  }\r\n\r\n  return {\r\n    valid: errors.length === 0,\r\n    errors\r\n  };\r\n}\r\n\r\nexport function getPerformanceBenchmark(metric: string) {\r\n  return revo10Metrics.BENCHMARKS[metric as keyof typeof revo10Metrics.BENCHMARKS];\r\n}\r\n\r\nexport function shouldAlert(metric: string, value: number): boolean {\r\n  const alerts = revo10Metrics.ALERTS;\r\n\r\n  switch (metric) {\r\n    case 'processingTime':\r\n      return value > alerts.processingTimeHigh;\r\n    case 'qualityScore':\r\n      return value < alerts.qualityScoreLow;\r\n    case 'successRate':\r\n      return value < alerts.successRateLow;\r\n    case 'errorRate':\r\n      return value > alerts.errorRateHigh;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAKM,MAAM,eAA4B;IACvC,WAAW;IACX,kBAAkB;QAAC;QAAc;QAAc;KAAS;IACxD,YAAY;IACZ,SAAS;IACT,iBAAiB;QACf,iBAAiB;QACjB,kBAAkB;QAClB,kBAAkB,EAAE,uCAAuC;IAC7D;IACA,gBAAgB;QACd,aAAa;QACb,WAAW;QACX,MAAM;QACN,MAAM,GAAG,mDAAmD;IAC9D;AACF;AAGO,MAAM,kBAAkB;IAC7B,uBAAuB;IACvB,UAAU;IACV,YAAY;IACZ,eAAe;IAEf,eAAe;IACf,yBAAyB;QAAC;KAAM;IAChC,qBAAqB;QAAC;QAAa;QAAY;QAAW;KAAW;IACrE,mBAAmB;IAEnB,sBAAsB;IACtB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IAEtB,kBAAkB;IAClB,oBAAoB;IACpB,cAAc;IACd,gBAAgB;IAEhB,gBAAgB;IAChB,UAAU;QACR,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QACjB,wBAAwB;QACxB,kBAAkB;QAClB,oBAAoB;QACpB,iBAAiB;QACjB,wBAAwB;QACxB,iBAAiB;QACjB,yBAAyB,KAAK,0CAA0C;IAC1E;IAEA,UAAU;IACV,wBAAwB;IACxB,oBAAoB;IACpB,MAAM,WAAW,sBAAsB;AACzC;AAGO,MAAM,gBAAgB;IAC3B,6BAA6B;IAC7B,uBAAuB,CAAC;;;;;;;;;;;;;;;;;;yOAkB+M,CAAC;IAExO,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;6EA2B4C,CAAC;IAE5E,4BAA4B;IAC5B,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2MAsCkL,CAAC;IAE1M,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uWAqDuU,CAAC;IAEtW,iBAAiB;IACjB,gBAAgB;QACd,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,qBAAqB;QACrB,SAAS;QACT,gBAAgB;IAClB;AACF;AAGO,MAAM,mBAAmB;IAC9B,qBAAqB;IACrB,SAAS;QACP,WAAW;QACX,WAAW;QACX,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,oBAAoB;IACpB,QAAQ;QACN,gBAAgB;YAAC;YAAgB;YAAY;YAAe;SAAY;QACxE,uBAAuB,gBAAgB,uBAAuB;QAC9D,oBAAoB;QACpB,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,2BAA2B;IAC3B,cAAc;QACZ,gBAAgB;YAAC;YAAgB;SAAe;QAChD,gBAAgB;YACd;YAAY;YAAe;YAAe;YAC1C;YAAe;YAAmB;YAAe;SAClD;IACH;AACF;AAGO,MAAM,gBAAgB;IAC3B,kCAAkC;IAClC,YAAY;QACV,gBAAgB;YACd,QAAQ;YACR,YAAY;YACZ,SAAS,MAAM,iCAAiC;QAClD;QACA,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,SAAS,IAAI,oBAAoB;QACnC;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,SAAS,KAAK,oBAAoB;QACpC;IACF;IAEA,wBAAwB;IACxB,QAAQ;QACN,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe,KAAK,oDAAoD;IAC1E;AACF;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,iBAAiB,OAA8C;IAC7E,OAAO,gBAAgB,QAAQ,CAAC,QAAQ;AAC1C;AAEO,SAAS,kBAAkB,IAA0B,EAAE,YAAoB;IAChF,IAAI,SAAS,WAAW;QACtB,OAAO,cAAc,4BAA4B;IACnD,OAAO,IAAI,SAAS,UAAU;QAC5B,OAAO,cAAc,2BAA2B;IAClD;IACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC,EAAE,cAAc;AACpE;AAEO,SAAS,gBAAgB,IAA0B,EAAE,OAAY;IACtE,MAAM,SAAmB,EAAE;IAC3B,MAAM,aAAa,SAAS,YAAY,iBAAiB,OAAO,GAAG,iBAAiB,MAAM;IAE1F,wBAAwB;IACxB,KAAK,MAAM,SAAS,WAAW,cAAc,CAAE;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;QAChD;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,QAAQ,IAAI,CAAC,WAAW,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG;QACjF,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,QAAQ,EAAE;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,UAAU;QACrB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,iBAAiB,MAAM,CAAC,kBAAkB,EAAE;YAC9F,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAClG;IACF;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAEO,SAAS,wBAAwB,MAAc;IACpD,OAAO,cAAc,UAAU,CAAC,OAAgD;AAClF;AAEO,SAAS,YAAY,MAAc,EAAE,KAAa;IACvD,MAAM,SAAS,cAAc,MAAM;IAEnC,OAAQ;QACN,KAAK;YACH,OAAO,QAAQ,OAAO,kBAAkB;QAC1C,KAAK;YACH,OAAO,QAAQ,OAAO,eAAe;QACvC,KAAK;YACH,OAAO,QAAQ,OAAO,cAAc;QACtC,KAAK;YACH,OAAO,QAAQ,OAAO,aAAa;QACrC;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/rss-feed-service.ts"], "sourcesContent": ["/**\r\n * RSS Feed Service for Trending Content & Social Media Insights\r\n * Fetches and parses RSS feeds to extract trending topics, keywords, and themes\r\n */\r\n\r\nimport { parseStringPromise } from 'xml2js';\r\n\r\nexport interface RSSArticle {\r\n  title: string;\r\n  description: string;\r\n  link: string;\r\n  pubDate: Date;\r\n  category?: string;\r\n  keywords: string[];\r\n  source: string;\r\n}\r\n\r\nexport interface TrendingData {\r\n  keywords: string[];\r\n  topics: string[];\r\n  themes: string[];\r\n  articles: RSSArticle[];\r\n  lastUpdated: Date;\r\n}\r\n\r\nexport class RSSFeedService {\r\n  private cache: Map<string, { data: RSSArticle[]; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000; // 30 minutes default\r\n\r\n  private readonly feedUrls = {\r\n    // Social Media & Marketing Trends\r\n    socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,\r\n    socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,\r\n    bufferBlog: process.env.RSS_BUFFER_BLOG,\r\n    hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,\r\n    sproutSocial: process.env.RSS_SPROUT_SOCIAL,\r\n    laterBlog: process.env.RSS_LATER_BLOG,\r\n\r\n    // Trending Topics & News\r\n    googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,\r\n    redditPopular: process.env.RSS_REDDIT_POPULAR,\r\n    buzzfeed: process.env.RSS_BUZZFEED,\r\n    twitterTrending: process.env.RSS_TWITTER_TRENDING,\r\n\r\n    // Business & Marketing\r\n    hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,\r\n    contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,\r\n    marketingProfs: process.env.RSS_MARKETING_PROFS,\r\n    marketingLand: process.env.RSS_MARKETING_LAND,\r\n    neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,\r\n\r\n    // Industry News\r\n    techCrunch: process.env.RSS_TECHCRUNCH,\r\n    mashable: process.env.RSS_MASHABLE,\r\n    theVerge: process.env.RSS_THE_VERGE,\r\n    wired: process.env.RSS_WIRED,\r\n\r\n    // Platform-Specific\r\n    instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,\r\n    facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,\r\n    linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,\r\n    youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,\r\n    tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,\r\n\r\n    // Analytics & Data\r\n    googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,\r\n    hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,\r\n\r\n    // Design & Creative\r\n    canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,\r\n    adobeBlog: process.env.RSS_ADOBE_BLOG,\r\n    creativeBloq: process.env.RSS_CREATIVE_BLOQ,\r\n\r\n    // Seasonal & Events\r\n    eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG,\r\n  };\r\n\r\n  /**\r\n   * Fetch and parse a single RSS feed\r\n   */\r\n  private async fetchRSSFeed(url: string, sourceName: string): Promise<RSSArticle[]> {\r\n    try {\r\n      // Check cache first\r\n      const cached = this.cache.get(url);\r\n      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n        return cached.data;\r\n      }\r\n\r\n\r\n      const response = await fetch(url, {\r\n        headers: {\r\n          'User-Agent': 'Nevis-AI-Content-Generator/1.0',\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n      }\r\n\r\n      const xmlData = await response.text();\r\n      const parsed = await parseStringPromise(xmlData);\r\n\r\n      const articles: RSSArticle[] = [];\r\n      const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];\r\n\r\n      const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');\r\n\r\n      for (const item of items.slice(0, maxArticles)) {\r\n        const article: RSSArticle = {\r\n          title: this.extractText(item.title),\r\n          description: this.extractText(item.description || item.summary),\r\n          link: this.extractText(item.link || item.id),\r\n          pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),\r\n          category: this.extractText(item.category),\r\n          keywords: this.extractKeywords(\r\n            this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)\r\n          ),\r\n          source: sourceName,\r\n        };\r\n\r\n        articles.push(article);\r\n      }\r\n\r\n      // Cache the results\r\n      this.cache.set(url, { data: articles, timestamp: Date.now() });\r\n\r\n      return articles;\r\n\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract text content from RSS item fields\r\n   */\r\n  private extractText(field: any): string {\r\n    if (!field) return '';\r\n\r\n    if (typeof field === 'string') return field;\r\n    if (Array.isArray(field) && field.length > 0) {\r\n      return typeof field[0] === 'string' ? field[0] : field[0]._ || '';\r\n    }\r\n    if (typeof field === 'object' && field._) return field._;\r\n\r\n    return '';\r\n  }\r\n\r\n  /**\r\n   * Extract keywords from text content\r\n   */\r\n  private extractKeywords(text: string): string[] {\r\n    if (!text) return [];\r\n\r\n    // Remove HTML tags and normalize text\r\n    const cleanText = text\r\n      .replace(/<[^>]*>/g, '')\r\n      .toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .replace(/\\s+/g, ' ')\r\n      .trim();\r\n\r\n    // Extract meaningful words (3+ characters, not common stop words)\r\n    const stopWords = new Set([\r\n      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'\r\n    ]);\r\n\r\n    const words = cleanText\r\n      .split(' ')\r\n      .filter(word => word.length >= 3 && !stopWords.has(word))\r\n      .slice(0, 10); // Limit to top 10 keywords per article\r\n\r\n    return Array.from(new Set(words)); // Remove duplicates\r\n  }\r\n\r\n  /**\r\n   * Fetch all RSS feeds and return trending data\r\n   */\r\n  public async getTrendingData(): Promise<TrendingData> {\r\n\r\n    const allArticles: RSSArticle[] = [];\r\n    const fetchPromises: Promise<RSSArticle[]>[] = [];\r\n\r\n    // Fetch all feeds concurrently\r\n    for (const [sourceName, url] of Object.entries(this.feedUrls)) {\r\n      if (url) {\r\n        fetchPromises.push(this.fetchRSSFeed(url, sourceName));\r\n      }\r\n    }\r\n\r\n    const results = await Promise.allSettled(fetchPromises);\r\n\r\n    // Collect all successful results\r\n    results.forEach((result) => {\r\n      if (result.status === 'fulfilled') {\r\n        allArticles.push(...result.value);\r\n      }\r\n    });\r\n\r\n    // Sort articles by publication date (newest first)\r\n    allArticles.sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());\r\n\r\n    // Extract trending keywords and topics\r\n    const allKeywords: string[] = [];\r\n    const allTopics: string[] = [];\r\n    const allThemes: string[] = [];\r\n\r\n    allArticles.forEach(article => {\r\n      allKeywords.push(...article.keywords);\r\n      if (article.title) allTopics.push(article.title);\r\n      if (article.category) allThemes.push(article.category);\r\n    });\r\n\r\n    // Count frequency and get top items\r\n    const keywordCounts = this.getTopItems(allKeywords, 50);\r\n    const topicCounts = this.getTopItems(allTopics, 30);\r\n    const themeCounts = this.getTopItems(allThemes, 20);\r\n\r\n\r\n    return {\r\n      keywords: keywordCounts,\r\n      topics: topicCounts,\r\n      themes: themeCounts,\r\n      articles: allArticles.slice(0, 100), // Return top 100 most recent articles\r\n      lastUpdated: new Date(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get top items by frequency\r\n   */\r\n  private getTopItems(items: string[], limit: number): string[] {\r\n    const counts = new Map<string, number>();\r\n\r\n    items.forEach(item => {\r\n      const normalized = item.toLowerCase().trim();\r\n      if (normalized.length >= 3) {\r\n        counts.set(normalized, (counts.get(normalized) || 0) + 1);\r\n      }\r\n    });\r\n\r\n    return Array.from(counts.entries())\r\n      .sort((a, b) => b[1] - a[1])\r\n      .slice(0, limit)\r\n      .map(([item]) => item);\r\n  }\r\n\r\n  /**\r\n   * Get trending keywords for a specific category\r\n   */\r\n  public async getTrendingKeywordsByCategory(category: 'social' | 'business' | 'tech' | 'design'): Promise<string[]> {\r\n    const trendingData = await this.getTrendingData();\r\n\r\n    const categoryFeeds = {\r\n      social: ['socialMediaToday', 'socialMediaExaminer', 'bufferBlog', 'hootsuiteBlogs'],\r\n      business: ['hubspotMarketing', 'contentMarketingInstitute', 'marketingProfs'],\r\n      tech: ['techCrunch', 'theVerge', 'wired'],\r\n      design: ['canvaDesignSchool', 'adobeBlog', 'creativeBloq'],\r\n    };\r\n\r\n    const categoryArticles = trendingData.articles.filter(article =>\r\n      categoryFeeds[category].includes(article.source)\r\n    );\r\n\r\n    const keywords: string[] = [];\r\n    categoryArticles.forEach(article => keywords.push(...article.keywords));\r\n\r\n    return this.getTopItems(keywords, 20);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const rssService = new RSSFeedService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAoBO,MAAM;IACH,QAAgE,IAAI,MAAM;IACjE,eAAe,SAAS,QAAQ,GAAG,CAAC,kBAAkB,IAAI,UAAU,KAAK;IAEzE,WAAW;QAC1B,kCAAkC;QAClC,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB;QACpD,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;QAC1D,YAAY,QAAQ,GAAG,CAAC,eAAe;QACvC,gBAAgB,QAAQ,GAAG,CAAC,kBAAkB;QAC9C,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAC3C,WAAW,QAAQ,GAAG,CAAC,cAAc;QAErC,yBAAyB;QACzB,oBAAoB,QAAQ,GAAG,CAAC,wBAAwB;QACxD,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QAEjD,uBAAuB;QACvB,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,2BAA2B,QAAQ,GAAG,CAAC,+BAA+B;QACtE,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,eAAe,QAAQ,GAAG,CAAC,mBAAmB;QAE9C,gBAAgB;QAChB,YAAY,QAAQ,GAAG,CAAC,cAAc;QACtC,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,UAAU,QAAQ,GAAG,CAAC,aAAa;QACnC,OAAO,QAAQ,GAAG,CAAC,SAAS;QAE5B,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAE/C,mBAAmB;QACnB,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QACjD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QAErD,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB;QACtD,WAAW,QAAQ,GAAG,CAAC,cAAc;QACrC,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAE3C,oBAAoB;QACpB,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;IACjD,EAAE;IAEF;;GAEC,GACD,MAAc,aAAa,GAAW,EAAE,UAAkB,EAAyB;QACjF,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC/D,OAAO,OAAO,IAAI;YACpB;YAGA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,MAAM,WAAyB,EAAE;YACjC,MAAM,QAAQ,OAAO,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS,EAAE;YAExE,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI;YAEtE,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,aAAc;gBAC9C,MAAM,UAAsB;oBAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;oBAClC,aAAa,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAC9D,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE;oBAC3C,SAAS,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK,KAAK,GAAG;oBAC9E,UAAU,IAAI,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACxC,UAAU,IAAI,CAAC,eAAe,CAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAExF,QAAQ;gBACV;gBAEA,SAAS,IAAI,CAAC;YAChB;YAEA,oBAAoB;YACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,MAAM;gBAAU,WAAW,KAAK,GAAG;YAAG;YAE5D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAU,EAAU;QACtC,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,OAAO,UAAU,UAAU,OAAO;QACtC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;YAC5C,OAAO,OAAO,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI;QACjE;QACA,IAAI,OAAO,UAAU,YAAY,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC;QAExD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBAAgB,IAAY,EAAY;QAC9C,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,sCAAsC;QACtC,MAAM,YAAY,KACf,OAAO,CAAC,YAAY,IACpB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,kEAAkE;QAClE,MAAM,YAAY,IAAI,IAAI;YACxB;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;SACvP;QAED,MAAM,QAAQ,UACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,OAClD,KAAK,CAAC,GAAG,KAAK,uCAAuC;QAExD,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,oBAAoB;IACzD;IAEA;;GAEC,GACD,MAAa,kBAAyC;QAEpD,MAAM,cAA4B,EAAE;QACpC,MAAM,gBAAyC,EAAE;QAEjD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,YAAY,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAG;YAC7D,IAAI,KAAK;gBACP,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;YAC5C;QACF;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;QAEzC,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,YAAY,IAAI,IAAI,OAAO,KAAK;YAClC;QACF;QAEA,mDAAmD;QACnD,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO,KAAK,EAAE,OAAO,CAAC,OAAO;QAElE,uCAAuC;QACvC,MAAM,cAAwB,EAAE;QAChC,MAAM,YAAsB,EAAE;QAC9B,MAAM,YAAsB,EAAE;QAE9B,YAAY,OAAO,CAAC,CAAA;YAClB,YAAY,IAAI,IAAI,QAAQ,QAAQ;YACpC,IAAI,QAAQ,KAAK,EAAE,UAAU,IAAI,CAAC,QAAQ,KAAK;YAC/C,IAAI,QAAQ,QAAQ,EAAE,UAAU,IAAI,CAAC,QAAQ,QAAQ;QACvD;QAEA,oCAAoC;QACpC,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;QACpD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAChD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAGhD,OAAO;YACL,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,UAAU,YAAY,KAAK,CAAC,GAAG;YAC/B,aAAa,IAAI;QACnB;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAe,EAAE,KAAa,EAAY;QAC5D,MAAM,SAAS,IAAI;QAEnB,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,aAAa,KAAK,WAAW,GAAG,IAAI;YAC1C,IAAI,WAAW,MAAM,IAAI,GAAG;gBAC1B,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI;YACzD;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,OAAO,OAAO,IAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IACrB;IAEA;;GAEC,GACD,MAAa,8BAA8B,QAAmD,EAAqB;QACjH,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;QAE/C,MAAM,gBAAgB;YACpB,QAAQ;gBAAC;gBAAoB;gBAAuB;gBAAc;aAAiB;YACnF,UAAU;gBAAC;gBAAoB;gBAA6B;aAAiB;YAC7E,MAAM;gBAAC;gBAAc;gBAAY;aAAQ;YACzC,QAAQ;gBAAC;gBAAqB;gBAAa;aAAe;QAC5D;QAEA,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UACpD,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAGjD,MAAM,WAAqB,EAAE;QAC7B,iBAAiB,OAAO,CAAC,CAAA,UAAW,SAAS,IAAI,IAAI,QAAQ,QAAQ;QAErE,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;IACpC;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/trending-content-enhancer.ts"], "sourcesContent": ["/**\r\n * Trending Content Enhancer\r\n * Integrates RSS feed data to enhance content generation with trending topics\r\n */\r\n\r\nimport { rssService, TrendingData } from '../services/rss-feed-service';\r\n\r\nexport interface TrendingEnhancement {\r\n  keywords: string[];\r\n  topics: string[];\r\n  hashtags: string[];\r\n  seasonalThemes: string[];\r\n  industryBuzz: string[];\r\n}\r\n\r\nexport interface ContentContext {\r\n  businessType?: string;\r\n  platform?: string;\r\n  location?: string;\r\n  targetAudience?: string;\r\n}\r\n\r\nexport class TrendingContentEnhancer {\r\n  private trendingCache: TrendingData | null = null;\r\n  private lastCacheUpdate: number = 0;\r\n  private readonly cacheTimeout = 30 * 60 * 1000; // 30 minutes\r\n\r\n  /**\r\n   * Get fresh trending data with caching\r\n   */\r\n  private async getTrendingData(): Promise<TrendingData> {\r\n    const now = Date.now();\r\n\r\n    if (this.trendingCache && (now - this.lastCacheUpdate) < this.cacheTimeout) {\r\n      return this.trendingCache;\r\n    }\r\n\r\n    this.trendingCache = await rssService.getTrendingData();\r\n    this.lastCacheUpdate = now;\r\n\r\n    return this.trendingCache;\r\n  }\r\n\r\n  /**\r\n   * Get trending enhancement data for content generation\r\n   */\r\n  public async getTrendingEnhancement(context: ContentContext = {}): Promise<TrendingEnhancement> {\r\n    try {\r\n      const trendingData = await this.getTrendingData();\r\n\r\n      // Filter and prioritize based on context\r\n      const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);\r\n      const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);\r\n\r\n      // Generate hashtags from trending keywords\r\n      const hashtags = this.generateHashtags(relevantKeywords, context);\r\n\r\n      // Extract seasonal themes\r\n      const seasonalThemes = this.extractSeasonalThemes(trendingData);\r\n\r\n      // Extract industry-specific buzz\r\n      const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);\r\n\r\n\r\n      return {\r\n        keywords: relevantKeywords.slice(0, 15),\r\n        topics: relevantTopics.slice(0, 10),\r\n        hashtags: hashtags.slice(0, 10),\r\n        seasonalThemes: seasonalThemes.slice(0, 5),\r\n        industryBuzz: industryBuzz.slice(0, 8),\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Return fallback data\r\n      return {\r\n        keywords: ['trending', 'viral', 'popular', 'latest', 'new'],\r\n        topics: ['social media trends', 'digital marketing', 'content creation'],\r\n        hashtags: ['#trending', '#viral', '#socialmedia', '#marketing'],\r\n        seasonalThemes: [],\r\n        industryBuzz: [],\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Filter keywords based on context relevance\r\n   */\r\n  private filterKeywordsByContext(keywords: string[], context: ContentContext): string[] {\r\n    const platformKeywords = {\r\n      instagram: ['visual', 'photo', 'story', 'reel', 'aesthetic', 'lifestyle'],\r\n      facebook: ['community', 'share', 'connect', 'family', 'local', 'event'],\r\n      twitter: ['news', 'update', 'breaking', 'discussion', 'opinion', 'thread'],\r\n      linkedin: ['professional', 'business', 'career', 'industry', 'networking', 'leadership'],\r\n      tiktok: ['viral', 'trend', 'challenge', 'creative', 'fun', 'entertainment'],\r\n      pinterest: ['inspiration', 'ideas', 'diy', 'design', 'home', 'style'],\r\n    };\r\n\r\n    const businessKeywords = {\r\n      restaurant: ['food', 'dining', 'menu', 'chef', 'cuisine', 'taste', 'fresh'],\r\n      retail: ['shopping', 'sale', 'fashion', 'style', 'product', 'deal', 'new'],\r\n      fitness: ['health', 'workout', 'training', 'wellness', 'strength', 'motivation'],\r\n      beauty: ['skincare', 'makeup', 'beauty', 'glow', 'treatment', 'style'],\r\n      tech: ['innovation', 'digital', 'technology', 'software', 'app', 'solution'],\r\n      healthcare: ['health', 'wellness', 'care', 'treatment', 'medical', 'patient'],\r\n    };\r\n\r\n    let filtered = [...keywords];\r\n\r\n    // Boost platform-relevant keywords\r\n    if (context.platform && platformKeywords[context.platform as keyof typeof platformKeywords]) {\r\n      const platformBoost = platformKeywords[context.platform as keyof typeof platformKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = platformBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = platformBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    // Boost business-relevant keywords\r\n    if (context.businessType && businessKeywords[context.businessType as keyof typeof businessKeywords]) {\r\n      const businessBoost = businessKeywords[context.businessType as keyof typeof businessKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = businessBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = businessBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Filter topics based on context relevance\r\n   */\r\n  private filterTopicsByContext(topics: string[], context: ContentContext): string[] {\r\n    // Remove topics that are too generic or not suitable for social media\r\n    const filtered = topics.filter(topic => {\r\n      const lower = topic.toLowerCase();\r\n      return !lower.includes('error') &&\r\n        !lower.includes('404') &&\r\n        !lower.includes('page not found') &&\r\n        lower.length > 10 &&\r\n        lower.length < 100;\r\n    });\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Generate relevant hashtags from keywords\r\n   */\r\n  private generateHashtags(keywords: string[], context: ContentContext): string[] {\r\n    const hashtags: string[] = [];\r\n\r\n    // Convert keywords to hashtags\r\n    keywords.forEach(keyword => {\r\n      const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\r\n      if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {\r\n        hashtags.push(`#${cleanKeyword}`);\r\n      }\r\n    });\r\n\r\n    // Add platform-specific hashtags\r\n    const platformHashtags = {\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#picoftheday'],\r\n      facebook: ['#community', '#local', '#share', '#connect'],\r\n      twitter: ['#news', '#update', '#discussion', '#trending'],\r\n      linkedin: ['#professional', '#business', '#career', '#networking'],\r\n      tiktok: ['#fyp', '#viral', '#trending', '#foryou'],\r\n      pinterest: ['#inspiration', '#ideas', '#diy', '#style'],\r\n    };\r\n\r\n    if (context.platform && platformHashtags[context.platform as keyof typeof platformHashtags]) {\r\n      hashtags.push(...platformHashtags[context.platform as keyof typeof platformHashtags]);\r\n    }\r\n\r\n    // Remove duplicates and return\r\n    return Array.from(new Set(hashtags));\r\n  }\r\n\r\n  /**\r\n   * Extract seasonal themes from trending data\r\n   */\r\n  private extractSeasonalThemes(trendingData: TrendingData): string[] {\r\n    const currentMonth = new Date().getMonth();\r\n    const seasonalKeywords = {\r\n      0: ['new year', 'resolution', 'fresh start', 'winter'], // January\r\n      1: ['valentine', 'love', 'romance', 'winter'], // February\r\n      2: ['spring', 'march madness', 'renewal', 'growth'], // March\r\n      3: ['easter', 'spring', 'bloom', 'fresh'], // April\r\n      4: ['mother\\'s day', 'spring', 'flowers', 'celebration'], // May\r\n      5: ['summer', 'graduation', 'father\\'s day', 'vacation'], // June\r\n      6: ['summer', 'july 4th', 'independence', 'freedom'], // July\r\n      7: ['summer', 'vacation', 'back to school', 'preparation'], // August\r\n      8: ['back to school', 'fall', 'autumn', 'harvest'], // September\r\n      9: ['halloween', 'october', 'spooky', 'fall'], // October\r\n      10: ['thanksgiving', 'gratitude', 'family', 'harvest'], // November\r\n      11: ['christmas', 'holiday', 'winter', 'celebration'], // December\r\n    };\r\n\r\n    const currentSeasonalKeywords = seasonalKeywords[currentMonth as keyof typeof seasonalKeywords] || [];\r\n\r\n    const seasonalThemes = trendingData.keywords.filter(keyword =>\r\n      currentSeasonalKeywords.some(seasonal =>\r\n        keyword.toLowerCase().includes(seasonal.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return seasonalThemes;\r\n  }\r\n\r\n  /**\r\n   * Extract industry-specific buzz from trending data\r\n   */\r\n  private extractIndustryBuzz(trendingData: TrendingData, businessType?: string): string[] {\r\n    if (!businessType) return [];\r\n\r\n    const industryKeywords = {\r\n      restaurant: ['food', 'dining', 'chef', 'cuisine', 'recipe', 'restaurant', 'menu'],\r\n      retail: ['shopping', 'fashion', 'style', 'product', 'brand', 'sale', 'deal'],\r\n      fitness: ['fitness', 'workout', 'health', 'gym', 'training', 'wellness', 'exercise'],\r\n      beauty: ['beauty', 'skincare', 'makeup', 'cosmetics', 'treatment', 'spa'],\r\n      tech: ['technology', 'tech', 'digital', 'software', 'app', 'innovation', 'ai'],\r\n      healthcare: ['health', 'medical', 'healthcare', 'wellness', 'treatment', 'care'],\r\n    };\r\n\r\n    const relevantKeywords = industryKeywords[businessType as keyof typeof industryKeywords] || [];\r\n\r\n    const industryBuzz = trendingData.keywords.filter(keyword =>\r\n      relevantKeywords.some(industry =>\r\n        keyword.toLowerCase().includes(industry.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return industryBuzz;\r\n  }\r\n\r\n  /**\r\n   * Get trending prompt enhancement for AI content generation\r\n   */\r\n  public async getTrendingPromptEnhancement(context: ContentContext = {}): Promise<string> {\r\n    const enhancement = await this.getTrendingEnhancement(context);\r\n\r\n    const promptParts: string[] = [];\r\n\r\n    if (enhancement.keywords.length > 0) {\r\n      promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.seasonalThemes.length > 0) {\r\n      promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.industryBuzz.length > 0) {\r\n      promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.hashtags.length > 0) {\r\n      promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);\r\n    }\r\n\r\n    return promptParts.join('\\n');\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const trendingEnhancer = new TrendingContentEnhancer();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAiBO,MAAM;IACH,gBAAqC,KAAK;IAC1C,kBAA0B,EAAE;IACnB,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAc,kBAAyC;QACrD,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,IAAI,CAAC,aAAa,IAAI,AAAC,MAAM,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,YAAY,EAAE;YAC1E,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,IAAI,CAAC,aAAa,GAAG,MAAM,2IAAA,CAAA,aAAU,CAAC,eAAe;QACrD,IAAI,CAAC,eAAe,GAAG;QAEvB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA;;GAEC,GACD,MAAa,uBAAuB,UAA0B,CAAC,CAAC,EAAgC;QAC9F,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;YAE/C,yCAAyC;YACzC,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC,aAAa,QAAQ,EAAE;YAC7E,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,aAAa,MAAM,EAAE;YAEvE,2CAA2C;YAC3C,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;YAEzD,0BAA0B;YAC1B,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAElD,iCAAiC;YACjC,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,cAAc,QAAQ,YAAY;YAGhF,OAAO;gBACL,UAAU,iBAAiB,KAAK,CAAC,GAAG;gBACpC,QAAQ,eAAe,KAAK,CAAC,GAAG;gBAChC,UAAU,SAAS,KAAK,CAAC,GAAG;gBAC5B,gBAAgB,eAAe,KAAK,CAAC,GAAG;gBACxC,cAAc,aAAa,KAAK,CAAC,GAAG;YACtC;QAEF,EAAE,OAAO,OAAO;YAEd,uBAAuB;YACvB,OAAO;gBACL,UAAU;oBAAC;oBAAY;oBAAS;oBAAW;oBAAU;iBAAM;gBAC3D,QAAQ;oBAAC;oBAAuB;oBAAqB;iBAAmB;gBACxE,UAAU;oBAAC;oBAAa;oBAAU;oBAAgB;iBAAa;gBAC/D,gBAAgB,EAAE;gBAClB,cAAc,EAAE;YAClB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,wBAAwB,QAAkB,EAAE,OAAuB,EAAY;QACrF,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAU;gBAAS;gBAAS;gBAAQ;gBAAa;aAAY;YACzE,UAAU;gBAAC;gBAAa;gBAAS;gBAAW;gBAAU;gBAAS;aAAQ;YACvE,SAAS;gBAAC;gBAAQ;gBAAU;gBAAY;gBAAc;gBAAW;aAAS;YAC1E,UAAU;gBAAC;gBAAgB;gBAAY;gBAAU;gBAAY;gBAAc;aAAa;YACxF,QAAQ;gBAAC;gBAAS;gBAAS;gBAAa;gBAAY;gBAAO;aAAgB;YAC3E,WAAW;gBAAC;gBAAe;gBAAS;gBAAO;gBAAU;gBAAQ;aAAQ;QACvE;QAEA,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAQ;gBAAW;gBAAS;aAAQ;YAC3E,QAAQ;gBAAC;gBAAY;gBAAQ;gBAAW;gBAAS;gBAAW;gBAAQ;aAAM;YAC1E,SAAS;gBAAC;gBAAU;gBAAW;gBAAY;gBAAY;gBAAY;aAAa;YAChF,QAAQ;gBAAC;gBAAY;gBAAU;gBAAU;gBAAQ;gBAAa;aAAQ;YACtE,MAAM;gBAAC;gBAAc;gBAAW;gBAAc;gBAAY;gBAAO;aAAW;YAC5E,YAAY;gBAAC;gBAAU;gBAAY;gBAAQ;gBAAa;gBAAW;aAAU;QAC/E;QAEA,IAAI,WAAW;eAAI;SAAS;QAE5B,mCAAmC;QACnC,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;YACzF,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,mCAAmC;QACnC,IAAI,QAAQ,YAAY,IAAI,gBAAgB,CAAC,QAAQ,YAAY,CAAkC,EAAE;YACnG,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,YAAY,CAAkC;YAC7F,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAgB,EAAE,OAAuB,EAAY;QACjF,sEAAsE;QACtE,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA;YAC7B,MAAM,QAAQ,MAAM,WAAW;YAC/B,OAAO,CAAC,MAAM,QAAQ,CAAC,YACrB,CAAC,MAAM,QAAQ,CAAC,UAChB,CAAC,MAAM,QAAQ,CAAC,qBAChB,MAAM,MAAM,GAAG,MACf,MAAM,MAAM,GAAG;QACnB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAkB,EAAE,OAAuB,EAAY;QAC9E,MAAM,WAAqB,EAAE;QAE7B,+BAA+B;QAC/B,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,eAAe,QAAQ,OAAO,CAAC,iBAAiB,IAAI,WAAW;YACrE,IAAI,aAAa,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI;gBACzD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;YAClC;QACF;QAEA,iCAAiC;QACjC,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;aAAe;YAC1E,UAAU;gBAAC;gBAAc;gBAAU;gBAAU;aAAW;YACxD,SAAS;gBAAC;gBAAS;gBAAW;gBAAe;aAAY;YACzD,UAAU;gBAAC;gBAAiB;gBAAa;gBAAW;aAAc;YAClE,QAAQ;gBAAC;gBAAQ;gBAAU;gBAAa;aAAU;YAClD,WAAW;gBAAC;gBAAgB;gBAAU;gBAAQ;aAAS;QACzD;QAEA,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,SAAS,IAAI,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;QACtF;QAEA,+BAA+B;QAC/B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;IAC5B;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAA0B,EAAY;QAClE,MAAM,eAAe,IAAI,OAAO,QAAQ;QACxC,MAAM,mBAAmB;YACvB,GAAG;gBAAC;gBAAY;gBAAc;gBAAe;aAAS;YACtD,GAAG;gBAAC;gBAAa;gBAAQ;gBAAW;aAAS;YAC7C,GAAG;gBAAC;gBAAU;gBAAiB;gBAAW;aAAS;YACnD,GAAG;gBAAC;gBAAU;gBAAU;gBAAS;aAAQ;YACzC,GAAG;gBAAC;gBAAiB;gBAAU;gBAAW;aAAc;YACxD,GAAG;gBAAC;gBAAU;gBAAc;gBAAiB;aAAW;YACxD,GAAG;gBAAC;gBAAU;gBAAY;gBAAgB;aAAU;YACpD,GAAG;gBAAC;gBAAU;gBAAY;gBAAkB;aAAc;YAC1D,GAAG;gBAAC;gBAAkB;gBAAQ;gBAAU;aAAU;YAClD,GAAG;gBAAC;gBAAa;gBAAW;gBAAU;aAAO;YAC7C,IAAI;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;YACtD,IAAI;gBAAC;gBAAa;gBAAW;gBAAU;aAAc;QACvD;QAEA,MAAM,0BAA0B,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAErG,MAAM,iBAAiB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAClD,wBAAwB,IAAI,CAAC,CAAA,WAC3B,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAA0B,EAAE,YAAqB,EAAY;QACvF,IAAI,CAAC,cAAc,OAAO,EAAE;QAE5B,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAW;gBAAU;gBAAc;aAAO;YACjF,QAAQ;gBAAC;gBAAY;gBAAW;gBAAS;gBAAW;gBAAS;gBAAQ;aAAO;YAC5E,SAAS;gBAAC;gBAAW;gBAAW;gBAAU;gBAAO;gBAAY;gBAAY;aAAW;YACpF,QAAQ;gBAAC;gBAAU;gBAAY;gBAAU;gBAAa;gBAAa;aAAM;YACzE,MAAM;gBAAC;gBAAc;gBAAQ;gBAAW;gBAAY;gBAAO;gBAAc;aAAK;YAC9E,YAAY;gBAAC;gBAAU;gBAAW;gBAAc;gBAAY;gBAAa;aAAO;QAClF;QAEA,MAAM,mBAAmB,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAE9F,MAAM,eAAe,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAChD,iBAAiB,IAAI,CAAC,CAAA,WACpB,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,MAAa,6BAA6B,UAA0B,CAAC,CAAC,EAAmB;QACvF,MAAM,cAAc,MAAM,IAAI,CAAC,sBAAsB,CAAC;QAEtD,MAAM,cAAwB,EAAE;QAEhC,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,+BAA+B,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QAClG;QAEA,IAAI,YAAY,cAAc,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY,IAAI,CAAC,CAAC,yBAAyB,EAAE,YAAY,cAAc,CAAC,IAAI,CAAC,OAAO;QACtF;QAEA,IAAI,YAAY,YAAY,CAAC,MAAM,GAAG,GAAG;YACvC,YAAY,IAAI,CAAC,CAAC,0BAA0B,EAAE,YAAY,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACjG;QAEA,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,oBAAoB,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;QACtF;QAEA,OAAO,YAAY,IAAI,CAAC;IAC1B;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/regional-communication-engine.ts"], "sourcesContent": ["/**\r\n * Regional Communication Engine\r\n * Deep understanding of how people actually communicate, advertise, and connect in different regions\r\n */\r\n\r\nexport interface RegionalProfile {\r\n  region: string;\r\n  country: string;\r\n  communicationStyle: CommunicationStyle;\r\n  advertisingPatterns: AdvertisingPattern[];\r\n  localSlang: LocalSlang;\r\n  culturalNuances: CulturalNuance[];\r\n  businessCommunication: BusinessCommunication;\r\n  socialMediaBehavior: SocialMediaBehavior;\r\n}\r\n\r\nexport interface CommunicationStyle {\r\n  directness: 'very_direct' | 'direct' | 'indirect' | 'very_indirect';\r\n  formality: 'very_formal' | 'formal' | 'casual' | 'very_casual';\r\n  emotionalExpression: 'high' | 'medium' | 'low';\r\n  humorStyle: string[];\r\n  persuasionTactics: string[];\r\n  attentionGrabbers: string[];\r\n}\r\n\r\nexport interface AdvertisingPattern {\r\n  type: string;\r\n  approach: string;\r\n  examples: string[];\r\n  effectiveness: 'high' | 'medium' | 'low';\r\n  platforms: string[];\r\n}\r\n\r\nexport interface LocalSlang {\r\n  greetings: string[];\r\n  excitement: string[];\r\n  approval: string[];\r\n  emphasis: string[];\r\n  callToAction: string[];\r\n  endingPhrases: string[];\r\n}\r\n\r\nexport interface CulturalNuance {\r\n  aspect: string;\r\n  importance: 'critical' | 'important' | 'moderate';\r\n  description: string;\r\n  doAndDonts: {\r\n    do: string[];\r\n    dont: string[];\r\n  };\r\n}\r\n\r\nexport interface BusinessCommunication {\r\n  trustBuilders: string[];\r\n  valuePropositions: string[];\r\n  communityConnection: string[];\r\n  localReferences: string[];\r\n}\r\n\r\nexport interface SocialMediaBehavior {\r\n  preferredPlatforms: string[];\r\n  contentPreferences: string[];\r\n  engagementStyle: string;\r\n  hashtagUsage: string;\r\n  visualPreferences: string[];\r\n}\r\n\r\nexport class RegionalCommunicationEngine {\r\n  private regionalProfiles: Map<string, RegionalProfile> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeRegionalProfiles();\r\n  }\r\n\r\n  private initializeRegionalProfiles() {\r\n    // KENYA - Nairobi and surrounding areas\r\n    this.regionalProfiles.set('kenya', {\r\n      region: 'Kenya',\r\n      country: 'Kenya',\r\n      communicationStyle: {\r\n        directness: 'direct',\r\n        formality: 'casual',\r\n        emotionalExpression: 'high',\r\n        humorStyle: ['witty', 'playful', 'community-based', 'storytelling'],\r\n        persuasionTactics: ['community benefit', 'family value', 'quality emphasis', 'local pride'],\r\n        attentionGrabbers: ['Eh!', 'Sawa sawa!', 'Mambo!', 'Poa!', 'Uko ready?']\r\n      },\r\n      advertisingPatterns: [\r\n        {\r\n          type: 'Community-Centered',\r\n          approach: 'Emphasize how the business serves the local community',\r\n          examples: [\r\n            'Serving our Nairobi family with love',\r\n            'Your neighborhood spot for authentic taste',\r\n            'Where Kenyans come together'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['facebook', 'whatsapp', 'instagram']\r\n        },\r\n        {\r\n          type: 'Quality & Freshness',\r\n          approach: 'Highlight freshness, quality, and authentic preparation',\r\n          examples: [\r\n            'Fresh from the kitchen to your table',\r\n            'Made with love, served with pride',\r\n            'Authentic taste that reminds you of home'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['instagram', 'facebook']\r\n        },\r\n        {\r\n          type: 'Swahili Integration',\r\n          approach: 'Natural mix of English and Swahili that feels authentic',\r\n          examples: [\r\n            'Chakula kizuri, bei nzuri!',\r\n            'Karibu for the best experience',\r\n            'Tupo hapa for you always'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['all']\r\n        }\r\n      ],\r\n      localSlang: {\r\n        greetings: ['Mambo!', 'Sasa!', 'Niaje!', 'Poa!', 'Karibu!'],\r\n        excitement: ['Poa kabisa!', 'Sawa sawa!', 'Fiti!', 'Bomba!', 'Noma!'],\r\n        approval: ['Safi!', 'Poa!', 'Nzuri!', 'Fiti kabisa!', 'Bomba sana!'],\r\n        emphasis: ['kabisa', 'sana', 'mzuri', 'noma', 'fiti'],\r\n        callToAction: ['Njoo uone!', 'Karibu!', 'Tupatane!', 'Uko ready?', 'Twende!'],\r\n        endingPhrases: ['Tutaonana!', 'Karibu tena!', 'Asante sana!', 'Mungu akubariki!']\r\n      },\r\n      culturalNuances: [\r\n        {\r\n          aspect: 'Community Connection',\r\n          importance: 'critical',\r\n          description: 'Kenyans value businesses that feel like part of the community',\r\n          doAndDonts: {\r\n            do: ['Reference local landmarks', 'Use \"our community\" language', 'Show family values'],\r\n            dont: ['Sound too corporate', 'Ignore local customs', 'Be overly formal']\r\n          }\r\n        },\r\n        {\r\n          aspect: 'Language Mixing',\r\n          importance: 'important',\r\n          description: 'Natural mixing of English and Swahili is expected and appreciated',\r\n          doAndDonts: {\r\n            do: ['Mix languages naturally', 'Use common Swahili phrases', 'Keep it conversational'],\r\n            dont: ['Force Swahili if unsure', 'Use formal Swahili only', 'Ignore English speakers']\r\n          }\r\n        }\r\n      ],\r\n      businessCommunication: {\r\n        trustBuilders: [\r\n          'Family-owned and operated',\r\n          'Serving the community for [X] years',\r\n          'Made with love by local hands',\r\n          'Your neighbors you can trust'\r\n        ],\r\n        valuePropositions: [\r\n          'Fresh ingredients sourced locally',\r\n          'Authentic recipes passed down generations',\r\n          'Fair prices for quality food',\r\n          'A place where everyone is family'\r\n        ],\r\n        communityConnection: [\r\n          'Part of the Nairobi family',\r\n          'Supporting local farmers and suppliers',\r\n          'Where neighbors become friends',\r\n          'Celebrating our Kenyan heritage'\r\n        ],\r\n        localReferences: [\r\n          'Just off [local road/landmark]',\r\n          'Near [well-known local spot]',\r\n          'In the heart of [neighborhood]',\r\n          'Where locals have been coming for years'\r\n        ]\r\n      },\r\n      socialMediaBehavior: {\r\n        preferredPlatforms: ['WhatsApp', 'Facebook', 'Instagram', 'TikTok'],\r\n        contentPreferences: ['food photos', 'behind-the-scenes', 'customer testimonials', 'community events'],\r\n        engagementStyle: 'High interaction, lots of comments and shares, community-focused',\r\n        hashtagUsage: 'Mix of English and Swahili hashtags, location-based tags',\r\n        visualPreferences: ['bright colors', 'authentic moments', 'people enjoying food', 'local settings']\r\n      }\r\n    });\r\n\r\n    // NIGERIA - Lagos and surrounding areas\r\n    this.regionalProfiles.set('nigeria', {\r\n      region: 'Nigeria',\r\n      country: 'Nigeria',\r\n      communicationStyle: {\r\n        directness: 'direct',\r\n        formality: 'casual',\r\n        emotionalExpression: 'high',\r\n        humorStyle: ['energetic', 'bold', 'confident', 'community-pride'],\r\n        persuasionTactics: ['quality emphasis', 'value for money', 'social status', 'community respect'],\r\n        attentionGrabbers: ['Oya!', 'See this one!', 'No be small thing!', 'This one sweet die!']\r\n      },\r\n      advertisingPatterns: [\r\n        {\r\n          type: 'Bold & Confident',\r\n          approach: 'Strong, confident statements about quality and value',\r\n          examples: [\r\n            'The best in Lagos, no cap!',\r\n            'Quality wey go shock you!',\r\n            'This one na correct business!'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['instagram', 'twitter', 'facebook']\r\n        },\r\n        {\r\n          type: 'Value Emphasis',\r\n          approach: 'Highlight exceptional value and quality for the price',\r\n          examples: [\r\n            'Quality food, affordable price',\r\n            'Where your money get value',\r\n            'Premium taste, pocket-friendly price'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['all']\r\n        }\r\n      ],\r\n      localSlang: {\r\n        greetings: ['How far?', 'Wetin dey happen?', 'Oya!', 'My guy!'],\r\n        excitement: ['E sweet die!', 'This one correct!', 'Na fire!', 'Too much!'],\r\n        approval: ['Correct!', 'Na so!', 'Perfect!', 'E good die!'],\r\n        emphasis: ['die', 'well well', 'proper', 'correct'],\r\n        callToAction: ['Come try am!', 'Oya come!', 'Make you taste am!', 'No waste time!'],\r\n        endingPhrases: ['See you soon!', 'We dey wait for you!', 'Come back again!']\r\n      },\r\n      culturalNuances: [\r\n        {\r\n          aspect: 'Confidence & Quality',\r\n          importance: 'critical',\r\n          description: 'Nigerians appreciate confident, bold statements about quality',\r\n          doAndDonts: {\r\n            do: ['Be confident about your quality', 'Use bold language', 'Emphasize value'],\r\n            dont: ['Be too modest', 'Undersell your quality', 'Sound uncertain']\r\n          }\r\n        }\r\n      ],\r\n      businessCommunication: {\r\n        trustBuilders: [\r\n          'Tested and trusted',\r\n          'Lagos people choice',\r\n          'Quality wey you fit trust',\r\n          'We no dey disappoint'\r\n        ],\r\n        valuePropositions: [\r\n          'Best quality for your money',\r\n          'Fresh ingredients, authentic taste',\r\n          'Where quality meets affordability',\r\n          'Premium service, reasonable price'\r\n        ],\r\n        communityConnection: [\r\n          'Proudly Nigerian',\r\n          'Serving Lagos with pride',\r\n          'Your neighborhood favorite',\r\n          'Where Lagos people gather'\r\n        ],\r\n        localReferences: [\r\n          'For Lagos Island',\r\n          'Victoria Island area',\r\n          'Mainland favorite',\r\n          'Ikeja corridor'\r\n        ]\r\n      },\r\n      socialMediaBehavior: {\r\n        preferredPlatforms: ['Instagram', 'Twitter', 'WhatsApp', 'Facebook'],\r\n        contentPreferences: ['food videos', 'customer reactions', 'quality showcases', 'value demonstrations'],\r\n        engagementStyle: 'High energy, lots of reactions, sharing culture',\r\n        hashtagUsage: 'Mix of English and Pidgin, location tags, trending topics',\r\n        visualPreferences: ['vibrant colors', 'appetizing close-ups', 'happy customers', 'quality focus']\r\n      }\r\n    });\r\n\r\n    // SOUTH AFRICA - Johannesburg/Cape Town\r\n    this.regionalProfiles.set('south_africa', {\r\n      region: 'South Africa',\r\n      country: 'South Africa',\r\n      communicationStyle: {\r\n        directness: 'direct',\r\n        formality: 'casual',\r\n        emotionalExpression: 'medium',\r\n        humorStyle: ['laid-back', 'friendly', 'inclusive', 'warm'],\r\n        persuasionTactics: ['quality focus', 'local pride', 'community value', 'authentic experience'],\r\n        attentionGrabbers: ['Howzit!', 'Check this out!', 'Lekker!', 'Sharp!']\r\n      },\r\n      advertisingPatterns: [\r\n        {\r\n          type: 'Lekker & Local',\r\n          approach: 'Emphasize local flavor and authentic South African experience',\r\n          examples: [\r\n            'Proper lekker food, hey!',\r\n            'Authentic South African taste',\r\n            'Made with love in Mzansi'\r\n          ],\r\n          effectiveness: 'high',\r\n          platforms: ['instagram', 'facebook']\r\n        }\r\n      ],\r\n      localSlang: {\r\n        greetings: ['Howzit!', 'Sharp!', 'Sawubona!', 'Hey!'],\r\n        excitement: ['Lekker!', 'Sharp sharp!', 'Eish!', 'Awesome!'],\r\n        approval: ['Lekker!', 'Sharp!', 'Cool!', 'Nice one!'],\r\n        emphasis: ['proper', 'lekker', 'sharp', 'hey'],\r\n        callToAction: ['Come check us out!', 'Pop in!', 'Give us a try!'],\r\n        endingPhrases: ['Cheers!', 'See you now!', 'Sharp!']\r\n      },\r\n      culturalNuances: [\r\n        {\r\n          aspect: 'Rainbow Nation Unity',\r\n          importance: 'important',\r\n          description: 'Inclusive language that welcomes all South Africans',\r\n          doAndDonts: {\r\n            do: ['Be inclusive', 'Celebrate diversity', 'Use local terms naturally'],\r\n            dont: ['Exclude any group', 'Be overly formal', 'Ignore local culture']\r\n          }\r\n        }\r\n      ],\r\n      businessCommunication: {\r\n        trustBuilders: [\r\n          'Proudly South African',\r\n          'Local family business',\r\n          'Trusted by locals',\r\n          'Authentic Mzansi experience'\r\n        ],\r\n        valuePropositions: [\r\n          'Lekker food, fair prices',\r\n          'Authentic local flavors',\r\n          'Quality you can trust',\r\n          'Where everyone is welcome'\r\n        ],\r\n        communityConnection: [\r\n          'Part of the local community',\r\n          'Supporting local suppliers',\r\n          'Where neighbors meet',\r\n          'Celebrating our heritage'\r\n        ],\r\n        localReferences: [\r\n          'In the heart of [area]',\r\n          'Your local [business type]',\r\n          'Joburg favorite',\r\n          'Cape Town gem'\r\n        ]\r\n      },\r\n      socialMediaBehavior: {\r\n        preferredPlatforms: ['Facebook', 'Instagram', 'WhatsApp', 'Twitter'],\r\n        contentPreferences: ['local culture', 'food heritage', 'community events', 'authentic moments'],\r\n        engagementStyle: 'Friendly, inclusive, community-focused',\r\n        hashtagUsage: 'Local slang mixed with English, location-based',\r\n        visualPreferences: ['natural lighting', 'authentic settings', 'diverse people', 'local culture']\r\n      }\r\n    });\r\n\r\n    // Add more regions as needed...\r\n  }\r\n\r\n  /**\r\n   * Get regional communication profile\r\n   */\r\n  public getRegionalProfile(location: string): RegionalProfile | null {\r\n    const locationLower = location.toLowerCase();\r\n\r\n    // Kenya detection\r\n    if (locationLower.includes('kenya') || locationLower.includes('nairobi') ||\r\n      locationLower.includes('mombasa') || locationLower.includes('kisumu')) {\r\n      return this.regionalProfiles.get('kenya');\r\n    }\r\n\r\n    // Nigeria detection\r\n    if (locationLower.includes('nigeria') || locationLower.includes('lagos') ||\r\n      locationLower.includes('abuja') || locationLower.includes('kano')) {\r\n      return this.regionalProfiles.get('nigeria');\r\n    }\r\n\r\n    // South Africa detection\r\n    if (locationLower.includes('south africa') || locationLower.includes('johannesburg') ||\r\n      locationLower.includes('cape town') || locationLower.includes('durban')) {\r\n      return this.regionalProfiles.get('south_africa');\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Generate regionally authentic content\r\n   */\r\n  public generateRegionalContent(\r\n    businessType: string,\r\n    businessName: string,\r\n    location: string,\r\n    contentType: 'headline' | 'subheadline' | 'caption' | 'cta' = 'headline'\r\n  ): string {\r\n    const profile = this.getRegionalProfile(location);\r\n\r\n    if (!profile) {\r\n      return this.generateGenericContent(businessType, businessName, contentType);\r\n    }\r\n\r\n    switch (contentType) {\r\n      case 'headline':\r\n        return this.generateRegionalHeadline(businessType, businessName, profile);\r\n      case 'subheadline':\r\n        return this.generateRegionalSubheadline(businessType, businessName, profile);\r\n      case 'caption':\r\n        return this.generateRegionalCaption(businessType, businessName, profile);\r\n      case 'cta':\r\n        return this.generateRegionalCTA(businessType, businessName, profile);\r\n      default:\r\n        return this.generateRegionalHeadline(businessType, businessName, profile);\r\n    }\r\n  }\r\n\r\n  private generateRegionalHeadline(businessType: string, businessName: string, profile: RegionalProfile): string {\r\n    const { localSlang, advertisingPatterns, businessCommunication } = profile;\r\n\r\n    // Get relevant advertising pattern\r\n    const relevantPattern = advertisingPatterns.find(p => p.effectiveness === 'high') || advertisingPatterns[0];\r\n\r\n    // Create meaningful headlines that tell a story\r\n    const meaningfulTemplates = [\r\n      `What makes ${businessName} different in ${profile.region}?`,\r\n      `The ${profile.region.toLowerCase()} secret everyone's talking about`,\r\n      `Why ${businessName} is ${profile.region}'s best kept secret`,\r\n      `${this.getRandomElement(businessCommunication.valuePropositions)} - ${businessName}`,\r\n      `Discover what makes ${businessName} special`,\r\n      `${businessName}: ${this.getRandomElement(businessCommunication.trustBuilders)}`,\r\n    ];\r\n\r\n    // Add local flavor to meaningful content\r\n    const selectedTemplate = this.getRandomElement(meaningfulTemplates);\r\n\r\n    // Enhance with local expressions where appropriate\r\n    if (Math.random() > 0.6) {\r\n      const localTouch = this.getRandomElement(localSlang.excitement);\r\n      return `${selectedTemplate} ${localTouch}`;\r\n    }\r\n\r\n    return selectedTemplate;\r\n  }\r\n\r\n  private generateRegionalSubheadline(businessType: string, businessName: string, profile: RegionalProfile): string {\r\n    const { localSlang, businessCommunication } = profile;\r\n\r\n    // Create meaningful subheadlines that provide context\r\n    const meaningfulTemplates = [\r\n      `${this.getRandomElement(businessCommunication.valuePropositions)} you can trust`,\r\n      `Authentic ${businessType.toLowerCase()} with a local touch`,\r\n      `Where tradition meets innovation`,\r\n      `${this.getRandomElement(businessCommunication.trustBuilders)} since day one`,\r\n      `Bringing ${profile.region}'s finest to your table`,\r\n      `More than just ${businessType.toLowerCase()} - it's an experience`,\r\n      `Crafted with care, served with pride`,\r\n      `Your neighborhood's favorite gathering place`,\r\n      `Quality ingredients, time-tested recipes`,\r\n      `Where every customer becomes family`\r\n    ];\r\n\r\n    // Occasionally add local flair\r\n    const baseSubheadline = this.getRandomElement(meaningfulTemplates);\r\n\r\n    if (Math.random() > 0.8) {\r\n      const localEmphasis = this.getRandomElement(localSlang.emphasis);\r\n      return `${localEmphasis} ${baseSubheadline.toLowerCase()}`;\r\n    }\r\n\r\n    return baseSubheadline;\r\n  }\r\n\r\n  private generateRegionalCaption(businessType: string, businessName: string, profile: RegionalProfile): string {\r\n    const { localSlang, businessCommunication, culturalNuances } = profile;\r\n\r\n    // Create meaningful story-driven captions\r\n    const storyTemplates = [\r\n      {\r\n        opening: `Ever wondered what makes ${businessName} stand out?`,\r\n        story: `We've been ${this.getRandomElement(businessCommunication.trustBuilders)} for years, bringing you ${this.getRandomElement(businessCommunication.valuePropositions)}. Our secret? We understand what ${profile.region} truly values.`,\r\n        proof: `From our carefully selected ingredients to our time-tested recipes, every detail matters. That's why we're ${this.getRandomElement(businessCommunication.communityConnection)}.`,\r\n        action: `Ready to taste the difference? ${this.getRandomElement(localSlang.callToAction)}`\r\n      },\r\n      {\r\n        opening: `Here's what makes ${businessName} special in ${profile.region}:`,\r\n        story: `✨ ${this.getRandomElement(businessCommunication.valuePropositions)}\\n✨ ${this.getRandomElement(businessCommunication.trustBuilders)}\\n✨ ${this.getRandomElement(businessCommunication.communityConnection)}`,\r\n        proof: `We don't just serve ${businessType.toLowerCase()} - we create experiences that bring people together. That's the ${profile.region} way!`,\r\n        action: `Come see for yourself why locals choose us. ${this.getRandomElement(localSlang.callToAction)}`\r\n      },\r\n      {\r\n        opening: `The story behind ${businessName}:`,\r\n        story: `We started with a simple mission: to be ${this.getRandomElement(businessCommunication.trustBuilders)} while delivering ${this.getRandomElement(businessCommunication.valuePropositions)}.`,\r\n        proof: `Today, we're proud to be ${this.getRandomElement(businessCommunication.communityConnection)}, serving authentic ${businessType.toLowerCase()} that reflects our heritage and values.`,\r\n        action: `Join our growing family! ${this.getRandomElement(localSlang.callToAction)}`\r\n      }\r\n    ];\r\n\r\n    const selectedStory = this.getRandomElement(storyTemplates);\r\n\r\n    // Add local greeting and closing\r\n    const greeting = Math.random() > 0.7 ? `${this.getRandomElement(localSlang.greetings)} ` : '';\r\n    const excitement = Math.random() > 0.5 ? ` ${this.getRandomElement(localSlang.excitement)}` : '';\r\n    const ending = this.getRandomElement(localSlang.endingPhrases);\r\n\r\n    return `${greeting}${selectedStory.opening}\r\n\r\n${selectedStory.story}\r\n\r\n${selectedStory.proof}${excitement}\r\n\r\n${selectedStory.action}\r\n\r\n${ending}`;\r\n  }\r\n\r\n  private generateRegionalCTA(businessType: string, businessName: string, profile: RegionalProfile): string {\r\n    const { localSlang, businessCommunication } = profile;\r\n\r\n    // Create meaningful CTAs that provide clear value\r\n    const meaningfulCTAs = [\r\n      `Taste the difference at ${businessName}`,\r\n      `Experience authentic ${businessType.toLowerCase()} today`,\r\n      `Join our community of satisfied customers`,\r\n      `Discover why locals choose ${businessName}`,\r\n      `Book your table and taste the tradition`,\r\n      `Visit us and see what makes us special`,\r\n      `Come for the food, stay for the experience`,\r\n      `Your next favorite meal awaits`,\r\n      `Ready to become part of our family?`,\r\n      `Let us show you what quality means`\r\n    ];\r\n\r\n    // Add local CTAs with context\r\n    const localCTAs = localSlang.callToAction.map(cta => {\r\n      if (Math.random() > 0.5) {\r\n        return `${cta} - ${this.getRandomElement(businessCommunication.valuePropositions)}`;\r\n      }\r\n      return cta;\r\n    });\r\n\r\n    const allCTAs = [...meaningfulCTAs, ...localCTAs];\r\n    return this.getRandomElement(allCTAs);\r\n  }\r\n\r\n  private generateGenericContent(businessType: string, businessName: string, contentType: string): string {\r\n    // Fallback for unsupported regions\r\n    const templates = {\r\n      headline: `Experience the best at ${businessName}`,\r\n      subheadline: `Quality ${businessType.toLowerCase()} you can trust`,\r\n      caption: `Welcome to ${businessName}! We're committed to providing you with exceptional ${businessType} services. Visit us today!`,\r\n      cta: `Visit ${businessName} today!`\r\n    };\r\n\r\n    return templates[contentType as keyof typeof templates] || templates.headline;\r\n  }\r\n\r\n  private getRandomElement<T>(array: T[]): T {\r\n    return array[Math.floor(Math.random() * array.length)] || array[0];\r\n  }\r\n\r\n  /**\r\n   * Get regional hashtags\r\n   */\r\n  public getRegionalHashtags(location: string, businessType: string): string[] {\r\n    const profile = this.getRegionalProfile(location);\r\n\r\n    if (!profile) {\r\n      return [`#${businessType}`, `#local`, `#quality`];\r\n    }\r\n\r\n    const hashtags: string[] = [];\r\n\r\n    // Add location-based hashtags\r\n    if (location.toLowerCase().includes('nairobi')) {\r\n      hashtags.push('#NairobiEats', '#KenyanFood', '#NairobiLife', '#254Food');\r\n    } else if (location.toLowerCase().includes('lagos')) {\r\n      hashtags.push('#LagosEats', '#NaijaFood', '#LagosLife', '#9jaFood');\r\n    } else if (location.toLowerCase().includes('johannesburg')) {\r\n      hashtags.push('#JoziEats', '#SouthAfricanFood', '#MzansiFood', '#JHBLife');\r\n    }\r\n\r\n    // Add business type hashtags with local flavor\r\n    hashtags.push(`#${businessType}`, '#LocalBusiness', '#CommunityFavorite');\r\n\r\n    return hashtags;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const regionalEngine = new RegionalCommunicationEngine();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAgEM,MAAM;IACH,mBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,0BAA0B;IACjC;IAEQ,6BAA6B;QACnC,wCAAwC;QACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS;YACjC,QAAQ;YACR,SAAS;YACT,oBAAoB;gBAClB,YAAY;gBACZ,WAAW;gBACX,qBAAqB;gBACrB,YAAY;oBAAC;oBAAS;oBAAW;oBAAmB;iBAAe;gBACnE,mBAAmB;oBAAC;oBAAqB;oBAAgB;oBAAoB;iBAAc;gBAC3F,mBAAmB;oBAAC;oBAAO;oBAAc;oBAAU;oBAAQ;iBAAa;YAC1E;YACA,qBAAqB;gBACnB;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;wBAAY;wBAAY;qBAAY;gBAClD;gBACA;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;wBAAa;qBAAW;gBACtC;gBACA;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;qBAAM;gBACpB;aACD;YACD,YAAY;gBACV,WAAW;oBAAC;oBAAU;oBAAS;oBAAU;oBAAQ;iBAAU;gBAC3D,YAAY;oBAAC;oBAAe;oBAAc;oBAAS;oBAAU;iBAAQ;gBACrE,UAAU;oBAAC;oBAAS;oBAAQ;oBAAU;oBAAgB;iBAAc;gBACpE,UAAU;oBAAC;oBAAU;oBAAQ;oBAAS;oBAAQ;iBAAO;gBACrD,cAAc;oBAAC;oBAAc;oBAAW;oBAAa;oBAAc;iBAAU;gBAC7E,eAAe;oBAAC;oBAAc;oBAAgB;oBAAgB;iBAAmB;YACnF;YACA,iBAAiB;gBACf;oBACE,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;wBACV,IAAI;4BAAC;4BAA6B;4BAAgC;yBAAqB;wBACvF,MAAM;4BAAC;4BAAuB;4BAAwB;yBAAmB;oBAC3E;gBACF;gBACA;oBACE,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;wBACV,IAAI;4BAAC;4BAA2B;4BAA8B;yBAAyB;wBACvF,MAAM;4BAAC;4BAA2B;4BAA2B;yBAA0B;oBACzF;gBACF;aACD;YACD,uBAAuB;gBACrB,eAAe;oBACb;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;iBACD;gBACD,qBAAqB;oBACnB;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;iBACD;YACH;YACA,qBAAqB;gBACnB,oBAAoB;oBAAC;oBAAY;oBAAY;oBAAa;iBAAS;gBACnE,oBAAoB;oBAAC;oBAAe;oBAAqB;oBAAyB;iBAAmB;gBACrG,iBAAiB;gBACjB,cAAc;gBACd,mBAAmB;oBAAC;oBAAiB;oBAAqB;oBAAwB;iBAAiB;YACrG;QACF;QAEA,wCAAwC;QACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW;YACnC,QAAQ;YACR,SAAS;YACT,oBAAoB;gBAClB,YAAY;gBACZ,WAAW;gBACX,qBAAqB;gBACrB,YAAY;oBAAC;oBAAa;oBAAQ;oBAAa;iBAAkB;gBACjE,mBAAmB;oBAAC;oBAAoB;oBAAmB;oBAAiB;iBAAoB;gBAChG,mBAAmB;oBAAC;oBAAQ;oBAAiB;oBAAsB;iBAAsB;YAC3F;YACA,qBAAqB;gBACnB;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;wBAAa;wBAAW;qBAAW;gBACjD;gBACA;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;qBAAM;gBACpB;aACD;YACD,YAAY;gBACV,WAAW;oBAAC;oBAAY;oBAAqB;oBAAQ;iBAAU;gBAC/D,YAAY;oBAAC;oBAAgB;oBAAqB;oBAAY;iBAAY;gBAC1E,UAAU;oBAAC;oBAAY;oBAAU;oBAAY;iBAAc;gBAC3D,UAAU;oBAAC;oBAAO;oBAAa;oBAAU;iBAAU;gBACnD,cAAc;oBAAC;oBAAgB;oBAAa;oBAAsB;iBAAiB;gBACnF,eAAe;oBAAC;oBAAiB;oBAAwB;iBAAmB;YAC9E;YACA,iBAAiB;gBACf;oBACE,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;wBACV,IAAI;4BAAC;4BAAmC;4BAAqB;yBAAkB;wBAC/E,MAAM;4BAAC;4BAAiB;4BAA0B;yBAAkB;oBACtE;gBACF;aACD;YACD,uBAAuB;gBACrB,eAAe;oBACb;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;iBACD;gBACD,qBAAqB;oBACnB;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;iBACD;YACH;YACA,qBAAqB;gBACnB,oBAAoB;oBAAC;oBAAa;oBAAW;oBAAY;iBAAW;gBACpE,oBAAoB;oBAAC;oBAAe;oBAAsB;oBAAqB;iBAAuB;gBACtG,iBAAiB;gBACjB,cAAc;gBACd,mBAAmB;oBAAC;oBAAkB;oBAAwB;oBAAmB;iBAAgB;YACnG;QACF;QAEA,wCAAwC;QACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB;YACxC,QAAQ;YACR,SAAS;YACT,oBAAoB;gBAClB,YAAY;gBACZ,WAAW;gBACX,qBAAqB;gBACrB,YAAY;oBAAC;oBAAa;oBAAY;oBAAa;iBAAO;gBAC1D,mBAAmB;oBAAC;oBAAiB;oBAAe;oBAAmB;iBAAuB;gBAC9F,mBAAmB;oBAAC;oBAAW;oBAAmB;oBAAW;iBAAS;YACxE;YACA,qBAAqB;gBACnB;oBACE,MAAM;oBACN,UAAU;oBACV,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,eAAe;oBACf,WAAW;wBAAC;wBAAa;qBAAW;gBACtC;aACD;YACD,YAAY;gBACV,WAAW;oBAAC;oBAAW;oBAAU;oBAAa;iBAAO;gBACrD,YAAY;oBAAC;oBAAW;oBAAgB;oBAAS;iBAAW;gBAC5D,UAAU;oBAAC;oBAAW;oBAAU;oBAAS;iBAAY;gBACrD,UAAU;oBAAC;oBAAU;oBAAU;oBAAS;iBAAM;gBAC9C,cAAc;oBAAC;oBAAsB;oBAAW;iBAAiB;gBACjE,eAAe;oBAAC;oBAAW;oBAAgB;iBAAS;YACtD;YACA,iBAAiB;gBACf;oBACE,QAAQ;oBACR,YAAY;oBACZ,aAAa;oBACb,YAAY;wBACV,IAAI;4BAAC;4BAAgB;4BAAuB;yBAA4B;wBACxE,MAAM;4BAAC;4BAAqB;4BAAoB;yBAAuB;oBACzE;gBACF;aACD;YACD,uBAAuB;gBACrB,eAAe;oBACb;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;iBACD;gBACD,qBAAqB;oBACnB;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;iBACD;YACH;YACA,qBAAqB;gBACnB,oBAAoB;oBAAC;oBAAY;oBAAa;oBAAY;iBAAU;gBACpE,oBAAoB;oBAAC;oBAAiB;oBAAiB;oBAAoB;iBAAoB;gBAC/F,iBAAiB;gBACjB,cAAc;gBACd,mBAAmB;oBAAC;oBAAoB;oBAAsB;oBAAkB;iBAAgB;YAClG;QACF;IAEA,gCAAgC;IAClC;IAEA;;GAEC,GACD,AAAO,mBAAmB,QAAgB,EAA0B;QAClE,MAAM,gBAAgB,SAAS,WAAW;QAE1C,kBAAkB;QAClB,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,cAC5D,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,WAAW;YACvE,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACnC;QAEA,oBAAoB;QACpB,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,YAC9D,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,SAAS;YACnE,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACnC;QAEA,yBAAyB;QACzB,IAAI,cAAc,QAAQ,CAAC,mBAAmB,cAAc,QAAQ,CAAC,mBACnE,cAAc,QAAQ,CAAC,gBAAgB,cAAc,QAAQ,CAAC,WAAW;YACzE,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAO,wBACL,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,cAA8D,UAAU,EAChE;QACR,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAExC,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,cAAc;QACjE;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,cAAc,cAAc;YACnE,KAAK;gBACH,OAAO,IAAI,CAAC,2BAA2B,CAAC,cAAc,cAAc;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,uBAAuB,CAAC,cAAc,cAAc;YAClE,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,cAAc;YAC9D;gBACE,OAAO,IAAI,CAAC,wBAAwB,CAAC,cAAc,cAAc;QACrE;IACF;IAEQ,yBAAyB,YAAoB,EAAE,YAAoB,EAAE,OAAwB,EAAU;QAC7G,MAAM,EAAE,UAAU,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,GAAG;QAEnE,mCAAmC;QACnC,MAAM,kBAAkB,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,WAAW,mBAAmB,CAAC,EAAE;QAE3G,gDAAgD;QAChD,MAAM,sBAAsB;YAC1B,CAAC,WAAW,EAAE,aAAa,cAAc,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YAC5D,CAAC,IAAI,EAAE,QAAQ,MAAM,CAAC,WAAW,GAAG,gCAAgC,CAAC;YACrE,CAAC,IAAI,EAAE,aAAa,IAAI,EAAE,QAAQ,MAAM,CAAC,mBAAmB,CAAC;YAC7D,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,GAAG,EAAE,cAAc;YACrF,CAAC,oBAAoB,EAAE,aAAa,QAAQ,CAAC;YAC7C,GAAG,aAAa,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,GAAG;SACjF;QAED,yCAAyC;QACzC,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAAC;QAE/C,mDAAmD;QACnD,IAAI,KAAK,MAAM,KAAK,KAAK;YACvB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC,WAAW,UAAU;YAC9D,OAAO,GAAG,iBAAiB,CAAC,EAAE,YAAY;QAC5C;QAEA,OAAO;IACT;IAEQ,4BAA4B,YAAoB,EAAE,YAAoB,EAAE,OAAwB,EAAU;QAChH,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,GAAG;QAE9C,sDAAsD;QACtD,MAAM,sBAAsB;YAC1B,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,cAAc,CAAC;YACjF,CAAC,UAAU,EAAE,aAAa,WAAW,GAAG,mBAAmB,CAAC;YAC5D,CAAC,gCAAgC,CAAC;YAClC,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,EAAE,cAAc,CAAC;YAC7E,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,uBAAuB,CAAC;YACnD,CAAC,eAAe,EAAE,aAAa,WAAW,GAAG,qBAAqB,CAAC;YACnE,CAAC,oCAAoC,CAAC;YACtC,CAAC,4CAA4C,CAAC;YAC9C,CAAC,wCAAwC,CAAC;YAC1C,CAAC,mCAAmC,CAAC;SACtC;QAED,+BAA+B;QAC/B,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC;QAE9C,IAAI,KAAK,MAAM,KAAK,KAAK;YACvB,MAAM,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ;YAC/D,OAAO,GAAG,cAAc,CAAC,EAAE,gBAAgB,WAAW,IAAI;QAC5D;QAEA,OAAO;IACT;IAEQ,wBAAwB,YAAoB,EAAE,YAAoB,EAAE,OAAwB,EAAU;QAC5G,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,eAAe,EAAE,GAAG;QAE/D,0CAA0C;QAC1C,MAAM,iBAAiB;YACrB;gBACE,SAAS,CAAC,yBAAyB,EAAE,aAAa,WAAW,CAAC;gBAC9D,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,EAAE,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,iCAAiC,EAAE,QAAQ,MAAM,CAAC,cAAc,CAAC;gBAC3O,OAAO,CAAC,2GAA2G,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,mBAAmB,EAAE,CAAC,CAAC;gBACxL,QAAQ,CAAC,+BAA+B,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,YAAY,GAAG;YAC5F;YACA;gBACE,SAAS,CAAC,kBAAkB,EAAE,aAAa,YAAY,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;gBAC1E,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,mBAAmB,GAAG;gBACpN,OAAO,CAAC,oBAAoB,EAAE,aAAa,WAAW,GAAG,gEAAgE,EAAE,QAAQ,MAAM,CAAC,KAAK,CAAC;gBAChJ,QAAQ,CAAC,4CAA4C,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,YAAY,GAAG;YACzG;YACA;gBACE,SAAS,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;gBAC5C,OAAO,CAAC,wCAAwC,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,EAAE,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,CAAC,CAAC;gBAClM,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,mBAAmB,EAAE,oBAAoB,EAAE,aAAa,WAAW,GAAG,uCAAuC,CAAC;gBAC7L,QAAQ,CAAC,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,YAAY,GAAG;YACtF;SACD;QAED,MAAM,gBAAgB,IAAI,CAAC,gBAAgB,CAAC;QAE5C,iCAAiC;QACjC,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC,GAAG;QAC3F,MAAM,aAAa,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,UAAU,GAAG,GAAG;QAC9F,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC,WAAW,aAAa;QAE7D,OAAO,GAAG,WAAW,cAAc,OAAO,CAAC;;AAE/C,EAAE,cAAc,KAAK,CAAC;;AAEtB,EAAE,cAAc,KAAK,GAAG,WAAW;;AAEnC,EAAE,cAAc,MAAM,CAAC;;AAEvB,EAAE,QAAQ;IACR;IAEQ,oBAAoB,YAAoB,EAAE,YAAoB,EAAE,OAAwB,EAAU;QACxG,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,GAAG;QAE9C,kDAAkD;QAClD,MAAM,iBAAiB;YACrB,CAAC,wBAAwB,EAAE,cAAc;YACzC,CAAC,qBAAqB,EAAE,aAAa,WAAW,GAAG,MAAM,CAAC;YAC1D,CAAC,yCAAyC,CAAC;YAC3C,CAAC,2BAA2B,EAAE,cAAc;YAC5C,CAAC,uCAAuC,CAAC;YACzC,CAAC,sCAAsC,CAAC;YACxC,CAAC,0CAA0C,CAAC;YAC5C,CAAC,8BAA8B,CAAC;YAChC,CAAC,mCAAmC,CAAC;YACrC,CAAC,kCAAkC,CAAC;SACrC;QAED,8BAA8B;QAC9B,MAAM,YAAY,WAAW,YAAY,CAAC,GAAG,CAAC,CAAA;YAC5C,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,OAAO,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,GAAG;YACrF;YACA,OAAO;QACT;QAEA,MAAM,UAAU;eAAI;eAAmB;SAAU;QACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B;IAEQ,uBAAuB,YAAoB,EAAE,YAAoB,EAAE,WAAmB,EAAU;QACtG,mCAAmC;QACnC,MAAM,YAAY;YAChB,UAAU,CAAC,uBAAuB,EAAE,cAAc;YAClD,aAAa,CAAC,QAAQ,EAAE,aAAa,WAAW,GAAG,cAAc,CAAC;YAClE,SAAS,CAAC,WAAW,EAAE,aAAa,oDAAoD,EAAE,aAAa,0BAA0B,CAAC;YAClI,KAAK,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC;QACrC;QAEA,OAAO,SAAS,CAAC,YAAsC,IAAI,UAAU,QAAQ;IAC/E;IAEQ,iBAAoB,KAAU,EAAK;QACzC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE,IAAI,KAAK,CAAC,EAAE;IACpE;IAEA;;GAEC,GACD,AAAO,oBAAoB,QAAgB,EAAE,YAAoB,EAAY;QAC3E,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAExC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAC,CAAC,CAAC,EAAE,cAAc;gBAAE,CAAC,MAAM,CAAC;gBAAE,CAAC,QAAQ,CAAC;aAAC;QACnD;QAEA,MAAM,WAAqB,EAAE;QAE7B,8BAA8B;QAC9B,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,YAAY;YAC9C,SAAS,IAAI,CAAC,gBAAgB,eAAe,gBAAgB;QAC/D,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU;YACnD,SAAS,IAAI,CAAC,cAAc,cAAc,cAAc;QAC1D,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,iBAAiB;YAC1D,SAAS,IAAI,CAAC,aAAa,qBAAqB,eAAe;QACjE;QAEA,+CAA+C;QAC/C,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,kBAAkB;QAEpD,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/advanced-content-generator.ts"], "sourcesContent": ["/**\r\n * Advanced Content Generator\r\n * Deep business understanding, cultural awareness, and competitive analysis\r\n */\r\n\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\nimport { rssService } from '@/services/rss-feed-service';\r\nimport { regionalEngine } from './regional-communication-engine';\r\n\r\nexport interface BusinessProfile {\r\n  businessName: string;\r\n  businessType: string;\r\n  location: string;\r\n  targetAudience: string;\r\n  brandVoice: string;\r\n  uniqueSellingPoints: string[];\r\n  competitors: string[];\r\n  previousPosts?: SocialMediaPost[];\r\n  businessHours?: string;\r\n  specialOffers?: string[];\r\n  seasonalFocus?: string[];\r\n}\r\n\r\nexport interface SocialMediaPost {\r\n  headline: string;\r\n  subheadline?: string;\r\n  caption: string;\r\n  cta: string;\r\n  hashtags: string[];\r\n  platform: string;\r\n  engagement?: number;\r\n  performance?: 'high' | 'medium' | 'low';\r\n}\r\n\r\nexport interface ContentAnalysis {\r\n  businessIntelligence: BusinessIntelligence;\r\n  culturalContext: CulturalContext;\r\n  competitiveAnalysis: CompetitiveAnalysis;\r\n  trendingInsights: TrendingInsights;\r\n}\r\n\r\nexport interface BusinessIntelligence {\r\n  industryKeywords: string[];\r\n  businessStrengths: string[];\r\n  targetEmotions: string[];\r\n  valuePropositions: string[];\r\n  localRelevance: string[];\r\n  seasonalOpportunities: string[];\r\n}\r\n\r\nexport interface CulturalContext {\r\n  primaryLanguage: string;\r\n  localPhrases: string[];\r\n  culturalValues: string[];\r\n  localEvents: string[];\r\n  communicationStyle: string;\r\n  localInfluencers: string[];\r\n}\r\n\r\nexport interface CompetitiveAnalysis {\r\n  industryBenchmarks: string[];\r\n  competitorStrategies: string[];\r\n  marketGaps: string[];\r\n  differentiators: string[];\r\n  performanceTargets: string[];\r\n}\r\n\r\nexport interface TrendingInsights {\r\n  currentTrends: string[];\r\n  viralPatterns: string[];\r\n  platformSpecificTrends: string[];\r\n  seasonalTrends: string[];\r\n  emergingTopics: string[];\r\n}\r\n\r\nexport class AdvancedContentGenerator {\r\n  private businessIntelligence: Map<string, BusinessIntelligence> = new Map();\r\n  private culturalDatabase: Map<string, CulturalContext> = new Map();\r\n  private performanceHistory: Map<string, SocialMediaPost[]> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeCulturalDatabase();\r\n    this.initializeBusinessIntelligence();\r\n  }\r\n\r\n  /**\r\n   * Initialize cultural database with local knowledge\r\n   */\r\n  private initializeCulturalDatabase() {\r\n    const cultures = {\r\n      'United States': {\r\n        primaryLanguage: 'English',\r\n        localPhrases: ['awesome', 'amazing', 'game-changer', 'must-have', 'life-changing'],\r\n        culturalValues: ['innovation', 'convenience', 'quality', 'value', 'authenticity'],\r\n        localEvents: ['Black Friday', 'Super Bowl', 'Memorial Day', 'Labor Day'],\r\n        communicationStyle: 'direct, enthusiastic, benefit-focused',\r\n        localInfluencers: ['lifestyle', 'fitness', 'food', 'tech', 'business'],\r\n      },\r\n      'United Kingdom': {\r\n        primaryLanguage: 'English',\r\n        localPhrases: ['brilliant', 'fantastic', 'proper', 'lovely', 'spot on'],\r\n        culturalValues: ['tradition', 'quality', 'reliability', 'heritage', 'craftsmanship'],\r\n        localEvents: ['Boxing Day', 'Bank Holiday', 'Wimbledon', 'Royal events'],\r\n        communicationStyle: 'polite, understated, witty',\r\n        localInfluencers: ['lifestyle', 'fashion', 'food', 'travel', 'culture'],\r\n      },\r\n      'Canada': {\r\n        primaryLanguage: 'English',\r\n        localPhrases: ['eh', 'beauty', 'fantastic', 'wonderful', 'great'],\r\n        culturalValues: ['friendliness', 'inclusivity', 'nature', 'community', 'sustainability'],\r\n        localEvents: ['Canada Day', 'Victoria Day', 'Thanksgiving', 'Winter Olympics'],\r\n        communicationStyle: 'friendly, inclusive, nature-focused',\r\n        localInfluencers: ['outdoor', 'lifestyle', 'food', 'wellness', 'community'],\r\n      },\r\n      'Australia': {\r\n        primaryLanguage: 'English',\r\n        localPhrases: ['mate', 'fair dinkum', 'ripper', 'bonzer', 'ace'],\r\n        culturalValues: ['laid-back', 'outdoor lifestyle', 'mateship', 'adventure', 'authenticity'],\r\n        localEvents: ['Australia Day', 'Melbourne Cup', 'ANZAC Day', 'AFL Grand Final'],\r\n        communicationStyle: 'casual, friendly, straightforward',\r\n        localInfluencers: ['outdoor', 'fitness', 'food', 'travel', 'lifestyle'],\r\n      },\r\n    };\r\n\r\n    Object.entries(cultures).forEach(([location, context]) => {\r\n      this.culturalDatabase.set(location, context as CulturalContext);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Initialize business intelligence database\r\n   */\r\n  private initializeBusinessIntelligence() {\r\n    const businessTypes = {\r\n      restaurant: {\r\n        industryKeywords: ['fresh', 'delicious', 'authentic', 'homemade', 'seasonal', 'local', 'chef-crafted'],\r\n        businessStrengths: ['taste', 'atmosphere', 'service', 'ingredients', 'experience'],\r\n        targetEmotions: ['hunger', 'comfort', 'satisfaction', 'joy', 'nostalgia'],\r\n        valuePropositions: ['quality ingredients', 'unique flavors', 'memorable experience', 'value for money'],\r\n        localRelevance: ['neighborhood favorite', 'local ingredients', 'community gathering'],\r\n        seasonalOpportunities: ['seasonal menu', 'holiday specials', 'summer patio', 'winter comfort'],\r\n      },\r\n      retail: {\r\n        industryKeywords: ['trendy', 'stylish', 'affordable', 'quality', 'exclusive', 'limited', 'new arrival'],\r\n        businessStrengths: ['selection', 'price', 'quality', 'customer service', 'convenience'],\r\n        targetEmotions: ['desire', 'confidence', 'satisfaction', 'excitement', 'belonging'],\r\n        valuePropositions: ['best prices', 'latest trends', 'quality guarantee', 'exclusive access'],\r\n        localRelevance: ['local fashion', 'community style', 'neighborhood store'],\r\n        seasonalOpportunities: ['seasonal collections', 'holiday sales', 'back-to-school', 'summer styles'],\r\n      },\r\n      fitness: {\r\n        industryKeywords: ['strong', 'healthy', 'fit', 'transformation', 'results', 'energy', 'powerful'],\r\n        businessStrengths: ['expertise', 'results', 'community', 'equipment', 'motivation'],\r\n        targetEmotions: ['motivation', 'confidence', 'achievement', 'energy', 'determination'],\r\n        valuePropositions: ['proven results', 'expert guidance', 'supportive community', 'flexible schedules'],\r\n        localRelevance: ['neighborhood gym', 'local fitness community', 'accessible location'],\r\n        seasonalOpportunities: ['New Year resolutions', 'summer body', 'holiday fitness', 'spring training'],\r\n      },\r\n      beauty: {\r\n        industryKeywords: ['glowing', 'radiant', 'beautiful', 'flawless', 'natural', 'luxurious', 'rejuvenating'],\r\n        businessStrengths: ['expertise', 'products', 'results', 'relaxation', 'personalization'],\r\n        targetEmotions: ['confidence', 'relaxation', 'beauty', 'self-care', 'transformation'],\r\n        valuePropositions: ['expert care', 'premium products', 'personalized service', 'lasting results'],\r\n        localRelevance: ['trusted local salon', 'community beauty expert', 'neighborhood favorite'],\r\n        seasonalOpportunities: ['bridal season', 'holiday glam', 'summer skin', 'winter care'],\r\n      },\r\n    };\r\n\r\n    Object.entries(businessTypes).forEach(([type, intelligence]) => {\r\n      this.businessIntelligence.set(type, intelligence as BusinessIntelligence);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Analyze business and context for content generation\r\n   */\r\n  public async analyzeBusinessContext(profile: BusinessProfile): Promise<ContentAnalysis> {\r\n\r\n    // Get business intelligence\r\n    const businessIntelligence = this.businessIntelligence.get(profile.businessType) || {\r\n      industryKeywords: [],\r\n      businessStrengths: [],\r\n      targetEmotions: [],\r\n      valuePropositions: [],\r\n      localRelevance: [],\r\n      seasonalOpportunities: [],\r\n    };\r\n\r\n    // Get cultural context\r\n    const culturalContext = this.culturalDatabase.get(profile.location) || {\r\n      primaryLanguage: 'English',\r\n      localPhrases: [],\r\n      culturalValues: [],\r\n      localEvents: [],\r\n      communicationStyle: 'friendly, professional',\r\n      localInfluencers: [],\r\n    };\r\n\r\n    // Get trending insights\r\n    const trendingData = await trendingEnhancer.getTrendingEnhancement({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      targetAudience: profile.targetAudience,\r\n    });\r\n\r\n    const trendingInsights: TrendingInsights = {\r\n      currentTrends: trendingData.keywords,\r\n      viralPatterns: trendingData.topics,\r\n      platformSpecificTrends: trendingData.hashtags,\r\n      seasonalTrends: trendingData.seasonalThemes,\r\n      emergingTopics: trendingData.industryBuzz,\r\n    };\r\n\r\n    // Analyze competitors (simulated for now)\r\n    const competitiveAnalysis: CompetitiveAnalysis = {\r\n      industryBenchmarks: this.generateIndustryBenchmarks(profile.businessType),\r\n      competitorStrategies: this.analyzeCompetitorStrategies(profile.competitors),\r\n      marketGaps: this.identifyMarketGaps(profile.businessType),\r\n      differentiators: profile.uniqueSellingPoints,\r\n      performanceTargets: this.setPerformanceTargets(profile.businessType),\r\n    };\r\n\r\n\r\n    return {\r\n      businessIntelligence,\r\n      culturalContext,\r\n      competitiveAnalysis,\r\n      trendingInsights,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate highly engaging content based on analysis\r\n   */\r\n  public async generateEngagingContent(\r\n    profile: BusinessProfile,\r\n    platform: string,\r\n    contentType: 'promotional' | 'educational' | 'entertaining' | 'seasonal' = 'promotional'\r\n  ): Promise<SocialMediaPost> {\r\n\r\n    const analysis = await this.analyzeBusinessContext(profile);\r\n\r\n    // Generate content components\r\n    const headline = await this.generateCatchyHeadline(profile, analysis, platform, contentType);\r\n    const subheadline = await this.generateSubheadline(profile, analysis, headline);\r\n    const caption = await this.generateEngagingCaption(profile, analysis, headline, platform);\r\n    const cta = await this.generateCompellingCTA(profile, analysis, platform, contentType);\r\n    const hashtags = await this.generateStrategicHashtags(profile, analysis, platform);\r\n\r\n    const post: SocialMediaPost = {\r\n      headline,\r\n      subheadline,\r\n      caption,\r\n      cta,\r\n      hashtags,\r\n      platform,\r\n    };\r\n\r\n    // Store for performance tracking\r\n    this.storePostForAnalysis(profile.businessName, post);\r\n\r\n    return post;\r\n  }\r\n\r\n  /**\r\n   * Generate catchy, business-specific headlines with regional authenticity\r\n   */\r\n  private async generateCatchyHeadline(\r\n    profile: BusinessProfile,\r\n    analysis: ContentAnalysis,\r\n    platform: string,\r\n    contentType: string\r\n  ): Promise<string> {\r\n    const { businessIntelligence, culturalContext, trendingInsights } = analysis;\r\n\r\n    // Try regional communication first for authentic local content\r\n    const regionalProfile = regionalEngine.getRegionalProfile(profile.location);\r\n    if (regionalProfile) {\r\n      const regionalHeadline = regionalEngine.generateRegionalContent(\r\n        profile.businessType,\r\n        profile.businessName,\r\n        profile.location,\r\n        'headline'\r\n      );\r\n\r\n      // Enhance with trending elements if available\r\n      if (trendingInsights.currentTrends.length > 0) {\r\n        const trendingElement = this.getRandomElement(trendingInsights.currentTrends);\r\n        return `${regionalHeadline} - ${trendingElement}`;\r\n      }\r\n\r\n      return regionalHeadline;\r\n    }\r\n\r\n    // Fallback to original method for unsupported regions\r\n    // Combine business strengths with trending topics\r\n    const powerWords = [...businessIntelligence.industryKeywords, ...culturalContext.localPhrases];\r\n    const trendingWords = trendingInsights.currentTrends.slice(0, 5);\r\n\r\n    // Create headline templates based on content type\r\n    const templates = {\r\n      promotional: [\r\n        `${this.getRandomElement(powerWords)} ${profile.businessName} ${this.getRandomElement(businessIntelligence.valuePropositions)}`,\r\n        `${this.getRandomElement(culturalContext.localPhrases)} ${this.getRandomElement(trendingWords)} at ${profile.businessName}`,\r\n        `${profile.businessName}: ${this.getRandomElement(businessIntelligence.businessStrengths)} that ${this.getRandomElement(businessIntelligence.targetEmotions)}`,\r\n      ],\r\n      educational: [\r\n        `${this.getRandomElement(trendingWords)} secrets from ${profile.businessName}`,\r\n        `Why ${profile.businessName} ${this.getRandomElement(businessIntelligence.businessStrengths)} matters`,\r\n        `The ${this.getRandomElement(powerWords)} guide to ${this.getRandomElement(businessIntelligence.industryKeywords)}`,\r\n      ],\r\n      entertaining: [\r\n        `${this.getRandomElement(culturalContext.localPhrases)}! ${profile.businessName} ${this.getRandomElement(trendingWords)}`,\r\n        `${profile.businessName} + ${this.getRandomElement(trendingWords)} = ${this.getRandomElement(powerWords)}`,\r\n        `When ${this.getRandomElement(businessIntelligence.targetEmotions)} meets ${profile.businessName}`,\r\n      ],\r\n      seasonal: [\r\n        `${this.getRandomElement(trendingInsights.seasonalTrends)} ${this.getRandomElement(powerWords)} at ${profile.businessName}`,\r\n        `${profile.businessName}'s ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,\r\n        `${this.getRandomElement(culturalContext.localEvents)} special: ${this.getRandomElement(businessIntelligence.valuePropositions)}`,\r\n      ],\r\n    };\r\n\r\n    const selectedTemplate = this.getRandomElement(templates[contentType as keyof typeof templates]);\r\n    return this.capitalizeWords(selectedTemplate);\r\n  }\r\n\r\n  /**\r\n   * Generate supporting subheadlines\r\n   */\r\n  private async generateSubheadline(\r\n    profile: BusinessProfile,\r\n    analysis: ContentAnalysis,\r\n    headline: string\r\n  ): Promise<string> {\r\n    const { businessIntelligence, culturalContext } = analysis;\r\n\r\n    const supportingElements = [\r\n      ...businessIntelligence.valuePropositions,\r\n      ...businessIntelligence.localRelevance,\r\n      ...culturalContext.culturalValues,\r\n    ];\r\n\r\n    const templates = [\r\n      `${this.getRandomElement(supportingElements)} in ${profile.location}`,\r\n      `${this.getRandomElement(businessIntelligence.businessStrengths)} you can trust`,\r\n      `${this.getRandomElement(culturalContext.localPhrases)} experience awaits`,\r\n    ];\r\n\r\n    return this.getRandomElement(templates);\r\n  }\r\n\r\n  /**\r\n   * Generate engaging, culturally-aware captions with regional authenticity\r\n   */\r\n  private async generateEngagingCaption(\r\n    profile: BusinessProfile,\r\n    analysis: ContentAnalysis,\r\n    headline: string,\r\n    platform: string\r\n  ): Promise<string> {\r\n    const { businessIntelligence, culturalContext, trendingInsights } = analysis;\r\n\r\n    // Try regional communication first for authentic local content\r\n    const regionalProfile = regionalEngine.getRegionalProfile(profile.location);\r\n    if (regionalProfile) {\r\n      return regionalEngine.generateRegionalContent(\r\n        profile.businessType,\r\n        profile.businessName,\r\n        profile.location,\r\n        'caption'\r\n      );\r\n    }\r\n\r\n    // Fallback to original method for unsupported regions\r\n    // Platform-specific caption styles\r\n    const platformStyles = {\r\n      instagram: 'visual, lifestyle-focused, emoji-rich',\r\n      facebook: 'community-focused, conversational, story-driven',\r\n      twitter: 'concise, witty, trending-aware',\r\n      linkedin: 'professional, value-driven, industry-focused',\r\n      tiktok: 'trendy, fun, challenge-oriented',\r\n    };\r\n\r\n    const captionElements = [\r\n      `At ${profile.businessName}, we believe ${this.getRandomElement(businessIntelligence.valuePropositions)}.`,\r\n      `Our ${this.getRandomElement(businessIntelligence.businessStrengths)} brings ${this.getRandomElement(businessIntelligence.targetEmotions)} to ${profile.location}.`,\r\n      `${this.getRandomElement(culturalContext.localPhrases)}! ${this.getRandomElement(trendingInsights.currentTrends)} meets ${this.getRandomElement(businessIntelligence.industryKeywords)}.`,\r\n      `Join our ${profile.location} community for ${this.getRandomElement(businessIntelligence.localRelevance)}.`,\r\n    ];\r\n\r\n    return captionElements.slice(0, 2).join(' ');\r\n  }\r\n\r\n  /**\r\n   * Generate compelling CTAs with regional authenticity\r\n   */\r\n  private async generateCompellingCTA(\r\n    profile: BusinessProfile,\r\n    analysis: ContentAnalysis,\r\n    platform: string,\r\n    contentType: string\r\n  ): Promise<string> {\r\n    const { businessIntelligence, culturalContext } = analysis;\r\n\r\n    // Try regional communication first for authentic local CTAs\r\n    const regionalProfile = regionalEngine.getRegionalProfile(profile.location);\r\n    if (regionalProfile) {\r\n      return regionalEngine.generateRegionalContent(\r\n        profile.businessType,\r\n        profile.businessName,\r\n        profile.location,\r\n        'cta'\r\n      );\r\n    }\r\n\r\n    // Fallback to original method for unsupported regions\r\n    const ctaTemplates = {\r\n      promotional: [\r\n        `Visit ${profile.businessName} today!`,\r\n        `Experience ${this.getRandomElement(businessIntelligence.businessStrengths)} now`,\r\n        `Book your ${this.getRandomElement(businessIntelligence.targetEmotions)} experience`,\r\n      ],\r\n      educational: [\r\n        `Learn more at ${profile.businessName}`,\r\n        `Discover the ${this.getRandomElement(businessIntelligence.industryKeywords)} difference`,\r\n        `Get expert advice from ${profile.businessName}`,\r\n      ],\r\n      entertaining: [\r\n        `Join the fun at ${profile.businessName}!`,\r\n        `Share your ${profile.businessName} experience`,\r\n        `Tag a friend who needs this!`,\r\n      ],\r\n      seasonal: [\r\n        `Don't miss our ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,\r\n        `Limited time at ${profile.businessName}`,\r\n        `Celebrate with ${profile.businessName}`,\r\n      ],\r\n    };\r\n\r\n    return this.getRandomElement(ctaTemplates[contentType as keyof typeof ctaTemplates]);\r\n  }\r\n\r\n  /**\r\n   * Generate strategic hashtags with regional authenticity\r\n   */\r\n  private async generateStrategicHashtags(\r\n    profile: BusinessProfile,\r\n    analysis: ContentAnalysis,\r\n    platform: string\r\n  ): Promise<string[]> {\r\n    const { businessIntelligence, trendingInsights } = analysis;\r\n\r\n    // Try regional hashtags first for authentic local content\r\n    const regionalProfile = regionalEngine.getRegionalProfile(profile.location);\r\n    if (regionalProfile) {\r\n      const regionalHashtags = regionalEngine.getRegionalHashtags(profile.location, profile.businessType);\r\n\r\n      // Add business-specific hashtags\r\n      const businessHashtags = [\r\n        `#${profile.businessName.replace(/\\s+/g, '')}`,\r\n        `#${profile.businessType}`,\r\n      ];\r\n\r\n      // Combine regional and business hashtags\r\n      const combinedHashtags = [...regionalHashtags, ...businessHashtags];\r\n\r\n      // Add some trending hashtags if available\r\n      trendingInsights.platformSpecificTrends.slice(0, 2).forEach(hashtag => {\r\n        if (!combinedHashtags.includes(hashtag)) {\r\n          combinedHashtags.push(hashtag);\r\n        }\r\n      });\r\n\r\n      return combinedHashtags.slice(0, 10);\r\n    }\r\n\r\n    // Fallback to original method for unsupported regions\r\n    const hashtags: string[] = [];\r\n\r\n    // Business-specific hashtags\r\n    hashtags.push(`#${profile.businessName.replace(/\\s+/g, '')}`);\r\n    hashtags.push(`#${profile.businessType}`);\r\n    hashtags.push(`#${profile.location.replace(/\\s+/g, '')}`);\r\n\r\n    // Industry hashtags\r\n    businessIntelligence.industryKeywords.slice(0, 3).forEach(keyword => {\r\n      hashtags.push(`#${keyword.replace(/\\s+/g, '')}`);\r\n    });\r\n\r\n    // Trending hashtags\r\n    trendingInsights.platformSpecificTrends.slice(0, 4).forEach(hashtag => {\r\n      if (!hashtags.includes(hashtag)) {\r\n        hashtags.push(hashtag);\r\n      }\r\n    });\r\n\r\n    // Platform-specific hashtags\r\n    const platformHashtags = {\r\n      instagram: ['#instagood', '#photooftheday'],\r\n      facebook: ['#community', '#local'],\r\n      twitter: ['#trending', '#news'],\r\n      linkedin: ['#business', '#professional'],\r\n      tiktok: ['#fyp', '#viral'],\r\n    };\r\n\r\n    if (platformHashtags[platform as keyof typeof platformHashtags]) {\r\n      hashtags.push(...platformHashtags[platform as keyof typeof platformHashtags]);\r\n    }\r\n\r\n    return hashtags.slice(0, 10); // Limit to 10 hashtags\r\n  }\r\n\r\n  /**\r\n   * Generate regional subheadlines\r\n   */\r\n  private generateRegionalSubheadline(\r\n    profile: BusinessProfile,\r\n    regionalProfile: any,\r\n    context: ContentAnalysis\r\n  ): string {\r\n    const { businessCommunication, localSlang } = regionalProfile;\r\n\r\n    const templates = [\r\n      `${this.getRandomElement(businessCommunication.valuePropositions)} in ${profile.location}`,\r\n      `${this.getRandomElement(businessCommunication.trustBuilders)} - ${this.getRandomElement(localSlang.approval)}`,\r\n      `${this.getRandomElement(businessCommunication.communityConnection)} ${this.getRandomElement(localSlang.emphasis)}`,\r\n    ];\r\n\r\n    return this.getRandomElement(templates);\r\n  }\r\n\r\n  // Helper methods\r\n  private getRandomElement<T>(array: T[]): T {\r\n    return array[Math.floor(Math.random() * array.length)] || array[0];\r\n  }\r\n\r\n  private capitalizeWords(str: string): string {\r\n    return str.replace(/\\b\\w/g, l => l.toUpperCase());\r\n  }\r\n\r\n  private generateIndustryBenchmarks(businessType: string): string[] {\r\n    return [`${businessType} industry standard`, 'market leader performance', 'customer satisfaction benchmark'];\r\n  }\r\n\r\n  private analyzeCompetitorStrategies(competitors: string[]): string[] {\r\n    return competitors.map(comp => `${comp} strategy analysis`);\r\n  }\r\n\r\n  private identifyMarketGaps(businessType: string): string[] {\r\n    return [`${businessType} market opportunity`, 'underserved customer segment', 'innovation gap'];\r\n  }\r\n\r\n  private setPerformanceTargets(businessType: string): string[] {\r\n    return ['high engagement rate', 'increased brand awareness', 'customer acquisition'];\r\n  }\r\n\r\n  private storePostForAnalysis(businessName: string, post: SocialMediaPost): void {\r\n    const existing = this.performanceHistory.get(businessName) || [];\r\n    existing.push(post);\r\n    this.performanceHistory.set(businessName, existing.slice(-50)); // Keep last 50 posts\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const advancedContentGenerator = new AdvancedContentGenerator();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAoEO,MAAM;IACH,uBAA0D,IAAI,MAAM;IACpE,mBAAiD,IAAI,MAAM;IAC3D,qBAAqD,IAAI,MAAM;IAEvE,aAAc;QACZ,IAAI,CAAC,0BAA0B;QAC/B,IAAI,CAAC,8BAA8B;IACrC;IAEA;;GAEC,GACD,AAAQ,6BAA6B;QACnC,MAAM,WAAW;YACf,iBAAiB;gBACf,iBAAiB;gBACjB,cAAc;oBAAC;oBAAW;oBAAW;oBAAgB;oBAAa;iBAAgB;gBAClF,gBAAgB;oBAAC;oBAAc;oBAAe;oBAAW;oBAAS;iBAAe;gBACjF,aAAa;oBAAC;oBAAgB;oBAAc;oBAAgB;iBAAY;gBACxE,oBAAoB;gBACpB,kBAAkB;oBAAC;oBAAa;oBAAW;oBAAQ;oBAAQ;iBAAW;YACxE;YACA,kBAAkB;gBAChB,iBAAiB;gBACjB,cAAc;oBAAC;oBAAa;oBAAa;oBAAU;oBAAU;iBAAU;gBACvE,gBAAgB;oBAAC;oBAAa;oBAAW;oBAAe;oBAAY;iBAAgB;gBACpF,aAAa;oBAAC;oBAAc;oBAAgB;oBAAa;iBAAe;gBACxE,oBAAoB;gBACpB,kBAAkB;oBAAC;oBAAa;oBAAW;oBAAQ;oBAAU;iBAAU;YACzE;YACA,UAAU;gBACR,iBAAiB;gBACjB,cAAc;oBAAC;oBAAM;oBAAU;oBAAa;oBAAa;iBAAQ;gBACjE,gBAAgB;oBAAC;oBAAgB;oBAAe;oBAAU;oBAAa;iBAAiB;gBACxF,aAAa;oBAAC;oBAAc;oBAAgB;oBAAgB;iBAAkB;gBAC9E,oBAAoB;gBACpB,kBAAkB;oBAAC;oBAAW;oBAAa;oBAAQ;oBAAY;iBAAY;YAC7E;YACA,aAAa;gBACX,iBAAiB;gBACjB,cAAc;oBAAC;oBAAQ;oBAAe;oBAAU;oBAAU;iBAAM;gBAChE,gBAAgB;oBAAC;oBAAa;oBAAqB;oBAAY;oBAAa;iBAAe;gBAC3F,aAAa;oBAAC;oBAAiB;oBAAiB;oBAAa;iBAAkB;gBAC/E,oBAAoB;gBACpB,kBAAkB;oBAAC;oBAAW;oBAAW;oBAAQ;oBAAU;iBAAY;YACzE;QACF;QAEA,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,UAAU,QAAQ;YACnD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;QACtC;IACF;IAEA;;GAEC,GACD,AAAQ,iCAAiC;QACvC,MAAM,gBAAgB;YACpB,YAAY;gBACV,kBAAkB;oBAAC;oBAAS;oBAAa;oBAAa;oBAAY;oBAAY;oBAAS;iBAAe;gBACtG,mBAAmB;oBAAC;oBAAS;oBAAc;oBAAW;oBAAe;iBAAa;gBAClF,gBAAgB;oBAAC;oBAAU;oBAAW;oBAAgB;oBAAO;iBAAY;gBACzE,mBAAmB;oBAAC;oBAAuB;oBAAkB;oBAAwB;iBAAkB;gBACvG,gBAAgB;oBAAC;oBAAyB;oBAAqB;iBAAsB;gBACrF,uBAAuB;oBAAC;oBAAiB;oBAAoB;oBAAgB;iBAAiB;YAChG;YACA,QAAQ;gBACN,kBAAkB;oBAAC;oBAAU;oBAAW;oBAAc;oBAAW;oBAAa;oBAAW;iBAAc;gBACvG,mBAAmB;oBAAC;oBAAa;oBAAS;oBAAW;oBAAoB;iBAAc;gBACvF,gBAAgB;oBAAC;oBAAU;oBAAc;oBAAgB;oBAAc;iBAAY;gBACnF,mBAAmB;oBAAC;oBAAe;oBAAiB;oBAAqB;iBAAmB;gBAC5F,gBAAgB;oBAAC;oBAAiB;oBAAmB;iBAAqB;gBAC1E,uBAAuB;oBAAC;oBAAwB;oBAAiB;oBAAkB;iBAAgB;YACrG;YACA,SAAS;gBACP,kBAAkB;oBAAC;oBAAU;oBAAW;oBAAO;oBAAkB;oBAAW;oBAAU;iBAAW;gBACjG,mBAAmB;oBAAC;oBAAa;oBAAW;oBAAa;oBAAa;iBAAa;gBACnF,gBAAgB;oBAAC;oBAAc;oBAAc;oBAAe;oBAAU;iBAAgB;gBACtF,mBAAmB;oBAAC;oBAAkB;oBAAmB;oBAAwB;iBAAqB;gBACtG,gBAAgB;oBAAC;oBAAoB;oBAA2B;iBAAsB;gBACtF,uBAAuB;oBAAC;oBAAwB;oBAAe;oBAAmB;iBAAkB;YACtG;YACA,QAAQ;gBACN,kBAAkB;oBAAC;oBAAW;oBAAW;oBAAa;oBAAY;oBAAW;oBAAa;iBAAe;gBACzG,mBAAmB;oBAAC;oBAAa;oBAAY;oBAAW;oBAAc;iBAAkB;gBACxF,gBAAgB;oBAAC;oBAAc;oBAAc;oBAAU;oBAAa;iBAAiB;gBACrF,mBAAmB;oBAAC;oBAAe;oBAAoB;oBAAwB;iBAAkB;gBACjG,gBAAgB;oBAAC;oBAAuB;oBAA2B;iBAAwB;gBAC3F,uBAAuB;oBAAC;oBAAiB;oBAAgB;oBAAe;iBAAc;YACxF;QACF;QAEA,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,MAAM,aAAa;YACzD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM;QACtC;IACF;IAEA;;GAEC,GACD,MAAa,uBAAuB,OAAwB,EAA4B;QAEtF,4BAA4B;QAC5B,MAAM,uBAAuB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,YAAY,KAAK;YAClF,kBAAkB,EAAE;YACpB,mBAAmB,EAAE;YACrB,gBAAgB,EAAE;YAClB,mBAAmB,EAAE;YACrB,gBAAgB,EAAE;YAClB,uBAAuB,EAAE;QAC3B;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,QAAQ,KAAK;YACrE,iBAAiB;YACjB,cAAc,EAAE;YAChB,gBAAgB,EAAE;YAClB,aAAa,EAAE;YACf,oBAAoB;YACpB,kBAAkB,EAAE;QACtB;QAEA,wBAAwB;QACxB,MAAM,eAAe,MAAM,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;YACjE,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,gBAAgB,QAAQ,cAAc;QACxC;QAEA,MAAM,mBAAqC;YACzC,eAAe,aAAa,QAAQ;YACpC,eAAe,aAAa,MAAM;YAClC,wBAAwB,aAAa,QAAQ;YAC7C,gBAAgB,aAAa,cAAc;YAC3C,gBAAgB,aAAa,YAAY;QAC3C;QAEA,0CAA0C;QAC1C,MAAM,sBAA2C;YAC/C,oBAAoB,IAAI,CAAC,0BAA0B,CAAC,QAAQ,YAAY;YACxE,sBAAsB,IAAI,CAAC,2BAA2B,CAAC,QAAQ,WAAW;YAC1E,YAAY,IAAI,CAAC,kBAAkB,CAAC,QAAQ,YAAY;YACxD,iBAAiB,QAAQ,mBAAmB;YAC5C,oBAAoB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,YAAY;QACrE;QAGA,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAa,wBACX,OAAwB,EACxB,QAAgB,EAChB,cAA2E,aAAa,EAC9D;QAE1B,MAAM,WAAW,MAAM,IAAI,CAAC,sBAAsB,CAAC;QAEnD,8BAA8B;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,UAAU,UAAU;QAChF,MAAM,cAAc,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,UAAU;QACtE,MAAM,UAAU,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,UAAU,UAAU;QAChF,MAAM,MAAM,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,UAAU,UAAU;QAC1E,MAAM,WAAW,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,UAAU;QAEzE,MAAM,OAAwB;YAC5B;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,iCAAiC;QACjC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,YAAY,EAAE;QAEhD,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,uBACZ,OAAwB,EACxB,QAAyB,EACzB,QAAgB,EAChB,WAAmB,EACF;QACjB,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;QAEpE,+DAA+D;QAC/D,MAAM,kBAAkB,kJAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,QAAQ;QAC1E,IAAI,iBAAiB;YACnB,MAAM,mBAAmB,kJAAA,CAAA,iBAAc,CAAC,uBAAuB,CAC7D,QAAQ,YAAY,EACpB,QAAQ,YAAY,EACpB,QAAQ,QAAQ,EAChB;YAGF,8CAA8C;YAC9C,IAAI,iBAAiB,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,aAAa;gBAC5E,OAAO,GAAG,iBAAiB,GAAG,EAAE,iBAAiB;YACnD;YAEA,OAAO;QACT;QAEA,sDAAsD;QACtD,kDAAkD;QAClD,MAAM,aAAa;eAAI,qBAAqB,gBAAgB;eAAK,gBAAgB,YAAY;SAAC;QAC9F,MAAM,gBAAgB,iBAAiB,aAAa,CAAC,KAAK,CAAC,GAAG;QAE9D,kDAAkD;QAClD,MAAM,YAAY;YAChB,aAAa;gBACX,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,QAAQ,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,GAAG;gBAC/H,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,IAAI,EAAE,QAAQ,YAAY,EAAE;gBAC3H,GAAG,QAAQ,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,cAAc,GAAG;aAC/J;YACD,aAAa;gBACX,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,cAAc,EAAE,QAAQ,YAAY,EAAE;gBAC9E,CAAC,IAAI,EAAE,QAAQ,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,QAAQ,CAAC;gBACtG,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,GAAG;aACpH;YACD,cAAc;gBACZ,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,YAAY,EAAE,EAAE,EAAE,QAAQ,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;gBACzH,GAAG,QAAQ,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,aAAa;gBAC1G,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,cAAc,EAAE,OAAO,EAAE,QAAQ,YAAY,EAAE;aACnG;YACD,UAAU;gBACR,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,cAAc,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,IAAI,EAAE,QAAQ,YAAY,EAAE;gBAC3H,GAAG,QAAQ,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,qBAAqB,GAAG;gBAChG,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,GAAG;aAClI;QACH;QAEA,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAsC;QAC/F,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B;IAEA;;GAEC,GACD,MAAc,oBACZ,OAAwB,EACxB,QAAyB,EACzB,QAAgB,EACC;QACjB,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAE,GAAG;QAElD,MAAM,qBAAqB;eACtB,qBAAqB,iBAAiB;eACtC,qBAAqB,cAAc;eACnC,gBAAgB,cAAc;SAClC;QAED,MAAM,YAAY;YAChB,GAAG,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,IAAI,EAAE,QAAQ,QAAQ,EAAE;YACrE,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,cAAc,CAAC;YAChF,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,YAAY,EAAE,kBAAkB,CAAC;SAC3E;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B;IAEA;;GAEC,GACD,MAAc,wBACZ,OAAwB,EACxB,QAAyB,EACzB,QAAgB,EAChB,QAAgB,EACC;QACjB,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;QAEpE,+DAA+D;QAC/D,MAAM,kBAAkB,kJAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,QAAQ;QAC1E,IAAI,iBAAiB;YACnB,OAAO,kJAAA,CAAA,iBAAc,CAAC,uBAAuB,CAC3C,QAAQ,YAAY,EACpB,QAAQ,YAAY,EACpB,QAAQ,QAAQ,EAChB;QAEJ;QAEA,sDAAsD;QACtD,mCAAmC;QACnC,MAAM,iBAAiB;YACrB,WAAW;YACX,UAAU;YACV,SAAS;YACT,UAAU;YACV,QAAQ;QACV;QAEA,MAAM,kBAAkB;YACtB,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,CAAC,CAAC;YAC1G,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,cAAc,EAAE,IAAI,EAAE,QAAQ,QAAQ,CAAC,CAAC,CAAC;YACnK,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,EAAE,CAAC,CAAC;YACzL,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,cAAc,EAAE,CAAC,CAAC;SAC5G;QAED,OAAO,gBAAgB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IAC1C;IAEA;;GAEC,GACD,MAAc,sBACZ,OAAwB,EACxB,QAAyB,EACzB,QAAgB,EAChB,WAAmB,EACF;QACjB,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAE,GAAG;QAElD,4DAA4D;QAC5D,MAAM,kBAAkB,kJAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,QAAQ;QAC1E,IAAI,iBAAiB;YACnB,OAAO,kJAAA,CAAA,iBAAc,CAAC,uBAAuB,CAC3C,QAAQ,YAAY,EACpB,QAAQ,YAAY,EACpB,QAAQ,QAAQ,EAChB;QAEJ;QAEA,sDAAsD;QACtD,MAAM,eAAe;YACnB,aAAa;gBACX,CAAC,MAAM,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC;gBACtC,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,iBAAiB,EAAE,IAAI,CAAC;gBACjF,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,cAAc,EAAE,WAAW,CAAC;aACrF;YACD,aAAa;gBACX,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;gBACvC,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,EAAE,WAAW,CAAC;gBACzF,CAAC,uBAAuB,EAAE,QAAQ,YAAY,EAAE;aACjD;YACD,cAAc;gBACZ,CAAC,gBAAgB,EAAE,QAAQ,YAAY,CAAC,CAAC,CAAC;gBAC1C,CAAC,WAAW,EAAE,QAAQ,YAAY,CAAC,WAAW,CAAC;gBAC/C,CAAC,4BAA4B,CAAC;aAC/B;YACD,UAAU;gBACR,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,qBAAqB,GAAG;gBACrF,CAAC,gBAAgB,EAAE,QAAQ,YAAY,EAAE;gBACzC,CAAC,eAAe,EAAE,QAAQ,YAAY,EAAE;aACzC;QACH;QAEA,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,YAAyC;IACrF;IAEA;;GAEC,GACD,MAAc,0BACZ,OAAwB,EACxB,QAAyB,EACzB,QAAgB,EACG;QACnB,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG;QAEnD,0DAA0D;QAC1D,MAAM,kBAAkB,kJAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,QAAQ;QAC1E,IAAI,iBAAiB;YACnB,MAAM,mBAAmB,kJAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,QAAQ,QAAQ,EAAE,QAAQ,YAAY;YAElG,iCAAiC;YACjC,MAAM,mBAAmB;gBACvB,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;gBAC9C,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE;aAC3B;YAED,yCAAyC;YACzC,MAAM,mBAAmB;mBAAI;mBAAqB;aAAiB;YAEnE,0CAA0C;YAC1C,iBAAiB,sBAAsB,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;gBAC1D,IAAI,CAAC,iBAAiB,QAAQ,CAAC,UAAU;oBACvC,iBAAiB,IAAI,CAAC;gBACxB;YACF;YAEA,OAAO,iBAAiB,KAAK,CAAC,GAAG;QACnC;QAEA,sDAAsD;QACtD,MAAM,WAAqB,EAAE;QAE7B,6BAA6B;QAC7B,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;QAC5D,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE;QACxC,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,OAAO,CAAC,QAAQ,KAAK;QAExD,oBAAoB;QACpB,qBAAqB,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;YACxD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK;QACjD;QAEA,oBAAoB;QACpB,iBAAiB,sBAAsB,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;YAC1D,IAAI,CAAC,SAAS,QAAQ,CAAC,UAAU;gBAC/B,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,6BAA6B;QAC7B,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAc;aAAiB;YAC3C,UAAU;gBAAC;gBAAc;aAAS;YAClC,SAAS;gBAAC;gBAAa;aAAQ;YAC/B,UAAU;gBAAC;gBAAa;aAAgB;YACxC,QAAQ;gBAAC;gBAAQ;aAAS;QAC5B;QAEA,IAAI,gBAAgB,CAAC,SAA0C,EAAE;YAC/D,SAAS,IAAI,IAAI,gBAAgB,CAAC,SAA0C;QAC9E;QAEA,OAAO,SAAS,KAAK,CAAC,GAAG,KAAK,uBAAuB;IACvD;IAEA;;GAEC,GACD,AAAQ,4BACN,OAAwB,EACxB,eAAoB,EACpB,OAAwB,EAChB;QACR,MAAM,EAAE,qBAAqB,EAAE,UAAU,EAAE,GAAG;QAE9C,MAAM,YAAY;YAChB,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,iBAAiB,EAAE,IAAI,EAAE,QAAQ,QAAQ,EAAE;YAC1F,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ,GAAG;YAC/G,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,mBAAmB,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ,GAAG;SACpH;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B;IAEA,iBAAiB;IACT,iBAAoB,KAAU,EAAK;QACzC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE,IAAI,KAAK,CAAC,EAAE;IACpE;IAEQ,gBAAgB,GAAW,EAAU;QAC3C,OAAO,IAAI,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;IAChD;IAEQ,2BAA2B,YAAoB,EAAY;QACjE,OAAO;YAAC,GAAG,aAAa,kBAAkB,CAAC;YAAE;YAA6B;SAAkC;IAC9G;IAEQ,4BAA4B,WAAqB,EAAY;QACnE,OAAO,YAAY,GAAG,CAAC,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;IAC5D;IAEQ,mBAAmB,YAAoB,EAAY;QACzD,OAAO;YAAC,GAAG,aAAa,mBAAmB,CAAC;YAAE;YAAgC;SAAiB;IACjG;IAEQ,sBAAsB,YAAoB,EAAY;QAC5D,OAAO;YAAC;YAAwB;YAA6B;SAAuB;IACtF;IAEQ,qBAAqB,YAAoB,EAAE,IAAqB,EAAQ;QAC9E,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAChE,SAAS,IAAI,CAAC;QACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,SAAS,KAAK,CAAC,CAAC,MAAM,qBAAqB;IACvF;AACF;AAGO,MAAM,2BAA2B,IAAI", "debugId": null}}, {"offset": {"line": 2506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/content-performance-analyzer.ts"], "sourcesContent": ["/**\r\n * Content Performance Analyzer\r\n * Benchmarks against industry standards and continuously improves content quality\r\n */\r\n\r\nimport { SocialMediaPost, BusinessProfile } from './advanced-content-generator';\r\n\r\nexport interface PerformanceMetrics {\r\n  engagementRate: number;\r\n  reachRate: number;\r\n  clickThroughRate: number;\r\n  conversionRate: number;\r\n  shareRate: number;\r\n  commentRate: number;\r\n  saveRate: number;\r\n  overallScore: number;\r\n}\r\n\r\nexport interface IndustryBenchmark {\r\n  businessType: string;\r\n  platform: string;\r\n  averageEngagement: number;\r\n  topPerformerEngagement: number;\r\n  averageReach: number;\r\n  bestPractices: string[];\r\n  commonMistakes: string[];\r\n  successPatterns: string[];\r\n}\r\n\r\nexport interface ContentOptimization {\r\n  strengths: string[];\r\n  improvements: string[];\r\n  recommendations: string[];\r\n  nextIterationFocus: string[];\r\n  competitiveAdvantages: string[];\r\n}\r\n\r\nexport class ContentPerformanceAnalyzer {\r\n  private industryBenchmarks: Map<string, IndustryBenchmark[]> = new Map();\r\n  private performanceHistory: Map<string, PerformanceMetrics[]> = new Map();\r\n  private contentPatterns: Map<string, string[]> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeIndustryBenchmarks();\r\n    this.initializeSuccessPatterns();\r\n  }\r\n\r\n  /**\r\n   * Initialize industry benchmarks for different business types\r\n   */\r\n  private initializeIndustryBenchmarks() {\r\n    const benchmarks = {\r\n      restaurant: [\r\n        {\r\n          businessType: 'restaurant',\r\n          platform: 'instagram',\r\n          averageEngagement: 3.2,\r\n          topPerformerEngagement: 8.5,\r\n          averageReach: 15.4,\r\n          bestPractices: [\r\n            'High-quality food photography',\r\n            'Behind-the-scenes content',\r\n            'Customer testimonials',\r\n            'Seasonal menu highlights',\r\n            'Local ingredient stories'\r\n          ],\r\n          commonMistakes: [\r\n            'Poor lighting in photos',\r\n            'Generic captions',\r\n            'Inconsistent posting',\r\n            'Ignoring local trends',\r\n            'Over-promotional content'\r\n          ],\r\n          successPatterns: [\r\n            'Food close-ups with natural lighting',\r\n            'Stories about ingredients and preparation',\r\n            'Customer experience highlights',\r\n            'Local community involvement',\r\n            'Seasonal and trending ingredients'\r\n          ]\r\n        },\r\n        {\r\n          businessType: 'restaurant',\r\n          platform: 'facebook',\r\n          averageEngagement: 2.8,\r\n          topPerformerEngagement: 6.2,\r\n          averageReach: 12.1,\r\n          bestPractices: [\r\n            'Community engagement',\r\n            'Event announcements',\r\n            'Customer reviews sharing',\r\n            'Local partnerships',\r\n            'Family-friendly content'\r\n          ],\r\n          commonMistakes: [\r\n            'Posting only promotional content',\r\n            'Ignoring customer comments',\r\n            'Not leveraging local events',\r\n            'Generic stock photos',\r\n            'Inconsistent brand voice'\r\n          ],\r\n          successPatterns: [\r\n            'Community event participation',\r\n            'Customer story sharing',\r\n            'Local ingredient sourcing stories',\r\n            'Family dining experiences',\r\n            'Seasonal celebration posts'\r\n          ]\r\n        }\r\n      ],\r\n      retail: [\r\n        {\r\n          businessType: 'retail',\r\n          platform: 'instagram',\r\n          averageEngagement: 2.9,\r\n          topPerformerEngagement: 7.8,\r\n          averageReach: 18.2,\r\n          bestPractices: [\r\n            'Product styling and flat lays',\r\n            'User-generated content',\r\n            'Trend-focused content',\r\n            'Behind-the-brand stories',\r\n            'Seasonal collections'\r\n          ],\r\n          commonMistakes: [\r\n            'Product-only posts',\r\n            'Poor product photography',\r\n            'Ignoring fashion trends',\r\n            'Not showcasing versatility',\r\n            'Generic product descriptions'\r\n          ],\r\n          successPatterns: [\r\n            'Lifestyle product integration',\r\n            'Trend-forward styling',\r\n            'Customer styling examples',\r\n            'Seasonal fashion guides',\r\n            'Sustainable fashion stories'\r\n          ]\r\n        }\r\n      ],\r\n      fitness: [\r\n        {\r\n          businessType: 'fitness',\r\n          platform: 'instagram',\r\n          averageEngagement: 4.1,\r\n          topPerformerEngagement: 9.3,\r\n          averageReach: 16.7,\r\n          bestPractices: [\r\n            'Transformation stories',\r\n            'Workout demonstrations',\r\n            'Motivational content',\r\n            'Community challenges',\r\n            'Expert tips and advice'\r\n          ],\r\n          commonMistakes: [\r\n            'Intimidating content for beginners',\r\n            'Only showing perfect bodies',\r\n            'Generic motivational quotes',\r\n            'Not addressing different fitness levels',\r\n            'Ignoring mental health aspects'\r\n          ],\r\n          successPatterns: [\r\n            'Inclusive fitness content',\r\n            'Real transformation journeys',\r\n            'Beginner-friendly workouts',\r\n            'Mental health and fitness connection',\r\n            'Community support stories'\r\n          ]\r\n        }\r\n      ],\r\n      beauty: [\r\n        {\r\n          businessType: 'beauty',\r\n          platform: 'instagram',\r\n          averageEngagement: 3.7,\r\n          topPerformerEngagement: 8.9,\r\n          averageReach: 14.3,\r\n          bestPractices: [\r\n            'Before/after transformations',\r\n            'Tutorial content',\r\n            'Product demonstrations',\r\n            'Skin care education',\r\n            'Inclusive beauty content'\r\n          ],\r\n          commonMistakes: [\r\n            'Over-filtered photos',\r\n            'Not showing diverse skin types',\r\n            'Generic beauty tips',\r\n            'Ignoring skincare science',\r\n            'Not addressing common concerns'\r\n          ],\r\n          successPatterns: [\r\n            'Natural beauty enhancement',\r\n            'Educational skincare content',\r\n            'Diverse model representation',\r\n            'Seasonal beauty tips',\r\n            'Self-care and confidence building'\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    Object.entries(benchmarks).forEach(([businessType, benchmarkArray]) => {\r\n      this.industryBenchmarks.set(businessType, benchmarkArray);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Initialize success patterns for content optimization\r\n   */\r\n  private initializeSuccessPatterns() {\r\n    const patterns = {\r\n      'high-engagement-headlines': [\r\n        'Question-based headlines that spark curiosity',\r\n        'Numbers and statistics in headlines',\r\n        'Emotional trigger words',\r\n        'Local references and community connection',\r\n        'Trending topic integration',\r\n        'Problem-solution format',\r\n        'Exclusive or limited-time offers',\r\n        'Behind-the-scenes insights'\r\n      ],\r\n      'effective-captions': [\r\n        'Storytelling approach',\r\n        'Personal anecdotes and experiences',\r\n        'Call-to-action integration',\r\n        'Community questions and engagement',\r\n        'Educational value provision',\r\n        'Emotional connection building',\r\n        'Local culture and language integration',\r\n        'Trending hashtag utilization'\r\n      ],\r\n      'compelling-ctas': [\r\n        'Action-oriented language',\r\n        'Urgency and scarcity elements',\r\n        'Clear value proposition',\r\n        'Personalized messaging',\r\n        'Community-focused calls',\r\n        'Experience-based invitations',\r\n        'Social proof integration',\r\n        'Local relevance emphasis'\r\n      ]\r\n    };\r\n\r\n    Object.entries(patterns).forEach(([category, patternList]) => {\r\n      this.contentPatterns.set(category, patternList);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Analyze content performance against industry benchmarks\r\n   */\r\n  public analyzePerformance(\r\n    post: SocialMediaPost,\r\n    profile: BusinessProfile,\r\n    actualMetrics?: PerformanceMetrics\r\n  ): ContentOptimization {\r\n\r\n    const benchmarks = this.industryBenchmarks.get(profile.businessType) || [];\r\n    const platformBenchmark = benchmarks.find(b => b.platform === post.platform);\r\n\r\n    if (!platformBenchmark) {\r\n      return this.generateGenericOptimization();\r\n    }\r\n\r\n    // Analyze content elements\r\n    const headlineAnalysis = this.analyzeHeadline(post.headline, platformBenchmark);\r\n    const captionAnalysis = this.analyzeCaption(post.caption, platformBenchmark);\r\n    const ctaAnalysis = this.analyzeCTA(post.cta, platformBenchmark);\r\n    const hashtagAnalysis = this.analyzeHashtags(post.hashtags, platformBenchmark);\r\n\r\n    // Generate optimization recommendations\r\n    const optimization: ContentOptimization = {\r\n      strengths: [\r\n        ...headlineAnalysis.strengths,\r\n        ...captionAnalysis.strengths,\r\n        ...ctaAnalysis.strengths,\r\n        ...hashtagAnalysis.strengths\r\n      ],\r\n      improvements: [\r\n        ...headlineAnalysis.improvements,\r\n        ...captionAnalysis.improvements,\r\n        ...ctaAnalysis.improvements,\r\n        ...hashtagAnalysis.improvements\r\n      ],\r\n      recommendations: this.generateRecommendations(platformBenchmark, profile),\r\n      nextIterationFocus: this.identifyNextIterationFocus(platformBenchmark, profile),\r\n      competitiveAdvantages: this.identifyCompetitiveAdvantages(platformBenchmark, profile)\r\n    };\r\n\r\n    return optimization;\r\n  }\r\n\r\n  /**\r\n   * Analyze headline effectiveness\r\n   */\r\n  private analyzeHeadline(headline: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    // Check for success patterns\r\n    const successPatterns = this.contentPatterns.get('high-engagement-headlines') || [];\r\n    \r\n    if (headline.includes('?')) {\r\n      strengths.push('Uses question format to engage audience');\r\n    } else {\r\n      improvements.push('Consider using questions to increase engagement');\r\n    }\r\n\r\n    if (/\\d+/.test(headline)) {\r\n      strengths.push('Includes numbers for credibility');\r\n    } else {\r\n      improvements.push('Consider adding specific numbers or statistics');\r\n    }\r\n\r\n    if (headline.length > 10 && headline.length < 60) {\r\n      strengths.push('Optimal headline length for platform');\r\n    } else {\r\n      improvements.push('Adjust headline length for better readability');\r\n    }\r\n\r\n    // Check for emotional triggers\r\n    const emotionalWords = ['amazing', 'incredible', 'exclusive', 'limited', 'secret', 'proven'];\r\n    if (emotionalWords.some(word => headline.toLowerCase().includes(word))) {\r\n      strengths.push('Uses emotional trigger words');\r\n    } else {\r\n      improvements.push('Add emotional trigger words to increase appeal');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze caption effectiveness\r\n   */\r\n  private analyzeCaption(caption: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    if (caption.length > 50 && caption.length < 300) {\r\n      strengths.push('Optimal caption length for engagement');\r\n    } else {\r\n      improvements.push('Adjust caption length for better engagement');\r\n    }\r\n\r\n    // Check for storytelling elements\r\n    if (caption.includes('we') || caption.includes('our') || caption.includes('story')) {\r\n      strengths.push('Uses storytelling approach');\r\n    } else {\r\n      improvements.push('Add storytelling elements to create connection');\r\n    }\r\n\r\n    // Check for community engagement\r\n    if (caption.includes('?') || caption.includes('comment') || caption.includes('share')) {\r\n      strengths.push('Encourages community engagement');\r\n    } else {\r\n      improvements.push('Add questions or engagement prompts');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze CTA effectiveness\r\n   */\r\n  private analyzeCTA(cta: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    const actionWords = ['visit', 'book', 'call', 'order', 'try', 'discover', 'experience'];\r\n    if (actionWords.some(word => cta.toLowerCase().includes(word))) {\r\n      strengths.push('Uses strong action words');\r\n    } else {\r\n      improvements.push('Use more compelling action words');\r\n    }\r\n\r\n    if (cta.length > 5 && cta.length < 50) {\r\n      strengths.push('Appropriate CTA length');\r\n    } else {\r\n      improvements.push('Optimize CTA length for clarity');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze hashtag strategy\r\n   */\r\n  private analyzeHashtags(hashtags: string[], benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    if (hashtags.length >= 5 && hashtags.length <= 10) {\r\n      strengths.push('Optimal number of hashtags');\r\n    } else {\r\n      improvements.push('Adjust hashtag count for better reach');\r\n    }\r\n\r\n    // Check for mix of popular and niche hashtags\r\n    const hasPopular = hashtags.some(tag => tag.includes('trending') || tag.includes('viral'));\r\n    const hasNiche = hashtags.some(tag => tag.length > 15);\r\n\r\n    if (hasPopular && hasNiche) {\r\n      strengths.push('Good mix of popular and niche hashtags');\r\n    } else {\r\n      improvements.push('Balance popular and niche hashtags for better reach');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Generate specific recommendations based on benchmarks\r\n   */\r\n  private generateRecommendations(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    // Add benchmark-specific recommendations\r\n    benchmark.bestPractices.forEach(practice => {\r\n      recommendations.push(`Implement: ${practice}`);\r\n    });\r\n\r\n    // Add business-specific recommendations\r\n    recommendations.push(`Leverage ${profile.location} local culture and events`);\r\n    recommendations.push(`Highlight unique selling points: ${profile.uniqueSellingPoints.join(', ')}`);\r\n    recommendations.push(`Target ${profile.targetAudience} with personalized messaging`);\r\n\r\n    return recommendations.slice(0, 8); // Limit to top 8 recommendations\r\n  }\r\n\r\n  /**\r\n   * Identify focus areas for next iteration\r\n   */\r\n  private identifyNextIterationFocus(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const focus: string[] = [];\r\n\r\n    // Focus on top-performing patterns\r\n    benchmark.successPatterns.forEach(pattern => {\r\n      focus.push(`Enhance: ${pattern}`);\r\n    });\r\n\r\n    // Avoid common mistakes\r\n    benchmark.commonMistakes.forEach(mistake => {\r\n      focus.push(`Avoid: ${mistake}`);\r\n    });\r\n\r\n    return focus.slice(0, 6); // Limit to top 6 focus areas\r\n  }\r\n\r\n  /**\r\n   * Identify competitive advantages\r\n   */\r\n  private identifyCompetitiveAdvantages(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const advantages: string[] = [];\r\n\r\n    // Business-specific advantages\r\n    profile.uniqueSellingPoints.forEach(usp => {\r\n      advantages.push(`Unique advantage: ${usp}`);\r\n    });\r\n\r\n    // Location-based advantages\r\n    advantages.push(`Local market expertise in ${profile.location}`);\r\n    advantages.push(`Community connection and trust`);\r\n    advantages.push(`Cultural understanding and relevance`);\r\n\r\n    return advantages.slice(0, 5); // Limit to top 5 advantages\r\n  }\r\n\r\n  /**\r\n   * Generate generic optimization for unknown business types\r\n   */\r\n  private generateGenericOptimization(): ContentOptimization {\r\n    return {\r\n      strengths: ['Content created with business context'],\r\n      improvements: ['Add industry-specific benchmarks', 'Enhance local relevance'],\r\n      recommendations: ['Research industry best practices', 'Analyze competitor content'],\r\n      nextIterationFocus: ['Improve targeting', 'Enhance engagement'],\r\n      competitiveAdvantages: ['Personalized approach', 'Local market focus']\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Track performance over time for continuous improvement\r\n   */\r\n  public trackPerformance(businessName: string, metrics: PerformanceMetrics): void {\r\n    const history = this.performanceHistory.get(businessName) || [];\r\n    history.push(metrics);\r\n    this.performanceHistory.set(businessName, history.slice(-20)); // Keep last 20 records\r\n\r\n  }\r\n\r\n  /**\r\n   * Get performance trends for a business\r\n   */\r\n  public getPerformanceTrends(businessName: string): {\r\n    trend: 'improving' | 'declining' | 'stable';\r\n    averageScore: number;\r\n    bestPerformingContent: string[];\r\n  } {\r\n    const history = this.performanceHistory.get(businessName) || [];\r\n    \r\n    if (history.length < 2) {\r\n      return {\r\n        trend: 'stable',\r\n        averageScore: history[0]?.overallScore || 0,\r\n        bestPerformingContent: []\r\n      };\r\n    }\r\n\r\n    const recent = history.slice(-5);\r\n    const older = history.slice(-10, -5);\r\n    \r\n    const recentAvg = recent.reduce((sum, m) => sum + m.overallScore, 0) / recent.length;\r\n    const olderAvg = older.reduce((sum, m) => sum + m.overallScore, 0) / older.length;\r\n    \r\n    let trend: 'improving' | 'declining' | 'stable' = 'stable';\r\n    if (recentAvg > olderAvg + 0.5) trend = 'improving';\r\n    else if (recentAvg < olderAvg - 0.5) trend = 'declining';\r\n\r\n    const averageScore = history.reduce((sum, m) => sum + m.overallScore, 0) / history.length;\r\n\r\n    return {\r\n      trend,\r\n      averageScore,\r\n      bestPerformingContent: ['High-engagement headlines', 'Community-focused content', 'Local relevance']\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const performanceAnalyzer = new ContentPerformanceAnalyzer();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAkCM,MAAM;IACH,qBAAuD,IAAI,MAAM;IACjE,qBAAwD,IAAI,MAAM;IAClE,kBAAyC,IAAI,MAAM;IAE3D,aAAc;QACZ,IAAI,CAAC,4BAA4B;QACjC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,AAAQ,+BAA+B;QACrC,MAAM,aAAa;YACjB,YAAY;gBACV;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,QAAQ;gBACN;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,SAAS;gBACP;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,QAAQ;gBACN;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;QACH;QAEA,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,cAAc,eAAe;YAChE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc;QAC5C;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B;QAClC,MAAM,WAAW;YACf,6BAA6B;gBAC3B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,UAAU,YAAY;YACvD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU;QACrC;IACF;IAEA;;GAEC,GACD,AAAO,mBACL,IAAqB,EACrB,OAAwB,EACxB,aAAkC,EACb;QAErB,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,YAAY,KAAK,EAAE;QAC1E,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,KAAK,QAAQ;QAE3E,IAAI,CAAC,mBAAmB;YACtB,OAAO,IAAI,CAAC,2BAA2B;QACzC;QAEA,2BAA2B;QAC3B,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QAC7D,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;QAC1D,MAAM,cAAc,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;QAC9C,MAAM,kBAAkB,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QAE5D,wCAAwC;QACxC,MAAM,eAAoC;YACxC,WAAW;mBACN,iBAAiB,SAAS;mBAC1B,gBAAgB,SAAS;mBACzB,YAAY,SAAS;mBACrB,gBAAgB,SAAS;aAC7B;YACD,cAAc;mBACT,iBAAiB,YAAY;mBAC7B,gBAAgB,YAAY;mBAC5B,YAAY,YAAY;mBACxB,gBAAgB,YAAY;aAChC;YACD,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,mBAAmB;YACjE,oBAAoB,IAAI,CAAC,0BAA0B,CAAC,mBAAmB;YACvE,uBAAuB,IAAI,CAAC,6BAA6B,CAAC,mBAAmB;QAC/E;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBAAgB,QAAgB,EAAE,SAA4B,EAAmD;QACvH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,6BAA6B;QAC7B,MAAM,kBAAkB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gCAAgC,EAAE;QAEnF,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,MAAM,IAAI,CAAC,WAAW;YACxB,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,SAAS,MAAM,GAAG,MAAM,SAAS,MAAM,GAAG,IAAI;YAChD,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,+BAA+B;QAC/B,MAAM,iBAAiB;YAAC;YAAW;YAAc;YAAa;YAAW;YAAU;SAAS;QAC5F,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,SAAS,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACtE,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,eAAe,OAAe,EAAE,SAA4B,EAAmD;QACrH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,IAAI,QAAQ,MAAM,GAAG,MAAM,QAAQ,MAAM,GAAG,KAAK;YAC/C,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,kCAAkC;QAClC,IAAI,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;YAClF,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,iCAAiC;QACjC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YACrF,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,WAAW,GAAW,EAAE,SAA4B,EAAmD;QAC7G,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,MAAM,cAAc;YAAC;YAAS;YAAQ;YAAQ;YAAS;YAAO;YAAY;SAAa;QACvF,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ;YAC9D,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,IAAI,MAAM,GAAG,KAAK,IAAI,MAAM,GAAG,IAAI;YACrC,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,gBAAgB,QAAkB,EAAE,SAA4B,EAAmD;QACzH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,IAAI;YACjD,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,8CAA8C;QAC9C,MAAM,aAAa,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC;QACjF,MAAM,WAAW,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;QAEnD,IAAI,cAAc,UAAU;YAC1B,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,wBAAwB,SAA4B,EAAE,OAAwB,EAAY;QAChG,MAAM,kBAA4B,EAAE;QAEpC,yCAAyC;QACzC,UAAU,aAAa,CAAC,OAAO,CAAC,CAAA;YAC9B,gBAAgB,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU;QAC/C;QAEA,wCAAwC;QACxC,gBAAgB,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,yBAAyB,CAAC;QAC5E,gBAAgB,IAAI,CAAC,CAAC,iCAAiC,EAAE,QAAQ,mBAAmB,CAAC,IAAI,CAAC,OAAO;QACjG,gBAAgB,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,cAAc,CAAC,4BAA4B,CAAC;QAEnF,OAAO,gBAAgB,KAAK,CAAC,GAAG,IAAI,iCAAiC;IACvE;IAEA;;GAEC,GACD,AAAQ,2BAA2B,SAA4B,EAAE,OAAwB,EAAY;QACnG,MAAM,QAAkB,EAAE;QAE1B,mCAAmC;QACnC,UAAU,eAAe,CAAC,OAAO,CAAC,CAAA;YAChC,MAAM,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS;QAClC;QAEA,wBAAwB;QACxB,UAAU,cAAc,CAAC,OAAO,CAAC,CAAA;YAC/B,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS;QAChC;QAEA,OAAO,MAAM,KAAK,CAAC,GAAG,IAAI,6BAA6B;IACzD;IAEA;;GAEC,GACD,AAAQ,8BAA8B,SAA4B,EAAE,OAAwB,EAAY;QACtG,MAAM,aAAuB,EAAE;QAE/B,+BAA+B;QAC/B,QAAQ,mBAAmB,CAAC,OAAO,CAAC,CAAA;YAClC,WAAW,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK;QAC5C;QAEA,4BAA4B;QAC5B,WAAW,IAAI,CAAC,CAAC,0BAA0B,EAAE,QAAQ,QAAQ,EAAE;QAC/D,WAAW,IAAI,CAAC,CAAC,8BAA8B,CAAC;QAChD,WAAW,IAAI,CAAC,CAAC,oCAAoC,CAAC;QAEtD,OAAO,WAAW,KAAK,CAAC,GAAG,IAAI,4BAA4B;IAC7D;IAEA;;GAEC,GACD,AAAQ,8BAAmD;QACzD,OAAO;YACL,WAAW;gBAAC;aAAwC;YACpD,cAAc;gBAAC;gBAAoC;aAA0B;YAC7E,iBAAiB;gBAAC;gBAAoC;aAA6B;YACnF,oBAAoB;gBAAC;gBAAqB;aAAqB;YAC/D,uBAAuB;gBAAC;gBAAyB;aAAqB;QACxE;IACF;IAEA;;GAEC,GACD,AAAO,iBAAiB,YAAoB,EAAE,OAA2B,EAAQ;QAC/E,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAC/D,QAAQ,IAAI,CAAC;QACb,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,QAAQ,KAAK,CAAC,CAAC,MAAM,uBAAuB;IAExF;IAEA;;GAEC,GACD,AAAO,qBAAqB,YAAoB,EAI9C;QACA,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAE/D,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,OAAO;gBACL,OAAO;gBACP,cAAc,OAAO,CAAC,EAAE,EAAE,gBAAgB;gBAC1C,uBAAuB,EAAE;YAC3B;QACF;QAEA,MAAM,SAAS,QAAQ,KAAK,CAAC,CAAC;QAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC;QAElC,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,OAAO,MAAM;QACpF,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,MAAM,MAAM;QAEjF,IAAI,QAA8C;QAClD,IAAI,YAAY,WAAW,KAAK,QAAQ;aACnC,IAAI,YAAY,WAAW,KAAK,QAAQ;QAE7C,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,QAAQ,MAAM;QAEzF,OAAO;YACL;YACA;YACA,uBAAuB;gBAAC;gBAA6B;gBAA6B;aAAkB;QACtG;IACF;AACF;AAGO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 2981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/viral-hashtag-engine.ts"], "sourcesContent": ["/**\r\n * Viral Hashtag Engine - Real-time trending hashtag generation\r\n * Integrates with RSS feeds and trending data to generate viral hashtags\r\n */\r\n\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\n\r\nexport interface ViralHashtagStrategy {\r\n  trending: string[];      // Currently trending hashtags\r\n  viral: string[];         // High-engagement viral hashtags  \r\n  niche: string[];         // Business-specific niche hashtags\r\n  location: string[];      // Location-based hashtags\r\n  community: string[];     // Community engagement hashtags\r\n  seasonal: string[];      // Seasonal/timely hashtags\r\n  platform: string[];     // Platform-specific hashtags\r\n  total: string[];         // Final combined strategy (15 hashtags)\r\n}\r\n\r\nexport class ViralHashtagEngine {\r\n\r\n  /**\r\n   * Generate viral hashtag strategy using real-time trending data\r\n   */\r\n  async generateViralHashtags(\r\n    businessType: string,\r\n    businessName: string,\r\n    location: string,\r\n    platform: string,\r\n    services?: string,\r\n    targetAudience?: string\r\n  ): Promise<ViralHashtagStrategy> {\r\n\r\n\r\n    try {\r\n      // Get trending data from RSS feeds and trending enhancer\r\n      const trendingData = await trendingEnhancer.getTrendingEnhancement({\r\n        businessType,\r\n        location,\r\n        platform,\r\n        targetAudience\r\n      });\r\n\r\n\r\n      // Generate different hashtag categories\r\n      const trending = await this.getTrendingHashtags(trendingData, businessType, platform);\r\n      const viral = this.getViralHashtags(businessType, platform);\r\n      const niche = this.getNicheHashtags(businessType, services);\r\n      const location_tags = this.getLocationHashtags(location);\r\n      const community = this.getCommunityHashtags(businessType, targetAudience);\r\n      const seasonal = this.getSeasonalHashtags();\r\n      const platform_tags = this.getPlatformHashtags(platform);\r\n\r\n      // Combine and optimize for virality\r\n      const total = this.optimizeForVirality([\r\n        ...trending.slice(0, 4),\r\n        ...viral.slice(0, 3),\r\n        ...niche.slice(0, 2),\r\n        ...location_tags.slice(0, 2),\r\n        ...community.slice(0, 2),\r\n        ...seasonal.slice(0, 1),\r\n        ...platform_tags.slice(0, 1)\r\n      ]);\r\n\r\n\r\n      return {\r\n        trending,\r\n        viral,\r\n        niche,\r\n        location: location_tags,\r\n        community,\r\n        seasonal,\r\n        platform: platform_tags,\r\n        total\r\n      };\r\n\r\n    } catch (error) {\r\n      return this.getFallbackHashtags(businessType, location, platform);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get trending hashtags from RSS data\r\n   */\r\n  private async getTrendingHashtags(trendingData: any, businessType: string, platform: string): Promise<string[]> {\r\n    const hashtags = [...trendingData.hashtags];\r\n\r\n    // Add business-relevant trending hashtags\r\n    const businessTrending = this.getBusinessTrendingHashtags(businessType, platform);\r\n    hashtags.push(...businessTrending);\r\n\r\n    // Remove duplicates and return top trending\r\n    return Array.from(new Set(hashtags)).slice(0, 8);\r\n  }\r\n\r\n  /**\r\n   * Get high-engagement viral hashtags\r\n   */\r\n  private getViralHashtags(businessType: string, platform: string): string[] {\r\n    const viralHashtags = {\r\n      general: ['#viral', '#trending', '#fyp', '#explore', '#discover', '#amazing', '#incredible', '#mustsee'],\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#reels', '#explorepage'],\r\n      tiktok: ['#fyp', '#foryou', '#viral', '#trending', '#foryoupage'],\r\n      facebook: ['#viral', '#share', '#community', '#local', '#trending'],\r\n      twitter: ['#trending', '#viral', '#breaking', '#news', '#update'],\r\n      linkedin: ['#professional', '#business', '#networking', '#career', '#industry']\r\n    };\r\n\r\n    const general = viralHashtags.general.sort(() => 0.5 - Math.random()).slice(0, 4);\r\n    const platformSpecific = viralHashtags[platform.toLowerCase() as keyof typeof viralHashtags] || [];\r\n\r\n    return [...general, ...platformSpecific.slice(0, 3)];\r\n  }\r\n\r\n  /**\r\n   * Get business-specific niche hashtags\r\n   */\r\n  private getNicheHashtags(businessType: string, services?: string): string[] {\r\n    const nicheMap: Record<string, string[]> = {\r\n      restaurant: ['#foodie', '#delicious', '#freshfood', '#localeats', '#foodlover', '#tasty', '#chef', '#dining'],\r\n      bakery: ['#freshbaked', '#artisan', '#homemade', '#bakery', '#pastry', '#bread', '#dessert', '#sweet'],\r\n      fitness: ['#fitness', '#workout', '#health', '#gym', '#strong', '#motivation', '#fitlife', '#training'],\r\n      beauty: ['#beauty', '#skincare', '#makeup', '#glam', '#selfcare', '#beautiful', '#style', '#cosmetics'],\r\n      tech: ['#tech', '#innovation', '#digital', '#software', '#technology', '#startup', '#coding', '#ai'],\r\n      retail: ['#shopping', '#fashion', '#style', '#sale', '#newcollection', '#boutique', '#trendy', '#deals']\r\n    };\r\n\r\n    const baseNiche = nicheMap[businessType.toLowerCase()] || ['#business', '#service', '#quality', '#professional'];\r\n\r\n    // Add service-specific hashtags if provided\r\n    if (services) {\r\n      const serviceWords = services.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const serviceHashtags = serviceWords.slice(0, 3).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      baseNiche.push(...serviceHashtags);\r\n    }\r\n\r\n    return baseNiche.slice(0, 6);\r\n  }\r\n\r\n  /**\r\n   * Get location-based hashtags\r\n   */\r\n  private getLocationHashtags(location: string): string[] {\r\n    const locationParts = location.split(',').map(part => part.trim());\r\n    const hashtags: string[] = [];\r\n\r\n    locationParts.forEach(part => {\r\n      const cleanLocation = part.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '');\r\n      if (cleanLocation.length > 2) {\r\n        hashtags.push(`#${cleanLocation.toLowerCase()}`);\r\n      }\r\n    });\r\n\r\n    // Add generic location hashtags\r\n    hashtags.push('#local', '#community', '#neighborhood');\r\n\r\n    return hashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get community engagement hashtags\r\n   */\r\n  private getCommunityHashtags(businessType: string, targetAudience?: string): string[] {\r\n    const communityHashtags = ['#community', '#local', '#support', '#family', '#friends', '#together', '#love'];\r\n\r\n    if (targetAudience) {\r\n      const audienceWords = targetAudience.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const audienceHashtags = audienceWords.slice(0, 2).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      communityHashtags.push(...audienceHashtags);\r\n    }\r\n\r\n    return communityHashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get seasonal/timely hashtags\r\n   */\r\n  private getSeasonalHashtags(): string[] {\r\n    const now = new Date();\r\n    const month = now.getMonth();\r\n    const day = now.getDate();\r\n\r\n    // Seasonal hashtags based on current time\r\n    const seasonal: Record<number, string[]> = {\r\n      0: ['#newyear', '#january', '#fresh', '#newbeginnings'], // January\r\n      1: ['#february', '#love', '#valentine', '#winter'], // February  \r\n      2: ['#march', '#spring', '#fresh', '#bloom'], // March\r\n      3: ['#april', '#spring', '#easter', '#renewal'], // April\r\n      4: ['#may', '#spring', '#mothers', '#bloom'], // May\r\n      5: ['#june', '#summer', '#fathers', '#sunshine'], // June\r\n      6: ['#july', '#summer', '#vacation', '#hot'], // July\r\n      7: ['#august', '#summer', '#vacation', '#sunny'], // August\r\n      8: ['#september', '#fall', '#autumn', '#backtoschool'], // September\r\n      9: ['#october', '#fall', '#halloween', '#autumn'], // October\r\n      10: ['#november', '#thanksgiving', '#grateful', '#fall'], // November\r\n      11: ['#december', '#christmas', '#holiday', '#winter'] // December\r\n    };\r\n\r\n    return seasonal[month] || ['#today', '#now', '#current'];\r\n  }\r\n\r\n  /**\r\n   * Get platform-specific hashtags\r\n   */\r\n  private getPlatformHashtags(platform: string): string[] {\r\n    const platformHashtags: Record<string, string[]> = {\r\n      instagram: ['#instagram', '#insta', '#ig'],\r\n      facebook: ['#facebook', '#fb', '#social'],\r\n      twitter: ['#twitter', '#tweet', '#x'],\r\n      linkedin: ['#linkedin', '#professional', '#business'],\r\n      tiktok: ['#tiktok', '#tt', '#video']\r\n    };\r\n\r\n    return platformHashtags[platform.toLowerCase()] || ['#social', '#media'];\r\n  }\r\n\r\n  /**\r\n   * Get business-relevant trending hashtags\r\n   */\r\n  private getBusinessTrendingHashtags(businessType: string, platform: string): string[] {\r\n    // This would integrate with real trending APIs in production\r\n    const trendingByBusiness: Record<string, string[]> = {\r\n      restaurant: ['#foodtrends', '#eats2024', '#localfood', '#foodie'],\r\n      fitness: ['#fitness2024', '#healthtrends', '#workout', '#wellness'],\r\n      beauty: ['#beautytrends', '#skincare2024', '#makeup', '#selfcare'],\r\n      tech: ['#tech2024', '#innovation', '#ai', '#digital'],\r\n      retail: ['#fashion2024', '#shopping', '#style', '#trends']\r\n    };\r\n\r\n    return trendingByBusiness[businessType.toLowerCase()] || ['#trending', '#popular', '#new'];\r\n  }\r\n\r\n  /**\r\n   * Optimize hashtag selection for maximum virality\r\n   */\r\n  private optimizeForVirality(hashtags: string[]): string[] {\r\n    // Remove duplicates\r\n    const unique = Array.from(new Set(hashtags));\r\n\r\n    // Sort by estimated engagement potential (simplified scoring)\r\n    const scored = unique.map(tag => ({\r\n      tag,\r\n      score: this.calculateViralScore(tag)\r\n    }));\r\n\r\n    scored.sort((a, b) => b.score - a.score);\r\n\r\n    return scored.slice(0, 15).map(item => item.tag);\r\n  }\r\n\r\n  /**\r\n   * Calculate viral potential score for a hashtag\r\n   */\r\n  private calculateViralScore(hashtag: string): number {\r\n    let score = 0;\r\n\r\n    // High-engagement keywords get bonus points\r\n    const viralKeywords = ['viral', 'trending', 'fyp', 'explore', 'amazing', 'incredible'];\r\n    if (viralKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 10;\r\n    }\r\n\r\n    // Platform-specific hashtags get bonus\r\n    const platformKeywords = ['instagram', 'tiktok', 'reels', 'story'];\r\n    if (platformKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 5;\r\n    }\r\n\r\n    // Local hashtags get moderate bonus\r\n    const localKeywords = ['local', 'community', 'neighborhood'];\r\n    if (localKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 3;\r\n    }\r\n\r\n    // Length penalty (very long hashtags perform worse)\r\n    if (hashtag.length > 20) score -= 2;\r\n    if (hashtag.length > 30) score -= 5;\r\n\r\n    return score + Math.random(); // Add randomness for variety\r\n  }\r\n\r\n  /**\r\n   * Fallback hashtags when trending data fails\r\n   */\r\n  private getFallbackHashtags(businessType: string, location: string, platform: string): ViralHashtagStrategy {\r\n    return {\r\n      trending: ['#trending', '#viral', '#popular', '#new'],\r\n      viral: ['#amazing', '#incredible', '#mustsee', '#wow'],\r\n      niche: [`#${businessType}`, '#quality', '#professional', '#service'],\r\n      location: ['#local', '#community', `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`],\r\n      community: ['#community', '#support', '#family', '#love'],\r\n      seasonal: ['#today', '#now'],\r\n      platform: [`#${platform.toLowerCase()}`],\r\n      total: [\r\n        '#trending', '#viral', `#${businessType}`, '#local', '#community',\r\n        '#amazing', '#quality', '#professional', '#popular', '#new',\r\n        '#support', '#service', `#${platform.toLowerCase()}`, '#today', '#love'\r\n      ]\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const viralHashtagEngine = new ViralHashtagEngine();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAaO,MAAM;IAEX;;GAEC,GACD,MAAM,sBACJ,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACjB,cAAuB,EACQ;QAG/B,IAAI;YACF,yDAAyD;YACzD,MAAM,eAAe,MAAM,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;gBACjE;gBACA;gBACA;gBACA;YACF;YAGA,wCAAwC;YACxC,MAAM,WAAW,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,cAAc;YAC5E,MAAM,QAAQ,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClD,MAAM,QAAQ,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClD,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;YAC/C,MAAM,YAAY,IAAI,CAAC,oBAAoB,CAAC,cAAc;YAC1D,MAAM,WAAW,IAAI,CAAC,mBAAmB;YACzC,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;YAE/C,oCAAoC;YACpC,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;mBAClC,SAAS,KAAK,CAAC,GAAG;mBAClB,MAAM,KAAK,CAAC,GAAG;mBACf,MAAM,KAAK,CAAC,GAAG;mBACf,cAAc,KAAK,CAAC,GAAG;mBACvB,UAAU,KAAK,CAAC,GAAG;mBACnB,SAAS,KAAK,CAAC,GAAG;mBAClB,cAAc,KAAK,CAAC,GAAG;aAC3B;YAGD,OAAO;gBACL;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA,UAAU;gBACV;YACF;QAEF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,UAAU;QAC1D;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,YAAiB,EAAE,YAAoB,EAAE,QAAgB,EAAqB;QAC9G,MAAM,WAAW;eAAI,aAAa,QAAQ;SAAC;QAE3C,0CAA0C;QAC1C,MAAM,mBAAmB,IAAI,CAAC,2BAA2B,CAAC,cAAc;QACxE,SAAS,IAAI,IAAI;QAEjB,4CAA4C;QAC5C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAgB,EAAY;QACzE,MAAM,gBAAgB;YACpB,SAAS;gBAAC;gBAAU;gBAAa;gBAAQ;gBAAY;gBAAa;gBAAY;gBAAe;aAAW;YACxG,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;gBAAU;aAAe;YACpF,QAAQ;gBAAC;gBAAQ;gBAAW;gBAAU;gBAAa;aAAc;YACjE,UAAU;gBAAC;gBAAU;gBAAU;gBAAc;gBAAU;aAAY;YACnE,SAAS;gBAAC;gBAAa;gBAAU;gBAAa;gBAAS;aAAU;YACjE,UAAU;gBAAC;gBAAiB;gBAAa;gBAAe;gBAAW;aAAY;QACjF;QAEA,MAAM,UAAU,cAAc,OAAO,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG;QAC/E,MAAM,mBAAmB,aAAa,CAAC,SAAS,WAAW,GAAiC,IAAI,EAAE;QAElG,OAAO;eAAI;eAAY,iBAAiB,KAAK,CAAC,GAAG;SAAG;IACtD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAiB,EAAY;QAC1E,MAAM,WAAqC;YACzC,YAAY;gBAAC;gBAAW;gBAAc;gBAAc;gBAAc;gBAAc;gBAAU;gBAAS;aAAU;YAC7G,QAAQ;gBAAC;gBAAe;gBAAY;gBAAa;gBAAW;gBAAW;gBAAU;gBAAY;aAAS;YACtG,SAAS;gBAAC;gBAAY;gBAAY;gBAAW;gBAAQ;gBAAW;gBAAe;gBAAY;aAAY;YACvG,QAAQ;gBAAC;gBAAW;gBAAa;gBAAW;gBAAS;gBAAa;gBAAc;gBAAU;aAAa;YACvG,MAAM;gBAAC;gBAAS;gBAAe;gBAAY;gBAAa;gBAAe;gBAAY;gBAAW;aAAM;YACpG,QAAQ;gBAAC;gBAAa;gBAAY;gBAAU;gBAAS;gBAAkB;gBAAa;gBAAW;aAAS;QAC1G;QAEA,MAAM,YAAY,QAAQ,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;YAAY;SAAgB;QAEhH,4CAA4C;QAC5C,IAAI,UAAU;YACZ,MAAM,eAAe,SAAS,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YACzF,MAAM,kBAAkB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACjG,UAAU,IAAI,IAAI;QACpB;QAEA,OAAO,UAAU,KAAK,CAAC,GAAG;IAC5B;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC/D,MAAM,WAAqB,EAAE;QAE7B,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,gBAAgB,KAAK,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ;YAC1E,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,WAAW,IAAI;YACjD;QACF;QAEA,gCAAgC;QAChC,SAAS,IAAI,CAAC,UAAU,cAAc;QAEtC,OAAO,SAAS,KAAK,CAAC,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAQ,qBAAqB,YAAoB,EAAE,cAAuB,EAAY;QACpF,MAAM,oBAAoB;YAAC;YAAc;YAAU;YAAY;YAAW;YAAY;YAAa;SAAQ;QAE3G,IAAI,gBAAgB;YAClB,MAAM,gBAAgB,eAAe,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAChG,MAAM,mBAAmB,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACnG,kBAAkB,IAAI,IAAI;QAC5B;QAEA,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IAEA;;GAEC,GACD,AAAQ,sBAAgC;QACtC,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,QAAQ;QAC1B,MAAM,MAAM,IAAI,OAAO;QAEvB,0CAA0C;QAC1C,MAAM,WAAqC;YACzC,GAAG;gBAAC;gBAAY;gBAAY;gBAAU;aAAiB;YACvD,GAAG;gBAAC;gBAAa;gBAAS;gBAAc;aAAU;YAClD,GAAG;gBAAC;gBAAU;gBAAW;gBAAU;aAAS;YAC5C,GAAG;gBAAC;gBAAU;gBAAW;gBAAW;aAAW;YAC/C,GAAG;gBAAC;gBAAQ;gBAAW;gBAAY;aAAS;YAC5C,GAAG;gBAAC;gBAAS;gBAAW;gBAAY;aAAY;YAChD,GAAG;gBAAC;gBAAS;gBAAW;gBAAa;aAAO;YAC5C,GAAG;gBAAC;gBAAW;gBAAW;gBAAa;aAAS;YAChD,GAAG;gBAAC;gBAAc;gBAAS;gBAAW;aAAgB;YACtD,GAAG;gBAAC;gBAAY;gBAAS;gBAAc;aAAU;YACjD,IAAI;gBAAC;gBAAa;gBAAiB;gBAAa;aAAQ;YACxD,IAAI;gBAAC;gBAAa;gBAAc;gBAAY;aAAU,CAAC,WAAW;QACpE;QAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;YAAC;YAAU;YAAQ;SAAW;IAC1D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,mBAA6C;YACjD,WAAW;gBAAC;gBAAc;gBAAU;aAAM;YAC1C,UAAU;gBAAC;gBAAa;gBAAO;aAAU;YACzC,SAAS;gBAAC;gBAAY;gBAAU;aAAK;YACrC,UAAU;gBAAC;gBAAa;gBAAiB;aAAY;YACrD,QAAQ;gBAAC;gBAAW;gBAAO;aAAS;QACtC;QAEA,OAAO,gBAAgB,CAAC,SAAS,WAAW,GAAG,IAAI;YAAC;YAAW;SAAS;IAC1E;IAEA;;GAEC,GACD,AAAQ,4BAA4B,YAAoB,EAAE,QAAgB,EAAY;QACpF,6DAA6D;QAC7D,MAAM,qBAA+C;YACnD,YAAY;gBAAC;gBAAe;gBAAa;gBAAc;aAAU;YACjE,SAAS;gBAAC;gBAAgB;gBAAiB;gBAAY;aAAY;YACnE,QAAQ;gBAAC;gBAAiB;gBAAiB;gBAAW;aAAY;YAClE,MAAM;gBAAC;gBAAa;gBAAe;gBAAO;aAAW;YACrD,QAAQ;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;QAC5D;QAEA,OAAO,kBAAkB,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;SAAO;IAC5F;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAkB,EAAY;QACxD,oBAAoB;QACpB,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,IAAI;QAElC,8DAA8D;QAC9D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;gBAChC;gBACA,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAEvC,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAe,EAAU;QACnD,IAAI,QAAQ;QAEZ,4CAA4C;QAC5C,MAAM,gBAAgB;YAAC;YAAS;YAAY;YAAO;YAAW;YAAW;SAAa;QACtF,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,uCAAuC;QACvC,MAAM,mBAAmB;YAAC;YAAa;YAAU;YAAS;SAAQ;QAClE,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC7E,SAAS;QACX;QAEA,oCAAoC;QACpC,MAAM,gBAAgB;YAAC;YAAS;YAAa;SAAe;QAC5D,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,oDAAoD;QACpD,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAClC,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAElC,OAAO,QAAQ,KAAK,MAAM,IAAI,6BAA6B;IAC7D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAAoB,EAAE,QAAgB,EAAE,QAAgB,EAAwB;QAC1G,OAAO;YACL,UAAU;gBAAC;gBAAa;gBAAU;gBAAY;aAAO;YACrD,OAAO;gBAAC;gBAAY;gBAAe;gBAAY;aAAO;YACtD,OAAO;gBAAC,CAAC,CAAC,EAAE,cAAc;gBAAE;gBAAY;gBAAiB;aAAW;YACpE,UAAU;gBAAC;gBAAU;gBAAc,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,iBAAiB,IAAI,WAAW,IAAI;aAAC;YAC7F,WAAW;gBAAC;gBAAc;gBAAY;gBAAW;aAAQ;YACzD,UAAU;gBAAC;gBAAU;aAAO;YAC5B,UAAU;gBAAC,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;aAAC;YACxC,OAAO;gBACL;gBAAa;gBAAU,CAAC,CAAC,EAAE,cAAc;gBAAE;gBAAU;gBACrD;gBAAY;gBAAY;gBAAiB;gBAAY;gBACrD;gBAAY;gBAAY,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;gBAAE;gBAAU;aACjE;QACH;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 3500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/dynamic-cta-generator.ts"], "sourcesContent": ["/**\r\n * Dynamic CTA Generator - Creates compelling, conversion-focused call-to-actions\r\n * Uses AI and business intelligence to generate CTAs that drive action\r\n */\r\n\r\nexport interface CTAStrategy {\r\n  primary: string;           // Main CTA for the post\r\n  alternatives: string[];    // Alternative CTAs for A/B testing\r\n  style: string;            // CTA style used (e.g., URGENCY, BENEFIT_FOCUSED)\r\n  reasoning: string;        // Why this CTA was chosen\r\n  platform: string;         // Platform-optimized version\r\n}\r\n\r\nexport class DynamicCTAGenerator {\r\n\r\n  /**\r\n   * Generate dynamic, conversion-focused CTA\r\n   */\r\n  async generateDynamicCTA(\r\n    businessName: string,\r\n    businessType: string,\r\n    location: string,\r\n    platform: string,\r\n    contentGoal: string = 'engagement',\r\n    services?: string,\r\n    targetAudience?: string\r\n  ): Promise<CTAStrategy> {\r\n\r\n\r\n    // Select optimal CTA style based on business context\r\n    const ctaStyle = this.selectOptimalCTAStyle(businessType, platform, contentGoal);\r\n\r\n    // Generate primary CTA\r\n    const primary = this.generateCTAByStyle(ctaStyle, businessName, businessType, location, platform, services);\r\n\r\n    // Generate alternatives for A/B testing\r\n    const alternatives = this.generateAlternativeCTAs(businessName, businessType, location, platform, services);\r\n\r\n    // Get reasoning for CTA choice\r\n    const reasoning = this.getCTAReasoning(ctaStyle, businessType, platform);\r\n\r\n\r\n    return {\r\n      primary,\r\n      alternatives,\r\n      style: ctaStyle,\r\n      reasoning,\r\n      platform\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Select optimal CTA style based on business context\r\n   */\r\n  private selectOptimalCTAStyle(businessType: string, platform: string, contentGoal: string): string {\r\n    const businessCTAMap: Record<string, string[]> = {\r\n      restaurant: ['URGENCY', 'INVITATION', 'SENSORY', 'LOCAL_REFERENCE'],\r\n      bakery: ['SENSORY', 'URGENCY', 'INVITATION', 'COMMUNITY'],\r\n      fitness: ['CHALLENGE', 'BENEFIT_FOCUSED', 'MOTIVATION', 'PERSONAL'],\r\n      beauty: ['TRANSFORMATION', 'CONFIDENCE', 'EXCLUSIVE', 'PERSONAL'],\r\n      retail: ['URGENCY', 'EXCLUSIVE', 'BENEFIT_FOCUSED', 'DISCOVERY'],\r\n      tech: ['INNOVATION', 'BENEFIT_FOCUSED', 'CURIOSITY', 'PROFESSIONAL'],\r\n      service: ['DIRECT_ACTION', 'BENEFIT_FOCUSED', 'TRUST', 'LOCAL_REFERENCE']\r\n    };\r\n\r\n    const platformCTAMap: Record<string, string[]> = {\r\n      instagram: ['VISUAL', 'DISCOVERY', 'COMMUNITY', 'INVITATION'],\r\n      facebook: ['COMMUNITY', 'LOCAL_REFERENCE', 'INVITATION', 'SHARE'],\r\n      twitter: ['URGENCY', 'CURIOSITY', 'DIRECT_ACTION', 'TRENDING'],\r\n      linkedin: ['PROFESSIONAL', 'BENEFIT_FOCUSED', 'NETWORKING', 'EXPERTISE'],\r\n      tiktok: ['CHALLENGE', 'TRENDING', 'VIRAL', 'FUN']\r\n    };\r\n\r\n    const goalCTAMap: Record<string, string[]> = {\r\n      engagement: ['CURIOSITY', 'COMMUNITY', 'INVITATION', 'QUESTION'],\r\n      conversion: ['URGENCY', 'BENEFIT_FOCUSED', 'EXCLUSIVE', 'DIRECT_ACTION'],\r\n      awareness: ['DISCOVERY', 'CURIOSITY', 'SHARE', 'VIRAL'],\r\n      retention: ['COMMUNITY', 'LOYALTY', 'PERSONAL', 'APPRECIATION']\r\n    };\r\n\r\n    // Get possible styles from each category\r\n    const businessStyles = businessCTAMap[businessType.toLowerCase()] || ['DIRECT_ACTION', 'BENEFIT_FOCUSED'];\r\n    const platformStyles = platformCTAMap[platform.toLowerCase()] || ['DIRECT_ACTION'];\r\n    const goalStyles = goalCTAMap[contentGoal.toLowerCase()] || ['ENGAGEMENT'];\r\n\r\n    // Find intersection or pick best match\r\n    const allStyles = [...businessStyles, ...platformStyles, ...goalStyles];\r\n    const styleCounts = allStyles.reduce((acc, style) => {\r\n      acc[style] = (acc[style] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>);\r\n\r\n    // Return style with highest count (most relevant)\r\n    const bestStyle = Object.entries(styleCounts)\r\n      .sort(([, a], [, b]) => b - a)[0][0];\r\n\r\n    return bestStyle;\r\n  }\r\n\r\n  /**\r\n   * Generate CTA based on selected style\r\n   */\r\n  private generateCTAByStyle(\r\n    style: string,\r\n    businessName: string,\r\n    businessType: string,\r\n    location: string,\r\n    platform: string,\r\n    services?: string\r\n  ): string {\r\n\r\n    const timestamp = Date.now();\r\n    const variation = timestamp % 4; // 4 variations per style\r\n\r\n    const ctaTemplates: Record<string, string[]> = {\r\n      URGENCY: [\r\n        `Book now - limited spots!`,\r\n        `Don't wait - call today!`,\r\n        `Limited time offer - act fast!`,\r\n        `Only a few left - grab yours!`\r\n      ],\r\n      INVITATION: [\r\n        `Come experience the difference!`,\r\n        `Visit us this weekend!`,\r\n        `Join our community today!`,\r\n        `See what everyone's talking about!`\r\n      ],\r\n      CHALLENGE: [\r\n        `Try to find better - we dare you!`,\r\n        `Challenge yourself today!`,\r\n        `Beat this quality anywhere!`,\r\n        `Prove us wrong - we're confident!`\r\n      ],\r\n      BENEFIT_FOCUSED: [\r\n        `Get more for your money!`,\r\n        `Save time and hassle!`,\r\n        `Double your results!`,\r\n        `Feel the difference immediately!`\r\n      ],\r\n      COMMUNITY: [\r\n        `Join the ${location} family!`,\r\n        `Be part of something special!`,\r\n        `Connect with your neighbors!`,\r\n        `Share the love with friends!`\r\n      ],\r\n      CURIOSITY: [\r\n        `Discover what makes us different!`,\r\n        `Find out why locals choose us!`,\r\n        `See what the buzz is about!`,\r\n        `Uncover ${location}'s best kept secret!`\r\n      ],\r\n      LOCAL_REFERENCE: [\r\n        `${location}'s favorite spot awaits!`,\r\n        `Proudly serving ${location} families!`,\r\n        `Your neighborhood ${businessType}!`,\r\n        `Where ${location} locals go!`\r\n      ],\r\n      SENSORY: [\r\n        `Taste the difference today!`,\r\n        `Experience pure quality!`,\r\n        `Feel the freshness!`,\r\n        `Savor every moment!`\r\n      ],\r\n      EXCLUSIVE: [\r\n        `VIP treatment awaits you!`,\r\n        `Exclusive access - members only!`,\r\n        `Premium experience guaranteed!`,\r\n        `Elite service, just for you!`\r\n      ],\r\n      DIRECT_ACTION: [\r\n        `Call us now!`,\r\n        `Book your appointment!`,\r\n        `Order online today!`,\r\n        `Get started immediately!`\r\n      ]\r\n    };\r\n\r\n    const templates = ctaTemplates[style] || ctaTemplates.DIRECT_ACTION;\r\n    let cta = templates[variation];\r\n\r\n    // Personalize with business name or location when appropriate\r\n    if (Math.random() > 0.5 && !cta.includes(businessName) && !cta.includes(location)) {\r\n      const personalizations = [\r\n        `at ${businessName}`,\r\n        `with ${businessName}`,\r\n        `- ${businessName}`,\r\n        `@ ${businessName}`\r\n      ];\r\n      const personalization = personalizations[variation % personalizations.length];\r\n      cta = `${cta.replace('!', '')} ${personalization}!`;\r\n    }\r\n\r\n    return cta;\r\n  }\r\n\r\n  /**\r\n   * Generate alternative CTAs for A/B testing\r\n   */\r\n  private generateAlternativeCTAs(\r\n    businessName: string,\r\n    businessType: string,\r\n    location: string,\r\n    platform: string,\r\n    services?: string\r\n  ): string[] {\r\n\r\n    const alternativeStyles = ['URGENCY', 'INVITATION', 'BENEFIT_FOCUSED', 'COMMUNITY', 'CURIOSITY'];\r\n    const alternatives: string[] = [];\r\n\r\n    alternativeStyles.forEach(style => {\r\n      const cta = this.generateCTAByStyle(style, businessName, businessType, location, platform, services);\r\n      alternatives.push(cta);\r\n    });\r\n\r\n    // Remove duplicates and return top 3\r\n    return Array.from(new Set(alternatives)).slice(0, 3);\r\n  }\r\n\r\n  /**\r\n   * Get reasoning for CTA choice\r\n   */\r\n  private getCTAReasoning(style: string, businessType: string, platform: string): string {\r\n    const reasoningMap: Record<string, string> = {\r\n      URGENCY: `Creates immediate action through scarcity and time pressure, effective for ${businessType} conversions`,\r\n      INVITATION: `Builds welcoming community feeling, perfect for local ${businessType} businesses`,\r\n      CHALLENGE: `Engages competitive spirit and confidence, great for ${businessType} differentiation`,\r\n      BENEFIT_FOCUSED: `Highlights clear value proposition, drives ${businessType} decision-making`,\r\n      COMMUNITY: `Leverages local connection and belonging, ideal for neighborhood ${businessType}`,\r\n      CURIOSITY: `Sparks interest and discovery, effective for ${platform} engagement`,\r\n      LOCAL_REFERENCE: `Emphasizes local pride and familiarity, builds ${businessType} trust`,\r\n      SENSORY: `Appeals to emotional and physical experience, perfect for ${businessType}`,\r\n      EXCLUSIVE: `Creates premium positioning and special treatment feeling`,\r\n      DIRECT_ACTION: `Clear, straightforward instruction that drives immediate response`\r\n    };\r\n\r\n    return reasoningMap[style] || `Optimized for ${businessType} on ${platform} to drive engagement and conversions`;\r\n  }\r\n\r\n  /**\r\n   * Generate platform-optimized CTA\r\n   */\r\n  generatePlatformOptimizedCTA(baseCTA: string, platform: string): string {\r\n    const platformOptimizations: Record<string, (cta: string) => string> = {\r\n      instagram: (cta) => `${cta} 📸✨`,\r\n      facebook: (cta) => `${cta} Share with friends! 👥`,\r\n      twitter: (cta) => `${cta} #${new Date().getFullYear()}`,\r\n      linkedin: (cta) => `${cta} Connect with us professionally.`,\r\n      tiktok: (cta) => `${cta} 🔥💯`\r\n    };\r\n\r\n    const optimizer = platformOptimizations[platform.toLowerCase()];\r\n    return optimizer ? optimizer(baseCTA) : baseCTA;\r\n  }\r\n\r\n  /**\r\n   * Generate time-sensitive CTA\r\n   */\r\n  generateTimeSensitiveCTA(businessType: string, location: string): string {\r\n    const now = new Date();\r\n    const hour = now.getHours();\r\n    const day = now.getDay();\r\n    const isWeekend = day === 0 || day === 6;\r\n\r\n    if (hour < 11) {\r\n      return `Start your day right - visit us now!`;\r\n    } else if (hour < 14) {\r\n      return `Perfect lunch break spot - come by!`;\r\n    } else if (hour < 17) {\r\n      return `Afternoon pick-me-up awaits!`;\r\n    } else if (hour < 20) {\r\n      return `End your day on a high note!`;\r\n    } else {\r\n      return `Evening treat - you deserve it!`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate seasonal CTA\r\n   */\r\n  generateSeasonalCTA(businessType: string, location: string): string {\r\n    const now = new Date();\r\n    const month = now.getMonth();\r\n\r\n    const seasonalCTAs: Record<number, string[]> = {\r\n      0: [`New Year, new experiences - try us!`, `Start 2024 right with us!`], // January\r\n      1: [`Warm up with us this February!`, `Love is in the air - visit us!`], // February\r\n      2: [`Spring into action - book now!`, `Fresh start, fresh experience!`], // March\r\n      3: [`April showers bring May flowers - and great service!`, `Spring special awaits!`], // April\r\n      4: [`May we serve you today?`, `Mother's Day special - treat her!`], // May\r\n      5: [`Summer starts here - join us!`, `Father's Day celebration awaits!`], // June\r\n      6: [`Beat the heat with us!`, `Summer vibes, great service!`], // July\r\n      7: [`August special - don't miss out!`, `Late summer treat awaits!`], // August\r\n      8: [`Back to school, back to us!`, `Fall into great service!`], // September\r\n      9: [`October surprise awaits you!`, `Halloween special - spooktacular!`], // October\r\n      10: [`Thanksgiving gratitude - visit us!`, `Give thanks for great service!`], // November\r\n      11: [`Holiday magic awaits you!`, `Christmas special - ho ho ho!`] // December\r\n    };\r\n\r\n    const monthCTAs = seasonalCTAs[month] || [`Visit us today!`, `Experience the difference!`];\r\n    return monthCTAs[Math.floor(Math.random() * monthCTAs.length)];\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const dynamicCTAGenerator = new DynamicCTAGenerator();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAUM,MAAM;IAEX;;GAEC,GACD,MAAM,mBACJ,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,cAAsB,YAAY,EAClC,QAAiB,EACjB,cAAuB,EACD;QAGtB,qDAAqD;QACrD,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,cAAc,UAAU;QAEpE,uBAAuB;QACvB,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,UAAU,cAAc,cAAc,UAAU,UAAU;QAElG,wCAAwC;QACxC,MAAM,eAAe,IAAI,CAAC,uBAAuB,CAAC,cAAc,cAAc,UAAU,UAAU;QAElG,+BAA+B;QAC/B,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,UAAU,cAAc;QAG/D,OAAO;YACL;YACA;YACA,OAAO;YACP;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAAoB,EAAE,QAAgB,EAAE,WAAmB,EAAU;QACjG,MAAM,iBAA2C;YAC/C,YAAY;gBAAC;gBAAW;gBAAc;gBAAW;aAAkB;YACnE,QAAQ;gBAAC;gBAAW;gBAAW;gBAAc;aAAY;YACzD,SAAS;gBAAC;gBAAa;gBAAmB;gBAAc;aAAW;YACnE,QAAQ;gBAAC;gBAAkB;gBAAc;gBAAa;aAAW;YACjE,QAAQ;gBAAC;gBAAW;gBAAa;gBAAmB;aAAY;YAChE,MAAM;gBAAC;gBAAc;gBAAmB;gBAAa;aAAe;YACpE,SAAS;gBAAC;gBAAiB;gBAAmB;gBAAS;aAAkB;QAC3E;QAEA,MAAM,iBAA2C;YAC/C,WAAW;gBAAC;gBAAU;gBAAa;gBAAa;aAAa;YAC7D,UAAU;gBAAC;gBAAa;gBAAmB;gBAAc;aAAQ;YACjE,SAAS;gBAAC;gBAAW;gBAAa;gBAAiB;aAAW;YAC9D,UAAU;gBAAC;gBAAgB;gBAAmB;gBAAc;aAAY;YACxE,QAAQ;gBAAC;gBAAa;gBAAY;gBAAS;aAAM;QACnD;QAEA,MAAM,aAAuC;YAC3C,YAAY;gBAAC;gBAAa;gBAAa;gBAAc;aAAW;YAChE,YAAY;gBAAC;gBAAW;gBAAmB;gBAAa;aAAgB;YACxE,WAAW;gBAAC;gBAAa;gBAAa;gBAAS;aAAQ;YACvD,WAAW;gBAAC;gBAAa;gBAAW;gBAAY;aAAe;QACjE;QAEA,yCAAyC;QACzC,MAAM,iBAAiB,cAAc,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAiB;SAAkB;QACzG,MAAM,iBAAiB,cAAc,CAAC,SAAS,WAAW,GAAG,IAAI;YAAC;SAAgB;QAClF,MAAM,aAAa,UAAU,CAAC,YAAY,WAAW,GAAG,IAAI;YAAC;SAAa;QAE1E,uCAAuC;QACvC,MAAM,YAAY;eAAI;eAAmB;eAAmB;SAAW;QACvE,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;YACzC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI;YACjC,OAAO;QACT,GAAG,CAAC;QAEJ,kDAAkD;QAClD,MAAM,YAAY,OAAO,OAAO,CAAC,aAC9B,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;QAEtC,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBACN,KAAa,EACb,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACT;QAER,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,YAAY,YAAY,GAAG,yBAAyB;QAE1D,MAAM,eAAyC;YAC7C,SAAS;gBACP,CAAC,yBAAyB,CAAC;gBAC3B,CAAC,wBAAwB,CAAC;gBAC1B,CAAC,8BAA8B,CAAC;gBAChC,CAAC,6BAA6B,CAAC;aAChC;YACD,YAAY;gBACV,CAAC,+BAA+B,CAAC;gBACjC,CAAC,sBAAsB,CAAC;gBACxB,CAAC,yBAAyB,CAAC;gBAC3B,CAAC,kCAAkC,CAAC;aACrC;YACD,WAAW;gBACT,CAAC,iCAAiC,CAAC;gBACnC,CAAC,yBAAyB,CAAC;gBAC3B,CAAC,2BAA2B,CAAC;gBAC7B,CAAC,iCAAiC,CAAC;aACpC;YACD,iBAAiB;gBACf,CAAC,wBAAwB,CAAC;gBAC1B,CAAC,qBAAqB,CAAC;gBACvB,CAAC,oBAAoB,CAAC;gBACtB,CAAC,gCAAgC,CAAC;aACnC;YACD,WAAW;gBACT,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC;gBAC9B,CAAC,6BAA6B,CAAC;gBAC/B,CAAC,4BAA4B,CAAC;gBAC9B,CAAC,4BAA4B,CAAC;aAC/B;YACD,WAAW;gBACT,CAAC,iCAAiC,CAAC;gBACnC,CAAC,8BAA8B,CAAC;gBAChC,CAAC,2BAA2B,CAAC;gBAC7B,CAAC,QAAQ,EAAE,SAAS,oBAAoB,CAAC;aAC1C;YACD,iBAAiB;gBACf,GAAG,SAAS,wBAAwB,CAAC;gBACrC,CAAC,gBAAgB,EAAE,SAAS,UAAU,CAAC;gBACvC,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBACpC,CAAC,MAAM,EAAE,SAAS,WAAW,CAAC;aAC/B;YACD,SAAS;gBACP,CAAC,2BAA2B,CAAC;gBAC7B,CAAC,wBAAwB,CAAC;gBAC1B,CAAC,mBAAmB,CAAC;gBACrB,CAAC,mBAAmB,CAAC;aACtB;YACD,WAAW;gBACT,CAAC,yBAAyB,CAAC;gBAC3B,CAAC,gCAAgC,CAAC;gBAClC,CAAC,8BAA8B,CAAC;gBAChC,CAAC,4BAA4B,CAAC;aAC/B;YACD,eAAe;gBACb,CAAC,YAAY,CAAC;gBACd,CAAC,sBAAsB,CAAC;gBACxB,CAAC,mBAAmB,CAAC;gBACrB,CAAC,wBAAwB,CAAC;aAC3B;QACH;QAEA,MAAM,YAAY,YAAY,CAAC,MAAM,IAAI,aAAa,aAAa;QACnE,IAAI,MAAM,SAAS,CAAC,UAAU;QAE9B,8DAA8D;QAC9D,IAAI,KAAK,MAAM,KAAK,OAAO,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,WAAW;YACjF,MAAM,mBAAmB;gBACvB,CAAC,GAAG,EAAE,cAAc;gBACpB,CAAC,KAAK,EAAE,cAAc;gBACtB,CAAC,EAAE,EAAE,cAAc;gBACnB,CAAC,EAAE,EAAE,cAAc;aACpB;YACD,MAAM,kBAAkB,gBAAgB,CAAC,YAAY,iBAAiB,MAAM,CAAC;YAC7E,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,gBAAgB,CAAC,CAAC;QACrD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBACN,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACP;QAEV,MAAM,oBAAoB;YAAC;YAAW;YAAc;YAAmB;YAAa;SAAY;QAChG,MAAM,eAAyB,EAAE;QAEjC,kBAAkB,OAAO,CAAC,CAAA;YACxB,MAAM,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,cAAc,cAAc,UAAU,UAAU;YAC3F,aAAa,IAAI,CAAC;QACpB;QAEA,qCAAqC;QACrC,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,eAAe,KAAK,CAAC,GAAG;IACpD;IAEA;;GAEC,GACD,AAAQ,gBAAgB,KAAa,EAAE,YAAoB,EAAE,QAAgB,EAAU;QACrF,MAAM,eAAuC;YAC3C,SAAS,CAAC,2EAA2E,EAAE,aAAa,YAAY,CAAC;YACjH,YAAY,CAAC,sDAAsD,EAAE,aAAa,WAAW,CAAC;YAC9F,WAAW,CAAC,qDAAqD,EAAE,aAAa,gBAAgB,CAAC;YACjG,iBAAiB,CAAC,2CAA2C,EAAE,aAAa,gBAAgB,CAAC;YAC7F,WAAW,CAAC,iEAAiE,EAAE,cAAc;YAC7F,WAAW,CAAC,6CAA6C,EAAE,SAAS,WAAW,CAAC;YAChF,iBAAiB,CAAC,+CAA+C,EAAE,aAAa,MAAM,CAAC;YACvF,SAAS,CAAC,0DAA0D,EAAE,cAAc;YACpF,WAAW,CAAC,yDAAyD,CAAC;YACtE,eAAe,CAAC,iEAAiE,CAAC;QACpF;QAEA,OAAO,YAAY,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,aAAa,IAAI,EAAE,SAAS,oCAAoC,CAAC;IAClH;IAEA;;GAEC,GACD,6BAA6B,OAAe,EAAE,QAAgB,EAAU;QACtE,MAAM,wBAAiE;YACrE,WAAW,CAAC,MAAQ,GAAG,IAAI,IAAI,CAAC;YAChC,UAAU,CAAC,MAAQ,GAAG,IAAI,uBAAuB,CAAC;YAClD,SAAS,CAAC,MAAQ,GAAG,IAAI,EAAE,EAAE,IAAI,OAAO,WAAW,IAAI;YACvD,UAAU,CAAC,MAAQ,GAAG,IAAI,gCAAgC,CAAC;YAC3D,QAAQ,CAAC,MAAQ,GAAG,IAAI,KAAK,CAAC;QAChC;QAEA,MAAM,YAAY,qBAAqB,CAAC,SAAS,WAAW,GAAG;QAC/D,OAAO,YAAY,UAAU,WAAW;IAC1C;IAEA;;GAEC,GACD,yBAAyB,YAAoB,EAAE,QAAgB,EAAU;QACvE,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,QAAQ;QACzB,MAAM,MAAM,IAAI,MAAM;QACtB,MAAM,YAAY,QAAQ,KAAK,QAAQ;QAEvC,IAAI,OAAO,IAAI;YACb,OAAO,CAAC,oCAAoC,CAAC;QAC/C,OAAO,IAAI,OAAO,IAAI;YACpB,OAAO,CAAC,mCAAmC,CAAC;QAC9C,OAAO,IAAI,OAAO,IAAI;YACpB,OAAO,CAAC,4BAA4B,CAAC;QACvC,OAAO,IAAI,OAAO,IAAI;YACpB,OAAO,CAAC,4BAA4B,CAAC;QACvC,OAAO;YACL,OAAO,CAAC,+BAA+B,CAAC;QAC1C;IACF;IAEA;;GAEC,GACD,oBAAoB,YAAoB,EAAE,QAAgB,EAAU;QAClE,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,QAAQ;QAE1B,MAAM,eAAyC;YAC7C,GAAG;gBAAC,CAAC,mCAAmC,CAAC;gBAAE,CAAC,yBAAyB,CAAC;aAAC;YACvE,GAAG;gBAAC,CAAC,8BAA8B,CAAC;gBAAE,CAAC,8BAA8B,CAAC;aAAC;YACvE,GAAG;gBAAC,CAAC,8BAA8B,CAAC;gBAAE,CAAC,8BAA8B,CAAC;aAAC;YACvE,GAAG;gBAAC,CAAC,oDAAoD,CAAC;gBAAE,CAAC,sBAAsB,CAAC;aAAC;YACrF,GAAG;gBAAC,CAAC,uBAAuB,CAAC;gBAAE,CAAC,iCAAiC,CAAC;aAAC;YACnE,GAAG;gBAAC,CAAC,6BAA6B,CAAC;gBAAE,CAAC,gCAAgC,CAAC;aAAC;YACxE,GAAG;gBAAC,CAAC,sBAAsB,CAAC;gBAAE,CAAC,4BAA4B,CAAC;aAAC;YAC7D,GAAG;gBAAC,CAAC,gCAAgC,CAAC;gBAAE,CAAC,yBAAyB,CAAC;aAAC;YACpE,GAAG;gBAAC,CAAC,2BAA2B,CAAC;gBAAE,CAAC,wBAAwB,CAAC;aAAC;YAC9D,GAAG;gBAAC,CAAC,4BAA4B,CAAC;gBAAE,CAAC,iCAAiC,CAAC;aAAC;YACxE,IAAI;gBAAC,CAAC,kCAAkC,CAAC;gBAAE,CAAC,8BAA8B,CAAC;aAAC;YAC5E,IAAI;gBAAC,CAAC,yBAAyB,CAAC;gBAAE,CAAC,6BAA6B,CAAC;aAAC,CAAC,WAAW;QAChF;QAEA,MAAM,YAAY,YAAY,CAAC,MAAM,IAAI;YAAC,CAAC,eAAe,CAAC;YAAE,CAAC,0BAA0B,CAAC;SAAC;QAC1F,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IAChE;AACF;AAGO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 3875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/creative-enhancement.ts"], "sourcesContent": ["/**\r\n * Enhanced Business Intelligence & Strategic Content Generation System\r\n * Replaces generic templates with business-specific insights and strategic planning\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport { viralHashtagEngine } from './viral-hashtag-engine';\r\nimport { dynamicCTAGenerator } from './dynamic-cta-generator';\r\n\r\n// Word Repetition Removal Function - Fixes issues like \"buy now now pay later\"\r\nfunction removeWordRepetitions(text: string): string {\r\n  // Simple approach: split by spaces and check for consecutive duplicate words\r\n  const words = text.split(/\\s+/);\r\n  const cleanedWords: string[] = [];\r\n\r\n  for (let i = 0; i < words.length; i++) {\r\n    const currentWord = words[i];\r\n    const previousWord = cleanedWords[cleanedWords.length - 1];\r\n\r\n    // Skip if current word is the same as previous word (case-insensitive)\r\n    // Only for actual words (not empty strings)\r\n    if (currentWord && previousWord &&\r\n      currentWord.toLowerCase() === previousWord.toLowerCase() &&\r\n      currentWord.trim().length > 0) {\r\n      continue; // Skip this duplicate word\r\n    }\r\n\r\n    cleanedWords.push(currentWord);\r\n  }\r\n\r\n  return cleanedWords.join(' ');\r\n}\r\n\r\n// Shared AI initialization to avoid duplicate variable names\r\nfunction initializeAI() {\r\n  const geminiApiKey = process.env.GOOGLE_AI_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.GEMINI_API_KEY;\r\n  const genAI = new GoogleGenerativeAI(geminiApiKey!);\r\n  // Use the same model as Revo 1.0 service for consistency\r\n  return genAI.getGenerativeModel({\r\n    model: 'gemini-2.5-flash-image-preview',\r\n    generationConfig: {\r\n      temperature: 0.9, // Higher temperature for more creative, varied responses\r\n      topP: 0.95,\r\n      topK: 40,\r\n      maxOutputTokens: 4096,\r\n    }\r\n  });\r\n}\r\n\r\n// Dynamic approach instructions to force variety\r\nfunction getApproachInstructions(approach: string, businessName: string, location: string, creativityBoost: number): string {\r\n  switch (approach) {\r\n    case 'DIRECT_BENEFIT':\r\n      return `HEADLINES: Lead with specific benefit. Example: \"8g Protein Per Cookie\" SUBHEADLINES: Expand with business details. Example: \"Finally snacks that fuel kids properly - made fresh daily in ${location}\" CAPTIONS: Full benefit story with RSS/business data.`;\r\n\r\n    case 'SOCIAL_PROOF':\r\n      return `HEADLINES: Reference community adoption. Example: \"200+ ${location} Families Agree\" SUBHEADLINES: Add business specifics. Example: \"Our protein cookies beat sugar crashes every time\" CAPTIONS: Full social proof story with testimonials and business intelligence.`;\r\n\r\n    case 'PROBLEM_SOLUTION':\r\n      return `HEADLINES: State the problem. Example: \"Sugar Crashes Ruining Snacktime\" SUBHEADLINES: Present solution. Example: \"${businessName}'s protein cookies keep energy steady for hours\" CAPTIONS: Full problem-solution narrative with business details.`;\r\n\r\n    case 'LOCAL_INSIDER':\r\n      return `HEADLINES: Use local insider knowledge. Example: \"${location} Parents Secret Weapon\" SUBHEADLINES: Add business insider details. Example: \"What 500+ local families know about our cookies\" CAPTIONS: Full insider story with local references and business intelligence.`;\r\n\r\n    case 'URGENCY_SCARCITY':\r\n      return `HEADLINES: Create real urgency. Example: \"Only 50 Packs Left\" SUBHEADLINES: Add business context. Example: \"This week's batch selling faster than expected\" CAPTIONS: Full urgency story with business details and RSS trends.`;\r\n\r\n    case 'QUESTION_HOOK':\r\n      return `HEADLINES: Ask specific question. Example: \"Tired of Sugar Crashes?\" SUBHEADLINES: Hint at solution. Example: \"${businessName} has the protein-packed answer parents love\" CAPTIONS: Full question-answer story with business intelligence.`;\r\n\r\n    case 'STATISTIC_LEAD':\r\n      return `HEADLINES: Lead with business statistic. Example: \"95% Same-Day Fix Rate\" SUBHEADLINES: Add context. Example: \"Our certified technicians solve most issues within hours\" CAPTIONS: Full statistic story with business details and proof.`;\r\n\r\n    case 'STORY_ANGLE':\r\n      return `HEADLINES: Start story hook. Example: \"Local Baker's Secret Recipe\" SUBHEADLINES: Continue story. Example: \"Three generations of ${location} families can't be wrong\" CAPTIONS: Full story with business history and customer experiences.`;\r\n\r\n    case 'COMPARISON':\r\n      return `HEADLINES: Set up comparison. Example: \"Better Than Downtown Options\" SUBHEADLINES: Specify difference. Example: \"Same quality, half the price, right in ${location}\" CAPTIONS: Full comparison with business advantages and local benefits.`;\r\n\r\n    case 'NEWS_TREND':\r\n      return `HEADLINES: Connect to current news/trends. Example: \"Holiday Rush Solution Found\" SUBHEADLINES: Add business connection. Example: \"${businessName} handles your busiest season stress-free\" CAPTIONS: Full trend connection with RSS data and business solutions.`;\r\n\r\n    default:\r\n      return `Create unique content that could only apply to ${businessName} in ${location}. Be specific and authentic.`;\r\n  }\r\n}\r\n\r\n// Dynamic CTA style instructions to force variety\r\nfunction getCtaStyleInstructions(style: string, businessName: string, location: string): string {\r\n  switch (style) {\r\n    case 'DIRECT_ACTION':\r\n      return `Use action verbs specific to the business. Example: \"Grab your protein cookies today\" NOT generic \"Shop now\".`;\r\n\r\n    case 'INVITATION':\r\n      return `Sound like a personal invitation from ${businessName}. Example: \"Come taste what ${location} is talking about\" NOT generic invites.`;\r\n\r\n    case 'CHALLENGE':\r\n      return `Challenge the customer to try something. Example: \"Find better cookies - we dare you\" NOT generic challenges.`;\r\n\r\n    case 'BENEFIT_FOCUSED':\r\n      return `Lead with the specific benefit. Example: \"Get 8g protein per bite\" NOT generic benefits.`;\r\n\r\n    case 'COMMUNITY':\r\n      return `Reference the ${location} community. Example: \"Join 200+ ${location} families\" NOT generic community language.`;\r\n\r\n    case 'URGENCY':\r\n      return `Create real urgency with specifics. Example: \"Only 50 left this week\" NOT generic \"limited time\".`;\r\n\r\n    case 'CURIOSITY':\r\n      return `Make them curious about something specific. Example: \"See why kids ask for seconds\" NOT generic curiosity.`;\r\n\r\n    case 'LOCAL_REFERENCE':\r\n      return `Use actual ${location} references. Example: \"Better than Main Street bakery\" NOT generic local references.`;\r\n\r\n    case 'PERSONAL':\r\n      return `Sound personal and direct. Example: \"Your kids will thank you\" NOT generic personal language.`;\r\n\r\n    case 'EXCLUSIVE':\r\n      return `Make it feel exclusive to ${businessName}. Example: \"Only at ${businessName}\" NOT generic exclusivity.`;\r\n\r\n    default:\r\n      return `Create a unique CTA that could only apply to ${businessName} in ${location}.`;\r\n  }\r\n}\r\n\r\n// Business Intelligence System - Deep Market Understanding\r\nexport const BUSINESS_INTELLIGENCE_SYSTEM = {\r\n  industryInsights: {\r\n    'restaurant': {\r\n      trends: ['farm-to-table', 'fusion cuisine', 'sustainable dining', 'local ingredients', 'chef-driven menus', 'wine pairing', 'seasonal specialties'],\r\n      challenges: ['food costs', 'staff retention', 'customer loyalty', 'online reviews', 'seasonal fluctuations', 'competition from chains'],\r\n      opportunities: ['private dining', 'catering services', 'cooking classes', 'wine tastings', 'local partnerships'],\r\n      uniqueValue: ['chef expertise', 'local sourcing', 'authentic recipes', 'atmosphere', 'service quality'],\r\n      customerPainPoints: ['long wait times', 'expensive prices', 'limited options', 'poor service', 'generic food'],\r\n      successMetrics: ['repeat customers', 'online reviews', 'word-of-mouth', 'reservations', 'social media engagement'],\r\n      localCompetition: ['chain restaurants', 'fast food', 'other local restaurants', 'food trucks', 'delivery services'],\r\n      seasonalOpportunities: ['summer outdoor dining', 'winter comfort food', 'holiday specials', 'local events', 'weather-based menus']\r\n    },\r\n    'technology': {\r\n      trends: ['AI integration', 'automation', 'cloud solutions', 'cybersecurity', 'digital transformation', 'remote work tools'],\r\n      challenges: ['rapid change', 'skill gaps', 'security', 'scalability', 'competition', 'client retention'],\r\n      opportunities: ['consulting services', 'custom development', 'training programs', 'maintenance contracts', 'upgrades'],\r\n      uniqueValue: ['technical expertise', 'local support', 'custom solutions', 'ongoing partnership', 'industry knowledge'],\r\n      customerPainPoints: ['complex technology', 'high costs', 'poor support', 'outdated systems', 'security concerns'],\r\n      successMetrics: ['client satisfaction', 'project completion', 'referrals', 'long-term contracts', 'technical outcomes'],\r\n      localCompetition: ['large tech companies', 'freelancers', 'other local tech firms', 'national chains', 'online services'],\r\n      seasonalOpportunities: ['year-end planning', 'tax season', 'new fiscal year', 'back-to-school', 'holiday e-commerce']\r\n    },\r\n    'healthcare': {\r\n      trends: ['telemedicine', 'preventive care', 'patient experience', 'digital health', 'personalized medicine', 'wellness focus'],\r\n      challenges: ['regulations', 'patient trust', 'technology adoption', 'insurance complexity', 'staff shortages'],\r\n      opportunities: ['preventive programs', 'specialized services', 'wellness coaching', 'community outreach', 'partnerships'],\r\n      uniqueValue: ['medical expertise', 'personalized care', 'local accessibility', 'trusted relationships', 'comprehensive services'],\r\n      customerPainPoints: ['long wait times', 'high costs', 'complex insurance', 'poor communication', 'impersonal care'],\r\n      successMetrics: ['patient outcomes', 'satisfaction scores', 'referrals', 'community trust', 'health improvements'],\r\n      localCompetition: ['hospitals', 'other clinics', 'urgent care centers', 'specialists', 'online health services'],\r\n      seasonalOpportunities: ['flu season', 'summer wellness', 'back-to-school checkups', 'holiday stress', 'new year resolutions']\r\n    },\r\n    'fitness': {\r\n      trends: ['home workouts', 'personal training', 'group classes', 'mind-body connection', 'nutrition integration', 'wearable tech'],\r\n      challenges: ['member retention', 'seasonal fluctuations', 'competition', 'staff turnover', 'facility costs'],\r\n      opportunities: ['online programs', 'corporate wellness', 'specialized training', 'nutrition coaching', 'community events'],\r\n      uniqueValue: ['expert trainers', 'community atmosphere', 'personalized programs', 'convenient location', 'proven results'],\r\n      customerPainPoints: ['lack of motivation', 'time constraints', 'intimidation', 'poor results', 'expensive memberships'],\r\n      successMetrics: ['member retention', 'goal achievement', 'referrals', 'class attendance', 'community engagement'],\r\n      localCompetition: ['other gyms', 'home equipment', 'online programs', 'personal trainers', 'sports clubs'],\r\n      seasonalOpportunities: ['new year resolutions', 'summer body prep', 'holiday fitness', 'back-to-school', 'weather-based activities']\r\n    },\r\n    'retail': {\r\n      trends: ['omnichannel', 'personalization', 'sustainability', 'local sourcing', 'experiential shopping', 'community focus'],\r\n      challenges: ['online competition', 'inventory management', 'customer experience', 'seasonal sales', 'staff training'],\r\n      opportunities: ['online presence', 'local partnerships', 'loyalty programs', 'events', 'personal shopping'],\r\n      uniqueValue: ['curated selection', 'personal service', 'local knowledge', 'quality products', 'community connection'],\r\n      customerPainPoints: ['limited selection', 'high prices', 'poor service', 'inconvenient hours', 'lack of expertise'],\r\n      successMetrics: ['sales growth', 'customer loyalty', 'repeat purchases', 'word-of-mouth', 'community engagement'],\r\n      localCompetition: ['online retailers', 'big box stores', 'other local shops', 'malls', 'direct sales'],\r\n      seasonalOpportunities: ['holiday shopping', 'back-to-school', 'summer sales', 'seasonal products', 'local events']\r\n    },\r\n    'real-estate': {\r\n      trends: ['virtual tours', 'digital marketing', 'local expertise', 'investment focus', 'sustainability', 'smart homes'],\r\n      challenges: ['market fluctuations', 'competition', 'client acquisition', 'market knowledge', 'technology adoption'],\r\n      opportunities: ['investment properties', 'property management', 'consulting services', 'local partnerships', 'specialized markets'],\r\n      uniqueValue: ['local expertise', 'market knowledge', 'personal service', 'proven track record', 'community connections'],\r\n      customerPainPoints: ['high fees', 'poor communication', 'lack of expertise', 'market uncertainty', 'slow process'],\r\n      successMetrics: ['sales volume', 'client satisfaction', 'referrals', 'market share', 'repeat clients'],\r\n      localCompetition: ['other agents', 'online platforms', 'national companies', 'for-sale-by-owner', 'investors'],\r\n      seasonalOpportunities: ['spring market', 'summer families', 'fall investors', 'winter deals', 'holiday moves']\r\n    },\r\n    'automotive': {\r\n      trends: ['electric vehicles', 'digital services', 'sustainability', 'convenience', 'technology integration', 'online sales'],\r\n      challenges: ['parts shortages', 'technician shortage', 'technology changes', 'competition', 'customer expectations'],\r\n      opportunities: ['EV services', 'mobile repair', 'fleet services', 'specialized repairs', 'maintenance programs'],\r\n      uniqueValue: ['expert technicians', 'honest service', 'convenient location', 'quality parts', 'warranty support'],\r\n      customerPainPoints: ['expensive repairs', 'poor service', 'unreliable work', 'long wait times', 'hidden costs'],\r\n      successMetrics: ['customer satisfaction', 'repeat business', 'referrals', 'online reviews', 'service quality'],\r\n      localCompetition: ['dealerships', 'other repair shops', 'chain stores', 'mobile services', 'online parts'],\r\n      seasonalOpportunities: ['winter preparation', 'summer road trips', 'back-to-school', 'holiday travel', 'seasonal maintenance']\r\n    },\r\n    'beauty': {\r\n      trends: ['clean beauty', 'personalization', 'sustainability', 'wellness integration', 'technology', 'inclusivity'],\r\n      challenges: ['product costs', 'staff retention', 'trend changes', 'competition', 'client retention'],\r\n      opportunities: ['online services', 'product sales', 'membership programs', 'events', 'corporate services'],\r\n      uniqueValue: ['expert stylists', 'quality products', 'personalized service', 'convenient location', 'trend knowledge'],\r\n      customerPainPoints: ['high costs', 'poor results', 'long appointments', 'limited availability', 'product damage'],\r\n      successMetrics: ['client retention', 'referrals', 'online reviews', 'service quality', 'product sales'],\r\n      localCompetition: ['salons', 'chain stores', 'mobile services', 'online products', 'other beauty professionals'],\r\n      seasonalOpportunities: ['wedding season', 'holiday parties', 'summer styles', 'back-to-school', 'special events']\r\n    }\r\n  },\r\n\r\n  audiencePsychology: {\r\n    motivations: ['success', 'security', 'convenience', 'status', 'belonging', 'growth', 'health', 'savings', 'recognition'],\r\n    painPoints: ['time constraints', 'budget concerns', 'trust issues', 'complexity', 'uncertainty', 'frustration', 'stress'],\r\n    aspirations: ['better life', 'success', 'recognition', 'peace of mind', 'efficiency', 'independence', 'security'],\r\n    communication: ['clear benefits', 'social proof', 'emotional connection', 'practical value', 'expertise', 'trust']\r\n  },\r\n\r\n  // New: Strategic Content Planning\r\n  contentStrategy: {\r\n    'awareness': {\r\n      goal: 'Introduce business and build recognition',\r\n      approach: 'Educational, informative, community-focused',\r\n      contentTypes: ['industry insights', 'local news', 'educational tips', 'community stories'],\r\n      emotionalTone: 'helpful, informative, community-minded'\r\n    },\r\n    'consideration': {\r\n      goal: 'Build trust and demonstrate expertise',\r\n      approach: 'Problem-solving, expertise demonstration, social proof',\r\n      contentTypes: ['case studies', 'expert tips', 'customer stories', 'industry knowledge'],\r\n      emotionalTone: 'expert, trustworthy, helpful'\r\n    },\r\n    'conversion': {\r\n      goal: 'Drive action and sales',\r\n      approach: 'Urgency, offers, clear benefits, strong CTAs',\r\n      contentTypes: ['special offers', 'limited time deals', 'clear benefits', 'action-oriented content'],\r\n      emotionalTone: 'urgent, compelling, confident'\r\n    },\r\n    'retention': {\r\n      goal: 'Keep existing customers engaged',\r\n      approach: 'Value-added content, community building, ongoing support',\r\n      contentTypes: ['loyalty programs', 'exclusive content', 'community events', 'ongoing value'],\r\n      emotionalTone: 'appreciative, supportive, community-focused'\r\n    }\r\n  }\r\n};\r\n\r\n// Strategic Content Planning System\r\nexport class StrategicContentPlanner {\r\n  static generateBusinessSpecificContent(\r\n    businessType: string,\r\n    businessName: string,\r\n    location: string,\r\n    businessDetails: any,\r\n    platform: string,\r\n    contentGoal: 'awareness' | 'consideration' | 'conversion' | 'retention' = 'awareness'\r\n  ) {\r\n    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n      BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n    const strategy = BUSINESS_INTELLIGENCE_SYSTEM.contentStrategy[contentGoal];\r\n\r\n    // Analyze business strengths and opportunities\r\n    const businessStrengths = this.analyzeBusinessStrengths(businessDetails, industry);\r\n    const marketOpportunities = this.identifyMarketOpportunities(industry, location);\r\n    const customerPainPoints = this.mapCustomerPainPoints(industry, businessStrengths);\r\n\r\n    return {\r\n      strategy: strategy,\r\n      businessType: businessType,\r\n      businessStrengths,\r\n      marketOpportunities,\r\n      customerPainPoints,\r\n      contentAngle: this.determineContentAngle(contentGoal, businessStrengths, marketOpportunities),\r\n      emotionalHook: this.selectEmotionalHook(contentGoal, customerPainPoints),\r\n      valueProposition: this.craftValueProposition(businessStrengths, customerPainPoints),\r\n      localRelevance: this.createLocalRelevance(location, industry, businessDetails)\r\n    };\r\n  }\r\n\r\n  private static analyzeBusinessStrengths(businessDetails: any, industry: any) {\r\n    const strengths = [];\r\n\r\n    if (businessDetails.experience) strengths.push(`${businessDetails.experience} years of experience`);\r\n    if (businessDetails.expertise) strengths.push(`specialized in ${businessDetails.expertise}`);\r\n    if (businessDetails.awards) strengths.push(`award-winning ${businessDetails.awards}`);\r\n    if (businessDetails.certifications) strengths.push(`certified in ${businessDetails.certifications}`);\r\n    if (businessDetails.uniqueServices) strengths.push(`unique ${businessDetails.uniqueServices} services`);\r\n\r\n    // Add industry-specific strengths\r\n    strengths.push(...industry.uniqueValue.slice(0, 3));\r\n\r\n    return strengths;\r\n  }\r\n\r\n  private static identifyMarketOpportunities(industry: any, location: string) {\r\n    return industry.seasonalOpportunities.map(opportunity =>\r\n      `${opportunity} in ${location}`\r\n    ).slice(0, 3);\r\n  }\r\n\r\n  private static mapCustomerPainPoints(industry: any, businessStrengths: string[]) {\r\n    return industry.customerPainPoints.filter(painPoint =>\r\n      businessStrengths.some(strength =>\r\n        strength.toLowerCase().includes(painPoint.toLowerCase().replace(/\\s+/g, ''))\r\n      )\r\n    ).slice(0, 3);\r\n  }\r\n\r\n  private static determineContentAngle(\r\n    contentGoal: string,\r\n    businessStrengths: string[],\r\n    marketOpportunities: string[]\r\n  ) {\r\n    const angles = {\r\n      'awareness': ['educational', 'community', 'industry insights'],\r\n      'consideration': ['problem-solving', 'expertise', 'social proof'],\r\n      'conversion': ['benefits', 'offers', 'urgency'],\r\n      'retention': ['value-added', 'community', 'exclusive']\r\n    };\r\n\r\n    return angles[contentGoal] || angles['awareness'];\r\n  }\r\n\r\n  private static selectEmotionalHook(\r\n    contentGoal: string,\r\n    customerPainPoints: string[]\r\n  ) {\r\n    const hooks = {\r\n      'awareness': ['curiosity', 'community pride', 'local knowledge'],\r\n      'consideration': ['frustration relief', 'trust building', 'expertise recognition'],\r\n      'conversion': ['urgency', 'excitement', 'confidence'],\r\n      'retention': ['appreciation', 'belonging', 'exclusive access']\r\n    };\r\n\r\n    return hooks[contentGoal] || hooks['awareness'];\r\n  }\r\n\r\n  private static craftValueProposition(businessStrengths: string[], customerPainPoints: string[]) {\r\n    if (businessStrengths.length === 0 || customerPainPoints.length === 0) {\r\n      return 'Quality service and expertise';\r\n    }\r\n\r\n    const strength = businessStrengths[0];\r\n    const painPoint = customerPainPoints[0];\r\n\r\n    return `We solve ${painPoint} with ${strength}`;\r\n  }\r\n\r\n  private static createLocalRelevance(location: string, industry: any, businessDetails: any) {\r\n    return {\r\n      localMarket: `${location} market insights`,\r\n      communityConnection: `${location} community focus`,\r\n      localCompetition: `Understanding ${location} competition`,\r\n      localOpportunities: industry.seasonalOpportunities.map(opp => `${opp} in ${location}`)\r\n    };\r\n  }\r\n}\r\n\r\n// Business-Specific Headline Generator - AI-Powered Dynamic Generation\r\nexport async function generateBusinessSpecificHeadline(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  businessDetails: any,\r\n  platform: string,\r\n  contentGoal: 'awareness' | 'consideration' | 'conversion' | 'retention' = 'awareness',\r\n  trendingData?: any,\r\n  businessIntelligence?: any\r\n): Promise<{ headline: string; approach: string; emotionalImpact: string }> {\r\n\r\n  const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(\r\n    businessType, businessName, location, businessDetails, platform, contentGoal\r\n  );\r\n\r\n  const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n    BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n  // Initialize AI generation capability\r\n  const model = initializeAI();\r\n\r\n  // Create dynamic AI prompt for headline generation with RSS trends and regional marketing intelligence\r\n  const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];\r\n  const trendingHashtags = trendingData?.hashtags?.slice(0, 3) || [];\r\n  const regionalLanguage = getRegionalLanguageStyle(location);\r\n  const marketingStyle = getRegionalMarketingStyle(location);\r\n\r\n  const prompt = `You are a brilliant local marketing expert who deeply understands ${location} culture, language, and market dynamics. You stay updated with current trends and know exactly how businesses in ${location} market themselves successfully.\r\n\r\nBUSINESS INTELLIGENCE:\r\n- Business: ${businessName} (${businessType})\r\n- Location: ${location}\r\n- Experience: ${businessDetails.experience || 'established business'}\r\n- Specialties: ${businessDetails.expertise || businessDetails.services || 'professional services'}\r\n- Target Market: ${businessDetails.targetAudience || 'local community'}\r\n- Marketing Goal: ${contentGoal}\r\n\r\nCURRENT TRENDING CONTEXT (Use these insights to make content relevant):\r\n- Trending Keywords: ${trendingKeywords.join(', ') || 'quality, authentic, local, fresh, community'}\r\n- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality'}\r\n- Regional Marketing Style: ${marketingStyle}\r\n- Local Language Tone: ${regionalLanguage}\r\n\r\nLOCAL MARKET INTELLIGENCE:\r\n- Industry Trends: ${industry.trends.slice(0, 3).join(', ')}\r\n- Competitive Advantages: ${industry.uniqueValue.slice(0, 2).join(', ')}\r\n- Market Opportunities: ${industry.opportunities.slice(0, 2).join(', ')}\r\n- How locals in ${location} talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}\r\n\r\nREGIONAL MARKETING STRATEGY:\r\nYou understand that in ${location}, people respond to ${marketingStyle} marketing. Use the trending keywords naturally and speak like locals do. Create content that feels authentic to ${location} culture and current market trends.\r\n\r\nCONVERSION PSYCHOLOGY REQUIREMENTS:\r\n- Maximum 5 words that trigger immediate desire to try/buy\r\n- Use psychological triggers: scarcity, exclusivity, curiosity, FOMO\r\n- Create urgency and desire - make people think \"I NEED this NOW\"\r\n- Sound like a successful local marketer who knows conversion psychology\r\n- Incorporate trending elements naturally (don't force them)\r\n- Use language patterns that drive action in ${location}\r\n- Focus on what makes people instantly want to experience ${businessName}\r\n- Create curiosity gaps that make people want to know more\r\n\r\nCONVERSION-FOCUSED EXAMPLES (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):\r\n- \"Secret Recipe Finally Revealed\" (curiosity + exclusivity)\r\n- \"Last Batch This Week\" (scarcity + urgency)\r\n- \"Addictive Flavors Warning Inside\" (intrigue + benefit)\r\n- \"Hidden Gem Locals Obsess\" (social proof + exclusivity)\r\n- \"Revolutionary Taste Experience Awaits\" (innovation + anticipation)\r\n\r\nCRITICAL ANTI-REPETITION INSTRUCTIONS:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before\r\n❌ AVOID any pattern that sounds like a template or formula\r\n\r\nIMPORTANT: Generate ONLY ONE headline, not multiple options or lists.\r\nDo NOT write \"Here are headlines\" or provide multiple choices.\r\nGenerate ONE unique headline that makes people instantly want to try ${businessName}. Focus on conversion, not just awareness.\r\nMake it so specific to ${businessName} in ${location} that it could never be used for another business.`;\r\n\r\n  try {\r\n    // Add unique generation context to prevent repetitive responses\r\n    const uniqueContext = `\\n\\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n    This generation must be completely different from any previous generation.\r\n    Use this unique context to ensure fresh, original content that has never been generated before.\r\n\r\n    CRITICAL WORD REPETITION RULES:\r\n    - NEVER repeat the same word consecutively (e.g., \"buy now now pay\" should be \"buy now pay\")\r\n    - Check each sentence for duplicate adjacent words before finalizing\r\n    - If you write \"now now\" or \"the the\" or any repeated word, remove the duplicate\r\n    - Read your output carefully to ensure no word appears twice in a row`;\r\n\r\n    const result = await model.generateContent(prompt + uniqueContext);\r\n    let headline = result.response.text().trim();\r\n\r\n    // Post-process to remove word repetitions\r\n    headline = removeWordRepetitions(headline);\r\n\r\n    // Add randomization to approach and emotional impact to ensure variety\r\n    const approaches = ['strategic', 'creative', 'authentic', 'bold', 'community-focused', 'innovative'];\r\n    const emotions = ['engaging', 'inspiring', 'trustworthy', 'exciting', 'confident', 'welcoming'];\r\n\r\n    return {\r\n      headline: headline,\r\n      approach: approaches[Math.floor(Math.random() * approaches.length)],\r\n      emotionalImpact: emotions[Math.floor(Math.random() * emotions.length)]\r\n    };\r\n  } catch (error) {\r\n\r\n    // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback\r\n    try {\r\n\r\n      const simplifiedHeadlinePrompt = `Create a unique 5-word headline for ${businessName}, a ${businessType} in ${location}.\r\n\r\nMake it:\r\n- Conversion-focused (makes people want to try it NOW)\r\n- Different from typical marketing words\r\n- Uses psychological triggers like scarcity, urgency, or exclusivity\r\n- Locally relevant to ${location}\r\n\r\nCRITICAL ANTI-REPETITION RULES:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before\r\n\r\nJust return the headline, nothing else.`;\r\n\r\n      // Add unique generation context to headline retry as well\r\n      const headlineRetryContext = `\\n\\nUNIQUE HEADLINE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n      This headline retry must be completely different and avoid repetitive patterns.`;\r\n\r\n      const retryResult = await model.generateContent(simplifiedHeadlinePrompt + headlineRetryContext);\r\n      const retryHeadline = retryResult.response.text().trim();\r\n\r\n\r\n      return {\r\n        headline: retryHeadline,\r\n        approach: 'ai-retry-generated',\r\n        emotionalImpact: 'conversion-focused'\r\n      };\r\n\r\n    } catch (retryError) {\r\n\r\n      // EMERGENCY AI GENERATION - Ultra Simple Prompt\r\n      try {\r\n        const emergencyPrompt = `Write a catchy 5-word headline for ${businessName} in ${location}. Make it unique and compelling.\r\n\r\nCRITICAL ANTI-REPETITION RULES:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before`;\r\n\r\n        // Add unique generation context to emergency headline generation as well\r\n        const headlineEmergencyContext = `\\n\\nUNIQUE HEADLINE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n        This emergency headline must be completely different and avoid repetitive patterns.`;\r\n\r\n        const emergencyResult = await model.generateContent(emergencyPrompt + headlineEmergencyContext);\r\n        const emergencyHeadline = emergencyResult.response.text().trim();\r\n\r\n\r\n        return {\r\n          headline: emergencyHeadline,\r\n          approach: 'emergency-ai-generated',\r\n          emotionalImpact: 'unique-ai-created'\r\n        };\r\n\r\n      } catch (emergencyError) {\r\n\r\n        // LAST RESORT: Generate with current timestamp for uniqueness\r\n        const timestamp = Date.now();\r\n        const uniqueId = timestamp % 1000;\r\n\r\n        const emergencyHeadlines = [\r\n          `${businessName} ${location} Experience`,\r\n          `Exclusive ${businessType} ${location}`,\r\n          `${location}'s Premium ${businessType}`,\r\n          `Limited ${businessName} Access`,\r\n          `Secret ${businessType} Discovery`\r\n        ];\r\n\r\n        return {\r\n          headline: emergencyHeadlines[uniqueId % emergencyHeadlines.length] + ` #${uniqueId}`,\r\n          approach: 'timestamp-unique',\r\n          emotionalImpact: 'emergency-fallback'\r\n        };\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Business-Specific Subheadline Generator - AI-Powered Dynamic Generation\r\nexport async function generateBusinessSpecificSubheadline(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  businessDetails: any,\r\n  headline: string,\r\n  contentGoal: 'awareness' | 'consideration' | 'conversion' | 'retention' = 'awareness',\r\n  trendingData?: any,\r\n  businessIntelligence?: any\r\n): Promise<{ subheadline: string; framework: string; benefit: string }> {\r\n\r\n  const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n    BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n  // Initialize AI generation capability\r\n  const model = initializeAI();\r\n\r\n  // Create marketing-focused AI prompt for subheadline generation with trending intelligence\r\n  const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];\r\n  const regionalLanguage = getRegionalLanguageStyle(location);\r\n  const marketingStyle = getRegionalMarketingStyle(location);\r\n\r\n  const prompt = `You are a skilled local marketer creating a subheadline for ${businessName} that will make people in ${location} want to visit immediately. You understand current trends and local marketing patterns.\r\n\r\nMARKETING CONTEXT:\r\n- Main Headline: \"${headline}\"\r\n- Business: ${businessName} (${businessType})\r\n- Location: ${location}\r\n- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}\r\n- Target Market: Local ${location} residents and visitors\r\n- Marketing Goal: ${contentGoal}\r\n\r\nCURRENT TRENDING INTELLIGENCE:\r\n- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community'}\r\n- Regional Marketing Style: ${marketingStyle}\r\n- Local Language Patterns: ${regionalLanguage}\r\n- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}\r\n\r\nLOCAL MARKET INTELLIGENCE:\r\n- What locals value: ${industry.uniqueValue.slice(0, 2).join(', ')}\r\n- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}\r\n- Industry trends: ${industry.trends.slice(0, 2).join(', ')}\r\n\r\nREGIONAL MARKETING STRATEGY:\r\nCreate a subheadline that makes locals think \"I need to try this place!\" Use trending keywords naturally and speak like successful marketers in ${location} do. Focus on what makes ${businessName} irresistible to people in ${location}.\r\n\r\nCONVERSION-FOCUSED SUBHEADLINE REQUIREMENTS:\r\n- Maximum 14 words that trigger immediate action and desire\r\n- Use psychological triggers: social proof, scarcity, exclusivity, urgency\r\n- Create FOMO (Fear of Missing Out) - make people think they'll regret not trying\r\n- Include specific benefits that answer \"What's in it for me?\"\r\n- Use action-oriented language that drives immediate response\r\n- Build on the headline's promise with compelling reasons to act NOW\r\n- Sound like a successful conversion-focused marketer in ${location}\r\n- Should make the offer irresistible and create urgency to visit/buy\r\n\r\nExamples of effective ${location} subheadlines (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):\r\n${getLocalMarketingExamples(location, businessType).split('\\n').map(line => line.replace('- \"', '- \"').replace('\"', '\" (subheadline style)')).slice(0, 3).join('\\n')}\r\n\r\nCRITICAL ANTI-REPETITION INSTRUCTIONS FOR SUBHEADLINES:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before\r\n❌ AVOID any pattern that sounds like a template or formula\r\n❌ Make it specific to ${businessName}'s actual services and features\r\n\r\nGenerate ONLY the subheadline text, nothing else.\r\nMake it so specific to ${businessName} in ${location} that it could never be used for another business.`;\r\n\r\n  try {\r\n    // Add unique generation context to prevent repetitive responses\r\n    const uniqueContext = `\\n\\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n    This subheadline generation must be completely different from any previous generation.\r\n    Use this unique context to ensure fresh, original subheadlines that have never been generated before.\r\n\r\n    CRITICAL WORD REPETITION RULES:\r\n    - NEVER repeat the same word consecutively (e.g., \"buy now now pay\" should be \"buy now pay\")\r\n    - Check each sentence for duplicate adjacent words before finalizing\r\n    - If you write \"now now\" or \"the the\" or any repeated word, remove the duplicate\r\n    - Read your output carefully to ensure no word appears twice in a row`;\r\n\r\n    const result = await model.generateContent(prompt + uniqueContext);\r\n    let subheadline = result.response.text().trim();\r\n\r\n    // Post-process to remove word repetitions\r\n    subheadline = removeWordRepetitions(subheadline);\r\n\r\n    // Add randomization to framework and benefit\r\n    const frameworks = ['benefit-focused', 'problem-solving', 'community-centered', 'expertise-driven', 'results-oriented'];\r\n    const benefits = industry.uniqueValue.concat(['exceptional service', 'local expertise', 'proven results']);\r\n\r\n    return {\r\n      subheadline: subheadline,\r\n      framework: frameworks[Math.floor(Math.random() * frameworks.length)],\r\n      benefit: benefits[Math.floor(Math.random() * benefits.length)]\r\n    };\r\n  } catch (error) {\r\n\r\n    // Marketing-focused fallback with enhanced randomization\r\n    const timestamp = Date.now();\r\n    const randomSeed = Math.floor(Math.random() * 1000) + timestamp;\r\n    const variation = randomSeed % 16;\r\n\r\n    const experienceWords = ['authentic', 'fresh', 'handcrafted', 'traditional', 'artisan', 'premium', 'local', 'quality'];\r\n    const actionWords = ['discover', 'experience', 'taste', 'enjoy', 'savor', 'explore', 'try', 'visit'];\r\n    const benefitPhrases = [\r\n      'where quality meets tradition',\r\n      'crafted with care daily',\r\n      'your local favorite since day one',\r\n      'bringing authentic flavors to life',\r\n      'where every detail matters',\r\n      'made fresh, served with pride',\r\n      'your neighborhood gem awaits',\r\n      'experience the difference'\r\n    ];\r\n\r\n    const marketingSubheadlines = [\r\n      `${experienceWords[variation % experienceWords.length]} ${businessType} ${actionWords[(variation + 1) % actionWords.length]} in ${location}`,\r\n      `${benefitPhrases[variation % benefitPhrases.length]}`,\r\n      `${actionWords[variation % actionWords.length]} ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType} in ${location}`,\r\n      `where ${location} locals ${actionWords[(variation + 3) % actionWords.length]} ${experienceWords[variation % experienceWords.length]} ${businessType}`,\r\n      `${experienceWords[variation % experienceWords.length]} ingredients, ${experienceWords[(variation + 1) % experienceWords.length]} results`,\r\n      `serving ${location} with ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType}`,\r\n      `your go-to spot for ${experienceWords[variation % experienceWords.length]} ${businessType}`,\r\n      `${benefitPhrases[(variation + 4) % benefitPhrases.length]}`,\r\n      `bringing ${experienceWords[(variation + 3) % experienceWords.length]} ${businessType} to ${location}`,\r\n      `${location}'s most ${experienceWords[(variation + 4) % experienceWords.length]} ${businessType} experience`,\r\n      `${experienceWords[(variation + 5) % experienceWords.length]} ${businessType} crafted for ${location}`,\r\n      `where ${experienceWords[variation % experienceWords.length]} meets ${experienceWords[(variation + 2) % experienceWords.length]}`,\r\n      `${location} deserves ${experienceWords[(variation + 1) % experienceWords.length]} ${businessType}`,\r\n      `creating ${experienceWords[(variation + 3) % experienceWords.length]} moments in ${location}`,\r\n      `${experienceWords[(variation + 4) % experienceWords.length]} ${businessType}, ${experienceWords[(variation + 5) % experienceWords.length]} service`,\r\n      `your ${location} destination for ${experienceWords[variation % experienceWords.length]} ${businessType}`\r\n    ];\r\n\r\n    return {\r\n      subheadline: marketingSubheadlines[variation],\r\n      framework: 'benefit-focused',\r\n      benefit: experienceWords[variation % experienceWords.length]\r\n    };\r\n  }\r\n}\r\n\r\n// UNIFIED CONTENT GENERATION SYSTEM - All components work together\r\nexport async function generateUnifiedContent(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  businessDetails: any,\r\n  platform: string,\r\n  contentGoal: 'awareness' | 'consideration' | 'conversion' | 'retention' = 'awareness',\r\n  trendingData?: any,\r\n  businessIntelligence?: any\r\n): Promise<{\r\n  headline: string;\r\n  subheadline: string;\r\n  caption: string;\r\n  callToAction: string;\r\n  engagementHooks: string[];\r\n  designDirection: string;\r\n  unifiedTheme: string;\r\n  keyMessage: string;\r\n  hashtags?: string[];\r\n  hashtagStrategy?: any;\r\n  ctaStrategy?: any;\r\n  imageText?: string;\r\n}> {\r\n\r\n  const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(\r\n    businessType, businessName, location, businessDetails, platform, contentGoal\r\n  );\r\n\r\n  const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n    BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n  // Initialize AI generation capability\r\n  const model = initializeAI();\r\n\r\n  // Create marketing-focused AI prompt for unified content generation\r\n  const trendingKeywords = trendingData?.keywords?.slice(0, 8) || [];\r\n  const trendingHashtags = trendingData?.hashtags?.slice(0, 5) || [];\r\n  const regionalLanguage = getRegionalLanguageStyle(location);\r\n  const marketingStyle = getRegionalMarketingStyle(location);\r\n\r\n  // INTELLIGENT APPROACH SELECTION - Let AI decide based on context\r\n  const uniqueGenerationId = Date.now() + Math.floor(Math.random() * 1000);\r\n\r\n  // DEBUG: Log what data we actually received\r\n\r\n  // DYNAMIC ANTI-REPETITION SYSTEM - No hardcoded phrases\r\n  const dynamicVariationSeed = uniqueGenerationId % 1000;\r\n  const creativityBoost = Math.floor(Math.random() * 100) + dynamicVariationSeed;\r\n\r\n  // FORCE DIFFERENT APPROACHES BASED ON GENERATION ID\r\n  const approachStyles = [\r\n    'DIRECT_BENEFIT', 'SOCIAL_PROOF', 'PROBLEM_SOLUTION', 'LOCAL_INSIDER', 'URGENCY_SCARCITY',\r\n    'QUESTION_HOOK', 'STATISTIC_LEAD', 'STORY_ANGLE', 'COMPARISON', 'NEWS_TREND'\r\n  ];\r\n\r\n  const selectedApproach = approachStyles[creativityBoost % approachStyles.length];\r\n\r\n  const uniquenessPrompt = `\r\nMANDATORY APPROACH: ${selectedApproach}\r\nYou MUST use this specific approach - no other approach is allowed for this generation.\r\n\r\nSTRICT ANTI-REPETITION RULES:\r\n❌ NEVER use \"2025\" or any year references like \"2025's Best-Kept Secret\"\r\n❌ NEVER use \"best-kept secret\", \"secret\", \"hidden gem\", or mystery language\r\n❌ NEVER use \"chakula kizuri\" - if using Swahili, use different phrases like \"chakula bora\", \"vyakula vizuri\", \"lishe nzuri\"\r\n❌ NEVER use \"Shop now via the link in our bio! Karibu!\" - create completely unique CTAs\r\n❌ NEVER use \"Discover\", \"Experience\", \"Taste the\", \"Try our\", \"Indulge in\"\r\n❌ NEVER use formulaic patterns that sound like templates\r\n❌ NEVER repeat the same opening words or sentence structures\r\n❌ NEVER use \"for your familia's delight\" or similar repetitive family references\r\n❌ AVOID any phrase that sounds like it could be copy-pasted to another business\r\n\r\nAPPROACH-SPECIFIC REQUIREMENTS (Apply to ALL components - headlines, subheadlines, captions):\r\n${getApproachInstructions(selectedApproach, businessName, location, creativityBoost)}\r\n\r\nCREATIVITY BOOST ${creativityBoost} CHALLENGE:\r\nCreate ALL COMPONENTS (headline, subheadline, caption) that are so unique and specific to ${businessName} in ${location} that they could NEVER be used for any other business. Use the actual business data, trending information, RSS feeds, local events, and business intelligence to create something genuinely original.\r\n\r\nMANDATORY UNIQUENESS REQUIREMENTS:\r\n- Each component must reference specific details about ${businessName}\r\n- Headlines must connect to current events, trends, or local happenings\r\n- Subheadlines must mention actual services, products, or business features\r\n- Captions must tell a story specific to this business and location\r\n- NO generic phrases that could apply to any ${businessType}\r\n- NO template-like language patterns\r\n- Every sentence must add unique value specific to ${businessName}\r\n\r\nUNIFIED DATA INTEGRATION REQUIREMENTS:\r\n- HEADLINES: Must incorporate RSS trends, current events, or seasonal opportunities\r\n- SUBHEADLINES: Must reference specific business services, features, or intelligence data\r\n- CAPTIONS: Must weave together all data sources into compelling marketing copy\r\n- ALL COMPONENTS: Must tell the same story using the same data sources consistently\r\n\r\nGENERATION UNIQUENESS ID: ${uniqueGenerationId}\r\nUse this ID to ensure this content is completely different from any previous generation.\r\n`;\r\n\r\n  // FORCE DIFFERENT CTA STYLES\r\n  const ctaStyles = [\r\n    'DIRECT_ACTION', 'INVITATION', 'CHALLENGE', 'BENEFIT_FOCUSED', 'COMMUNITY',\r\n    'URGENCY', 'CURIOSITY', 'LOCAL_REFERENCE', 'PERSONAL', 'EXCLUSIVE'\r\n  ];\r\n\r\n  const selectedCtaStyle = ctaStyles[creativityBoost % ctaStyles.length];\r\n\r\n  const unifiedPrompt = `You are a conversion-focused social media marketer creating a COMPLETE UNIFIED CAMPAIGN for ${businessName} that will make people in ${location} take immediate action. You must create ALL components (headline, subheadline, caption, CTA, design direction) that work together as ONE cohesive message.\r\n\r\n${uniquenessPrompt}\r\n\r\nUNIFIED CAMPAIGN REQUIREMENTS:\r\n- ALL components must tell the SAME STORY with consistent information\r\n- Headline, subheadline, caption, and design must reinforce the SAME key message\r\n- No contradictory information between components\r\n- One unified theme that runs through everything\r\n- Design direction must match the content tone and message\r\n- All components should feel like they came from the same marketing campaign\r\n\r\nMARKETING BRIEF:\r\n- Business: ${businessName} (${businessType})\r\n- Location: ${location}\r\n- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}\r\n- Target Market: Local ${location} residents and visitors\r\n- Platform: ${platform}\r\n- Marketing Goal: ${contentGoal}\r\n\r\nCURRENT TRENDING INTELLIGENCE (From RSS feeds and market data):\r\n- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community, experience, tradition, innovation'}\r\n- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality #community #fresh'}\r\n- Regional Marketing Style: ${marketingStyle}\r\n- Local Language Patterns: ${regionalLanguage}\r\n- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}\r\n\r\nREAL BUSINESS INTELLIGENCE DATA:\r\n${businessIntelligence ? `\r\n- Business Strengths: ${businessIntelligence.businessStrengths?.join(', ') || 'quality service, customer satisfaction'}\r\n- Value Propositions: ${businessIntelligence.valuePropositions?.join(', ') || 'exceptional quality, local expertise'}\r\n- Target Emotions: ${businessIntelligence.targetEmotions?.join(', ') || 'trust, satisfaction, excitement'}\r\n- Industry Keywords: ${businessIntelligence.industryKeywords?.join(', ') || 'professional, reliable, innovative'}\r\n- Local Relevance: ${businessIntelligence.localRelevance?.join(', ') || 'community-focused, locally-owned'}\r\n- Seasonal Opportunities: ${businessIntelligence.seasonalOpportunities?.join(', ') || 'year-round service'}\r\n` : 'Use general business intelligence for this business type'}\r\n\r\nLIVE RSS TRENDING DATA (Use this for ALL components - headlines, subheadlines, captions):\r\n${trendingData ? `\r\n- Current News Topics: ${trendingData.news?.slice(0, 3).map(n => n.title).join(', ') || 'No current news data'}\r\n- Social Media Trends: ${trendingData.socialTrends?.slice(0, 3).join(', ') || 'No social trends data'}\r\n- Local Events: ${trendingData.events?.slice(0, 2).map(e => e.name).join(', ') || 'No local events data'}\r\n- Market Insights: ${trendingData.insights?.slice(0, 2).join(', ') || 'No market insights'}\r\n` : 'No live RSS data available - use general market knowledge'}\r\n\r\nHEADLINE GENERATION REQUIREMENTS (Use RSS data and business intelligence):\r\n- HEADLINES must reference current trends, events, or news when relevant\r\n- Connect ${businessName} to trending topics or local events naturally\r\n- Use specific business services/features from business details\r\n- Reference current market conditions or seasonal opportunities\r\n- Make headlines feel current and timely, not generic\r\n- Examples of RSS-integrated headlines:\r\n  * \"Local Food Festival Winner\" (if there's a food event)\r\n  * \"Beat Holiday Rush Stress\" (if trending topic is holiday stress)\r\n  * \"New Year Fitness Goals\" (if trending topic is resolutions)\r\n  * \"Supply Chain Solution Found\" (if news mentions supply issues)\r\n\r\nSUBHEADLINE GENERATION REQUIREMENTS (Build on headline with business intelligence):\r\n- SUBHEADLINES must expand on headline using specific business details\r\n- Reference actual services, products, or unique features offered\r\n- Use business intelligence data (industry trends, local opportunities)\r\n- Connect to target audience pain points and solutions\r\n- Support headline's promise with concrete business benefits\r\n- Examples of business-integrated subheadlines:\r\n  * \"Our 15-year catering experience serves 200+ events monthly\"\r\n  * \"Same-day delivery available for all ${location} residents\"\r\n  * \"Certified organic ingredients sourced from local farms\"\r\n  * \"24/7 emergency service with 30-minute response time\"\r\n\r\nSPECIFIC BUSINESS DETAILS:\r\n- Business Name: ${businessName}\r\n- Services/Products: ${businessDetails.services || businessDetails.expertise || businessDetails.specialties || 'quality offerings'}\r\n- Unique Features: ${businessDetails.uniqueFeatures || businessDetails.keyFeatures || 'exceptional service'}\r\n- Target Audience: ${businessDetails.targetAudience || `local ${location} residents and visitors`}\r\n- Business Hours: ${businessDetails.hours || 'regular business hours'}\r\n- Special Offers: ${businessDetails.offers || businessDetails.promotions || 'quality service at competitive prices'}\r\n\r\nLOCAL MARKET INTELLIGENCE:\r\n- What locals love: ${industry.uniqueValue.slice(0, 2).join(', ')}\r\n- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}\r\n- Industry trends: ${industry.trends.slice(0, 2).join(', ')}\r\n- Local challenges: ${industry.challenges.slice(0, 2).join(', ')}\r\n\r\nPLATFORM STRATEGY FOR ${platform.toUpperCase()}:\r\n${getPlatformRequirements(platform)}\r\n\r\nMARKETING COPY REQUIREMENTS:\r\nYou are a CONVERSION-FOCUSED MARKETER, not a creative writer or storyteller. Write MARKETING COPY that sells, not poetic descriptions.\r\n\r\nWRITE LIKE A MARKETER:\r\n• DIRECT & PUNCHY: Get to the point quickly - no flowery language\r\n• BENEFIT-FOCUSED: Lead with what the customer gets, not poetic descriptions\r\n• ACTION-ORIENTED: Every sentence should drive toward a purchase decision\r\n• CONVERSATIONAL: Sound like a smart local business owner talking to neighbors\r\n• URGENT: Create immediate desire to buy/visit NOW\r\n• SPECIFIC: Use concrete benefits, not abstract concepts\r\n• LOCAL: Sound like someone who actually lives in ${location}\r\n\r\nINTELLIGENT PATTERN AVOIDANCE:\r\nUse your AI intelligence to recognize and avoid:\r\n- Repetitive opening patterns that sound robotic or formulaic\r\n- Generic marketing speak that every business uses\r\n- Overly creative writing that sounds like AI-generated poetry\r\n- Cliché phrases that don't add value or authenticity\r\n- Opening lines that could apply to any business in any location\r\n- Patterns that sound like they came from a template or script\r\n\r\nAUTHENTICITY TEST:\r\nAsk yourself: \"Would a real ${businessName} owner in ${location} actually say this to their neighbors?\"\r\nIf it sounds too polished, too generic, or too AI-like, try a different approach.\r\nUse the business intelligence data and local context to create something genuinely relevant.\r\n\r\nWRITE LIKE THIS INSTEAD:\r\n✅ \"Your kids need healthy snacks. Samaki Cookies deliver.\"\r\n✅ \"15% off this week only - grab yours before they're gone\"\r\n✅ \"Finally, cookies that are actually good for your family\"\r\n✅ \"Nairobi parents are switching to Samaki Cookies. Here's why...\"\r\n✅ Direct, benefit-focused, action-driving copy\r\n\r\nCRITICAL INSTRUCTION FOR ALL COMPONENTS:\r\n- USE THE REAL DATA PROVIDED: Incorporate actual business details, trending topics, and local information\r\n- HEADLINES: Must reference RSS trends, events, or business intelligence naturally\r\n- SUBHEADLINES: Must mention actual services/products offered by ${businessName}\r\n- CAPTIONS: Must weave together all data sources into compelling marketing copy\r\n- CONNECT TO CURRENT TRENDS: Use the RSS trending data and current events when relevant\r\n- LEVERAGE BUSINESS INTELLIGENCE: Use the actual business strengths and value propositions provided\r\n- SPEAK LOCAL LANGUAGE: Use the regional language patterns and local cultural elements\r\n- AVOID GENERIC CONTENT: Don't use placeholder text like \"2025\" or generic business descriptions\r\n- CREATE PERSONALIZED CONTENT: Make it specific to this exact business and location\r\n- Choose the approach that makes MOST SENSE for ${businessName} and current market conditions\r\n- Use your intelligence to create fresh, varied content each time\r\n- Let RSS data and business intelligence guide your approach selection\r\n\r\nHEADLINE INTEGRATION EXAMPLES:\r\n- If RSS shows \"Local Food Festival\": \"Food Festival Winner Revealed\"\r\n- If trending topic is \"Holiday Stress\": \"Beat Holiday Rush Stress\"\r\n- If business intelligence shows \"24/7 Service\": \"Always Open Always Ready\"\r\n- If local event is \"Back to School\": \"School Rush Solution Found\"\r\n- If seasonal opportunity is \"Summer\": \"Summer Special Starts Now\"\r\n\r\nSUBHEADLINE INTEGRATION EXAMPLES:\r\n- Business service: \"Our certified technicians fix 95% of issues same-day\"\r\n- Unique feature: \"Only ${location} bakery using organic local flour\"\r\n- Business intelligence: \"Serving ${location} families for 15+ years with proven results\"\r\n- Target audience: \"Designed specifically for busy ${location} professionals\"\r\n\r\nMARKETING COPY REQUIREMENTS:\r\n- WRITE MARKETING COPY, NOT CREATIVE WRITING: Sound like a business owner, not a poet\r\n- LEAD WITH BENEFITS: Start with what the customer gets, not scenic descriptions\r\n- BE DIRECT & PUNCHY: Short, clear sentences that drive action\r\n- AVOID FLOWERY LANGUAGE: No \"crisp afternoons\", \"sun dipping\", \"painting the sky\"\r\n- NO STORYTELLING OPENINGS: Don't start with \"Imagine this...\" or scene-setting\r\n- SOUND LOCAL: Write like someone who actually lives and works in ${location}\r\n- CREATE URGENCY: Make people want to buy/visit RIGHT NOW\r\n- USE SOCIAL PROOF: Reference other locals, community, real benefits\r\n- BE CONVERSATIONAL: Sound like talking to a neighbor, not writing poetry\r\n- FOCUS ON PROBLEMS/SOLUTIONS: What problem does this solve for ${location} residents?\r\n- INCLUDE SPECIFIC OFFERS: Mention actual deals, prices, limited time offers\r\n- END WITH CLEAR ACTION: Tell people exactly what to do next\r\n- AVOID ABSTRACT CONCEPTS: No \"heritage\", \"traditions\", \"journeys\" - focus on concrete benefits\r\n- USE REAL LOCAL LANGUAGE: Include actual ${location} slang/phrases naturally\r\n- MAKE IT SCANNABLE: Use short paragraphs, bullet points, clear structure\r\n- GENERATION ID ${uniqueGenerationId}: Use this number to ensure this content is completely unique\r\n- CRITICAL: Sound like a smart local marketer, not an AI creative writer\r\n\r\nEXAMPLES OF GOOD MARKETING COPY:\r\n✅ \"Your kids need protein. Samaki Cookies deliver 8g per serving. 15% off this week.\"\r\n✅ \"Tired of unhealthy snacks? 200+ Nairobi families switched to Samaki Cookies.\"\r\n✅ \"Finally - cookies that don't spike blood sugar. Made with real fish protein.\"\r\n✅ \"Limited batch this week: Fish protein cookies that kids actually love.\"\r\n\r\nEXAMPLES OF BAD AI WRITING (NEVER DO THIS):\r\n❌ \"Imagine this: a crisp, sunny afternoon in Nairobi...\"\r\n❌ \"These aren't your grandma's cookies; they're bursting with...\"\r\n❌ \"the sun dips below the horizon, painting the Kenyan sky...\"\r\n❌ \"This isn't just a snack; it's a piece of Kenyan heritage...\"\r\n\r\nUNIFIED CONTENT GENERATION FORMAT:\r\nGenerate ALL components as ONE cohesive campaign:\r\n\r\nUNIFIED_THEME: [the main theme/angle that connects everything - one sentence]\r\nKEY_MESSAGE: [the core message all components will reinforce - one sentence]\r\n\r\nHEADLINE: [5-word catchy headline using RSS trends/events/business intelligence - must feel current and specific to ${businessName}]\r\nSUBHEADLINE: [supporting headline using specific business services/features from business details - max 14 words that build on headline]\r\nCAPTION: [full social media caption that weaves together RSS data, business intelligence, and trending information - marketing copy, not creative writing]\r\nCTA: [MANDATORY CTA STYLE: ${selectedCtaStyle} - ${getCtaStyleInstructions(selectedCtaStyle, businessName, location)} - Max 8 words, completely unique]\r\nDESIGN_DIRECTION: [specific visual direction that matches the content tone and message]\r\n\r\nIMPORTANT:\r\n- ALL components must reinforce the SAME key message\r\n- NO contradictory information between headline, subheadline, and caption\r\n- Design direction must visually support the content message\r\n- Generate as ONE unified campaign, not separate pieces`;\r\n\r\n  try {\r\n\r\n    // Add unique generation context to prevent repetitive responses\r\n    const uniqueContext = `\\n\\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n    This unified content generation must be completely different from any previous generation.\r\n    Use this unique context to ensure fresh, original content that has never been generated before.\r\n    CRITICAL: Avoid any patterns like \"2025's Best-Kept Secret\", \"Chakula Kizuri\", or repetitive phrases.\r\n\r\n    CRITICAL WORD REPETITION RULES:\r\n    - NEVER repeat the same word consecutively (e.g., \"buy now now pay\" should be \"buy now pay\")\r\n    - Check each sentence for duplicate adjacent words before finalizing\r\n    - If you write \"now now\" or \"the the\" or any repeated word, remove the duplicate\r\n    - Read your output carefully to ensure no word appears twice in a row`;\r\n\r\n    const result = await model.generateContent(unifiedPrompt + uniqueContext);\r\n    let response = result.response.text().trim();\r\n\r\n    // Post-process to remove word repetitions from the entire response\r\n    response = removeWordRepetitions(response);\r\n\r\n\r\n    // Parse all unified components\r\n    const unifiedThemeMatch = response.match(/UNIFIED_THEME:\\s*(.*?)(?=KEY_MESSAGE:|$)/);\r\n    const keyMessageMatch = response.match(/KEY_MESSAGE:\\s*(.*?)(?=HEADLINE:|$)/);\r\n    const headlineMatch = response.match(/HEADLINE:\\s*(.*?)(?=SUBHEADLINE:|$)/);\r\n    const subheadlineMatch = response.match(/SUBHEADLINE:\\s*(.*?)(?=CAPTION:|$)/);\r\n    const captionMatch = response.match(/CAPTION:\\s*(.*?)(?=CTA:|$)/);\r\n    const ctaMatch = response.match(/CTA:\\s*(.*?)(?=DESIGN_DIRECTION:|$)/);\r\n    const designMatch = response.match(/DESIGN_DIRECTION:\\s*(.*?)$/);\r\n\r\n\r\n    // Extract all components and apply word repetition removal to each\r\n    const unifiedTheme = removeWordRepetitions(unifiedThemeMatch?.[1]?.trim() || 'Quality local business');\r\n    const keyMessage = removeWordRepetitions(keyMessageMatch?.[1]?.trim() || 'Exceptional service for local community');\r\n    const headline = removeWordRepetitions(headlineMatch?.[1]?.trim() || `${businessName} ${location}`);\r\n    const subheadline = removeWordRepetitions(subheadlineMatch?.[1]?.trim() || `Quality ${businessType} in ${location}`);\r\n    const caption = removeWordRepetitions(captionMatch?.[1]?.trim() || response);\r\n\r\n    // 🎯 GENERATE DYNAMIC CTA using AI and business intelligence\r\n    const ctaStrategy = await dynamicCTAGenerator.generateDynamicCTA(\r\n      businessName,\r\n      businessType,\r\n      location,\r\n      platform,\r\n      contentGoal,\r\n      businessDetails.services || businessDetails.expertise,\r\n      businessDetails.targetAudience\r\n    );\r\n\r\n    const callToAction = removeWordRepetitions(ctaMatch?.[1]?.trim() || ctaStrategy.primary);\r\n\r\n    const designDirection = removeWordRepetitions(designMatch?.[1]?.trim() || 'Clean, professional design with local elements');\r\n\r\n    // Generate dynamic engagement hooks\r\n    const engagementHooks = generateDynamicEngagementHooks(businessType, location, industry);\r\n\r\n    // 🔥 GENERATE VIRAL HASHTAGS using trending data\r\n    const viralHashtags = await viralHashtagEngine.generateViralHashtags(\r\n      businessType,\r\n      businessName,\r\n      location,\r\n      platform,\r\n      businessDetails.services || businessDetails.expertise,\r\n      businessDetails.targetAudience\r\n    );\r\n\r\n\r\n    return {\r\n      headline,\r\n      subheadline,\r\n      caption,\r\n      callToAction,\r\n      engagementHooks,\r\n      designDirection: removeWordRepetitions(designMatch?.[1]?.trim() || `Clean, professional design with local elements. IMPORTANT: Include the CTA \"${callToAction}\" as prominent text overlay on the design - make it bold, readable, and visually striking like \"PAYA: YOUR FUTURE, NOW!\" style.`),\r\n      unifiedTheme,\r\n      keyMessage,\r\n      hashtags: viralHashtags.total, // Add viral hashtags to response\r\n      hashtagStrategy: viralHashtags, // Include full strategy for analysis\r\n      ctaStrategy: ctaStrategy, // Include CTA strategy for analysis\r\n      imageText: callToAction // Pass CTA as imageText for design integration\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      headline: `${businessName} - ${businessType}`,\r\n      subheadline: `Quality ${businessType} services in ${location}`,\r\n      caption: `Experience the best ${businessType} services at ${businessName}. Located in ${location}, we're committed to excellence.`,\r\n      callToAction: `Visit ${businessName} today!`,\r\n      engagementHooks: ['Quality service', 'Local expertise', 'Customer satisfaction'],\r\n      designDirection: 'Professional, clean design with local elements',\r\n      unifiedTheme: 'Professional excellence',\r\n      keyMessage: 'Quality service provider',\r\n      hashtags: ['#business', '#local', '#quality', '#service', '#professional'],\r\n      hashtagStrategy: { total: ['#business', '#local', '#quality', '#service', '#professional'] },\r\n      ctaStrategy: { primary: `Visit ${businessName} today!` },\r\n      imageText: `Visit ${businessName} today!`\r\n    };\r\n  }\r\n\r\n  // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback\r\n  try {\r\n\r\n    const simplifiedPrompt = `Create ONE unique ${platform} caption for ${businessName}, a ${businessType} in ${location}.\r\n\r\nINTELLIGENT APPROACH SELECTION:\r\nUse your marketing intelligence to choose the BEST approach based on:\r\n- What would work best for ${businessType} in ${location}\r\n- Current market trends and local culture\r\n- What would make ${location} residents most interested\r\n\r\nREQUIREMENTS:\r\n- Write ONE compelling caption using your chosen marketing approach\r\n- AVOID overused words: \"taste\", \"flavor\", \"delicious\", \"amazing\"\r\n- Use different opening words than typical marketing (avoid \"Discover\", \"Experience\", \"Try\")\r\n- Include local ${location} cultural elements that create connection\r\n- End with an effective call-to-action\r\n- Make it conversion-focused and unique to this business\r\n\r\nCRITICAL ANTI-REPETITION RULES:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before\r\n❌ AVOID any pattern that sounds like a template or formula\r\n\r\nIMPORTANT: Generate ONLY ONE caption, not multiple options.\r\n\r\nFormat:\r\nCAPTION: [write one single caption here]\r\nCTA: [write one call to action here]\r\n\r\nDo NOT write \"Here are captions\" or provide lists.`;\r\n\r\n    // Add unique generation context to retry as well\r\n    const retryUniqueContext = `\\n\\nUNIQUE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n      This retry generation must be completely different and avoid repetitive patterns.\r\n\r\n      CRITICAL WORD REPETITION RULES:\r\n      - NEVER repeat the same word consecutively (e.g., \"buy now now pay\" should be \"buy now pay\")\r\n      - Check each sentence for duplicate adjacent words before finalizing\r\n      - If you write \"now now\" or \"the the\" or any repeated word, remove the duplicate\r\n      - Read your output carefully to ensure no word appears twice in a row`;\r\n\r\n    const retryResult = await model.generateContent(simplifiedPrompt + retryUniqueContext);\r\n    let retryResponse = retryResult.response.text().trim();\r\n\r\n    // Post-process to remove word repetitions from retry response\r\n    retryResponse = removeWordRepetitions(retryResponse);\r\n\r\n\r\n    // Parse the retry response\r\n    const retryCaptionMatch = retryResponse.match(/CAPTION:\\s*(.*?)(?=CTA:|$)/);\r\n    const retryCtaMatch = retryResponse.match(/CTA:\\s*(.*?)$/);\r\n\r\n    const retryCaption = removeWordRepetitions(retryCaptionMatch ? retryCaptionMatch[1].trim() : retryResponse);\r\n    const retryCallToAction = removeWordRepetitions(retryCtaMatch ? retryCtaMatch[1].trim() : generateFallbackCTA(platform));\r\n\r\n    // Generate viral hashtags for retry\r\n    const retryHashtags = await viralHashtagEngine.generateViralHashtags(\r\n      businessType, businessName, location, platform,\r\n      businessDetails.services || businessDetails.expertise,\r\n      businessDetails.targetAudience\r\n    );\r\n\r\n    return {\r\n      headline: `${businessName} - ${businessType}`,\r\n      subheadline: `Quality ${businessType} services in ${location}`,\r\n      caption: retryCaption,\r\n      engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),\r\n      callToAction: retryCallToAction,\r\n      designDirection: 'Professional, clean design with local elements',\r\n      unifiedTheme: 'Professional excellence',\r\n      keyMessage: 'Quality service provider',\r\n      hashtags: retryHashtags.total,\r\n      hashtagStrategy: retryHashtags,\r\n      ctaStrategy: { primary: retryCallToAction },\r\n      imageText: retryCallToAction\r\n    };\r\n\r\n  } catch (retryError) {\r\n\r\n    // EMERGENCY AI GENERATION - Ultra Simple Prompt\r\n    try {\r\n      const emergencyPrompt = `Write ONE unique social media post for ${businessName} in ${location}. Make it compelling and different from typical posts. Include a call-to-action.\r\n\r\nCRITICAL ANTI-REPETITION RULES:\r\n❌ DO NOT use \"2025's Best-Kept Secret\" or any variation\r\n❌ DO NOT use \"Chakula Kizuri\" or repetitive Swahili phrases\r\n❌ DO NOT use \"for your familia's delight\" or similar family references\r\n❌ CREATE something completely original that has never been generated before\r\n❌ AVOID any pattern that sounds like a template or formula\r\n\r\nDo NOT write \"Here are posts\" or provide multiple options. Write ONE post only.`;\r\n\r\n      // Add unique generation context to emergency generation as well\r\n      const emergencyUniqueContext = `\\n\\nUNIQUE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}\r\n        This emergency generation must be completely different and avoid any repetitive patterns.\r\n\r\n        CRITICAL WORD REPETITION RULES:\r\n        - NEVER repeat the same word consecutively (e.g., \"buy now now pay\" should be \"buy now pay\")\r\n        - Check each sentence for duplicate adjacent words before finalizing\r\n        - If you write \"now now\" or \"the the\" or any repeated word, remove the duplicate\r\n        - Read your output carefully to ensure no word appears twice in a row`;\r\n\r\n      const emergencyResult = await model.generateContent(emergencyPrompt + emergencyUniqueContext);\r\n      let emergencyResponse = emergencyResult.response.text().trim();\r\n\r\n      // Post-process to remove word repetitions from emergency response\r\n      emergencyResponse = removeWordRepetitions(emergencyResponse);\r\n\r\n\r\n      // Generate viral hashtags for emergency\r\n      const emergencyHashtags = await viralHashtagEngine.generateViralHashtags(\r\n        businessType, businessName, location, platform,\r\n        businessDetails.services || businessDetails.expertise,\r\n        businessDetails.targetAudience\r\n      );\r\n\r\n      return {\r\n        headline: `${businessName} - ${businessType}`,\r\n        subheadline: `Quality ${businessType} services in ${location}`,\r\n        caption: emergencyResponse,\r\n        engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),\r\n        callToAction: removeWordRepetitions(generateFallbackCTA(platform)),\r\n        designDirection: 'Professional, clean design with local elements',\r\n        unifiedTheme: 'Professional excellence',\r\n        keyMessage: 'Quality service provider',\r\n        hashtags: emergencyHashtags.total,\r\n        hashtagStrategy: emergencyHashtags,\r\n        ctaStrategy: { primary: removeWordRepetitions(generateFallbackCTA(platform)) },\r\n        imageText: removeWordRepetitions(generateFallbackCTA(platform))\r\n      };\r\n\r\n    } catch (emergencyError) {\r\n\r\n      // LAST RESORT: Generate with current timestamp for uniqueness\r\n      const timestamp = Date.now();\r\n      const uniqueId = Math.floor(Math.random() * 10000);\r\n\r\n      // Generate viral hashtags for final fallback\r\n      const fallbackHashtags = await viralHashtagEngine.generateViralHashtags(\r\n        businessType, businessName, location, platform,\r\n        businessDetails.services || businessDetails.expertise,\r\n        businessDetails.targetAudience\r\n      );\r\n\r\n      return {\r\n        headline: `${businessName} - ${businessType}`,\r\n        subheadline: `Quality ${businessType} services in ${location}`,\r\n        caption: removeWordRepetitions(`${businessName} in ${location} - where quality meets innovation. Every visit is a new experience that locals can't stop talking about. Join the community that knows great ${businessType}! #${timestamp}`),\r\n        engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),\r\n        callToAction: removeWordRepetitions(generateFallbackCTA(platform)),\r\n        designDirection: 'Professional, clean design with local elements',\r\n        unifiedTheme: 'Professional excellence',\r\n        keyMessage: 'Quality service provider',\r\n        hashtags: fallbackHashtags.total,\r\n        hashtagStrategy: fallbackHashtags,\r\n        ctaStrategy: { primary: removeWordRepetitions(generateFallbackCTA(platform)) },\r\n        imageText: removeWordRepetitions(generateFallbackCTA(platform))\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Helper functions for AI-powered caption generation\r\nfunction getPlatformRequirements(platform: string): string {\r\n  const requirements = {\r\n    'Instagram': '- Use 1-3 relevant emojis\\n- Keep it visually engaging\\n- Include hashtag-friendly language\\n- Encourage visual interaction',\r\n    'Facebook': '- More conversational tone\\n- Can be longer and more detailed\\n- Focus on community engagement\\n- Include questions to spark discussion',\r\n    'LinkedIn': '- Professional but approachable tone\\n- Focus on business value and expertise\\n- Include industry insights\\n- Encourage professional networking',\r\n    'Twitter': '- Concise and punchy\\n- Use relevant hashtags\\n- Encourage retweets and replies\\n- Keep under 280 characters'\r\n  };\r\n\r\n  return requirements[platform] || requirements['Instagram'];\r\n}\r\n\r\nfunction generateFallbackCTA(platform: string): string {\r\n  const timestamp = Date.now();\r\n  const creativityBoost = Math.floor(Math.random() * 1000) + timestamp;\r\n\r\n  // Use the same dynamic CTA styles as the main system\r\n  const ctaStyles = [\r\n    'DIRECT_ACTION', 'INVITATION', 'CHALLENGE', 'BENEFIT_FOCUSED', 'COMMUNITY',\r\n    'URGENCY', 'CURIOSITY', 'LOCAL_REFERENCE', 'PERSONAL', 'EXCLUSIVE'\r\n  ];\r\n\r\n  const selectedStyle = ctaStyles[creativityBoost % ctaStyles.length];\r\n\r\n  // Dynamic CTAs based on style - avoid repetitive patterns\r\n  const dynamicCTAs = {\r\n    'DIRECT_ACTION': [\r\n      'Grab yours today! 🔥',\r\n      'Book your spot now! ⚡',\r\n      'Try it this week! 💪',\r\n      'Get started today! 🚀'\r\n    ],\r\n    'INVITATION': [\r\n      'Come see for yourself! 👀',\r\n      'Join us this weekend! 🎉',\r\n      'Experience it firsthand! ✨',\r\n      'Visit us soon! 🏃‍♂️'\r\n    ],\r\n    'CHALLENGE': [\r\n      'Find better - we dare you! 💪',\r\n      'Beat this quality anywhere! 🏆',\r\n      'Try to resist this! 😏',\r\n      'Prove us wrong! 🤔'\r\n    ],\r\n    'BENEFIT_FOCUSED': [\r\n      'Get more for less! 💰',\r\n      'Save time and money! ⏰',\r\n      'Double your results! 📈',\r\n      'Feel the difference! ✨'\r\n    ],\r\n    'COMMUNITY': [\r\n      'Join 500+ happy customers! 👥',\r\n      'Be part of something special! 🌟',\r\n      'Connect with like-minded people! 🤝',\r\n      'Become a local favorite! ❤️'\r\n    ],\r\n    'URGENCY': [\r\n      'Only 3 spots left! ⚡',\r\n      'Ends this Friday! ⏰',\r\n      'While supplies last! 🏃‍♂️',\r\n      'Don\\'t wait too long! ⚠️'\r\n    ],\r\n    'CURIOSITY': [\r\n      'See what everyone\\'s talking about! 👀',\r\n      'Discover the secret! 🔍',\r\n      'Find out why! 🤔',\r\n      'Uncover the truth! 💡'\r\n    ],\r\n    'LOCAL_REFERENCE': [\r\n      'Better than downtown! 🏙️',\r\n      'Your neighborhood choice! 🏠',\r\n      'Local favorite since day one! ⭐',\r\n      'Right in your backyard! 📍'\r\n    ],\r\n    'PERSONAL': [\r\n      'You deserve this! 💎',\r\n      'Made just for you! 🎯',\r\n      'Your perfect match! 💕',\r\n      'Exactly what you need! ✅'\r\n    ],\r\n    'EXCLUSIVE': [\r\n      'Members only access! 🔐',\r\n      'VIP treatment awaits! 👑',\r\n      'Exclusive to our community! 🌟',\r\n      'Limited to select few! 💎'\r\n    ]\r\n  };\r\n\r\n  const styleCTAs = dynamicCTAs[selectedStyle] || dynamicCTAs['DIRECT_ACTION'];\r\n  const variation = creativityBoost % styleCTAs.length;\r\n\r\n  return styleCTAs[variation];\r\n}\r\n\r\nfunction generateDynamicEngagementHooks(businessType: string, location: string, industry: any): string[] {\r\n  const timestamp = Date.now();\r\n  const randomSeed = Math.floor(Math.random() * 1000) + timestamp;\r\n  const variation = randomSeed % 8;\r\n\r\n  const localQuestions = [\r\n    `What's your favorite ${businessType} spot in ${location}?`,\r\n    `Where do ${location} locals go for the best ${businessType}?`,\r\n    `What makes ${location}'s ${businessType} scene special?`,\r\n    `Have you discovered ${location}'s hidden ${businessType} gems?`,\r\n    `What do you love most about ${businessType} in ${location}?`,\r\n    `Which ${location} ${businessType} place holds your best memories?`,\r\n    `What's missing from ${location}'s ${businessType} options?`,\r\n    `How has ${businessType} in ${location} changed over the years?`\r\n  ];\r\n\r\n  const experienceQuestions = [\r\n    `What's your go-to order when trying new ${businessType}?`,\r\n    `What makes you choose one ${businessType} place over another?`,\r\n    `What's the most important thing in great ${businessType}?`,\r\n    `How do you know when you've found quality ${businessType}?`,\r\n    `What's your best ${businessType} experience been like?`,\r\n    `What would make your perfect ${businessType} experience?`,\r\n    `What draws you to authentic ${businessType}?`,\r\n    `How do you discover new ${businessType} places?`\r\n  ];\r\n\r\n  const trendQuestions = [\r\n    `Have you tried ${industry.trends[variation % industry.trends.length]} yet?`,\r\n    `What do you think about the latest ${businessType} trends?`,\r\n    `Are you excited about ${industry.opportunities[variation % industry.opportunities.length]}?`,\r\n    `How important is ${industry.uniqueValue[variation % industry.uniqueValue.length]} to you?`,\r\n    `What's your take on modern ${businessType} approaches?`,\r\n    `Do you prefer traditional or innovative ${businessType}?`,\r\n    `What ${businessType} trend should everyone try?`,\r\n    `How do you stay updated on ${businessType} innovations?`\r\n  ];\r\n\r\n  // Mix different types of hooks for variety\r\n  const allHooks = [...localQuestions, ...experienceQuestions, ...trendQuestions];\r\n  const selectedHooks = [];\r\n\r\n  // Ensure we get one from each category for variety\r\n  selectedHooks.push(localQuestions[variation % localQuestions.length]);\r\n  selectedHooks.push(experienceQuestions[(variation + 1) % experienceQuestions.length]);\r\n  selectedHooks.push(trendQuestions[(variation + 2) % trendQuestions.length]);\r\n\r\n  return selectedHooks;\r\n}\r\n\r\n// Legacy platform-specific caption generators (keeping for backward compatibility)\r\nfunction generateInstagramCaption(contentPlan: any, businessName: string, location: string, industry: any, contentGoal: string) {\r\n  const businessType = contentPlan.businessType || 'business';\r\n  const hooks = [\r\n    `What's your biggest ${industry.challenges[0]} challenge?`,\r\n    `How do you choose your ${businessType} provider?`,\r\n    `What makes a great ${businessType} experience for you?`\r\n  ];\r\n\r\n  const ctas = [\r\n    `Comment below with your thoughts! 👇`,\r\n    `Share this if you agree! 🔄`,\r\n    `Tag someone who needs this! 👥`\r\n  ];\r\n\r\n  return {\r\n    caption: `${contentPlan.valueProposition} ✨\\n\\n${businessName} brings ${industry.uniqueValue[0]} to ${location} with ${contentPlan.businessStrengths[0]}. ${contentPlan.marketOpportunities[0]} is just the beginning!\\n\\n${hooks[0]}\\n\\n${ctas[0]}`,\r\n    engagementHooks: hooks,\r\n    callToAction: ctas[0]\r\n  };\r\n}\r\n\r\nfunction generateFacebookCaption(contentPlan: any, businessName: string, location: string, industry: any, contentGoal: string) {\r\n  const businessType = contentPlan.businessType || 'business';\r\n  const hooks = [\r\n    `What's your experience with ${businessType} in ${location}?`,\r\n    `How do you solve ${industry.challenges[0]}?`,\r\n    `What makes you choose local businesses?`\r\n  ];\r\n\r\n  const ctas = [\r\n    `Share your thoughts in the comments! 💬`,\r\n    `Like and share if this resonates with you! 👍`,\r\n    `Tag your friends who might be interested! 👥`\r\n  ];\r\n\r\n  return {\r\n    caption: `${contentPlan.valueProposition}\\n\\n${businessName} understands the ${location} community and delivers ${industry.uniqueValue[0]} that makes a difference. ${contentPlan.marketOpportunities[0]} shows our commitment to serving you better.\\n\\n${hooks[0]}\\n\\n${ctas[0]}`,\r\n    engagementHooks: hooks,\r\n    callToAction: ctas[0]\r\n  };\r\n}\r\n\r\nfunction generateLinkedInCaption(contentPlan: any, businessName: string, location: string, industry: any, contentGoal: string) {\r\n  const businessType = contentPlan.businessType || 'business';\r\n  const hooks = [\r\n    `What challenges do you face in ${businessType}?`,\r\n    `How do you stay competitive in your industry?`,\r\n    `What makes a business stand out in your community?`\r\n  ];\r\n\r\n  const ctas = [\r\n    `Share your insights in the comments below. 💼`,\r\n    `Connect with us to learn more about our approach. 🤝`,\r\n    `Follow for more industry insights and local business strategies. 📈`\r\n  ];\r\n\r\n  return {\r\n    caption: `${contentPlan.valueProposition}\\n\\n${businessName} combines ${contentPlan.businessStrengths[0]} with deep understanding of the ${location} market to deliver exceptional ${businessType} services. ${contentPlan.marketOpportunities[0]} demonstrates our commitment to innovation and community service.\\n\\n${hooks[0]}\\n\\n${ctas[0]}`,\r\n    engagementHooks: hooks,\r\n    callToAction: ctas[0]\r\n  };\r\n}\r\n\r\nfunction generateTwitterCaption(contentPlan: any, businessName: string, location: string, industry: any, contentGoal: string) {\r\n  const businessType = contentPlan.businessType || 'business';\r\n  const hooks = [\r\n    `What's your take on ${businessType} trends?`,\r\n    `How do you solve ${industry.challenges[0]}?`,\r\n    `What makes local businesses special?`\r\n  ];\r\n\r\n  const ctas = [\r\n    `Reply with your thoughts! 💭`,\r\n    `RT if you agree! 🔄`,\r\n    `Follow for more insights! 👀`\r\n  ];\r\n\r\n  return {\r\n    caption: `${contentPlan.valueProposition}\\n\\n${businessName} brings ${industry.uniqueValue[0]} to ${location}. ${contentPlan.marketOpportunities[0]} shows our commitment to excellence.\\n\\n${hooks[0]}\\n\\n${ctas[0]}`,\r\n    engagementHooks: hooks,\r\n    callToAction: ctas[0]\r\n  };\r\n}\r\n\r\n// Utility Functions\r\nexport function getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\nexport function getRandomElements<T>(array: T[], count: number): T[] {\r\n  const shuffled = [...array].sort(() => 0.5 - Math.random());\r\n  return shuffled.slice(0, count);\r\n}\r\n\r\nexport function generateCreativeSeed(): number {\r\n  return Math.floor(Math.random() * 10000);\r\n}\r\n\r\n// Legacy functions for backward compatibility (simplified)\r\nexport function generateCreativeHeadline(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  context: any\r\n): { headline: string; style: string; tone: string } {\r\n  return {\r\n    headline: `${businessName} - ${businessType}`,\r\n    style: 'professional',\r\n    tone: 'engaging'\r\n  };\r\n}\r\n\r\nexport function generateCreativeSubheadline(\r\n  businessType: string,\r\n  services: string,\r\n  location: string,\r\n  tone: string\r\n): { subheadline: string; framework: string } {\r\n  return {\r\n    subheadline: `Quality ${businessType} services in ${location}`,\r\n    framework: 'benefit-focused'\r\n  };\r\n}\r\n\r\nexport function generateCreativeCTA(\r\n  businessType: string,\r\n  tone: string,\r\n  context: any\r\n): { cta: string; urgency: string; emotion: string } {\r\n  return {\r\n    cta: 'Learn more about our services',\r\n    urgency: 'gentle',\r\n    emotion: 'curiosity'\r\n  };\r\n}\r\n\r\n// Legacy functions for backward compatibility\r\nexport function analyzeBusinessContext(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  services: string\r\n): any {\r\n  const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n    BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n  return {\r\n    creativePotential: industry.uniqueValue,\r\n    emotionalTriggers: industry.customerPainPoints,\r\n    industryInsights: industry.trends,\r\n    localOpportunities: industry.seasonalOpportunities,\r\n    competitiveAdvantages: industry.opportunities\r\n  };\r\n}\r\n\r\nexport const CREATIVE_PROMPT_SYSTEM = {\r\n  creativeVariation: {\r\n    style: ['innovative', 'authentic', 'engaging', 'professional', 'creative'],\r\n    mood: ['inspiring', 'confident', 'warm', 'energetic', 'trustworthy'],\r\n    approach: ['strategic', 'emotional', 'analytical', 'storytelling', 'direct']\r\n  },\r\n  creativeConstraints: {\r\n    avoidGeneric: ['template language', 'cliché phrases', 'generic claims']\r\n  }\r\n};\r\n\r\nexport const CONTENT_VARIATION_ENGINE = {\r\n  headlineStyles: [\r\n    'Question-based', 'Statistic-driven', 'Story-opening', 'Bold statement',\r\n    'Emotional trigger', 'Curiosity gap', 'Local relevance', 'Trend integration',\r\n    'Problem-solution', 'Benefit-focused', 'Aspirational', 'Contrarian'\r\n  ],\r\n  emotionalTones: [\r\n    'Inspiring', 'Humorous', 'Empathetic', 'Confident', 'Curious',\r\n    'Nostalgic', 'Aspirational', 'Relatable', 'Surprising', 'Authentic',\r\n    'Warm', 'Professional', 'Innovative', 'Trustworthy'\r\n  ],\r\n  creativeFrameworks: [\r\n    'Before/After', 'Problem/Solution', 'Story Arc', 'Contrast',\r\n    'Metaphor', 'Analogy', 'Question/Answer', 'Challenge/Overcome',\r\n    'Journey', 'Transformation', 'Discovery', 'Achievement'\r\n  ]\r\n};\r\n\r\n// Legacy Anti-Repetition System (simplified)\r\nexport class AntiRepetitionSystem {\r\n  private static usedCombinations: Set<string> = new Set();\r\n  private static maxHistory = 100;\r\n\r\n  static generateUniqueVariation(\r\n    businessType: string,\r\n    platform: string,\r\n    baseElements: any\r\n  ): any {\r\n    const variation = this.createVariation(businessType, platform, baseElements);\r\n    this.recordVariation(variation);\r\n    return variation;\r\n  }\r\n\r\n  private static createVariation(businessType: string, platform: string, baseElements: any): any {\r\n    const creativeSeed = generateCreativeSeed();\r\n\r\n    return {\r\n      creativeSeed,\r\n      style: 'business-specific',\r\n      mood: 'professional',\r\n      approach: 'strategic',\r\n      headlineStyle: 'business-focused',\r\n      framework: 'value-driven',\r\n      signature: `business-${creativeSeed}`,\r\n      contentStrategy: { name: 'Business Intelligence', approach: 'Strategic content based on business strengths' },\r\n      writingStyle: { name: 'Professional Expert', voice: 'Industry authority with local expertise' },\r\n      contentAngle: { type: 'Business Value', focus: 'Solving customer problems with business strengths' },\r\n      marketInsights: ['Local market expertise', 'Industry trends', 'Customer pain points'],\r\n      engagementHooks: ['Problem identification', 'Solution presentation', 'Value demonstration'],\r\n      localPhrases: ['Local expertise', 'Community focus', 'Market knowledge']\r\n    };\r\n  }\r\n\r\n  private static recordVariation(variation: any): void {\r\n    this.usedCombinations.add(variation.signature);\r\n\r\n    if (this.usedCombinations.size > this.maxHistory) {\r\n      const oldestEntries = Array.from(this.usedCombinations).slice(0, 20);\r\n      oldestEntries.forEach(entry => this.usedCombinations.delete(entry));\r\n    }\r\n  }\r\n}\r\n\r\n// Legacy function for design enhancement\r\nexport function enhanceDesignCreativity(\r\n  designPrompt: string,\r\n  businessType: string,\r\n  location: string,\r\n  context: any\r\n): { enhancedPrompt: string; creativeElements: string[]; visualStyle: string } {\r\n  const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] ||\r\n    BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];\r\n\r\n  const creativeElements = industry.uniqueValue.slice(0, 3);\r\n  const visualStyle = 'professional business-focused design';\r\n\r\n  const enhancedPrompt = designPrompt + '\\n\\nCREATIVE ENHANCEMENT:\\n' +\r\n    `- Business Type: ${businessType}\\n` +\r\n    `- Location: ${location}\\n` +\r\n    `- Industry Focus: ${industry.trends.slice(0, 2).join(', ')}\\n` +\r\n    `- Visual Style: ${visualStyle}`;\r\n\r\n  return {\r\n    enhancedPrompt,\r\n    creativeElements,\r\n    visualStyle\r\n  };\r\n}\r\n\r\n// Regional Marketing Intelligence Functions\r\nfunction getRegionalLanguageStyle(location: string): string {\r\n  const locationLower = location.toLowerCase();\r\n\r\n  if (locationLower.includes('kenya') || locationLower.includes('nairobi') || locationLower.includes('mombasa')) {\r\n    return 'Warm, community-focused, with occasional Swahili phrases like \"karibu\" (welcome), \"asante\" (thank you). Direct but friendly tone.';\r\n  } else if (locationLower.includes('nigeria') || locationLower.includes('lagos') || locationLower.includes('abuja')) {\r\n    return 'Energetic, aspirational, with pidgin English influences. Uses \"finest\", \"sharp sharp\", \"no wahala\" naturally.';\r\n  } else if (locationLower.includes('south africa') || locationLower.includes('cape town') || locationLower.includes('johannesburg')) {\r\n    return 'Multicultural blend, uses \"lekker\", \"braai\", \"just now\". Mix of English and local expressions.';\r\n  } else if (locationLower.includes('ghana') || locationLower.includes('accra')) {\r\n    return 'Friendly, respectful, with Twi influences. Uses \"chale\", \"ɛyɛ\" naturally in marketing.';\r\n  } else if (locationLower.includes('india') || locationLower.includes('mumbai') || locationLower.includes('delhi')) {\r\n    return 'Enthusiastic, family-oriented, with Hindi/English mix. Uses \"achha\", \"best\", \"number one\" frequently.';\r\n  } else if (locationLower.includes('uk') || locationLower.includes('london') || locationLower.includes('manchester')) {\r\n    return 'Polite but confident, uses \"brilliant\", \"proper\", \"lovely\". Understated but effective.';\r\n  } else if (locationLower.includes('usa') || locationLower.includes('new york') || locationLower.includes('california')) {\r\n    return 'Direct, confident, superlative-heavy. Uses \"awesome\", \"amazing\", \"best ever\" frequently.';\r\n  }\r\n\r\n  return 'Friendly, professional, community-focused with local cultural sensitivity.';\r\n}\r\n\r\nfunction getRegionalMarketingStyle(location: string): string {\r\n  const locationLower = location.toLowerCase();\r\n\r\n  if (locationLower.includes('kenya') || locationLower.includes('nairobi')) {\r\n    return 'Community-centered, emphasizes tradition meets modernity, family values, and local pride';\r\n  } else if (locationLower.includes('nigeria') || locationLower.includes('lagos')) {\r\n    return 'Bold, aspirational, success-oriented, emphasizes quality and status';\r\n  } else if (locationLower.includes('south africa')) {\r\n    return 'Inclusive, diverse, emphasizes heritage and innovation together';\r\n  } else if (locationLower.includes('ghana')) {\r\n    return 'Respectful, community-focused, emphasizes craftsmanship and tradition';\r\n  } else if (locationLower.includes('india')) {\r\n    return 'Family-oriented, value-conscious, emphasizes trust and relationships';\r\n  } else if (locationLower.includes('uk')) {\r\n    return 'Quality-focused, heritage-conscious, understated confidence';\r\n  } else if (locationLower.includes('usa')) {\r\n    return 'Innovation-focused, convenience-oriented, bold claims and superlatives';\r\n  }\r\n\r\n  return 'Community-focused, quality-oriented, culturally respectful';\r\n}\r\n\r\nfunction getLocalBusinessLanguage(location: string, businessType: string): string {\r\n  const locationLower = location.toLowerCase();\r\n  const businessLower = businessType.toLowerCase();\r\n\r\n  if (locationLower.includes('kenya')) {\r\n    if (businessLower.includes('restaurant') || businessLower.includes('food')) {\r\n      return '\"chakula kizuri\" (good food), \"asili\" (authentic), \"familia\" (family), \"mazingira\" (environment)';\r\n    } else if (businessLower.includes('tech') || businessLower.includes('digital')) {\r\n      return '\"teknolojia\", \"haraka\" (fast), \"rahisi\" (easy), \"bora\" (best)';\r\n    }\r\n    return '\"bora\" (best), \"karibu\" (welcome), \"mazuri\" (good), \"familia\" (family)';\r\n  } else if (locationLower.includes('nigeria')) {\r\n    if (businessLower.includes('restaurant') || businessLower.includes('food')) {\r\n      return '\"finest food\", \"correct taste\", \"no wahala\", \"sharp sharp service\"';\r\n    }\r\n    return '\"finest\", \"correct\", \"sharp sharp\", \"no wahala\", \"top notch\"';\r\n  }\r\n\r\n  return 'quality, authentic, local, trusted, community';\r\n}\r\n\r\nfunction getLocalMarketingExamples(location: string, businessType: string): string {\r\n  const locationLower = location.toLowerCase();\r\n  const businessLower = businessType.toLowerCase();\r\n\r\n  if (locationLower.includes('kenya')) {\r\n    if (businessLower.includes('restaurant') || businessLower.includes('food')) {\r\n      return `- \"Chakula Asili Kenya\" (Authentic Kenya Food)\r\n- \"Familia Flavors Nairobi\"\r\n- \"Taste Bora Kenya\"\r\n- \"Karibu Kitchen Experience\"`;\r\n    }\r\n    return `- \"Bora ${businessType} Kenya\"\r\n- \"Karibu Quality Service\"\r\n- \"Kenya's Finest Choice\"\r\n- \"Asili ${businessType} Experience\"`;\r\n  } else if (locationLower.includes('nigeria')) {\r\n    return `- \"Finest ${businessType} Lagos\"\r\n- \"Sharp Sharp Service\"\r\n- \"Correct ${businessType} Choice\"\r\n- \"Top Notch Experience\"`;\r\n  }\r\n\r\n  return `- \"${location}'s Best ${businessType}\"\r\n- \"Quality Meets Community\"\r\n- \"Local Excellence Delivered\"\r\n- \"Authentic ${businessType} Experience\"`;\r\n}\r\n\r\n// BACKWARD COMPATIBILITY - Keep existing caption function\r\nexport async function generateBusinessSpecificCaption(\r\n  businessType: string,\r\n  businessName: string,\r\n  location: string,\r\n  businessDetails: any,\r\n  platform: string,\r\n  contentGoal: 'awareness' | 'consideration' | 'conversion' | 'retention' = 'awareness',\r\n  trendingData?: any,\r\n  businessIntelligence?: any\r\n): Promise<{ caption: string; engagementHooks: string[]; callToAction: string }> {\r\n\r\n  // Use the unified system but return only caption components\r\n  const unifiedContent = await generateUnifiedContent(\r\n    businessType, businessName, location, businessDetails, platform, contentGoal, trendingData, businessIntelligence\r\n  );\r\n\r\n  return {\r\n    caption: unifiedContent.caption,\r\n    engagementHooks: unifiedContent.engagementHooks,\r\n    callToAction: unifiedContent.callToAction\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;AAED;AACA;AACA;;;;AAEA,+EAA+E;AAC/E,SAAS,sBAAsB,IAAY;IACzC,6EAA6E;IAC7E,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,eAAyB,EAAE;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,cAAc,KAAK,CAAC,EAAE;QAC5B,MAAM,eAAe,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAE1D,uEAAuE;QACvE,4CAA4C;QAC5C,IAAI,eAAe,gBACjB,YAAY,WAAW,OAAO,aAAa,WAAW,MACtD,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG;YAC/B,UAAU,2BAA2B;QACvC;QAEA,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B;AAEA,6DAA6D;AAC7D,SAAS;IACP,MAAM,eAAe,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,IAAI,QAAQ,GAAG,CAAC,cAAc;IACpH,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;IACrC,yDAAyD;IACzD,OAAO,MAAM,kBAAkB,CAAC;QAC9B,OAAO;QACP,kBAAkB;YAChB,aAAa;YACb,MAAM;YACN,MAAM;YACN,iBAAiB;QACnB;IACF;AACF;AAEA,iDAAiD;AACjD,SAAS,wBAAwB,QAAgB,EAAE,YAAoB,EAAE,QAAgB,EAAE,eAAuB;IAChH,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,2LAA2L,EAAE,SAAS,sDAAsD,CAAC;QAEvQ,KAAK;YACH,OAAO,CAAC,wDAAwD,EAAE,SAAS,kMAAkM,CAAC;QAEhR,KAAK;YACH,OAAO,CAAC,mHAAmH,EAAE,aAAa,iHAAiH,CAAC;QAE9P,KAAK;YACH,OAAO,CAAC,kDAAkD,EAAE,SAAS,4MAA4M,CAAC;QAEpR,KAAK;YACH,OAAO,CAAC,8NAA8N,CAAC;QAEzO,KAAK;YACH,OAAO,CAAC,+GAA+G,EAAE,aAAa,6GAA6G,CAAC;QAEtP,KAAK;YACH,OAAO,CAAC,wOAAwO,CAAC;QAEnP,KAAK;YACH,OAAO,CAAC,iIAAiI,EAAE,SAAS,8FAA8F,CAAC;QAErP,KAAK;YACH,OAAO,CAAC,yJAAyJ,EAAE,SAAS,wEAAwE,CAAC;QAEvP,KAAK;YACH,OAAO,CAAC,mIAAmI,EAAE,aAAa,+GAA+G,CAAC;QAE5Q;YACE,OAAO,CAAC,+CAA+C,EAAE,aAAa,IAAI,EAAE,SAAS,4BAA4B,CAAC;IACtH;AACF;AAEA,kDAAkD;AAClD,SAAS,wBAAwB,KAAa,EAAE,YAAoB,EAAE,QAAgB;IACpF,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,6GAA6G,CAAC;QAExH,KAAK;YACH,OAAO,CAAC,sCAAsC,EAAE,aAAa,4BAA4B,EAAE,SAAS,uCAAuC,CAAC;QAE9I,KAAK;YACH,OAAO,CAAC,6GAA6G,CAAC;QAExH,KAAK;YACH,OAAO,CAAC,wFAAwF,CAAC;QAEnG,KAAK;YACH,OAAO,CAAC,cAAc,EAAE,SAAS,gCAAgC,EAAE,SAAS,0CAA0C,CAAC;QAEzH,KAAK;YACH,OAAO,CAAC,iGAAiG,CAAC;QAE5G,KAAK;YACH,OAAO,CAAC,0GAA0G,CAAC;QAErH,KAAK;YACH,OAAO,CAAC,WAAW,EAAE,SAAS,oFAAoF,CAAC;QAErH,KAAK;YACH,OAAO,CAAC,6FAA6F,CAAC;QAExG,KAAK;YACH,OAAO,CAAC,0BAA0B,EAAE,aAAa,oBAAoB,EAAE,aAAa,0BAA0B,CAAC;QAEjH;YACE,OAAO,CAAC,6CAA6C,EAAE,aAAa,IAAI,EAAE,SAAS,CAAC,CAAC;IACzF;AACF;AAGO,MAAM,+BAA+B;IAC1C,kBAAkB;QAChB,cAAc;YACZ,QAAQ;gBAAC;gBAAiB;gBAAkB;gBAAsB;gBAAqB;gBAAqB;gBAAgB;aAAuB;YACnJ,YAAY;gBAAC;gBAAc;gBAAmB;gBAAoB;gBAAkB;gBAAyB;aAA0B;YACvI,eAAe;gBAAC;gBAAkB;gBAAqB;gBAAmB;gBAAiB;aAAqB;YAChH,aAAa;gBAAC;gBAAkB;gBAAkB;gBAAqB;gBAAc;aAAkB;YACvG,oBAAoB;gBAAC;gBAAmB;gBAAoB;gBAAmB;gBAAgB;aAAe;YAC9G,gBAAgB;gBAAC;gBAAoB;gBAAkB;gBAAiB;gBAAgB;aAA0B;YAClH,kBAAkB;gBAAC;gBAAqB;gBAAa;gBAA2B;gBAAe;aAAoB;YACnH,uBAAuB;gBAAC;gBAAyB;gBAAuB;gBAAoB;gBAAgB;aAAsB;QACpI;QACA,cAAc;YACZ,QAAQ;gBAAC;gBAAkB;gBAAc;gBAAmB;gBAAiB;gBAA0B;aAAoB;YAC3H,YAAY;gBAAC;gBAAgB;gBAAc;gBAAY;gBAAe;gBAAe;aAAmB;YACxG,eAAe;gBAAC;gBAAuB;gBAAsB;gBAAqB;gBAAyB;aAAW;YACtH,aAAa;gBAAC;gBAAuB;gBAAiB;gBAAoB;gBAAuB;aAAqB;YACtH,oBAAoB;gBAAC;gBAAsB;gBAAc;gBAAgB;gBAAoB;aAAoB;YACjH,gBAAgB;gBAAC;gBAAuB;gBAAsB;gBAAa;gBAAuB;aAAqB;YACvH,kBAAkB;gBAAC;gBAAwB;gBAAe;gBAA0B;gBAAmB;aAAkB;YACzH,uBAAuB;gBAAC;gBAAqB;gBAAc;gBAAmB;gBAAkB;aAAqB;QACvH;QACA,cAAc;YACZ,QAAQ;gBAAC;gBAAgB;gBAAmB;gBAAsB;gBAAkB;gBAAyB;aAAiB;YAC9H,YAAY;gBAAC;gBAAe;gBAAiB;gBAAuB;gBAAwB;aAAkB;YAC9G,eAAe;gBAAC;gBAAuB;gBAAwB;gBAAqB;gBAAsB;aAAe;YACzH,aAAa;gBAAC;gBAAqB;gBAAqB;gBAAuB;gBAAyB;aAAyB;YACjI,oBAAoB;gBAAC;gBAAmB;gBAAc;gBAAqB;gBAAsB;aAAkB;YACnH,gBAAgB;gBAAC;gBAAoB;gBAAuB;gBAAa;gBAAmB;aAAsB;YAClH,kBAAkB;gBAAC;gBAAa;gBAAiB;gBAAuB;gBAAe;aAAyB;YAChH,uBAAuB;gBAAC;gBAAc;gBAAmB;gBAA2B;gBAAkB;aAAuB;QAC/H;QACA,WAAW;YACT,QAAQ;gBAAC;gBAAiB;gBAAqB;gBAAiB;gBAAwB;gBAAyB;aAAgB;YACjI,YAAY;gBAAC;gBAAoB;gBAAyB;gBAAe;gBAAkB;aAAiB;YAC5G,eAAe;gBAAC;gBAAmB;gBAAsB;gBAAwB;gBAAsB;aAAmB;YAC1H,aAAa;gBAAC;gBAAmB;gBAAwB;gBAAyB;gBAAuB;aAAiB;YAC1H,oBAAoB;gBAAC;gBAAsB;gBAAoB;gBAAgB;gBAAgB;aAAwB;YACvH,gBAAgB;gBAAC;gBAAoB;gBAAoB;gBAAa;gBAAoB;aAAuB;YACjH,kBAAkB;gBAAC;gBAAc;gBAAkB;gBAAmB;gBAAqB;aAAe;YAC1G,uBAAuB;gBAAC;gBAAwB;gBAAoB;gBAAmB;gBAAkB;aAA2B;QACtI;QACA,UAAU;YACR,QAAQ;gBAAC;gBAAe;gBAAmB;gBAAkB;gBAAkB;gBAAyB;aAAkB;YAC1H,YAAY;gBAAC;gBAAsB;gBAAwB;gBAAuB;gBAAkB;aAAiB;YACrH,eAAe;gBAAC;gBAAmB;gBAAsB;gBAAoB;gBAAU;aAAoB;YAC3G,aAAa;gBAAC;gBAAqB;gBAAoB;gBAAmB;gBAAoB;aAAuB;YACrH,oBAAoB;gBAAC;gBAAqB;gBAAe;gBAAgB;gBAAsB;aAAoB;YACnH,gBAAgB;gBAAC;gBAAgB;gBAAoB;gBAAoB;gBAAiB;aAAuB;YACjH,kBAAkB;gBAAC;gBAAoB;gBAAkB;gBAAqB;gBAAS;aAAe;YACtG,uBAAuB;gBAAC;gBAAoB;gBAAkB;gBAAgB;gBAAqB;aAAe;QACpH;QACA,eAAe;YACb,QAAQ;gBAAC;gBAAiB;gBAAqB;gBAAmB;gBAAoB;gBAAkB;aAAc;YACtH,YAAY;gBAAC;gBAAuB;gBAAe;gBAAsB;gBAAoB;aAAsB;YACnH,eAAe;gBAAC;gBAAyB;gBAAuB;gBAAuB;gBAAsB;aAAsB;YACnI,aAAa;gBAAC;gBAAmB;gBAAoB;gBAAoB;gBAAuB;aAAwB;YACxH,oBAAoB;gBAAC;gBAAa;gBAAsB;gBAAqB;gBAAsB;aAAe;YAClH,gBAAgB;gBAAC;gBAAgB;gBAAuB;gBAAa;gBAAgB;aAAiB;YACtG,kBAAkB;gBAAC;gBAAgB;gBAAoB;gBAAsB;gBAAqB;aAAY;YAC9G,uBAAuB;gBAAC;gBAAiB;gBAAmB;gBAAkB;gBAAgB;aAAgB;QAChH;QACA,cAAc;YACZ,QAAQ;gBAAC;gBAAqB;gBAAoB;gBAAkB;gBAAe;gBAA0B;aAAe;YAC5H,YAAY;gBAAC;gBAAmB;gBAAuB;gBAAsB;gBAAe;aAAwB;YACpH,eAAe;gBAAC;gBAAe;gBAAiB;gBAAkB;gBAAuB;aAAuB;YAChH,aAAa;gBAAC;gBAAsB;gBAAkB;gBAAuB;gBAAiB;aAAmB;YACjH,oBAAoB;gBAAC;gBAAqB;gBAAgB;gBAAmB;gBAAmB;aAAe;YAC/G,gBAAgB;gBAAC;gBAAyB;gBAAmB;gBAAa;gBAAkB;aAAkB;YAC9G,kBAAkB;gBAAC;gBAAe;gBAAsB;gBAAgB;gBAAmB;aAAe;YAC1G,uBAAuB;gBAAC;gBAAsB;gBAAqB;gBAAkB;gBAAkB;aAAuB;QAChI;QACA,UAAU;YACR,QAAQ;gBAAC;gBAAgB;gBAAmB;gBAAkB;gBAAwB;gBAAc;aAAc;YAClH,YAAY;gBAAC;gBAAiB;gBAAmB;gBAAiB;gBAAe;aAAmB;YACpG,eAAe;gBAAC;gBAAmB;gBAAiB;gBAAuB;gBAAU;aAAqB;YAC1G,aAAa;gBAAC;gBAAmB;gBAAoB;gBAAwB;gBAAuB;aAAkB;YACtH,oBAAoB;gBAAC;gBAAc;gBAAgB;gBAAqB;gBAAwB;aAAiB;YACjH,gBAAgB;gBAAC;gBAAoB;gBAAa;gBAAkB;gBAAmB;aAAgB;YACvG,kBAAkB;gBAAC;gBAAU;gBAAgB;gBAAmB;gBAAmB;aAA6B;YAChH,uBAAuB;gBAAC;gBAAkB;gBAAmB;gBAAiB;gBAAkB;aAAiB;QACnH;IACF;IAEA,oBAAoB;QAClB,aAAa;YAAC;YAAW;YAAY;YAAe;YAAU;YAAa;YAAU;YAAU;YAAW;SAAc;QACxH,YAAY;YAAC;YAAoB;YAAmB;YAAgB;YAAc;YAAe;YAAe;SAAS;QACzH,aAAa;YAAC;YAAe;YAAW;YAAe;YAAiB;YAAc;YAAgB;SAAW;QACjH,eAAe;YAAC;YAAkB;YAAgB;YAAwB;YAAmB;YAAa;SAAQ;IACpH;IAEA,kCAAkC;IAClC,iBAAiB;QACf,aAAa;YACX,MAAM;YACN,UAAU;YACV,cAAc;gBAAC;gBAAqB;gBAAc;gBAAoB;aAAoB;YAC1F,eAAe;QACjB;QACA,iBAAiB;YACf,MAAM;YACN,UAAU;YACV,cAAc;gBAAC;gBAAgB;gBAAe;gBAAoB;aAAqB;YACvF,eAAe;QACjB;QACA,cAAc;YACZ,MAAM;YACN,UAAU;YACV,cAAc;gBAAC;gBAAkB;gBAAsB;gBAAkB;aAA0B;YACnG,eAAe;QACjB;QACA,aAAa;YACX,MAAM;YACN,UAAU;YACV,cAAc;gBAAC;gBAAoB;gBAAqB;gBAAoB;aAAgB;YAC5F,eAAe;QACjB;IACF;AACF;AAGO,MAAM;IACX,OAAO,gCACL,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,eAAoB,EACpB,QAAgB,EAChB,cAA0E,WAAW,EACrF;QACA,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;QAEzD,MAAM,WAAW,6BAA6B,eAAe,CAAC,YAAY;QAE1E,+CAA+C;QAC/C,MAAM,oBAAoB,IAAI,CAAC,wBAAwB,CAAC,iBAAiB;QACzE,MAAM,sBAAsB,IAAI,CAAC,2BAA2B,CAAC,UAAU;QACvE,MAAM,qBAAqB,IAAI,CAAC,qBAAqB,CAAC,UAAU;QAEhE,OAAO;YACL,UAAU;YACV,cAAc;YACd;YACA;YACA;YACA,cAAc,IAAI,CAAC,qBAAqB,CAAC,aAAa,mBAAmB;YACzE,eAAe,IAAI,CAAC,mBAAmB,CAAC,aAAa;YACrD,kBAAkB,IAAI,CAAC,qBAAqB,CAAC,mBAAmB;YAChE,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,UAAU,UAAU;QAChE;IACF;IAEA,OAAe,yBAAyB,eAAoB,EAAE,QAAa,EAAE;QAC3E,MAAM,YAAY,EAAE;QAEpB,IAAI,gBAAgB,UAAU,EAAE,UAAU,IAAI,CAAC,GAAG,gBAAgB,UAAU,CAAC,oBAAoB,CAAC;QAClG,IAAI,gBAAgB,SAAS,EAAE,UAAU,IAAI,CAAC,CAAC,eAAe,EAAE,gBAAgB,SAAS,EAAE;QAC3F,IAAI,gBAAgB,MAAM,EAAE,UAAU,IAAI,CAAC,CAAC,cAAc,EAAE,gBAAgB,MAAM,EAAE;QACpF,IAAI,gBAAgB,cAAc,EAAE,UAAU,IAAI,CAAC,CAAC,aAAa,EAAE,gBAAgB,cAAc,EAAE;QACnG,IAAI,gBAAgB,cAAc,EAAE,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,gBAAgB,cAAc,CAAC,SAAS,CAAC;QAEtG,kCAAkC;QAClC,UAAU,IAAI,IAAI,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG;QAEhD,OAAO;IACT;IAEA,OAAe,4BAA4B,QAAa,EAAE,QAAgB,EAAE;QAC1E,OAAO,SAAS,qBAAqB,CAAC,GAAG,CAAC,CAAA,cACxC,GAAG,YAAY,IAAI,EAAE,UAAU,EAC/B,KAAK,CAAC,GAAG;IACb;IAEA,OAAe,sBAAsB,QAAa,EAAE,iBAA2B,EAAE;QAC/E,OAAO,SAAS,kBAAkB,CAAC,MAAM,CAAC,CAAA,YACxC,kBAAkB,IAAI,CAAC,CAAA,WACrB,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU,WAAW,GAAG,OAAO,CAAC,QAAQ,OAE1E,KAAK,CAAC,GAAG;IACb;IAEA,OAAe,sBACb,WAAmB,EACnB,iBAA2B,EAC3B,mBAA6B,EAC7B;QACA,MAAM,SAAS;YACb,aAAa;gBAAC;gBAAe;gBAAa;aAAoB;YAC9D,iBAAiB;gBAAC;gBAAmB;gBAAa;aAAe;YACjE,cAAc;gBAAC;gBAAY;gBAAU;aAAU;YAC/C,aAAa;gBAAC;gBAAe;gBAAa;aAAY;QACxD;QAEA,OAAO,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY;IACnD;IAEA,OAAe,oBACb,WAAmB,EACnB,kBAA4B,EAC5B;QACA,MAAM,QAAQ;YACZ,aAAa;gBAAC;gBAAa;gBAAmB;aAAkB;YAChE,iBAAiB;gBAAC;gBAAsB;gBAAkB;aAAwB;YAClF,cAAc;gBAAC;gBAAW;gBAAc;aAAa;YACrD,aAAa;gBAAC;gBAAgB;gBAAa;aAAmB;QAChE;QAEA,OAAO,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY;IACjD;IAEA,OAAe,sBAAsB,iBAA2B,EAAE,kBAA4B,EAAE;QAC9F,IAAI,kBAAkB,MAAM,KAAK,KAAK,mBAAmB,MAAM,KAAK,GAAG;YACrE,OAAO;QACT;QAEA,MAAM,WAAW,iBAAiB,CAAC,EAAE;QACrC,MAAM,YAAY,kBAAkB,CAAC,EAAE;QAEvC,OAAO,CAAC,SAAS,EAAE,UAAU,MAAM,EAAE,UAAU;IACjD;IAEA,OAAe,qBAAqB,QAAgB,EAAE,QAAa,EAAE,eAAoB,EAAE;QACzF,OAAO;YACL,aAAa,GAAG,SAAS,gBAAgB,CAAC;YAC1C,qBAAqB,GAAG,SAAS,gBAAgB,CAAC;YAClD,kBAAkB,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC;YACzD,oBAAoB,SAAS,qBAAqB,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,EAAE,UAAU;QACvF;IACF;AACF;AAGO,eAAe,iCACpB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,eAAoB,EACpB,QAAgB,EAChB,cAA0E,WAAW,EACrF,YAAkB,EAClB,oBAA0B;IAG1B,MAAM,cAAc,wBAAwB,+BAA+B,CACzE,cAAc,cAAc,UAAU,iBAAiB,UAAU;IAGnE,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;IAEzD,sCAAsC;IACtC,MAAM,QAAQ;IAEd,uGAAuG;IACvG,MAAM,mBAAmB,cAAc,UAAU,MAAM,GAAG,MAAM,EAAE;IAClE,MAAM,mBAAmB,cAAc,UAAU,MAAM,GAAG,MAAM,EAAE;IAClE,MAAM,mBAAmB,yBAAyB;IAClD,MAAM,iBAAiB,0BAA0B;IAEjD,MAAM,SAAS,CAAC,kEAAkE,EAAE,SAAS,iHAAiH,EAAE,SAAS;;;YAG/M,EAAE,aAAa,EAAE,EAAE,aAAa;YAChC,EAAE,SAAS;cACT,EAAE,gBAAgB,UAAU,IAAI,uBAAuB;eACtD,EAAE,gBAAgB,SAAS,IAAI,gBAAgB,QAAQ,IAAI,wBAAwB;iBACjF,EAAE,gBAAgB,cAAc,IAAI,kBAAkB;kBACrD,EAAE,YAAY;;;qBAGX,EAAE,iBAAiB,IAAI,CAAC,SAAS,8CAA8C;oBAChF,EAAE,iBAAiB,IAAI,CAAC,SAAS,6BAA6B;4BACtD,EAAE,eAAe;uBACtB,EAAE,iBAAiB;;;mBAGvB,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;0BAClC,EAAE,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;wBAChD,EAAE,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;gBACxD,EAAE,SAAS,YAAY,EAAE,aAAa,EAAE,EAAE,yBAAyB,UAAU,cAAc;;;uBAGpF,EAAE,SAAS,oBAAoB,EAAE,eAAe,iHAAiH,EAAE,SAAS;;;;;;;;6CAQtJ,EAAE,SAAS;0DACE,EAAE,aAAa;;;;;;;;;;;;;;;;;;;qEAmBJ,EAAE,aAAa;uBAC7D,EAAE,aAAa,IAAI,EAAE,SAAS,kDAAkD,CAAC;IAEtG,IAAI;QACF,gEAAgE;QAChE,MAAM,gBAAgB,CAAC,+BAA+B,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;;;;;;;;yEAQzC,CAAC;QAEtE,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC,SAAS;QACpD,IAAI,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QAE1C,0CAA0C;QAC1C,WAAW,sBAAsB;QAEjC,uEAAuE;QACvE,MAAM,aAAa;YAAC;YAAa;YAAY;YAAa;YAAQ;YAAqB;SAAa;QACpG,MAAM,WAAW;YAAC;YAAY;YAAa;YAAe;YAAY;YAAa;SAAY;QAE/F,OAAO;YACL,UAAU;YACV,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YACnE,iBAAiB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QACxE;IACF,EAAE,OAAO,OAAO;QAEd,uDAAuD;QACvD,IAAI;YAEF,MAAM,2BAA2B,CAAC,oCAAoC,EAAE,aAAa,IAAI,EAAE,aAAa,IAAI,EAAE,SAAS;;;;;;sBAMvG,EAAE,SAAS;;;;;;;;uCAQM,CAAC;YAElC,0DAA0D;YAC1D,MAAM,uBAAuB,CAAC,mCAAmC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;qFAC1C,CAAC;YAEhF,MAAM,cAAc,MAAM,MAAM,eAAe,CAAC,2BAA2B;YAC3E,MAAM,gBAAgB,YAAY,QAAQ,CAAC,IAAI,GAAG,IAAI;YAGtD,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,iBAAiB;YACnB;QAEF,EAAE,OAAO,YAAY;YAEnB,gDAAgD;YAChD,IAAI;gBACF,MAAM,kBAAkB,CAAC,mCAAmC,EAAE,aAAa,IAAI,EAAE,SAAS;;;;;;2EAMvB,CAAC;gBAEpE,yEAAyE;gBACzE,MAAM,2BAA2B,CAAC,uCAAuC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;2FAC9C,CAAC;gBAEpF,MAAM,kBAAkB,MAAM,MAAM,eAAe,CAAC,kBAAkB;gBACtE,MAAM,oBAAoB,gBAAgB,QAAQ,CAAC,IAAI,GAAG,IAAI;gBAG9D,OAAO;oBACL,UAAU;oBACV,UAAU;oBACV,iBAAiB;gBACnB;YAEF,EAAE,OAAO,gBAAgB;gBAEvB,8DAA8D;gBAC9D,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,YAAY;gBAE7B,MAAM,qBAAqB;oBACzB,GAAG,aAAa,CAAC,EAAE,SAAS,WAAW,CAAC;oBACxC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,UAAU;oBACvC,GAAG,SAAS,WAAW,EAAE,cAAc;oBACvC,CAAC,QAAQ,EAAE,aAAa,OAAO,CAAC;oBAChC,CAAC,OAAO,EAAE,aAAa,UAAU,CAAC;iBACnC;gBAED,OAAO;oBACL,UAAU,kBAAkB,CAAC,WAAW,mBAAmB,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU;oBACpF,UAAU;oBACV,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,eAAe,oCACpB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,eAAoB,EACpB,QAAgB,EAChB,cAA0E,WAAW,EACrF,YAAkB,EAClB,oBAA0B;IAG1B,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;IAEzD,sCAAsC;IACtC,MAAM,QAAQ;IAEd,2FAA2F;IAC3F,MAAM,mBAAmB,cAAc,UAAU,MAAM,GAAG,MAAM,EAAE;IAClE,MAAM,mBAAmB,yBAAyB;IAClD,MAAM,iBAAiB,0BAA0B;IAEjD,MAAM,SAAS,CAAC,4DAA4D,EAAE,aAAa,0BAA0B,EAAE,SAAS;;;kBAGhH,EAAE,SAAS;YACjB,EAAE,aAAa,EAAE,EAAE,aAAa;YAChC,EAAE,SAAS;qBACF,EAAE,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,IAAI,oBAAoB;uBAC7E,EAAE,SAAS;kBAChB,EAAE,YAAY;;;qBAGX,EAAE,iBAAiB,IAAI,CAAC,SAAS,8CAA8C;4BACxE,EAAE,eAAe;2BAClB,EAAE,iBAAiB;wBACtB,EAAE,aAAa,EAAE,EAAE,yBAAyB,UAAU,cAAc;;;qBAGvE,EAAE,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;wBAC3C,EAAE,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;mBACrD,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;;;gJAGoF,EAAE,SAAS,yBAAyB,EAAE,aAAa,2BAA2B,EAAE,SAAS;;;;;;;;;yDAShL,EAAE,SAAS;;;sBAG9C,EAAE,SAAS;AACjC,EAAE,0BAA0B,UAAU,cAAc,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,OAAO,OAAO,OAAO,CAAC,KAAK,0BAA0B,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;;;;;;;;sBAQ/I,EAAE,aAAa;;;uBAGd,EAAE,aAAa,IAAI,EAAE,SAAS,kDAAkD,CAAC;IAEtG,IAAI;QACF,gEAAgE;QAChE,MAAM,gBAAgB,CAAC,+BAA+B,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;;;;;;;;yEAQzC,CAAC;QAEtE,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC,SAAS;QACpD,IAAI,cAAc,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QAE7C,0CAA0C;QAC1C,cAAc,sBAAsB;QAEpC,6CAA6C;QAC7C,MAAM,aAAa;YAAC;YAAmB;YAAmB;YAAsB;YAAoB;SAAmB;QACvH,MAAM,WAAW,SAAS,WAAW,CAAC,MAAM,CAAC;YAAC;YAAuB;YAAmB;SAAiB;QAEzG,OAAO;YACL,aAAa;YACb,WAAW,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;YACpE,SAAS,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QAChE;IACF,EAAE,OAAO,OAAO;QAEd,yDAAyD;QACzD,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;QACtD,MAAM,YAAY,aAAa;QAE/B,MAAM,kBAAkB;YAAC;YAAa;YAAS;YAAe;YAAe;YAAW;YAAW;YAAS;SAAU;QACtH,MAAM,cAAc;YAAC;YAAY;YAAc;YAAS;YAAS;YAAS;YAAW;YAAO;SAAQ;QACpG,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,wBAAwB;YAC5B,GAAG,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC,YAAY,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,IAAI,EAAE,UAAU;YAC5I,GAAG,cAAc,CAAC,YAAY,eAAe,MAAM,CAAC,EAAE;YACtD,GAAG,WAAW,CAAC,YAAY,YAAY,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,EAAE,UAAU;YAC5I,CAAC,MAAM,EAAE,SAAS,QAAQ,EAAE,WAAW,CAAC,CAAC,YAAY,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc;YACtJ,GAAG,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,QAAQ,CAAC;YAC1I,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc;YACvG,CAAC,oBAAoB,EAAE,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc;YAC5F,GAAG,cAAc,CAAC,CAAC,YAAY,CAAC,IAAI,eAAe,MAAM,CAAC,EAAE;YAC5D,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,EAAE,UAAU;YACtG,GAAG,SAAS,QAAQ,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,WAAW,CAAC;YAC5G,GAAG,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,aAAa,EAAE,UAAU;YACtG,CAAC,MAAM,EAAE,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,EAAE;YACjI,GAAG,SAAS,UAAU,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc;YACnG,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU;YAC9F,GAAG,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,gBAAgB,MAAM,CAAC,CAAC,QAAQ,CAAC;YACpJ,CAAC,KAAK,EAAE,SAAS,iBAAiB,EAAE,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc;SAC1G;QAED,OAAO;YACL,aAAa,qBAAqB,CAAC,UAAU;YAC7C,WAAW;YACX,SAAS,eAAe,CAAC,YAAY,gBAAgB,MAAM,CAAC;QAC9D;IACF;AACF;AAGO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,eAAoB,EACpB,QAAgB,EAChB,cAA0E,WAAW,EACrF,YAAkB,EAClB,oBAA0B;IAgB1B,MAAM,cAAc,wBAAwB,+BAA+B,CACzE,cAAc,cAAc,UAAU,iBAAiB,UAAU;IAGnE,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;IAEzD,sCAAsC;IACtC,MAAM,QAAQ;IAEd,oEAAoE;IACpE,MAAM,mBAAmB,cAAc,UAAU,MAAM,GAAG,MAAM,EAAE;IAClE,MAAM,mBAAmB,cAAc,UAAU,MAAM,GAAG,MAAM,EAAE;IAClE,MAAM,mBAAmB,yBAAyB;IAClD,MAAM,iBAAiB,0BAA0B;IAEjD,kEAAkE;IAClE,MAAM,qBAAqB,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAEnE,4CAA4C;IAE5C,wDAAwD;IACxD,MAAM,uBAAuB,qBAAqB;IAClD,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;IAE1D,oDAAoD;IACpD,MAAM,iBAAiB;QACrB;QAAkB;QAAgB;QAAoB;QAAiB;QACvE;QAAiB;QAAkB;QAAe;QAAc;KACjE;IAED,MAAM,mBAAmB,cAAc,CAAC,kBAAkB,eAAe,MAAM,CAAC;IAEhF,MAAM,mBAAmB,CAAC;oBACR,EAAE,iBAAiB;;;;;;;;;;;;;;;AAevC,EAAE,wBAAwB,kBAAkB,cAAc,UAAU,iBAAiB;;iBAEpE,EAAE,gBAAgB;0FACuD,EAAE,aAAa,IAAI,EAAE,SAAS;;;uDAGjE,EAAE,aAAa;;;;6CAIzB,EAAE,aAAa;;mDAET,EAAE,aAAa;;;;;;;;0BAQxC,EAAE,mBAAmB;;AAE/C,CAAC;IAEC,6BAA6B;IAC7B,MAAM,YAAY;QAChB;QAAiB;QAAc;QAAa;QAAmB;QAC/D;QAAW;QAAa;QAAmB;QAAY;KACxD;IAED,MAAM,mBAAmB,SAAS,CAAC,kBAAkB,UAAU,MAAM,CAAC;IAEtE,MAAM,gBAAgB,CAAC,4FAA4F,EAAE,aAAa,0BAA0B,EAAE,SAAS;;AAEzK,EAAE,iBAAiB;;;;;;;;;;;YAWP,EAAE,aAAa,EAAE,EAAE,aAAa;YAChC,EAAE,SAAS;qBACF,EAAE,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,IAAI,oBAAoB;uBAC7E,EAAE,SAAS;YACtB,EAAE,SAAS;kBACL,EAAE,YAAY;;;qBAGX,EAAE,iBAAiB,IAAI,CAAC,SAAS,iFAAiF;oBACnH,EAAE,iBAAiB,IAAI,CAAC,SAAS,+CAA+C;4BACxE,EAAE,eAAe;2BAClB,EAAE,iBAAiB;wBACtB,EAAE,aAAa,EAAE,EAAE,yBAAyB,UAAU,cAAc;;;AAG5F,EAAE,uBAAuB,CAAC;sBACJ,EAAE,qBAAqB,iBAAiB,EAAE,KAAK,SAAS,yCAAyC;sBACjG,EAAE,qBAAqB,iBAAiB,EAAE,KAAK,SAAS,uCAAuC;mBAClG,EAAE,qBAAqB,cAAc,EAAE,KAAK,SAAS,kCAAkC;qBACrF,EAAE,qBAAqB,gBAAgB,EAAE,KAAK,SAAS,qCAAqC;mBAC9F,EAAE,qBAAqB,cAAc,EAAE,KAAK,SAAS,mCAAmC;0BACjF,EAAE,qBAAqB,qBAAqB,EAAE,KAAK,SAAS,qBAAqB;AAC3G,CAAC,GAAG,2DAA2D;;;AAG/D,EAAE,eAAe,CAAC;uBACK,EAAE,aAAa,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,IAAK,EAAE,KAAK,EAAE,KAAK,SAAS,uBAAuB;uBACxF,EAAE,aAAa,YAAY,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,wBAAwB;gBACtF,EAAE,aAAa,MAAM,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,IAAK,EAAE,IAAI,EAAE,KAAK,SAAS,uBAAuB;mBACtF,EAAE,aAAa,QAAQ,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,qBAAqB;AAC3F,CAAC,GAAG,4DAA4D;;;;UAItD,EAAE,aAAa;;;;;;;;;;;;;;;;;;yCAkBgB,EAAE,SAAS;;;;;iBAKnC,EAAE,aAAa;qBACX,EAAE,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,IAAI,gBAAgB,WAAW,IAAI,oBAAoB;mBAChH,EAAE,gBAAgB,cAAc,IAAI,gBAAgB,WAAW,IAAI,sBAAsB;mBACzF,EAAE,gBAAgB,cAAc,IAAI,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC,CAAC;kBAChF,EAAE,gBAAgB,KAAK,IAAI,yBAAyB;kBACpD,EAAE,gBAAgB,MAAM,IAAI,gBAAgB,UAAU,IAAI,wCAAwC;;;oBAGhG,EAAE,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;wBAC1C,EAAE,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;mBACrD,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;oBACxC,EAAE,SAAS,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;;sBAE3C,EAAE,SAAS,WAAW,GAAG;AAC/C,EAAE,wBAAwB,UAAU;;;;;;;;;;;;kDAYc,EAAE,SAAS;;;;;;;;;;;;4BAYjC,EAAE,aAAa,UAAU,EAAE,SAAS;;;;;;;;;;;;;;iEAcC,EAAE,aAAa;;;;;;;gDAOhC,EAAE,aAAa;;;;;;;;;;;;;wBAavC,EAAE,SAAS;kCACD,EAAE,SAAS;mDACM,EAAE,SAAS;;;;;;;;kEAQI,EAAE,SAAS;;;;gEAIb,EAAE,SAAS;;;;0CAIjC,EAAE,SAAS;;gBAErC,EAAE,mBAAmB;;;;;;;;;;;;;;;;;;;;;oHAqB+E,EAAE,aAAa;;;2BAGxG,EAAE,iBAAiB,GAAG,EAAE,wBAAwB,kBAAkB,cAAc,UAAU;;;;;;;uDAO9D,CAAC;IAEtD,IAAI;QAEF,gEAAgE;QAChE,MAAM,gBAAgB,CAAC,+BAA+B,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;;;;;;;;;yEASzC,CAAC;QAEtE,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC,gBAAgB;QAC3D,IAAI,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QAE1C,mEAAmE;QACnE,WAAW,sBAAsB;QAGjC,+BAA+B;QAC/B,MAAM,oBAAoB,SAAS,KAAK,CAAC;QACzC,MAAM,kBAAkB,SAAS,KAAK,CAAC;QACvC,MAAM,gBAAgB,SAAS,KAAK,CAAC;QACrC,MAAM,mBAAmB,SAAS,KAAK,CAAC;QACxC,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,WAAW,SAAS,KAAK,CAAC;QAChC,MAAM,cAAc,SAAS,KAAK,CAAC;QAGnC,mEAAmE;QACnE,MAAM,eAAe,sBAAsB,mBAAmB,CAAC,EAAE,EAAE,UAAU;QAC7E,MAAM,aAAa,sBAAsB,iBAAiB,CAAC,EAAE,EAAE,UAAU;QACzE,MAAM,WAAW,sBAAsB,eAAe,CAAC,EAAE,EAAE,UAAU,GAAG,aAAa,CAAC,EAAE,UAAU;QAClG,MAAM,cAAc,sBAAsB,kBAAkB,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,UAAU;QACnH,MAAM,UAAU,sBAAsB,cAAc,CAAC,EAAE,EAAE,UAAU;QAEnE,6DAA6D;QAC7D,MAAM,cAAc,MAAM,0IAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAC9D,cACA,cACA,UACA,UACA,aACA,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,EACrD,gBAAgB,cAAc;QAGhC,MAAM,eAAe,sBAAsB,UAAU,CAAC,EAAE,EAAE,UAAU,YAAY,OAAO;QAEvF,MAAM,kBAAkB,sBAAsB,aAAa,CAAC,EAAE,EAAE,UAAU;QAE1E,oCAAoC;QACpC,MAAM,kBAAkB,+BAA+B,cAAc,UAAU;QAE/E,iDAAiD;QACjD,MAAM,gBAAgB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAClE,cACA,cACA,UACA,UACA,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,EACrD,gBAAgB,cAAc;QAIhC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,iBAAiB,sBAAsB,aAAa,CAAC,EAAE,EAAE,UAAU,CAAC,4EAA4E,EAAE,aAAa,+HAA+H,CAAC;YAC/R;YACA;YACA,UAAU,cAAc,KAAK;YAC7B,iBAAiB;YACjB,aAAa;YACb,WAAW,aAAa,+CAA+C;QACzE;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,UAAU,GAAG,aAAa,GAAG,EAAE,cAAc;YAC7C,aAAa,CAAC,QAAQ,EAAE,aAAa,aAAa,EAAE,UAAU;YAC9D,SAAS,CAAC,oBAAoB,EAAE,aAAa,aAAa,EAAE,aAAa,aAAa,EAAE,SAAS,gCAAgC,CAAC;YAClI,cAAc,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC;YAC5C,iBAAiB;gBAAC;gBAAmB;gBAAmB;aAAwB;YAChF,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,UAAU;gBAAC;gBAAa;gBAAU;gBAAY;gBAAY;aAAgB;YAC1E,iBAAiB;gBAAE,OAAO;oBAAC;oBAAa;oBAAU;oBAAY;oBAAY;iBAAgB;YAAC;YAC3F,aAAa;gBAAE,SAAS,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC;YAAC;YACvD,WAAW,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC;QAC3C;IACF;IAEA,uDAAuD;IACvD,IAAI;QAEF,MAAM,mBAAmB,CAAC,kBAAkB,EAAE,SAAS,aAAa,EAAE,aAAa,IAAI,EAAE,aAAa,IAAI,EAAE,SAAS;;;;2BAI9F,EAAE,aAAa,IAAI,EAAE,SAAS;;kBAEvC,EAAE,SAAS;;;;;;gBAMb,EAAE,SAAS;;;;;;;;;;;;;;;;;kDAiBuB,CAAC;QAE/C,iDAAiD;QACjD,MAAM,qBAAqB,CAAC,0BAA0B,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;;;;;;;2EAOvC,CAAC;QAExE,MAAM,cAAc,MAAM,MAAM,eAAe,CAAC,mBAAmB;QACnE,IAAI,gBAAgB,YAAY,QAAQ,CAAC,IAAI,GAAG,IAAI;QAEpD,8DAA8D;QAC9D,gBAAgB,sBAAsB;QAGtC,2BAA2B;QAC3B,MAAM,oBAAoB,cAAc,KAAK,CAAC;QAC9C,MAAM,gBAAgB,cAAc,KAAK,CAAC;QAE1C,MAAM,eAAe,sBAAsB,oBAAoB,iBAAiB,CAAC,EAAE,CAAC,IAAI,KAAK;QAC7F,MAAM,oBAAoB,sBAAsB,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI,KAAK,oBAAoB;QAE9G,oCAAoC;QACpC,MAAM,gBAAgB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAClE,cAAc,cAAc,UAAU,UACtC,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,EACrD,gBAAgB,cAAc;QAGhC,OAAO;YACL,UAAU,GAAG,aAAa,GAAG,EAAE,cAAc;YAC7C,aAAa,CAAC,QAAQ,EAAE,aAAa,aAAa,EAAE,UAAU;YAC9D,SAAS;YACT,iBAAiB,+BAA+B,cAAc,UAAU;YACxE,cAAc;YACd,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,UAAU,cAAc,KAAK;YAC7B,iBAAiB;YACjB,aAAa;gBAAE,SAAS;YAAkB;YAC1C,WAAW;QACb;IAEF,EAAE,OAAO,YAAY;QAEnB,gDAAgD;QAChD,IAAI;YACF,MAAM,kBAAkB,CAAC,uCAAuC,EAAE,aAAa,IAAI,EAAE,SAAS;;;;;;;;;+EASrB,CAAC;YAE1E,gEAAgE;YAChE,MAAM,yBAAyB,CAAC,8BAA8B,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG;;;;;;;6EAO/C,CAAC;YAExE,MAAM,kBAAkB,MAAM,MAAM,eAAe,CAAC,kBAAkB;YACtE,IAAI,oBAAoB,gBAAgB,QAAQ,CAAC,IAAI,GAAG,IAAI;YAE5D,kEAAkE;YAClE,oBAAoB,sBAAsB;YAG1C,wCAAwC;YACxC,MAAM,oBAAoB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CACtE,cAAc,cAAc,UAAU,UACtC,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,EACrD,gBAAgB,cAAc;YAGhC,OAAO;gBACL,UAAU,GAAG,aAAa,GAAG,EAAE,cAAc;gBAC7C,aAAa,CAAC,QAAQ,EAAE,aAAa,aAAa,EAAE,UAAU;gBAC9D,SAAS;gBACT,iBAAiB,+BAA+B,cAAc,UAAU;gBACxE,cAAc,sBAAsB,oBAAoB;gBACxD,iBAAiB;gBACjB,cAAc;gBACd,YAAY;gBACZ,UAAU,kBAAkB,KAAK;gBACjC,iBAAiB;gBACjB,aAAa;oBAAE,SAAS,sBAAsB,oBAAoB;gBAAW;gBAC7E,WAAW,sBAAsB,oBAAoB;YACvD;QAEF,EAAE,OAAO,gBAAgB;YAEvB,8DAA8D;YAC9D,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAE5C,6CAA6C;YAC7C,MAAM,mBAAmB,MAAM,yIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CACrE,cAAc,cAAc,UAAU,UACtC,gBAAgB,QAAQ,IAAI,gBAAgB,SAAS,EACrD,gBAAgB,cAAc;YAGhC,OAAO;gBACL,UAAU,GAAG,aAAa,GAAG,EAAE,cAAc;gBAC7C,aAAa,CAAC,QAAQ,EAAE,aAAa,aAAa,EAAE,UAAU;gBAC9D,SAAS,sBAAsB,GAAG,aAAa,IAAI,EAAE,SAAS,6IAA6I,EAAE,aAAa,GAAG,EAAE,WAAW;gBAC1O,iBAAiB,+BAA+B,cAAc,UAAU;gBACxE,cAAc,sBAAsB,oBAAoB;gBACxD,iBAAiB;gBACjB,cAAc;gBACd,YAAY;gBACZ,UAAU,iBAAiB,KAAK;gBAChC,iBAAiB;gBACjB,aAAa;oBAAE,SAAS,sBAAsB,oBAAoB;gBAAW;gBAC7E,WAAW,sBAAsB,oBAAoB;YACvD;QACF;IACF;AACF;AAEA,qDAAqD;AACrD,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,eAAe;QACnB,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,YAAY;AAC5D;AAEA,SAAS,oBAAoB,QAAgB;IAC3C,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;IAE3D,qDAAqD;IACrD,MAAM,YAAY;QAChB;QAAiB;QAAc;QAAa;QAAmB;QAC/D;QAAW;QAAa;QAAmB;QAAY;KACxD;IAED,MAAM,gBAAgB,SAAS,CAAC,kBAAkB,UAAU,MAAM,CAAC;IAEnE,0DAA0D;IAC1D,MAAM,cAAc;QAClB,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,mBAAmB;YACjB;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,mBAAmB;YACjB;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,YAAY,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,gBAAgB;IAC5E,MAAM,YAAY,kBAAkB,UAAU,MAAM;IAEpD,OAAO,SAAS,CAAC,UAAU;AAC7B;AAEA,SAAS,+BAA+B,YAAoB,EAAE,QAAgB,EAAE,QAAa;IAC3F,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;IACtD,MAAM,YAAY,aAAa;IAE/B,MAAM,iBAAiB;QACrB,CAAC,qBAAqB,EAAE,aAAa,SAAS,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC,SAAS,EAAE,SAAS,wBAAwB,EAAE,aAAa,CAAC,CAAC;QAC9D,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE,aAAa,eAAe,CAAC;QACzD,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE,aAAa,MAAM,CAAC;QAChE,CAAC,4BAA4B,EAAE,aAAa,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7D,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,gCAAgC,CAAC;QACnE,CAAC,oBAAoB,EAAE,SAAS,GAAG,EAAE,aAAa,SAAS,CAAC;QAC5D,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,SAAS,wBAAwB,CAAC;KACjE;IAED,MAAM,sBAAsB;QAC1B,CAAC,wCAAwC,EAAE,aAAa,CAAC,CAAC;QAC1D,CAAC,0BAA0B,EAAE,aAAa,oBAAoB,CAAC;QAC/D,CAAC,yCAAyC,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC,0CAA0C,EAAE,aAAa,CAAC,CAAC;QAC5D,CAAC,iBAAiB,EAAE,aAAa,sBAAsB,CAAC;QACxD,CAAC,6BAA6B,EAAE,aAAa,YAAY,CAAC;QAC1D,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC;QAC9C,CAAC,wBAAwB,EAAE,aAAa,QAAQ,CAAC;KAClD;IAED,MAAM,iBAAiB;QACrB,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,YAAY,SAAS,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;QAC5E,CAAC,mCAAmC,EAAE,aAAa,QAAQ,CAAC;QAC5D,CAAC,sBAAsB,EAAE,SAAS,aAAa,CAAC,YAAY,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,iBAAiB,EAAE,SAAS,WAAW,CAAC,YAAY,SAAS,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC;QAC3F,CAAC,2BAA2B,EAAE,aAAa,YAAY,CAAC;QACxD,CAAC,wCAAwC,EAAE,aAAa,CAAC,CAAC;QAC1D,CAAC,KAAK,EAAE,aAAa,2BAA2B,CAAC;QACjD,CAAC,2BAA2B,EAAE,aAAa,aAAa,CAAC;KAC1D;IAED,2CAA2C;IAC3C,MAAM,WAAW;WAAI;WAAmB;WAAwB;KAAe;IAC/E,MAAM,gBAAgB,EAAE;IAExB,mDAAmD;IACnD,cAAc,IAAI,CAAC,cAAc,CAAC,YAAY,eAAe,MAAM,CAAC;IACpE,cAAc,IAAI,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,IAAI,oBAAoB,MAAM,CAAC;IACpF,cAAc,IAAI,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,IAAI,eAAe,MAAM,CAAC;IAE1E,OAAO;AACT;AAEA,mFAAmF;AACnF,SAAS,yBAAyB,WAAgB,EAAE,YAAoB,EAAE,QAAgB,EAAE,QAAa,EAAE,WAAmB;IAC5H,MAAM,eAAe,YAAY,YAAY,IAAI;IACjD,MAAM,QAAQ;QACZ,CAAC,oBAAoB,EAAE,SAAS,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC;QAC1D,CAAC,uBAAuB,EAAE,aAAa,UAAU,CAAC;QAClD,CAAC,mBAAmB,EAAE,aAAa,oBAAoB,CAAC;KACzD;IAED,MAAM,OAAO;QACX,CAAC,oCAAoC,CAAC;QACtC,CAAC,2BAA2B,CAAC;QAC7B,CAAC,8BAA8B,CAAC;KACjC;IAED,OAAO;QACL,SAAS,GAAG,YAAY,gBAAgB,CAAC,MAAM,EAAE,aAAa,QAAQ,EAAE,SAAS,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,MAAM,EAAE,YAAY,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,YAAY,mBAAmB,CAAC,EAAE,CAAC,2BAA2B,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACpP,iBAAiB;QACjB,cAAc,IAAI,CAAC,EAAE;IACvB;AACF;AAEA,SAAS,wBAAwB,WAAgB,EAAE,YAAoB,EAAE,QAAgB,EAAE,QAAa,EAAE,WAAmB;IAC3H,MAAM,eAAe,YAAY,YAAY,IAAI;IACjD,MAAM,QAAQ;QACZ,CAAC,4BAA4B,EAAE,aAAa,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7D,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,uCAAuC,CAAC;KAC1C;IAED,MAAM,OAAO;QACX,CAAC,uCAAuC,CAAC;QACzC,CAAC,6CAA6C,CAAC;QAC/C,CAAC,4CAA4C,CAAC;KAC/C;IAED,OAAO;QACL,SAAS,GAAG,YAAY,gBAAgB,CAAC,IAAI,EAAE,aAAa,iBAAiB,EAAE,SAAS,wBAAwB,EAAE,SAAS,WAAW,CAAC,EAAE,CAAC,0BAA0B,EAAE,YAAY,mBAAmB,CAAC,EAAE,CAAC,gDAAgD,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACnR,iBAAiB;QACjB,cAAc,IAAI,CAAC,EAAE;IACvB;AACF;AAEA,SAAS,wBAAwB,WAAgB,EAAE,YAAoB,EAAE,QAAgB,EAAE,QAAa,EAAE,WAAmB;IAC3H,MAAM,eAAe,YAAY,YAAY,IAAI;IACjD,MAAM,QAAQ;QACZ,CAAC,+BAA+B,EAAE,aAAa,CAAC,CAAC;QACjD,CAAC,6CAA6C,CAAC;QAC/C,CAAC,kDAAkD,CAAC;KACrD;IAED,MAAM,OAAO;QACX,CAAC,6CAA6C,CAAC;QAC/C,CAAC,oDAAoD,CAAC;QACtD,CAAC,mEAAmE,CAAC;KACtE;IAED,OAAO;QACL,SAAS,GAAG,YAAY,gBAAgB,CAAC,IAAI,EAAE,aAAa,UAAU,EAAE,YAAY,iBAAiB,CAAC,EAAE,CAAC,gCAAgC,EAAE,SAAS,+BAA+B,EAAE,aAAa,WAAW,EAAE,YAAY,mBAAmB,CAAC,EAAE,CAAC,qEAAqE,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACjV,iBAAiB;QACjB,cAAc,IAAI,CAAC,EAAE;IACvB;AACF;AAEA,SAAS,uBAAuB,WAAgB,EAAE,YAAoB,EAAE,QAAgB,EAAE,QAAa,EAAE,WAAmB;IAC1H,MAAM,eAAe,YAAY,YAAY,IAAI;IACjD,MAAM,QAAQ;QACZ,CAAC,oBAAoB,EAAE,aAAa,QAAQ,CAAC;QAC7C,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,oCAAoC,CAAC;KACvC;IAED,MAAM,OAAO;QACX,CAAC,4BAA4B,CAAC;QAC9B,CAAC,mBAAmB,CAAC;QACrB,CAAC,4BAA4B,CAAC;KAC/B;IAED,OAAO;QACL,SAAS,GAAG,YAAY,gBAAgB,CAAC,IAAI,EAAE,aAAa,QAAQ,EAAE,SAAS,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,YAAY,mBAAmB,CAAC,EAAE,CAAC,wCAAwC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACtN,iBAAiB;QACjB,cAAc,IAAI,CAAC,EAAE;IACvB;AACF;AAGO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,kBAAqB,KAAU,EAAE,KAAa;IAC5D,MAAM,WAAW;WAAI;KAAM,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACxD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;AACpC;AAGO,SAAS,yBACd,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,OAAY;IAEZ,OAAO;QACL,UAAU,GAAG,aAAa,GAAG,EAAE,cAAc;QAC7C,OAAO;QACP,MAAM;IACR;AACF;AAEO,SAAS,4BACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,IAAY;IAEZ,OAAO;QACL,aAAa,CAAC,QAAQ,EAAE,aAAa,aAAa,EAAE,UAAU;QAC9D,WAAW;IACb;AACF;AAEO,SAAS,oBACd,YAAoB,EACpB,IAAY,EACZ,OAAY;IAEZ,OAAO;QACL,KAAK;QACL,SAAS;QACT,SAAS;IACX;AACF;AAGO,SAAS,uBACd,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB;IAEhB,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;IAEzD,OAAO;QACL,mBAAmB,SAAS,WAAW;QACvC,mBAAmB,SAAS,kBAAkB;QAC9C,kBAAkB,SAAS,MAAM;QACjC,oBAAoB,SAAS,qBAAqB;QAClD,uBAAuB,SAAS,aAAa;IAC/C;AACF;AAEO,MAAM,yBAAyB;IACpC,mBAAmB;QACjB,OAAO;YAAC;YAAc;YAAa;YAAY;YAAgB;SAAW;QAC1E,MAAM;YAAC;YAAa;YAAa;YAAQ;YAAa;SAAc;QACpE,UAAU;YAAC;YAAa;YAAa;YAAc;YAAgB;SAAS;IAC9E;IACA,qBAAqB;QACnB,cAAc;YAAC;YAAqB;YAAkB;SAAiB;IACzE;AACF;AAEO,MAAM,2BAA2B;IACtC,gBAAgB;QACd;QAAkB;QAAoB;QAAiB;QACvD;QAAqB;QAAiB;QAAmB;QACzD;QAAoB;QAAmB;QAAgB;KACxD;IACD,gBAAgB;QACd;QAAa;QAAY;QAAc;QAAa;QACpD;QAAa;QAAgB;QAAa;QAAc;QACxD;QAAQ;QAAgB;QAAc;KACvC;IACD,oBAAoB;QAClB;QAAgB;QAAoB;QAAa;QACjD;QAAY;QAAW;QAAmB;QAC1C;QAAW;QAAkB;QAAa;KAC3C;AACH;AAGO,MAAM;IACX,OAAe,mBAAgC,IAAI,MAAM;IACzD,OAAe,aAAa,IAAI;IAEhC,OAAO,wBACL,YAAoB,EACpB,QAAgB,EAChB,YAAiB,EACZ;QACL,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,cAAc,UAAU;QAC/D,IAAI,CAAC,eAAe,CAAC;QACrB,OAAO;IACT;IAEA,OAAe,gBAAgB,YAAoB,EAAE,QAAgB,EAAE,YAAiB,EAAO;QAC7F,MAAM,eAAe;QAErB,OAAO;YACL;YACA,OAAO;YACP,MAAM;YACN,UAAU;YACV,eAAe;YACf,WAAW;YACX,WAAW,CAAC,SAAS,EAAE,cAAc;YACrC,iBAAiB;gBAAE,MAAM;gBAAyB,UAAU;YAAgD;YAC5G,cAAc;gBAAE,MAAM;gBAAuB,OAAO;YAA0C;YAC9F,cAAc;gBAAE,MAAM;gBAAkB,OAAO;YAAoD;YACnG,gBAAgB;gBAAC;gBAA0B;gBAAmB;aAAuB;YACrF,iBAAiB;gBAAC;gBAA0B;gBAAyB;aAAsB;YAC3F,cAAc;gBAAC;gBAAmB;gBAAmB;aAAmB;QAC1E;IACF;IAEA,OAAe,gBAAgB,SAAc,EAAQ;QACnD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,SAAS;QAE7C,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE;YAChD,MAAM,gBAAgB,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,GAAG;YACjE,cAAc,OAAO,CAAC,CAAA,QAAS,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAC9D;IACF;AACF;AAGO,SAAS,wBACd,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,OAAY;IAEZ,MAAM,WAAW,6BAA6B,gBAAgB,CAAC,aAAa,WAAW,GAAG,IACxF,6BAA6B,gBAAgB,CAAC,SAAS;IAEzD,MAAM,mBAAmB,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG;IACvD,MAAM,cAAc;IAEpB,MAAM,iBAAiB,eAAe,gCACpC,CAAC,iBAAiB,EAAE,aAAa,EAAE,CAAC,GACpC,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC,GAC3B,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/D,CAAC,gBAAgB,EAAE,aAAa;IAElC,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA,4CAA4C;AAC5C,SAAS,yBAAyB,QAAgB;IAChD,MAAM,gBAAgB,SAAS,WAAW;IAE1C,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,YAAY;QAC7G,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;QAClH,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,mBAAmB,cAAc,QAAQ,CAAC,gBAAgB,cAAc,QAAQ,CAAC,iBAAiB;QAClI,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;QAC7E,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,UAAU;QACjH,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,SAAS,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,eAAe;QACnH,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,eAAe;QACtH,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,0BAA0B,QAAgB;IACjD,MAAM,gBAAgB,SAAS,WAAW;IAE1C,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,YAAY;QACxE,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,UAAU;QAC/E,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,iBAAiB;QACjD,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,UAAU;QAC1C,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,UAAU;QAC1C,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,OAAO;QACvC,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,QAAQ;QACxC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,yBAAyB,QAAgB,EAAE,YAAoB;IACtE,MAAM,gBAAgB,SAAS,WAAW;IAC1C,MAAM,gBAAgB,aAAa,WAAW;IAE9C,IAAI,cAAc,QAAQ,CAAC,UAAU;QACnC,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,SAAS;YAC1E,OAAO;QACT,OAAO,IAAI,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,YAAY;YAC9E,OAAO;QACT;QACA,OAAO;IACT,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY;QAC5C,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,SAAS;YAC1E,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,0BAA0B,QAAgB,EAAE,YAAoB;IACvE,MAAM,gBAAgB,SAAS,WAAW;IAC1C,MAAM,gBAAgB,aAAa,WAAW;IAE9C,IAAI,cAAc,QAAQ,CAAC,UAAU;QACnC,IAAI,cAAc,QAAQ,CAAC,iBAAiB,cAAc,QAAQ,CAAC,SAAS;YAC1E,OAAO,CAAC;;;6BAGe,CAAC;QAC1B;QACA,OAAO,CAAC,QAAQ,EAAE,aAAa;;;SAG1B,EAAE,aAAa,YAAY,CAAC;IACnC,OAAO,IAAI,cAAc,QAAQ,CAAC,YAAY;QAC5C,OAAO,CAAC,UAAU,EAAE,aAAa;;WAE1B,EAAE,aAAa;wBACF,CAAC;IACvB;IAEA,OAAO,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,aAAa;;;aAGlC,EAAE,aAAa,YAAY,CAAC;AACzC;AAGO,eAAe,gCACpB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,eAAoB,EACpB,QAAgB,EAChB,cAA0E,WAAW,EACrF,YAAkB,EAClB,oBAA0B;IAG1B,4DAA4D;IAC5D,MAAM,iBAAiB,MAAM,uBAC3B,cAAc,cAAc,UAAU,iBAAiB,UAAU,aAAa,cAAc;IAG9F,OAAO;QACL,SAAS,eAAe,OAAO;QAC/B,iBAAiB,eAAe,eAAe;QAC/C,cAAc,eAAe,YAAY;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 5973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-1.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview\r\n * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport { BrandProfile } from '@/lib/types';\r\nimport { revo10Config, revo10Prompts } from './models/versions/revo-1.0/config';\r\nimport { advancedContentGenerator, BusinessProfile } from './advanced-content-generator';\r\nimport { performanceAnalyzer } from './content-performance-analyzer';\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\nimport {\r\n  generateCreativeHeadline,\r\n  generateCreativeSubheadline,\r\n  enhanceDesignCreativity,\r\n  generateCreativeCTA,\r\n  analyzeBusinessContext,\r\n  AntiRepetitionSystem,\r\n  CREATIVE_PROMPT_SYSTEM,\r\n  CONTENT_VARIATION_ENGINE,\r\n  // NEW: Import business-specific content generation\r\n  StrategicContentPlanner,\r\n  generateBusinessSpecificHeadline,\r\n  generateBusinessSpecificSubheadline,\r\n  generateBusinessSpecificCaption\r\n} from './creative-enhancement';\r\n\r\n// Advanced features integration (simplified for now)\r\n// TODO: Import advanced features from Revo 1.5 when available\r\n\r\n// Helper functions for advanced design generation\r\nfunction getBusinessDesignDNA(businessType: string): string {\r\n  const designDNA: Record<string, string> = {\r\n    'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',\r\n    'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',\r\n    'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',\r\n    'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',\r\n    'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',\r\n    'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',\r\n    'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',\r\n    'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',\r\n    'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'\r\n  };\r\n\r\n  return designDNA[businessType.toLowerCase()] || designDNA['default'];\r\n}\r\n\r\n// NEW: 7 truly different design types for dynamic social media feeds\r\nfunction getHumanDesignVariations(seed: number): any {\r\n  const variations = [\r\n    {\r\n      style: 'Watercolor Quotes',\r\n      layout: 'Soft, artistic watercolor background with elegant typography overlay',\r\n      composition: 'Centered or asymmetrical text with flowing watercolor elements',\r\n      mood: 'Artistic, elegant, inspirational',\r\n      elements: 'Watercolor textures, elegant fonts, soft color transitions, artistic backgrounds',\r\n      description: 'Create a design that looks like an artist painted it with watercolors, with flowing, organic shapes and elegant typography that feels handcrafted and artistic.'\r\n    },\r\n    {\r\n      style: 'Split Photo Collages',\r\n      layout: 'Two or three photo sections with text overlay on one section',\r\n      composition: 'Grid-based photo layout with text integrated naturally',\r\n      mood: 'Modern, dynamic, photo-driven',\r\n      elements: 'Photo sections, clean grid lines, integrated text, modern typography',\r\n      description: 'Design with a clean grid layout that splits the image into photo sections, with text naturally integrated into one section. Think Instagram grid meets modern magazine layout.'\r\n    },\r\n    {\r\n      style: 'Meme-Style Posts',\r\n      layout: 'Bold, punchy text with minimal background and high contrast',\r\n      composition: 'Centered text with simple, impactful background',\r\n      mood: 'Fun, viral, shareable',\r\n      elements: 'Bold typography, simple backgrounds, high contrast, meme-like simplicity',\r\n      description: 'Create a design that feels like a viral meme - bold, simple text with minimal background elements. Think Twitter meme aesthetics but professional.'\r\n    },\r\n    {\r\n      style: 'Polaroid-Style Testimonials',\r\n      layout: 'Polaroid frame with photo area and handwritten-style text',\r\n      composition: 'Polaroid border with content inside, vintage feel',\r\n      mood: 'Authentic, personal, nostalgic',\r\n      elements: 'Polaroid borders, vintage textures, handwritten fonts, authentic feel',\r\n      description: 'Design that looks like a vintage Polaroid photo with a white border, containing either a photo area or text that feels handwritten and personal.'\r\n    },\r\n    {\r\n      style: 'Minimal Photo-Driven Promos',\r\n      layout: 'Large photo background with minimal text overlay',\r\n      composition: 'Photo as hero element with subtle text placement',\r\n      mood: 'Clean, premium, photo-focused',\r\n      elements: 'Large photos, minimal text, clean typography, lots of white space',\r\n      description: 'Create a design where a beautiful photo is the main focus, with minimal, elegant text overlay. Think high-end magazine or premium brand aesthetics.'\r\n    },\r\n    {\r\n      style: 'Mixed-Media Artistic Posts',\r\n      layout: 'Layered design with multiple textures, patterns, and artistic elements',\r\n      composition: 'Complex layering with artistic elements and modern typography',\r\n      mood: 'Creative, artistic, unique',\r\n      elements: 'Multiple textures, artistic patterns, layered elements, creative typography',\r\n      description: 'Design with multiple artistic layers - think digital art meets graphic design. Include textures, patterns, and creative elements that feel like modern digital art.'\r\n    },\r\n    {\r\n      style: 'Branded Posters (Current Style)',\r\n      layout: 'Illustration-heavy design with brand elements and structured layout',\r\n      composition: 'Illustrated background with organized text and brand placement',\r\n      mood: 'Professional, branded, consistent',\r\n      elements: 'Illustrations, brand colors, structured typography, consistent branding',\r\n      description: 'The current style - professional illustrated posters with brand consistency. Use when you need to maintain strong brand identity.'\r\n    }\r\n  ];\r\n\r\n  return variations[seed % variations.length];\r\n}\r\n\r\n// NEW: Simple, clean design instructions for better visual appeal\r\nfunction injectHumanImperfections(designPrompt: string, seed: number): string {\r\n  const instructions = [\r\n    'Use natural spacing and proportions that feel balanced and appealing',\r\n    'Create a design that feels modern and current, not overly perfect',\r\n    'Focus on visual appeal and what people actually like to see',\r\n    'Make it look like something from a successful, popular brand'\r\n  ];\r\n\r\n  const selectedInstruction = instructions[seed % instructions.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN FOCUS:\r\n${selectedInstruction}\r\n\r\nKeep the design simple, clean, and visually appealing.`;\r\n}\r\n\r\n// NEW: Simple creative approach for better designs\r\nfunction injectCreativeRebellion(designPrompt: string, seed: number): string {\r\n  const approaches = [\r\n    `DESIGN APPROACH: Create a design that's visually appealing and engaging. Focus on what looks good and what people want to engage with.`,\r\n\r\n    `CREATIVE STYLE: Use a clean, modern approach that feels current and appealing. Make it look like something people would actually want to interact with.`,\r\n\r\n    `VISUAL APPROACH: Design with a focus on visual appeal and engagement. Create something that stands out and looks good.`,\r\n\r\n    `DESIGN PHILOSOPHY: Focus on creating designs that people want to engage with - clean, modern, and visually appealing.`\r\n  ];\r\n\r\n  const selectedApproach = approaches[seed % approaches.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN APPROACH:\r\n${selectedApproach}\r\n\r\nFocus on creating designs that are visually appealing and engaging.`;\r\n}\r\n\r\n// NEW: Simple design guidelines for better results\r\nfunction addArtisticConstraints(designPrompt: string, seed: number): string {\r\n  const constraints = [\r\n    `DESIGN FOCUS: Create a design that's visually appealing and engaging. Focus on clean, modern aesthetics that people actually like.`,\r\n\r\n    `COMPOSITION APPROACH: Use simple, clean layouts that are easy to read and understand. Less is more.`,\r\n\r\n    `CREATIVE ELEMENTS: Add modern, contemporary elements that make the design look good and engaging.`,\r\n\r\n    `VISUAL BALANCE: Create a design that feels balanced and appealing, with elements that work together well.`,\r\n\r\n    `DESIGN STYLE: Use a clean, modern approach that feels current and professional. Focus on visual appeal.`,\r\n\r\n    `CREATIVE APPROACH: Design with a focus on what people actually want to see and engage with.`,\r\n\r\n    `VISUAL HIERARCHY: Create clear visual hierarchy that guides the eye naturally through the design.`,\r\n\r\n    `DESIGN PRINCIPLES: Focus on creating a design that's both beautiful and engaging. Make it look good.`\r\n  ];\r\n\r\n  const selectedConstraint = constraints[seed % constraints.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN GUIDELINE:\r\n${selectedConstraint}\r\n\r\nKeep the design simple, clean, and visually appealing.`;\r\n}\r\n\r\nfunction getPlatformOptimization(platform: string): string {\r\n  const optimizations: Record<string, string> = {\r\n    'instagram': `\r\n- Mobile-first design with bold, clear elements\r\n- High contrast colors that pop on small screens\r\n- Text minimum 24px equivalent for readability\r\n- Center important elements for square crop compatibility\r\n- Thumb-stopping power for fast scroll feeds\r\n- Logo: Bottom right corner or naturally integrated`,\r\n\r\n    'linkedin': `\r\n- Professional, business-appropriate aesthetics\r\n- Corporate design standards and clean look\r\n- Clear value proposition for business audience\r\n- Professional photography and imagery\r\n- Thought leadership positioning\r\n- Logo: Prominent placement for brand authority`,\r\n\r\n    'facebook': `\r\n- Desktop and mobile viewing optimization\r\n- Engagement and shareability focus\r\n- Clear value proposition in visual hierarchy\r\n- Authentic, relatable imagery\r\n- Community-focused design elements\r\n- Logo: Top left or bottom right corner`,\r\n\r\n    'twitter': `\r\n- Rapid consumption and high engagement design\r\n- Bold, contrasting colors for timeline visibility\r\n- Minimal, impactful text elements\r\n- Trending visual styles integration\r\n- Real-time relevance\r\n- Logo: Small, subtle placement`,\r\n\r\n    'default': `\r\n- Cross-platform compatibility\r\n- Universal appeal and accessibility\r\n- Balanced design for multiple contexts\r\n- Professional appearance across devices\r\n- Logo: Flexible placement based on composition`\r\n  };\r\n\r\n  return optimizations[platform.toLowerCase()] || optimizations['default'];\r\n}\r\n\r\n// Advanced real-time context gathering for Revo 1.0 (enhanced version)\r\nasync function gatherRealTimeContext(businessType: string, location: string, platform: string) {\r\n  const context: any = {\r\n    trends: [],\r\n    weather: null,\r\n    events: [],\r\n    news: [],\r\n    localLanguage: {},\r\n    climateInsights: {},\r\n    trendingTopics: [],\r\n    timeContext: {\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      month: new Date().toLocaleDateString('en-US', { month: 'long' }),\r\n      season: getSeason(),\r\n      timeOfDay: getTimeOfDay()\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Generate contextual trends based on business type and location\r\n    context.trends = generateContextualTrends(businessType, location);\r\n\r\n    // Generate weather-appropriate content suggestions\r\n    context.weather = generateWeatherContext(location);\r\n\r\n    // Generate local business opportunities\r\n    context.events = generateLocalOpportunities(businessType, location);\r\n\r\n    // NEW: Enhanced local language and cultural context\r\n    context.localLanguage = generateLocalLanguageContext(location);\r\n\r\n    // NEW: Advanced climate insights for business relevance\r\n    context.climateInsights = generateClimateInsights(location, businessType);\r\n\r\n    // NEW: Real-time trending topics (simulated for now, can be enhanced with actual APIs)\r\n    context.trendingTopics = generateTrendingTopics(businessType, location, platform);\r\n\r\n    // NEW: Local news and market insights\r\n    context.news = generateLocalNewsContext(businessType, location);\r\n\r\n    return context;\r\n\r\n  } catch (error) {\r\n    return context; // Return partial context\r\n  }\r\n}\r\n\r\n// Advanced design enhancement functions\r\nfunction shouldIncludePeopleInDesign(businessType: string, location: string, visualStyle: string): boolean {\r\n  const peopleBusinessTypes = [\r\n    'restaurant', 'fitness', 'healthcare', 'education', 'retail', 'hospitality',\r\n    'beauty', 'wellness', 'consulting', 'coaching', 'real estate', 'finance',\r\n    'technology', 'marketing', 'events', 'photography', 'fashion'\r\n  ];\r\n\r\n  return peopleBusinessTypes.some(type =>\r\n    businessType.toLowerCase().includes(type) ||\r\n    visualStyle === 'lifestyle' ||\r\n    visualStyle === 'authentic'\r\n  );\r\n}\r\n\r\nfunction getLocalCulturalContext(location: string): string {\r\n  const culturalContexts: Record<string, string> = {\r\n    'kenya': 'Subtle Kenyan elements: warm earth tones, natural textures, community feel',\r\n    'nigeria': 'Subtle Nigerian elements: vibrant accents, natural patterns, community warmth',\r\n    'south africa': 'Subtle South African elements: diverse representation, natural colors, community spirit',\r\n    'ghana': 'Subtle Ghanaian elements: warm tones, natural textures, community connection',\r\n    'uganda': 'Subtle Ugandan elements: natural colors, community feel, authentic representation',\r\n    'tanzania': 'Subtle Tanzanian elements: coastal influences, natural textures, community warmth',\r\n    'ethiopia': 'Subtle Ethiopian elements: natural earth tones, community connection, authentic feel',\r\n    'rwanda': 'Subtle Rwandan elements: natural colors, community spirit, authentic representation',\r\n    'default': 'Natural, authentic feel with subtle local elements that feel genuine, not forced'\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  for (const [key, context] of Object.entries(culturalContexts)) {\r\n    if (locationKey.includes(key)) {\r\n      return context;\r\n    }\r\n  }\r\n  return culturalContexts['default'];\r\n}\r\n\r\nfunction getDesignVariations(seed: number) {\r\n  const variations = [\r\n    {\r\n      style: 'Modern Minimalist',\r\n      layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',\r\n      composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',\r\n      mood: 'Professional, clean, sophisticated',\r\n      elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'\r\n    },\r\n    {\r\n      style: 'Dynamic Action',\r\n      layout: 'Diagonal composition with movement, multiple focal points, energetic flow',\r\n      composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',\r\n      mood: 'Energetic, exciting, forward-moving',\r\n      elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'\r\n    },\r\n    {\r\n      style: 'Lifestyle Authentic',\r\n      layout: 'Natural, candid composition with real-world settings, human-centered design',\r\n      composition: 'Environmental context, natural lighting, authentic moments captured',\r\n      mood: 'Warm, relatable, trustworthy, human',\r\n      elements: 'Natural lighting, authentic people, real environments, warm color tones'\r\n    },\r\n    {\r\n      style: 'Corporate Professional',\r\n      layout: 'Structured grid layout, balanced composition, formal presentation',\r\n      composition: 'Symmetrical balance, clear hierarchy, professional spacing',\r\n      mood: 'Trustworthy, established, reliable, premium',\r\n      elements: 'Corporate colors, professional imagery, clean typography, structured layout'\r\n    },\r\n    {\r\n      style: 'Creative Artistic',\r\n      layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',\r\n      composition: 'Creative angles, artistic overlays, unique visual treatments',\r\n      mood: 'Creative, innovative, unique, inspiring',\r\n      elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'\r\n    },\r\n    {\r\n      style: 'Tech Innovation',\r\n      layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',\r\n      composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',\r\n      mood: 'Innovative, cutting-edge, digital, forward-thinking',\r\n      elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'\r\n    },\r\n    {\r\n      style: 'Cultural Heritage',\r\n      layout: 'Traditional patterns mixed with modern design, cultural elements integrated',\r\n      composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',\r\n      mood: 'Cultural, authentic, heritage-proud, modern-traditional',\r\n      elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'\r\n    },\r\n    {\r\n      style: 'Luxury Premium',\r\n      layout: 'Elegant, sophisticated layout with premium materials and finishes',\r\n      composition: 'Luxurious spacing, premium typography, elegant proportions',\r\n      mood: 'Luxurious, premium, exclusive, sophisticated',\r\n      elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'\r\n    }\r\n  ];\r\n\r\n  return variations[seed % variations.length];\r\n}\r\n\r\nfunction getAdvancedPeopleInstructions(businessType: string, location: string): string {\r\n  const culturalContext = getLocalCulturalContext(location);\r\n\r\n  return `\r\n**ADVANCED PEOPLE INTEGRATION:**\r\n- Include diverse, authentic people with PERFECT FACIAL FEATURES\r\n- Complete faces, symmetrical features, natural expressions, professional poses\r\n- Faces fully visible, well-lit, anatomically correct with no deformations\r\n- Cultural Context: ${culturalContext}\r\n- Show people in varied, engaging settings:\r\n  * Professional environments (modern offices, studios, workshops)\r\n  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)\r\n  * Industry-specific contexts (${businessType} environments)\r\n  * Cultural celebrations and modern community gatherings\r\n  * Urban settings (co-working spaces, tech hubs, modern city life)\r\n  * Traditional meets modern (cultural heritage with contemporary life)\r\n- Ensure representation reflects local demographics and cultural values\r\n- Show real people in natural, engaging situations that vary by design\r\n- People should be actively engaged with the business/service context\r\n- Use authentic expressions of joy, confidence, success, and community\r\n- Include intergenerational representation when appropriate\r\n- Show modern African/local fashion and styling\r\n- Ensure people are central to the story, not just decorative elements`;\r\n}\r\n\r\n// NEW: Industry Intelligence System with World-Class Design Benchmarks\r\nfunction getIndustryDesignIntelligence(businessType: string): any {\r\n  const industryIntelligence: Record<string, any> = {\r\n    'restaurant': {\r\n      name: 'Restaurant & Food Service',\r\n      worldClassBrands: ['Noma', 'Eleven Madison Park', 'The French Laundry', 'Osteria Francescana', 'Gaggan'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Sophisticated, appetizing, experiential',\r\n        colorPalettes: ['Warm earth tones', 'Rich burgundies', 'Cream whites', 'Deep greens', 'Gold accents'],\r\n        typography: 'Elegant serifs, sophisticated sans-serifs, handwritten touches',\r\n        imagery: 'Food photography, intimate dining scenes, chef portraits, ingredient close-ups',\r\n        layout: 'Clean, spacious, food-focused, premium feel',\r\n        creativeElements: ['Food textures', 'Culinary tools', 'Seasonal ingredients', 'Dining atmosphere', 'Chef artistry']\r\n      },\r\n      creativityFrameworks: [\r\n        'Culinary storytelling through visual narrative',\r\n        'Seasonal and ingredient-driven design evolution',\r\n        'Chef personality and restaurant atmosphere integration',\r\n        'Food photography as art form',\r\n        'Dining experience visualization'\r\n      ],\r\n      industryTrends: ['Farm-to-table aesthetics', 'Minimalist plating influence', 'Chef celebrity culture', 'Sustainable dining', 'Global fusion']\r\n    },\r\n\r\n    'technology': {\r\n      name: 'Technology & Innovation',\r\n      worldClassBrands: ['Apple', 'Tesla', 'SpaceX', 'Google', 'Microsoft', 'Adobe'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Futuristic, clean, innovative, premium',\r\n        colorPalettes: ['Deep blues', 'Pure whites', 'Accent colors', 'Gradients', 'Neon highlights'],\r\n        typography: 'Modern sans-serifs, geometric precision, clean hierarchy',\r\n        imagery: 'Abstract tech elements, clean interfaces, innovation concepts, premium materials',\r\n        layout: 'Grid-based, clean lines, lots of white space, focused messaging',\r\n        creativeElements: ['Geometric shapes', 'Digital interfaces', 'Innovation metaphors', 'Premium materials', 'Future concepts']\r\n      },\r\n      creativityFrameworks: [\r\n        'Technology as art and innovation',\r\n        'Clean, premium aesthetic with bold innovation',\r\n        'Future-focused visual storytelling',\r\n        'Interface and product integration',\r\n        'Innovation and progress visualization'\r\n      ],\r\n      industryTrends: ['AI integration', 'Sustainable tech', 'Minimalist interfaces', 'Premium positioning', 'Innovation focus']\r\n    },\r\n\r\n    'healthcare': {\r\n      name: 'Healthcare & Wellness',\r\n      worldClassBrands: ['Mayo Clinic', 'Cleveland Clinic', 'Johns Hopkins', 'Stanford Health', 'Cleveland Clinic'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Trustworthy, caring, professional, accessible',\r\n        colorPalettes: ['Calming blues', 'Soft greens', 'Warm whites', 'Accent colors', 'Professional tones'],\r\n        typography: 'Clean, readable fonts, professional hierarchy, accessible sizing',\r\n        imagery: 'Caring professionals, modern facilities, wellness concepts, community health',\r\n        layout: 'Clean, organized, easy to navigate, trustworthy appearance',\r\n        creativeElements: ['Medical symbols', 'Wellness imagery', 'Community health', 'Professional care', 'Modern facilities']\r\n      },\r\n      creativityFrameworks: [\r\n        'Care and compassion through visual design',\r\n        'Trust and professionalism building',\r\n        'Wellness and health promotion',\r\n        'Community health engagement',\r\n        'Modern healthcare accessibility'\r\n      ],\r\n      industryTrends: ['Telehealth integration', 'Patient-centered care', 'Digital health', 'Wellness focus', 'Community health']\r\n    },\r\n\r\n    'fitness': {\r\n      name: 'Fitness & Wellness',\r\n      worldClassBrands: ['Peloton', 'Nike', 'Adidas', 'Equinox', 'Planet Fitness', 'CrossFit'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Energetic, motivational, premium, inclusive',\r\n        colorPalettes: ['Bold reds', 'Energetic oranges', 'Motivational yellows', 'Strong blacks', 'Accent colors'],\r\n        typography: 'Bold, energetic fonts, motivational messaging, strong hierarchy',\r\n        imagery: 'Action shots, diverse athletes, motivational scenes, fitness environments',\r\n        layout: 'Dynamic, energetic, motivational, inclusive',\r\n        creativeElements: ['Movement lines', 'Athletic energy', 'Diversity representation', 'Motivational elements', 'Fitness environments']\r\n      },\r\n      creativityFrameworks: [\r\n        'Energy and motivation through visual design',\r\n        'Inclusive fitness for all',\r\n        'Athletic achievement celebration',\r\n        'Community and belonging',\r\n        'Personal transformation stories'\r\n      ],\r\n      industryTrends: ['Digital fitness', 'Inclusive representation', 'Community building', 'Personal transformation', 'Wellness integration']\r\n    },\r\n\r\n    'finance': {\r\n      name: 'Finance & Banking',\r\n      worldClassBrands: ['Goldman Sachs', 'JP Morgan', 'Morgan Stanley', 'BlackRock', 'Visa', 'Mastercard'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Trustworthy, sophisticated, stable, premium',\r\n        colorPalettes: ['Deep blues', 'Professional grays', 'Gold accents', 'Clean whites', 'Trustworthy tones'],\r\n        typography: 'Professional serifs, clean sans-serifs, authoritative hierarchy',\r\n        imagery: 'Modern buildings, professional environments, growth concepts, stability symbols',\r\n        layout: 'Structured, professional, trustworthy, premium',\r\n        creativeElements: ['Financial symbols', 'Growth metaphors', 'Stability elements', 'Professional environments', 'Premium materials']\r\n      },\r\n      creativityFrameworks: [\r\n        'Trust and stability through design',\r\n        'Sophistication and premium positioning',\r\n        'Growth and progress visualization',\r\n        'Professional excellence',\r\n        'Financial security representation'\r\n      ],\r\n      industryTrends: ['Digital banking', 'Fintech innovation', 'Sustainable finance', 'Personal finance', 'Cryptocurrency']\r\n    },\r\n\r\n    'education': {\r\n      name: 'Education & Learning',\r\n      worldClassBrands: ['Harvard', 'MIT', 'Stanford', 'Coursera', 'Khan Academy', 'Duolingo'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Inspiring, accessible, modern, engaging',\r\n        colorPalettes: ['Inspiring blues', 'Creative purples', 'Warm oranges', 'Growth greens', 'Accent colors'],\r\n        typography: 'Readable fonts, inspiring hierarchy, accessible design',\r\n        imagery: 'Learning environments, diverse students, innovation concepts, growth metaphors',\r\n        layout: 'Engaging, organized, inspiring, accessible',\r\n        creativeElements: ['Learning symbols', 'Growth metaphors', 'Innovation elements', 'Diversity representation', 'Knowledge visualization']\r\n      },\r\n      creativityFrameworks: [\r\n        'Inspiration and learning through design',\r\n        'Accessibility and inclusion',\r\n        'Innovation and progress',\r\n        'Community and collaboration',\r\n        'Personal growth stories'\r\n      ],\r\n      industryTrends: ['Online learning', 'Personalized education', 'STEM focus', 'Global accessibility', 'Innovation in learning']\r\n    },\r\n\r\n    'retail': {\r\n      name: 'Retail & E-commerce',\r\n      worldClassBrands: ['Amazon', 'Apple', 'Nike', 'IKEA', 'Zara', 'Uniqlo'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Attractive, commercial, engaging, conversion-focused',\r\n        colorPalettes: ['Brand colors', 'Attractive accents', 'Commercial tones', 'Engaging highlights'],\r\n        typography: 'Commercial fonts, conversion-focused messaging, attractive hierarchy',\r\n        imagery: 'Product showcases, lifestyle integration, commercial appeal, brand personality',\r\n        layout: 'Commercial, attractive, conversion-optimized, engaging',\r\n        creativeElements: ['Product elements', 'Lifestyle integration', 'Commercial appeal', 'Brand personality', 'Conversion elements']\r\n      },\r\n      creativityFrameworks: [\r\n        'Commercial appeal and conversion',\r\n        'Brand personality expression',\r\n        'Lifestyle integration',\r\n        'Product storytelling',\r\n        'Customer engagement'\r\n      ],\r\n      industryTrends: ['E-commerce growth', 'Personalization', 'Sustainability', 'Mobile commerce', 'Social commerce']\r\n    },\r\n\r\n    'real estate': {\r\n      name: 'Real Estate & Property',\r\n      worldClassBrands: ['Sotheby\\'s', 'Christie\\'s', 'Douglas Elliman', 'Compass', 'Zillow'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Luxurious, aspirational, trustworthy, premium',\r\n        colorPalettes: ['Luxury golds', 'Sophisticated grays', 'Premium whites', 'Rich browns', 'Accent colors'],\r\n        typography: 'Luxury fonts, sophisticated hierarchy, premium appearance',\r\n        imagery: 'Luxury properties, premium environments, aspirational lifestyles, professional service',\r\n        layout: 'Luxurious, sophisticated, premium, aspirational',\r\n        creativeElements: ['Luxury elements', 'Premium materials', 'Aspirational lifestyles', 'Professional service', 'Property showcase']\r\n      },\r\n      creativityFrameworks: [\r\n        'Luxury and aspiration through design',\r\n        'Trust and professionalism',\r\n        'Premium positioning',\r\n        'Lifestyle visualization',\r\n        'Property storytelling'\r\n      ],\r\n      industryTrends: ['Digital property viewing', 'Sustainable properties', 'Luxury market growth', 'Technology integration', 'Global investment']\r\n    },\r\n\r\n    'default': {\r\n      name: 'Professional Services',\r\n      worldClassBrands: ['McKinsey', 'Bain', 'BCG', 'Deloitte', 'PwC', 'EY'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Professional, trustworthy, modern, sophisticated',\r\n        colorPalettes: ['Professional blues', 'Trustworthy grays', 'Modern accents', 'Clean whites'],\r\n        typography: 'Professional fonts, clean hierarchy, trustworthy appearance',\r\n        imagery: 'Professional environments, modern offices, business concepts, trust symbols',\r\n        layout: 'Professional, organized, trustworthy, modern',\r\n        creativeElements: ['Professional elements', 'Business concepts', 'Trust symbols', 'Modern environments', 'Success indicators']\r\n      },\r\n      creativityFrameworks: [\r\n        'Professional excellence through design',\r\n        'Trust and credibility building',\r\n        'Modern sophistication',\r\n        'Business success visualization',\r\n        'Professional service representation'\r\n      ],\r\n      industryTrends: ['Digital transformation', 'Remote work', 'Sustainability', 'Innovation focus', 'Global expansion']\r\n    }\r\n  };\r\n\r\n  return industryIntelligence[businessType.toLowerCase()] || industryIntelligence['default'];\r\n}\r\n\r\n// NEW: Enhanced Creativity System with Industry Intelligence\r\nfunction getEnhancedCreativityFramework(businessType: string, designStyle: string, seed: number): any {\r\n  const industryIntel = getIndustryDesignIntelligence(businessType);\r\n\r\n  const creativityFrameworks = [\r\n    {\r\n      name: 'World-Class Benchmarking',\r\n      approach: `Study and emulate the design excellence of ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}`,\r\n      focus: 'Premium positioning, industry best practices, sophisticated aesthetics',\r\n      elements: industryIntel.designBenchmarks.creativeElements,\r\n      description: `Create designs that rival the sophistication and quality of ${industryIntel.name} industry leaders`\r\n    },\r\n    {\r\n      name: 'Industry Trend Integration',\r\n      approach: `Incorporate current ${industryIntel.name} trends: ${industryIntel.industryTrends.slice(0, 3).join(', ')}`,\r\n      focus: 'Modern relevance, industry innovation, forward-thinking design',\r\n      elements: ['Trend elements', 'Innovation concepts', 'Modern aesthetics', 'Industry relevance'],\r\n      description: 'Design that feels current and relevant to the industry while maintaining creativity'\r\n    },\r\n    {\r\n      name: 'Creative Storytelling',\r\n      approach: industryIntel.creativityFrameworks[seed % industryIntel.creativityFrameworks.length],\r\n      focus: 'Narrative design, emotional connection, brand storytelling',\r\n      elements: ['Story elements', 'Emotional triggers', 'Narrative flow', 'Brand personality'],\r\n      description: 'Use visual design to tell compelling stories that connect with the audience'\r\n    },\r\n    {\r\n      name: 'Innovation & Disruption',\r\n      approach: 'Challenge industry conventions with creative innovation',\r\n      focus: 'Breaking norms, creative disruption, unique positioning',\r\n      elements: ['Innovation elements', 'Disruptive concepts', 'Unique approaches', 'Creative risk-taking'],\r\n      description: 'Create designs that stand out by challenging industry conventions'\r\n    },\r\n    {\r\n      name: 'Cultural & Global Fusion',\r\n      approach: 'Blend local cultural elements with global industry standards',\r\n      focus: 'Cultural authenticity, global relevance, unique positioning',\r\n      elements: ['Cultural elements', 'Global standards', 'Local authenticity', 'Fusion concepts'],\r\n      description: 'Create designs that feel both locally authentic and globally competitive'\r\n    }\r\n  ];\r\n\r\n  return creativityFrameworks[seed % creativityFrameworks.length];\r\n}\r\n\r\n// NEW: Industry-Specific Design Enhancement\r\nfunction enhanceDesignWithIndustryIntelligence(designPrompt: string, businessType: string, designStyle: string, seed: number): string {\r\n  const industryIntel = getIndustryDesignIntelligence(businessType);\r\n  const creativityFramework = getEnhancedCreativityFramework(businessType, designStyle, seed);\r\n\r\n  const industryEnhancement = `\r\n🏭 INDUSTRY INTELLIGENCE INTEGRATION:\r\n**Industry:** ${industryIntel.name}\r\n**World-Class Benchmarks:** ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}\r\n**Industry Visual Style:** ${industryIntel.designBenchmarks.visualStyle}\r\n**Industry Color Palettes:** ${industryIntel.designBenchmarks.colorPalettes.slice(0, 3).join(', ')}\r\n**Industry Typography:** ${industryIntel.designBenchmarks.typography}\r\n**Industry Imagery:** ${industryIntel.designBenchmarks.imagery}\r\n**Industry Layout:** ${industryIntel.designBenchmarks.layout}\r\n\r\n🎨 CREATIVITY FRAMEWORK: ${creativityFramework.name}\r\n**Approach:** ${creativityFramework.approach}\r\n**Focus:** ${creativityFramework.focus}\r\n**Creative Elements:** ${creativityFramework.elements.slice(0, 3).join(', ')}\r\n**Description:** ${creativityFramework.description}\r\n\r\n🚀 INDUSTRY TRENDS TO INCORPORATE:\r\n${industryIntel.industryTrends.slice(0, 3).map((trend, i) => `${i + 1}. ${trend}`).join('\\n')}\r\n\r\n🎯 DESIGN REQUIREMENTS:\r\n- **Industry Benchmarking:** Create designs that rival ${industryIntel.name} industry leaders\r\n- **Trend Integration:** Incorporate current industry trends naturally\r\n- **Creative Innovation:** Use ${creativityFramework.name} approach for unique positioning\r\n- **Quality Standards:** Match world-class design quality and sophistication\r\n- **Industry Relevance:** Ensure design feels authentic to ${industryIntel.name} industry`;\r\n\r\n  return designPrompt + industryEnhancement;\r\n}\r\n\r\n// NEW: Business Intelligence Engine - Local Marketing Expert System\r\nfunction getBusinessIntelligenceEngine(businessType: string, location: string): any {\r\n  const businessIntelligence: Record<string, any> = {\r\n    'restaurant': {\r\n      name: 'Restaurant & Food Service',\r\n      localExpertise: {\r\n        experience: '25+ years in hospitality and culinary marketing',\r\n        marketDynamics: [\r\n          'Seasonal menu optimization and local ingredient sourcing',\r\n          'Customer loyalty programs and repeat business strategies',\r\n          'Local competition analysis and unique positioning',\r\n          'Food trends and cultural preferences in the area',\r\n          'Pricing strategies for local market conditions'\r\n        ],\r\n        localPhrases: [\r\n          'Taste of [location]',\r\n          'Where locals eat',\r\n          'Fresh from our kitchen',\r\n          'Made with love',\r\n          'Family recipe',\r\n          'Local favorite',\r\n          'Chef\\'s special',\r\n          'Daily fresh',\r\n          'Home-cooked taste',\r\n          'Local ingredients'\r\n        ],\r\n        contentStrategies: [\r\n          'Behind-the-scenes kitchen stories',\r\n          'Chef personality and cooking philosophy',\r\n          'Local ingredient sourcing stories',\r\n          'Customer testimonials and success stories',\r\n          'Seasonal menu highlights',\r\n          'Local food culture integration',\r\n          'Community involvement and events',\r\n          'Sustainability and local farming partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Food memories and nostalgia',\r\n          'Local pride and community connection',\r\n          'Health and wellness benefits',\r\n          'Family traditions and gatherings',\r\n          'Adventure and trying new flavors',\r\n          'Social sharing and food photography',\r\n          'Exclusive offers and VIP experiences',\r\n          'Local events and celebrations'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'technology': {\r\n      name: 'Technology & Innovation',\r\n      localExpertise: {\r\n        experience: '22+ years in tech marketing and digital transformation',\r\n        marketDynamics: [\r\n          'Local tech ecosystem and startup culture',\r\n          'Digital adoption rates in the region',\r\n          'Competitive landscape and innovation gaps',\r\n          'Local talent pool and skill development',\r\n          'Government tech initiatives and support'\r\n        ],\r\n        localPhrases: [\r\n          'Innovation hub',\r\n          'Digital transformation',\r\n          'Tech-forward solutions',\r\n          'Future-ready',\r\n          'Smart [location]',\r\n          'Digital innovation',\r\n          'Tech excellence',\r\n          'Innovation center',\r\n          'Digital leadership',\r\n          'Tech ecosystem'\r\n        ],\r\n        contentStrategies: [\r\n          'Local tech success stories',\r\n          'Innovation case studies',\r\n          'Digital transformation journeys',\r\n          'Tech talent development',\r\n          'Local startup ecosystem',\r\n          'Government tech partnerships',\r\n          'Digital skills training',\r\n          'Smart city initiatives'\r\n        ],\r\n        engagementHooks: [\r\n          'Career advancement and skill development',\r\n          'Innovation and future thinking',\r\n          'Local tech community building',\r\n          'Digital transformation success',\r\n          'Tech entrepreneurship',\r\n          'Smart city development',\r\n          'Digital inclusion',\r\n          'Tech for social good'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'healthcare': {\r\n      name: 'Healthcare & Wellness',\r\n      localExpertise: {\r\n        experience: '28+ years in healthcare marketing and patient care',\r\n        marketDynamics: [\r\n          'Local health demographics and needs',\r\n          'Healthcare accessibility and insurance coverage',\r\n          'Competing healthcare providers and services',\r\n          'Local health trends and concerns',\r\n          'Community health initiatives and partnerships'\r\n        ],\r\n        localPhrases: [\r\n          'Your health, our priority',\r\n          'Caring for [location] families',\r\n          'Local healthcare excellence',\r\n          'Community health partner',\r\n          'Your wellness journey',\r\n          'Health close to home',\r\n          'Caring professionals',\r\n          'Local health experts',\r\n          'Community wellness',\r\n          'Health for everyone'\r\n        ],\r\n        contentStrategies: [\r\n          'Patient success stories and testimonials',\r\n          'Local health education and prevention',\r\n          'Community health initiatives',\r\n          'Healthcare professional spotlights',\r\n          'Local health trends and insights',\r\n          'Wellness tips and advice',\r\n          'Health technology integration',\r\n          'Community partnerships and events'\r\n        ],\r\n        engagementHooks: [\r\n          'Family health and wellness',\r\n          'Preventive care and early detection',\r\n          'Local health community',\r\n          'Professional healthcare expertise',\r\n          'Health technology innovation',\r\n          'Community health improvement',\r\n          'Patient-centered care',\r\n          'Health education and awareness'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'fitness': {\r\n      name: 'Fitness & Wellness',\r\n      localExpertise: {\r\n        experience: '24+ years in fitness marketing and community building',\r\n        marketDynamics: [\r\n          'Local fitness culture and preferences',\r\n          'Competing gyms and fitness options',\r\n          'Seasonal fitness trends and activities',\r\n          'Local sports teams and community events',\r\n          'Health awareness and wellness trends'\r\n        ],\r\n        localPhrases: [\r\n          'Your fitness journey starts here',\r\n          'Stronger [location] community',\r\n          'Local fitness excellence',\r\n          'Your wellness partner',\r\n          'Fitness for everyone',\r\n          'Local strength',\r\n          'Community fitness',\r\n          'Your health transformation',\r\n          'Local fitness family',\r\n          'Wellness close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Member transformation stories',\r\n          'Local fitness challenges and events',\r\n          'Community fitness initiatives',\r\n          'Trainer spotlights and expertise',\r\n          'Local sports team partnerships',\r\n          'Seasonal fitness programs',\r\n          'Wellness education and tips',\r\n          'Community health partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Personal transformation and goals',\r\n          'Community fitness challenges',\r\n          'Local sports pride',\r\n          'Health and wellness education',\r\n          'Fitness community building',\r\n          'Seasonal fitness motivation',\r\n          'Professional training expertise',\r\n          'Inclusive fitness for all'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'finance': {\r\n      name: 'Finance & Banking',\r\n      localExpertise: {\r\n        experience: '26+ years in financial services and local banking',\r\n        marketDynamics: [\r\n          'Local economic conditions and growth',\r\n          'Competing financial institutions',\r\n          'Local business financing needs',\r\n          'Personal finance trends in the area',\r\n          'Community investment opportunities'\r\n        ],\r\n        localPhrases: [\r\n          'Your financial partner in [location]',\r\n          'Local financial expertise',\r\n          'Community banking excellence',\r\n          'Your financial future',\r\n          'Local financial solutions',\r\n          'Community financial partner',\r\n          'Your money, our care',\r\n          'Local financial guidance',\r\n          'Community wealth building',\r\n          'Financial security close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local business success stories',\r\n          'Financial education and literacy',\r\n          'Community investment initiatives',\r\n          'Local economic insights',\r\n          'Personal finance success stories',\r\n          'Business financing solutions',\r\n          'Local financial trends',\r\n          'Community financial partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Financial security and planning',\r\n          'Local business growth',\r\n          'Community economic development',\r\n          'Personal finance education',\r\n          'Investment opportunities',\r\n          'Business financing solutions',\r\n          'Local economic pride',\r\n          'Financial wellness for families'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'education': {\r\n      name: 'Education & Learning',\r\n      localExpertise: {\r\n        experience: '23+ years in educational marketing and community learning',\r\n        marketDynamics: [\r\n          'Local education standards and performance',\r\n          'Competing educational institutions',\r\n          'Local learning needs and preferences',\r\n          'Community education initiatives',\r\n          'Employment and skill development needs'\r\n        ],\r\n        localPhrases: [\r\n          'Learning excellence in [location]',\r\n          'Your educational journey',\r\n          'Local learning excellence',\r\n          'Community education partner',\r\n          'Your learning success',\r\n          'Local educational leadership',\r\n          'Community learning center',\r\n          'Your knowledge partner',\r\n          'Local educational excellence',\r\n          'Learning close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Student success stories',\r\n          'Local educational achievements',\r\n          'Community learning initiatives',\r\n          'Educational innovation and technology',\r\n          'Local employment partnerships',\r\n          'Skill development programs',\r\n          'Community education events',\r\n          'Local learning trends'\r\n        ],\r\n        engagementHooks: [\r\n          'Personal growth and development',\r\n          'Career advancement opportunities',\r\n          'Local educational pride',\r\n          'Community learning initiatives',\r\n          'Innovation in education',\r\n          'Skill development and training',\r\n          'Local employment success',\r\n          'Educational excellence recognition'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'retail': {\r\n      name: 'Retail & E-commerce',\r\n      localExpertise: {\r\n        experience: '25+ years in retail marketing and customer experience',\r\n        marketDynamics: [\r\n          'Local shopping preferences and trends',\r\n          'Competing retail options and malls',\r\n          'Local economic conditions and spending',\r\n          'Seasonal shopping patterns',\r\n          'Community shopping habits and events'\r\n        ],\r\n        localPhrases: [\r\n          'Your local shopping destination',\r\n          'Shopping excellence in [location]',\r\n          'Local retail leadership',\r\n          'Your shopping partner',\r\n          'Local retail excellence',\r\n          'Community shopping center',\r\n          'Your retail destination',\r\n          'Local shopping experience',\r\n          'Community retail partner',\r\n          'Shopping close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local product highlights',\r\n          'Customer success stories',\r\n          'Community shopping events',\r\n          'Local brand partnerships',\r\n          'Seasonal shopping guides',\r\n          'Local shopping trends',\r\n          'Community retail initiatives',\r\n          'Local customer appreciation'\r\n        ],\r\n        engagementHooks: [\r\n          'Local product discovery',\r\n          'Community shopping events',\r\n          'Seasonal shopping excitement',\r\n          'Local brand support',\r\n          'Customer appreciation',\r\n          'Shopping convenience',\r\n          'Local retail pride',\r\n          'Community shopping experience'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'real estate': {\r\n      name: 'Real Estate & Property',\r\n      localExpertise: {\r\n        experience: '27+ years in real estate marketing and local property',\r\n        marketDynamics: [\r\n          'Local property market conditions',\r\n          'Competing real estate agencies',\r\n          'Local property trends and values',\r\n          'Community development and growth',\r\n          'Local investment opportunities'\r\n        ],\r\n        localPhrases: [\r\n          'Your local real estate expert',\r\n          'Real estate excellence in [location]',\r\n          'Local property specialist',\r\n          'Your property partner',\r\n          'Local real estate leadership',\r\n          'Community property expert',\r\n          'Your real estate guide',\r\n          'Local property excellence',\r\n          'Community real estate partner',\r\n          'Property close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local property success stories',\r\n          'Community development updates',\r\n          'Local property market insights',\r\n          'Property investment opportunities',\r\n          'Local neighborhood highlights',\r\n          'Community real estate events',\r\n          'Local property trends',\r\n          'Community property partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Property investment opportunities',\r\n          'Local neighborhood pride',\r\n          'Community development',\r\n          'Property market insights',\r\n          'Local real estate success',\r\n          'Community property events',\r\n          'Property investment guidance',\r\n          'Local real estate expertise'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'default': {\r\n      name: 'Professional Services',\r\n      localExpertise: {\r\n        experience: '20+ years in professional services and local business',\r\n        marketDynamics: [\r\n          'Local business environment and competition',\r\n          'Community business needs and trends',\r\n          'Local economic conditions',\r\n          'Business development opportunities',\r\n          'Community partnerships and networking'\r\n        ],\r\n        localPhrases: [\r\n          'Your local business partner',\r\n          'Professional excellence in [location]',\r\n          'Local business expertise',\r\n          'Your success partner',\r\n          'Local professional leadership',\r\n          'Community business partner',\r\n          'Your business guide',\r\n          'Local professional excellence',\r\n          'Community business expert',\r\n          'Success close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local business success stories',\r\n          'Community business initiatives',\r\n          'Local business insights',\r\n          'Business development opportunities',\r\n          'Local business trends',\r\n          'Community business events',\r\n          'Local business partnerships',\r\n          'Community business support'\r\n        ],\r\n        engagementHooks: [\r\n          'Business growth and success',\r\n          'Local business community',\r\n          'Professional development',\r\n          'Business opportunities',\r\n          'Local business pride',\r\n          'Community business support',\r\n          'Business innovation',\r\n          'Local business expertise'\r\n        ]\r\n      }\r\n    },\r\n    'financial technology software': {\r\n      name: 'Financial Technology Software',\r\n      localExpertise: {\r\n        experience: '15+ years in fintech and digital payments',\r\n        marketDynamics: [\r\n          'Digital payment adoption rates in the region',\r\n          'Mobile banking and fintech competition',\r\n          'Financial inclusion and accessibility needs',\r\n          'Regulatory compliance and security requirements',\r\n          'Local banking partnerships and integrations'\r\n        ],\r\n        contentStrategies: [\r\n          'Digital financial innovation',\r\n          'Payment security and trust',\r\n          'Financial inclusion stories',\r\n          'Fintech industry insights',\r\n          'User experience excellence',\r\n          'Local market expansion',\r\n          'Partnership announcements',\r\n          'Technology advancement'\r\n        ],\r\n        engagementHooks: [\r\n          'Financial innovation',\r\n          'Digital payments',\r\n          'Financial inclusion',\r\n          'Secure transactions',\r\n          'Fintech solutions',\r\n          'Payment convenience',\r\n          'Financial empowerment',\r\n          'Digital banking'\r\n        ]\r\n      },\r\n      localPhrases: [\r\n        'Your digital payment partner',\r\n        'Fintech innovation in [location]',\r\n        'Digital financial solutions',\r\n        'Your payment solution',\r\n        'Financial technology excellence',\r\n        'Digital banking for [location]',\r\n        'Your fintech partner',\r\n        'Payment innovation'\r\n      ]\r\n    },\r\n    'default': {\r\n      name: 'Professional Services',\r\n      localExpertise: {\r\n        experience: '20+ years in professional services',\r\n        marketDynamics: [\r\n          'Local business environment and competition',\r\n          'Market trends and opportunities',\r\n          'Customer needs and preferences',\r\n          'Industry best practices and standards',\r\n          'Local economic conditions and growth'\r\n        ],\r\n        contentStrategies: [\r\n          'Professional excellence and expertise',\r\n          'Client success stories',\r\n          'Industry insights and trends',\r\n          'Local market knowledge',\r\n          'Service quality and reliability',\r\n          'Innovation and solutions',\r\n          'Community involvement',\r\n          'Professional development'\r\n        ],\r\n        engagementHooks: [\r\n          'Professional excellence',\r\n          'Client success',\r\n          'Industry expertise',\r\n          'Local market knowledge',\r\n          'Quality service',\r\n          'Innovation solutions',\r\n          'Community partnership',\r\n          'Professional growth'\r\n        ]\r\n      },\r\n      localPhrases: [\r\n        'Your local professional partner',\r\n        'Excellence in [location]',\r\n        'Local expertise you can trust',\r\n        'Your success partner',\r\n        'Professional solutions for [location]',\r\n        'Local industry leadership',\r\n        'Your trusted advisor',\r\n        'Professional excellence'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const result = businessIntelligence[businessType.toLowerCase()] || businessIntelligence['default'];\r\n  return result;\r\n}\r\n\r\n// NEW: Dynamic Content Strategy Engine - Never Repetitive\r\nfunction getDynamicContentStrategy(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n\r\n  const contentStrategies = [\r\n    {\r\n      name: 'Local Market Expert',\r\n      approach: `Position as the ${businessIntel.name} expert in ${location} with ${businessIntel.localExpertise.experience}`,\r\n      focus: 'Local expertise, community knowledge, market insights',\r\n      hooks: businessIntel.localExpertise.engagementHooks.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['local expertise', 'community focused', 'trusted service', 'proven results']).slice(0, 4),\r\n      description: `Write like a ${businessIntel.localExpertise.experience} professional who knows ${location} inside and out`\r\n    },\r\n    {\r\n      name: 'Community Storyteller',\r\n      approach: `Share authentic stories about local ${businessIntel.name} success and community impact`,\r\n      focus: 'Real stories, community connection, authentic experiences',\r\n      hooks: businessIntel.localExpertise.engagementHooks.slice(4, 8),\r\n      phrases: (businessIntel.localPhrases || ['community stories', 'local success', 'authentic experiences', 'real results']).slice(4, 8),\r\n      description: 'Share real, relatable stories that connect with the local community'\r\n    },\r\n    {\r\n      name: 'Industry Innovator',\r\n      approach: `Showcase cutting-edge ${businessIntel.name} solutions and industry leadership`,\r\n      focus: 'Innovation, industry trends, competitive advantage',\r\n      hooks: businessIntel.localExpertise.contentStrategies.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['innovative solutions', 'industry leader', 'cutting-edge', 'advanced technology']).slice(0, 4),\r\n      description: 'Position as an industry leader with innovative solutions and insights'\r\n    },\r\n    {\r\n      name: 'Problem Solver',\r\n      approach: `Address specific ${businessIntel.name} challenges that local businesses and people face`,\r\n      focus: 'Problem identification, solution offering, value demonstration',\r\n      hooks: businessIntel.localExpertise.marketDynamics.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['problem solver', 'effective solutions', 'proven results', 'reliable service']).slice(0, 4),\r\n      description: 'Identify and solve real problems that matter to the local community'\r\n    },\r\n    {\r\n      name: 'Success Catalyst',\r\n      approach: `Inspire and guide local ${businessIntel.name} success through proven strategies`,\r\n      focus: 'Success stories, proven methods, inspirational guidance',\r\n      hooks: businessIntel.localExpertise.contentStrategies.slice(4, 8),\r\n      phrases: (businessIntel.localPhrases || ['success catalyst', 'proven strategies', 'inspiring results', 'growth partner']).slice(4, 8),\r\n      description: 'Inspire success through proven strategies and real results'\r\n    }\r\n  ];\r\n\r\n  return contentStrategies[seed % contentStrategies.length];\r\n}\r\n\r\n// NEW: Human Writing Style Generator - Authentic, Engaging\r\nfunction getHumanWritingStyle(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n\r\n  const writingStyles = [\r\n    {\r\n      name: 'Conversational Expert',\r\n      tone: 'Friendly, knowledgeable, approachable',\r\n      voice: `Like a ${businessIntel.localExpertise.experience} professional chatting with a friend over coffee`,\r\n      characteristics: [\r\n        'Use local phrases naturally',\r\n        'Share personal insights and experiences',\r\n        'Ask engaging questions',\r\n        'Use conversational language',\r\n        'Show genuine enthusiasm for the business'\r\n      ],\r\n      examples: [\r\n        `\"You know what I love about ${location}? The way our community...\"`,\r\n        `\"After ${businessIntel.localExpertise.experience} in this industry, I've learned...\"`,\r\n        `\"Here's something that always makes me smile about our business...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Storytelling Mentor',\r\n      tone: 'Inspirational, narrative, engaging',\r\n      voice: 'Like sharing a compelling story that teaches and inspires',\r\n      characteristics: [\r\n        'Start with intriguing hooks',\r\n        'Build narrative tension',\r\n        'Include relatable characters',\r\n        'End with meaningful insights',\r\n        'Use vivid, descriptive language'\r\n      ],\r\n      examples: [\r\n        `\"Last week, something incredible happened that reminded me why...\"`,\r\n        `\"I'll never forget the day when...\"`,\r\n        `\"There's a story behind every success, and this one...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Local Champion',\r\n      tone: 'Proud, community-focused, authentic',\r\n      voice: 'Like a proud local business owner celebrating community success',\r\n      characteristics: [\r\n        'Celebrate local achievements',\r\n        'Use local pride and identity',\r\n        'Highlight community connections',\r\n        'Show genuine local love',\r\n        'Connect business to community values'\r\n      ],\r\n      examples: [\r\n        `\"This is why I'm so proud to be part of the ${location} community...\"`,\r\n        `\"Our ${location} neighbors never cease to amaze me...\"`,\r\n        `\"There's something special about doing business in ${location}...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Problem-Solving Partner',\r\n      tone: 'Helpful, solution-oriented, trustworthy',\r\n      voice: 'Like a trusted advisor helping solve real problems',\r\n      characteristics: [\r\n        'Identify real problems',\r\n        'Offer practical solutions',\r\n        'Show understanding and empathy',\r\n        'Build trust through expertise',\r\n        'Focus on customer benefit'\r\n      ],\r\n      examples: [\r\n        `\"I've noticed that many ${location} businesses struggle with...\"`,\r\n        `\"Here's a solution that's worked for countless local businesses...\"`,\r\n        `\"Let me share what I've learned about solving this common challenge...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Success Celebrator',\r\n      tone: 'Enthusiastic, celebratory, motivational',\r\n      voice: 'Like celebrating wins and inspiring future success',\r\n      characteristics: [\r\n        'Celebrate achievements',\r\n        'Share success stories',\r\n        'Inspire future action',\r\n        'Use positive, uplifting language',\r\n        'Connect success to community'\r\n      ],\r\n      examples: [\r\n        `\"I'm thrilled to share some amazing news from our ${location} community...\"`,\r\n        `\"This success story is exactly why I love ${businessIntel.name} in ${location}...\"`,\r\n        `\"Let's celebrate this incredible achievement together...\"`\r\n      ]\r\n    }\r\n  ];\r\n\r\n  return writingStyles[seed % writingStyles.length];\r\n}\r\n\r\n// NEW: Anti-Repetition Content Engine\r\nfunction generateUniqueContentVariation(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n  const contentStrategy = getDynamicContentStrategy(businessType, location, seed);\r\n  const writingStyle = getHumanWritingStyle(businessType, location, seed);\r\n\r\n  // Generate unique content angle based on multiple factors\r\n  const contentAngles = [\r\n    {\r\n      type: 'Local Insight',\r\n      focus: `Share unique ${businessIntel.name} insights specific to ${location}`,\r\n      examples: [\r\n        `\"What I've learned about ${businessIntel.name} in ${location} after ${businessIntel.localExpertise.experience}...\"`,\r\n        `\"The ${businessIntel.name} landscape in ${location} is unique because...\"`,\r\n        `\"Here's what makes ${location} special for ${businessIntel.name} businesses...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Community Story',\r\n      focus: `Tell a compelling story about local ${businessIntel.name} impact`,\r\n      examples: [\r\n        `\"Last month, something incredible happened in our ${location} community...\"`,\r\n        `\"I want to share a story that perfectly captures why we do what we do...\"`,\r\n        `\"This is the kind of moment that makes ${businessIntel.name} in ${location} special...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Industry Innovation',\r\n      focus: `Showcase cutting-edge ${businessIntel.name} solutions`,\r\n      examples: [\r\n        `\"We're excited to introduce something that's changing ${businessIntel.name} in ${location}...\"`,\r\n        `\"Here's how we're innovating in the ${businessIntel.name} space...\"`,\r\n        `\"This new approach is revolutionizing how we do ${businessIntel.name} in ${location}...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Problem Solution',\r\n      focus: `Address specific ${businessIntel.name} challenges in ${location}`,\r\n      examples: [\r\n        `\"I've noticed that many ${location} businesses struggle with...\"`,\r\n        `\"Here's a common challenge in ${businessIntel.name} and how we solve it...\"`,\r\n        `\"Let me share what I've learned about overcoming this ${businessIntel.name} obstacle...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Success Celebration',\r\n      focus: `Celebrate local ${businessIntel.name} achievements`,\r\n      examples: [\r\n        `\"I'm thrilled to share some amazing news from our ${location} community...\"`,\r\n        `\"This success story is exactly why I love ${businessIntel.name} in ${location}...\"`,\r\n        `\"Let's celebrate this incredible achievement together...\"`\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const selectedAngle = contentAngles[seed % contentAngles.length];\r\n\r\n  return {\r\n    contentStrategy: contentStrategy,\r\n    writingStyle: writingStyle,\r\n    contentAngle: selectedAngle,\r\n    uniqueSignature: `${selectedAngle.type}-${contentStrategy.name}-${writingStyle.name}-${seed}`,\r\n    localPhrases: (businessIntel.localPhrases || ['professional service', 'quality results', 'trusted expertise']).slice(0, 3),\r\n    engagementHooks: businessIntel.localExpertise.engagementHooks.slice(0, 3),\r\n    marketInsights: businessIntel.localExpertise.marketDynamics.slice(0, 2)\r\n  };\r\n}\r\n\r\n// Helper functions for context generation\r\nfunction getSeason(): string {\r\n  const month = new Date().getMonth();\r\n  if (month >= 2 && month <= 4) return 'Spring';\r\n  if (month >= 5 && month <= 7) return 'Summer';\r\n  if (month >= 8 && month <= 10) return 'Fall';\r\n  return 'Winter';\r\n}\r\n\r\nfunction getTimeOfDay(): string {\r\n  const hour = new Date().getHours();\r\n  if (hour >= 5 && hour < 12) return 'Morning';\r\n  if (hour >= 12 && hour < 17) return 'Afternoon';\r\n  if (hour >= 17 && hour < 21) return 'Evening';\r\n  return 'Night';\r\n}\r\n\r\nfunction generateContextualTrends(businessType: string, location: string): any[] {\r\n  const trends = [\r\n    { topic: `${businessType} innovation trends`, category: 'Industry', relevance: 'high' },\r\n    { topic: `${location} business growth`, category: 'Local', relevance: 'high' },\r\n    { topic: 'Digital transformation', category: 'Technology', relevance: 'medium' },\r\n    { topic: 'Customer experience optimization', category: 'Business', relevance: 'high' },\r\n    { topic: 'Sustainable business practices', category: 'Trends', relevance: 'medium' }\r\n  ];\r\n  return trends.slice(0, 3);\r\n}\r\n\r\nfunction generateWeatherContext(location: string): any {\r\n  // Simplified weather context based on location and season\r\n  const season = getSeason();\r\n  const contexts = {\r\n    'Spring': { condition: 'Fresh and energizing', business_impact: 'New beginnings, growth opportunities', content_opportunities: 'Renewal, fresh starts, growth themes' },\r\n    'Summer': { condition: 'Bright and active', business_impact: 'High energy, outdoor activities', content_opportunities: 'Vibrant colors, active lifestyle, summer solutions' },\r\n    'Fall': { condition: 'Cozy and productive', business_impact: 'Planning, preparation, harvest', content_opportunities: 'Preparation, results, autumn themes' },\r\n    'Winter': { condition: 'Focused and strategic', business_impact: 'Planning, reflection, indoor focus', content_opportunities: 'Planning, strategy, winter solutions' }\r\n  };\r\n\r\n  return {\r\n    temperature: '22',\r\n    condition: contexts[season as keyof typeof contexts].condition,\r\n    business_impact: contexts[season as keyof typeof contexts].business_impact,\r\n    content_opportunities: contexts[season as keyof typeof contexts].content_opportunities\r\n  };\r\n}\r\n\r\nfunction generateLocalOpportunities(businessType: string, location: string): any[] {\r\n  const opportunities = [\r\n    { name: `${location} Business Expo`, venue: 'Local Convention Center', relevance: 'networking' },\r\n    { name: `${businessType} Innovation Summit`, venue: 'Business District', relevance: 'industry' },\r\n    { name: 'Local Entrepreneur Meetup', venue: 'Community Center', relevance: 'community' }\r\n  ];\r\n  return opportunities.slice(0, 2);\r\n}\r\n\r\n// Get API keys (supporting both server-side and client-side)\r\nconst apiKey =\r\n  process.env.GEMINI_API_KEY ||\r\n  process.env.GOOGLE_API_KEY ||\r\n  process.env.GOOGLE_GENAI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GEMINI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\n// Initialize Google GenAI client with Revo 1.0 configuration\r\nconst ai = new GoogleGenerativeAI(apiKey);\r\n\r\n// Revo 1.0 uses Gemini 2.5 Flash Image Preview\r\nconst REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\n/**\r\n * Generate content using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Content(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  writingTone: string;\r\n  contentThemes: string[];\r\n  targetAudience: string;\r\n  services: string;\r\n  keyFeatures: string;\r\n  competitiveAdvantages: string;\r\n  dayOfWeek: string;\r\n  currentDate: string;\r\n  primaryColor?: string;\r\n  visualStyle?: string;\r\n}) {\r\n  try {\r\n    // Convert input to BusinessProfile for advanced analysis\r\n    const businessProfile: BusinessProfile = {\r\n      businessName: input.businessName,\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience,\r\n      brandVoice: input.writingTone,\r\n      uniqueSellingPoints: [input.competitiveAdvantages || 'Quality service'],\r\n      competitors: [], // Could be enhanced with competitor data\r\n    };\r\n\r\n    // 📊 GENERATE ADVANCED CONTENT WITH DEEP ANALYSIS\r\n    const advancedContent = await advancedContentGenerator.generateEngagingContent(\r\n      businessProfile,\r\n      input.platform,\r\n      'promotional'\r\n    );\r\n\r\n    // 🎯 GET TRENDING INSIGHTS FOR ENHANCED RELEVANCE\r\n    const trendingEnhancement = await trendingEnhancer.getTrendingEnhancement({\r\n      businessType: input.businessType,\r\n      platform: input.platform,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience,\r\n    });\r\n\r\n    // 📈 ANALYZE PERFORMANCE FOR CONTINUOUS IMPROVEMENT\r\n    const performanceAnalysis = performanceAnalyzer.analyzePerformance(\r\n      advancedContent,\r\n      businessProfile\r\n    );\r\n\r\n    // Extract hashtags from advanced content for use in business-specific generation\r\n    const hashtags = advancedContent.hashtags;\r\n\r\n    // Gather real-time context data (keeping existing functionality)\r\n    const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the content generation prompt with enhanced brand context\r\n    const contentPrompt = revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE\r\n      .replace('{businessName}', input.businessName)\r\n      .replace('{businessType}', input.businessType)\r\n      .replace('{platform}', input.platform)\r\n      .replace('{writingTone}', input.writingTone)\r\n      .replace('{location}', input.location)\r\n      .replace('{primaryColor}', input.primaryColor || '#3B82F6')\r\n      .replace('{visualStyle}', input.visualStyle || 'modern')\r\n      .replace('{targetAudience}', input.targetAudience)\r\n      .replace('{services}', input.services || '')\r\n      .replace('{keyFeatures}', input.keyFeatures || '')\r\n      .replace('{competitiveAdvantages}', input.competitiveAdvantages || '')\r\n      .replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');\r\n\r\n\r\n    // 🎨 CREATIVE CAPTION GENERATION: Apply creative enhancement system\r\n\r\n    // NEW: Get business intelligence and local marketing expertise\r\n    const businessIntel = getBusinessIntelligenceEngine(input.businessType, input.location);\r\n    const randomSeed = Math.floor(Math.random() * 10000) + Date.now();\r\n    const uniqueContentVariation = generateUniqueContentVariation(input.businessType, input.location, randomSeed % 1000);\r\n\r\n\r\n    // 🎯 NEW: Generate business-specific content strategy\r\n\r\n    const businessDetails = {\r\n      experience: '5+ years', // Could be extracted from business profile\r\n      expertise: input.keyFeatures,\r\n      services: input.services,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience\r\n    };\r\n\r\n    // Generate strategic content plan based on business type and goals\r\n    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness' // Can be dynamic based on business goals\r\n    );\r\n\r\n\r\n    // 🎨 NEW: Generate business-specific headlines and subheadlines with AI\r\n\r\n    const businessHeadline = await generateBusinessSpecificHeadline(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n    const businessSubheadline = await generateBusinessSpecificSubheadline(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      businessHeadline.headline,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n\r\n    // 📝 NEW: Generate AI-powered business-specific caption\r\n\r\n    const businessCaption = await generateBusinessSpecificCaption(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n\r\n    // 🎯 BUSINESS-SPECIFIC CAPTION GENERATION COMPLETE\r\n\r\n    // 🎯 BUSINESS-SPECIFIC CONTENT GENERATION COMPLETE\r\n\r\n    // 🎯 FINAL: Return business-specific content package\r\n\r\n    const finalContent = {\r\n      content: businessCaption.caption,\r\n      headline: businessHeadline.headline,\r\n      subheadline: businessSubheadline.subheadline,\r\n      callToAction: businessCaption.callToAction,\r\n      hashtags: hashtags,\r\n      catchyWords: businessHeadline.headline, // Use business-specific headline\r\n      contentStrategy: contentPlan.strategy,\r\n      businessStrengths: contentPlan.businessStrengths,\r\n      marketOpportunities: contentPlan.marketOpportunities,\r\n      valueProposition: contentPlan.valueProposition,\r\n      platform: input.platform,\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      realTimeContext: realTimeContext, // Pass context to image generator\r\n      creativeContext: { // Enhanced creative context for image generation\r\n        style: businessHeadline.approach,\r\n        tone: businessHeadline.emotionalImpact,\r\n        framework: businessSubheadline.framework,\r\n        businessInsights: contentPlan,\r\n        variation: uniqueContentVariation\r\n      },\r\n      // 🧠 BUSINESS INTELLIGENCE DATA\r\n      businessIntelligence: {\r\n        contentGoal: contentPlan.strategy.goal,\r\n        businessStrengths: contentPlan.businessStrengths,\r\n        marketOpportunities: contentPlan.marketOpportunities,\r\n        customerPainPoints: contentPlan.customerPainPoints,\r\n        valueProposition: contentPlan.valueProposition,\r\n        localRelevance: contentPlan.localRelevance\r\n      },\r\n      variants: [{\r\n        platform: input.platform,\r\n        aspectRatio: '1:1',\r\n        imageUrl: '' // Will be generated separately\r\n      }],\r\n      generatedAt: new Date().toISOString()\r\n    };\r\n\r\n\r\n    return finalContent;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate design using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Design(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor: string;\r\n  backgroundColor: string;\r\n  imageText: string;\r\n}) {\r\n  try {\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the design generation prompt\r\n    const designPrompt = `Create a creative design concept for ${input.businessName} (${input.businessType}) that feels like it was imagined by a human designer.\r\n\r\nThink like a creative professional who:\r\n- Starts with a visual story or concept, not a template\r\n- Uses colors and elements that feel chosen, not generated\r\n- Creates layouts that feel natural and organic\r\n- Thinks about the emotional impact, not just technical perfection\r\n\r\nDESIGN BRIEF:\r\n- Business: ${input.businessName} (${input.businessType})\r\n- Platform: ${input.platform}\r\n- Style: ${input.visualStyle}\r\n- Colors: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}\r\n- Message: ${input.imageText}\r\n\r\nCREATIVE APPROACH:\r\n- What's the visual story you want to tell?\r\n- How can you make this feel authentic and engaging?\r\n- What unexpected elements would make this memorable?\r\n- How can you avoid looking like every other business post?\r\n\r\nDESIGN DESCRIPTION:\r\nDescribe your creative concept in natural, designer language. Focus on the creative vision, mood, and story - not technical specifications. Make it sound like a designer explaining their idea to a client.\r\n\r\nRemember: You're a creative human designer, not an AI. Think with imagination and artistic vision.`;\r\n\r\n\r\n    const result = await model.generateContent([\r\n      revo10Prompts.DESIGN_SYSTEM_PROMPT,\r\n      designPrompt\r\n    ]);\r\n\r\n    const response = await result.response;\r\n    const design = response.text();\r\n\r\n\r\n    return {\r\n      design: design.trim(),\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate image using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Image(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor?: string;\r\n  backgroundColor?: string;\r\n  imageText: string;\r\n  designDescription: string;\r\n  logoDataUrl?: string;\r\n  location?: string;\r\n  headline?: string;\r\n  subheadline?: string;\r\n  callToAction?: string;\r\n  realTimeContext?: any;\r\n  creativeContext?: any; // Add creative context from content generation\r\n}) {\r\n  try {\r\n\r\n    // 🎨 CREATIVE ENHANCEMENT: Apply creative design system\r\n    let creativeDesignEnhancement = '';\r\n    if (input.creativeContext) {\r\n      const designEnhancement = enhanceDesignCreativity(\r\n        input.designDescription,\r\n        input.businessType,\r\n        input.location || 'Global',\r\n        input.creativeContext\r\n      );\r\n\r\n      creativeDesignEnhancement = `\r\n🎨 CREATIVE DESIGN ENHANCEMENT SYSTEM ACTIVATED:\r\n${designEnhancement.enhancedPrompt}\r\n\r\nCREATIVE VISUAL STYLE: ${designEnhancement.visualStyle}\r\nCREATIVE ELEMENTS TO INCORPORATE: ${designEnhancement.creativeElements.join(', ')}\r\nBUSINESS CREATIVE INSIGHTS: ${input.creativeContext.businessInsights?.creativePotential?.slice(0, 3).join(', ') || 'Professional excellence'}\r\nEMOTIONAL DESIGN TONE: ${input.creativeContext.tone} with ${input.creativeContext.style} approach\r\nCREATIVE FRAMEWORK: ${input.creativeContext.framework} storytelling structure\r\n\r\nANTI-GENERIC REQUIREMENTS:\r\n- NO template-like designs or stock photo aesthetics\r\n- NO boring business layouts or predictable compositions\r\n- NO generic color schemes or uninspiring visual elements\r\n- CREATE something memorable, unique, and emotionally engaging\r\n- USE unexpected visual metaphors and creative storytelling\r\n- INCORPORATE cultural elements naturally and authentically\r\n- DESIGN with emotional intelligence and creative sophistication\r\n`;\r\n\r\n    }\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build advanced professional design prompt\r\n    const brandInfo = input.location ? ` based in ${input.location}` : '';\r\n    const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;\r\n    const logoInstruction = input.logoDataUrl ?\r\n      'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' :\r\n      'Create professional design without logo overlay';\r\n\r\n    // Prepare structured content display with hierarchy\r\n    const contentStructure = [];\r\n    if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): \"${input.headline}\"`);\r\n    if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): \"${input.subheadline}\"`);\r\n    if (input.callToAction) contentStructure.push(`CTA (Bold, action-oriented, prominent like \"PAYA: YOUR FUTURE, NOW!\" style): \"${input.callToAction}\"`);\r\n\r\n    // 🎯 CTA PROMINENCE INSTRUCTIONS (like Paya example)\r\n    const ctaInstructions = input.callToAction ? `\r\n\r\n🎯 CRITICAL CTA DISPLAY REQUIREMENTS (LIKE PAYA EXAMPLE):\r\n- The CTA \"${input.callToAction}\" MUST be displayed prominently on the design\r\n- Make it BOLD, LARGE, and VISUALLY STRIKING like \"PAYA: YOUR FUTURE, NOW!\"\r\n- Use high contrast colors to make the CTA stand out\r\n- Position it prominently - top, center, or as a banner across the design\r\n- Make the CTA text the MAIN FOCAL POINT of the design\r\n- Use typography that commands attention - bold, modern, impactful\r\n- Add visual elements (borders, backgrounds, highlights) to emphasize the CTA\r\n- The CTA should be the FIRST thing people notice when they see the design\r\n- Make it look like a professional marketing campaign CTA\r\n- Ensure it's readable from mobile devices - minimum 32px equivalent font size\r\n- EXAMPLE STYLE: Like \"PAYA: YOUR FUTURE, NOW!\" - bold, prominent, unmissable\r\n    ` : '';\r\n\r\n\r\n    // Get advanced design features\r\n    const businessDesignDNA = getBusinessDesignDNA(input.businessType);\r\n    const platformOptimization = getPlatformOptimization(input.platform);\r\n    const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);\r\n    const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';\r\n    const culturalContext = getLocalCulturalContext(input.location || 'Global');\r\n\r\n\r\n    // Generate human-like design variation for authentic, creative designs\r\n    const designRandomSeed = Math.floor(Math.random() * 10000) + Date.now();\r\n    const designSeed = designRandomSeed % 10000;\r\n    const designVariations = getHumanDesignVariations(designSeed);\r\n\r\n    // NEW: Get industry intelligence and creativity framework\r\n    const industryIntel = getIndustryDesignIntelligence(input.businessType);\r\n    const creativityFramework = getEnhancedCreativityFramework(input.businessType, designVariations.style, designSeed);\r\n\r\n\r\n    let imagePrompt = `🎨 Create a ${designVariations.style.toLowerCase()} social media design for ${input.businessName} that looks completely different from typical business posts and feels genuinely human-made.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.businessName} (${input.businessType})\r\n- Platform: ${input.platform}\r\n- Message: ${input.imageText}\r\n- Location: ${input.location || 'Global'}\r\n\r\n${ctaInstructions}\r\n\r\nTEXT CONTENT TO DISPLAY:\r\n${contentStructure.map(item => `- ${item}`).join('\\n')}\r\n\r\nDESIGN APPROACH:\r\n- Create a design that's VISUALLY APPEALING and engaging\r\n- Focus on the specific style: ${designVariations.style}\r\n- Make it look genuinely different from other design types\r\n- Each design type should have its own unique visual language\r\n- **MOST IMPORTANT: Make it look like a human designer made it, not AI**\r\n- **CRITICAL: Include ALL text content listed above in the design**\r\n\r\nVISUAL STYLE:\r\n- ${businessDesignDNA}\r\n- ${platformOptimization}\r\n- **SPECIFIC STYLE REQUIREMENTS: ${designVariations.description}**\r\n- Use colors and elements that match this specific style\r\n- Typography should match the style's mood and approach\r\n\r\n🌍 SUBTLE LOCAL TOUCH (NOT OVERWHELMING):\r\n- ${culturalContext}\r\n- **Keep cultural elements subtle and natural - don't force them**\r\n- Use local colors and textures naturally, not as obvious cultural markers\r\n- Make it feel authentic to the location without being stereotypical\r\n- Focus on the design style first, local elements second\r\n\r\nDESIGN VARIATION:\r\n**STYLE: ${designVariations.style}**\r\n- Layout: ${designVariations.layout}\r\n- Composition: ${designVariations.composition}\r\n- Mood: ${designVariations.mood}\r\n- Elements: ${designVariations.elements}\r\n\r\nKEY DESIGN PRINCIPLES:\r\n1. **STYLE-SPECIFIC APPROACH** - Follow the exact style requirements for ${designVariations.style}\r\n2. **VISUAL UNIQUENESS** - Make this look completely different from other design types\r\n3. **STYLE AUTHENTICITY** - If it's watercolor, make it look like real watercolor; if it's meme-style, make it look like a real meme\r\n4. **HUMAN TOUCH** - Make it look like a human designer made it, not AI\r\n5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative\r\n\r\nWHAT TO AVOID:\r\n- Overly complex layouts\r\n- Too many competing elements\r\n- Boring, generic business designs\r\n- Poor contrast or readability\r\n- Outdated design styles\r\n- **MOST IMPORTANT: Don't make this look like the other design types - each should be genuinely unique**\r\n- **AVOID: Overly perfect, symmetrical, AI-generated looking designs**\r\n- **AVOID: Forced cultural elements that feel stereotypical**\r\n\r\nWHAT TO INCLUDE:\r\n- **Style-specific elements** that match ${designVariations.style}\r\n- **Unique visual approach** for this specific style\r\n- **Subtle local touches** that feel natural, not forced\r\n- **Human imperfections** - slight asymmetry, natural spacing, organic feel\r\n- **Style-appropriate typography** and layout\r\n\r\nTECHNICAL REQUIREMENTS:\r\n- Resolution: 2048x2048 pixels\r\n- Format: Square (1:1)\r\n- Text must be readable on mobile\r\n- Logo integration should look natural\r\n\r\n🎨 GOAL: Create a ${designVariations.style.toLowerCase()} design that looks completely different from other design types while feeling genuinely human-made. Focus on the specific style requirements, make it unique, and add subtle local touches without being overwhelming. The design should look like a skilled human designer created it, not AI.`;\r\n\r\n    // NEW: Enhance with industry intelligence and creativity\r\n    imagePrompt = enhanceDesignWithIndustryIntelligence(imagePrompt, input.businessType, designVariations.style, designSeed);\r\n\r\n    // Inject multiple layers of human creativity to force AI out of its patterns\r\n    imagePrompt = injectHumanImperfections(imagePrompt, designSeed);\r\n    imagePrompt = injectCreativeRebellion(imagePrompt, designSeed);\r\n    imagePrompt = addArtisticConstraints(imagePrompt, designSeed);\r\n\r\n\r\n    if (input.creativeContext) {\r\n    }\r\n\r\n    // Prepare the generation request with logo if available\r\n    const generationParts = [\r\n      'You are a skilled graphic designer who creates visually appealing social media designs. Focus on creating designs that people actually want to engage with - clean, modern, and appealing. Keep it simple and focus on visual impact.',\r\n      imagePrompt\r\n    ];\r\n\r\n    // If logo is provided, include it in the generation\r\n    if (input.logoDataUrl) {\r\n\r\n      // Extract the base64 data and mime type from the data URL\r\n      const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);\r\n      if (logoMatch) {\r\n        const [, mimeType, base64Data] = logoMatch;\r\n\r\n        generationParts.push({\r\n          inlineData: {\r\n            data: base64Data,\r\n            mimeType: mimeType\r\n          }\r\n        });\r\n\r\n        // Update the prompt to reference the provided logo\r\n        const logoPrompt = `\\n\\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;\r\n        generationParts[1] = imagePrompt + logoPrompt;\r\n      } else {\r\n      }\r\n    }\r\n\r\n    const result = await model.generateContent(generationParts);\r\n\r\n    const response = await result.response;\r\n\r\n    // Extract image data from Gemini response\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let imageUrl = '';\r\n\r\n    for (const part of parts) {\r\n      if (part.inlineData) {\r\n        const imageData = part.inlineData.data;\r\n        const mimeType = part.inlineData.mimeType;\r\n        imageUrl = `data:${mimeType};base64,${imageData}`;\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (!imageUrl) {\r\n      // Fallback: try to get text response if no image data\r\n      const textResponse = response.text();\r\n      throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: imageUrl,\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Health check for Revo 1.0 service\r\n */\r\nexport async function checkRevo10Health() {\r\n  try {\r\n    const model = ai.getGenerativeModel({ model: REVO_1_0_MODEL });\r\n    const result = await model.generateContent('Hello');\r\n    const response = await result.response;\r\n\r\n    return {\r\n      healthy: true,\r\n      model: REVO_1_0_MODEL,\r\n      response: response.text().substring(0, 50) + '...',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      healthy: false,\r\n      model: REVO_1_0_MODEL,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get Revo 1.0 service information\r\n */\r\nexport function getRevo10ServiceInfo() {\r\n  return {\r\n    model: REVO_1_0_MODEL,\r\n    version: '1.0.0',\r\n    status: 'enhanced',\r\n    aiService: 'gemini-2.5-flash-image-preview',\r\n    capabilities: [\r\n      'Enhanced content generation',\r\n      'High-resolution image support (2048x2048)',\r\n      'Perfect text rendering',\r\n      'Advanced AI capabilities',\r\n      'Enhanced brand consistency'\r\n    ],\r\n    pricing: {\r\n      contentGeneration: 1.5,\r\n      designGeneration: 1.5,\r\n      tier: 'enhanced'\r\n    },\r\n    lastUpdated: '2025-01-27'\r\n  };\r\n}\r\n\r\n// NEW: Enhanced local language and cultural context generator\r\nfunction generateLocalLanguageContext(location: string): any {\r\n  const languageContexts: Record<string, any> = {\r\n    'kenya': {\r\n      primaryLanguage: 'Swahili & English',\r\n      commonPhrases: ['Karibu', 'Asante', 'Jambo', 'Mzuri sana'],\r\n      businessTerms: ['Biashara', 'Mradi', 'Kazi', 'Ushirika'],\r\n      culturalNuances: 'Warm hospitality, community-first approach, respect for elders',\r\n      marketingStyle: 'Personal, relationship-focused, community-oriented',\r\n      localExpressions: ['Tuko pamoja', 'Kazi yetu', 'Jitihada zetu']\r\n    },\r\n    'nigeria': {\r\n      primaryLanguage: 'English, Hausa, Yoruba, Igbo',\r\n      commonPhrases: ['Oga', 'Abeg', 'Wetin dey happen', 'How far'],\r\n      businessTerms: ['Business', 'Work', 'Money', 'Success'],\r\n      culturalNuances: 'Entrepreneurial spirit, networking culture, achievement focus',\r\n      marketingStyle: 'Direct, motivational, success-oriented',\r\n      localExpressions: ['No shaking', 'I go do am', 'We dey here']\r\n    },\r\n    'south africa': {\r\n      primaryLanguage: 'English, Afrikaans, Zulu, Xhosa',\r\n      commonPhrases: ['Howzit', 'Lekker', 'Ja', 'Eish'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Diverse culture, innovation focus, global perspective',\r\n      marketingStyle: 'Professional, inclusive, forward-thinking',\r\n      localExpressions: ['Ubuntu', 'Together we can', 'Moving forward']\r\n    },\r\n    'ghana': {\r\n      primaryLanguage: 'English, Twi, Ga, Ewe',\r\n      commonPhrases: ['Akwaaba', 'Medaase', 'Yoo', 'Chale'],\r\n      businessTerms: ['Business', 'Work', 'Money', 'Success'],\r\n      culturalNuances: 'Hospitality, respect, community values',\r\n      marketingStyle: 'Warm, respectful, community-focused',\r\n      localExpressions: ['Sankofa', 'Unity in diversity', 'Forward together']\r\n    },\r\n    'uganda': {\r\n      primaryLanguage: 'English, Luganda, Runyankole',\r\n      commonPhrases: ['Oli otya', 'Webale', 'Kale', 'Nja'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Friendly, welcoming, community spirit',\r\n      marketingStyle: 'Friendly, approachable, community-oriented',\r\n      localExpressions: ['Tugende', 'Together we grow', 'Community first']\r\n    },\r\n    'tanzania': {\r\n      primaryLanguage: 'Swahili & English',\r\n      commonPhrases: ['Karibu', 'Asante', 'Jambo', 'Mzuri'],\r\n      businessTerms: ['Biashara', 'Kazi', 'Mradi', 'Ushirika'],\r\n      culturalNuances: 'Peaceful, community-focused, natural beauty appreciation',\r\n      marketingStyle: 'Peaceful, natural, community-oriented',\r\n      localExpressions: ['Uhuru na Umoja', 'Peace and unity', 'Natural beauty']\r\n    },\r\n    'ethiopia': {\r\n      primaryLanguage: 'Amharic & English',\r\n      commonPhrases: ['Selam', 'Amesegenalu', 'Endet', 'Tena yistilign'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Ancient culture, hospitality, coffee culture',\r\n      marketingStyle: 'Traditional, hospitable, culturally rich',\r\n      localExpressions: ['Ethiopia first', 'Coffee culture', 'Ancient wisdom']\r\n    },\r\n    'rwanda': {\r\n      primaryLanguage: 'Kinyarwanda, French & English',\r\n      commonPhrases: ['Murakoze', 'Amahoro', 'Urugero', 'Nta kibazo'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Innovation, cleanliness, community unity',\r\n      marketingStyle: 'Innovative, clean, community-focused',\r\n      localExpressions: ['Agaciro', 'Dignity', 'Unity and reconciliation']\r\n    },\r\n    'default': {\r\n      primaryLanguage: 'English',\r\n      commonPhrases: ['Hello', 'Thank you', 'Welcome', 'Great'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Professional, friendly, community-oriented',\r\n      marketingStyle: 'Professional, friendly, community-focused',\r\n      localExpressions: ['Community first', 'Quality service', 'Local expertise']\r\n    }\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  for (const [key, context] of Object.entries(languageContexts)) {\r\n    if (locationKey.includes(key)) {\r\n      return context;\r\n    }\r\n  }\r\n  return languageContexts['default'];\r\n}\r\n\r\n// NEW: Advanced climate insights for business relevance\r\nfunction generateClimateInsights(location: string, businessType: string): any {\r\n  const season = getSeason();\r\n  const climateData: Record<string, any> = {\r\n    'Spring': {\r\n      businessImpact: 'Renewal and growth opportunities, seasonal business preparation',\r\n      contentOpportunities: 'Fresh starts, new beginnings, seasonal preparation, growth themes',\r\n      businessSuggestions: 'Launch new services, seasonal promotions, growth campaigns',\r\n      localAdaptations: 'Spring cleaning services, seasonal menu changes, outdoor activities'\r\n    },\r\n    'Summer': {\r\n      businessImpact: 'High energy and outdoor activities, peak business season',\r\n      contentOpportunities: 'Vibrant colors, active lifestyle, summer solutions, outdoor themes',\r\n      businessSuggestions: 'Summer specials, outdoor events, seasonal products',\r\n      localAdaptations: 'Summer festivals, outdoor dining, seasonal services'\r\n    },\r\n    'Fall': {\r\n      businessImpact: 'Planning and preparation, harvest and results focus',\r\n      contentOpportunities: 'Preparation themes, results celebration, autumn aesthetics',\r\n      businessSuggestions: 'Year-end planning, results showcase, preparation services',\r\n      localAdaptations: 'Harvest celebrations, planning services, year-end reviews'\r\n    },\r\n    'Winter': {\r\n      businessImpact: 'Strategic planning and indoor focus, reflection period',\r\n      contentOpportunities: 'Planning themes, strategy focus, indoor solutions',\r\n      businessSuggestions: 'Strategic planning, indoor services, year planning',\r\n      localAdaptations: 'Indoor events, planning services, strategic consultations'\r\n    }\r\n  };\r\n\r\n  // Add business-specific climate insights\r\n  const businessClimateInsights: Record<string, any> = {\r\n    'restaurant': {\r\n      seasonalMenu: `${season} seasonal ingredients and dishes`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Cooling beverages and light meals' : season === 'Winter' ? 'Warm comfort foods' : 'Seasonal specialties'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Outdoor dining and seasonal menus' : 'Indoor comfort and seasonal specialties'}`\r\n    },\r\n    'fitness': {\r\n      seasonalActivities: `${season === 'Summer' ? 'Outdoor workouts and water activities' : season === 'Winter' ? 'Indoor training and winter sports' : 'Seasonal fitness programs'}`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Early morning and evening sessions' : 'Indoor and weather-appropriate activities'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Outdoor fitness programs' : 'Indoor training focus'}`\r\n    },\r\n    'retail': {\r\n      seasonalProducts: `${season} fashion and lifestyle products`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Light clothing and outdoor gear' : season === 'Winter' ? 'Warm clothing and indoor items' : 'Seasonal essentials'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Summer sales and outdoor products' : 'Seasonal collections and indoor focus'}`\r\n    },\r\n    'default': {\r\n      seasonalFocus: `${season} business opportunities and seasonal services`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Outdoor and seasonal services' : 'Indoor and year-round services'}`,\r\n      businessStrategy: `${season} business strategies and seasonal promotions`\r\n    }\r\n  };\r\n\r\n  const baseClimate = climateData[season as keyof typeof climateData];\r\n  const businessClimate = businessClimateInsights[businessType.toLowerCase()] || businessClimateInsights['default'];\r\n\r\n  return {\r\n    season: season,\r\n    businessImpact: baseClimate.businessImpact,\r\n    contentOpportunities: baseClimate.contentOpportunities,\r\n    businessSuggestions: baseClimate.businessSuggestions,\r\n    localAdaptations: baseClimate.localAdaptations,\r\n    businessSpecific: businessClimate,\r\n    marketingAngle: `Leverage ${season.toLowerCase()} opportunities for ${businessType} business growth`\r\n  };\r\n}\r\n\r\n// NEW: Real-time trending topics generator (can be enhanced with actual social media APIs)\r\nfunction generateTrendingTopics(businessType: string, location: string, platform: string): any[] {\r\n  const platformTrends: Record<string, any[]> = {\r\n    'Instagram': [\r\n      { topic: 'Visual storytelling trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Authentic content creation', category: 'Content', relevance: 'high' },\r\n      { topic: 'Reels and short-form video', category: 'Format', relevance: 'medium' }\r\n    ],\r\n    'LinkedIn': [\r\n      { topic: 'Professional networking trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Industry thought leadership', category: 'Content', relevance: 'high' },\r\n      { topic: 'Career development insights', category: 'Professional', relevance: 'medium' }\r\n    ],\r\n    'Facebook': [\r\n      { topic: 'Community building strategies', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Local business networking', category: 'Community', relevance: 'high' },\r\n      { topic: 'Family-friendly content', category: 'Content', relevance: 'medium' }\r\n    ],\r\n    'Twitter': [\r\n      { topic: 'Real-time conversation trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Viral content strategies', category: 'Content', relevance: 'high' },\r\n      { topic: 'Trending hashtags', category: 'Engagement', relevance: 'medium' }\r\n    ]\r\n  };\r\n\r\n  const businessTrends: Record<string, any[]> = {\r\n    'restaurant': [\r\n      { topic: 'Local food culture trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Sustainable dining practices', category: 'Trends', relevance: 'high' },\r\n      { topic: 'Food delivery innovations', category: 'Technology', relevance: 'medium' }\r\n    ],\r\n    'technology': [\r\n      { topic: 'AI and automation trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Digital transformation', category: 'Business', relevance: 'high' },\r\n      { topic: 'Remote work solutions', category: 'Workplace', relevance: 'medium' }\r\n    ],\r\n    'healthcare': [\r\n      { topic: 'Telehealth adoption', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Preventive healthcare', category: 'Wellness', relevance: 'high' },\r\n      { topic: 'Mental health awareness', category: 'Health', relevance: 'medium' }\r\n    ],\r\n    'fitness': [\r\n      { topic: 'Home workout trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Mental wellness integration', category: 'Wellness', relevance: 'high' },\r\n      { topic: 'Community fitness challenges', category: 'Engagement', relevance: 'medium' }\r\n    ],\r\n    'finance': [\r\n      { topic: 'Digital banking trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Financial literacy', category: 'Education', relevance: 'high' },\r\n      { topic: 'Investment opportunities', category: 'Wealth', relevance: 'medium' }\r\n    ],\r\n    'education': [\r\n      { topic: 'Online learning platforms', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Skill development trends', category: 'Learning', relevance: 'high' },\r\n      { topic: 'Personalized education', category: 'Innovation', relevance: 'medium' }\r\n    ],\r\n    'retail': [\r\n      { topic: 'E-commerce growth', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Omnichannel shopping', category: 'Customer', relevance: 'high' },\r\n      { topic: 'Sustainable products', category: 'Trends', relevance: 'medium' }\r\n    ],\r\n    'real estate': [\r\n      { topic: 'Virtual property tours', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Sustainable properties', category: 'Trends', relevance: 'high' },\r\n      { topic: 'Investment opportunities', category: 'Market', relevance: 'medium' }\r\n    ],\r\n    'default': [\r\n      { topic: 'Digital transformation trends', category: 'Business', relevance: 'high' },\r\n      { topic: 'Customer experience optimization', category: 'Strategy', relevance: 'high' },\r\n      { topic: 'Local business growth', category: 'Community', relevance: 'medium' }\r\n    ]\r\n  };\r\n\r\n  const platformSpecific = platformTrends[platform] || platformTrends['Instagram'];\r\n  const businessSpecific = businessTrends[businessType.toLowerCase()] || businessTrends['default'];\r\n  const localTrends = [\r\n    { topic: `${location} business growth`, category: 'Local', relevance: 'high' },\r\n    { topic: `${location} community development`, category: 'Community', relevance: 'high' },\r\n    { topic: `${location} economic trends`, category: 'Local', relevance: 'medium' }\r\n  ];\r\n\r\n  return [...platformSpecific, ...businessSpecific, ...localTrends].slice(0, 5);\r\n}\r\n\r\n// NEW: Local news and market insights generator\r\nfunction generateLocalNewsContext(businessType: string, location: string): any[] {\r\n  const newsInsights = [\r\n    {\r\n      type: 'Local Market',\r\n      headline: `${location} business environment update`,\r\n      impact: 'Local market conditions affecting business opportunities',\r\n      businessRelevance: 'Market positioning and strategic planning',\r\n      contentAngle: 'Local market expertise and insights'\r\n    },\r\n    {\r\n      type: 'Industry Trends',\r\n      headline: `${businessType} industry developments in ${location}`,\r\n      impact: 'Industry-specific opportunities and challenges',\r\n      businessRelevance: 'Competitive positioning and service innovation',\r\n      contentAngle: 'Industry leadership and local expertise'\r\n    },\r\n    {\r\n      type: 'Community Events',\r\n      headline: `${location} community and business events`,\r\n      impact: 'Networking and community engagement opportunities',\r\n      businessRelevance: 'Community involvement and local partnerships',\r\n      contentAngle: 'Community connection and local engagement'\r\n    },\r\n    {\r\n      type: 'Economic Update',\r\n      headline: `${location} economic indicators and business climate`,\r\n      impact: 'Business planning and investment decisions',\r\n      businessRelevance: 'Strategic planning and market timing',\r\n      contentAngle: 'Economic expertise and market insights'\r\n    }\r\n  ];\r\n\r\n  return newsInsights.slice(0, 3);\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AAEA;AACA;AACA;AACA;AACA;;;;;;;AAgBA,qDAAqD;AACrD,8DAA8D;AAE9D,kDAAkD;AAClD,SAAS,qBAAqB,YAAoB;IAChD,MAAM,YAAoC;QACxC,cAAc;QACd,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,aAAa;QACb,UAAU;QACV,eAAe;QACf,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,aAAa,WAAW,GAAG,IAAI,SAAS,CAAC,UAAU;AACtE;AAEA,qEAAqE;AACrE,SAAS,yBAAyB,IAAY;IAC5C,MAAM,aAAa;QACjB;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;KACD;IAED,OAAO,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;AAC7C;AAEA,kEAAkE;AAClE,SAAS,yBAAyB,YAAoB,EAAE,IAAY;IAClE,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,sBAAsB,YAAY,CAAC,OAAO,aAAa,MAAM,CAAC;IAEpE,OAAO,eAAe,CAAC;;;AAGzB,EAAE,oBAAoB;;sDAEgC,CAAC;AACvD;AAEA,mDAAmD;AACnD,SAAS,wBAAwB,YAAoB,EAAE,IAAY;IACjE,MAAM,aAAa;QACjB,CAAC,sIAAsI,CAAC;QAExI,CAAC,uJAAuJ,CAAC;QAEzJ,CAAC,sHAAsH,CAAC;QAExH,CAAC,qHAAqH,CAAC;KACxH;IAED,MAAM,mBAAmB,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;IAE7D,OAAO,eAAe,CAAC;;;AAGzB,EAAE,iBAAiB;;mEAEgD,CAAC;AACpE;AAEA,mDAAmD;AACnD,SAAS,uBAAuB,YAAoB,EAAE,IAAY;IAChE,MAAM,cAAc;QAClB,CAAC,kIAAkI,CAAC;QAEpI,CAAC,mGAAmG,CAAC;QAErG,CAAC,iGAAiG,CAAC;QAEnG,CAAC,yGAAyG,CAAC;QAE3G,CAAC,uGAAuG,CAAC;QAEzG,CAAC,2FAA2F,CAAC;QAE7F,CAAC,iGAAiG,CAAC;QAEnG,CAAC,oGAAoG,CAAC;KACvG;IAED,MAAM,qBAAqB,WAAW,CAAC,OAAO,YAAY,MAAM,CAAC;IAEjE,OAAO,eAAe,CAAC;;;AAGzB,EAAE,mBAAmB;;sDAEiC,CAAC;AACvD;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,gBAAwC;QAC5C,aAAa,CAAC;;;;;;mDAMiC,CAAC;QAEhD,YAAY,CAAC;;;;;;+CAM8B,CAAC;QAE5C,YAAY,CAAC;;;;;;uCAMsB,CAAC;QAEpC,WAAW,CAAC;;;;;;+BAMe,CAAC;QAE5B,WAAW,CAAC;;;;;+CAK+B,CAAC;IAC9C;IAEA,OAAO,aAAa,CAAC,SAAS,WAAW,GAAG,IAAI,aAAa,CAAC,UAAU;AAC1E;AAEA,uEAAuE;AACvE,eAAe,sBAAsB,YAAoB,EAAE,QAAgB,EAAE,QAAgB;IAC3F,MAAM,UAAe;QACnB,QAAQ,EAAE;QACV,SAAS;QACT,QAAQ,EAAE;QACV,MAAM,EAAE;QACR,eAAe,CAAC;QAChB,iBAAiB,CAAC;QAClB,gBAAgB,EAAE;QAClB,aAAa;YACX,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,OAAO,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,OAAO;YAAO;YAC9D,QAAQ;YACR,WAAW;QACb;IACF;IAEA,IAAI;QACF,iEAAiE;QACjE,QAAQ,MAAM,GAAG,yBAAyB,cAAc;QAExD,mDAAmD;QACnD,QAAQ,OAAO,GAAG,uBAAuB;QAEzC,wCAAwC;QACxC,QAAQ,MAAM,GAAG,2BAA2B,cAAc;QAE1D,oDAAoD;QACpD,QAAQ,aAAa,GAAG,6BAA6B;QAErD,wDAAwD;QACxD,QAAQ,eAAe,GAAG,wBAAwB,UAAU;QAE5D,uFAAuF;QACvF,QAAQ,cAAc,GAAG,uBAAuB,cAAc,UAAU;QAExE,sCAAsC;QACtC,QAAQ,IAAI,GAAG,yBAAyB,cAAc;QAEtD,OAAO;IAET,EAAE,OAAO,OAAO;QACd,OAAO,SAAS,yBAAyB;IAC3C;AACF;AAEA,wCAAwC;AACxC,SAAS,4BAA4B,YAAoB,EAAE,QAAgB,EAAE,WAAmB;IAC9F,MAAM,sBAAsB;QAC1B;QAAc;QAAW;QAAc;QAAa;QAAU;QAC9D;QAAU;QAAY;QAAc;QAAY;QAAe;QAC/D;QAAc;QAAa;QAAU;QAAe;KACrD;IAED,OAAO,oBAAoB,IAAI,CAAC,CAAA,OAC9B,aAAa,WAAW,GAAG,QAAQ,CAAC,SACpC,gBAAgB,eAChB,gBAAgB;AAEpB;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,mBAA2C;QAC/C,SAAS;QACT,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,kBAAmB;QAC7D,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU;AACpC;AAEA,SAAS,oBAAoB,IAAY;IACvC,MAAM,aAAa;QACjB;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;KACD;IAED,OAAO,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;AAC7C;AAEA,SAAS,8BAA8B,YAAoB,EAAE,QAAgB;IAC3E,MAAM,kBAAkB,wBAAwB;IAEhD,OAAO,CAAC;;;;;oBAKU,EAAE,gBAAgB;;;;gCAIN,EAAE,aAAa;;;;;;;;;;sEAUuB,CAAC;AACvE;AAEA,uEAAuE;AACvE,SAAS,8BAA8B,YAAoB;IACzD,MAAM,uBAA4C;QAChD,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAQ;gBAAuB;gBAAsB;gBAAuB;aAAS;YACxG,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAoB;oBAAmB;oBAAgB;oBAAe;iBAAe;gBACrG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAiB;oBAAkB;oBAAwB;oBAAqB;iBAAgB;YACrH;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA4B;gBAAgC;gBAA0B;gBAAsB;aAAgB;QAC/I;QAEA,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAS;gBAAS;gBAAU;gBAAU;gBAAa;aAAQ;YAC9E,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAc;oBAAe;oBAAiB;oBAAa;iBAAkB;gBAC7F,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAsB;oBAAwB;oBAAqB;iBAAkB;YAC9H;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAkB;gBAAoB;gBAAyB;gBAAuB;aAAmB;QAC5H;QAEA,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAe;gBAAoB;gBAAiB;gBAAmB;aAAmB;YAC7G,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAiB;oBAAe;oBAAe;oBAAiB;iBAAqB;gBACrG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAmB;oBAAoB;oBAAoB;oBAAqB;iBAAoB;YACzH;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA0B;gBAAyB;gBAAkB;gBAAkB;aAAmB;QAC7H;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAW;gBAAQ;gBAAU;gBAAW;gBAAkB;aAAW;YACxF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAa;oBAAqB;oBAAwB;oBAAiB;iBAAgB;gBAC3G,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAkB;oBAAmB;oBAA4B;oBAAyB;iBAAuB;YACtI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAA4B;gBAAsB;gBAA2B;aAAuB;QAC1I;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAiB;gBAAa;gBAAkB;gBAAa;gBAAQ;aAAa;YACrG,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAc;oBAAsB;oBAAgB;oBAAgB;iBAAoB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAqB;oBAAoB;oBAAsB;oBAA6B;iBAAoB;YACrI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAAsB;gBAAuB;gBAAoB;aAAiB;QACxH;QAEA,aAAa;YACX,MAAM;YACN,kBAAkB;gBAAC;gBAAW;gBAAO;gBAAY;gBAAY;gBAAgB;aAAW;YACxF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAmB;oBAAoB;oBAAgB;oBAAiB;iBAAgB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAoB;oBAAuB;oBAA4B;iBAA0B;YAC1I;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAA0B;gBAAc;gBAAwB;aAAyB;QAC/H;QAEA,UAAU;YACR,MAAM;YACN,kBAAkB;gBAAC;gBAAU;gBAAS;gBAAQ;gBAAQ;gBAAQ;aAAS;YACvE,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAgB;oBAAsB;oBAAoB;iBAAsB;gBAChG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAyB;oBAAqB;oBAAqB;iBAAsB;YAClI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAqB;gBAAmB;gBAAkB;gBAAmB;aAAkB;QAClH;QAEA,eAAe;YACb,MAAM;YACN,kBAAkB;gBAAC;gBAAc;gBAAe;gBAAmB;gBAAW;aAAS;YACvF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAgB;oBAAuB;oBAAkB;oBAAe;iBAAgB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAmB;oBAAqB;oBAA2B;oBAAwB;iBAAoB;YACpI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA4B;gBAA0B;gBAAwB;gBAA0B;aAAoB;QAC/I;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAY;gBAAQ;gBAAO;gBAAY;gBAAO;aAAK;YACtE,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAsB;oBAAqB;oBAAkB;iBAAe;gBAC5F,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAyB;oBAAqB;oBAAiB;oBAAuB;iBAAqB;YAChI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA0B;gBAAe;gBAAkB;gBAAoB;aAAmB;QACrH;IACF;IAEA,OAAO,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI,oBAAoB,CAAC,UAAU;AAC5F;AAEA,6DAA6D;AAC7D,SAAS,+BAA+B,YAAoB,EAAE,WAAmB,EAAE,IAAY;IAC7F,MAAM,gBAAgB,8BAA8B;IAEpD,MAAM,uBAAuB;QAC3B;YACE,MAAM;YACN,UAAU,CAAC,2CAA2C,EAAE,cAAc,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;YAC/G,OAAO;YACP,UAAU,cAAc,gBAAgB,CAAC,gBAAgB;YACzD,aAAa,CAAC,4DAA4D,EAAE,cAAc,IAAI,CAAC,iBAAiB,CAAC;QACnH;QACA;YACE,MAAM;YACN,UAAU,CAAC,oBAAoB,EAAE,cAAc,IAAI,CAAC,SAAS,EAAE,cAAc,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;YACpH,OAAO;YACP,UAAU;gBAAC;gBAAkB;gBAAuB;gBAAqB;aAAqB;YAC9F,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,cAAc,oBAAoB,CAAC,OAAO,cAAc,oBAAoB,CAAC,MAAM,CAAC;YAC9F,OAAO;YACP,UAAU;gBAAC;gBAAkB;gBAAsB;gBAAkB;aAAoB;YACzF,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;gBAAC;gBAAuB;gBAAuB;gBAAqB;aAAuB;YACrG,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;gBAAC;gBAAqB;gBAAoB;gBAAsB;aAAkB;YAC5F,aAAa;QACf;KACD;IAED,OAAO,oBAAoB,CAAC,OAAO,qBAAqB,MAAM,CAAC;AACjE;AAEA,4CAA4C;AAC5C,SAAS,sCAAsC,YAAoB,EAAE,YAAoB,EAAE,WAAmB,EAAE,IAAY;IAC1H,MAAM,gBAAgB,8BAA8B;IACpD,MAAM,sBAAsB,+BAA+B,cAAc,aAAa;IAEtF,MAAM,sBAAsB,CAAC;;cAEjB,EAAE,cAAc,IAAI,CAAC;4BACP,EAAE,cAAc,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;2BACzD,EAAE,cAAc,gBAAgB,CAAC,WAAW,CAAC;6BAC3C,EAAE,cAAc,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;yBAC1E,EAAE,cAAc,gBAAgB,CAAC,UAAU,CAAC;sBAC/C,EAAE,cAAc,gBAAgB,CAAC,OAAO,CAAC;qBAC1C,EAAE,cAAc,gBAAgB,CAAC,MAAM,CAAC;;yBAEpC,EAAE,oBAAoB,IAAI,CAAC;cACtC,EAAE,oBAAoB,QAAQ,CAAC;WAClC,EAAE,oBAAoB,KAAK,CAAC;uBAChB,EAAE,oBAAoB,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;iBAC5D,EAAE,oBAAoB,WAAW,CAAC;;;AAGnD,EAAE,cAAc,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,IAAM,GAAG,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM;;;uDAGvC,EAAE,cAAc,IAAI,CAAC;;+BAE7C,EAAE,oBAAoB,IAAI,CAAC;;2DAEC,EAAE,cAAc,IAAI,CAAC,SAAS,CAAC;IAExF,OAAO,eAAe;AACxB;AAEA,oEAAoE;AACpE,SAAS,8BAA8B,YAAoB,EAAE,QAAgB;IAC3E,MAAM,uBAA4C;QAChD,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,aAAa;YACX,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,UAAU;YACR,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,eAAe;YACb,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QACA,iCAAiC;YAC/B,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,SAAS,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI,oBAAoB,CAAC,UAAU;IAClG,OAAO;AACT;AAEA,0DAA0D;AAC1D,SAAS,0BAA0B,YAAoB,EAAE,QAAgB,EAAE,IAAY;IACrF,MAAM,gBAAgB,8BAA8B,cAAc;IAElE,MAAM,oBAAoB;QACxB;YACE,MAAM;YACN,UAAU,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE,cAAc,cAAc,CAAC,UAAU,EAAE;YACvH,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAmB;gBAAqB;gBAAmB;aAAiB,EAAE,KAAK,CAAC,GAAG;YAChI,aAAa,CAAC,aAAa,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,wBAAwB,EAAE,SAAS,eAAe,CAAC;QAC1H;QACA;YACE,MAAM;YACN,UAAU,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,6BAA6B,CAAC;YAClG,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAqB;gBAAiB;gBAAyB;aAAe,EAAE,KAAK,CAAC,GAAG;YAClI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,sBAAsB,EAAE,cAAc,IAAI,CAAC,kCAAkC,CAAC;YACzF,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG;YAC/D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAwB;gBAAmB;gBAAgB;aAAsB,EAAE,KAAK,CAAC,GAAG;YACrI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,iBAAiB,EAAE,cAAc,IAAI,CAAC,iDAAiD,CAAC;YACnG,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;YAC5D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAkB;gBAAuB;gBAAkB;aAAmB,EAAE,KAAK,CAAC,GAAG;YAClI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,wBAAwB,EAAE,cAAc,IAAI,CAAC,kCAAkC,CAAC;YAC3F,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG;YAC/D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAoB;gBAAqB;gBAAqB;aAAiB,EAAE,KAAK,CAAC,GAAG;YACnI,aAAa;QACf;KACD;IAED,OAAO,iBAAiB,CAAC,OAAO,kBAAkB,MAAM,CAAC;AAC3D;AAEA,2DAA2D;AAC3D,SAAS,qBAAqB,YAAoB,EAAE,QAAgB,EAAE,IAAY;IAChF,MAAM,gBAAgB,8BAA8B,cAAc;IAElE,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,MAAM;YACN,OAAO,CAAC,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,gDAAgD,CAAC;YAC1G,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,4BAA4B,EAAE,SAAS,2BAA2B,CAAC;gBACpE,CAAC,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,mCAAmC,CAAC;gBACtF,CAAC,mEAAmE,CAAC;aACtE;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,kEAAkE,CAAC;gBACpE,CAAC,mCAAmC,CAAC;gBACrC,CAAC,uDAAuD,CAAC;aAC1D;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,4CAA4C,EAAE,SAAS,cAAc,CAAC;gBACvE,CAAC,KAAK,EAAE,SAAS,sCAAsC,CAAC;gBACxD,CAAC,mDAAmD,EAAE,SAAS,IAAI,CAAC;aACrE;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,wBAAwB,EAAE,SAAS,6BAA6B,CAAC;gBAClE,CAAC,mEAAmE,CAAC;gBACrE,CAAC,uEAAuE,CAAC;aAC1E;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,0CAA0C,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBACpF,CAAC,yDAAyD,CAAC;aAC5D;QACH;KACD;IAED,OAAO,aAAa,CAAC,OAAO,cAAc,MAAM,CAAC;AACnD;AAEA,sCAAsC;AACtC,SAAS,+BAA+B,YAAoB,EAAE,QAAgB,EAAE,IAAY;IAC1F,MAAM,gBAAgB,8BAA8B,cAAc;IAClE,MAAM,kBAAkB,0BAA0B,cAAc,UAAU;IAC1E,MAAM,eAAe,qBAAqB,cAAc,UAAU;IAElE,0DAA0D;IAC1D,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,OAAO,CAAC,aAAa,EAAE,cAAc,IAAI,CAAC,sBAAsB,EAAE,UAAU;YAC5E,UAAU;gBACR,CAAC,yBAAyB,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC;gBACpH,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,cAAc,EAAE,SAAS,sBAAsB,CAAC;gBAC3E,CAAC,mBAAmB,EAAE,SAAS,aAAa,EAAE,cAAc,IAAI,CAAC,eAAe,CAAC;aAClF;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,OAAO,CAAC;YACzE,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,yEAAyE,CAAC;gBAC3E,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,YAAY,CAAC;aAC1F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,sBAAsB,EAAE,cAAc,IAAI,CAAC,UAAU,CAAC;YAC9D,UAAU;gBACR,CAAC,sDAAsD,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBAChG,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,UAAU,CAAC;gBACrE,CAAC,gDAAgD,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;aAC3F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,iBAAiB,EAAE,cAAc,IAAI,CAAC,eAAe,EAAE,UAAU;YACzE,UAAU;gBACR,CAAC,wBAAwB,EAAE,SAAS,6BAA6B,CAAC;gBAClE,CAAC,8BAA8B,EAAE,cAAc,IAAI,CAAC,wBAAwB,CAAC;gBAC7E,CAAC,sDAAsD,EAAE,cAAc,IAAI,CAAC,aAAa,CAAC;aAC3F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,aAAa,CAAC;YAC3D,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,0CAA0C,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBACpF,CAAC,yDAAyD,CAAC;aAC5D;QACH;KACD;IAED,MAAM,gBAAgB,aAAa,CAAC,OAAO,cAAc,MAAM,CAAC;IAEhE,OAAO;QACL,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,iBAAiB,GAAG,cAAc,IAAI,CAAC,CAAC,EAAE,gBAAgB,IAAI,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE,MAAM;QAC7F,cAAc,CAAC,cAAc,YAAY,IAAI;YAAC;YAAwB;YAAmB;SAAoB,EAAE,KAAK,CAAC,GAAG;QACxH,iBAAiB,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;QACvE,gBAAgB,cAAc,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;IACvE;AACF;AAEA,0CAA0C;AAC1C,SAAS;IACP,MAAM,QAAQ,IAAI,OAAO,QAAQ;IACjC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO;IACtC,OAAO;AACT;AAEA,SAAS;IACP,MAAM,OAAO,IAAI,OAAO,QAAQ;IAChC,IAAI,QAAQ,KAAK,OAAO,IAAI,OAAO;IACnC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,OAAO;AACT;AAEA,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,SAAS;QACb;YAAE,OAAO,GAAG,aAAa,kBAAkB,CAAC;YAAE,UAAU;YAAY,WAAW;QAAO;QACtF;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAO;QAC7E;YAAE,OAAO;YAA0B,UAAU;YAAc,WAAW;QAAS;QAC/E;YAAE,OAAO;YAAoC,UAAU;YAAY,WAAW;QAAO;QACrF;YAAE,OAAO;YAAkC,UAAU;YAAU,WAAW;QAAS;KACpF;IACD,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB;AAEA,SAAS,uBAAuB,QAAgB;IAC9C,0DAA0D;IAC1D,MAAM,SAAS;IACf,MAAM,WAAW;QACf,UAAU;YAAE,WAAW;YAAwB,iBAAiB;YAAwC,uBAAuB;QAAuC;QACtK,UAAU;YAAE,WAAW;YAAqB,iBAAiB;YAAmC,uBAAuB;QAAqD;QAC5K,QAAQ;YAAE,WAAW;YAAuB,iBAAiB;YAAkC,uBAAuB;QAAsC;QAC5J,UAAU;YAAE,WAAW;YAAyB,iBAAiB;YAAsC,uBAAuB;QAAuC;IACvK;IAEA,OAAO;QACL,aAAa;QACb,WAAW,QAAQ,CAAC,OAAgC,CAAC,SAAS;QAC9D,iBAAiB,QAAQ,CAAC,OAAgC,CAAC,eAAe;QAC1E,uBAAuB,QAAQ,CAAC,OAAgC,CAAC,qBAAqB;IACxF;AACF;AAEA,SAAS,2BAA2B,YAAoB,EAAE,QAAgB;IACxE,MAAM,gBAAgB;QACpB;YAAE,MAAM,GAAG,SAAS,cAAc,CAAC;YAAE,OAAO;YAA2B,WAAW;QAAa;QAC/F;YAAE,MAAM,GAAG,aAAa,kBAAkB,CAAC;YAAE,OAAO;YAAqB,WAAW;QAAW;QAC/F;YAAE,MAAM;YAA6B,OAAO;YAAoB,WAAW;QAAY;KACxF;IACD,OAAO,cAAc,KAAK,CAAC,GAAG;AAChC;AAEA,6DAA6D;AAC7D,MAAM,SACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,gCAAgC;AAE9C,IAAI,CAAC,QAAQ,CACb;AAEA,6DAA6D;AAC7D,MAAM,KAAK,IAAI,gKAAA,CAAA,qBAAkB,CAAC;AAElC,+CAA+C;AAC/C,MAAM,iBAAiB;AAKhB,eAAe,sBAAsB,KAe3C;IACC,IAAI;QACF,yDAAyD;QACzD,MAAM,kBAAmC;YACvC,cAAc,MAAM,YAAY;YAChC,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;YACpC,YAAY,MAAM,WAAW;YAC7B,qBAAqB;gBAAC,MAAM,qBAAqB,IAAI;aAAkB;YACvE,aAAa,EAAE;QACjB;QAEA,kDAAkD;QAClD,MAAM,kBAAkB,MAAM,+IAAA,CAAA,2BAAwB,CAAC,uBAAuB,CAC5E,iBACA,MAAM,QAAQ,EACd;QAGF,kDAAkD;QAClD,MAAM,sBAAsB,MAAM,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;YACxE,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC;QAEA,oDAAoD;QACpD,MAAM,sBAAsB,iJAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAChE,iBACA;QAGF,iFAAiF;QACjF,MAAM,WAAW,gBAAgB,QAAQ;QAEzC,iEAAiE;QACjE,MAAM,kBAAkB,MAAM,sBAAsB,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ;QAEtG,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,6JAAA,CAAA,gBAAa,CAAC,4BAA4B,CAC7D,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,iBAAiB,MAAM,WAAW,EAC1C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,kBAAkB,MAAM,YAAY,IAAI,WAChD,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,UAC9C,OAAO,CAAC,oBAAoB,MAAM,cAAc,EAChD,OAAO,CAAC,cAAc,MAAM,QAAQ,IAAI,IACxC,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,IAC9C,OAAO,CAAC,2BAA2B,MAAM,qBAAqB,IAAI,IAClE,OAAO,CAAC,mBAAmB,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS;QAGhE,oEAAoE;QAEpE,+DAA+D;QAC/D,MAAM,gBAAgB,8BAA8B,MAAM,YAAY,EAAE,MAAM,QAAQ;QACtF,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG;QAC/D,MAAM,yBAAyB,+BAA+B,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,aAAa;QAG/G,sDAAsD;QAEtD,MAAM,kBAAkB;YACtB,YAAY;YACZ,WAAW,MAAM,WAAW;YAC5B,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC;QAEA,mEAAmE;QACnE,MAAM,cAAc,sIAAA,CAAA,0BAAuB,CAAC,+BAA+B,CACzE,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,YAAY,yCAAyC;;QAIvD,wEAAwE;QAExE,MAAM,mBAAmB,MAAM,CAAA,GAAA,sIAAA,CAAA,mCAAgC,AAAD,EAC5D,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,aACA,qBACA;QAGF,MAAM,sBAAsB,MAAM,CAAA,GAAA,sIAAA,CAAA,sCAAmC,AAAD,EAClE,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,iBAAiB,QAAQ,EACzB,aACA,qBACA;QAIF,wDAAwD;QAExD,MAAM,kBAAkB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD,EAC1D,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,aACA,qBACA;QAIF,mDAAmD;QAEnD,mDAAmD;QAEnD,qDAAqD;QAErD,MAAM,eAAe;YACnB,SAAS,gBAAgB,OAAO;YAChC,UAAU,iBAAiB,QAAQ;YACnC,aAAa,oBAAoB,WAAW;YAC5C,cAAc,gBAAgB,YAAY;YAC1C,UAAU;YACV,aAAa,iBAAiB,QAAQ;YACtC,iBAAiB,YAAY,QAAQ;YACrC,mBAAmB,YAAY,iBAAiB;YAChD,qBAAqB,YAAY,mBAAmB;YACpD,kBAAkB,YAAY,gBAAgB;YAC9C,UAAU,MAAM,QAAQ;YACxB,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,iBAAiB;YACjB,iBAAiB;gBACf,OAAO,iBAAiB,QAAQ;gBAChC,MAAM,iBAAiB,eAAe;gBACtC,WAAW,oBAAoB,SAAS;gBACxC,kBAAkB;gBAClB,WAAW;YACb;YACA,gCAAgC;YAChC,sBAAsB;gBACpB,aAAa,YAAY,QAAQ,CAAC,IAAI;gBACtC,mBAAmB,YAAY,iBAAiB;gBAChD,qBAAqB,YAAY,mBAAmB;gBACpD,oBAAoB,YAAY,kBAAkB;gBAClD,kBAAkB,YAAY,gBAAgB;gBAC9C,gBAAgB,YAAY,cAAc;YAC5C;YACA,UAAU;gBAAC;oBACT,UAAU,MAAM,QAAQ;oBACxB,aAAa;oBACb,UAAU,GAAG,+BAA+B;gBAC9C;aAAE;YACF,aAAa,IAAI,OAAO,WAAW;QACrC;QAGA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,qBAAqB,KAS1C;IACC,IAAI;QAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,qCAAqC;QACrC,MAAM,eAAe,CAAC,qCAAqC,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;;;;;;;;;YAS/F,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;YAC5C,EAAE,MAAM,QAAQ,CAAC;SACpB,EAAE,MAAM,WAAW,CAAC;kBACX,EAAE,MAAM,YAAY,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,aAAa,EAAE,MAAM,eAAe,CAAC;WAC9F,EAAE,MAAM,SAAS,CAAC;;;;;;;;;;;kGAWqE,CAAC;QAG/F,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YACzC,6JAAA,CAAA,gBAAa,CAAC,oBAAoB;YAClC;SACD;QAED,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,SAAS,SAAS,IAAI;QAG5B,OAAO;YACL,QAAQ,OAAO,IAAI;YACnB,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,oBAAoB,KAiBzC;IACC,IAAI;QAEF,wDAAwD;QACxD,IAAI,4BAA4B;QAChC,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,oBAAoB,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAC9C,MAAM,iBAAiB,EACvB,MAAM,YAAY,EAClB,MAAM,QAAQ,IAAI,UAClB,MAAM,eAAe;YAGvB,4BAA4B,CAAC;;AAEnC,EAAE,kBAAkB,cAAc,CAAC;;uBAEZ,EAAE,kBAAkB,WAAW,CAAC;kCACrB,EAAE,kBAAkB,gBAAgB,CAAC,IAAI,CAAC,MAAM;4BACtD,EAAE,MAAM,eAAe,CAAC,gBAAgB,EAAE,mBAAmB,MAAM,GAAG,GAAG,KAAK,SAAS,0BAA0B;uBACtH,EAAE,MAAM,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,eAAe,CAAC,KAAK,CAAC;oBACpE,EAAE,MAAM,eAAe,CAAC,SAAS,CAAC;;;;;;;;;;AAUtD,CAAC;QAEG;QAEA,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,6JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,4CAA4C;QAC5C,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM,QAAQ,EAAE,GAAG;QACnE,MAAM,cAAc,CAAC,SAAS,EAAE,MAAM,YAAY,CAAC,yBAAyB,EAAE,MAAM,WAAW,IAAI,UAAU,8BAA8B,EAAE,MAAM,eAAe,IAAI,UAAU,iBAAiB,CAAC;QAClM,MAAM,kBAAkB,MAAM,WAAW,GACvC,4FACA;QAEF,oDAAoD;QACpD,MAAM,mBAAmB,EAAE;QAC3B,IAAI,MAAM,QAAQ,EAAE,iBAAiB,IAAI,CAAC,CAAC,oCAAoC,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC;QAClG,IAAI,MAAM,WAAW,EAAE,iBAAiB,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM,WAAW,CAAC,CAAC,CAAC;QACrG,IAAI,MAAM,YAAY,EAAE,iBAAiB,IAAI,CAAC,CAAC,8EAA8E,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC;QAEpJ,qDAAqD;QACrD,MAAM,kBAAkB,MAAM,YAAY,GAAG,CAAC;;;WAGvC,EAAE,MAAM,YAAY,CAAC;;;;;;;;;;;IAW5B,CAAC,GAAG;QAGJ,+BAA+B;QAC/B,MAAM,oBAAoB,qBAAqB,MAAM,YAAY;QACjE,MAAM,uBAAuB,wBAAwB,MAAM,QAAQ;QACnE,MAAM,sBAAsB,4BAA4B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,UAAU,MAAM,WAAW;QACzH,MAAM,qBAAqB,sBAAsB,8BAA8B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,YAAY;QACjI,MAAM,kBAAkB,wBAAwB,MAAM,QAAQ,IAAI;QAGlE,uEAAuE;QACvE,MAAM,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG;QACrE,MAAM,aAAa,mBAAmB;QACtC,MAAM,mBAAmB,yBAAyB;QAElD,0DAA0D;QAC1D,MAAM,gBAAgB,8BAA8B,MAAM,YAAY;QACtE,MAAM,sBAAsB,+BAA+B,MAAM,YAAY,EAAE,iBAAiB,KAAK,EAAE;QAGvG,IAAI,cAAc,CAAC,YAAY,EAAE,iBAAiB,KAAK,CAAC,WAAW,GAAG,yBAAyB,EAAE,MAAM,YAAY,CAAC;;;YAG5G,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;YAC5C,EAAE,MAAM,QAAQ,CAAC;WAClB,EAAE,MAAM,SAAS,CAAC;YACjB,EAAE,MAAM,QAAQ,IAAI,SAAS;;AAEzC,EAAE,gBAAgB;;;AAGlB,EAAE,iBAAiB,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;;;;+BAIxB,EAAE,iBAAiB,KAAK,CAAC;;;;;;;EAOtD,EAAE,kBAAkB;EACpB,EAAE,qBAAqB;iCACQ,EAAE,iBAAiB,WAAW,CAAC;;;;;EAK9D,EAAE,gBAAgB;;;;;;;SAOX,EAAE,iBAAiB,KAAK,CAAC;UACxB,EAAE,iBAAiB,MAAM,CAAC;eACrB,EAAE,iBAAiB,WAAW,CAAC;QACtC,EAAE,iBAAiB,IAAI,CAAC;YACpB,EAAE,iBAAiB,QAAQ,CAAC;;;yEAGiC,EAAE,iBAAiB,KAAK,CAAC;;;;;;;;;;;;;;;;;yCAiBzD,EAAE,iBAAiB,KAAK,CAAC;;;;;;;;;;;;kBAYhD,EAAE,iBAAiB,KAAK,CAAC,WAAW,GAAG,+RAA+R,CAAC;QAErV,yDAAyD;QACzD,cAAc,sCAAsC,aAAa,MAAM,YAAY,EAAE,iBAAiB,KAAK,EAAE;QAE7G,6EAA6E;QAC7E,cAAc,yBAAyB,aAAa;QACpD,cAAc,wBAAwB,aAAa;QACnD,cAAc,uBAAuB,aAAa;QAGlD,IAAI,MAAM,eAAe,EAAE,CAC3B;QAEA,wDAAwD;QACxD,MAAM,kBAAkB;YACtB;YACA;SACD;QAED,oDAAoD;QACpD,IAAI,MAAM,WAAW,EAAE;YAErB,0DAA0D;YAC1D,MAAM,YAAY,MAAM,WAAW,CAAC,KAAK,CAAC;YAC1C,IAAI,WAAW;gBACb,MAAM,GAAG,UAAU,WAAW,GAAG;gBAEjC,gBAAgB,IAAI,CAAC;oBACnB,YAAY;wBACV,MAAM;wBACN,UAAU;oBACZ;gBACF;gBAEA,mDAAmD;gBACnD,MAAM,aAAa,CAAC,6MAA6M,CAAC;gBAClO,eAAe,CAAC,EAAE,GAAG,cAAc;YACrC,OAAO,CACP;QACF;QAEA,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAE3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,0CAA0C;QAC1C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;gBACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;gBACzC,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;gBACjD;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,sDAAsD;YACtD,MAAM,eAAe,SAAS,IAAI;YAClC,MAAM,IAAI,MAAM;QAClB;QAGA,OAAO;YACL,UAAU;YACV,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACjH;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,OAAO;YACL,SAAS;YACT,OAAO;YACP,UAAU,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG,MAAM;YAC7C,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO;YACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAKO,SAAS;IACd,OAAO;QACL,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP,mBAAmB;YACnB,kBAAkB;YAClB,MAAM;QACR;QACA,aAAa;IACf;AACF;AAEA,8DAA8D;AAC9D,SAAS,6BAA6B,QAAgB;IACpD,MAAM,mBAAwC;QAC5C,SAAS;YACP,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAS;aAAa;YAC1D,eAAe;gBAAC;gBAAY;gBAAS;gBAAQ;aAAW;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAe;gBAAa;aAAgB;QACjE;QACA,WAAW;YACT,iBAAiB;YACjB,eAAe;gBAAC;gBAAO;gBAAQ;gBAAoB;aAAU;YAC7D,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAU;YACvD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAc;gBAAc;aAAc;QAC/D;QACA,gBAAgB;YACd,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAM;aAAO;YACjD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAU;gBAAmB;aAAiB;QACnE;QACA,SAAS;YACP,iBAAiB;YACjB,eAAe;gBAAC;gBAAW;gBAAW;gBAAO;aAAQ;YACrD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAU;YACvD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAsB;aAAmB;QACzE;QACA,UAAU;YACR,iBAAiB;YACjB,eAAe;gBAAC;gBAAY;gBAAU;gBAAQ;aAAM;YACpD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAoB;aAAkB;QACtE;QACA,YAAY;YACV,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAS;aAAQ;YACrD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAW;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAkB;gBAAmB;aAAiB;QAC3E;QACA,YAAY;YACV,iBAAiB;YACjB,eAAe;gBAAC;gBAAS;gBAAe;gBAAS;aAAiB;YAClE,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAkB;gBAAkB;aAAiB;QAC1E;QACA,UAAU;YACR,iBAAiB;YACjB,eAAe;gBAAC;gBAAY;gBAAW;gBAAW;aAAa;YAC/D,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAW;aAA2B;QACtE;QACA,WAAW;YACT,iBAAiB;YACjB,eAAe;gBAAC;gBAAS;gBAAa;gBAAW;aAAQ;YACzD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAmB;gBAAmB;aAAkB;QAC7E;IACF;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,kBAAmB;QAC7D,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU;AACpC;AAEA,wDAAwD;AACxD,SAAS,wBAAwB,QAAgB,EAAE,YAAoB;IACrE,MAAM,SAAS;IACf,MAAM,cAAmC;QACvC,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,QAAQ;YACN,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;IACF;IAEA,yCAAyC;IACzC,MAAM,0BAA+C;QACnD,cAAc;YACZ,cAAc,GAAG,OAAO,gCAAgC,CAAC;YACzD,mBAAmB,GAAG,WAAW,WAAW,sCAAsC,WAAW,WAAW,uBAAuB,wBAAwB;YACvJ,kBAAkB,GAAG,WAAW,WAAW,sCAAsC,2CAA2C;QAC9H;QACA,WAAW;YACT,oBAAoB,GAAG,WAAW,WAAW,0CAA0C,WAAW,WAAW,sCAAsC,6BAA6B;YAChL,mBAAmB,GAAG,WAAW,WAAW,uCAAuC,6CAA6C;YAChI,kBAAkB,GAAG,WAAW,WAAW,6BAA6B,yBAAyB;QACnG;QACA,UAAU;YACR,kBAAkB,GAAG,OAAO,+BAA+B,CAAC;YAC5D,mBAAmB,GAAG,WAAW,WAAW,oCAAoC,WAAW,WAAW,mCAAmC,uBAAuB;YAChK,kBAAkB,GAAG,WAAW,WAAW,sCAAsC,yCAAyC;QAC5H;QACA,WAAW;YACT,eAAe,GAAG,OAAO,6CAA6C,CAAC;YACvE,mBAAmB,GAAG,WAAW,WAAW,kCAAkC,kCAAkC;YAChH,kBAAkB,GAAG,OAAO,4CAA4C,CAAC;QAC3E;IACF;IAEA,MAAM,cAAc,WAAW,CAAC,OAAmC;IACnE,MAAM,kBAAkB,uBAAuB,CAAC,aAAa,WAAW,GAAG,IAAI,uBAAuB,CAAC,UAAU;IAEjH,OAAO;QACL,QAAQ;QACR,gBAAgB,YAAY,cAAc;QAC1C,sBAAsB,YAAY,oBAAoB;QACtD,qBAAqB,YAAY,mBAAmB;QACpD,kBAAkB,YAAY,gBAAgB;QAC9C,kBAAkB;QAClB,gBAAgB,CAAC,SAAS,EAAE,OAAO,WAAW,GAAG,mBAAmB,EAAE,aAAa,gBAAgB,CAAC;IACtG;AACF;AAEA,2FAA2F;AAC3F,SAAS,uBAAuB,YAAoB,EAAE,QAAgB,EAAE,QAAgB;IACtF,MAAM,iBAAwC;QAC5C,aAAa;YACX;gBAAE,OAAO;gBAA8B,UAAU;gBAAY,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA8B,UAAU;gBAAW,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAA8B,UAAU;gBAAU,WAAW;YAAS;SAChF;QACD,YAAY;YACV;gBAAE,OAAO;gBAAkC,UAAU;gBAAY,WAAW;YAAO;YACnF;gBAAE,OAAO;gBAA+B,UAAU;gBAAW,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA+B,UAAU;gBAAgB,WAAW;YAAS;SACvF;QACD,YAAY;YACV;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAA6B,UAAU;gBAAa,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA2B,UAAU;gBAAW,WAAW;YAAS;SAC9E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAA4B,UAAU;gBAAW,WAAW;YAAO;YAC5E;gBAAE,OAAO;gBAAqB,UAAU;gBAAc,WAAW;YAAS;SAC3E;IACH;IAEA,MAAM,iBAAwC;QAC5C,cAAc;YACZ;gBAAE,OAAO;gBAA6B,UAAU;gBAAY,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAAgC,UAAU;gBAAU,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA6B,UAAU;gBAAc,WAAW;YAAS;SACnF;QACD,cAAc;YACZ;gBAAE,OAAO;gBAA4B,UAAU;gBAAY,WAAW;YAAO;YAC7E;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAAyB,UAAU;gBAAa,WAAW;YAAS;SAC9E;QACD,cAAc;YACZ;gBAAE,OAAO;gBAAuB,UAAU;gBAAY,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAAyB,UAAU;gBAAY,WAAW;YAAO;YAC1E;gBAAE,OAAO;gBAA2B,UAAU;gBAAU,WAAW;YAAS;SAC7E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAuB,UAAU;gBAAY,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAA+B,UAAU;gBAAY,WAAW;YAAO;YAChF;gBAAE,OAAO;gBAAgC,UAAU;gBAAc,WAAW;YAAS;SACtF;QACD,WAAW;YACT;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAAsB,UAAU;gBAAa,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAA4B,UAAU;gBAAU,WAAW;YAAS;SAC9E;QACD,aAAa;YACX;gBAAE,OAAO;gBAA6B,UAAU;gBAAY,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAA4B,UAAU;gBAAY,WAAW;YAAO;YAC7E;gBAAE,OAAO;gBAA0B,UAAU;gBAAc,WAAW;YAAS;SAChF;QACD,UAAU;YACR;gBAAE,OAAO;gBAAqB,UAAU;gBAAY,WAAW;YAAO;YACtE;gBAAE,OAAO;gBAAwB,UAAU;gBAAY,WAAW;YAAO;YACzE;gBAAE,OAAO;gBAAwB,UAAU;gBAAU,WAAW;YAAS;SAC1E;QACD,eAAe;YACb;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAA0B,UAAU;gBAAU,WAAW;YAAO;YACzE;gBAAE,OAAO;gBAA4B,UAAU;gBAAU,WAAW;YAAS;SAC9E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAAoC,UAAU;gBAAY,WAAW;YAAO;YACrF;gBAAE,OAAO;gBAAyB,UAAU;gBAAa,WAAW;YAAS;SAC9E;IACH;IAEA,MAAM,mBAAmB,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,YAAY;IAChF,MAAM,mBAAmB,cAAc,CAAC,aAAa,WAAW,GAAG,IAAI,cAAc,CAAC,UAAU;IAChG,MAAM,cAAc;QAClB;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAO;QAC7E;YAAE,OAAO,GAAG,SAAS,sBAAsB,CAAC;YAAE,UAAU;YAAa,WAAW;QAAO;QACvF;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAS;KAChF;IAED,OAAO;WAAI;WAAqB;WAAqB;KAAY,CAAC,KAAK,CAAC,GAAG;AAC7E;AAEA,gDAAgD;AAChD,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,eAAe;QACnB;YACE,MAAM;YACN,UAAU,GAAG,SAAS,4BAA4B,CAAC;YACnD,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,aAAa,0BAA0B,EAAE,UAAU;YAChE,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,SAAS,8BAA8B,CAAC;YACrD,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,SAAS,yCAAyC,CAAC;YAChE,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;KACD;IAED,OAAO,aAAa,KAAK,CAAC,GAAG;AAC/B", "debugId": null}}, {"offset": {"line": 8640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/advanced-content/route.ts"], "sourcesContent": ["/**\r\n * Advanced Content Generation API\r\n * Tests the new advanced content generation system\r\n */\r\n\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { generateRevo10Content } from '@/ai/revo-1.0-service';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    \r\n    \r\n    // Default test data if not provided\r\n    const testData = {\r\n      businessType: body.businessType || 'restaurant',\r\n      businessName: body.businessName || 'Bella Vista Restaurant',\r\n      location: body.location || 'New York, NY',\r\n      platform: body.platform || 'instagram',\r\n      writingTone: body.writingTone || 'friendly',\r\n      contentThemes: body.contentThemes || ['food', 'dining', 'experience'],\r\n      targetAudience: body.targetAudience || 'food lovers and families',\r\n      services: body.services || 'Fine dining, catering, private events',\r\n      keyFeatures: body.keyFeatures || 'Fresh ingredients, authentic recipes, cozy atmosphere',\r\n      competitiveAdvantages: body.competitiveAdvantages || 'Family-owned, locally sourced, 20+ years experience',\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString(),\r\n      primaryColor: body.primaryColor || '#D97706',\r\n      visualStyle: body.visualStyle || 'warm and inviting',\r\n      ...body\r\n    };\r\n\r\n\r\n    // Generate advanced content\r\n    const result = await generateRevo10Content(testData);\r\n\r\n\r\n    // Return enhanced response with analysis\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: {\r\n        // Core content\r\n        headline: result.headline,\r\n        subheadline: result.subheadline,\r\n        caption: result.content,\r\n        cta: result.callToAction,\r\n        hashtags: result.hashtags,\r\n        \r\n        // Advanced intelligence insights\r\n        intelligence: result.businessIntelligence,\r\n        \r\n        // Generation metadata\r\n        metadata: {\r\n          businessName: testData.businessName,\r\n          businessType: testData.businessType,\r\n          location: testData.location,\r\n          platform: testData.platform,\r\n          generatedAt: new Date().toISOString(),\r\n          model: 'Revo 1.0 Enhanced',\r\n          aiService: 'gemini-2.5-flash-image-preview'\r\n        },\r\n\r\n        // Performance insights\r\n        performance: {\r\n          hashtagCount: result.hashtags.length,\r\n          captionLength: result.content.length,\r\n          headlineLength: result.headline?.length || 0,\r\n          trendingKeywords: result.businessIntelligence?.trendingKeywords?.length || 0,\r\n          recommendations: result.businessIntelligence?.performanceRecommendations?.length || 0\r\n        },\r\n\r\n        // Quality metrics\r\n        quality: {\r\n          hasHeadline: !!result.headline,\r\n          hasSubheadline: !!result.subheadline,\r\n          hasCTA: !!result.callToAction,\r\n          hashtagOptimized: result.hashtags.length >= 5 && result.hashtags.length <= 15,\r\n          captionOptimized: result.content.length >= 50 && result.content.length <= 500,\r\n          trendingIntegrated: (result.businessIntelligence?.trendingKeywords?.length || 0) > 0,\r\n          performanceAnalyzed: (result.businessIntelligence?.performanceRecommendations?.length || 0) > 0\r\n        }\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    \r\n    return NextResponse.json({\r\n      success: false,\r\n      error: 'Failed to generate advanced content',\r\n      message: error instanceof Error ? error.message : 'Unknown error',\r\n      timestamp: new Date().toISOString()\r\n    }, { status: 500 });\r\n  }\r\n}\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const businessType = searchParams.get('businessType') || 'restaurant';\r\n    const platform = searchParams.get('platform') || 'instagram';\r\n    const location = searchParams.get('location') || 'New York, NY';\r\n\r\n    // Quick test generation\r\n    const testData = {\r\n      businessType,\r\n      businessName: `Test ${businessType.charAt(0).toUpperCase() + businessType.slice(1)}`,\r\n      location,\r\n      platform,\r\n      writingTone: 'professional',\r\n      contentThemes: ['quality', 'service', 'experience'],\r\n      targetAudience: 'local customers',\r\n      services: 'Professional services',\r\n      keyFeatures: 'Quality and reliability',\r\n      competitiveAdvantages: 'Local expertise',\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString(),\r\n      primaryColor: '#3B82F6',\r\n      visualStyle: 'modern'\r\n    };\r\n\r\n    const result = await generateRevo10Content(testData);\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Advanced content generation test successful',\r\n      data: {\r\n        headline: result.headline,\r\n        caption: result.content.substring(0, 200) + '...',\r\n        hashtagCount: result.hashtags.length,\r\n        trendingKeywords: result.businessIntelligence?.trendingKeywords?.slice(0, 5) || [],\r\n        recommendations: result.businessIntelligence?.performanceRecommendations?.slice(0, 3) || []\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    \r\n    return NextResponse.json({\r\n      success: false,\r\n      error: 'Failed to test advanced content generation',\r\n      message: error instanceof Error ? error.message : 'Unknown error'\r\n    }, { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAG/B,oCAAoC;QACpC,MAAM,WAAW;YACf,cAAc,KAAK,YAAY,IAAI;YACnC,cAAc,KAAK,YAAY,IAAI;YACnC,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,aAAa,KAAK,WAAW,IAAI;YACjC,eAAe,KAAK,aAAa,IAAI;gBAAC;gBAAQ;gBAAU;aAAa;YACrE,gBAAgB,KAAK,cAAc,IAAI;YACvC,UAAU,KAAK,QAAQ,IAAI;YAC3B,aAAa,KAAK,WAAW,IAAI;YACjC,uBAAuB,KAAK,qBAAqB,IAAI;YACrD,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,aAAa,IAAI,OAAO,kBAAkB;YAC1C,cAAc,KAAK,YAAY,IAAI;YACnC,aAAa,KAAK,WAAW,IAAI;YACjC,GAAG,IAAI;QACT;QAGA,4BAA4B;QAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;QAG3C,yCAAyC;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,eAAe;gBACf,UAAU,OAAO,QAAQ;gBACzB,aAAa,OAAO,WAAW;gBAC/B,SAAS,OAAO,OAAO;gBACvB,KAAK,OAAO,YAAY;gBACxB,UAAU,OAAO,QAAQ;gBAEzB,iCAAiC;gBACjC,cAAc,OAAO,oBAAoB;gBAEzC,sBAAsB;gBACtB,UAAU;oBACR,cAAc,SAAS,YAAY;oBACnC,cAAc,SAAS,YAAY;oBACnC,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,aAAa,IAAI,OAAO,WAAW;oBACnC,OAAO;oBACP,WAAW;gBACb;gBAEA,uBAAuB;gBACvB,aAAa;oBACX,cAAc,OAAO,QAAQ,CAAC,MAAM;oBACpC,eAAe,OAAO,OAAO,CAAC,MAAM;oBACpC,gBAAgB,OAAO,QAAQ,EAAE,UAAU;oBAC3C,kBAAkB,OAAO,oBAAoB,EAAE,kBAAkB,UAAU;oBAC3E,iBAAiB,OAAO,oBAAoB,EAAE,4BAA4B,UAAU;gBACtF;gBAEA,kBAAkB;gBAClB,SAAS;oBACP,aAAa,CAAC,CAAC,OAAO,QAAQ;oBAC9B,gBAAgB,CAAC,CAAC,OAAO,WAAW;oBACpC,QAAQ,CAAC,CAAC,OAAO,YAAY;oBAC7B,kBAAkB,OAAO,QAAQ,CAAC,MAAM,IAAI,KAAK,OAAO,QAAQ,CAAC,MAAM,IAAI;oBAC3E,kBAAkB,OAAO,OAAO,CAAC,MAAM,IAAI,MAAM,OAAO,OAAO,CAAC,MAAM,IAAI;oBAC1E,oBAAoB,CAAC,OAAO,oBAAoB,EAAE,kBAAkB,UAAU,CAAC,IAAI;oBACnF,qBAAqB,CAAC,OAAO,oBAAoB,EAAE,4BAA4B,UAAU,CAAC,IAAI;gBAChG;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,WAAW,IAAI,OAAO,WAAW;QACnC,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,eAAe,aAAa,GAAG,CAAC,mBAAmB;QACzD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,wBAAwB;QACxB,MAAM,WAAW;YACf;YACA,cAAc,CAAC,KAAK,EAAE,aAAa,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,KAAK,CAAC,IAAI;YACpF;YACA;YACA,aAAa;YACb,eAAe;gBAAC;gBAAW;gBAAW;aAAa;YACnD,gBAAgB;YAChB,UAAU;YACV,aAAa;YACb,uBAAuB;YACvB,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,aAAa,IAAI,OAAO,kBAAkB;YAC1C,cAAc;YACd,aAAa;QACf;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,UAAU,OAAO,QAAQ;gBACzB,SAAS,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;gBAC5C,cAAc,OAAO,QAAQ,CAAC,MAAM;gBACpC,kBAAkB,OAAO,oBAAoB,EAAE,kBAAkB,MAAM,GAAG,MAAM,EAAE;gBAClF,iBAAiB,OAAO,oBAAoB,EAAE,4BAA4B,MAAM,GAAG,MAAM,EAAE;YAC7F;QACF;IAEF,EAAE,OAAO,OAAO;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}