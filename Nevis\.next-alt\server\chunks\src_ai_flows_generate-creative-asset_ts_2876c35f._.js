module.exports = {

"[project]/src/ai/flows/generate-creative-asset.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/_b7e24e37._.js",
  "server/chunks/node_modules_next_dist_7f047d35._.js",
  "server/chunks/node_modules_@genkit-ai_core_lib_2c6c2150._.js",
  "server/chunks/node_modules_zod_lib_ae7c5ef1._.js",
  "server/chunks/node_modules_ajv_dist_4699a16a._.js",
  "server/chunks/node_modules_zod-to-json-schema_dist_e510fa2f._.js",
  "server/chunks/node_modules_@opentelemetry_core_build_esm_9067dde5._.js",
  "server/chunks/node_modules_@opentelemetry_semantic-conventions_build_esm_0a0e9d1f._.js",
  "server/chunks/node_modules_@opentelemetry_resources_build_esm_53b99a33._.js",
  "server/chunks/node_modules_@opentelemetry_sdk-metrics_build_esm_36d82ede._.js",
  "server/chunks/node_modules_@opentelemetry_sdk-trace-base_build_esm_2004cd04._.js",
  "server/chunks/node_modules_protobufjs_eb75e312._.js",
  "server/chunks/node_modules_@opentelemetry_otlp-transformer_build_esm_8f2a63ec._.js",
  "server/chunks/node_modules_@grpc_grpc-js_eb53fed0._.js",
  "server/chunks/node_modules_handlebars_d645c47f._.js",
  "server/chunks/node_modules_yaml_dist_04fd9803._.js",
  "server/chunks/node_modules_@genkit-ai_ai_lib_d9ad1ab4._.js",
  "server/chunks/node_modules_e0df0c7f._.js",
  "server/chunks/[root-of-the-server]__cee8c1ba._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/flows/generate-creative-asset.ts [app-route] (ecmascript)");
    });
});
}}),

};