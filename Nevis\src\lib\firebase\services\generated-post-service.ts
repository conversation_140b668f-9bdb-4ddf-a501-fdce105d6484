// Generated Posts Firebase service
import { query, where, orderBy, limit, getDocs, collection } from 'firebase/firestore';
import { db } from '../config';
import { DatabaseService } from '../database';
import { COLLECTIONS, GeneratedPostDocument, GeneratedPostDocumentSchema } from '../schema';
import type { GeneratedPost, Platform } from '@/lib/types';

export class GeneratedPostService extends DatabaseService<GeneratedPostDocument> {
  constructor() {
    super(COLLECTIONS.GENERATED_POSTS);
  }

  // Convert from app GeneratedPost to Firestore document
  private toFirestoreDocument(
    post: GeneratedPost,
    userId: string,
    brandProfileId: string
  ): Omit<GeneratedPostDocument, 'id' | 'createdAt' | 'updatedAt'> {
    // Helper function to clean undefined values and flatten nested objects
    const cleanValue = (value: any, defaultValue: any = '') => {
      return value !== undefined && value !== null ? value : defaultValue;
    };

    // Helper function to handle base64 and long strings for Firestore
    const sanitizeForFirestore = (value: any): string => {
      if (value === null || value === undefined) return '';
      if (typeof value === 'string') {
        // Handle base64 data URLs - they're too long for Firestore
        if (value.startsWith('data:image/') && value.includes('base64,')) {
          // Extract just the format info, not the actual base64 data
          const formatMatch = value.match(/data:(image\/[^;]+)/);
          return formatMatch ? `[Base64 ${formatMatch[1]} image]` : '[Base64 image]';
        }
        // Limit string length to prevent Firestore issues
        return value.length > 1000 ? value.substring(0, 1000) + '...[truncated]' : value;
      }
      if (typeof value === 'number' || typeof value === 'boolean') return String(value);
      // Convert everything else to JSON string to ensure no nested entities
      return JSON.stringify(value);
    };

    // Helper function to ensure valid platform enum value
    const validatePlatform = (platform: string): GeneratedPostDocument['platform'] => {
      const validPlatforms = ['instagram', 'facebook', 'twitter', 'linkedin', 'tiktok'] as const;
      const normalizedPlatform = platform?.toLowerCase();
      return validPlatforms.includes(normalizedPlatform as any) ? normalizedPlatform as GeneratedPostDocument['platform'] : 'instagram';
    };

    // Helper function to ensure valid postType enum value
    const validatePostType = (postType: string): GeneratedPostDocument['postType'] => {
      const validPostTypes = ['post', 'story', 'reel', 'advertisement'] as const;
      return validPostTypes.includes(postType as any) ? postType as GeneratedPostDocument['postType'] : 'post';
    };

    // Helper function to clamp analytics values between 0-100
    const clampAnalyticsValue = (value: any, defaultValue: number): number => {
      const numValue = typeof value === 'number' ? value : defaultValue;
      return Math.min(100, Math.max(0, numValue));
    };

    // Handle hashtags - convert string to array if needed
    const hashtags = Array.isArray(post.hashtags)
      ? post.hashtags.filter(tag => tag && typeof tag === 'string')
      : typeof post.hashtags === 'string'
        ? post.hashtags.split(' ').filter(tag => tag.startsWith('#'))
        : [];

    // Helper function to remove undefined values from objects
    const removeUndefined = (obj: any): any => {
      const cleaned: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
          cleaned[key] = value;
        }
      }
      return cleaned;
    };

    // Build metadata object and remove undefined values
    const metadata = removeUndefined({
      businessType: post.businessType ? sanitizeForFirestore(post.businessType) : undefined,
      visualStyle: post.visualStyle ? sanitizeForFirestore(post.visualStyle) : undefined,
      targetAudience: post.targetAudience ? sanitizeForFirestore(post.targetAudience) : undefined,
      generationPrompt: post.generationPrompt ? sanitizeForFirestore(post.generationPrompt) : undefined,
      aiModel: post.aiModel ? sanitizeForFirestore(post.aiModel) : undefined,
    });

    return {
      userId,
      brandProfileId,
      platform: validatePlatform(cleanValue(post.platform, 'instagram')),
      postType: validatePostType(cleanValue(post.postType, 'post')),
      content: removeUndefined({
        text: sanitizeForFirestore(post.content).trim() || 'Generated content',
        hashtags: hashtags.length > 0 ? hashtags.map(tag => String(tag)) : undefined,
        mentions: undefined, // Not in current GeneratedPost type, so omit it
        imageUrl: sanitizeForFirestore(post.imageUrl || post.variants?.[0]?.imageUrl || '') || undefined,
        videoUrl: sanitizeForFirestore(post.videoUrl || '') || undefined,
      }),
      metadata,
      analytics: {
        qualityScore: clampAnalyticsValue(post.qualityScore, 75),
        engagementPrediction: clampAnalyticsValue(post.engagementPrediction, 70),
        brandAlignmentScore: clampAnalyticsValue(post.brandAlignmentScore, 80),
        views: 0,
        likes: 0,
        shares: 0,
        comments: 0,
      },
      status: post.status === 'posted' ? 'published' : 'draft',
    };
  }

  // Convert from Firestore document to app GeneratedPost
  private fromFirestoreDocument(doc: GeneratedPostDocument): GeneratedPost {
    return {
      id: doc.id,
      platform: doc.platform as Platform,
      postType: doc.postType,
      content: doc.content.text,
      hashtags: doc.content.hashtags || [],
      imageUrl: doc.content.imageUrl,
      videoUrl: doc.content.videoUrl,
      businessType: doc.metadata.businessType,
      visualStyle: doc.metadata.visualStyle,
      targetAudience: doc.metadata.targetAudience,
      generationPrompt: doc.metadata.generationPrompt,
      aiModel: doc.metadata.aiModel,
      qualityScore: doc.analytics?.qualityScore,
      engagementPrediction: doc.analytics?.engagementPrediction,
      brandAlignmentScore: doc.analytics?.brandAlignmentScore,
      date: doc.createdAt instanceof Date ? doc.createdAt.toISOString() : new Date().toISOString(),
      // Legacy fields for backward compatibility
      status: doc.status === 'published' ? 'posted' : 'generated',
      variants: [{
        platform: doc.platform as Platform,
        imageUrl: doc.content.imageUrl || '',
      }],
      catchyWords: '', // Not stored in Firestore, will be populated from new posts
      subheadline: undefined, // Not stored in Firestore, will be populated from new posts
      callToAction: undefined, // Not stored in Firestore, will be populated from new posts
    };
  }

  // Save generated post
  async saveGeneratedPost(
    post: GeneratedPost,
    userId: string,
    brandProfileId: string
  ): Promise<string> {
    try {

      const firestoreData = this.toFirestoreDocument(post, userId, brandProfileId);

      // Additional validation to catch nested entities
      const validateNoNestedEntities = (obj: any, path: string = ''): void => {
        if (obj === null || obj === undefined) return;

        if (typeof obj === 'object' && !Array.isArray(obj) && !(obj instanceof Date)) {
          Object.keys(obj).forEach(key => {
            const value = obj[key];
            const currentPath = path ? `${path}.${key}` : key;

            if (value !== null && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
              // Check if this is a simple object with only primitive values
              const hasComplexNesting = Object.values(value).some(v =>
                v !== null && typeof v === 'object' && !Array.isArray(v) && !(v instanceof Date)
              );

              if (hasComplexNesting) {
                throw new Error(`Property ${currentPath} contains an invalid nested entity`);
              }
            }

            validateNoNestedEntities(value, currentPath);
          });
        } else if (Array.isArray(obj)) {
          obj.forEach((item, index) => {
            validateNoNestedEntities(item, `${path}[${index}]`);
          });
        }
      };

      validateNoNestedEntities(firestoreData);

      // Validate data with schema
      const validatedData = GeneratedPostDocumentSchema.omit({
        id: true,
        createdAt: true,
        updatedAt: true,
      }).parse(firestoreData);

      const result = await this.create(validatedData);

      return result;
    } catch (error) {
      throw error;
    }
  }

  // Get user's generated posts as app format
  async getUserGeneratedPosts(
    userId: string,
    options?: {
      limit?: number;
      platform?: Platform;
      brandProfileId?: string;
    }
  ): Promise<GeneratedPost[]> {
    let q = query(
      collection(db, COLLECTIONS.GENERATED_POSTS),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    if (options?.platform) {
      q = query(q, where('platform', '==', options.platform));
    }

    if (options?.brandProfileId) {
      q = query(q, where('brandProfileId', '==', options.brandProfileId));
    }

    if (options?.limit) {
      q = query(q, limit(options.limit));
    }

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as GeneratedPostDocument[];

    return docs.map(doc => this.fromFirestoreDocument(doc));
  }

  // Get recent posts for a specific brand profile
  async getRecentPostsForBrand(
    userId: string,
    brandProfileId: string,
    limitCount: number = 10
  ): Promise<GeneratedPost[]> {
    const q = query(
      collection(db, COLLECTIONS.GENERATED_POSTS),
      where('userId', '==', userId),
      where('brandProfileId', '==', brandProfileId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as GeneratedPostDocument[];

    return docs.map(doc => this.fromFirestoreDocument(doc));
  }

  // Update post analytics
  async updatePostAnalytics(
    postId: string,
    analytics: {
      views?: number;
      likes?: number;
      shares?: number;
      comments?: number;
      qualityScore?: number;
      engagementPrediction?: number;
      brandAlignmentScore?: number;
    }
  ): Promise<void> {
    const updateData: Partial<GeneratedPostDocument> = {};

    if (analytics.views !== undefined ||
      analytics.likes !== undefined ||
      analytics.shares !== undefined ||
      analytics.comments !== undefined ||
      analytics.qualityScore !== undefined ||
      analytics.engagementPrediction !== undefined ||
      analytics.brandAlignmentScore !== undefined) {

      // Get current document to merge analytics
      const currentDoc = await this.getById(postId);
      if (currentDoc) {
        updateData.analytics = {
          ...currentDoc.analytics,
          ...analytics,
        };
      }
    }

    await this.update(postId, updateData);
  }

  // Update post status
  async updatePostStatus(
    postId: string,
    status: GeneratedPostDocument['status'],
    scheduledAt?: Date,
    publishedAt?: Date
  ): Promise<void> {
    const updateData: Partial<GeneratedPostDocument> = { status };

    if (scheduledAt) {
      updateData.scheduledAt = scheduledAt;
    }

    if (publishedAt) {
      updateData.publishedAt = publishedAt;
    }

    await this.update(postId, updateData);
  }

  // Get posts by status
  async getPostsByStatus(
    userId: string,
    status: GeneratedPostDocument['status']
  ): Promise<GeneratedPost[]> {
    const q = query(
      collection(db, COLLECTIONS.GENERATED_POSTS),
      where('userId', '==', userId),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as GeneratedPostDocument[];

    return docs.map(doc => this.fromFirestoreDocument(doc));
  }

  // Delete old posts (cleanup utility)
  async deleteOldPosts(userId: string, daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const q = query(
      collection(db, COLLECTIONS.GENERATED_POSTS),
      where('userId', '==', userId),
      where('createdAt', '<', cutoffDate)
    );

    const querySnapshot = await getDocs(q);
    const batch = this.createBatch();

    querySnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await this.executeBatch(batch);
    return querySnapshot.docs.length;
  }
}

// Export singleton instance
export const generatedPostFirebaseService = new GeneratedPostService();
