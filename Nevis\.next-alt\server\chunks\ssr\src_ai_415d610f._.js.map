{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/capabilities.ts"], "sourcesContent": ["/**\n * Model Capabilities Configuration\n * Defines what each model version can do\n */\n\nimport type { ModelCapabilities, RevoModelId } from '../types/model-types';\nimport type { Platform } from '@/lib/types';\n\n// Define capabilities for each model version\nexport const modelCapabilities: Record<RevoModelId, ModelCapabilities> = {\n  'revo-1.0': {\n    // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Not supported in 1.0\n    enhancedFeatures: true, // Upgraded from false\n    artifactSupport: false, // Basic model doesn't support artifacts\n    aspectRatios: ['1:1'], // Only square images\n    maxQuality: 9, // Upgraded from 7 for Gemini 2.5 Flash Image Preview\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Enhanced brand consistency\n    realTimeContext: true, // Now enabled for better context\n    perfectTextRendering: true, // NEW: Gemini 2.5 Flash Image Preview feature\n    highResolution: true // NEW: 2048x2048 support\n  },\n\n  'revo-1.5': {\n    // Enhanced model with advanced features\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Video coming in 2.0\n    enhancedFeatures: true,\n    artifactSupport: true, // Full artifact support\n    aspectRatios: ['1:1', '16:9', '9:16'], // Multiple aspect ratios\n    maxQuality: 8, // Superior quality\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Advanced brand consistency\n    realTimeContext: true // Real-time context and trends\n  },\n\n\n\n  'revo-2.0': {\n    // Premium Next-Gen AI model\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Focus on premium image generation\n    enhancedFeatures: true,\n    artifactSupport: true, // Premium artifact support\n    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'], // All aspect ratios\n    maxQuality: 10, // Maximum quality with native image generation\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Perfect brand consistency with character consistency\n    realTimeContext: true, // Premium real-time features\n    characterConsistency: true, // NEW: Maintain character consistency across images\n    intelligentEditing: true, // NEW: Inpainting, outpainting, targeted edits\n    multimodalReasoning: true // NEW: Advanced visual context understanding\n  }\n};\n\n// Capability comparison matrix\nexport const capabilityMatrix = {\n  contentGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from standard\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  designGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  videoGeneration: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'none'\n  },\n  artifactSupport: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'full',\n    'revo-2.0': 'premium'\n  },\n  brandConsistency: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'advanced',\n    'revo-2.0': 'perfect'\n  },\n  characterConsistency: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  },\n  intelligentEditing: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  }\n} as const;\n\n// Feature availability by model\nexport const featureAvailability = {\n  // Content features\n  hashtagGeneration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  catchyWords: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  subheadlines: ['revo-1.5', 'revo-2.0'],\n  callToAction: ['revo-1.5', 'revo-2.0'],\n  contentVariants: ['revo-1.5', 'revo-2.0'],\n\n  // Design features\n  logoIntegration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  brandColors: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  designExamples: ['revo-1.5', 'revo-2.0'],\n  textOverlay: ['revo-1.5', 'revo-2.0'],\n  multipleAspectRatios: ['revo-1.5', 'revo-2.0'],\n\n  // Advanced features\n  realTimeContext: ['revo-1.5', 'revo-2.0'],\n  trendingTopics: ['revo-1.5', 'revo-2.0'],\n  marketIntelligence: ['revo-1.5', 'revo-2.0'],\n  competitorAnalysis: ['revo-2.0'],\n\n  // Revo 2.0 exclusive features\n  characterConsistency: ['revo-2.0'],\n  intelligentEditing: ['revo-2.0'],\n  inpainting: ['revo-2.0'],\n  outpainting: ['revo-2.0'],\n  multimodalReasoning: ['revo-2.0'],\n\n  // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)\n  perfectTextRendering: ['revo-1.0', 'revo-2.0'],\n  highResolution: ['revo-1.0', 'revo-2.0'],\n\n  // Artifact features\n  artifactReference: ['revo-1.5'],\n  exactUseArtifacts: ['revo-1.5'],\n  textOverlayArtifacts: ['revo-1.5']\n} as const;\n\n// Platform-specific capabilities\nexport const platformCapabilities = {\n  Instagram: {\n    'revo-1.0': {\n      aspectRatios: ['1:1'],\n      maxQuality: 7,\n      features: ['basic-design', 'hashtags']\n    },\n    'revo-1.5': {\n      aspectRatios: ['1:1', '9:16'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'hashtags', 'stories', 'reels-ready']\n    }\n  },\n  Facebook: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'page-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'page-posts', 'stories']\n    }\n  },\n  Twitter: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'tweets']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'tweets', 'threads']\n    }\n  },\n  LinkedIn: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'professional-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'professional-posts', 'articles']\n    }\n  }\n} as const;\n\n// Utility functions\nexport function hasCapability(modelId: RevoModelId, capability: keyof ModelCapabilities): boolean {\n  return modelCapabilities[modelId][capability] as boolean;\n}\n\nexport function getCapabilityLevel(modelId: RevoModelId, capability: keyof typeof capabilityMatrix): string {\n  return capabilityMatrix[capability][modelId];\n}\n\nexport function hasFeature(modelId: RevoModelId, feature: keyof typeof featureAvailability): boolean {\n  return featureAvailability[feature].includes(modelId);\n}\n\nexport function getModelsByFeature(feature: keyof typeof featureAvailability): RevoModelId[] {\n  return [...featureAvailability[feature]] as RevoModelId[];\n}\n\nexport function getPlatformCapabilities(modelId: RevoModelId, platform: Platform) {\n  return platformCapabilities[platform]?.[modelId] || null;\n}\n\nexport function getMaxQualityForPlatform(modelId: RevoModelId, platform: Platform): number {\n  const platformCaps = getPlatformCapabilities(modelId, platform);\n  return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;\n}\n\nexport function getSupportedAspectRatios(modelId: RevoModelId, platform?: Platform): string[] {\n  if (platform) {\n    const platformCaps = getPlatformCapabilities(modelId, platform);\n    return platformCaps?.aspectRatios ? [...platformCaps.aspectRatios] : [...modelCapabilities[modelId].aspectRatios];\n  }\n  return [...modelCapabilities[modelId].aspectRatios];\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAMM,MAAM,oBAA4D;IACvE,YAAY;QACV,yEAAyE;QACzE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;SAAM;QACrB,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,gBAAgB,KAAK,yBAAyB;IAChD;IAEA,YAAY;QACV,wCAAwC;QACxC,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;SAAO;QACrC,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB,KAAK,+BAA+B;IACvD;IAIA,YAAY;QACV,4BAA4B;QAC5B,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;SAAM;QACnD,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,oBAAoB;QACpB,qBAAqB,KAAK,6CAA6C;IACzE;AACF;AAGO,MAAM,mBAAmB;IAC9B,mBAAmB;QACjB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,sBAAsB;QACpB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,oBAAoB;QAClB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,mBAAmB;IACnB,mBAAmB;QAAC;QAAY;QAAY;KAAW;IACvD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,cAAc;QAAC;QAAY;KAAW;IACtC,cAAc;QAAC;QAAY;KAAW;IACtC,iBAAiB;QAAC;QAAY;KAAW;IAEzC,kBAAkB;IAClB,iBAAiB;QAAC;QAAY;QAAY;KAAW;IACrD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,gBAAgB;QAAC;QAAY;KAAW;IACxC,aAAa;QAAC;QAAY;KAAW;IACrC,sBAAsB;QAAC;QAAY;KAAW;IAE9C,oBAAoB;IACpB,iBAAiB;QAAC;QAAY;KAAW;IACzC,gBAAgB;QAAC;QAAY;KAAW;IACxC,oBAAoB;QAAC;QAAY;KAAW;IAC5C,oBAAoB;QAAC;KAAW;IAEhC,8BAA8B;IAC9B,sBAAsB;QAAC;KAAW;IAClC,oBAAoB;QAAC;KAAW;IAChC,YAAY;QAAC;KAAW;IACxB,aAAa;QAAC;KAAW;IACzB,qBAAqB;QAAC;KAAW;IAEjC,uEAAuE;IACvE,sBAAsB;QAAC;QAAY;KAAW;IAC9C,gBAAgB;QAAC;QAAY;KAAW;IAExC,oBAAoB;IACpB,mBAAmB;QAAC;KAAW;IAC/B,mBAAmB;QAAC;KAAW;IAC/B,sBAAsB;QAAC;KAAW;AACpC;AAGO,MAAM,uBAAuB;IAClC,WAAW;QACT,YAAY;YACV,cAAc;gBAAC;aAAM;YACrB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAW;QACxC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAO;aAAO;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAY;gBAAW;aAAc;QACrE;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAa;QAC1C;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAc;aAAU;QACxD;IACF;IACA,SAAS;QACP,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAS;QACtC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAU;aAAU;QACpD;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAqB;QAClD;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAsB;aAAW;QACjE;IACF;AACF;AAGO,SAAS,cAAc,OAAoB,EAAE,UAAmC;IACrF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,WAAW;AAC/C;AAEO,SAAS,mBAAmB,OAAoB,EAAE,UAAyC;IAChG,OAAO,gBAAgB,CAAC,WAAW,CAAC,QAAQ;AAC9C;AAEO,SAAS,WAAW,OAAoB,EAAE,OAAyC;IACxF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AAEO,SAAS,mBAAmB,OAAyC;IAC1E,OAAO;WAAI,mBAAmB,CAAC,QAAQ;KAAC;AAC1C;AAEO,SAAS,wBAAwB,OAAoB,EAAE,QAAkB;IAC9E,OAAO,oBAAoB,CAAC,SAAS,EAAE,CAAC,QAAQ,IAAI;AACtD;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAkB;IAC/E,MAAM,eAAe,wBAAwB,SAAS;IACtD,OAAO,cAAc,cAAc,iBAAiB,CAAC,QAAQ,CAAC,UAAU;AAC1E;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAmB;IAChF,IAAI,UAAU;QACZ,MAAM,eAAe,wBAAwB,SAAS;QACtD,OAAO,cAAc,eAAe;eAAI,aAAa,YAAY;SAAC,GAAG;eAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;SAAC;IACnH;IACA,OAAO;WAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;KAAC;AACrD", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/pricing.ts"], "sourcesContent": ["/**\n * Model Pricing Configuration\n * Defines credit costs and pricing tiers for each model\n */\n\nimport type { ModelPricing, RevoModelId } from '../types/model-types';\n\n// Pricing configuration for each model\nexport const modelPricing: Record<RevoModelId, ModelPricing> = {\n  'revo-1.0': {\n    creditsPerGeneration: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerDesign: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerVideo: 0, // Video not supported\n    tier: 'enhanced' // Upgraded from basic\n  },\n\n  'revo-1.5': {\n    creditsPerGeneration: 2,\n    creditsPerDesign: 2,\n    creditsPerVideo: 0, // Video not supported yet\n    tier: 'premium'\n  },\n\n\n\n  'revo-2.0': {\n    creditsPerGeneration: 5,\n    creditsPerDesign: 5,\n    creditsPerVideo: 0, // Focus on premium image generation\n    tier: 'premium'\n  }\n};\n\n// Pricing tiers and their characteristics\nexport const pricingTiers = {\n  basic: {\n    name: 'Basic',\n    description: 'Reliable and cost-effective',\n    maxCreditsPerGeneration: 2,\n    features: [\n      'Standard quality generation',\n      'Basic brand consistency',\n      'Core platform support',\n      'Standard processing speed'\n    ],\n    recommendedFor: [\n      'Small businesses',\n      'Personal brands',\n      'Budget-conscious users',\n      'Basic content needs'\n    ]\n  },\n  premium: {\n    name: 'Premium',\n    description: 'Enhanced features and quality',\n    maxCreditsPerGeneration: 10,\n    features: [\n      'Enhanced quality generation',\n      'Advanced brand consistency',\n      'Full platform support',\n      'Artifact integration',\n      'Real-time context',\n      'Trending topics',\n      'Multiple aspect ratios'\n    ],\n    recommendedFor: [\n      'Growing businesses',\n      'Marketing agencies',\n      'Content creators',\n      'Professional brands'\n    ]\n  },\n  enterprise: {\n    name: 'Enterprise',\n    description: 'Maximum quality and features',\n    maxCreditsPerGeneration: 20,\n    features: [\n      'Premium quality generation',\n      '4K resolution support',\n      'Perfect text rendering',\n      'Advanced style controls',\n      'Priority processing',\n      'Dedicated support',\n      'Custom integrations'\n    ],\n    recommendedFor: [\n      'Large enterprises',\n      'Premium brands',\n      'High-volume users',\n      'Quality-focused campaigns'\n    ]\n  }\n} as const;\n\n// Credit packages and their values\nexport const creditPackages = {\n  starter: {\n    name: 'Starter Pack',\n    credits: 50,\n    price: 9.99,\n    pricePerCredit: 0.20,\n    bestFor: 'revo-1.0',\n    estimatedGenerations: {\n      'revo-1.0': 50,\n      'revo-1.5': 25,\n      'imagen-4': 5\n    }\n  },\n  professional: {\n    name: 'Professional Pack',\n    credits: 200,\n    price: 29.99,\n    pricePerCredit: 0.15,\n    bestFor: 'revo-1.5',\n    estimatedGenerations: {\n      'revo-1.0': 200,\n      'revo-1.5': 100,\n      'imagen-4': 20\n    }\n  },\n  business: {\n    name: 'Business Pack',\n    credits: 500,\n    price: 59.99,\n    pricePerCredit: 0.12,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 500,\n      'revo-1.5': 250,\n      'imagen-4': 50\n    }\n  },\n  enterprise: {\n    name: 'Enterprise Pack',\n    credits: 1000,\n    price: 99.99,\n    pricePerCredit: 0.10,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 1000,\n      'revo-1.5': 500,\n      'revo-2.0': 200,\n      'imagen-4': 100\n    }\n  }\n} as const;\n\n// Usage-based pricing calculations\nexport const usageCalculations = {\n  // Calculate cost for a specific generation request\n  calculateGenerationCost(modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): number {\n    const pricing = modelPricing[modelId];\n\n    switch (type) {\n      case 'content':\n        return pricing.creditsPerGeneration;\n      case 'design':\n        return pricing.creditsPerDesign;\n      case 'video':\n        return pricing.creditsPerVideo || 0;\n      default:\n        return pricing.creditsPerGeneration;\n    }\n  },\n\n  // Calculate total cost for multiple generations\n  calculateBatchCost(requests: { modelId: RevoModelId; type: 'content' | 'design' | 'video' }[]): number {\n    return requests.reduce((total, request) => {\n      return total + this.calculateGenerationCost(request.modelId, request.type);\n    }, 0);\n  },\n\n  // Estimate monthly cost based on usage patterns\n  estimateMonthlyCost(usage: {\n    modelId: RevoModelId;\n    generationsPerDay: number;\n    designsPerDay: number;\n    videosPerDay?: number;\n  }): {\n    dailyCost: number;\n    monthlyCost: number;\n    recommendedPackage: keyof typeof creditPackages;\n  } {\n    const pricing = modelPricing[usage.modelId];\n\n    const dailyCost =\n      (usage.generationsPerDay * pricing.creditsPerGeneration) +\n      (usage.designsPerDay * pricing.creditsPerDesign) +\n      ((usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0));\n\n    const monthlyCost = dailyCost * 30;\n\n    // Recommend package based on monthly cost\n    let recommendedPackage: keyof typeof creditPackages = 'starter';\n    if (monthlyCost > 400) recommendedPackage = 'enterprise';\n    else if (monthlyCost > 150) recommendedPackage = 'business';\n    else if (monthlyCost > 50) recommendedPackage = 'professional';\n\n    return {\n      dailyCost,\n      monthlyCost,\n      recommendedPackage\n    };\n  },\n\n  // Check if user has enough credits for a request\n  canAfford(userCredits: number, modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): boolean {\n    const cost = this.calculateGenerationCost(modelId, type);\n    return userCredits >= cost;\n  },\n\n  // Get the best model within budget\n  getBestModelForBudget(availableCredits: number, type: 'content' | 'design' | 'video' = 'content'): RevoModelId[] {\n    const affordableModels: RevoModelId[] = [];\n\n    for (const [modelId, pricing] of Object.entries(modelPricing)) {\n      const cost = type === 'content' ? pricing.creditsPerGeneration :\n        type === 'design' ? pricing.creditsPerDesign :\n          pricing.creditsPerVideo || 0;\n\n      if (cost <= availableCredits && cost > 0) {\n        affordableModels.push(modelId as RevoModelId);\n      }\n    }\n\n    // Sort by quality (higher credit cost usually means higher quality)\n    return affordableModels.sort((a, b) => {\n      const costA = this.calculateGenerationCost(a, type);\n      const costB = this.calculateGenerationCost(b, type);\n      return costB - costA; // Descending order (highest quality first)\n    });\n  }\n};\n\n// Pricing display utilities\nexport const pricingDisplay = {\n  // Format credits for display\n  formatCredits(credits: number): string {\n    if (credits >= 1000) {\n      return `${(credits / 1000).toFixed(1)}K`;\n    }\n    return credits.toString();\n  },\n\n  // Format price for display\n  formatPrice(price: number): string {\n    return `$${price.toFixed(2)}`;\n  },\n\n  // Get pricing tier info\n  getTierInfo(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    return pricingTiers[pricing.tier];\n  },\n\n  // Get cost comparison between models\n  compareCosts(modelA: RevoModelId, modelB: RevoModelId) {\n    const costA = modelPricing[modelA].creditsPerGeneration;\n    const costB = modelPricing[modelB].creditsPerGeneration;\n\n    const difference = Math.abs(costA - costB);\n    const percentDifference = ((difference / Math.min(costA, costB)) * 100).toFixed(0);\n\n    return {\n      cheaper: costA < costB ? modelA : modelB,\n      moreExpensive: costA > costB ? modelA : modelB,\n      difference,\n      percentDifference: `${percentDifference}%`,\n      ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`\n    };\n  },\n\n  // Get value proposition for each model\n  getValueProposition(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    const tierInfo = pricingTiers[pricing.tier];\n\n    return {\n      model: modelId,\n      tier: pricing.tier,\n      creditsPerGeneration: pricing.creditsPerGeneration,\n      valueScore: tierInfo.features.length / pricing.creditsPerGeneration, // Features per credit\n      description: tierInfo.description,\n      bestFor: tierInfo.recommendedFor\n    };\n  }\n};\n\n// Export utility functions\nexport function getModelPricing(modelId: RevoModelId): ModelPricing {\n  return modelPricing[modelId];\n}\n\nexport function getAllPricing(): Record<RevoModelId, ModelPricing> {\n  return modelPricing;\n}\n\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModelId[] {\n  return Object.entries(modelPricing)\n    .filter(([_, pricing]) => pricing.tier === tier)\n    .map(([modelId]) => modelId as RevoModelId);\n}\n\nexport function getCheapestModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((cheapest, [modelId, pricing]) => {\n      const currentCheapest = modelPricing[cheapest as RevoModelId];\n      return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ?\n        modelId as RevoModelId : cheapest as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n\nexport function getMostExpensiveModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((mostExpensive, [modelId, pricing]) => {\n      const currentMostExpensive = modelPricing[mostExpensive as RevoModelId];\n      return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ?\n        modelId as RevoModelId : mostExpensive as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAKM,MAAM,eAAkD;IAC7D,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM,WAAW,sBAAsB;IACzC;IAEA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;IAIA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO;QACL,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B,SAAS;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,mDAAmD;IACnD,yBAAwB,OAAoB,EAAE,OAAuC,SAAS;QAC5F,MAAM,UAAU,YAAY,CAAC,QAAQ;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,oBAAoB;YACrC,KAAK;gBACH,OAAO,QAAQ,gBAAgB;YACjC,KAAK;gBACH,OAAO,QAAQ,eAAe,IAAI;YACpC;gBACE,OAAO,QAAQ,oBAAoB;QACvC;IACF;IAEA,gDAAgD;IAChD,oBAAmB,QAA0E;QAC3F,OAAO,SAAS,MAAM,CAAC,CAAC,OAAO;YAC7B,OAAO,QAAQ,IAAI,CAAC,uBAAuB,CAAC,QAAQ,OAAO,EAAE,QAAQ,IAAI;QAC3E,GAAG;IACL;IAEA,gDAAgD;IAChD,qBAAoB,KAKnB;QAKC,MAAM,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC;QAE3C,MAAM,YACJ,AAAC,MAAM,iBAAiB,GAAG,QAAQ,oBAAoB,GACtD,MAAM,aAAa,GAAG,QAAQ,gBAAgB,GAC9C,CAAC,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC;QAE5D,MAAM,cAAc,YAAY;QAEhC,0CAA0C;QAC1C,IAAI,qBAAkD;QACtD,IAAI,cAAc,KAAK,qBAAqB;aACvC,IAAI,cAAc,KAAK,qBAAqB;aAC5C,IAAI,cAAc,IAAI,qBAAqB;QAEhD,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,iDAAiD;IACjD,WAAU,WAAmB,EAAE,OAAoB,EAAE,OAAuC,SAAS;QACnG,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS;QACnD,OAAO,eAAe;IACxB;IAEA,mCAAmC;IACnC,uBAAsB,gBAAwB,EAAE,OAAuC,SAAS;QAC9F,MAAM,mBAAkC,EAAE;QAE1C,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAe;YAC7D,MAAM,OAAO,SAAS,YAAY,QAAQ,oBAAoB,GAC5D,SAAS,WAAW,QAAQ,gBAAgB,GAC1C,QAAQ,eAAe,IAAI;YAE/B,IAAI,QAAQ,oBAAoB,OAAO,GAAG;gBACxC,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,oEAAoE;QACpE,OAAO,iBAAiB,IAAI,CAAC,CAAC,GAAG;YAC/B,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,OAAO,QAAQ,OAAO,2CAA2C;QACnE;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,6BAA6B;IAC7B,eAAc,OAAe;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO,GAAG,CAAC,UAAU,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C;QACA,OAAO,QAAQ,QAAQ;IACzB;IAEA,2BAA2B;IAC3B,aAAY,KAAa;QACvB,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAC/B;IAEA,wBAAwB;IACxB,aAAY,OAAoB;QAC9B,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,OAAO,YAAY,CAAC,QAAQ,IAAI,CAAC;IACnC;IAEA,qCAAqC;IACrC,cAAa,MAAmB,EAAE,MAAmB;QACnD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QACvD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QAEvD,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ;QACpC,MAAM,oBAAoB,CAAC,AAAC,aAAa,KAAK,GAAG,CAAC,OAAO,SAAU,GAAG,EAAE,OAAO,CAAC;QAEhF,OAAO;YACL,SAAS,QAAQ,QAAQ,SAAS;YAClC,eAAe,QAAQ,QAAQ,SAAS;YACxC;YACA,mBAAmB,GAAG,kBAAkB,CAAC,CAAC;YAC1C,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,QAAQ;QAC9D;IACF;IAEA,uCAAuC;IACvC,qBAAoB,OAAoB;QACtC,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,MAAM,WAAW,YAAY,CAAC,QAAQ,IAAI,CAAC;QAE3C,OAAO;YACL,OAAO;YACP,MAAM,QAAQ,IAAI;YAClB,sBAAsB,QAAQ,oBAAoB;YAClD,YAAY,SAAS,QAAQ,CAAC,MAAM,GAAG,QAAQ,oBAAoB;YACnE,aAAa,SAAS,WAAW;YACjC,SAAS,SAAS,cAAc;QAClC;IACF;AACF;AAGO,SAAS,gBAAgB,OAAoB;IAClD,OAAO,YAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAK,QAAQ,IAAI,KAAK,MAC1C,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;AACxB;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,UAAU,CAAC,SAAS,QAAQ;QACnC,MAAM,kBAAkB,YAAY,CAAC,SAAwB;QAC7D,OAAO,QAAQ,oBAAoB,GAAG,gBAAgB,oBAAoB,GACxE,UAAyB;IAC7B,GAAG;AACP;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,eAAe,CAAC,SAAS,QAAQ;QACxC,MAAM,uBAAuB,YAAY,CAAC,cAA6B;QACvE,OAAO,QAAQ,oBAAoB,GAAG,qBAAqB,oBAAoB,GAC7E,UAAyB;IAC7B,GAAG;AACP", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/model-configs.ts"], "sourcesContent": ["/**\r\n * Model Configurations\r\n * Centralized configuration for all Revo model versions\r\n */\r\n\r\nimport type { RevoModel, RevoModelId } from '../types/model-types';\r\nimport { modelCapabilities } from './capabilities';\r\nimport { modelPricing } from './pricing';\r\n\r\n// Base configurations for different AI services\r\nconst baseConfigs = {\r\n  'gemini-2.0': {\r\n    aiService: 'gemini-2.0' as const,\r\n    fallbackServices: ['gemini-2.5', 'openai'],\r\n    maxRetries: 3,\r\n    timeout: 30000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 85,\r\n      enhancementLevel: 5\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.7,\r\n      maxTokens: 2048,\r\n      topP: 0.9,\r\n      topK: 40\r\n    }\r\n  },\r\n  'gemini-2.5': {\r\n    aiService: 'gemini-2.5' as const,\r\n    fallbackServices: ['gemini-2.0', 'openai'],\r\n    maxRetries: 2,\r\n    timeout: 45000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 90,\r\n      enhancementLevel: 7\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.8,\r\n      maxTokens: 4096,\r\n      topP: 0.95,\r\n      topK: 50\r\n    }\r\n  },\r\n  'openai': {\r\n    aiService: 'openai' as const,\r\n    fallbackServices: ['gemini-2.5', 'gemini-2.0'],\r\n    maxRetries: 3,\r\n    timeout: 35000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 88,\r\n      enhancementLevel: 6\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.7,\r\n      maxTokens: 3000,\r\n      topP: 0.9\r\n    }\r\n  },\r\n  'gemini-2.5-flash-image': {\r\n    aiService: 'gemini-2.5-flash-image' as const,\r\n    fallbackServices: ['imagen-4', 'gemini-2.5'],\r\n    maxRetries: 3,\r\n    timeout: 45000,\r\n    qualitySettings: {\r\n      imageResolution: '2048x2048',     // Ultra HD resolution\r\n      compressionLevel: 95,             // Maximum quality\r\n      enhancementLevel: 8               // Reduced for cleaner designs (was 10)\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.4,                 // Reduced creativity for consistency (was 0.9)\r\n      maxTokens: 4096,                  // Detailed prompts for clean instructions\r\n      topP: 0.7,                        // Reduced variety for cleaner results (was 0.95)\r\n      topK: 30                          // Fewer creative choices for consistency (was 60)\r\n    }\r\n  }\r\n};\r\n\r\n// Model definitions\r\nexport const modelConfigs: Record<RevoModelId, RevoModel> = {\r\n  'revo-1.0': {\r\n    id: 'revo-1.0',\r\n    name: 'Revo 1.0',\r\n    version: '1.0.0',\r\n    description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',\r\n    longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',\r\n    icon: 'Zap',\r\n    badge: 'Enhanced',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-1.0'],\r\n    config: baseConfigs['gemini-2.5-flash-image'],\r\n    pricing: modelPricing['revo-1.0'],\r\n    features: [\r\n      'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',\r\n      '1:1 Images with High Resolution',\r\n      'Core Features',\r\n      'Proven Performance',\r\n      'Multi-platform Support',\r\n      'Enhanced Brand Consistency',\r\n      'Perfect Text Rendering',\r\n      'High-Resolution Output (2048x2048)'\r\n    ],\r\n    releaseDate: '2024-01-15',\r\n    lastUpdated: '2025-01-27'\r\n  },\r\n\r\n  'revo-1.5': {\r\n    id: 'revo-1.5',\r\n    name: 'Revo 1.5',\r\n    version: '1.5.0',\r\n    description: 'Enhanced Model - Advanced Features',\r\n    longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',\r\n    icon: 'Sparkles',\r\n    badge: 'Enhanced',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-1.5'],\r\n    config: {\r\n      ...baseConfigs['gemini-2.5'],\r\n      qualitySettings: {\r\n        ...baseConfigs['gemini-2.5'].qualitySettings,\r\n        enhancementLevel: 8\r\n      }\r\n    },\r\n    pricing: modelPricing['revo-1.5'],\r\n    features: [\r\n      'Advanced AI Engine',\r\n      'Superior Quality',\r\n      'Enhanced Design',\r\n      'Smart Optimizations',\r\n      'Professional Templates',\r\n      'Advanced Brand Integration',\r\n      'Real-time Context',\r\n      'Trending Topics Integration'\r\n    ],\r\n    releaseDate: '2024-06-20',\r\n    lastUpdated: '2024-12-15'\r\n  },\r\n\r\n\r\n\r\n  'revo-2.0': {\r\n    id: 'revo-2.0',\r\n    name: 'Revo 2.0',\r\n    version: '2.0.0',\r\n    description: 'Next-Gen Model - Advanced AI with native image generation',\r\n    longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',\r\n    icon: 'Rocket',\r\n    badge: 'Next-Gen',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-2.0'],\r\n    config: baseConfigs['gemini-2.5-flash-image'],\r\n    pricing: modelPricing['revo-2.0'],\r\n    features: [\r\n      'Next-Gen AI Engine',\r\n      'Native Image Generation',\r\n      'Character Consistency',\r\n      'Intelligent Editing',\r\n      'Inpainting & Outpainting',\r\n      'Multimodal Reasoning',\r\n      'All Aspect Ratios',\r\n      'Perfect Brand Consistency'\r\n    ],\r\n    releaseDate: '2025-01-27',\r\n    lastUpdated: '2025-01-27'\r\n  }\r\n};\r\n\r\n// Helper functions\r\nexport function getModelConfig(modelId: RevoModelId): RevoModel {\r\n  const config = modelConfigs[modelId];\r\n  if (!config) {\r\n    throw new Error(`Model configuration not found for: ${modelId}`);\r\n  }\r\n  return config;\r\n}\r\n\r\nexport function getAllModelConfigs(): RevoModel[] {\r\n  return Object.values(modelConfigs);\r\n}\r\n\r\nexport function getModelsByStatus(status: RevoModel['status']): RevoModel[] {\r\n  return getAllModelConfigs().filter(model => model.status === status);\r\n}\r\n\r\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModel[] {\r\n  return getAllModelConfigs().filter(model => model.pricing.tier === tier);\r\n}\r\n\r\nexport function getLatestModels(): RevoModel[] {\r\n  return getAllModelConfigs()\r\n    .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())\r\n    .slice(0, 3);\r\n}\r\n\r\nexport function getRecommendedModel(): RevoModel {\r\n  // Return Revo 1.5 as the recommended balanced option\r\n  return modelConfigs['revo-1.5'];\r\n}\r\n\r\nexport function getModelForBudget(maxCredits: number): RevoModel[] {\r\n  return getAllModelConfigs()\r\n    .filter(model => model.pricing.creditsPerGeneration <= maxCredits)\r\n    .sort((a, b) => a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);\r\n}\r\n\r\n// Model comparison utilities\r\nexport function compareModels(modelA: RevoModelId, modelB: RevoModelId) {\r\n  const configA = getModelConfig(modelA);\r\n  const configB = getModelConfig(modelB);\r\n\r\n  return {\r\n    quality: {\r\n      a: configA.capabilities.maxQuality,\r\n      b: configB.capabilities.maxQuality,\r\n      winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB\r\n    },\r\n    cost: {\r\n      a: configA.pricing.creditsPerGeneration,\r\n      b: configB.pricing.creditsPerGeneration,\r\n      winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB\r\n    },\r\n    features: {\r\n      a: configA.features.length,\r\n      b: configB.features.length,\r\n      winner: configA.features.length > configB.features.length ? modelA : modelB\r\n    },\r\n    status: {\r\n      a: configA.status,\r\n      b: configB.status,\r\n      recommendation: configA.status === 'stable' || configB.status === 'stable' ?\r\n        (configA.status === 'stable' ? modelA : modelB) : modelA\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAGD;AACA;;;AAEA,gDAAgD;AAChD,MAAM,cAAc;IAClB,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,UAAU;QACR,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAa;QAC9C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;QACR;IACF;IACA,0BAA0B;QACxB,WAAW;QACX,kBAAkB;YAAC;YAAY;SAAa;QAC5C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB,EAAgB,uCAAuC;QAC3E;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM,GAA4B,kDAAkD;QACtF;IACF;AACF;AAGO,MAAM,eAA+C;IAC1D,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ;YACN,GAAG,WAAW,CAAC,aAAa;YAC5B,iBAAiB;gBACf,GAAG,WAAW,CAAC,aAAa,CAAC,eAAe;gBAC5C,kBAAkB;YACpB;QACF;QACA,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAIA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;AACF;AAGO,SAAS,eAAe,OAAoB;IACjD,MAAM,SAAS,YAAY,CAAC,QAAQ;IACpC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS;IACjE;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAEO,SAAS,kBAAkB,MAA2B;IAC3D,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AAC/D;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,IAAI,KAAK;AACrE;AAEO,SAAS;IACd,OAAO,qBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,IAClF,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,qDAAqD;IACrD,OAAO,YAAY,CAAC,WAAW;AACjC;AAEO,SAAS,kBAAkB,UAAkB;IAClD,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,oBAAoB,IAAI,YACtD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,oBAAoB,GAAG,EAAE,OAAO,CAAC,oBAAoB;AACnF;AAGO,SAAS,cAAc,MAAmB,EAAE,MAAmB;IACpE,MAAM,UAAU,eAAe;IAC/B,MAAM,UAAU,eAAe;IAE/B,OAAO;QACL,SAAS;YACP,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,QAAQ,QAAQ,YAAY,CAAC,UAAU,GAAG,QAAQ,YAAY,CAAC,UAAU,GAAG,SAAS;QACvF;QACA,MAAM;YACJ,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,QAAQ,QAAQ,OAAO,CAAC,oBAAoB,GAAG,QAAQ,OAAO,CAAC,oBAAoB,GAAG,SAAS;QACjG;QACA,UAAU;YACR,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,QAAQ,QAAQ,QAAQ,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,MAAM,GAAG,SAAS;QACvE;QACA,QAAQ;YACN,GAAG,QAAQ,MAAM;YACjB,GAAG,QAAQ,MAAM;YACjB,gBAAgB,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,KAAK,WAC/D,QAAQ,MAAM,KAAK,WAAW,SAAS,SAAU;QACtD;IACF;AACF", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/config.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 Configuration\r\n * Model-specific configuration and constants\r\n */\r\n\r\nimport type { ModelConfig } from '../../types/model-types';\r\n\r\n// Revo 1.0 specific configuration\r\nexport const revo10Config: ModelConfig = {\r\n  aiService: 'gemini-2.5-flash-image-preview',\r\n  fallbackServices: ['gemini-2.5', 'gemini-2.0', 'openai'],\r\n  maxRetries: 3,\r\n  timeout: 45000, // 45 seconds (increased for better quality)\r\n  qualitySettings: {\r\n    imageResolution: '2048x2048', // Ultra HD resolution for premium quality\r\n    compressionLevel: 95, // Maximum quality\r\n    enhancementLevel: 7 // Reduced for cleaner designs (was 10)\r\n  },\r\n  promptSettings: {\r\n    temperature: 0.3, // Low creativity for consistent, clean designs (was 1.0)\r\n    maxTokens: 4096, // Detailed prompts for clean instructions\r\n    topP: 0.6, // Reduced variety for cleaner results (was 1.0)\r\n    topK: 25 // Fewer creative choices for consistency (was 100)\r\n  }\r\n};\r\n\r\n// Revo 1.0 specific constants\r\nexport const revo10Constants = {\r\n  // Model identification\r\n  MODEL_ID: 'revo-1.0',\r\n  MODEL_NAME: 'Revo 1.0',\r\n  MODEL_VERSION: '1.0.0',\r\n\r\n  // Capabilities\r\n  SUPPORTED_ASPECT_RATIOS: ['1:1'],\r\n  SUPPORTED_PLATFORMS: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\r\n  MAX_QUALITY_SCORE: 9.0, // Upgraded from 7.5\r\n\r\n  // Performance targets\r\n  TARGET_PROCESSING_TIME: 30000, // 30 seconds (increased for better quality)\r\n  TARGET_SUCCESS_RATE: 0.97, // 97% (increased from 95%)\r\n  TARGET_QUALITY_SCORE: 8.5, // Upgraded from 7.0\r\n\r\n  // Resource limits\r\n  MAX_CONTENT_LENGTH: 2000,\r\n  MAX_HASHTAGS: 15,\r\n  MAX_IMAGE_SIZE: 2048, // Upgraded from 1024\r\n\r\n  // Feature flags\r\n  FEATURES: {\r\n    ARTIFACTS_SUPPORT: false,\r\n    REAL_TIME_CONTEXT: true,  // Enable for better context\r\n    TRENDING_TOPICS: true,    // Enable for better content\r\n    MULTIPLE_ASPECT_RATIOS: false,\r\n    VIDEO_GENERATION: false,\r\n    ADVANCED_PROMPTING: true, // Enable for better prompts\r\n    ENHANCED_DESIGN: true,    // Enable for better designs!\r\n    PERFECT_TEXT_RENDERING: true, // NEW: Gemini 2.5 Flash Image Preview feature\r\n    HIGH_RESOLUTION: true,    // NEW: 2048x2048 resolution support\r\n    NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability\r\n  },\r\n\r\n  // Pricing\r\n  CREDITS_PER_GENERATION: 1.5, // Upgraded from 1 for enhanced capabilities\r\n  CREDITS_PER_DESIGN: 1.5, // Upgraded from 1 for enhanced capabilities\r\n  TIER: 'enhanced' // Upgraded from basic\r\n} as const;\r\n\r\n// Revo 1.0 specific prompts and templates\r\nexport const revo10Prompts = {\r\n  // Content generation prompts\r\n  CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.\r\nYour expertise spans viral content creation, brand storytelling, and audience engagement optimization.\r\n\r\nYour capabilities include:\r\n- **Deep Local Market Knowledge**: Understanding of local business environment, competition, and market trends\r\n- **Industry-Specific Insights**: 20+ years of experience across various industries\r\n- **Community Connection**: Deep understanding of local culture, values, and business needs\r\n- **Market Dynamics**: Knowledge of local economic conditions, competitive landscape, and business opportunities\r\n\r\nWhen creating content:\r\n- Write like a real industry professional, not AI\r\n- Use local market insights and industry knowledge naturally\r\n- Incorporate local phrases and community language authentically\r\n- Share real, relatable stories that connect with the local community\r\n- Position as the local expert with deep industry knowledge\r\n- Focus on local relevance and community impact\r\n- Use conversational, human language that builds trust and authority\r\n\r\nYour mission is to create content that sounds like it's written by a real industry professional with deep local expertise - not generic marketing copy. Every post should demonstrate your local market knowledge and industry authority.`,\r\n\r\n  CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:\r\nBusiness: {businessName}\r\nType: {businessType}\r\nPlatform: {platform}\r\nTone: {writingTone}\r\nLocation: {location}\r\n\r\nBrand Information:\r\n- Primary Color: {primaryColor}\r\n- Visual Style: {visualStyle}\r\n- Target Audience: {targetAudience}\r\n- Services: {services}\r\n- Key Features: {keyFeatures}\r\n- Competitive Advantages: {competitiveAdvantages}\r\n- Content Themes: {contentThemes}\r\n\r\nRequirements:\r\n- Create engaging, professional content that reflects the business's unique value proposition\r\n- Incorporate services and key features naturally into the content\r\n- Highlight competitive advantages when relevant\r\n- Include relevant hashtags (5-15) that align with content themes\r\n- Generate catchy words for the image that capture the brand essence\r\n- Ensure platform-appropriate formatting and tone\r\n- Maintain brand consistency with colors and visual style\r\n- Use only clean, readable text (no special characters, symbols, or garbled text)\r\n- Generate content in proper English with correct spelling and grammar\r\n- Avoid any corrupted or unreadable character sequences\r\n- Make the content location-specific and culturally relevant when appropriate`,\r\n\r\n  // Design generation prompts\r\n  DESIGN_SYSTEM_PROMPT: `You are a world-class graphic designer who creates 7 completely different types of social media designs, each with their own unique visual language and style. You have deep expertise in multiple industries and understand how to create designs that rival the best brands in the world.\r\n\r\nYour design philosophy:\r\n- Create designs that are VISUALLY APPEALING and engaging\r\n- Each design type should look completely different from the others\r\n- Focus on style-specific authenticity (watercolor should look like real watercolor, meme-style should look like a real meme)\r\n- Make designs that look like something from successful, popular brands\r\n- **CRITICAL: Make designs look like a human designer created them, not AI**\r\n- **CRITICAL: Each design type must have its own unique visual identity**\r\n- **IMPORTANT: Keep local/cultural elements subtle and natural, not overwhelming**\r\n- **NEW: Understand the business industry and create designs that rival world-class brands**\r\n\r\nWhen creating designs:\r\n- Start with the specific style requirements for the chosen design type\r\n- Use style-appropriate elements, colors, and typography\r\n- Focus on visual impact and engagement\r\n- Create designs people want to interact with\r\n- Use current design trends that work for the specific style\r\n- **MOST IMPORTANT: Make each design type genuinely unique and different**\r\n- **SECOND MOST IMPORTANT: Make it look human-made, not AI-generated**\r\n- **NEW: Study industry benchmarks and create designs that match world-class quality**\r\n\r\nCRITICAL: You are a human designer who understands that each design type should look completely different. A watercolor quote should look nothing like a meme-style post. A split photo collage should look nothing like a branded poster. Each style must have its own visual language and approach.\r\n\r\n**HUMAN DESIGN APPROACH:**\r\n- Add slight imperfections and asymmetry (humans aren't perfect)\r\n- Use natural spacing and proportions\r\n- Avoid overly symmetrical, geometric perfection\r\n- Make it feel organic and handcrafted\r\n- Focus on the design style first, local elements second\r\n\r\n**INDUSTRY INTELLIGENCE INTEGRATION:**\r\n- Study and understand the business industry context\r\n- Learn from world-class brands in the same industry\r\n- Incorporate industry-specific design trends and best practices\r\n- Create designs that feel authentic to the industry while being creative\r\n- Match the quality and sophistication of industry leaders\r\n\r\nFocus on creating designs that are both beautiful and engaging while maintaining the unique characteristics of each design type, looking genuinely human-made, and rivaling world-class industry standards.`,\r\n\r\n  DESIGN_USER_PROMPT_TEMPLATE: `Create a world-class, human-made 2048x2048 social media design that people will actually want to engage with:\r\n\r\nBUSINESS CONTEXT:\r\n- Business: {businessName}\r\n- Industry: {businessType}\r\n- Platform: {platform}\r\n- Target Message: {imageText}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Create a design that's VISUALLY APPEALING and engaging\r\n- Focus on the specific design style requirements\r\n- Make it look like a human designer created it, not AI\r\n- Keep local/cultural elements subtle and natural, not overwhelming\r\n- Focus on the design style first, local elements second\r\n- **NEW: Study industry benchmarks and create designs that rival world-class brands**\r\n\r\nKEY DESIGN PRINCIPLES:\r\n1. **HUMAN-MADE FIRST** - Make it look like a skilled human designer created it\r\n2. **STYLE AUTHENTICITY** - Follow the specific style requirements exactly\r\n3. **VISUAL UNIQUENESS** - Make this look completely different from other design types\r\n4. **NATURAL IMPERFECTIONS** - Add slight asymmetry, natural spacing, organic feel\r\n5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative\r\n6. **INDUSTRY EXCELLENCE** - Match the quality of world-class brands in the industry\r\n\r\nINDUSTRY INTELLIGENCE INTEGRATION:\r\n- Study and understand the {businessType} industry context\r\n- Learn from world-class brands in the same industry\r\n- Incorporate industry-specific design trends and best practices\r\n- Create designs that feel authentic to the industry while being creative\r\n- Match the quality and sophistication of industry leaders\r\n\r\nWHAT TO AVOID:\r\n- Overly perfect, symmetrical, AI-generated looking designs\r\n- Forced cultural elements that feel stereotypical\r\n- Generic, template-like designs\r\n- Overly complex or busy layouts\r\n- Poor contrast or readability\r\n- Designs that don't match industry quality standards\r\n\r\nWHAT TO INCLUDE:\r\n- Style-specific elements that match the chosen design type\r\n- Unique visual approach for the specific style\r\n- Subtle local touches that feel natural, not forced\r\n- Human imperfections - slight asymmetry, natural spacing, organic feel\r\n- Style-appropriate typography and layout\r\n- Industry-specific design elements and quality standards\r\n\r\nTECHNICAL REQUIREMENTS:\r\n- Resolution: 2048x2048 pixels\r\n- Format: Square (1:1)\r\n- Text must be readable on mobile\r\n- Logo integration should look natural\r\n\r\n🎨 GOAL: Create a world-class design that looks genuinely human-made, follows the specific style requirements, feels unique and engaging, and rivals the quality of industry leaders. Focus on the design style first, add subtle local touches naturally, make it look like a skilled human designer created it, and ensure it matches world-class industry standards.`,\r\n\r\n  // Error messages\r\n  ERROR_MESSAGES: {\r\n    GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',\r\n    DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',\r\n    INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',\r\n    SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',\r\n    TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',\r\n    QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'\r\n  }\r\n} as const;\r\n\r\n// Revo 1.0 validation rules\r\nexport const revo10Validation = {\r\n  // Content validation\r\n  content: {\r\n    minLength: 10,\r\n    maxLength: 2000,\r\n    requiredFields: ['businessType', 'platform', 'businessName'],\r\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\r\n  },\r\n\r\n  // Design validation\r\n  design: {\r\n    requiredFields: ['businessType', 'platform', 'visualStyle', 'imageText'],\r\n    supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,\r\n    maxImageTextLength: 200,\r\n    supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS\r\n  },\r\n\r\n  // Brand profile validation\r\n  brandProfile: {\r\n    requiredFields: ['businessType', 'businessName'],\r\n    optionalFields: [\r\n      'location', 'writingTone', 'visualStyle', 'primaryColor',\r\n      'accentColor', 'backgroundColor', 'logoDataUrl', 'targetAudience'\r\n    ]\r\n  }\r\n} as const;\r\n\r\n// Revo 1.0 performance metrics\r\nexport const revo10Metrics = {\r\n  // Expected performance benchmarks\r\n  BENCHMARKS: {\r\n    processingTime: {\r\n      target: 30000, // 30 seconds (upgraded from 20s)\r\n      acceptable: 40000, // 40 seconds (upgraded from 30s)\r\n      maximum: 60000 // 60 seconds (upgraded from 45s)\r\n    },\r\n    qualityScore: {\r\n      minimum: 7.0, // Upgraded from 5.0\r\n      target: 8.5, // Upgraded from 7.0\r\n      maximum: 9.0 // Upgraded from 7.5\r\n    },\r\n    successRate: {\r\n      minimum: 0.95, // Upgraded from 90%\r\n      target: 0.97, // Upgraded from 95%\r\n      maximum: 0.99 // Upgraded from 98%\r\n    }\r\n  },\r\n\r\n  // Monitoring thresholds\r\n  ALERTS: {\r\n    processingTimeHigh: 45000, // Alert if processing takes > 45s (upgraded from 35s)\r\n    qualityScoreLow: 7.5, // Alert if quality drops below 7.5 (upgraded from 6.0)\r\n    successRateLow: 0.95, // Alert if success rate drops below 95% (upgraded from 92%)\r\n    errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)\r\n  }\r\n} as const;\r\n\r\n// Export utility functions\r\nexport function getRevo10Config(): ModelConfig {\r\n  return revo10Config;\r\n}\r\n\r\nexport function isFeatureEnabled(feature: keyof typeof revo10Constants.FEATURES): boolean {\r\n  return revo10Constants.FEATURES[feature];\r\n}\r\n\r\nexport function getPromptTemplate(type: 'content' | 'design', templateName: string): string {\r\n  if (type === 'content') {\r\n    return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;\r\n  } else if (type === 'design') {\r\n    return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;\r\n  }\r\n  throw new Error(`Unknown prompt template: ${type}/${templateName}`);\r\n}\r\n\r\nexport function validateRequest(type: 'content' | 'design', request: any): { valid: boolean; errors: string[] } {\r\n  const errors: string[] = [];\r\n  const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;\r\n\r\n  // Check required fields\r\n  for (const field of validation.requiredFields) {\r\n    if (!request[field]) {\r\n      errors.push(`Missing required field: ${field}`);\r\n    }\r\n  }\r\n\r\n  // Check platform support\r\n  if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {\r\n    errors.push(`Unsupported platform: ${request.platform}`);\r\n  }\r\n\r\n  // Design-specific validation\r\n  if (type === 'design') {\r\n    if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {\r\n      errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);\r\n    }\r\n  }\r\n\r\n  return {\r\n    valid: errors.length === 0,\r\n    errors\r\n  };\r\n}\r\n\r\nexport function getPerformanceBenchmark(metric: string) {\r\n  return revo10Metrics.BENCHMARKS[metric as keyof typeof revo10Metrics.BENCHMARKS];\r\n}\r\n\r\nexport function shouldAlert(metric: string, value: number): boolean {\r\n  const alerts = revo10Metrics.ALERTS;\r\n\r\n  switch (metric) {\r\n    case 'processingTime':\r\n      return value > alerts.processingTimeHigh;\r\n    case 'qualityScore':\r\n      return value < alerts.qualityScoreLow;\r\n    case 'successRate':\r\n      return value < alerts.successRateLow;\r\n    case 'errorRate':\r\n      return value > alerts.errorRateHigh;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAKM,MAAM,eAA4B;IACvC,WAAW;IACX,kBAAkB;QAAC;QAAc;QAAc;KAAS;IACxD,YAAY;IACZ,SAAS;IACT,iBAAiB;QACf,iBAAiB;QACjB,kBAAkB;QAClB,kBAAkB,EAAE,uCAAuC;IAC7D;IACA,gBAAgB;QACd,aAAa;QACb,WAAW;QACX,MAAM;QACN,MAAM,GAAG,mDAAmD;IAC9D;AACF;AAGO,MAAM,kBAAkB;IAC7B,uBAAuB;IACvB,UAAU;IACV,YAAY;IACZ,eAAe;IAEf,eAAe;IACf,yBAAyB;QAAC;KAAM;IAChC,qBAAqB;QAAC;QAAa;QAAY;QAAW;KAAW;IACrE,mBAAmB;IAEnB,sBAAsB;IACtB,wBAAwB;IACxB,qBAAqB;IACrB,sBAAsB;IAEtB,kBAAkB;IAClB,oBAAoB;IACpB,cAAc;IACd,gBAAgB;IAEhB,gBAAgB;IAChB,UAAU;QACR,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QACjB,wBAAwB;QACxB,kBAAkB;QAClB,oBAAoB;QACpB,iBAAiB;QACjB,wBAAwB;QACxB,iBAAiB;QACjB,yBAAyB,KAAK,0CAA0C;IAC1E;IAEA,UAAU;IACV,wBAAwB;IACxB,oBAAoB;IACpB,MAAM,WAAW,sBAAsB;AACzC;AAGO,MAAM,gBAAgB;IAC3B,6BAA6B;IAC7B,uBAAuB,CAAC;;;;;;;;;;;;;;;;;;yOAkB+M,CAAC;IAExO,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;6EA2B4C,CAAC;IAE5E,4BAA4B;IAC5B,sBAAsB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2MAsCkL,CAAC;IAE1M,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uWAqDuU,CAAC;IAEtW,iBAAiB;IACjB,gBAAgB;QACd,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,qBAAqB;QACrB,SAAS;QACT,gBAAgB;IAClB;AACF;AAGO,MAAM,mBAAmB;IAC9B,qBAAqB;IACrB,SAAS;QACP,WAAW;QACX,WAAW;QACX,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,oBAAoB;IACpB,QAAQ;QACN,gBAAgB;YAAC;YAAgB;YAAY;YAAe;SAAY;QACxE,uBAAuB,gBAAgB,uBAAuB;QAC9D,oBAAoB;QACpB,oBAAoB,gBAAgB,mBAAmB;IACzD;IAEA,2BAA2B;IAC3B,cAAc;QACZ,gBAAgB;YAAC;YAAgB;SAAe;QAChD,gBAAgB;YACd;YAAY;YAAe;YAAe;YAC1C;YAAe;YAAmB;YAAe;SAClD;IACH;AACF;AAGO,MAAM,gBAAgB;IAC3B,kCAAkC;IAClC,YAAY;QACV,gBAAgB;YACd,QAAQ;YACR,YAAY;YACZ,SAAS,MAAM,iCAAiC;QAClD;QACA,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,SAAS,IAAI,oBAAoB;QACnC;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,SAAS,KAAK,oBAAoB;QACpC;IACF;IAEA,wBAAwB;IACxB,QAAQ;QACN,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe,KAAK,oDAAoD;IAC1E;AACF;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,iBAAiB,OAA8C;IAC7E,OAAO,gBAAgB,QAAQ,CAAC,QAAQ;AAC1C;AAEO,SAAS,kBAAkB,IAA0B,EAAE,YAAoB;IAChF,IAAI,SAAS,WAAW;QACtB,OAAO,cAAc,4BAA4B;IACnD,OAAO,IAAI,SAAS,UAAU;QAC5B,OAAO,cAAc,2BAA2B;IAClD;IACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC,EAAE,cAAc;AACpE;AAEO,SAAS,gBAAgB,IAA0B,EAAE,OAAY;IACtE,MAAM,SAAmB,EAAE;IAC3B,MAAM,aAAa,SAAS,YAAY,iBAAiB,OAAO,GAAG,iBAAiB,MAAM;IAE1F,wBAAwB;IACxB,KAAK,MAAM,SAAS,WAAW,cAAc,CAAE;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;QAChD;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,QAAQ,IAAI,CAAC,WAAW,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG;QACjF,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,QAAQ,EAAE;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,UAAU;QACrB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,iBAAiB,MAAM,CAAC,kBAAkB,EAAE;YAC9F,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAClG;IACF;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAEO,SAAS,wBAAwB,MAAc;IACpD,OAAO,cAAc,UAAU,CAAC,OAAgD;AAClF;AAEO,SAAS,YAAY,MAAc,EAAE,KAAa;IACvD,MAAM,SAAS,cAAc,MAAM;IAEnC,OAAQ;QACN,KAAK;YACH,OAAO,QAAQ,OAAO,kBAAkB;QAC1C,KAAK;YACH,OAAO,QAAQ,OAAO,eAAe;QACvC,KAAK;YACH,OAAO,QAAQ,OAAO,cAAc;QACtC,KAAK;YACH,OAAO,QAAQ,OAAO,aAAa;QACrC;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/content-performance-analyzer.ts"], "sourcesContent": ["/**\r\n * Content Performance Analyzer\r\n * Benchmarks against industry standards and continuously improves content quality\r\n */\r\n\r\nimport { SocialMediaPost, BusinessProfile } from './advanced-content-generator';\r\n\r\nexport interface PerformanceMetrics {\r\n  engagementRate: number;\r\n  reachRate: number;\r\n  clickThroughRate: number;\r\n  conversionRate: number;\r\n  shareRate: number;\r\n  commentRate: number;\r\n  saveRate: number;\r\n  overallScore: number;\r\n}\r\n\r\nexport interface IndustryBenchmark {\r\n  businessType: string;\r\n  platform: string;\r\n  averageEngagement: number;\r\n  topPerformerEngagement: number;\r\n  averageReach: number;\r\n  bestPractices: string[];\r\n  commonMistakes: string[];\r\n  successPatterns: string[];\r\n}\r\n\r\nexport interface ContentOptimization {\r\n  strengths: string[];\r\n  improvements: string[];\r\n  recommendations: string[];\r\n  nextIterationFocus: string[];\r\n  competitiveAdvantages: string[];\r\n}\r\n\r\nexport class ContentPerformanceAnalyzer {\r\n  private industryBenchmarks: Map<string, IndustryBenchmark[]> = new Map();\r\n  private performanceHistory: Map<string, PerformanceMetrics[]> = new Map();\r\n  private contentPatterns: Map<string, string[]> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeIndustryBenchmarks();\r\n    this.initializeSuccessPatterns();\r\n  }\r\n\r\n  /**\r\n   * Initialize industry benchmarks for different business types\r\n   */\r\n  private initializeIndustryBenchmarks() {\r\n    const benchmarks = {\r\n      restaurant: [\r\n        {\r\n          businessType: 'restaurant',\r\n          platform: 'instagram',\r\n          averageEngagement: 3.2,\r\n          topPerformerEngagement: 8.5,\r\n          averageReach: 15.4,\r\n          bestPractices: [\r\n            'High-quality food photography',\r\n            'Behind-the-scenes content',\r\n            'Customer testimonials',\r\n            'Seasonal menu highlights',\r\n            'Local ingredient stories'\r\n          ],\r\n          commonMistakes: [\r\n            'Poor lighting in photos',\r\n            'Generic captions',\r\n            'Inconsistent posting',\r\n            'Ignoring local trends',\r\n            'Over-promotional content'\r\n          ],\r\n          successPatterns: [\r\n            'Food close-ups with natural lighting',\r\n            'Stories about ingredients and preparation',\r\n            'Customer experience highlights',\r\n            'Local community involvement',\r\n            'Seasonal and trending ingredients'\r\n          ]\r\n        },\r\n        {\r\n          businessType: 'restaurant',\r\n          platform: 'facebook',\r\n          averageEngagement: 2.8,\r\n          topPerformerEngagement: 6.2,\r\n          averageReach: 12.1,\r\n          bestPractices: [\r\n            'Community engagement',\r\n            'Event announcements',\r\n            'Customer reviews sharing',\r\n            'Local partnerships',\r\n            'Family-friendly content'\r\n          ],\r\n          commonMistakes: [\r\n            'Posting only promotional content',\r\n            'Ignoring customer comments',\r\n            'Not leveraging local events',\r\n            'Generic stock photos',\r\n            'Inconsistent brand voice'\r\n          ],\r\n          successPatterns: [\r\n            'Community event participation',\r\n            'Customer story sharing',\r\n            'Local ingredient sourcing stories',\r\n            'Family dining experiences',\r\n            'Seasonal celebration posts'\r\n          ]\r\n        }\r\n      ],\r\n      retail: [\r\n        {\r\n          businessType: 'retail',\r\n          platform: 'instagram',\r\n          averageEngagement: 2.9,\r\n          topPerformerEngagement: 7.8,\r\n          averageReach: 18.2,\r\n          bestPractices: [\r\n            'Product styling and flat lays',\r\n            'User-generated content',\r\n            'Trend-focused content',\r\n            'Behind-the-brand stories',\r\n            'Seasonal collections'\r\n          ],\r\n          commonMistakes: [\r\n            'Product-only posts',\r\n            'Poor product photography',\r\n            'Ignoring fashion trends',\r\n            'Not showcasing versatility',\r\n            'Generic product descriptions'\r\n          ],\r\n          successPatterns: [\r\n            'Lifestyle product integration',\r\n            'Trend-forward styling',\r\n            'Customer styling examples',\r\n            'Seasonal fashion guides',\r\n            'Sustainable fashion stories'\r\n          ]\r\n        }\r\n      ],\r\n      fitness: [\r\n        {\r\n          businessType: 'fitness',\r\n          platform: 'instagram',\r\n          averageEngagement: 4.1,\r\n          topPerformerEngagement: 9.3,\r\n          averageReach: 16.7,\r\n          bestPractices: [\r\n            'Transformation stories',\r\n            'Workout demonstrations',\r\n            'Motivational content',\r\n            'Community challenges',\r\n            'Expert tips and advice'\r\n          ],\r\n          commonMistakes: [\r\n            'Intimidating content for beginners',\r\n            'Only showing perfect bodies',\r\n            'Generic motivational quotes',\r\n            'Not addressing different fitness levels',\r\n            'Ignoring mental health aspects'\r\n          ],\r\n          successPatterns: [\r\n            'Inclusive fitness content',\r\n            'Real transformation journeys',\r\n            'Beginner-friendly workouts',\r\n            'Mental health and fitness connection',\r\n            'Community support stories'\r\n          ]\r\n        }\r\n      ],\r\n      beauty: [\r\n        {\r\n          businessType: 'beauty',\r\n          platform: 'instagram',\r\n          averageEngagement: 3.7,\r\n          topPerformerEngagement: 8.9,\r\n          averageReach: 14.3,\r\n          bestPractices: [\r\n            'Before/after transformations',\r\n            'Tutorial content',\r\n            'Product demonstrations',\r\n            'Skin care education',\r\n            'Inclusive beauty content'\r\n          ],\r\n          commonMistakes: [\r\n            'Over-filtered photos',\r\n            'Not showing diverse skin types',\r\n            'Generic beauty tips',\r\n            'Ignoring skincare science',\r\n            'Not addressing common concerns'\r\n          ],\r\n          successPatterns: [\r\n            'Natural beauty enhancement',\r\n            'Educational skincare content',\r\n            'Diverse model representation',\r\n            'Seasonal beauty tips',\r\n            'Self-care and confidence building'\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    Object.entries(benchmarks).forEach(([businessType, benchmarkArray]) => {\r\n      this.industryBenchmarks.set(businessType, benchmarkArray);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Initialize success patterns for content optimization\r\n   */\r\n  private initializeSuccessPatterns() {\r\n    const patterns = {\r\n      'high-engagement-headlines': [\r\n        'Question-based headlines that spark curiosity',\r\n        'Numbers and statistics in headlines',\r\n        'Emotional trigger words',\r\n        'Local references and community connection',\r\n        'Trending topic integration',\r\n        'Problem-solution format',\r\n        'Exclusive or limited-time offers',\r\n        'Behind-the-scenes insights'\r\n      ],\r\n      'effective-captions': [\r\n        'Storytelling approach',\r\n        'Personal anecdotes and experiences',\r\n        'Call-to-action integration',\r\n        'Community questions and engagement',\r\n        'Educational value provision',\r\n        'Emotional connection building',\r\n        'Local culture and language integration',\r\n        'Trending hashtag utilization'\r\n      ],\r\n      'compelling-ctas': [\r\n        'Action-oriented language',\r\n        'Urgency and scarcity elements',\r\n        'Clear value proposition',\r\n        'Personalized messaging',\r\n        'Community-focused calls',\r\n        'Experience-based invitations',\r\n        'Social proof integration',\r\n        'Local relevance emphasis'\r\n      ]\r\n    };\r\n\r\n    Object.entries(patterns).forEach(([category, patternList]) => {\r\n      this.contentPatterns.set(category, patternList);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Analyze content performance against industry benchmarks\r\n   */\r\n  public analyzePerformance(\r\n    post: SocialMediaPost,\r\n    profile: BusinessProfile,\r\n    actualMetrics?: PerformanceMetrics\r\n  ): ContentOptimization {\r\n\r\n    const benchmarks = this.industryBenchmarks.get(profile.businessType) || [];\r\n    const platformBenchmark = benchmarks.find(b => b.platform === post.platform);\r\n\r\n    if (!platformBenchmark) {\r\n      return this.generateGenericOptimization();\r\n    }\r\n\r\n    // Analyze content elements\r\n    const headlineAnalysis = this.analyzeHeadline(post.headline, platformBenchmark);\r\n    const captionAnalysis = this.analyzeCaption(post.caption, platformBenchmark);\r\n    const ctaAnalysis = this.analyzeCTA(post.cta, platformBenchmark);\r\n    const hashtagAnalysis = this.analyzeHashtags(post.hashtags, platformBenchmark);\r\n\r\n    // Generate optimization recommendations\r\n    const optimization: ContentOptimization = {\r\n      strengths: [\r\n        ...headlineAnalysis.strengths,\r\n        ...captionAnalysis.strengths,\r\n        ...ctaAnalysis.strengths,\r\n        ...hashtagAnalysis.strengths\r\n      ],\r\n      improvements: [\r\n        ...headlineAnalysis.improvements,\r\n        ...captionAnalysis.improvements,\r\n        ...ctaAnalysis.improvements,\r\n        ...hashtagAnalysis.improvements\r\n      ],\r\n      recommendations: this.generateRecommendations(platformBenchmark, profile),\r\n      nextIterationFocus: this.identifyNextIterationFocus(platformBenchmark, profile),\r\n      competitiveAdvantages: this.identifyCompetitiveAdvantages(platformBenchmark, profile)\r\n    };\r\n\r\n    return optimization;\r\n  }\r\n\r\n  /**\r\n   * Analyze headline effectiveness\r\n   */\r\n  private analyzeHeadline(headline: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    // Check for success patterns\r\n    const successPatterns = this.contentPatterns.get('high-engagement-headlines') || [];\r\n    \r\n    if (headline.includes('?')) {\r\n      strengths.push('Uses question format to engage audience');\r\n    } else {\r\n      improvements.push('Consider using questions to increase engagement');\r\n    }\r\n\r\n    if (/\\d+/.test(headline)) {\r\n      strengths.push('Includes numbers for credibility');\r\n    } else {\r\n      improvements.push('Consider adding specific numbers or statistics');\r\n    }\r\n\r\n    if (headline.length > 10 && headline.length < 60) {\r\n      strengths.push('Optimal headline length for platform');\r\n    } else {\r\n      improvements.push('Adjust headline length for better readability');\r\n    }\r\n\r\n    // Check for emotional triggers\r\n    const emotionalWords = ['amazing', 'incredible', 'exclusive', 'limited', 'secret', 'proven'];\r\n    if (emotionalWords.some(word => headline.toLowerCase().includes(word))) {\r\n      strengths.push('Uses emotional trigger words');\r\n    } else {\r\n      improvements.push('Add emotional trigger words to increase appeal');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze caption effectiveness\r\n   */\r\n  private analyzeCaption(caption: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    if (caption.length > 50 && caption.length < 300) {\r\n      strengths.push('Optimal caption length for engagement');\r\n    } else {\r\n      improvements.push('Adjust caption length for better engagement');\r\n    }\r\n\r\n    // Check for storytelling elements\r\n    if (caption.includes('we') || caption.includes('our') || caption.includes('story')) {\r\n      strengths.push('Uses storytelling approach');\r\n    } else {\r\n      improvements.push('Add storytelling elements to create connection');\r\n    }\r\n\r\n    // Check for community engagement\r\n    if (caption.includes('?') || caption.includes('comment') || caption.includes('share')) {\r\n      strengths.push('Encourages community engagement');\r\n    } else {\r\n      improvements.push('Add questions or engagement prompts');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze CTA effectiveness\r\n   */\r\n  private analyzeCTA(cta: string, benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    const actionWords = ['visit', 'book', 'call', 'order', 'try', 'discover', 'experience'];\r\n    if (actionWords.some(word => cta.toLowerCase().includes(word))) {\r\n      strengths.push('Uses strong action words');\r\n    } else {\r\n      improvements.push('Use more compelling action words');\r\n    }\r\n\r\n    if (cta.length > 5 && cta.length < 50) {\r\n      strengths.push('Appropriate CTA length');\r\n    } else {\r\n      improvements.push('Optimize CTA length for clarity');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Analyze hashtag strategy\r\n   */\r\n  private analyzeHashtags(hashtags: string[], benchmark: IndustryBenchmark): { strengths: string[]; improvements: string[] } {\r\n    const strengths: string[] = [];\r\n    const improvements: string[] = [];\r\n\r\n    if (hashtags.length >= 5 && hashtags.length <= 10) {\r\n      strengths.push('Optimal number of hashtags');\r\n    } else {\r\n      improvements.push('Adjust hashtag count for better reach');\r\n    }\r\n\r\n    // Check for mix of popular and niche hashtags\r\n    const hasPopular = hashtags.some(tag => tag.includes('trending') || tag.includes('viral'));\r\n    const hasNiche = hashtags.some(tag => tag.length > 15);\r\n\r\n    if (hasPopular && hasNiche) {\r\n      strengths.push('Good mix of popular and niche hashtags');\r\n    } else {\r\n      improvements.push('Balance popular and niche hashtags for better reach');\r\n    }\r\n\r\n    return { strengths, improvements };\r\n  }\r\n\r\n  /**\r\n   * Generate specific recommendations based on benchmarks\r\n   */\r\n  private generateRecommendations(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    // Add benchmark-specific recommendations\r\n    benchmark.bestPractices.forEach(practice => {\r\n      recommendations.push(`Implement: ${practice}`);\r\n    });\r\n\r\n    // Add business-specific recommendations\r\n    recommendations.push(`Leverage ${profile.location} local culture and events`);\r\n    recommendations.push(`Highlight unique selling points: ${profile.uniqueSellingPoints.join(', ')}`);\r\n    recommendations.push(`Target ${profile.targetAudience} with personalized messaging`);\r\n\r\n    return recommendations.slice(0, 8); // Limit to top 8 recommendations\r\n  }\r\n\r\n  /**\r\n   * Identify focus areas for next iteration\r\n   */\r\n  private identifyNextIterationFocus(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const focus: string[] = [];\r\n\r\n    // Focus on top-performing patterns\r\n    benchmark.successPatterns.forEach(pattern => {\r\n      focus.push(`Enhance: ${pattern}`);\r\n    });\r\n\r\n    // Avoid common mistakes\r\n    benchmark.commonMistakes.forEach(mistake => {\r\n      focus.push(`Avoid: ${mistake}`);\r\n    });\r\n\r\n    return focus.slice(0, 6); // Limit to top 6 focus areas\r\n  }\r\n\r\n  /**\r\n   * Identify competitive advantages\r\n   */\r\n  private identifyCompetitiveAdvantages(benchmark: IndustryBenchmark, profile: BusinessProfile): string[] {\r\n    const advantages: string[] = [];\r\n\r\n    // Business-specific advantages\r\n    profile.uniqueSellingPoints.forEach(usp => {\r\n      advantages.push(`Unique advantage: ${usp}`);\r\n    });\r\n\r\n    // Location-based advantages\r\n    advantages.push(`Local market expertise in ${profile.location}`);\r\n    advantages.push(`Community connection and trust`);\r\n    advantages.push(`Cultural understanding and relevance`);\r\n\r\n    return advantages.slice(0, 5); // Limit to top 5 advantages\r\n  }\r\n\r\n  /**\r\n   * Generate generic optimization for unknown business types\r\n   */\r\n  private generateGenericOptimization(): ContentOptimization {\r\n    return {\r\n      strengths: ['Content created with business context'],\r\n      improvements: ['Add industry-specific benchmarks', 'Enhance local relevance'],\r\n      recommendations: ['Research industry best practices', 'Analyze competitor content'],\r\n      nextIterationFocus: ['Improve targeting', 'Enhance engagement'],\r\n      competitiveAdvantages: ['Personalized approach', 'Local market focus']\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Track performance over time for continuous improvement\r\n   */\r\n  public trackPerformance(businessName: string, metrics: PerformanceMetrics): void {\r\n    const history = this.performanceHistory.get(businessName) || [];\r\n    history.push(metrics);\r\n    this.performanceHistory.set(businessName, history.slice(-20)); // Keep last 20 records\r\n\r\n  }\r\n\r\n  /**\r\n   * Get performance trends for a business\r\n   */\r\n  public getPerformanceTrends(businessName: string): {\r\n    trend: 'improving' | 'declining' | 'stable';\r\n    averageScore: number;\r\n    bestPerformingContent: string[];\r\n  } {\r\n    const history = this.performanceHistory.get(businessName) || [];\r\n    \r\n    if (history.length < 2) {\r\n      return {\r\n        trend: 'stable',\r\n        averageScore: history[0]?.overallScore || 0,\r\n        bestPerformingContent: []\r\n      };\r\n    }\r\n\r\n    const recent = history.slice(-5);\r\n    const older = history.slice(-10, -5);\r\n    \r\n    const recentAvg = recent.reduce((sum, m) => sum + m.overallScore, 0) / recent.length;\r\n    const olderAvg = older.reduce((sum, m) => sum + m.overallScore, 0) / older.length;\r\n    \r\n    let trend: 'improving' | 'declining' | 'stable' = 'stable';\r\n    if (recentAvg > olderAvg + 0.5) trend = 'improving';\r\n    else if (recentAvg < olderAvg - 0.5) trend = 'declining';\r\n\r\n    const averageScore = history.reduce((sum, m) => sum + m.overallScore, 0) / history.length;\r\n\r\n    return {\r\n      trend,\r\n      averageScore,\r\n      bestPerformingContent: ['High-engagement headlines', 'Community-focused content', 'Local relevance']\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const performanceAnalyzer = new ContentPerformanceAnalyzer();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAkCM,MAAM;IACH,qBAAuD,IAAI,MAAM;IACjE,qBAAwD,IAAI,MAAM;IAClE,kBAAyC,IAAI,MAAM;IAE3D,aAAc;QACZ,IAAI,CAAC,4BAA4B;QACjC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,AAAQ,+BAA+B;QACrC,MAAM,aAAa;YACjB,YAAY;gBACV;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,QAAQ;gBACN;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,SAAS;gBACP;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;YACD,QAAQ;gBACN;oBACE,cAAc;oBACd,UAAU;oBACV,mBAAmB;oBACnB,wBAAwB;oBACxB,cAAc;oBACd,eAAe;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,gBAAgB;wBACd;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,iBAAiB;wBACf;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;aACD;QACH;QAEA,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,cAAc,eAAe;YAChE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc;QAC5C;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B;QAClC,MAAM,WAAW;YACf,6BAA6B;gBAC3B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,UAAU,YAAY;YACvD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU;QACrC;IACF;IAEA;;GAEC,GACD,AAAO,mBACL,IAAqB,EACrB,OAAwB,EACxB,aAAkC,EACb;QAErB,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,YAAY,KAAK,EAAE;QAC1E,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,KAAK,QAAQ;QAE3E,IAAI,CAAC,mBAAmB;YACtB,OAAO,IAAI,CAAC,2BAA2B;QACzC;QAEA,2BAA2B;QAC3B,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QAC7D,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;QAC1D,MAAM,cAAc,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;QAC9C,MAAM,kBAAkB,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QAE5D,wCAAwC;QACxC,MAAM,eAAoC;YACxC,WAAW;mBACN,iBAAiB,SAAS;mBAC1B,gBAAgB,SAAS;mBACzB,YAAY,SAAS;mBACrB,gBAAgB,SAAS;aAC7B;YACD,cAAc;mBACT,iBAAiB,YAAY;mBAC7B,gBAAgB,YAAY;mBAC5B,YAAY,YAAY;mBACxB,gBAAgB,YAAY;aAChC;YACD,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,mBAAmB;YACjE,oBAAoB,IAAI,CAAC,0BAA0B,CAAC,mBAAmB;YACvE,uBAAuB,IAAI,CAAC,6BAA6B,CAAC,mBAAmB;QAC/E;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBAAgB,QAAgB,EAAE,SAA4B,EAAmD;QACvH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,6BAA6B;QAC7B,MAAM,kBAAkB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gCAAgC,EAAE;QAEnF,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,MAAM,IAAI,CAAC,WAAW;YACxB,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,SAAS,MAAM,GAAG,MAAM,SAAS,MAAM,GAAG,IAAI;YAChD,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,+BAA+B;QAC/B,MAAM,iBAAiB;YAAC;YAAW;YAAc;YAAa;YAAW;YAAU;SAAS;QAC5F,IAAI,eAAe,IAAI,CAAC,CAAA,OAAQ,SAAS,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACtE,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,eAAe,OAAe,EAAE,SAA4B,EAAmD;QACrH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,IAAI,QAAQ,MAAM,GAAG,MAAM,QAAQ,MAAM,GAAG,KAAK;YAC/C,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,kCAAkC;QAClC,IAAI,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;YAClF,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,iCAAiC;QACjC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YACrF,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,WAAW,GAAW,EAAE,SAA4B,EAAmD;QAC7G,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,MAAM,cAAc;YAAC;YAAS;YAAQ;YAAQ;YAAS;YAAO;YAAY;SAAa;QACvF,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ;YAC9D,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,IAAI,MAAM,GAAG,KAAK,IAAI,MAAM,GAAG,IAAI;YACrC,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,gBAAgB,QAAkB,EAAE,SAA4B,EAAmD;QACzH,MAAM,YAAsB,EAAE;QAC9B,MAAM,eAAyB,EAAE;QAEjC,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,IAAI,IAAI;YACjD,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,8CAA8C;QAC9C,MAAM,aAAa,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC;QACjF,MAAM,WAAW,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;QAEnD,IAAI,cAAc,UAAU;YAC1B,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;YAAE;YAAW;QAAa;IACnC;IAEA;;GAEC,GACD,AAAQ,wBAAwB,SAA4B,EAAE,OAAwB,EAAY;QAChG,MAAM,kBAA4B,EAAE;QAEpC,yCAAyC;QACzC,UAAU,aAAa,CAAC,OAAO,CAAC,CAAA;YAC9B,gBAAgB,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU;QAC/C;QAEA,wCAAwC;QACxC,gBAAgB,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,QAAQ,CAAC,yBAAyB,CAAC;QAC5E,gBAAgB,IAAI,CAAC,CAAC,iCAAiC,EAAE,QAAQ,mBAAmB,CAAC,IAAI,CAAC,OAAO;QACjG,gBAAgB,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,cAAc,CAAC,4BAA4B,CAAC;QAEnF,OAAO,gBAAgB,KAAK,CAAC,GAAG,IAAI,iCAAiC;IACvE;IAEA;;GAEC,GACD,AAAQ,2BAA2B,SAA4B,EAAE,OAAwB,EAAY;QACnG,MAAM,QAAkB,EAAE;QAE1B,mCAAmC;QACnC,UAAU,eAAe,CAAC,OAAO,CAAC,CAAA;YAChC,MAAM,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS;QAClC;QAEA,wBAAwB;QACxB,UAAU,cAAc,CAAC,OAAO,CAAC,CAAA;YAC/B,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS;QAChC;QAEA,OAAO,MAAM,KAAK,CAAC,GAAG,IAAI,6BAA6B;IACzD;IAEA;;GAEC,GACD,AAAQ,8BAA8B,SAA4B,EAAE,OAAwB,EAAY;QACtG,MAAM,aAAuB,EAAE;QAE/B,+BAA+B;QAC/B,QAAQ,mBAAmB,CAAC,OAAO,CAAC,CAAA;YAClC,WAAW,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK;QAC5C;QAEA,4BAA4B;QAC5B,WAAW,IAAI,CAAC,CAAC,0BAA0B,EAAE,QAAQ,QAAQ,EAAE;QAC/D,WAAW,IAAI,CAAC,CAAC,8BAA8B,CAAC;QAChD,WAAW,IAAI,CAAC,CAAC,oCAAoC,CAAC;QAEtD,OAAO,WAAW,KAAK,CAAC,GAAG,IAAI,4BAA4B;IAC7D;IAEA;;GAEC,GACD,AAAQ,8BAAmD;QACzD,OAAO;YACL,WAAW;gBAAC;aAAwC;YACpD,cAAc;gBAAC;gBAAoC;aAA0B;YAC7E,iBAAiB;gBAAC;gBAAoC;aAA6B;YACnF,oBAAoB;gBAAC;gBAAqB;aAAqB;YAC/D,uBAAuB;gBAAC;gBAAyB;aAAqB;QACxE;IACF;IAEA;;GAEC,GACD,AAAO,iBAAiB,YAAoB,EAAE,OAA2B,EAAQ;QAC/E,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAC/D,QAAQ,IAAI,CAAC;QACb,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,QAAQ,KAAK,CAAC,CAAC,MAAM,uBAAuB;IAExF;IAEA;;GAEC,GACD,AAAO,qBAAqB,YAAoB,EAI9C;QACA,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAE/D,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,OAAO;gBACL,OAAO;gBACP,cAAc,OAAO,CAAC,EAAE,EAAE,gBAAgB;gBAC1C,uBAAuB,EAAE;YAC3B;QACF;QAEA,MAAM,SAAS,QAAQ,KAAK,CAAC,CAAC;QAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC;QAElC,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,OAAO,MAAM;QACpF,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,MAAM,MAAM;QAEjF,IAAI,QAA8C;QAClD,IAAI,YAAY,WAAW,KAAK,QAAQ;aACnC,IAAI,YAAY,WAAW,KAAK,QAAQ;QAE7C,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,QAAQ,MAAM;QAEzF,OAAO;YACL;YACA;YACA,uBAAuB;gBAAC;gBAA6B;gBAA6B;aAAkB;QACtG;IACF;AACF;AAGO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-1.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview\r\n * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport { BrandProfile } from '@/lib/types';\r\nimport { revo10Config, revo10Prompts } from './models/versions/revo-1.0/config';\r\nimport { advancedContentGenerator, BusinessProfile } from './advanced-content-generator';\r\nimport { performanceAnalyzer } from './content-performance-analyzer';\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\nimport {\r\n  generateCreativeHeadline,\r\n  generateCreativeSubheadline,\r\n  enhanceDesignCreativity,\r\n  generateCreativeCTA,\r\n  analyzeBusinessContext,\r\n  AntiRepetitionSystem,\r\n  CREATIVE_PROMPT_SYSTEM,\r\n  CONTENT_VARIATION_ENGINE,\r\n  // NEW: Import business-specific content generation\r\n  StrategicContentPlanner,\r\n  generateBusinessSpecificHeadline,\r\n  generateBusinessSpecificSubheadline,\r\n  generateBusinessSpecificCaption\r\n} from './creative-enhancement';\r\n\r\n// Advanced features integration (simplified for now)\r\n// TODO: Import advanced features from Revo 1.5 when available\r\n\r\n// Helper functions for advanced design generation\r\nfunction getBusinessDesignDNA(businessType: string): string {\r\n  const designDNA: Record<string, string> = {\r\n    'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',\r\n    'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',\r\n    'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',\r\n    'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',\r\n    'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',\r\n    'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',\r\n    'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',\r\n    'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',\r\n    'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'\r\n  };\r\n\r\n  return designDNA[businessType.toLowerCase()] || designDNA['default'];\r\n}\r\n\r\n// NEW: 7 truly different design types for dynamic social media feeds\r\nfunction getHumanDesignVariations(seed: number): any {\r\n  const variations = [\r\n    {\r\n      style: 'Watercolor Quotes',\r\n      layout: 'Soft, artistic watercolor background with elegant typography overlay',\r\n      composition: 'Centered or asymmetrical text with flowing watercolor elements',\r\n      mood: 'Artistic, elegant, inspirational',\r\n      elements: 'Watercolor textures, elegant fonts, soft color transitions, artistic backgrounds',\r\n      description: 'Create a design that looks like an artist painted it with watercolors, with flowing, organic shapes and elegant typography that feels handcrafted and artistic.'\r\n    },\r\n    {\r\n      style: 'Split Photo Collages',\r\n      layout: 'Two or three photo sections with text overlay on one section',\r\n      composition: 'Grid-based photo layout with text integrated naturally',\r\n      mood: 'Modern, dynamic, photo-driven',\r\n      elements: 'Photo sections, clean grid lines, integrated text, modern typography',\r\n      description: 'Design with a clean grid layout that splits the image into photo sections, with text naturally integrated into one section. Think Instagram grid meets modern magazine layout.'\r\n    },\r\n    {\r\n      style: 'Meme-Style Posts',\r\n      layout: 'Bold, punchy text with minimal background and high contrast',\r\n      composition: 'Centered text with simple, impactful background',\r\n      mood: 'Fun, viral, shareable',\r\n      elements: 'Bold typography, simple backgrounds, high contrast, meme-like simplicity',\r\n      description: 'Create a design that feels like a viral meme - bold, simple text with minimal background elements. Think Twitter meme aesthetics but professional.'\r\n    },\r\n    {\r\n      style: 'Polaroid-Style Testimonials',\r\n      layout: 'Polaroid frame with photo area and handwritten-style text',\r\n      composition: 'Polaroid border with content inside, vintage feel',\r\n      mood: 'Authentic, personal, nostalgic',\r\n      elements: 'Polaroid borders, vintage textures, handwritten fonts, authentic feel',\r\n      description: 'Design that looks like a vintage Polaroid photo with a white border, containing either a photo area or text that feels handwritten and personal.'\r\n    },\r\n    {\r\n      style: 'Minimal Photo-Driven Promos',\r\n      layout: 'Large photo background with minimal text overlay',\r\n      composition: 'Photo as hero element with subtle text placement',\r\n      mood: 'Clean, premium, photo-focused',\r\n      elements: 'Large photos, minimal text, clean typography, lots of white space',\r\n      description: 'Create a design where a beautiful photo is the main focus, with minimal, elegant text overlay. Think high-end magazine or premium brand aesthetics.'\r\n    },\r\n    {\r\n      style: 'Mixed-Media Artistic Posts',\r\n      layout: 'Layered design with multiple textures, patterns, and artistic elements',\r\n      composition: 'Complex layering with artistic elements and modern typography',\r\n      mood: 'Creative, artistic, unique',\r\n      elements: 'Multiple textures, artistic patterns, layered elements, creative typography',\r\n      description: 'Design with multiple artistic layers - think digital art meets graphic design. Include textures, patterns, and creative elements that feel like modern digital art.'\r\n    },\r\n    {\r\n      style: 'Branded Posters (Current Style)',\r\n      layout: 'Illustration-heavy design with brand elements and structured layout',\r\n      composition: 'Illustrated background with organized text and brand placement',\r\n      mood: 'Professional, branded, consistent',\r\n      elements: 'Illustrations, brand colors, structured typography, consistent branding',\r\n      description: 'The current style - professional illustrated posters with brand consistency. Use when you need to maintain strong brand identity.'\r\n    }\r\n  ];\r\n\r\n  return variations[seed % variations.length];\r\n}\r\n\r\n// NEW: Simple, clean design instructions for better visual appeal\r\nfunction injectHumanImperfections(designPrompt: string, seed: number): string {\r\n  const instructions = [\r\n    'Use natural spacing and proportions that feel balanced and appealing',\r\n    'Create a design that feels modern and current, not overly perfect',\r\n    'Focus on visual appeal and what people actually like to see',\r\n    'Make it look like something from a successful, popular brand'\r\n  ];\r\n\r\n  const selectedInstruction = instructions[seed % instructions.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN FOCUS:\r\n${selectedInstruction}\r\n\r\nKeep the design simple, clean, and visually appealing.`;\r\n}\r\n\r\n// NEW: Simple creative approach for better designs\r\nfunction injectCreativeRebellion(designPrompt: string, seed: number): string {\r\n  const approaches = [\r\n    `DESIGN APPROACH: Create a design that's visually appealing and engaging. Focus on what looks good and what people want to engage with.`,\r\n\r\n    `CREATIVE STYLE: Use a clean, modern approach that feels current and appealing. Make it look like something people would actually want to interact with.`,\r\n\r\n    `VISUAL APPROACH: Design with a focus on visual appeal and engagement. Create something that stands out and looks good.`,\r\n\r\n    `DESIGN PHILOSOPHY: Focus on creating designs that people want to engage with - clean, modern, and visually appealing.`\r\n  ];\r\n\r\n  const selectedApproach = approaches[seed % approaches.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN APPROACH:\r\n${selectedApproach}\r\n\r\nFocus on creating designs that are visually appealing and engaging.`;\r\n}\r\n\r\n// NEW: Simple design guidelines for better results\r\nfunction addArtisticConstraints(designPrompt: string, seed: number): string {\r\n  const constraints = [\r\n    `DESIGN FOCUS: Create a design that's visually appealing and engaging. Focus on clean, modern aesthetics that people actually like.`,\r\n\r\n    `COMPOSITION APPROACH: Use simple, clean layouts that are easy to read and understand. Less is more.`,\r\n\r\n    `CREATIVE ELEMENTS: Add modern, contemporary elements that make the design look good and engaging.`,\r\n\r\n    `VISUAL BALANCE: Create a design that feels balanced and appealing, with elements that work together well.`,\r\n\r\n    `DESIGN STYLE: Use a clean, modern approach that feels current and professional. Focus on visual appeal.`,\r\n\r\n    `CREATIVE APPROACH: Design with a focus on what people actually want to see and engage with.`,\r\n\r\n    `VISUAL HIERARCHY: Create clear visual hierarchy that guides the eye naturally through the design.`,\r\n\r\n    `DESIGN PRINCIPLES: Focus on creating a design that's both beautiful and engaging. Make it look good.`\r\n  ];\r\n\r\n  const selectedConstraint = constraints[seed % constraints.length];\r\n\r\n  return designPrompt + `\r\n\r\n🎨 DESIGN GUIDELINE:\r\n${selectedConstraint}\r\n\r\nKeep the design simple, clean, and visually appealing.`;\r\n}\r\n\r\nfunction getPlatformOptimization(platform: string): string {\r\n  const optimizations: Record<string, string> = {\r\n    'instagram': `\r\n- Mobile-first design with bold, clear elements\r\n- High contrast colors that pop on small screens\r\n- Text minimum 24px equivalent for readability\r\n- Center important elements for square crop compatibility\r\n- Thumb-stopping power for fast scroll feeds\r\n- Logo: Bottom right corner or naturally integrated`,\r\n\r\n    'linkedin': `\r\n- Professional, business-appropriate aesthetics\r\n- Corporate design standards and clean look\r\n- Clear value proposition for business audience\r\n- Professional photography and imagery\r\n- Thought leadership positioning\r\n- Logo: Prominent placement for brand authority`,\r\n\r\n    'facebook': `\r\n- Desktop and mobile viewing optimization\r\n- Engagement and shareability focus\r\n- Clear value proposition in visual hierarchy\r\n- Authentic, relatable imagery\r\n- Community-focused design elements\r\n- Logo: Top left or bottom right corner`,\r\n\r\n    'twitter': `\r\n- Rapid consumption and high engagement design\r\n- Bold, contrasting colors for timeline visibility\r\n- Minimal, impactful text elements\r\n- Trending visual styles integration\r\n- Real-time relevance\r\n- Logo: Small, subtle placement`,\r\n\r\n    'default': `\r\n- Cross-platform compatibility\r\n- Universal appeal and accessibility\r\n- Balanced design for multiple contexts\r\n- Professional appearance across devices\r\n- Logo: Flexible placement based on composition`\r\n  };\r\n\r\n  return optimizations[platform.toLowerCase()] || optimizations['default'];\r\n}\r\n\r\n// Advanced real-time context gathering for Revo 1.0 (enhanced version)\r\nasync function gatherRealTimeContext(businessType: string, location: string, platform: string) {\r\n  const context: any = {\r\n    trends: [],\r\n    weather: null,\r\n    events: [],\r\n    news: [],\r\n    localLanguage: {},\r\n    climateInsights: {},\r\n    trendingTopics: [],\r\n    timeContext: {\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      month: new Date().toLocaleDateString('en-US', { month: 'long' }),\r\n      season: getSeason(),\r\n      timeOfDay: getTimeOfDay()\r\n    }\r\n  };\r\n\r\n  try {\r\n    // Generate contextual trends based on business type and location\r\n    context.trends = generateContextualTrends(businessType, location);\r\n\r\n    // Generate weather-appropriate content suggestions\r\n    context.weather = generateWeatherContext(location);\r\n\r\n    // Generate local business opportunities\r\n    context.events = generateLocalOpportunities(businessType, location);\r\n\r\n    // NEW: Enhanced local language and cultural context\r\n    context.localLanguage = generateLocalLanguageContext(location);\r\n\r\n    // NEW: Advanced climate insights for business relevance\r\n    context.climateInsights = generateClimateInsights(location, businessType);\r\n\r\n    // NEW: Real-time trending topics (simulated for now, can be enhanced with actual APIs)\r\n    context.trendingTopics = generateTrendingTopics(businessType, location, platform);\r\n\r\n    // NEW: Local news and market insights\r\n    context.news = generateLocalNewsContext(businessType, location);\r\n\r\n    return context;\r\n\r\n  } catch (error) {\r\n    return context; // Return partial context\r\n  }\r\n}\r\n\r\n// Advanced design enhancement functions\r\nfunction shouldIncludePeopleInDesign(businessType: string, location: string, visualStyle: string): boolean {\r\n  const peopleBusinessTypes = [\r\n    'restaurant', 'fitness', 'healthcare', 'education', 'retail', 'hospitality',\r\n    'beauty', 'wellness', 'consulting', 'coaching', 'real estate', 'finance',\r\n    'technology', 'marketing', 'events', 'photography', 'fashion'\r\n  ];\r\n\r\n  return peopleBusinessTypes.some(type =>\r\n    businessType.toLowerCase().includes(type) ||\r\n    visualStyle === 'lifestyle' ||\r\n    visualStyle === 'authentic'\r\n  );\r\n}\r\n\r\nfunction getLocalCulturalContext(location: string): string {\r\n  const culturalContexts: Record<string, string> = {\r\n    'kenya': 'Subtle Kenyan elements: warm earth tones, natural textures, community feel',\r\n    'nigeria': 'Subtle Nigerian elements: vibrant accents, natural patterns, community warmth',\r\n    'south africa': 'Subtle South African elements: diverse representation, natural colors, community spirit',\r\n    'ghana': 'Subtle Ghanaian elements: warm tones, natural textures, community connection',\r\n    'uganda': 'Subtle Ugandan elements: natural colors, community feel, authentic representation',\r\n    'tanzania': 'Subtle Tanzanian elements: coastal influences, natural textures, community warmth',\r\n    'ethiopia': 'Subtle Ethiopian elements: natural earth tones, community connection, authentic feel',\r\n    'rwanda': 'Subtle Rwandan elements: natural colors, community spirit, authentic representation',\r\n    'default': 'Natural, authentic feel with subtle local elements that feel genuine, not forced'\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  for (const [key, context] of Object.entries(culturalContexts)) {\r\n    if (locationKey.includes(key)) {\r\n      return context;\r\n    }\r\n  }\r\n  return culturalContexts['default'];\r\n}\r\n\r\nfunction getDesignVariations(seed: number) {\r\n  const variations = [\r\n    {\r\n      style: 'Modern Minimalist',\r\n      layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',\r\n      composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',\r\n      mood: 'Professional, clean, sophisticated',\r\n      elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'\r\n    },\r\n    {\r\n      style: 'Dynamic Action',\r\n      layout: 'Diagonal composition with movement, multiple focal points, energetic flow',\r\n      composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',\r\n      mood: 'Energetic, exciting, forward-moving',\r\n      elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'\r\n    },\r\n    {\r\n      style: 'Lifestyle Authentic',\r\n      layout: 'Natural, candid composition with real-world settings, human-centered design',\r\n      composition: 'Environmental context, natural lighting, authentic moments captured',\r\n      mood: 'Warm, relatable, trustworthy, human',\r\n      elements: 'Natural lighting, authentic people, real environments, warm color tones'\r\n    },\r\n    {\r\n      style: 'Corporate Professional',\r\n      layout: 'Structured grid layout, balanced composition, formal presentation',\r\n      composition: 'Symmetrical balance, clear hierarchy, professional spacing',\r\n      mood: 'Trustworthy, established, reliable, premium',\r\n      elements: 'Corporate colors, professional imagery, clean typography, structured layout'\r\n    },\r\n    {\r\n      style: 'Creative Artistic',\r\n      layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',\r\n      composition: 'Creative angles, artistic overlays, unique visual treatments',\r\n      mood: 'Creative, innovative, unique, inspiring',\r\n      elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'\r\n    },\r\n    {\r\n      style: 'Tech Innovation',\r\n      layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',\r\n      composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',\r\n      mood: 'Innovative, cutting-edge, digital, forward-thinking',\r\n      elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'\r\n    },\r\n    {\r\n      style: 'Cultural Heritage',\r\n      layout: 'Traditional patterns mixed with modern design, cultural elements integrated',\r\n      composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',\r\n      mood: 'Cultural, authentic, heritage-proud, modern-traditional',\r\n      elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'\r\n    },\r\n    {\r\n      style: 'Luxury Premium',\r\n      layout: 'Elegant, sophisticated layout with premium materials and finishes',\r\n      composition: 'Luxurious spacing, premium typography, elegant proportions',\r\n      mood: 'Luxurious, premium, exclusive, sophisticated',\r\n      elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'\r\n    }\r\n  ];\r\n\r\n  return variations[seed % variations.length];\r\n}\r\n\r\nfunction getAdvancedPeopleInstructions(businessType: string, location: string): string {\r\n  const culturalContext = getLocalCulturalContext(location);\r\n\r\n  return `\r\n**ADVANCED PEOPLE INTEGRATION:**\r\n- Include diverse, authentic people with PERFECT FACIAL FEATURES\r\n- Complete faces, symmetrical features, natural expressions, professional poses\r\n- Faces fully visible, well-lit, anatomically correct with no deformations\r\n- Cultural Context: ${culturalContext}\r\n- Show people in varied, engaging settings:\r\n  * Professional environments (modern offices, studios, workshops)\r\n  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)\r\n  * Industry-specific contexts (${businessType} environments)\r\n  * Cultural celebrations and modern community gatherings\r\n  * Urban settings (co-working spaces, tech hubs, modern city life)\r\n  * Traditional meets modern (cultural heritage with contemporary life)\r\n- Ensure representation reflects local demographics and cultural values\r\n- Show real people in natural, engaging situations that vary by design\r\n- People should be actively engaged with the business/service context\r\n- Use authentic expressions of joy, confidence, success, and community\r\n- Include intergenerational representation when appropriate\r\n- Show modern African/local fashion and styling\r\n- Ensure people are central to the story, not just decorative elements`;\r\n}\r\n\r\n// NEW: Industry Intelligence System with World-Class Design Benchmarks\r\nfunction getIndustryDesignIntelligence(businessType: string): any {\r\n  const industryIntelligence: Record<string, any> = {\r\n    'restaurant': {\r\n      name: 'Restaurant & Food Service',\r\n      worldClassBrands: ['Noma', 'Eleven Madison Park', 'The French Laundry', 'Osteria Francescana', 'Gaggan'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Sophisticated, appetizing, experiential',\r\n        colorPalettes: ['Warm earth tones', 'Rich burgundies', 'Cream whites', 'Deep greens', 'Gold accents'],\r\n        typography: 'Elegant serifs, sophisticated sans-serifs, handwritten touches',\r\n        imagery: 'Food photography, intimate dining scenes, chef portraits, ingredient close-ups',\r\n        layout: 'Clean, spacious, food-focused, premium feel',\r\n        creativeElements: ['Food textures', 'Culinary tools', 'Seasonal ingredients', 'Dining atmosphere', 'Chef artistry']\r\n      },\r\n      creativityFrameworks: [\r\n        'Culinary storytelling through visual narrative',\r\n        'Seasonal and ingredient-driven design evolution',\r\n        'Chef personality and restaurant atmosphere integration',\r\n        'Food photography as art form',\r\n        'Dining experience visualization'\r\n      ],\r\n      industryTrends: ['Farm-to-table aesthetics', 'Minimalist plating influence', 'Chef celebrity culture', 'Sustainable dining', 'Global fusion']\r\n    },\r\n\r\n    'technology': {\r\n      name: 'Technology & Innovation',\r\n      worldClassBrands: ['Apple', 'Tesla', 'SpaceX', 'Google', 'Microsoft', 'Adobe'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Futuristic, clean, innovative, premium',\r\n        colorPalettes: ['Deep blues', 'Pure whites', 'Accent colors', 'Gradients', 'Neon highlights'],\r\n        typography: 'Modern sans-serifs, geometric precision, clean hierarchy',\r\n        imagery: 'Abstract tech elements, clean interfaces, innovation concepts, premium materials',\r\n        layout: 'Grid-based, clean lines, lots of white space, focused messaging',\r\n        creativeElements: ['Geometric shapes', 'Digital interfaces', 'Innovation metaphors', 'Premium materials', 'Future concepts']\r\n      },\r\n      creativityFrameworks: [\r\n        'Technology as art and innovation',\r\n        'Clean, premium aesthetic with bold innovation',\r\n        'Future-focused visual storytelling',\r\n        'Interface and product integration',\r\n        'Innovation and progress visualization'\r\n      ],\r\n      industryTrends: ['AI integration', 'Sustainable tech', 'Minimalist interfaces', 'Premium positioning', 'Innovation focus']\r\n    },\r\n\r\n    'healthcare': {\r\n      name: 'Healthcare & Wellness',\r\n      worldClassBrands: ['Mayo Clinic', 'Cleveland Clinic', 'Johns Hopkins', 'Stanford Health', 'Cleveland Clinic'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Trustworthy, caring, professional, accessible',\r\n        colorPalettes: ['Calming blues', 'Soft greens', 'Warm whites', 'Accent colors', 'Professional tones'],\r\n        typography: 'Clean, readable fonts, professional hierarchy, accessible sizing',\r\n        imagery: 'Caring professionals, modern facilities, wellness concepts, community health',\r\n        layout: 'Clean, organized, easy to navigate, trustworthy appearance',\r\n        creativeElements: ['Medical symbols', 'Wellness imagery', 'Community health', 'Professional care', 'Modern facilities']\r\n      },\r\n      creativityFrameworks: [\r\n        'Care and compassion through visual design',\r\n        'Trust and professionalism building',\r\n        'Wellness and health promotion',\r\n        'Community health engagement',\r\n        'Modern healthcare accessibility'\r\n      ],\r\n      industryTrends: ['Telehealth integration', 'Patient-centered care', 'Digital health', 'Wellness focus', 'Community health']\r\n    },\r\n\r\n    'fitness': {\r\n      name: 'Fitness & Wellness',\r\n      worldClassBrands: ['Peloton', 'Nike', 'Adidas', 'Equinox', 'Planet Fitness', 'CrossFit'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Energetic, motivational, premium, inclusive',\r\n        colorPalettes: ['Bold reds', 'Energetic oranges', 'Motivational yellows', 'Strong blacks', 'Accent colors'],\r\n        typography: 'Bold, energetic fonts, motivational messaging, strong hierarchy',\r\n        imagery: 'Action shots, diverse athletes, motivational scenes, fitness environments',\r\n        layout: 'Dynamic, energetic, motivational, inclusive',\r\n        creativeElements: ['Movement lines', 'Athletic energy', 'Diversity representation', 'Motivational elements', 'Fitness environments']\r\n      },\r\n      creativityFrameworks: [\r\n        'Energy and motivation through visual design',\r\n        'Inclusive fitness for all',\r\n        'Athletic achievement celebration',\r\n        'Community and belonging',\r\n        'Personal transformation stories'\r\n      ],\r\n      industryTrends: ['Digital fitness', 'Inclusive representation', 'Community building', 'Personal transformation', 'Wellness integration']\r\n    },\r\n\r\n    'finance': {\r\n      name: 'Finance & Banking',\r\n      worldClassBrands: ['Goldman Sachs', 'JP Morgan', 'Morgan Stanley', 'BlackRock', 'Visa', 'Mastercard'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Trustworthy, sophisticated, stable, premium',\r\n        colorPalettes: ['Deep blues', 'Professional grays', 'Gold accents', 'Clean whites', 'Trustworthy tones'],\r\n        typography: 'Professional serifs, clean sans-serifs, authoritative hierarchy',\r\n        imagery: 'Modern buildings, professional environments, growth concepts, stability symbols',\r\n        layout: 'Structured, professional, trustworthy, premium',\r\n        creativeElements: ['Financial symbols', 'Growth metaphors', 'Stability elements', 'Professional environments', 'Premium materials']\r\n      },\r\n      creativityFrameworks: [\r\n        'Trust and stability through design',\r\n        'Sophistication and premium positioning',\r\n        'Growth and progress visualization',\r\n        'Professional excellence',\r\n        'Financial security representation'\r\n      ],\r\n      industryTrends: ['Digital banking', 'Fintech innovation', 'Sustainable finance', 'Personal finance', 'Cryptocurrency']\r\n    },\r\n\r\n    'education': {\r\n      name: 'Education & Learning',\r\n      worldClassBrands: ['Harvard', 'MIT', 'Stanford', 'Coursera', 'Khan Academy', 'Duolingo'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Inspiring, accessible, modern, engaging',\r\n        colorPalettes: ['Inspiring blues', 'Creative purples', 'Warm oranges', 'Growth greens', 'Accent colors'],\r\n        typography: 'Readable fonts, inspiring hierarchy, accessible design',\r\n        imagery: 'Learning environments, diverse students, innovation concepts, growth metaphors',\r\n        layout: 'Engaging, organized, inspiring, accessible',\r\n        creativeElements: ['Learning symbols', 'Growth metaphors', 'Innovation elements', 'Diversity representation', 'Knowledge visualization']\r\n      },\r\n      creativityFrameworks: [\r\n        'Inspiration and learning through design',\r\n        'Accessibility and inclusion',\r\n        'Innovation and progress',\r\n        'Community and collaboration',\r\n        'Personal growth stories'\r\n      ],\r\n      industryTrends: ['Online learning', 'Personalized education', 'STEM focus', 'Global accessibility', 'Innovation in learning']\r\n    },\r\n\r\n    'retail': {\r\n      name: 'Retail & E-commerce',\r\n      worldClassBrands: ['Amazon', 'Apple', 'Nike', 'IKEA', 'Zara', 'Uniqlo'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Attractive, commercial, engaging, conversion-focused',\r\n        colorPalettes: ['Brand colors', 'Attractive accents', 'Commercial tones', 'Engaging highlights'],\r\n        typography: 'Commercial fonts, conversion-focused messaging, attractive hierarchy',\r\n        imagery: 'Product showcases, lifestyle integration, commercial appeal, brand personality',\r\n        layout: 'Commercial, attractive, conversion-optimized, engaging',\r\n        creativeElements: ['Product elements', 'Lifestyle integration', 'Commercial appeal', 'Brand personality', 'Conversion elements']\r\n      },\r\n      creativityFrameworks: [\r\n        'Commercial appeal and conversion',\r\n        'Brand personality expression',\r\n        'Lifestyle integration',\r\n        'Product storytelling',\r\n        'Customer engagement'\r\n      ],\r\n      industryTrends: ['E-commerce growth', 'Personalization', 'Sustainability', 'Mobile commerce', 'Social commerce']\r\n    },\r\n\r\n    'real estate': {\r\n      name: 'Real Estate & Property',\r\n      worldClassBrands: ['Sotheby\\'s', 'Christie\\'s', 'Douglas Elliman', 'Compass', 'Zillow'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Luxurious, aspirational, trustworthy, premium',\r\n        colorPalettes: ['Luxury golds', 'Sophisticated grays', 'Premium whites', 'Rich browns', 'Accent colors'],\r\n        typography: 'Luxury fonts, sophisticated hierarchy, premium appearance',\r\n        imagery: 'Luxury properties, premium environments, aspirational lifestyles, professional service',\r\n        layout: 'Luxurious, sophisticated, premium, aspirational',\r\n        creativeElements: ['Luxury elements', 'Premium materials', 'Aspirational lifestyles', 'Professional service', 'Property showcase']\r\n      },\r\n      creativityFrameworks: [\r\n        'Luxury and aspiration through design',\r\n        'Trust and professionalism',\r\n        'Premium positioning',\r\n        'Lifestyle visualization',\r\n        'Property storytelling'\r\n      ],\r\n      industryTrends: ['Digital property viewing', 'Sustainable properties', 'Luxury market growth', 'Technology integration', 'Global investment']\r\n    },\r\n\r\n    'default': {\r\n      name: 'Professional Services',\r\n      worldClassBrands: ['McKinsey', 'Bain', 'BCG', 'Deloitte', 'PwC', 'EY'],\r\n      designBenchmarks: {\r\n        visualStyle: 'Professional, trustworthy, modern, sophisticated',\r\n        colorPalettes: ['Professional blues', 'Trustworthy grays', 'Modern accents', 'Clean whites'],\r\n        typography: 'Professional fonts, clean hierarchy, trustworthy appearance',\r\n        imagery: 'Professional environments, modern offices, business concepts, trust symbols',\r\n        layout: 'Professional, organized, trustworthy, modern',\r\n        creativeElements: ['Professional elements', 'Business concepts', 'Trust symbols', 'Modern environments', 'Success indicators']\r\n      },\r\n      creativityFrameworks: [\r\n        'Professional excellence through design',\r\n        'Trust and credibility building',\r\n        'Modern sophistication',\r\n        'Business success visualization',\r\n        'Professional service representation'\r\n      ],\r\n      industryTrends: ['Digital transformation', 'Remote work', 'Sustainability', 'Innovation focus', 'Global expansion']\r\n    }\r\n  };\r\n\r\n  return industryIntelligence[businessType.toLowerCase()] || industryIntelligence['default'];\r\n}\r\n\r\n// NEW: Enhanced Creativity System with Industry Intelligence\r\nfunction getEnhancedCreativityFramework(businessType: string, designStyle: string, seed: number): any {\r\n  const industryIntel = getIndustryDesignIntelligence(businessType);\r\n\r\n  const creativityFrameworks = [\r\n    {\r\n      name: 'World-Class Benchmarking',\r\n      approach: `Study and emulate the design excellence of ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}`,\r\n      focus: 'Premium positioning, industry best practices, sophisticated aesthetics',\r\n      elements: industryIntel.designBenchmarks.creativeElements,\r\n      description: `Create designs that rival the sophistication and quality of ${industryIntel.name} industry leaders`\r\n    },\r\n    {\r\n      name: 'Industry Trend Integration',\r\n      approach: `Incorporate current ${industryIntel.name} trends: ${industryIntel.industryTrends.slice(0, 3).join(', ')}`,\r\n      focus: 'Modern relevance, industry innovation, forward-thinking design',\r\n      elements: ['Trend elements', 'Innovation concepts', 'Modern aesthetics', 'Industry relevance'],\r\n      description: 'Design that feels current and relevant to the industry while maintaining creativity'\r\n    },\r\n    {\r\n      name: 'Creative Storytelling',\r\n      approach: industryIntel.creativityFrameworks[seed % industryIntel.creativityFrameworks.length],\r\n      focus: 'Narrative design, emotional connection, brand storytelling',\r\n      elements: ['Story elements', 'Emotional triggers', 'Narrative flow', 'Brand personality'],\r\n      description: 'Use visual design to tell compelling stories that connect with the audience'\r\n    },\r\n    {\r\n      name: 'Innovation & Disruption',\r\n      approach: 'Challenge industry conventions with creative innovation',\r\n      focus: 'Breaking norms, creative disruption, unique positioning',\r\n      elements: ['Innovation elements', 'Disruptive concepts', 'Unique approaches', 'Creative risk-taking'],\r\n      description: 'Create designs that stand out by challenging industry conventions'\r\n    },\r\n    {\r\n      name: 'Cultural & Global Fusion',\r\n      approach: 'Blend local cultural elements with global industry standards',\r\n      focus: 'Cultural authenticity, global relevance, unique positioning',\r\n      elements: ['Cultural elements', 'Global standards', 'Local authenticity', 'Fusion concepts'],\r\n      description: 'Create designs that feel both locally authentic and globally competitive'\r\n    }\r\n  ];\r\n\r\n  return creativityFrameworks[seed % creativityFrameworks.length];\r\n}\r\n\r\n// NEW: Industry-Specific Design Enhancement\r\nfunction enhanceDesignWithIndustryIntelligence(designPrompt: string, businessType: string, designStyle: string, seed: number): string {\r\n  const industryIntel = getIndustryDesignIntelligence(businessType);\r\n  const creativityFramework = getEnhancedCreativityFramework(businessType, designStyle, seed);\r\n\r\n  const industryEnhancement = `\r\n🏭 INDUSTRY INTELLIGENCE INTEGRATION:\r\n**Industry:** ${industryIntel.name}\r\n**World-Class Benchmarks:** ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}\r\n**Industry Visual Style:** ${industryIntel.designBenchmarks.visualStyle}\r\n**Industry Color Palettes:** ${industryIntel.designBenchmarks.colorPalettes.slice(0, 3).join(', ')}\r\n**Industry Typography:** ${industryIntel.designBenchmarks.typography}\r\n**Industry Imagery:** ${industryIntel.designBenchmarks.imagery}\r\n**Industry Layout:** ${industryIntel.designBenchmarks.layout}\r\n\r\n🎨 CREATIVITY FRAMEWORK: ${creativityFramework.name}\r\n**Approach:** ${creativityFramework.approach}\r\n**Focus:** ${creativityFramework.focus}\r\n**Creative Elements:** ${creativityFramework.elements.slice(0, 3).join(', ')}\r\n**Description:** ${creativityFramework.description}\r\n\r\n🚀 INDUSTRY TRENDS TO INCORPORATE:\r\n${industryIntel.industryTrends.slice(0, 3).map((trend, i) => `${i + 1}. ${trend}`).join('\\n')}\r\n\r\n🎯 DESIGN REQUIREMENTS:\r\n- **Industry Benchmarking:** Create designs that rival ${industryIntel.name} industry leaders\r\n- **Trend Integration:** Incorporate current industry trends naturally\r\n- **Creative Innovation:** Use ${creativityFramework.name} approach for unique positioning\r\n- **Quality Standards:** Match world-class design quality and sophistication\r\n- **Industry Relevance:** Ensure design feels authentic to ${industryIntel.name} industry`;\r\n\r\n  return designPrompt + industryEnhancement;\r\n}\r\n\r\n// NEW: Business Intelligence Engine - Local Marketing Expert System\r\nfunction getBusinessIntelligenceEngine(businessType: string, location: string): any {\r\n  const businessIntelligence: Record<string, any> = {\r\n    'restaurant': {\r\n      name: 'Restaurant & Food Service',\r\n      localExpertise: {\r\n        experience: '25+ years in hospitality and culinary marketing',\r\n        marketDynamics: [\r\n          'Seasonal menu optimization and local ingredient sourcing',\r\n          'Customer loyalty programs and repeat business strategies',\r\n          'Local competition analysis and unique positioning',\r\n          'Food trends and cultural preferences in the area',\r\n          'Pricing strategies for local market conditions'\r\n        ],\r\n        localPhrases: [\r\n          'Taste of [location]',\r\n          'Where locals eat',\r\n          'Fresh from our kitchen',\r\n          'Made with love',\r\n          'Family recipe',\r\n          'Local favorite',\r\n          'Chef\\'s special',\r\n          'Daily fresh',\r\n          'Home-cooked taste',\r\n          'Local ingredients'\r\n        ],\r\n        contentStrategies: [\r\n          'Behind-the-scenes kitchen stories',\r\n          'Chef personality and cooking philosophy',\r\n          'Local ingredient sourcing stories',\r\n          'Customer testimonials and success stories',\r\n          'Seasonal menu highlights',\r\n          'Local food culture integration',\r\n          'Community involvement and events',\r\n          'Sustainability and local farming partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Food memories and nostalgia',\r\n          'Local pride and community connection',\r\n          'Health and wellness benefits',\r\n          'Family traditions and gatherings',\r\n          'Adventure and trying new flavors',\r\n          'Social sharing and food photography',\r\n          'Exclusive offers and VIP experiences',\r\n          'Local events and celebrations'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'technology': {\r\n      name: 'Technology & Innovation',\r\n      localExpertise: {\r\n        experience: '22+ years in tech marketing and digital transformation',\r\n        marketDynamics: [\r\n          'Local tech ecosystem and startup culture',\r\n          'Digital adoption rates in the region',\r\n          'Competitive landscape and innovation gaps',\r\n          'Local talent pool and skill development',\r\n          'Government tech initiatives and support'\r\n        ],\r\n        localPhrases: [\r\n          'Innovation hub',\r\n          'Digital transformation',\r\n          'Tech-forward solutions',\r\n          'Future-ready',\r\n          'Smart [location]',\r\n          'Digital innovation',\r\n          'Tech excellence',\r\n          'Innovation center',\r\n          'Digital leadership',\r\n          'Tech ecosystem'\r\n        ],\r\n        contentStrategies: [\r\n          'Local tech success stories',\r\n          'Innovation case studies',\r\n          'Digital transformation journeys',\r\n          'Tech talent development',\r\n          'Local startup ecosystem',\r\n          'Government tech partnerships',\r\n          'Digital skills training',\r\n          'Smart city initiatives'\r\n        ],\r\n        engagementHooks: [\r\n          'Career advancement and skill development',\r\n          'Innovation and future thinking',\r\n          'Local tech community building',\r\n          'Digital transformation success',\r\n          'Tech entrepreneurship',\r\n          'Smart city development',\r\n          'Digital inclusion',\r\n          'Tech for social good'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'healthcare': {\r\n      name: 'Healthcare & Wellness',\r\n      localExpertise: {\r\n        experience: '28+ years in healthcare marketing and patient care',\r\n        marketDynamics: [\r\n          'Local health demographics and needs',\r\n          'Healthcare accessibility and insurance coverage',\r\n          'Competing healthcare providers and services',\r\n          'Local health trends and concerns',\r\n          'Community health initiatives and partnerships'\r\n        ],\r\n        localPhrases: [\r\n          'Your health, our priority',\r\n          'Caring for [location] families',\r\n          'Local healthcare excellence',\r\n          'Community health partner',\r\n          'Your wellness journey',\r\n          'Health close to home',\r\n          'Caring professionals',\r\n          'Local health experts',\r\n          'Community wellness',\r\n          'Health for everyone'\r\n        ],\r\n        contentStrategies: [\r\n          'Patient success stories and testimonials',\r\n          'Local health education and prevention',\r\n          'Community health initiatives',\r\n          'Healthcare professional spotlights',\r\n          'Local health trends and insights',\r\n          'Wellness tips and advice',\r\n          'Health technology integration',\r\n          'Community partnerships and events'\r\n        ],\r\n        engagementHooks: [\r\n          'Family health and wellness',\r\n          'Preventive care and early detection',\r\n          'Local health community',\r\n          'Professional healthcare expertise',\r\n          'Health technology innovation',\r\n          'Community health improvement',\r\n          'Patient-centered care',\r\n          'Health education and awareness'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'fitness': {\r\n      name: 'Fitness & Wellness',\r\n      localExpertise: {\r\n        experience: '24+ years in fitness marketing and community building',\r\n        marketDynamics: [\r\n          'Local fitness culture and preferences',\r\n          'Competing gyms and fitness options',\r\n          'Seasonal fitness trends and activities',\r\n          'Local sports teams and community events',\r\n          'Health awareness and wellness trends'\r\n        ],\r\n        localPhrases: [\r\n          'Your fitness journey starts here',\r\n          'Stronger [location] community',\r\n          'Local fitness excellence',\r\n          'Your wellness partner',\r\n          'Fitness for everyone',\r\n          'Local strength',\r\n          'Community fitness',\r\n          'Your health transformation',\r\n          'Local fitness family',\r\n          'Wellness close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Member transformation stories',\r\n          'Local fitness challenges and events',\r\n          'Community fitness initiatives',\r\n          'Trainer spotlights and expertise',\r\n          'Local sports team partnerships',\r\n          'Seasonal fitness programs',\r\n          'Wellness education and tips',\r\n          'Community health partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Personal transformation and goals',\r\n          'Community fitness challenges',\r\n          'Local sports pride',\r\n          'Health and wellness education',\r\n          'Fitness community building',\r\n          'Seasonal fitness motivation',\r\n          'Professional training expertise',\r\n          'Inclusive fitness for all'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'finance': {\r\n      name: 'Finance & Banking',\r\n      localExpertise: {\r\n        experience: '26+ years in financial services and local banking',\r\n        marketDynamics: [\r\n          'Local economic conditions and growth',\r\n          'Competing financial institutions',\r\n          'Local business financing needs',\r\n          'Personal finance trends in the area',\r\n          'Community investment opportunities'\r\n        ],\r\n        localPhrases: [\r\n          'Your financial partner in [location]',\r\n          'Local financial expertise',\r\n          'Community banking excellence',\r\n          'Your financial future',\r\n          'Local financial solutions',\r\n          'Community financial partner',\r\n          'Your money, our care',\r\n          'Local financial guidance',\r\n          'Community wealth building',\r\n          'Financial security close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local business success stories',\r\n          'Financial education and literacy',\r\n          'Community investment initiatives',\r\n          'Local economic insights',\r\n          'Personal finance success stories',\r\n          'Business financing solutions',\r\n          'Local financial trends',\r\n          'Community financial partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Financial security and planning',\r\n          'Local business growth',\r\n          'Community economic development',\r\n          'Personal finance education',\r\n          'Investment opportunities',\r\n          'Business financing solutions',\r\n          'Local economic pride',\r\n          'Financial wellness for families'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'education': {\r\n      name: 'Education & Learning',\r\n      localExpertise: {\r\n        experience: '23+ years in educational marketing and community learning',\r\n        marketDynamics: [\r\n          'Local education standards and performance',\r\n          'Competing educational institutions',\r\n          'Local learning needs and preferences',\r\n          'Community education initiatives',\r\n          'Employment and skill development needs'\r\n        ],\r\n        localPhrases: [\r\n          'Learning excellence in [location]',\r\n          'Your educational journey',\r\n          'Local learning excellence',\r\n          'Community education partner',\r\n          'Your learning success',\r\n          'Local educational leadership',\r\n          'Community learning center',\r\n          'Your knowledge partner',\r\n          'Local educational excellence',\r\n          'Learning close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Student success stories',\r\n          'Local educational achievements',\r\n          'Community learning initiatives',\r\n          'Educational innovation and technology',\r\n          'Local employment partnerships',\r\n          'Skill development programs',\r\n          'Community education events',\r\n          'Local learning trends'\r\n        ],\r\n        engagementHooks: [\r\n          'Personal growth and development',\r\n          'Career advancement opportunities',\r\n          'Local educational pride',\r\n          'Community learning initiatives',\r\n          'Innovation in education',\r\n          'Skill development and training',\r\n          'Local employment success',\r\n          'Educational excellence recognition'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'retail': {\r\n      name: 'Retail & E-commerce',\r\n      localExpertise: {\r\n        experience: '25+ years in retail marketing and customer experience',\r\n        marketDynamics: [\r\n          'Local shopping preferences and trends',\r\n          'Competing retail options and malls',\r\n          'Local economic conditions and spending',\r\n          'Seasonal shopping patterns',\r\n          'Community shopping habits and events'\r\n        ],\r\n        localPhrases: [\r\n          'Your local shopping destination',\r\n          'Shopping excellence in [location]',\r\n          'Local retail leadership',\r\n          'Your shopping partner',\r\n          'Local retail excellence',\r\n          'Community shopping center',\r\n          'Your retail destination',\r\n          'Local shopping experience',\r\n          'Community retail partner',\r\n          'Shopping close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local product highlights',\r\n          'Customer success stories',\r\n          'Community shopping events',\r\n          'Local brand partnerships',\r\n          'Seasonal shopping guides',\r\n          'Local shopping trends',\r\n          'Community retail initiatives',\r\n          'Local customer appreciation'\r\n        ],\r\n        engagementHooks: [\r\n          'Local product discovery',\r\n          'Community shopping events',\r\n          'Seasonal shopping excitement',\r\n          'Local brand support',\r\n          'Customer appreciation',\r\n          'Shopping convenience',\r\n          'Local retail pride',\r\n          'Community shopping experience'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'real estate': {\r\n      name: 'Real Estate & Property',\r\n      localExpertise: {\r\n        experience: '27+ years in real estate marketing and local property',\r\n        marketDynamics: [\r\n          'Local property market conditions',\r\n          'Competing real estate agencies',\r\n          'Local property trends and values',\r\n          'Community development and growth',\r\n          'Local investment opportunities'\r\n        ],\r\n        localPhrases: [\r\n          'Your local real estate expert',\r\n          'Real estate excellence in [location]',\r\n          'Local property specialist',\r\n          'Your property partner',\r\n          'Local real estate leadership',\r\n          'Community property expert',\r\n          'Your real estate guide',\r\n          'Local property excellence',\r\n          'Community real estate partner',\r\n          'Property close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local property success stories',\r\n          'Community development updates',\r\n          'Local property market insights',\r\n          'Property investment opportunities',\r\n          'Local neighborhood highlights',\r\n          'Community real estate events',\r\n          'Local property trends',\r\n          'Community property partnerships'\r\n        ],\r\n        engagementHooks: [\r\n          'Property investment opportunities',\r\n          'Local neighborhood pride',\r\n          'Community development',\r\n          'Property market insights',\r\n          'Local real estate success',\r\n          'Community property events',\r\n          'Property investment guidance',\r\n          'Local real estate expertise'\r\n        ]\r\n      }\r\n    },\r\n\r\n    'default': {\r\n      name: 'Professional Services',\r\n      localExpertise: {\r\n        experience: '20+ years in professional services and local business',\r\n        marketDynamics: [\r\n          'Local business environment and competition',\r\n          'Community business needs and trends',\r\n          'Local economic conditions',\r\n          'Business development opportunities',\r\n          'Community partnerships and networking'\r\n        ],\r\n        localPhrases: [\r\n          'Your local business partner',\r\n          'Professional excellence in [location]',\r\n          'Local business expertise',\r\n          'Your success partner',\r\n          'Local professional leadership',\r\n          'Community business partner',\r\n          'Your business guide',\r\n          'Local professional excellence',\r\n          'Community business expert',\r\n          'Success close to home'\r\n        ],\r\n        contentStrategies: [\r\n          'Local business success stories',\r\n          'Community business initiatives',\r\n          'Local business insights',\r\n          'Business development opportunities',\r\n          'Local business trends',\r\n          'Community business events',\r\n          'Local business partnerships',\r\n          'Community business support'\r\n        ],\r\n        engagementHooks: [\r\n          'Business growth and success',\r\n          'Local business community',\r\n          'Professional development',\r\n          'Business opportunities',\r\n          'Local business pride',\r\n          'Community business support',\r\n          'Business innovation',\r\n          'Local business expertise'\r\n        ]\r\n      }\r\n    },\r\n    'financial technology software': {\r\n      name: 'Financial Technology Software',\r\n      localExpertise: {\r\n        experience: '15+ years in fintech and digital payments',\r\n        marketDynamics: [\r\n          'Digital payment adoption rates in the region',\r\n          'Mobile banking and fintech competition',\r\n          'Financial inclusion and accessibility needs',\r\n          'Regulatory compliance and security requirements',\r\n          'Local banking partnerships and integrations'\r\n        ],\r\n        contentStrategies: [\r\n          'Digital financial innovation',\r\n          'Payment security and trust',\r\n          'Financial inclusion stories',\r\n          'Fintech industry insights',\r\n          'User experience excellence',\r\n          'Local market expansion',\r\n          'Partnership announcements',\r\n          'Technology advancement'\r\n        ],\r\n        engagementHooks: [\r\n          'Financial innovation',\r\n          'Digital payments',\r\n          'Financial inclusion',\r\n          'Secure transactions',\r\n          'Fintech solutions',\r\n          'Payment convenience',\r\n          'Financial empowerment',\r\n          'Digital banking'\r\n        ]\r\n      },\r\n      localPhrases: [\r\n        'Your digital payment partner',\r\n        'Fintech innovation in [location]',\r\n        'Digital financial solutions',\r\n        'Your payment solution',\r\n        'Financial technology excellence',\r\n        'Digital banking for [location]',\r\n        'Your fintech partner',\r\n        'Payment innovation'\r\n      ]\r\n    },\r\n    'default': {\r\n      name: 'Professional Services',\r\n      localExpertise: {\r\n        experience: '20+ years in professional services',\r\n        marketDynamics: [\r\n          'Local business environment and competition',\r\n          'Market trends and opportunities',\r\n          'Customer needs and preferences',\r\n          'Industry best practices and standards',\r\n          'Local economic conditions and growth'\r\n        ],\r\n        contentStrategies: [\r\n          'Professional excellence and expertise',\r\n          'Client success stories',\r\n          'Industry insights and trends',\r\n          'Local market knowledge',\r\n          'Service quality and reliability',\r\n          'Innovation and solutions',\r\n          'Community involvement',\r\n          'Professional development'\r\n        ],\r\n        engagementHooks: [\r\n          'Professional excellence',\r\n          'Client success',\r\n          'Industry expertise',\r\n          'Local market knowledge',\r\n          'Quality service',\r\n          'Innovation solutions',\r\n          'Community partnership',\r\n          'Professional growth'\r\n        ]\r\n      },\r\n      localPhrases: [\r\n        'Your local professional partner',\r\n        'Excellence in [location]',\r\n        'Local expertise you can trust',\r\n        'Your success partner',\r\n        'Professional solutions for [location]',\r\n        'Local industry leadership',\r\n        'Your trusted advisor',\r\n        'Professional excellence'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const result = businessIntelligence[businessType.toLowerCase()] || businessIntelligence['default'];\r\n  return result;\r\n}\r\n\r\n// NEW: Dynamic Content Strategy Engine - Never Repetitive\r\nfunction getDynamicContentStrategy(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n\r\n  const contentStrategies = [\r\n    {\r\n      name: 'Local Market Expert',\r\n      approach: `Position as the ${businessIntel.name} expert in ${location} with ${businessIntel.localExpertise.experience}`,\r\n      focus: 'Local expertise, community knowledge, market insights',\r\n      hooks: businessIntel.localExpertise.engagementHooks.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['local expertise', 'community focused', 'trusted service', 'proven results']).slice(0, 4),\r\n      description: `Write like a ${businessIntel.localExpertise.experience} professional who knows ${location} inside and out`\r\n    },\r\n    {\r\n      name: 'Community Storyteller',\r\n      approach: `Share authentic stories about local ${businessIntel.name} success and community impact`,\r\n      focus: 'Real stories, community connection, authentic experiences',\r\n      hooks: businessIntel.localExpertise.engagementHooks.slice(4, 8),\r\n      phrases: (businessIntel.localPhrases || ['community stories', 'local success', 'authentic experiences', 'real results']).slice(4, 8),\r\n      description: 'Share real, relatable stories that connect with the local community'\r\n    },\r\n    {\r\n      name: 'Industry Innovator',\r\n      approach: `Showcase cutting-edge ${businessIntel.name} solutions and industry leadership`,\r\n      focus: 'Innovation, industry trends, competitive advantage',\r\n      hooks: businessIntel.localExpertise.contentStrategies.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['innovative solutions', 'industry leader', 'cutting-edge', 'advanced technology']).slice(0, 4),\r\n      description: 'Position as an industry leader with innovative solutions and insights'\r\n    },\r\n    {\r\n      name: 'Problem Solver',\r\n      approach: `Address specific ${businessIntel.name} challenges that local businesses and people face`,\r\n      focus: 'Problem identification, solution offering, value demonstration',\r\n      hooks: businessIntel.localExpertise.marketDynamics.slice(0, 4),\r\n      phrases: (businessIntel.localPhrases || ['problem solver', 'effective solutions', 'proven results', 'reliable service']).slice(0, 4),\r\n      description: 'Identify and solve real problems that matter to the local community'\r\n    },\r\n    {\r\n      name: 'Success Catalyst',\r\n      approach: `Inspire and guide local ${businessIntel.name} success through proven strategies`,\r\n      focus: 'Success stories, proven methods, inspirational guidance',\r\n      hooks: businessIntel.localExpertise.contentStrategies.slice(4, 8),\r\n      phrases: (businessIntel.localPhrases || ['success catalyst', 'proven strategies', 'inspiring results', 'growth partner']).slice(4, 8),\r\n      description: 'Inspire success through proven strategies and real results'\r\n    }\r\n  ];\r\n\r\n  return contentStrategies[seed % contentStrategies.length];\r\n}\r\n\r\n// NEW: Human Writing Style Generator - Authentic, Engaging\r\nfunction getHumanWritingStyle(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n\r\n  const writingStyles = [\r\n    {\r\n      name: 'Conversational Expert',\r\n      tone: 'Friendly, knowledgeable, approachable',\r\n      voice: `Like a ${businessIntel.localExpertise.experience} professional chatting with a friend over coffee`,\r\n      characteristics: [\r\n        'Use local phrases naturally',\r\n        'Share personal insights and experiences',\r\n        'Ask engaging questions',\r\n        'Use conversational language',\r\n        'Show genuine enthusiasm for the business'\r\n      ],\r\n      examples: [\r\n        `\"You know what I love about ${location}? The way our community...\"`,\r\n        `\"After ${businessIntel.localExpertise.experience} in this industry, I've learned...\"`,\r\n        `\"Here's something that always makes me smile about our business...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Storytelling Mentor',\r\n      tone: 'Inspirational, narrative, engaging',\r\n      voice: 'Like sharing a compelling story that teaches and inspires',\r\n      characteristics: [\r\n        'Start with intriguing hooks',\r\n        'Build narrative tension',\r\n        'Include relatable characters',\r\n        'End with meaningful insights',\r\n        'Use vivid, descriptive language'\r\n      ],\r\n      examples: [\r\n        `\"Last week, something incredible happened that reminded me why...\"`,\r\n        `\"I'll never forget the day when...\"`,\r\n        `\"There's a story behind every success, and this one...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Local Champion',\r\n      tone: 'Proud, community-focused, authentic',\r\n      voice: 'Like a proud local business owner celebrating community success',\r\n      characteristics: [\r\n        'Celebrate local achievements',\r\n        'Use local pride and identity',\r\n        'Highlight community connections',\r\n        'Show genuine local love',\r\n        'Connect business to community values'\r\n      ],\r\n      examples: [\r\n        `\"This is why I'm so proud to be part of the ${location} community...\"`,\r\n        `\"Our ${location} neighbors never cease to amaze me...\"`,\r\n        `\"There's something special about doing business in ${location}...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Problem-Solving Partner',\r\n      tone: 'Helpful, solution-oriented, trustworthy',\r\n      voice: 'Like a trusted advisor helping solve real problems',\r\n      characteristics: [\r\n        'Identify real problems',\r\n        'Offer practical solutions',\r\n        'Show understanding and empathy',\r\n        'Build trust through expertise',\r\n        'Focus on customer benefit'\r\n      ],\r\n      examples: [\r\n        `\"I've noticed that many ${location} businesses struggle with...\"`,\r\n        `\"Here's a solution that's worked for countless local businesses...\"`,\r\n        `\"Let me share what I've learned about solving this common challenge...\"`\r\n      ]\r\n    },\r\n    {\r\n      name: 'Success Celebrator',\r\n      tone: 'Enthusiastic, celebratory, motivational',\r\n      voice: 'Like celebrating wins and inspiring future success',\r\n      characteristics: [\r\n        'Celebrate achievements',\r\n        'Share success stories',\r\n        'Inspire future action',\r\n        'Use positive, uplifting language',\r\n        'Connect success to community'\r\n      ],\r\n      examples: [\r\n        `\"I'm thrilled to share some amazing news from our ${location} community...\"`,\r\n        `\"This success story is exactly why I love ${businessIntel.name} in ${location}...\"`,\r\n        `\"Let's celebrate this incredible achievement together...\"`\r\n      ]\r\n    }\r\n  ];\r\n\r\n  return writingStyles[seed % writingStyles.length];\r\n}\r\n\r\n// NEW: Anti-Repetition Content Engine\r\nfunction generateUniqueContentVariation(businessType: string, location: string, seed: number): any {\r\n  const businessIntel = getBusinessIntelligenceEngine(businessType, location);\r\n  const contentStrategy = getDynamicContentStrategy(businessType, location, seed);\r\n  const writingStyle = getHumanWritingStyle(businessType, location, seed);\r\n\r\n  // Generate unique content angle based on multiple factors\r\n  const contentAngles = [\r\n    {\r\n      type: 'Local Insight',\r\n      focus: `Share unique ${businessIntel.name} insights specific to ${location}`,\r\n      examples: [\r\n        `\"What I've learned about ${businessIntel.name} in ${location} after ${businessIntel.localExpertise.experience}...\"`,\r\n        `\"The ${businessIntel.name} landscape in ${location} is unique because...\"`,\r\n        `\"Here's what makes ${location} special for ${businessIntel.name} businesses...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Community Story',\r\n      focus: `Tell a compelling story about local ${businessIntel.name} impact`,\r\n      examples: [\r\n        `\"Last month, something incredible happened in our ${location} community...\"`,\r\n        `\"I want to share a story that perfectly captures why we do what we do...\"`,\r\n        `\"This is the kind of moment that makes ${businessIntel.name} in ${location} special...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Industry Innovation',\r\n      focus: `Showcase cutting-edge ${businessIntel.name} solutions`,\r\n      examples: [\r\n        `\"We're excited to introduce something that's changing ${businessIntel.name} in ${location}...\"`,\r\n        `\"Here's how we're innovating in the ${businessIntel.name} space...\"`,\r\n        `\"This new approach is revolutionizing how we do ${businessIntel.name} in ${location}...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Problem Solution',\r\n      focus: `Address specific ${businessIntel.name} challenges in ${location}`,\r\n      examples: [\r\n        `\"I've noticed that many ${location} businesses struggle with...\"`,\r\n        `\"Here's a common challenge in ${businessIntel.name} and how we solve it...\"`,\r\n        `\"Let me share what I've learned about overcoming this ${businessIntel.name} obstacle...\"`\r\n      ]\r\n    },\r\n    {\r\n      type: 'Success Celebration',\r\n      focus: `Celebrate local ${businessIntel.name} achievements`,\r\n      examples: [\r\n        `\"I'm thrilled to share some amazing news from our ${location} community...\"`,\r\n        `\"This success story is exactly why I love ${businessIntel.name} in ${location}...\"`,\r\n        `\"Let's celebrate this incredible achievement together...\"`\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const selectedAngle = contentAngles[seed % contentAngles.length];\r\n\r\n  return {\r\n    contentStrategy: contentStrategy,\r\n    writingStyle: writingStyle,\r\n    contentAngle: selectedAngle,\r\n    uniqueSignature: `${selectedAngle.type}-${contentStrategy.name}-${writingStyle.name}-${seed}`,\r\n    localPhrases: (businessIntel.localPhrases || ['professional service', 'quality results', 'trusted expertise']).slice(0, 3),\r\n    engagementHooks: businessIntel.localExpertise.engagementHooks.slice(0, 3),\r\n    marketInsights: businessIntel.localExpertise.marketDynamics.slice(0, 2)\r\n  };\r\n}\r\n\r\n// Helper functions for context generation\r\nfunction getSeason(): string {\r\n  const month = new Date().getMonth();\r\n  if (month >= 2 && month <= 4) return 'Spring';\r\n  if (month >= 5 && month <= 7) return 'Summer';\r\n  if (month >= 8 && month <= 10) return 'Fall';\r\n  return 'Winter';\r\n}\r\n\r\nfunction getTimeOfDay(): string {\r\n  const hour = new Date().getHours();\r\n  if (hour >= 5 && hour < 12) return 'Morning';\r\n  if (hour >= 12 && hour < 17) return 'Afternoon';\r\n  if (hour >= 17 && hour < 21) return 'Evening';\r\n  return 'Night';\r\n}\r\n\r\nfunction generateContextualTrends(businessType: string, location: string): any[] {\r\n  const trends = [\r\n    { topic: `${businessType} innovation trends`, category: 'Industry', relevance: 'high' },\r\n    { topic: `${location} business growth`, category: 'Local', relevance: 'high' },\r\n    { topic: 'Digital transformation', category: 'Technology', relevance: 'medium' },\r\n    { topic: 'Customer experience optimization', category: 'Business', relevance: 'high' },\r\n    { topic: 'Sustainable business practices', category: 'Trends', relevance: 'medium' }\r\n  ];\r\n  return trends.slice(0, 3);\r\n}\r\n\r\nfunction generateWeatherContext(location: string): any {\r\n  // Simplified weather context based on location and season\r\n  const season = getSeason();\r\n  const contexts = {\r\n    'Spring': { condition: 'Fresh and energizing', business_impact: 'New beginnings, growth opportunities', content_opportunities: 'Renewal, fresh starts, growth themes' },\r\n    'Summer': { condition: 'Bright and active', business_impact: 'High energy, outdoor activities', content_opportunities: 'Vibrant colors, active lifestyle, summer solutions' },\r\n    'Fall': { condition: 'Cozy and productive', business_impact: 'Planning, preparation, harvest', content_opportunities: 'Preparation, results, autumn themes' },\r\n    'Winter': { condition: 'Focused and strategic', business_impact: 'Planning, reflection, indoor focus', content_opportunities: 'Planning, strategy, winter solutions' }\r\n  };\r\n\r\n  return {\r\n    temperature: '22',\r\n    condition: contexts[season as keyof typeof contexts].condition,\r\n    business_impact: contexts[season as keyof typeof contexts].business_impact,\r\n    content_opportunities: contexts[season as keyof typeof contexts].content_opportunities\r\n  };\r\n}\r\n\r\nfunction generateLocalOpportunities(businessType: string, location: string): any[] {\r\n  const opportunities = [\r\n    { name: `${location} Business Expo`, venue: 'Local Convention Center', relevance: 'networking' },\r\n    { name: `${businessType} Innovation Summit`, venue: 'Business District', relevance: 'industry' },\r\n    { name: 'Local Entrepreneur Meetup', venue: 'Community Center', relevance: 'community' }\r\n  ];\r\n  return opportunities.slice(0, 2);\r\n}\r\n\r\n// Get API keys (supporting both server-side and client-side)\r\nconst apiKey =\r\n  process.env.GEMINI_API_KEY ||\r\n  process.env.GOOGLE_API_KEY ||\r\n  process.env.GOOGLE_GENAI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GEMINI_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_API_KEY ||\r\n  process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;\r\n\r\nif (!apiKey) {\r\n}\r\n\r\n// Initialize Google GenAI client with Revo 1.0 configuration\r\nconst ai = new GoogleGenerativeAI(apiKey);\r\n\r\n// Revo 1.0 uses Gemini 2.5 Flash Image Preview\r\nconst REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\n/**\r\n * Generate content using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Content(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  writingTone: string;\r\n  contentThemes: string[];\r\n  targetAudience: string;\r\n  services: string;\r\n  keyFeatures: string;\r\n  competitiveAdvantages: string;\r\n  dayOfWeek: string;\r\n  currentDate: string;\r\n  primaryColor?: string;\r\n  visualStyle?: string;\r\n}) {\r\n  try {\r\n    // Convert input to BusinessProfile for advanced analysis\r\n    const businessProfile: BusinessProfile = {\r\n      businessName: input.businessName,\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience,\r\n      brandVoice: input.writingTone,\r\n      uniqueSellingPoints: [input.competitiveAdvantages || 'Quality service'],\r\n      competitors: [], // Could be enhanced with competitor data\r\n    };\r\n\r\n    // 📊 GENERATE ADVANCED CONTENT WITH DEEP ANALYSIS\r\n    const advancedContent = await advancedContentGenerator.generateEngagingContent(\r\n      businessProfile,\r\n      input.platform,\r\n      'promotional'\r\n    );\r\n\r\n    // 🎯 GET TRENDING INSIGHTS FOR ENHANCED RELEVANCE\r\n    const trendingEnhancement = await trendingEnhancer.getTrendingEnhancement({\r\n      businessType: input.businessType,\r\n      platform: input.platform,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience,\r\n    });\r\n\r\n    // 📈 ANALYZE PERFORMANCE FOR CONTINUOUS IMPROVEMENT\r\n    const performanceAnalysis = performanceAnalyzer.analyzePerformance(\r\n      advancedContent,\r\n      businessProfile\r\n    );\r\n\r\n    // Extract hashtags from advanced content for use in business-specific generation\r\n    const hashtags = advancedContent.hashtags;\r\n\r\n    // Gather real-time context data (keeping existing functionality)\r\n    const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the content generation prompt with enhanced brand context\r\n    const contentPrompt = revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE\r\n      .replace('{businessName}', input.businessName)\r\n      .replace('{businessType}', input.businessType)\r\n      .replace('{platform}', input.platform)\r\n      .replace('{writingTone}', input.writingTone)\r\n      .replace('{location}', input.location)\r\n      .replace('{primaryColor}', input.primaryColor || '#3B82F6')\r\n      .replace('{visualStyle}', input.visualStyle || 'modern')\r\n      .replace('{targetAudience}', input.targetAudience)\r\n      .replace('{services}', input.services || '')\r\n      .replace('{keyFeatures}', input.keyFeatures || '')\r\n      .replace('{competitiveAdvantages}', input.competitiveAdvantages || '')\r\n      .replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');\r\n\r\n\r\n    // 🎨 CREATIVE CAPTION GENERATION: Apply creative enhancement system\r\n\r\n    // NEW: Get business intelligence and local marketing expertise\r\n    const businessIntel = getBusinessIntelligenceEngine(input.businessType, input.location);\r\n    const randomSeed = Math.floor(Math.random() * 10000) + Date.now();\r\n    const uniqueContentVariation = generateUniqueContentVariation(input.businessType, input.location, randomSeed % 1000);\r\n\r\n\r\n    // 🎯 NEW: Generate business-specific content strategy\r\n\r\n    const businessDetails = {\r\n      experience: '5+ years', // Could be extracted from business profile\r\n      expertise: input.keyFeatures,\r\n      services: input.services,\r\n      location: input.location,\r\n      targetAudience: input.targetAudience\r\n    };\r\n\r\n    // Generate strategic content plan based on business type and goals\r\n    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness' // Can be dynamic based on business goals\r\n    );\r\n\r\n\r\n    // 🎨 NEW: Generate business-specific headlines and subheadlines with AI\r\n\r\n    const businessHeadline = await generateBusinessSpecificHeadline(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n    const businessSubheadline = await generateBusinessSpecificSubheadline(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      businessHeadline.headline,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n\r\n    // 📝 NEW: Generate AI-powered business-specific caption\r\n\r\n    const businessCaption = await generateBusinessSpecificCaption(\r\n      input.businessType,\r\n      input.businessName,\r\n      input.location,\r\n      businessDetails,\r\n      input.platform,\r\n      'awareness',\r\n      trendingEnhancement,\r\n      advancedContent\r\n    );\r\n\r\n\r\n    // 🎯 BUSINESS-SPECIFIC CAPTION GENERATION COMPLETE\r\n\r\n    // 🎯 BUSINESS-SPECIFIC CONTENT GENERATION COMPLETE\r\n\r\n    // 🎯 FINAL: Return business-specific content package\r\n\r\n    const finalContent = {\r\n      content: businessCaption.caption,\r\n      headline: businessHeadline.headline,\r\n      subheadline: businessSubheadline.subheadline,\r\n      callToAction: businessCaption.callToAction,\r\n      hashtags: hashtags,\r\n      catchyWords: businessHeadline.headline, // Use business-specific headline\r\n      contentStrategy: contentPlan.strategy,\r\n      businessStrengths: contentPlan.businessStrengths,\r\n      marketOpportunities: contentPlan.marketOpportunities,\r\n      valueProposition: contentPlan.valueProposition,\r\n      platform: input.platform,\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      realTimeContext: realTimeContext, // Pass context to image generator\r\n      creativeContext: { // Enhanced creative context for image generation\r\n        style: businessHeadline.approach,\r\n        tone: businessHeadline.emotionalImpact,\r\n        framework: businessSubheadline.framework,\r\n        businessInsights: contentPlan,\r\n        variation: uniqueContentVariation\r\n      },\r\n      // 🧠 BUSINESS INTELLIGENCE DATA\r\n      businessIntelligence: {\r\n        contentGoal: contentPlan.strategy.goal,\r\n        businessStrengths: contentPlan.businessStrengths,\r\n        marketOpportunities: contentPlan.marketOpportunities,\r\n        customerPainPoints: contentPlan.customerPainPoints,\r\n        valueProposition: contentPlan.valueProposition,\r\n        localRelevance: contentPlan.localRelevance\r\n      },\r\n      variants: [{\r\n        platform: input.platform,\r\n        aspectRatio: '1:1',\r\n        imageUrl: '' // Will be generated separately\r\n      }],\r\n      generatedAt: new Date().toISOString()\r\n    };\r\n\r\n\r\n    return finalContent;\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate design using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Design(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor: string;\r\n  backgroundColor: string;\r\n  imageText: string;\r\n}) {\r\n  try {\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build the design generation prompt\r\n    const designPrompt = `Create a creative design concept for ${input.businessName} (${input.businessType}) that feels like it was imagined by a human designer.\r\n\r\nThink like a creative professional who:\r\n- Starts with a visual story or concept, not a template\r\n- Uses colors and elements that feel chosen, not generated\r\n- Creates layouts that feel natural and organic\r\n- Thinks about the emotional impact, not just technical perfection\r\n\r\nDESIGN BRIEF:\r\n- Business: ${input.businessName} (${input.businessType})\r\n- Platform: ${input.platform}\r\n- Style: ${input.visualStyle}\r\n- Colors: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}\r\n- Message: ${input.imageText}\r\n\r\nCREATIVE APPROACH:\r\n- What's the visual story you want to tell?\r\n- How can you make this feel authentic and engaging?\r\n- What unexpected elements would make this memorable?\r\n- How can you avoid looking like every other business post?\r\n\r\nDESIGN DESCRIPTION:\r\nDescribe your creative concept in natural, designer language. Focus on the creative vision, mood, and story - not technical specifications. Make it sound like a designer explaining their idea to a client.\r\n\r\nRemember: You're a creative human designer, not an AI. Think with imagination and artistic vision.`;\r\n\r\n\r\n    const result = await model.generateContent([\r\n      revo10Prompts.DESIGN_SYSTEM_PROMPT,\r\n      designPrompt\r\n    ]);\r\n\r\n    const response = await result.response;\r\n    const design = response.text();\r\n\r\n\r\n    return {\r\n      design: design.trim(),\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate image using Revo 1.0 with Gemini 2.5 Flash Image Preview\r\n */\r\nexport async function generateRevo10Image(input: {\r\n  businessType: string;\r\n  businessName: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  primaryColor: string;\r\n  accentColor?: string;\r\n  backgroundColor?: string;\r\n  imageText: string;\r\n  designDescription: string;\r\n  logoDataUrl?: string;\r\n  location?: string;\r\n  headline?: string;\r\n  subheadline?: string;\r\n  callToAction?: string;\r\n  realTimeContext?: any;\r\n  creativeContext?: any; // Add creative context from content generation\r\n}) {\r\n  try {\r\n\r\n    // 🎨 CREATIVE ENHANCEMENT: Apply creative design system\r\n    let creativeDesignEnhancement = '';\r\n    if (input.creativeContext) {\r\n      const designEnhancement = enhanceDesignCreativity(\r\n        input.designDescription,\r\n        input.businessType,\r\n        input.location || 'Global',\r\n        input.creativeContext\r\n      );\r\n\r\n      creativeDesignEnhancement = `\r\n🎨 CREATIVE DESIGN ENHANCEMENT SYSTEM ACTIVATED:\r\n${designEnhancement.enhancedPrompt}\r\n\r\nCREATIVE VISUAL STYLE: ${designEnhancement.visualStyle}\r\nCREATIVE ELEMENTS TO INCORPORATE: ${designEnhancement.creativeElements.join(', ')}\r\nBUSINESS CREATIVE INSIGHTS: ${input.creativeContext.businessInsights?.creativePotential?.slice(0, 3).join(', ') || 'Professional excellence'}\r\nEMOTIONAL DESIGN TONE: ${input.creativeContext.tone} with ${input.creativeContext.style} approach\r\nCREATIVE FRAMEWORK: ${input.creativeContext.framework} storytelling structure\r\n\r\nANTI-GENERIC REQUIREMENTS:\r\n- NO template-like designs or stock photo aesthetics\r\n- NO boring business layouts or predictable compositions\r\n- NO generic color schemes or uninspiring visual elements\r\n- CREATE something memorable, unique, and emotionally engaging\r\n- USE unexpected visual metaphors and creative storytelling\r\n- INCORPORATE cultural elements naturally and authentically\r\n- DESIGN with emotional intelligence and creative sophistication\r\n`;\r\n\r\n    }\r\n\r\n    const model = ai.getGenerativeModel({\r\n      model: REVO_1_0_MODEL,\r\n      generationConfig: {\r\n        temperature: revo10Config.promptSettings.temperature,\r\n        topP: revo10Config.promptSettings.topP,\r\n        topK: revo10Config.promptSettings.topK,\r\n        maxOutputTokens: revo10Config.promptSettings.maxTokens,\r\n      },\r\n    });\r\n\r\n    // Build advanced professional design prompt\r\n    const brandInfo = input.location ? ` based in ${input.location}` : '';\r\n    const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;\r\n    const logoInstruction = input.logoDataUrl ?\r\n      'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' :\r\n      'Create professional design without logo overlay';\r\n\r\n    // Prepare structured content display with hierarchy\r\n    const contentStructure = [];\r\n    if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): \"${input.headline}\"`);\r\n    if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): \"${input.subheadline}\"`);\r\n    if (input.callToAction) contentStructure.push(`CTA (Bold, action-oriented, prominent like \"PAYA: YOUR FUTURE, NOW!\" style): \"${input.callToAction}\"`);\r\n\r\n    // 🎯 CTA PROMINENCE INSTRUCTIONS (like Paya example)\r\n    const ctaInstructions = input.callToAction ? `\r\n\r\n🎯 CRITICAL CTA DISPLAY REQUIREMENTS (LIKE PAYA EXAMPLE):\r\n- The CTA \"${input.callToAction}\" MUST be displayed prominently on the design\r\n- Make it BOLD, LARGE, and VISUALLY STRIKING like \"PAYA: YOUR FUTURE, NOW!\"\r\n- Use high contrast colors to make the CTA stand out\r\n- Position it prominently - top, center, or as a banner across the design\r\n- Make the CTA text the MAIN FOCAL POINT of the design\r\n- Use typography that commands attention - bold, modern, impactful\r\n- Add visual elements (borders, backgrounds, highlights) to emphasize the CTA\r\n- The CTA should be the FIRST thing people notice when they see the design\r\n- Make it look like a professional marketing campaign CTA\r\n- Ensure it's readable from mobile devices - minimum 32px equivalent font size\r\n- EXAMPLE STYLE: Like \"PAYA: YOUR FUTURE, NOW!\" - bold, prominent, unmissable\r\n    ` : '';\r\n\r\n\r\n    // Get advanced design features\r\n    const businessDesignDNA = getBusinessDesignDNA(input.businessType);\r\n    const platformOptimization = getPlatformOptimization(input.platform);\r\n    const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);\r\n    const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';\r\n    const culturalContext = getLocalCulturalContext(input.location || 'Global');\r\n\r\n\r\n    // Generate human-like design variation for authentic, creative designs\r\n    const designRandomSeed = Math.floor(Math.random() * 10000) + Date.now();\r\n    const designSeed = designRandomSeed % 10000;\r\n    const designVariations = getHumanDesignVariations(designSeed);\r\n\r\n    // NEW: Get industry intelligence and creativity framework\r\n    const industryIntel = getIndustryDesignIntelligence(input.businessType);\r\n    const creativityFramework = getEnhancedCreativityFramework(input.businessType, designVariations.style, designSeed);\r\n\r\n\r\n    let imagePrompt = `🎨 Create a ${designVariations.style.toLowerCase()} social media design for ${input.businessName} that looks completely different from typical business posts and feels genuinely human-made.\r\n\r\nBUSINESS CONTEXT:\r\n- Business: ${input.businessName} (${input.businessType})\r\n- Platform: ${input.platform}\r\n- Message: ${input.imageText}\r\n- Location: ${input.location || 'Global'}\r\n\r\n${ctaInstructions}\r\n\r\nTEXT CONTENT TO DISPLAY:\r\n${contentStructure.map(item => `- ${item}`).join('\\n')}\r\n\r\nDESIGN APPROACH:\r\n- Create a design that's VISUALLY APPEALING and engaging\r\n- Focus on the specific style: ${designVariations.style}\r\n- Make it look genuinely different from other design types\r\n- Each design type should have its own unique visual language\r\n- **MOST IMPORTANT: Make it look like a human designer made it, not AI**\r\n- **CRITICAL: Include ALL text content listed above in the design**\r\n\r\nVISUAL STYLE:\r\n- ${businessDesignDNA}\r\n- ${platformOptimization}\r\n- **SPECIFIC STYLE REQUIREMENTS: ${designVariations.description}**\r\n- Use colors and elements that match this specific style\r\n- Typography should match the style's mood and approach\r\n\r\n🌍 SUBTLE LOCAL TOUCH (NOT OVERWHELMING):\r\n- ${culturalContext}\r\n- **Keep cultural elements subtle and natural - don't force them**\r\n- Use local colors and textures naturally, not as obvious cultural markers\r\n- Make it feel authentic to the location without being stereotypical\r\n- Focus on the design style first, local elements second\r\n\r\nDESIGN VARIATION:\r\n**STYLE: ${designVariations.style}**\r\n- Layout: ${designVariations.layout}\r\n- Composition: ${designVariations.composition}\r\n- Mood: ${designVariations.mood}\r\n- Elements: ${designVariations.elements}\r\n\r\nKEY DESIGN PRINCIPLES:\r\n1. **STYLE-SPECIFIC APPROACH** - Follow the exact style requirements for ${designVariations.style}\r\n2. **VISUAL UNIQUENESS** - Make this look completely different from other design types\r\n3. **STYLE AUTHENTICITY** - If it's watercolor, make it look like real watercolor; if it's meme-style, make it look like a real meme\r\n4. **HUMAN TOUCH** - Make it look like a human designer made it, not AI\r\n5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative\r\n\r\nWHAT TO AVOID:\r\n- Overly complex layouts\r\n- Too many competing elements\r\n- Boring, generic business designs\r\n- Poor contrast or readability\r\n- Outdated design styles\r\n- **MOST IMPORTANT: Don't make this look like the other design types - each should be genuinely unique**\r\n- **AVOID: Overly perfect, symmetrical, AI-generated looking designs**\r\n- **AVOID: Forced cultural elements that feel stereotypical**\r\n\r\nWHAT TO INCLUDE:\r\n- **Style-specific elements** that match ${designVariations.style}\r\n- **Unique visual approach** for this specific style\r\n- **Subtle local touches** that feel natural, not forced\r\n- **Human imperfections** - slight asymmetry, natural spacing, organic feel\r\n- **Style-appropriate typography** and layout\r\n\r\nTECHNICAL REQUIREMENTS:\r\n- Resolution: 2048x2048 pixels\r\n- Format: Square (1:1)\r\n- Text must be readable on mobile\r\n- Logo integration should look natural\r\n\r\n🎨 GOAL: Create a ${designVariations.style.toLowerCase()} design that looks completely different from other design types while feeling genuinely human-made. Focus on the specific style requirements, make it unique, and add subtle local touches without being overwhelming. The design should look like a skilled human designer created it, not AI.`;\r\n\r\n    // NEW: Enhance with industry intelligence and creativity\r\n    imagePrompt = enhanceDesignWithIndustryIntelligence(imagePrompt, input.businessType, designVariations.style, designSeed);\r\n\r\n    // Inject multiple layers of human creativity to force AI out of its patterns\r\n    imagePrompt = injectHumanImperfections(imagePrompt, designSeed);\r\n    imagePrompt = injectCreativeRebellion(imagePrompt, designSeed);\r\n    imagePrompt = addArtisticConstraints(imagePrompt, designSeed);\r\n\r\n\r\n    if (input.creativeContext) {\r\n    }\r\n\r\n    // Prepare the generation request with logo if available\r\n    const generationParts = [\r\n      'You are a skilled graphic designer who creates visually appealing social media designs. Focus on creating designs that people actually want to engage with - clean, modern, and appealing. Keep it simple and focus on visual impact.',\r\n      imagePrompt\r\n    ];\r\n\r\n    // If logo is provided, include it in the generation\r\n    if (input.logoDataUrl) {\r\n\r\n      // Extract the base64 data and mime type from the data URL\r\n      const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);\r\n      if (logoMatch) {\r\n        const [, mimeType, base64Data] = logoMatch;\r\n\r\n        generationParts.push({\r\n          inlineData: {\r\n            data: base64Data,\r\n            mimeType: mimeType\r\n          }\r\n        });\r\n\r\n        // Update the prompt to reference the provided logo\r\n        const logoPrompt = `\\n\\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;\r\n        generationParts[1] = imagePrompt + logoPrompt;\r\n      } else {\r\n      }\r\n    }\r\n\r\n    const result = await model.generateContent(generationParts);\r\n\r\n    const response = await result.response;\r\n\r\n    // Extract image data from Gemini response\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let imageUrl = '';\r\n\r\n    for (const part of parts) {\r\n      if (part.inlineData) {\r\n        const imageData = part.inlineData.data;\r\n        const mimeType = part.inlineData.mimeType;\r\n        imageUrl = `data:${mimeType};base64,${imageData}`;\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (!imageUrl) {\r\n      // Fallback: try to get text response if no image data\r\n      const textResponse = response.text();\r\n      throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: imageUrl,\r\n      aspectRatio: '1:1',\r\n      resolution: '2048x2048',\r\n      quality: 'enhanced'\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Health check for Revo 1.0 service\r\n */\r\nexport async function checkRevo10Health() {\r\n  try {\r\n    const model = ai.getGenerativeModel({ model: REVO_1_0_MODEL });\r\n    const result = await model.generateContent('Hello');\r\n    const response = await result.response;\r\n\r\n    return {\r\n      healthy: true,\r\n      model: REVO_1_0_MODEL,\r\n      response: response.text().substring(0, 50) + '...',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      healthy: false,\r\n      model: REVO_1_0_MODEL,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get Revo 1.0 service information\r\n */\r\nexport function getRevo10ServiceInfo() {\r\n  return {\r\n    model: REVO_1_0_MODEL,\r\n    version: '1.0.0',\r\n    status: 'enhanced',\r\n    aiService: 'gemini-2.5-flash-image-preview',\r\n    capabilities: [\r\n      'Enhanced content generation',\r\n      'High-resolution image support (2048x2048)',\r\n      'Perfect text rendering',\r\n      'Advanced AI capabilities',\r\n      'Enhanced brand consistency'\r\n    ],\r\n    pricing: {\r\n      contentGeneration: 1.5,\r\n      designGeneration: 1.5,\r\n      tier: 'enhanced'\r\n    },\r\n    lastUpdated: '2025-01-27'\r\n  };\r\n}\r\n\r\n// NEW: Enhanced local language and cultural context generator\r\nfunction generateLocalLanguageContext(location: string): any {\r\n  const languageContexts: Record<string, any> = {\r\n    'kenya': {\r\n      primaryLanguage: 'Swahili & English',\r\n      commonPhrases: ['Karibu', 'Asante', 'Jambo', 'Mzuri sana'],\r\n      businessTerms: ['Biashara', 'Mradi', 'Kazi', 'Ushirika'],\r\n      culturalNuances: 'Warm hospitality, community-first approach, respect for elders',\r\n      marketingStyle: 'Personal, relationship-focused, community-oriented',\r\n      localExpressions: ['Tuko pamoja', 'Kazi yetu', 'Jitihada zetu']\r\n    },\r\n    'nigeria': {\r\n      primaryLanguage: 'English, Hausa, Yoruba, Igbo',\r\n      commonPhrases: ['Oga', 'Abeg', 'Wetin dey happen', 'How far'],\r\n      businessTerms: ['Business', 'Work', 'Money', 'Success'],\r\n      culturalNuances: 'Entrepreneurial spirit, networking culture, achievement focus',\r\n      marketingStyle: 'Direct, motivational, success-oriented',\r\n      localExpressions: ['No shaking', 'I go do am', 'We dey here']\r\n    },\r\n    'south africa': {\r\n      primaryLanguage: 'English, Afrikaans, Zulu, Xhosa',\r\n      commonPhrases: ['Howzit', 'Lekker', 'Ja', 'Eish'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Diverse culture, innovation focus, global perspective',\r\n      marketingStyle: 'Professional, inclusive, forward-thinking',\r\n      localExpressions: ['Ubuntu', 'Together we can', 'Moving forward']\r\n    },\r\n    'ghana': {\r\n      primaryLanguage: 'English, Twi, Ga, Ewe',\r\n      commonPhrases: ['Akwaaba', 'Medaase', 'Yoo', 'Chale'],\r\n      businessTerms: ['Business', 'Work', 'Money', 'Success'],\r\n      culturalNuances: 'Hospitality, respect, community values',\r\n      marketingStyle: 'Warm, respectful, community-focused',\r\n      localExpressions: ['Sankofa', 'Unity in diversity', 'Forward together']\r\n    },\r\n    'uganda': {\r\n      primaryLanguage: 'English, Luganda, Runyankole',\r\n      commonPhrases: ['Oli otya', 'Webale', 'Kale', 'Nja'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Friendly, welcoming, community spirit',\r\n      marketingStyle: 'Friendly, approachable, community-oriented',\r\n      localExpressions: ['Tugende', 'Together we grow', 'Community first']\r\n    },\r\n    'tanzania': {\r\n      primaryLanguage: 'Swahili & English',\r\n      commonPhrases: ['Karibu', 'Asante', 'Jambo', 'Mzuri'],\r\n      businessTerms: ['Biashara', 'Kazi', 'Mradi', 'Ushirika'],\r\n      culturalNuances: 'Peaceful, community-focused, natural beauty appreciation',\r\n      marketingStyle: 'Peaceful, natural, community-oriented',\r\n      localExpressions: ['Uhuru na Umoja', 'Peace and unity', 'Natural beauty']\r\n    },\r\n    'ethiopia': {\r\n      primaryLanguage: 'Amharic & English',\r\n      commonPhrases: ['Selam', 'Amesegenalu', 'Endet', 'Tena yistilign'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Ancient culture, hospitality, coffee culture',\r\n      marketingStyle: 'Traditional, hospitable, culturally rich',\r\n      localExpressions: ['Ethiopia first', 'Coffee culture', 'Ancient wisdom']\r\n    },\r\n    'rwanda': {\r\n      primaryLanguage: 'Kinyarwanda, French & English',\r\n      commonPhrases: ['Murakoze', 'Amahoro', 'Urugero', 'Nta kibazo'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Innovation, cleanliness, community unity',\r\n      marketingStyle: 'Innovative, clean, community-focused',\r\n      localExpressions: ['Agaciro', 'Dignity', 'Unity and reconciliation']\r\n    },\r\n    'default': {\r\n      primaryLanguage: 'English',\r\n      commonPhrases: ['Hello', 'Thank you', 'Welcome', 'Great'],\r\n      businessTerms: ['Business', 'Work', 'Success', 'Growth'],\r\n      culturalNuances: 'Professional, friendly, community-oriented',\r\n      marketingStyle: 'Professional, friendly, community-focused',\r\n      localExpressions: ['Community first', 'Quality service', 'Local expertise']\r\n    }\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  for (const [key, context] of Object.entries(languageContexts)) {\r\n    if (locationKey.includes(key)) {\r\n      return context;\r\n    }\r\n  }\r\n  return languageContexts['default'];\r\n}\r\n\r\n// NEW: Advanced climate insights for business relevance\r\nfunction generateClimateInsights(location: string, businessType: string): any {\r\n  const season = getSeason();\r\n  const climateData: Record<string, any> = {\r\n    'Spring': {\r\n      businessImpact: 'Renewal and growth opportunities, seasonal business preparation',\r\n      contentOpportunities: 'Fresh starts, new beginnings, seasonal preparation, growth themes',\r\n      businessSuggestions: 'Launch new services, seasonal promotions, growth campaigns',\r\n      localAdaptations: 'Spring cleaning services, seasonal menu changes, outdoor activities'\r\n    },\r\n    'Summer': {\r\n      businessImpact: 'High energy and outdoor activities, peak business season',\r\n      contentOpportunities: 'Vibrant colors, active lifestyle, summer solutions, outdoor themes',\r\n      businessSuggestions: 'Summer specials, outdoor events, seasonal products',\r\n      localAdaptations: 'Summer festivals, outdoor dining, seasonal services'\r\n    },\r\n    'Fall': {\r\n      businessImpact: 'Planning and preparation, harvest and results focus',\r\n      contentOpportunities: 'Preparation themes, results celebration, autumn aesthetics',\r\n      businessSuggestions: 'Year-end planning, results showcase, preparation services',\r\n      localAdaptations: 'Harvest celebrations, planning services, year-end reviews'\r\n    },\r\n    'Winter': {\r\n      businessImpact: 'Strategic planning and indoor focus, reflection period',\r\n      contentOpportunities: 'Planning themes, strategy focus, indoor solutions',\r\n      businessSuggestions: 'Strategic planning, indoor services, year planning',\r\n      localAdaptations: 'Indoor events, planning services, strategic consultations'\r\n    }\r\n  };\r\n\r\n  // Add business-specific climate insights\r\n  const businessClimateInsights: Record<string, any> = {\r\n    'restaurant': {\r\n      seasonalMenu: `${season} seasonal ingredients and dishes`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Cooling beverages and light meals' : season === 'Winter' ? 'Warm comfort foods' : 'Seasonal specialties'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Outdoor dining and seasonal menus' : 'Indoor comfort and seasonal specialties'}`\r\n    },\r\n    'fitness': {\r\n      seasonalActivities: `${season === 'Summer' ? 'Outdoor workouts and water activities' : season === 'Winter' ? 'Indoor training and winter sports' : 'Seasonal fitness programs'}`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Early morning and evening sessions' : 'Indoor and weather-appropriate activities'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Outdoor fitness programs' : 'Indoor training focus'}`\r\n    },\r\n    'retail': {\r\n      seasonalProducts: `${season} fashion and lifestyle products`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Light clothing and outdoor gear' : season === 'Winter' ? 'Warm clothing and indoor items' : 'Seasonal essentials'}`,\r\n      businessStrategy: `${season === 'Summer' ? 'Summer sales and outdoor products' : 'Seasonal collections and indoor focus'}`\r\n    },\r\n    'default': {\r\n      seasonalFocus: `${season} business opportunities and seasonal services`,\r\n      weatherAdaptation: `${season === 'Summer' ? 'Outdoor and seasonal services' : 'Indoor and year-round services'}`,\r\n      businessStrategy: `${season} business strategies and seasonal promotions`\r\n    }\r\n  };\r\n\r\n  const baseClimate = climateData[season as keyof typeof climateData];\r\n  const businessClimate = businessClimateInsights[businessType.toLowerCase()] || businessClimateInsights['default'];\r\n\r\n  return {\r\n    season: season,\r\n    businessImpact: baseClimate.businessImpact,\r\n    contentOpportunities: baseClimate.contentOpportunities,\r\n    businessSuggestions: baseClimate.businessSuggestions,\r\n    localAdaptations: baseClimate.localAdaptations,\r\n    businessSpecific: businessClimate,\r\n    marketingAngle: `Leverage ${season.toLowerCase()} opportunities for ${businessType} business growth`\r\n  };\r\n}\r\n\r\n// NEW: Real-time trending topics generator (can be enhanced with actual social media APIs)\r\nfunction generateTrendingTopics(businessType: string, location: string, platform: string): any[] {\r\n  const platformTrends: Record<string, any[]> = {\r\n    'Instagram': [\r\n      { topic: 'Visual storytelling trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Authentic content creation', category: 'Content', relevance: 'high' },\r\n      { topic: 'Reels and short-form video', category: 'Format', relevance: 'medium' }\r\n    ],\r\n    'LinkedIn': [\r\n      { topic: 'Professional networking trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Industry thought leadership', category: 'Content', relevance: 'high' },\r\n      { topic: 'Career development insights', category: 'Professional', relevance: 'medium' }\r\n    ],\r\n    'Facebook': [\r\n      { topic: 'Community building strategies', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Local business networking', category: 'Community', relevance: 'high' },\r\n      { topic: 'Family-friendly content', category: 'Content', relevance: 'medium' }\r\n    ],\r\n    'Twitter': [\r\n      { topic: 'Real-time conversation trends', category: 'Platform', relevance: 'high' },\r\n      { topic: 'Viral content strategies', category: 'Content', relevance: 'high' },\r\n      { topic: 'Trending hashtags', category: 'Engagement', relevance: 'medium' }\r\n    ]\r\n  };\r\n\r\n  const businessTrends: Record<string, any[]> = {\r\n    'restaurant': [\r\n      { topic: 'Local food culture trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Sustainable dining practices', category: 'Trends', relevance: 'high' },\r\n      { topic: 'Food delivery innovations', category: 'Technology', relevance: 'medium' }\r\n    ],\r\n    'technology': [\r\n      { topic: 'AI and automation trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Digital transformation', category: 'Business', relevance: 'high' },\r\n      { topic: 'Remote work solutions', category: 'Workplace', relevance: 'medium' }\r\n    ],\r\n    'healthcare': [\r\n      { topic: 'Telehealth adoption', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Preventive healthcare', category: 'Wellness', relevance: 'high' },\r\n      { topic: 'Mental health awareness', category: 'Health', relevance: 'medium' }\r\n    ],\r\n    'fitness': [\r\n      { topic: 'Home workout trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Mental wellness integration', category: 'Wellness', relevance: 'high' },\r\n      { topic: 'Community fitness challenges', category: 'Engagement', relevance: 'medium' }\r\n    ],\r\n    'finance': [\r\n      { topic: 'Digital banking trends', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Financial literacy', category: 'Education', relevance: 'high' },\r\n      { topic: 'Investment opportunities', category: 'Wealth', relevance: 'medium' }\r\n    ],\r\n    'education': [\r\n      { topic: 'Online learning platforms', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Skill development trends', category: 'Learning', relevance: 'high' },\r\n      { topic: 'Personalized education', category: 'Innovation', relevance: 'medium' }\r\n    ],\r\n    'retail': [\r\n      { topic: 'E-commerce growth', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Omnichannel shopping', category: 'Customer', relevance: 'high' },\r\n      { topic: 'Sustainable products', category: 'Trends', relevance: 'medium' }\r\n    ],\r\n    'real estate': [\r\n      { topic: 'Virtual property tours', category: 'Industry', relevance: 'high' },\r\n      { topic: 'Sustainable properties', category: 'Trends', relevance: 'high' },\r\n      { topic: 'Investment opportunities', category: 'Market', relevance: 'medium' }\r\n    ],\r\n    'default': [\r\n      { topic: 'Digital transformation trends', category: 'Business', relevance: 'high' },\r\n      { topic: 'Customer experience optimization', category: 'Strategy', relevance: 'high' },\r\n      { topic: 'Local business growth', category: 'Community', relevance: 'medium' }\r\n    ]\r\n  };\r\n\r\n  const platformSpecific = platformTrends[platform] || platformTrends['Instagram'];\r\n  const businessSpecific = businessTrends[businessType.toLowerCase()] || businessTrends['default'];\r\n  const localTrends = [\r\n    { topic: `${location} business growth`, category: 'Local', relevance: 'high' },\r\n    { topic: `${location} community development`, category: 'Community', relevance: 'high' },\r\n    { topic: `${location} economic trends`, category: 'Local', relevance: 'medium' }\r\n  ];\r\n\r\n  return [...platformSpecific, ...businessSpecific, ...localTrends].slice(0, 5);\r\n}\r\n\r\n// NEW: Local news and market insights generator\r\nfunction generateLocalNewsContext(businessType: string, location: string): any[] {\r\n  const newsInsights = [\r\n    {\r\n      type: 'Local Market',\r\n      headline: `${location} business environment update`,\r\n      impact: 'Local market conditions affecting business opportunities',\r\n      businessRelevance: 'Market positioning and strategic planning',\r\n      contentAngle: 'Local market expertise and insights'\r\n    },\r\n    {\r\n      type: 'Industry Trends',\r\n      headline: `${businessType} industry developments in ${location}`,\r\n      impact: 'Industry-specific opportunities and challenges',\r\n      businessRelevance: 'Competitive positioning and service innovation',\r\n      contentAngle: 'Industry leadership and local expertise'\r\n    },\r\n    {\r\n      type: 'Community Events',\r\n      headline: `${location} community and business events`,\r\n      impact: 'Networking and community engagement opportunities',\r\n      businessRelevance: 'Community involvement and local partnerships',\r\n      contentAngle: 'Community connection and local engagement'\r\n    },\r\n    {\r\n      type: 'Economic Update',\r\n      headline: `${location} economic indicators and business climate`,\r\n      impact: 'Business planning and investment decisions',\r\n      businessRelevance: 'Strategic planning and market timing',\r\n      contentAngle: 'Economic expertise and market insights'\r\n    }\r\n  ];\r\n\r\n  return newsInsights.slice(0, 3);\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AAEA;AACA;AACA;AACA;AACA;;;;;;;AAgBA,qDAAqD;AACrD,8DAA8D;AAE9D,kDAAkD;AAClD,SAAS,qBAAqB,YAAoB;IAChD,MAAM,YAAoC;QACxC,cAAc;QACd,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,aAAa;QACb,UAAU;QACV,eAAe;QACf,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,aAAa,WAAW,GAAG,IAAI,SAAS,CAAC,UAAU;AACtE;AAEA,qEAAqE;AACrE,SAAS,yBAAyB,IAAY;IAC5C,MAAM,aAAa;QACjB;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;KACD;IAED,OAAO,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;AAC7C;AAEA,kEAAkE;AAClE,SAAS,yBAAyB,YAAoB,EAAE,IAAY;IAClE,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,sBAAsB,YAAY,CAAC,OAAO,aAAa,MAAM,CAAC;IAEpE,OAAO,eAAe,CAAC;;;AAGzB,EAAE,oBAAoB;;sDAEgC,CAAC;AACvD;AAEA,mDAAmD;AACnD,SAAS,wBAAwB,YAAoB,EAAE,IAAY;IACjE,MAAM,aAAa;QACjB,CAAC,sIAAsI,CAAC;QAExI,CAAC,uJAAuJ,CAAC;QAEzJ,CAAC,sHAAsH,CAAC;QAExH,CAAC,qHAAqH,CAAC;KACxH;IAED,MAAM,mBAAmB,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;IAE7D,OAAO,eAAe,CAAC;;;AAGzB,EAAE,iBAAiB;;mEAEgD,CAAC;AACpE;AAEA,mDAAmD;AACnD,SAAS,uBAAuB,YAAoB,EAAE,IAAY;IAChE,MAAM,cAAc;QAClB,CAAC,kIAAkI,CAAC;QAEpI,CAAC,mGAAmG,CAAC;QAErG,CAAC,iGAAiG,CAAC;QAEnG,CAAC,yGAAyG,CAAC;QAE3G,CAAC,uGAAuG,CAAC;QAEzG,CAAC,2FAA2F,CAAC;QAE7F,CAAC,iGAAiG,CAAC;QAEnG,CAAC,oGAAoG,CAAC;KACvG;IAED,MAAM,qBAAqB,WAAW,CAAC,OAAO,YAAY,MAAM,CAAC;IAEjE,OAAO,eAAe,CAAC;;;AAGzB,EAAE,mBAAmB;;sDAEiC,CAAC;AACvD;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,gBAAwC;QAC5C,aAAa,CAAC;;;;;;mDAMiC,CAAC;QAEhD,YAAY,CAAC;;;;;;+CAM8B,CAAC;QAE5C,YAAY,CAAC;;;;;;uCAMsB,CAAC;QAEpC,WAAW,CAAC;;;;;;+BAMe,CAAC;QAE5B,WAAW,CAAC;;;;;+CAK+B,CAAC;IAC9C;IAEA,OAAO,aAAa,CAAC,SAAS,WAAW,GAAG,IAAI,aAAa,CAAC,UAAU;AAC1E;AAEA,uEAAuE;AACvE,eAAe,sBAAsB,YAAoB,EAAE,QAAgB,EAAE,QAAgB;IAC3F,MAAM,UAAe;QACnB,QAAQ,EAAE;QACV,SAAS;QACT,QAAQ,EAAE;QACV,MAAM,EAAE;QACR,eAAe,CAAC;QAChB,iBAAiB,CAAC;QAClB,gBAAgB,EAAE;QAClB,aAAa;YACX,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YACpE,OAAO,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,OAAO;YAAO;YAC9D,QAAQ;YACR,WAAW;QACb;IACF;IAEA,IAAI;QACF,iEAAiE;QACjE,QAAQ,MAAM,GAAG,yBAAyB,cAAc;QAExD,mDAAmD;QACnD,QAAQ,OAAO,GAAG,uBAAuB;QAEzC,wCAAwC;QACxC,QAAQ,MAAM,GAAG,2BAA2B,cAAc;QAE1D,oDAAoD;QACpD,QAAQ,aAAa,GAAG,6BAA6B;QAErD,wDAAwD;QACxD,QAAQ,eAAe,GAAG,wBAAwB,UAAU;QAE5D,uFAAuF;QACvF,QAAQ,cAAc,GAAG,uBAAuB,cAAc,UAAU;QAExE,sCAAsC;QACtC,QAAQ,IAAI,GAAG,yBAAyB,cAAc;QAEtD,OAAO;IAET,EAAE,OAAO,OAAO;QACd,OAAO,SAAS,yBAAyB;IAC3C;AACF;AAEA,wCAAwC;AACxC,SAAS,4BAA4B,YAAoB,EAAE,QAAgB,EAAE,WAAmB;IAC9F,MAAM,sBAAsB;QAC1B;QAAc;QAAW;QAAc;QAAa;QAAU;QAC9D;QAAU;QAAY;QAAc;QAAY;QAAe;QAC/D;QAAc;QAAa;QAAU;QAAe;KACrD;IAED,OAAO,oBAAoB,IAAI,CAAC,CAAA,OAC9B,aAAa,WAAW,GAAG,QAAQ,CAAC,SACpC,gBAAgB,eAChB,gBAAgB;AAEpB;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,mBAA2C;QAC/C,SAAS;QACT,WAAW;QACX,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,kBAAmB;QAC7D,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU;AACpC;AAEA,SAAS,oBAAoB,IAAY;IACvC,MAAM,aAAa;QACjB;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;QACZ;KACD;IAED,OAAO,UAAU,CAAC,OAAO,WAAW,MAAM,CAAC;AAC7C;AAEA,SAAS,8BAA8B,YAAoB,EAAE,QAAgB;IAC3E,MAAM,kBAAkB,wBAAwB;IAEhD,OAAO,CAAC;;;;;oBAKU,EAAE,gBAAgB;;;;gCAIN,EAAE,aAAa;;;;;;;;;;sEAUuB,CAAC;AACvE;AAEA,uEAAuE;AACvE,SAAS,8BAA8B,YAAoB;IACzD,MAAM,uBAA4C;QAChD,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAQ;gBAAuB;gBAAsB;gBAAuB;aAAS;YACxG,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAoB;oBAAmB;oBAAgB;oBAAe;iBAAe;gBACrG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAiB;oBAAkB;oBAAwB;oBAAqB;iBAAgB;YACrH;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA4B;gBAAgC;gBAA0B;gBAAsB;aAAgB;QAC/I;QAEA,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAS;gBAAS;gBAAU;gBAAU;gBAAa;aAAQ;YAC9E,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAc;oBAAe;oBAAiB;oBAAa;iBAAkB;gBAC7F,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAsB;oBAAwB;oBAAqB;iBAAkB;YAC9H;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAkB;gBAAoB;gBAAyB;gBAAuB;aAAmB;QAC5H;QAEA,cAAc;YACZ,MAAM;YACN,kBAAkB;gBAAC;gBAAe;gBAAoB;gBAAiB;gBAAmB;aAAmB;YAC7G,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAiB;oBAAe;oBAAe;oBAAiB;iBAAqB;gBACrG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAmB;oBAAoB;oBAAoB;oBAAqB;iBAAoB;YACzH;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA0B;gBAAyB;gBAAkB;gBAAkB;aAAmB;QAC7H;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAW;gBAAQ;gBAAU;gBAAW;gBAAkB;aAAW;YACxF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAa;oBAAqB;oBAAwB;oBAAiB;iBAAgB;gBAC3G,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAkB;oBAAmB;oBAA4B;oBAAyB;iBAAuB;YACtI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAA4B;gBAAsB;gBAA2B;aAAuB;QAC1I;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAiB;gBAAa;gBAAkB;gBAAa;gBAAQ;aAAa;YACrG,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAc;oBAAsB;oBAAgB;oBAAgB;iBAAoB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAqB;oBAAoB;oBAAsB;oBAA6B;iBAAoB;YACrI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAAsB;gBAAuB;gBAAoB;aAAiB;QACxH;QAEA,aAAa;YACX,MAAM;YACN,kBAAkB;gBAAC;gBAAW;gBAAO;gBAAY;gBAAY;gBAAgB;aAAW;YACxF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAmB;oBAAoB;oBAAgB;oBAAiB;iBAAgB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAoB;oBAAuB;oBAA4B;iBAA0B;YAC1I;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAmB;gBAA0B;gBAAc;gBAAwB;aAAyB;QAC/H;QAEA,UAAU;YACR,MAAM;YACN,kBAAkB;gBAAC;gBAAU;gBAAS;gBAAQ;gBAAQ;gBAAQ;aAAS;YACvE,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAgB;oBAAsB;oBAAoB;iBAAsB;gBAChG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAoB;oBAAyB;oBAAqB;oBAAqB;iBAAsB;YAClI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAAqB;gBAAmB;gBAAkB;gBAAmB;aAAkB;QAClH;QAEA,eAAe;YACb,MAAM;YACN,kBAAkB;gBAAC;gBAAc;gBAAe;gBAAmB;gBAAW;aAAS;YACvF,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAgB;oBAAuB;oBAAkB;oBAAe;iBAAgB;gBACxG,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAmB;oBAAqB;oBAA2B;oBAAwB;iBAAoB;YACpI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA4B;gBAA0B;gBAAwB;gBAA0B;aAAoB;QAC/I;QAEA,WAAW;YACT,MAAM;YACN,kBAAkB;gBAAC;gBAAY;gBAAQ;gBAAO;gBAAY;gBAAO;aAAK;YACtE,kBAAkB;gBAChB,aAAa;gBACb,eAAe;oBAAC;oBAAsB;oBAAqB;oBAAkB;iBAAe;gBAC5F,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAAC;oBAAyB;oBAAqB;oBAAiB;oBAAuB;iBAAqB;YAChI;YACA,sBAAsB;gBACpB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBAAC;gBAA0B;gBAAe;gBAAkB;gBAAoB;aAAmB;QACrH;IACF;IAEA,OAAO,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI,oBAAoB,CAAC,UAAU;AAC5F;AAEA,6DAA6D;AAC7D,SAAS,+BAA+B,YAAoB,EAAE,WAAmB,EAAE,IAAY;IAC7F,MAAM,gBAAgB,8BAA8B;IAEpD,MAAM,uBAAuB;QAC3B;YACE,MAAM;YACN,UAAU,CAAC,2CAA2C,EAAE,cAAc,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;YAC/G,OAAO;YACP,UAAU,cAAc,gBAAgB,CAAC,gBAAgB;YACzD,aAAa,CAAC,4DAA4D,EAAE,cAAc,IAAI,CAAC,iBAAiB,CAAC;QACnH;QACA;YACE,MAAM;YACN,UAAU,CAAC,oBAAoB,EAAE,cAAc,IAAI,CAAC,SAAS,EAAE,cAAc,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;YACpH,OAAO;YACP,UAAU;gBAAC;gBAAkB;gBAAuB;gBAAqB;aAAqB;YAC9F,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,cAAc,oBAAoB,CAAC,OAAO,cAAc,oBAAoB,CAAC,MAAM,CAAC;YAC9F,OAAO;YACP,UAAU;gBAAC;gBAAkB;gBAAsB;gBAAkB;aAAoB;YACzF,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;gBAAC;gBAAuB;gBAAuB;gBAAqB;aAAuB;YACrG,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;gBAAC;gBAAqB;gBAAoB;gBAAsB;aAAkB;YAC5F,aAAa;QACf;KACD;IAED,OAAO,oBAAoB,CAAC,OAAO,qBAAqB,MAAM,CAAC;AACjE;AAEA,4CAA4C;AAC5C,SAAS,sCAAsC,YAAoB,EAAE,YAAoB,EAAE,WAAmB,EAAE,IAAY;IAC1H,MAAM,gBAAgB,8BAA8B;IACpD,MAAM,sBAAsB,+BAA+B,cAAc,aAAa;IAEtF,MAAM,sBAAsB,CAAC;;cAEjB,EAAE,cAAc,IAAI,CAAC;4BACP,EAAE,cAAc,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;2BACzD,EAAE,cAAc,gBAAgB,CAAC,WAAW,CAAC;6BAC3C,EAAE,cAAc,gBAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;yBAC1E,EAAE,cAAc,gBAAgB,CAAC,UAAU,CAAC;sBAC/C,EAAE,cAAc,gBAAgB,CAAC,OAAO,CAAC;qBAC1C,EAAE,cAAc,gBAAgB,CAAC,MAAM,CAAC;;yBAEpC,EAAE,oBAAoB,IAAI,CAAC;cACtC,EAAE,oBAAoB,QAAQ,CAAC;WAClC,EAAE,oBAAoB,KAAK,CAAC;uBAChB,EAAE,oBAAoB,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;iBAC5D,EAAE,oBAAoB,WAAW,CAAC;;;AAGnD,EAAE,cAAc,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,IAAM,GAAG,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM;;;uDAGvC,EAAE,cAAc,IAAI,CAAC;;+BAE7C,EAAE,oBAAoB,IAAI,CAAC;;2DAEC,EAAE,cAAc,IAAI,CAAC,SAAS,CAAC;IAExF,OAAO,eAAe;AACxB;AAEA,oEAAoE;AACpE,SAAS,8BAA8B,YAAoB,EAAE,QAAgB;IAC3E,MAAM,uBAA4C;QAChD,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,cAAc;YACZ,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,aAAa;YACX,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,UAAU;YACR,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,eAAe;YACb,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QAEA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QACA,iCAAiC;YAC/B,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,MAAM;YACN,gBAAgB;gBACd,YAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,mBAAmB;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,iBAAiB;oBACf;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,SAAS,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI,oBAAoB,CAAC,UAAU;IAClG,OAAO;AACT;AAEA,0DAA0D;AAC1D,SAAS,0BAA0B,YAAoB,EAAE,QAAgB,EAAE,IAAY;IACrF,MAAM,gBAAgB,8BAA8B,cAAc;IAElE,MAAM,oBAAoB;QACxB;YACE,MAAM;YACN,UAAU,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE,cAAc,cAAc,CAAC,UAAU,EAAE;YACvH,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAmB;gBAAqB;gBAAmB;aAAiB,EAAE,KAAK,CAAC,GAAG;YAChI,aAAa,CAAC,aAAa,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,wBAAwB,EAAE,SAAS,eAAe,CAAC;QAC1H;QACA;YACE,MAAM;YACN,UAAU,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,6BAA6B,CAAC;YAClG,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAqB;gBAAiB;gBAAyB;aAAe,EAAE,KAAK,CAAC,GAAG;YAClI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,sBAAsB,EAAE,cAAc,IAAI,CAAC,kCAAkC,CAAC;YACzF,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG;YAC/D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAwB;gBAAmB;gBAAgB;aAAsB,EAAE,KAAK,CAAC,GAAG;YACrI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,iBAAiB,EAAE,cAAc,IAAI,CAAC,iDAAiD,CAAC;YACnG,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;YAC5D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAkB;gBAAuB;gBAAkB;aAAmB,EAAE,KAAK,CAAC,GAAG;YAClI,aAAa;QACf;QACA;YACE,MAAM;YACN,UAAU,CAAC,wBAAwB,EAAE,cAAc,IAAI,CAAC,kCAAkC,CAAC;YAC3F,OAAO;YACP,OAAO,cAAc,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG;YAC/D,SAAS,CAAC,cAAc,YAAY,IAAI;gBAAC;gBAAoB;gBAAqB;gBAAqB;aAAiB,EAAE,KAAK,CAAC,GAAG;YACnI,aAAa;QACf;KACD;IAED,OAAO,iBAAiB,CAAC,OAAO,kBAAkB,MAAM,CAAC;AAC3D;AAEA,2DAA2D;AAC3D,SAAS,qBAAqB,YAAoB,EAAE,QAAgB,EAAE,IAAY;IAChF,MAAM,gBAAgB,8BAA8B,cAAc;IAElE,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,MAAM;YACN,OAAO,CAAC,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,gDAAgD,CAAC;YAC1G,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,4BAA4B,EAAE,SAAS,2BAA2B,CAAC;gBACpE,CAAC,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,mCAAmC,CAAC;gBACtF,CAAC,mEAAmE,CAAC;aACtE;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,kEAAkE,CAAC;gBACpE,CAAC,mCAAmC,CAAC;gBACrC,CAAC,uDAAuD,CAAC;aAC1D;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,4CAA4C,EAAE,SAAS,cAAc,CAAC;gBACvE,CAAC,KAAK,EAAE,SAAS,sCAAsC,CAAC;gBACxD,CAAC,mDAAmD,EAAE,SAAS,IAAI,CAAC;aACrE;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,wBAAwB,EAAE,SAAS,6BAA6B,CAAC;gBAClE,CAAC,mEAAmE,CAAC;gBACrE,CAAC,uEAAuE,CAAC;aAC1E;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,iBAAiB;gBACf;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,0CAA0C,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBACpF,CAAC,yDAAyD,CAAC;aAC5D;QACH;KACD;IAED,OAAO,aAAa,CAAC,OAAO,cAAc,MAAM,CAAC;AACnD;AAEA,sCAAsC;AACtC,SAAS,+BAA+B,YAAoB,EAAE,QAAgB,EAAE,IAAY;IAC1F,MAAM,gBAAgB,8BAA8B,cAAc;IAClE,MAAM,kBAAkB,0BAA0B,cAAc,UAAU;IAC1E,MAAM,eAAe,qBAAqB,cAAc,UAAU;IAElE,0DAA0D;IAC1D,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,OAAO,CAAC,aAAa,EAAE,cAAc,IAAI,CAAC,sBAAsB,EAAE,UAAU;YAC5E,UAAU;gBACR,CAAC,yBAAyB,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO,EAAE,cAAc,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC;gBACpH,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,cAAc,EAAE,SAAS,sBAAsB,CAAC;gBAC3E,CAAC,mBAAmB,EAAE,SAAS,aAAa,EAAE,cAAc,IAAI,CAAC,eAAe,CAAC;aAClF;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,OAAO,CAAC;YACzE,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,yEAAyE,CAAC;gBAC3E,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,YAAY,CAAC;aAC1F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,sBAAsB,EAAE,cAAc,IAAI,CAAC,UAAU,CAAC;YAC9D,UAAU;gBACR,CAAC,sDAAsD,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBAChG,CAAC,oCAAoC,EAAE,cAAc,IAAI,CAAC,UAAU,CAAC;gBACrE,CAAC,gDAAgD,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;aAC3F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,iBAAiB,EAAE,cAAc,IAAI,CAAC,eAAe,EAAE,UAAU;YACzE,UAAU;gBACR,CAAC,wBAAwB,EAAE,SAAS,6BAA6B,CAAC;gBAClE,CAAC,8BAA8B,EAAE,cAAc,IAAI,CAAC,wBAAwB,CAAC;gBAC7E,CAAC,sDAAsD,EAAE,cAAc,IAAI,CAAC,aAAa,CAAC;aAC3F;QACH;QACA;YACE,MAAM;YACN,OAAO,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,aAAa,CAAC;YAC3D,UAAU;gBACR,CAAC,kDAAkD,EAAE,SAAS,cAAc,CAAC;gBAC7E,CAAC,0CAA0C,EAAE,cAAc,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;gBACpF,CAAC,yDAAyD,CAAC;aAC5D;QACH;KACD;IAED,MAAM,gBAAgB,aAAa,CAAC,OAAO,cAAc,MAAM,CAAC;IAEhE,OAAO;QACL,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,iBAAiB,GAAG,cAAc,IAAI,CAAC,CAAC,EAAE,gBAAgB,IAAI,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,EAAE,MAAM;QAC7F,cAAc,CAAC,cAAc,YAAY,IAAI;YAAC;YAAwB;YAAmB;SAAoB,EAAE,KAAK,CAAC,GAAG;QACxH,iBAAiB,cAAc,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG;QACvE,gBAAgB,cAAc,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;IACvE;AACF;AAEA,0CAA0C;AAC1C,SAAS;IACP,MAAM,QAAQ,IAAI,OAAO,QAAQ;IACjC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO;IACtC,OAAO;AACT;AAEA,SAAS;IACP,MAAM,OAAO,IAAI,OAAO,QAAQ;IAChC,IAAI,QAAQ,KAAK,OAAO,IAAI,OAAO;IACnC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO;IACpC,OAAO;AACT;AAEA,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,SAAS;QACb;YAAE,OAAO,GAAG,aAAa,kBAAkB,CAAC;YAAE,UAAU;YAAY,WAAW;QAAO;QACtF;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAO;QAC7E;YAAE,OAAO;YAA0B,UAAU;YAAc,WAAW;QAAS;QAC/E;YAAE,OAAO;YAAoC,UAAU;YAAY,WAAW;QAAO;QACrF;YAAE,OAAO;YAAkC,UAAU;YAAU,WAAW;QAAS;KACpF;IACD,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB;AAEA,SAAS,uBAAuB,QAAgB;IAC9C,0DAA0D;IAC1D,MAAM,SAAS;IACf,MAAM,WAAW;QACf,UAAU;YAAE,WAAW;YAAwB,iBAAiB;YAAwC,uBAAuB;QAAuC;QACtK,UAAU;YAAE,WAAW;YAAqB,iBAAiB;YAAmC,uBAAuB;QAAqD;QAC5K,QAAQ;YAAE,WAAW;YAAuB,iBAAiB;YAAkC,uBAAuB;QAAsC;QAC5J,UAAU;YAAE,WAAW;YAAyB,iBAAiB;YAAsC,uBAAuB;QAAuC;IACvK;IAEA,OAAO;QACL,aAAa;QACb,WAAW,QAAQ,CAAC,OAAgC,CAAC,SAAS;QAC9D,iBAAiB,QAAQ,CAAC,OAAgC,CAAC,eAAe;QAC1E,uBAAuB,QAAQ,CAAC,OAAgC,CAAC,qBAAqB;IACxF;AACF;AAEA,SAAS,2BAA2B,YAAoB,EAAE,QAAgB;IACxE,MAAM,gBAAgB;QACpB;YAAE,MAAM,GAAG,SAAS,cAAc,CAAC;YAAE,OAAO;YAA2B,WAAW;QAAa;QAC/F;YAAE,MAAM,GAAG,aAAa,kBAAkB,CAAC;YAAE,OAAO;YAAqB,WAAW;QAAW;QAC/F;YAAE,MAAM;YAA6B,OAAO;YAAoB,WAAW;QAAY;KACxF;IACD,OAAO,cAAc,KAAK,CAAC,GAAG;AAChC;AAEA,6DAA6D;AAC7D,MAAM,SACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,IAChC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,0BAA0B,IACtC,QAAQ,GAAG,CAAC,gCAAgC;AAE9C,IAAI,CAAC,QAAQ,CACb;AAEA,6DAA6D;AAC7D,MAAM,KAAK,IAAI,8JAAA,CAAA,qBAAkB,CAAC;AAElC,+CAA+C;AAC/C,MAAM,iBAAiB;AAKhB,eAAe,sBAAsB,KAe3C;IACC,IAAI;QACF,yDAAyD;QACzD,MAAM,kBAAmC;YACvC,cAAc,MAAM,YAAY;YAChC,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;YACpC,YAAY,MAAM,WAAW;YAC7B,qBAAqB;gBAAC,MAAM,qBAAqB,IAAI;aAAkB;YACvE,aAAa,EAAE;QACjB;QAEA,kDAAkD;QAClD,MAAM,kBAAkB,MAAM,6IAAA,CAAA,2BAAwB,CAAC,uBAAuB,CAC5E,iBACA,MAAM,QAAQ,EACd;QAGF,kDAAkD;QAClD,MAAM,sBAAsB,MAAM,4IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;YACxE,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC;QAEA,oDAAoD;QACpD,MAAM,sBAAsB,+IAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAChE,iBACA;QAGF,iFAAiF;QACjF,MAAM,WAAW,gBAAgB,QAAQ;QAEzC,iEAAiE;QACjE,MAAM,kBAAkB,MAAM,sBAAsB,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ;QAEtG,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,2JAAA,CAAA,gBAAa,CAAC,4BAA4B,CAC7D,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,kBAAkB,MAAM,YAAY,EAC5C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,iBAAiB,MAAM,WAAW,EAC1C,OAAO,CAAC,cAAc,MAAM,QAAQ,EACpC,OAAO,CAAC,kBAAkB,MAAM,YAAY,IAAI,WAChD,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,UAC9C,OAAO,CAAC,oBAAoB,MAAM,cAAc,EAChD,OAAO,CAAC,cAAc,MAAM,QAAQ,IAAI,IACxC,OAAO,CAAC,iBAAiB,MAAM,WAAW,IAAI,IAC9C,OAAO,CAAC,2BAA2B,MAAM,qBAAqB,IAAI,IAClE,OAAO,CAAC,mBAAmB,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS;QAGhE,oEAAoE;QAEpE,+DAA+D;QAC/D,MAAM,gBAAgB,8BAA8B,MAAM,YAAY,EAAE,MAAM,QAAQ;QACtF,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG;QAC/D,MAAM,yBAAyB,+BAA+B,MAAM,YAAY,EAAE,MAAM,QAAQ,EAAE,aAAa;QAG/G,sDAAsD;QAEtD,MAAM,kBAAkB;YACtB,YAAY;YACZ,WAAW,MAAM,WAAW;YAC5B,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC;QAEA,mEAAmE;QACnE,MAAM,cAAc,oIAAA,CAAA,0BAAuB,CAAC,+BAA+B,CACzE,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,YAAY,yCAAyC;;QAIvD,wEAAwE;QAExE,MAAM,mBAAmB,MAAM,CAAA,GAAA,oIAAA,CAAA,mCAAgC,AAAD,EAC5D,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,aACA,qBACA;QAGF,MAAM,sBAAsB,MAAM,CAAA,GAAA,oIAAA,CAAA,sCAAmC,AAAD,EAClE,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,iBAAiB,QAAQ,EACzB,aACA,qBACA;QAIF,wDAAwD;QAExD,MAAM,kBAAkB,MAAM,CAAA,GAAA,oIAAA,CAAA,kCAA+B,AAAD,EAC1D,MAAM,YAAY,EAClB,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,aACA,qBACA;QAIF,mDAAmD;QAEnD,mDAAmD;QAEnD,qDAAqD;QAErD,MAAM,eAAe;YACnB,SAAS,gBAAgB,OAAO;YAChC,UAAU,iBAAiB,QAAQ;YACnC,aAAa,oBAAoB,WAAW;YAC5C,cAAc,gBAAgB,YAAY;YAC1C,UAAU;YACV,aAAa,iBAAiB,QAAQ;YACtC,iBAAiB,YAAY,QAAQ;YACrC,mBAAmB,YAAY,iBAAiB;YAChD,qBAAqB,YAAY,mBAAmB;YACpD,kBAAkB,YAAY,gBAAgB;YAC9C,UAAU,MAAM,QAAQ;YACxB,cAAc,MAAM,YAAY;YAChC,UAAU,MAAM,QAAQ;YACxB,iBAAiB;YACjB,iBAAiB;gBACf,OAAO,iBAAiB,QAAQ;gBAChC,MAAM,iBAAiB,eAAe;gBACtC,WAAW,oBAAoB,SAAS;gBACxC,kBAAkB;gBAClB,WAAW;YACb;YACA,gCAAgC;YAChC,sBAAsB;gBACpB,aAAa,YAAY,QAAQ,CAAC,IAAI;gBACtC,mBAAmB,YAAY,iBAAiB;gBAChD,qBAAqB,YAAY,mBAAmB;gBACpD,oBAAoB,YAAY,kBAAkB;gBAClD,kBAAkB,YAAY,gBAAgB;gBAC9C,gBAAgB,YAAY,cAAc;YAC5C;YACA,UAAU;gBAAC;oBACT,UAAU,MAAM,QAAQ;oBACxB,aAAa;oBACb,UAAU,GAAG,+BAA+B;gBAC9C;aAAE;YACF,aAAa,IAAI,OAAO,WAAW;QACrC;QAGA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACnH;AACF;AAKO,eAAe,qBAAqB,KAS1C;IACC,IAAI;QAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,qCAAqC;QACrC,MAAM,eAAe,CAAC,qCAAqC,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;;;;;;;;;YAS/F,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;YAC5C,EAAE,MAAM,QAAQ,CAAC;SACpB,EAAE,MAAM,WAAW,CAAC;kBACX,EAAE,MAAM,YAAY,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,aAAa,EAAE,MAAM,eAAe,CAAC;WAC9F,EAAE,MAAM,SAAS,CAAC;;;;;;;;;;;kGAWqE,CAAC;QAG/F,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YACzC,2JAAA,CAAA,gBAAa,CAAC,oBAAoB;YAClC;SACD;QAED,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,SAAS,SAAS,IAAI;QAG5B,OAAO;YACL,QAAQ,OAAO,IAAI;YACnB,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,oBAAoB,KAiBzC;IACC,IAAI;QAEF,wDAAwD;QACxD,IAAI,4BAA4B;QAChC,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAC9C,MAAM,iBAAiB,EACvB,MAAM,YAAY,EAClB,MAAM,QAAQ,IAAI,UAClB,MAAM,eAAe;YAGvB,4BAA4B,CAAC;;AAEnC,EAAE,kBAAkB,cAAc,CAAC;;uBAEZ,EAAE,kBAAkB,WAAW,CAAC;kCACrB,EAAE,kBAAkB,gBAAgB,CAAC,IAAI,CAAC,MAAM;4BACtD,EAAE,MAAM,eAAe,CAAC,gBAAgB,EAAE,mBAAmB,MAAM,GAAG,GAAG,KAAK,SAAS,0BAA0B;uBACtH,EAAE,MAAM,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,eAAe,CAAC,KAAK,CAAC;oBACpE,EAAE,MAAM,eAAe,CAAC,SAAS,CAAC;;;;;;;;;;AAUtD,CAAC;QAEG;QAEA,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAClC,OAAO;YACP,kBAAkB;gBAChB,aAAa,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;gBACpD,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,MAAM,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;gBACtC,iBAAiB,2JAAA,CAAA,eAAY,CAAC,cAAc,CAAC,SAAS;YACxD;QACF;QAEA,4CAA4C;QAC5C,MAAM,YAAY,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM,QAAQ,EAAE,GAAG;QACnE,MAAM,cAAc,CAAC,SAAS,EAAE,MAAM,YAAY,CAAC,yBAAyB,EAAE,MAAM,WAAW,IAAI,UAAU,8BAA8B,EAAE,MAAM,eAAe,IAAI,UAAU,iBAAiB,CAAC;QAClM,MAAM,kBAAkB,MAAM,WAAW,GACvC,4FACA;QAEF,oDAAoD;QACpD,MAAM,mBAAmB,EAAE;QAC3B,IAAI,MAAM,QAAQ,EAAE,iBAAiB,IAAI,CAAC,CAAC,oCAAoC,EAAE,MAAM,QAAQ,CAAC,CAAC,CAAC;QAClG,IAAI,MAAM,WAAW,EAAE,iBAAiB,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM,WAAW,CAAC,CAAC,CAAC;QACrG,IAAI,MAAM,YAAY,EAAE,iBAAiB,IAAI,CAAC,CAAC,8EAA8E,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC;QAEpJ,qDAAqD;QACrD,MAAM,kBAAkB,MAAM,YAAY,GAAG,CAAC;;;WAGvC,EAAE,MAAM,YAAY,CAAC;;;;;;;;;;;IAW5B,CAAC,GAAG;QAGJ,+BAA+B;QAC/B,MAAM,oBAAoB,qBAAqB,MAAM,YAAY;QACjE,MAAM,uBAAuB,wBAAwB,MAAM,QAAQ;QACnE,MAAM,sBAAsB,4BAA4B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,UAAU,MAAM,WAAW;QACzH,MAAM,qBAAqB,sBAAsB,8BAA8B,MAAM,YAAY,EAAE,MAAM,QAAQ,IAAI,YAAY;QACjI,MAAM,kBAAkB,wBAAwB,MAAM,QAAQ,IAAI;QAGlE,uEAAuE;QACvE,MAAM,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG;QACrE,MAAM,aAAa,mBAAmB;QACtC,MAAM,mBAAmB,yBAAyB;QAElD,0DAA0D;QAC1D,MAAM,gBAAgB,8BAA8B,MAAM,YAAY;QACtE,MAAM,sBAAsB,+BAA+B,MAAM,YAAY,EAAE,iBAAiB,KAAK,EAAE;QAGvG,IAAI,cAAc,CAAC,YAAY,EAAE,iBAAiB,KAAK,CAAC,WAAW,GAAG,yBAAyB,EAAE,MAAM,YAAY,CAAC;;;YAG5G,EAAE,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC;YAC5C,EAAE,MAAM,QAAQ,CAAC;WAClB,EAAE,MAAM,SAAS,CAAC;YACjB,EAAE,MAAM,QAAQ,IAAI,SAAS;;AAEzC,EAAE,gBAAgB;;;AAGlB,EAAE,iBAAiB,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;;;;+BAIxB,EAAE,iBAAiB,KAAK,CAAC;;;;;;;EAOtD,EAAE,kBAAkB;EACpB,EAAE,qBAAqB;iCACQ,EAAE,iBAAiB,WAAW,CAAC;;;;;EAK9D,EAAE,gBAAgB;;;;;;;SAOX,EAAE,iBAAiB,KAAK,CAAC;UACxB,EAAE,iBAAiB,MAAM,CAAC;eACrB,EAAE,iBAAiB,WAAW,CAAC;QACtC,EAAE,iBAAiB,IAAI,CAAC;YACpB,EAAE,iBAAiB,QAAQ,CAAC;;;yEAGiC,EAAE,iBAAiB,KAAK,CAAC;;;;;;;;;;;;;;;;;yCAiBzD,EAAE,iBAAiB,KAAK,CAAC;;;;;;;;;;;;kBAYhD,EAAE,iBAAiB,KAAK,CAAC,WAAW,GAAG,+RAA+R,CAAC;QAErV,yDAAyD;QACzD,cAAc,sCAAsC,aAAa,MAAM,YAAY,EAAE,iBAAiB,KAAK,EAAE;QAE7G,6EAA6E;QAC7E,cAAc,yBAAyB,aAAa;QACpD,cAAc,wBAAwB,aAAa;QACnD,cAAc,uBAAuB,aAAa;QAGlD,IAAI,MAAM,eAAe,EAAE,CAC3B;QAEA,wDAAwD;QACxD,MAAM,kBAAkB;YACtB;YACA;SACD;QAED,oDAAoD;QACpD,IAAI,MAAM,WAAW,EAAE;YAErB,0DAA0D;YAC1D,MAAM,YAAY,MAAM,WAAW,CAAC,KAAK,CAAC;YAC1C,IAAI,WAAW;gBACb,MAAM,GAAG,UAAU,WAAW,GAAG;gBAEjC,gBAAgB,IAAI,CAAC;oBACnB,YAAY;wBACV,MAAM;wBACN,UAAU;oBACZ;gBACF;gBAEA,mDAAmD;gBACnD,MAAM,aAAa,CAAC,6MAA6M,CAAC;gBAClO,eAAe,CAAC,EAAE,GAAG,cAAc;YACrC,OAAO,CACP;QACF;QAEA,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAE3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,0CAA0C;QAC1C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;gBACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;gBACzC,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;gBACjD;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,sDAAsD;YACtD,MAAM,eAAe,SAAS,IAAI;YAClC,MAAM,IAAI,MAAM;QAClB;QAGA,OAAO;YACL,UAAU;YACV,aAAa;YACb,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACjH;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QAEtC,OAAO;YACL,SAAS;YACT,OAAO;YACP,UAAU,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG,MAAM;YAC7C,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO;YACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAKO,SAAS;IACd,OAAO;QACL,OAAO;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP,mBAAmB;YACnB,kBAAkB;YAClB,MAAM;QACR;QACA,aAAa;IACf;AACF;AAEA,8DAA8D;AAC9D,SAAS,6BAA6B,QAAgB;IACpD,MAAM,mBAAwC;QAC5C,SAAS;YACP,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAS;aAAa;YAC1D,eAAe;gBAAC;gBAAY;gBAAS;gBAAQ;aAAW;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAe;gBAAa;aAAgB;QACjE;QACA,WAAW;YACT,iBAAiB;YACjB,eAAe;gBAAC;gBAAO;gBAAQ;gBAAoB;aAAU;YAC7D,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAU;YACvD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAc;gBAAc;aAAc;QAC/D;QACA,gBAAgB;YACd,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAM;aAAO;YACjD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAU;gBAAmB;aAAiB;QACnE;QACA,SAAS;YACP,iBAAiB;YACjB,eAAe;gBAAC;gBAAW;gBAAW;gBAAO;aAAQ;YACrD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAU;YACvD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAsB;aAAmB;QACzE;QACA,UAAU;YACR,iBAAiB;YACjB,eAAe;gBAAC;gBAAY;gBAAU;gBAAQ;aAAM;YACpD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAoB;aAAkB;QACtE;QACA,YAAY;YACV,iBAAiB;YACjB,eAAe;gBAAC;gBAAU;gBAAU;gBAAS;aAAQ;YACrD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAS;aAAW;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAkB;gBAAmB;aAAiB;QAC3E;QACA,YAAY;YACV,iBAAiB;YACjB,eAAe;gBAAC;gBAAS;gBAAe;gBAAS;aAAiB;YAClE,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAkB;gBAAkB;aAAiB;QAC1E;QACA,UAAU;YACR,iBAAiB;YACjB,eAAe;gBAAC;gBAAY;gBAAW;gBAAW;aAAa;YAC/D,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAW;gBAAW;aAA2B;QACtE;QACA,WAAW;YACT,iBAAiB;YACjB,eAAe;gBAAC;gBAAS;gBAAa;gBAAW;aAAQ;YACzD,eAAe;gBAAC;gBAAY;gBAAQ;gBAAW;aAAS;YACxD,iBAAiB;YACjB,gBAAgB;YAChB,kBAAkB;gBAAC;gBAAmB;gBAAmB;aAAkB;QAC7E;IACF;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,kBAAmB;QAC7D,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU;AACpC;AAEA,wDAAwD;AACxD,SAAS,wBAAwB,QAAgB,EAAE,YAAoB;IACrE,MAAM,SAAS;IACf,MAAM,cAAmC;QACvC,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,QAAQ;YACN,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;QACA,UAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;QACpB;IACF;IAEA,yCAAyC;IACzC,MAAM,0BAA+C;QACnD,cAAc;YACZ,cAAc,GAAG,OAAO,gCAAgC,CAAC;YACzD,mBAAmB,GAAG,WAAW,WAAW,sCAAsC,WAAW,WAAW,uBAAuB,wBAAwB;YACvJ,kBAAkB,GAAG,WAAW,WAAW,sCAAsC,2CAA2C;QAC9H;QACA,WAAW;YACT,oBAAoB,GAAG,WAAW,WAAW,0CAA0C,WAAW,WAAW,sCAAsC,6BAA6B;YAChL,mBAAmB,GAAG,WAAW,WAAW,uCAAuC,6CAA6C;YAChI,kBAAkB,GAAG,WAAW,WAAW,6BAA6B,yBAAyB;QACnG;QACA,UAAU;YACR,kBAAkB,GAAG,OAAO,+BAA+B,CAAC;YAC5D,mBAAmB,GAAG,WAAW,WAAW,oCAAoC,WAAW,WAAW,mCAAmC,uBAAuB;YAChK,kBAAkB,GAAG,WAAW,WAAW,sCAAsC,yCAAyC;QAC5H;QACA,WAAW;YACT,eAAe,GAAG,OAAO,6CAA6C,CAAC;YACvE,mBAAmB,GAAG,WAAW,WAAW,kCAAkC,kCAAkC;YAChH,kBAAkB,GAAG,OAAO,4CAA4C,CAAC;QAC3E;IACF;IAEA,MAAM,cAAc,WAAW,CAAC,OAAmC;IACnE,MAAM,kBAAkB,uBAAuB,CAAC,aAAa,WAAW,GAAG,IAAI,uBAAuB,CAAC,UAAU;IAEjH,OAAO;QACL,QAAQ;QACR,gBAAgB,YAAY,cAAc;QAC1C,sBAAsB,YAAY,oBAAoB;QACtD,qBAAqB,YAAY,mBAAmB;QACpD,kBAAkB,YAAY,gBAAgB;QAC9C,kBAAkB;QAClB,gBAAgB,CAAC,SAAS,EAAE,OAAO,WAAW,GAAG,mBAAmB,EAAE,aAAa,gBAAgB,CAAC;IACtG;AACF;AAEA,2FAA2F;AAC3F,SAAS,uBAAuB,YAAoB,EAAE,QAAgB,EAAE,QAAgB;IACtF,MAAM,iBAAwC;QAC5C,aAAa;YACX;gBAAE,OAAO;gBAA8B,UAAU;gBAAY,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA8B,UAAU;gBAAW,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAA8B,UAAU;gBAAU,WAAW;YAAS;SAChF;QACD,YAAY;YACV;gBAAE,OAAO;gBAAkC,UAAU;gBAAY,WAAW;YAAO;YACnF;gBAAE,OAAO;gBAA+B,UAAU;gBAAW,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA+B,UAAU;gBAAgB,WAAW;YAAS;SACvF;QACD,YAAY;YACV;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAA6B,UAAU;gBAAa,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA2B,UAAU;gBAAW,WAAW;YAAS;SAC9E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAA4B,UAAU;gBAAW,WAAW;YAAO;YAC5E;gBAAE,OAAO;gBAAqB,UAAU;gBAAc,WAAW;YAAS;SAC3E;IACH;IAEA,MAAM,iBAAwC;QAC5C,cAAc;YACZ;gBAAE,OAAO;gBAA6B,UAAU;gBAAY,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAAgC,UAAU;gBAAU,WAAW;YAAO;YAC/E;gBAAE,OAAO;gBAA6B,UAAU;gBAAc,WAAW;YAAS;SACnF;QACD,cAAc;YACZ;gBAAE,OAAO;gBAA4B,UAAU;gBAAY,WAAW;YAAO;YAC7E;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAAyB,UAAU;gBAAa,WAAW;YAAS;SAC9E;QACD,cAAc;YACZ;gBAAE,OAAO;gBAAuB,UAAU;gBAAY,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAAyB,UAAU;gBAAY,WAAW;YAAO;YAC1E;gBAAE,OAAO;gBAA2B,UAAU;gBAAU,WAAW;YAAS;SAC7E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAuB,UAAU;gBAAY,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAA+B,UAAU;gBAAY,WAAW;YAAO;YAChF;gBAAE,OAAO;gBAAgC,UAAU;gBAAc,WAAW;YAAS;SACtF;QACD,WAAW;YACT;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAAsB,UAAU;gBAAa,WAAW;YAAO;YACxE;gBAAE,OAAO;gBAA4B,UAAU;gBAAU,WAAW;YAAS;SAC9E;QACD,aAAa;YACX;gBAAE,OAAO;gBAA6B,UAAU;gBAAY,WAAW;YAAO;YAC9E;gBAAE,OAAO;gBAA4B,UAAU;gBAAY,WAAW;YAAO;YAC7E;gBAAE,OAAO;gBAA0B,UAAU;gBAAc,WAAW;YAAS;SAChF;QACD,UAAU;YACR;gBAAE,OAAO;gBAAqB,UAAU;gBAAY,WAAW;YAAO;YACtE;gBAAE,OAAO;gBAAwB,UAAU;gBAAY,WAAW;YAAO;YACzE;gBAAE,OAAO;gBAAwB,UAAU;gBAAU,WAAW;YAAS;SAC1E;QACD,eAAe;YACb;gBAAE,OAAO;gBAA0B,UAAU;gBAAY,WAAW;YAAO;YAC3E;gBAAE,OAAO;gBAA0B,UAAU;gBAAU,WAAW;YAAO;YACzE;gBAAE,OAAO;gBAA4B,UAAU;gBAAU,WAAW;YAAS;SAC9E;QACD,WAAW;YACT;gBAAE,OAAO;gBAAiC,UAAU;gBAAY,WAAW;YAAO;YAClF;gBAAE,OAAO;gBAAoC,UAAU;gBAAY,WAAW;YAAO;YACrF;gBAAE,OAAO;gBAAyB,UAAU;gBAAa,WAAW;YAAS;SAC9E;IACH;IAEA,MAAM,mBAAmB,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,YAAY;IAChF,MAAM,mBAAmB,cAAc,CAAC,aAAa,WAAW,GAAG,IAAI,cAAc,CAAC,UAAU;IAChG,MAAM,cAAc;QAClB;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAO;QAC7E;YAAE,OAAO,GAAG,SAAS,sBAAsB,CAAC;YAAE,UAAU;YAAa,WAAW;QAAO;QACvF;YAAE,OAAO,GAAG,SAAS,gBAAgB,CAAC;YAAE,UAAU;YAAS,WAAW;QAAS;KAChF;IAED,OAAO;WAAI;WAAqB;WAAqB;KAAY,CAAC,KAAK,CAAC,GAAG;AAC7E;AAEA,gDAAgD;AAChD,SAAS,yBAAyB,YAAoB,EAAE,QAAgB;IACtE,MAAM,eAAe;QACnB;YACE,MAAM;YACN,UAAU,GAAG,SAAS,4BAA4B,CAAC;YACnD,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,aAAa,0BAA0B,EAAE,UAAU;YAChE,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,SAAS,8BAA8B,CAAC;YACrD,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;QACA;YACE,MAAM;YACN,UAAU,GAAG,SAAS,yCAAyC,CAAC;YAChE,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;KACD;IAED,OAAO,aAAa,KAAK,CAAC,GAAG;AAC/B", "debugId": null}}, {"offset": {"line": 4399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/content-generator.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 Content Generator\r\n * Handles content generation for the stable foundation model\r\n */\r\n\r\nimport type {\r\n  IContentGenerator,\r\n  ContentGenerationRequest,\r\n  GenerationResponse\r\n} from '../../types/model-types';\r\nimport type { GeneratedPost } from '@/lib/types';\r\nimport { generateRevo10Content } from '@/ai/revo-1.0-service';\r\n\r\nexport class Revo10ContentGenerator implements IContentGenerator {\r\n  private readonly modelId = 'revo-1.0';\r\n\r\n  /**\r\n   * Generate content using Revo 1.0 specifications\r\n   */\r\n  async generateContent(request: ContentGenerationRequest): Promise<GenerationResponse<GeneratedPost>> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n\r\n      // Validate request\r\n      if (!this.validateRequest(request)) {\r\n        throw new Error('Invalid content generation request for Revo 1.0');\r\n      }\r\n\r\n      // Prepare generation parameters for Revo 1.0\r\n      const generationParams = this.prepareGenerationParams(request);\r\n\r\n      // Generate content using Revo 1.0 service with Gemini 2.5 Flash Image Preview\r\n      const postDetails = await generateRevo10Content({\r\n        businessType: generationParams.businessType,\r\n        businessName: generationParams.businessName || 'Business',\r\n        location: generationParams.location || 'Location',\r\n        platform: generationParams.variants[0]?.platform || 'instagram',\r\n        writingTone: generationParams.writingTone || 'professional',\r\n        contentThemes: generationParams.contentThemes || [],\r\n        targetAudience: generationParams.targetAudience || 'General',\r\n        services: generationParams.services || '',\r\n        keyFeatures: generationParams.keyFeatures || '',\r\n        competitiveAdvantages: generationParams.competitiveAdvantages || '',\r\n        dayOfWeek: generationParams.dayOfWeek || 'Monday',\r\n        currentDate: generationParams.currentDate || new Date().toLocaleDateString(),\r\n        primaryColor: generationParams.primaryColor,\r\n        visualStyle: generationParams.visualStyle\r\n      });\r\n\r\n      // Generate image using the catchy words and brand profile data\r\n\r\n      const { generateRevo10Image } = await import('@/ai/revo-1.0-service');\r\n      // Prepare structured text for image\r\n      const imageTextComponents = [];\r\n      if (postDetails.catchyWords) imageTextComponents.push(postDetails.catchyWords);\r\n      if (postDetails.subheadline) imageTextComponents.push(postDetails.subheadline);\r\n      if (postDetails.callToAction) imageTextComponents.push(postDetails.callToAction);\r\n\r\n      const structuredImageText = imageTextComponents.join(' | ');\r\n\r\n      // Get real-time context for enhanced design\r\n      const realTimeContext = (postDetails as any).realTimeContext || null;\r\n\r\n      const imageResult = await generateRevo10Image({\r\n        businessType: generationParams.businessType,\r\n        businessName: generationParams.businessName || 'Business',\r\n        platform: generationParams.variants[0]?.platform || 'instagram',\r\n        visualStyle: generationParams.visualStyle || 'modern',\r\n        primaryColor: generationParams.primaryColor || '#3B82F6',\r\n        accentColor: generationParams.accentColor || '#1E40AF',\r\n        backgroundColor: generationParams.backgroundColor || '#FFFFFF',\r\n        imageText: structuredImageText,\r\n        designDescription: `Professional ${generationParams.businessType} content with structured headline, subheadline, and CTA for ${generationParams.variants[0]?.platform || 'instagram'}`,\r\n        logoDataUrl: generationParams.logoDataUrl,\r\n        location: generationParams.location,\r\n        headline: postDetails.catchyWords,\r\n        subheadline: postDetails.subheadline,\r\n        callToAction: postDetails.callToAction,\r\n        realTimeContext: realTimeContext,\r\n        creativeContext: (postDetails as any).creativeContext // 🎨 Pass creative context to image generation\r\n      });\r\n\r\n      // Update variants with the generated image\r\n      postDetails.variants = postDetails.variants.map(variant => ({\r\n        ...variant,\r\n        imageUrl: imageResult.imageUrl\r\n      }));\r\n\r\n      // Create the generated post\r\n      const generatedPost: GeneratedPost = {\r\n        id: new Date().toISOString(),\r\n        date: new Date().toISOString(),\r\n        content: postDetails.content,\r\n        hashtags: postDetails.hashtags,\r\n        status: 'generated',\r\n        variants: postDetails.variants,\r\n        catchyWords: postDetails.catchyWords,\r\n        subheadline: postDetails.subheadline,\r\n        callToAction: postDetails.callToAction,\r\n        // Revo 1.0 doesn't include advanced features\r\n        contentVariants: undefined,\r\n        hashtagAnalysis: undefined,\r\n        marketIntelligence: undefined,\r\n        localContext: undefined,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          modelVersion: '1.0.0',\r\n          generationType: 'standard',\r\n          processingTime: Date.now() - startTime,\r\n          qualityLevel: 'standard'\r\n        }\r\n      };\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      const qualityScore = this.calculateQualityScore(generatedPost);\r\n\r\n\r\n      return {\r\n        success: true,\r\n        data: generatedPost,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore,\r\n          creditsUsed: 1.5, // Revo 1.0 now uses 1.5 credits for enhanced capabilities\r\n          enhancementsApplied: ['enhanced-optimization', 'platform-formatting', 'gemini-2.5-flash-image']\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore: 0,\r\n          creditsUsed: 0,\r\n          enhancementsApplied: []\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate content generation request for Revo 1.0\r\n   */\r\n  private validateRequest(request: ContentGenerationRequest): boolean {\r\n    // Check required fields\r\n    if (!request.profile || !request.platform) {\r\n      return false;\r\n    }\r\n\r\n    // Check if profile has minimum required information\r\n    if (!request.profile.businessType || !request.profile.businessName) {\r\n      return false;\r\n    }\r\n\r\n    // Revo 1.0 doesn't support artifacts\r\n    if (request.artifactIds && request.artifactIds.length > 0) {\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Prepare generation parameters optimized for Revo 1.0\r\n   */\r\n  private prepareGenerationParams(request: ContentGenerationRequest) {\r\n    const { profile, platform, brandConsistency } = request;\r\n    const today = new Date();\r\n\r\n    // Convert arrays to strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n    return {\r\n      businessName: profile.businessName || profile.name || 'Business', // Add business name\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: today.toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: '1:1', // Revo 1.0 only supports 1:1\r\n      }],\r\n      services: servicesString,\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Revo 1.0 specific constraints (updated to match config)\r\n      modelConstraints: {\r\n        maxComplexity: 'enhanced', // Upgraded from basic\r\n        enhancedFeatures: true,    // Now enabled\r\n        realTimeContext: true,     // Now enabled\r\n        trendingTopics: true,      // Now enabled\r\n        artifactSupport: false     // Keep disabled for Revo 1.0\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate quality score for generated content\r\n   */\r\n  private calculateQualityScore(post: GeneratedPost): number {\r\n    let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)\r\n\r\n    // Content quality checks\r\n    if (post.content && post.content.length > 50) score += 1;\r\n    if (post.content && post.content.length > 100) score += 0.5;\r\n\r\n    // Hashtag quality\r\n    if (post.hashtags && post.hashtags.length >= 5) score += 1;\r\n    if (post.hashtags && post.hashtags.length >= 10) score += 0.5;\r\n\r\n    // Catchy words presence\r\n    if (post.catchyWords && post.catchyWords.trim().length > 0) score += 1;\r\n\r\n    // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)\r\n    if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {\r\n      score += 1.5; // Increased from 1 for better image quality\r\n    }\r\n\r\n    // Cap at 10\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Health check for content generator\r\n   */\r\n  async healthCheck(): Promise<boolean> {\r\n    try {\r\n      // Check if we can access the AI service\r\n      const hasApiKey = !!(\r\n        process.env.GEMINI_API_KEY ||\r\n        process.env.GOOGLE_API_KEY ||\r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n\r\n      return hasApiKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get generator-specific information\r\n   */\r\n  getGeneratorInfo() {\r\n    return {\r\n      modelId: this.modelId,\r\n      type: 'content',\r\n      capabilities: [\r\n        'Enhanced content generation with Gemini 2.5 Flash Image Preview',\r\n        'Platform-specific formatting',\r\n        'Hashtag generation',\r\n        'Catchy words creation',\r\n        'Brand consistency (enhanced)',\r\n        'Perfect text rendering',\r\n        'High-resolution image support'\r\n      ],\r\n      limitations: [\r\n        'No real-time context',\r\n        'No trending topics',\r\n        'No artifact support',\r\n        'Enhanced quality optimization',\r\n        'Limited customization'\r\n      ],\r\n      averageProcessingTime: '20-30 seconds (enhanced for quality)',\r\n      qualityRange: '8-9/10 (upgraded from 6-8/10)',\r\n      costPerGeneration: 1.5 // Upgraded from 1 for enhanced capabilities\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAQD;;AAEO,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,gBAAgB,OAAiC,EAA8C;QACnG,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YAEF,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,6CAA6C;YAC7C,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC;YAEtD,8EAA8E;YAC9E,MAAM,cAAc,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAC9C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,eAAe,iBAAiB,aAAa,IAAI,EAAE;gBACnD,gBAAgB,iBAAiB,cAAc,IAAI;gBACnD,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,uBAAuB,iBAAiB,qBAAqB,IAAI;gBACjE,WAAW,iBAAiB,SAAS,IAAI;gBACzC,aAAa,iBAAiB,WAAW,IAAI,IAAI,OAAO,kBAAkB;gBAC1E,cAAc,iBAAiB,YAAY;gBAC3C,aAAa,iBAAiB,WAAW;YAC3C;YAEA,+DAA+D;YAE/D,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,oCAAoC;YACpC,MAAM,sBAAsB,EAAE;YAC9B,IAAI,YAAY,WAAW,EAAE,oBAAoB,IAAI,CAAC,YAAY,WAAW;YAC7E,IAAI,YAAY,WAAW,EAAE,oBAAoB,IAAI,CAAC,YAAY,WAAW;YAC7E,IAAI,YAAY,YAAY,EAAE,oBAAoB,IAAI,CAAC,YAAY,YAAY;YAE/E,MAAM,sBAAsB,oBAAoB,IAAI,CAAC;YAErD,4CAA4C;YAC5C,MAAM,kBAAkB,AAAC,YAAoB,eAAe,IAAI;YAEhE,MAAM,cAAc,MAAM,oBAAoB;gBAC5C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,iBAAiB,iBAAiB,eAAe,IAAI;gBACrD,WAAW;gBACX,mBAAmB,CAAC,aAAa,EAAE,iBAAiB,YAAY,CAAC,4DAA4D,EAAE,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY,aAAa;gBACtL,aAAa,iBAAiB,WAAW;gBACzC,UAAU,iBAAiB,QAAQ;gBACnC,UAAU,YAAY,WAAW;gBACjC,aAAa,YAAY,WAAW;gBACpC,cAAc,YAAY,YAAY;gBACtC,iBAAiB;gBACjB,iBAAiB,AAAC,YAAoB,eAAe,CAAC,+CAA+C;YACvG;YAEA,2CAA2C;YAC3C,YAAY,QAAQ,GAAG,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC1D,GAAG,OAAO;oBACV,UAAU,YAAY,QAAQ;gBAChC,CAAC;YAED,4BAA4B;YAC5B,MAAM,gBAA+B;gBACnC,IAAI,IAAI,OAAO,WAAW;gBAC1B,MAAM,IAAI,OAAO,WAAW;gBAC5B,SAAS,YAAY,OAAO;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,QAAQ;gBACR,UAAU,YAAY,QAAQ;gBAC9B,aAAa,YAAY,WAAW;gBACpC,aAAa,YAAY,WAAW;gBACpC,cAAc,YAAY,YAAY;gBACtC,6CAA6C;gBAC7C,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,cAAc;gBACd,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB,KAAK,GAAG,KAAK;oBAC7B,cAAc;gBAChB;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAGhD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBAAC;wBAAyB;wBAAuB;qBAAyB;gBACjG;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAiC,EAAW;QAClE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACzC,OAAO;QACT;QAEA,oDAAoD;QACpD,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,EAAE;YAClE,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG,CAC3D;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,OAAiC,EAAE;QACjE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG;QAChD,MAAM,QAAQ,IAAI;QAElB,8CAA8C;QAC9C,MAAM,oBAAoB,MAAM,OAAO,CAAC,QAAQ,WAAW,IACvD,QAAQ,WAAW,CAAC,IAAI,CAAC,QACzB,QAAQ,WAAW,IAAI;QAE3B,MAAM,8BAA8B,MAAM,OAAO,CAAC,QAAQ,qBAAqB,IAC3E,QAAQ,qBAAqB,CAAC,IAAI,CAAC,QACnC,QAAQ,qBAAqB,IAAI;QAErC,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,QAAQ,IACjD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UACrB,OAAO,YAAY,YAAY,QAAQ,IAAI,GACvC,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,IAAI,IAAI,GAC/C,SACJ,IAAI,CAAC,QACL,QAAQ,QAAQ,IAAI;QAExB,OAAO;YACL,cAAc,QAAQ,YAAY,IAAI,QAAQ,IAAI,IAAI;YACtD,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa;YACpC,aAAa,QAAQ,WAAW;YAChC,aAAa,QAAQ,WAAW;YAChC,gBAAgB,kBAAkB,oBAAqB,QAAQ,cAAc,IAAI,EAAE,GAAI,EAAE;YACzF,cAAc,QAAQ,YAAY;YAClC,aAAa,QAAQ,WAAW;YAChC,iBAAiB,QAAQ,eAAe;YACxC,WAAW,MAAM,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YAC/D,aAAa,MAAM,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,OAAO;gBAAQ,KAAK;YAAU;YAChG,UAAU;gBAAC;oBACT,UAAU;oBACV,aAAa;gBACf;aAAE;YACF,UAAU;YACV,gBAAgB,QAAQ,cAAc;YACtC,aAAa;YACb,uBAAuB;YACvB,kBAAkB,oBAAoB;gBAAE,mBAAmB;gBAAO,mBAAmB;YAAK;YAC1F,0DAA0D;YAC1D,kBAAkB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB,MAAU,6BAA6B;YAC1D;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAmB,EAAU;QACzD,IAAI,QAAQ,GAAG,kEAAkE;QAEjF,yBAAyB;QACzB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,SAAS;QACvD,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,SAAS;QAExD,kBAAkB;QAClB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,GAAG,SAAS;QACzD,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,IAAI,SAAS;QAE1D,wBAAwB;QACxB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,SAAS;QAErE,yEAAyE;QACzE,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1E,SAAS,KAAK,4CAA4C;QAC5D;QAEA,YAAY;QACZ,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,wCAAwC;YACxC,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,uBAAuB;YACvB,cAAc;YACd,mBAAmB,IAAI,4CAA4C;QACrE;IACF;AACF", "debugId": null}}, {"offset": {"line": 4658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/design-generator.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 Design Generator\r\n * Handles design generation for the stable foundation model\r\n */\r\n\r\nimport type {\r\n  IDesignGenerator,\r\n  DesignGenerationRequest,\r\n  GenerationResponse\r\n} from '../../types/model-types';\r\nimport type { PostVariant } from '@/lib/types';\r\n\r\nexport class Revo10DesignGenerator implements IDesignGenerator {\r\n  private readonly modelId = 'revo-1.0';\r\n\r\n  /**\r\n   * Generate design using Revo 1.0 specifications\r\n   */\r\n  async generateDesign(request: DesignGenerationRequest): Promise<GenerationResponse<PostVariant>> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n\r\n      // Validate request\r\n      if (!this.validateRequest(request)) {\r\n        throw new Error('Invalid design generation request for Revo 1.0');\r\n      }\r\n\r\n      // Generate design using basic Gemini 2.0 approach\r\n      const designResult = await this.generateBasicDesign(request);\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      const qualityScore = this.calculateQualityScore(designResult);\r\n\r\n\r\n      return {\r\n        success: true,\r\n        data: designResult,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore,\r\n          creditsUsed: 1.5, // Revo 1.0 now uses 1.5 credits for enhanced capabilities\r\n          enhancementsApplied: ['enhanced-styling', 'brand-colors', 'platform-optimization', 'gemini-2.5-flash-image']\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore: 0,\r\n          creditsUsed: 0,\r\n          enhancementsApplied: []\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate basic design using Gemini 2.0\r\n   */\r\n  private async generateBasicDesign(request: DesignGenerationRequest): Promise<PostVariant> {\r\n    try {\r\n      // Import the basic generation flow\r\n      const { generateRevo10Design } = await import('@/ai/revo-1.0-service');\r\n\r\n      // Prepare image text\r\n      let imageText: string;\r\n      if (typeof request.imageText === 'string') {\r\n        imageText = request.imageText;\r\n      } else {\r\n        // Combine components for Revo 1.0 (simpler approach)\r\n        imageText = request.imageText.catchyWords;\r\n        if (request.imageText.subheadline) {\r\n          imageText += '\\n' + request.imageText.subheadline;\r\n        }\r\n      }\r\n\r\n      // Create a simplified generation request\r\n      const generationParams = {\r\n        businessType: request.businessType,\r\n        location: request.brandProfile.location || '',\r\n        writingTone: request.brandProfile.writingTone || 'professional',\r\n        contentThemes: request.brandProfile.contentThemes || '',\r\n        visualStyle: request.visualStyle,\r\n        logoDataUrl: request.brandProfile.logoDataUrl,\r\n        designExamples: request.brandConsistency?.strictConsistency ?\r\n          (request.brandProfile.designExamples || []) : [],\r\n        primaryColor: request.brandProfile.primaryColor,\r\n        accentColor: request.brandProfile.accentColor,\r\n        backgroundColor: request.brandProfile.backgroundColor,\r\n        dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n        currentDate: new Date().toLocaleDateString('en-US', {\r\n          year: 'numeric', month: 'long', day: 'numeric'\r\n        }),\r\n        variants: [{\r\n          platform: request.platform,\r\n          aspectRatio: '1:1', // Revo 1.0 only supports 1:1\r\n        }],\r\n        services: '',\r\n        targetAudience: request.brandProfile.targetAudience || '',\r\n        keyFeatures: '',\r\n        competitiveAdvantages: '',\r\n        brandConsistency: request.brandConsistency || {\r\n          strictConsistency: false,\r\n          followBrandColors: true\r\n        }\r\n      };\r\n\r\n      // First generate design description\r\n      const designResult = await generateRevo10Design({\r\n        businessType: generationParams.businessType,\r\n        businessName: generationParams.businessName || 'Business',\r\n        platform: generationParams.variants[0]?.platform || 'instagram',\r\n        visualStyle: generationParams.visualStyle || 'modern',\r\n        primaryColor: generationParams.primaryColor || '#3B82F6',\r\n        accentColor: generationParams.accentColor || '#1E40AF',\r\n        backgroundColor: generationParams.backgroundColor || '#FFFFFF',\r\n        imageText: imageText || 'Your Text Here'\r\n      });\r\n\r\n      // Then generate the actual image using the design description\r\n      const { generateRevo10Image } = await import('@/ai/revo-1.0-service');\r\n      const imageResult = await generateRevo10Image({\r\n        businessType: generationParams.businessType,\r\n        businessName: generationParams.businessName || 'Business',\r\n        platform: generationParams.variants[0]?.platform || 'instagram',\r\n        visualStyle: generationParams.visualStyle || 'modern',\r\n        primaryColor: generationParams.primaryColor || '#3B82F6',\r\n        imageText: imageText || 'Your Text Here',\r\n        designDescription: designResult.design\r\n      });\r\n\r\n      // Return the complete result with actual image URL\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: imageResult.imageUrl,\r\n        caption: imageText,\r\n        hashtags: [],\r\n        design: designResult.design,\r\n        aspectRatio: imageResult.aspectRatio,\r\n        resolution: imageResult.resolution,\r\n        quality: imageResult.quality\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Return a fallback variant\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: '', // Empty URL indicates generation failure\r\n        caption: typeof request.imageText === 'string' ?\r\n          request.imageText : request.imageText.catchyWords,\r\n        hashtags: []\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate design generation request for Revo 1.0\r\n   */\r\n  private validateRequest(request: DesignGenerationRequest): boolean {\r\n    // Check required fields\r\n    if (!request.businessType || !request.platform || !request.brandProfile) {\r\n      return false;\r\n    }\r\n\r\n    // Check image text\r\n    if (!request.imageText) {\r\n      return false;\r\n    }\r\n\r\n    // Revo 1.0 only supports 1:1 aspect ratio\r\n    // We don't enforce this here as the generator will handle it\r\n\r\n    // Warn about unsupported features\r\n    if (request.artifactInstructions) {\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Calculate quality score for generated design\r\n   */\r\n  private calculateQualityScore(variant: PostVariant): number {\r\n    let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)\r\n\r\n    // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)\r\n    if (variant.imageUrl && variant.imageUrl.length > 0) {\r\n      score += 2.5; // Increased from 2 for better image quality\r\n    }\r\n\r\n    // Caption quality\r\n    if (variant.caption && variant.caption.length > 10) {\r\n      score += 1;\r\n    }\r\n\r\n    // Hashtags presence\r\n    if (variant.hashtags && variant.hashtags.length > 0) {\r\n      score += 1;\r\n    }\r\n\r\n    // Platform optimization (basic check)\r\n    if (variant.platform) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Revo 1.0 now has higher quality ceiling due to Gemini 2.5 Flash Image Preview\r\n    return Math.min(score, 9.0);\r\n  }\r\n\r\n  /**\r\n   * Health check for design generator\r\n   */\r\n  async healthCheck(): Promise<boolean> {\r\n    try {\r\n      // Check if we can access the AI service\r\n      const hasApiKey = !!(\r\n        process.env.GEMINI_API_KEY ||\r\n        process.env.GOOGLE_API_KEY ||\r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n\r\n      return hasApiKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get generator-specific information\r\n   */\r\n  getGeneratorInfo() {\r\n    return {\r\n      modelId: this.modelId,\r\n      type: 'design',\r\n      capabilities: [\r\n        'Enhanced image generation with Gemini 2.5 Flash Image Preview',\r\n        '1:1 aspect ratio only',\r\n        'Brand color integration',\r\n        'Logo placement',\r\n        'Platform optimization',\r\n        'Text overlay (enhanced)',\r\n        'Perfect text rendering',\r\n        'High-resolution 2048x2048 output'\r\n      ],\r\n      limitations: [\r\n        'Single aspect ratio (1:1)',\r\n        'No artifact support',\r\n        'Enhanced styling options',\r\n        'Limited customization',\r\n        'High-resolution support'\r\n      ],\r\n      supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\r\n      supportedAspectRatios: ['1:1'],\r\n      averageProcessingTime: '25-35 seconds (enhanced for quality)',\r\n      qualityRange: '7-9/10 (upgraded from 5-7.5/10)',\r\n      costPerGeneration: 1.5, // Upgraded from 1 for enhanced capabilities\r\n      resolution: '2048x2048 (upgraded from 1024x1024)'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get supported features for this design generator\r\n   */\r\n  getSupportedFeatures() {\r\n    return {\r\n      aspectRatios: ['1:1'],\r\n      textOverlay: 'enhanced', // Upgraded from basic\r\n      brandIntegration: 'standard',\r\n      logoPlacement: true,\r\n      colorCustomization: true,\r\n      templateSupport: false,\r\n      artifactSupport: false,\r\n      advancedStyling: true, // Upgraded from false\r\n      multipleVariants: false,\r\n      highResolution: true, // NEW: 2048x2048 support\r\n      perfectTextRendering: true // NEW: Gemini 2.5 Flash Image Preview feature\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AASM,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,eAAe,OAAgC,EAA4C;QAC/F,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YAEF,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,kDAAkD;YAClD,MAAM,eAAe,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAEpD,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAGhD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBAAC;wBAAoB;wBAAgB;wBAAyB;qBAAyB;gBAC9G;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,OAAgC,EAAwB;QACxF,IAAI;YACF,mCAAmC;YACnC,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,qBAAqB;YACrB,IAAI;YACJ,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,YAAY,QAAQ,SAAS;YAC/B,OAAO;gBACL,qDAAqD;gBACrD,YAAY,QAAQ,SAAS,CAAC,WAAW;gBACzC,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE;oBACjC,aAAa,OAAO,QAAQ,SAAS,CAAC,WAAW;gBACnD;YACF;YAEA,yCAAyC;YACzC,MAAM,mBAAmB;gBACvB,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,YAAY,CAAC,QAAQ,IAAI;gBAC3C,aAAa,QAAQ,YAAY,CAAC,WAAW,IAAI;gBACjD,eAAe,QAAQ,YAAY,CAAC,aAAa,IAAI;gBACrD,aAAa,QAAQ,WAAW;gBAChC,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,gBAAgB,QAAQ,gBAAgB,EAAE,oBACvC,QAAQ,YAAY,CAAC,cAAc,IAAI,EAAE,GAAI,EAAE;gBAClD,cAAc,QAAQ,YAAY,CAAC,YAAY;gBAC/C,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,iBAAiB,QAAQ,YAAY,CAAC,eAAe;gBACrD,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAAE,SAAS;gBAAO;gBACpE,aAAa,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAClD,MAAM;oBAAW,OAAO;oBAAQ,KAAK;gBACvC;gBACA,UAAU;oBAAC;wBACT,UAAU,QAAQ,QAAQ;wBAC1B,aAAa;oBACf;iBAAE;gBACF,UAAU;gBACV,gBAAgB,QAAQ,YAAY,CAAC,cAAc,IAAI;gBACvD,aAAa;gBACb,uBAAuB;gBACvB,kBAAkB,QAAQ,gBAAgB,IAAI;oBAC5C,mBAAmB;oBACnB,mBAAmB;gBACrB;YACF;YAEA,oCAAoC;YACpC,MAAM,eAAe,MAAM,qBAAqB;gBAC9C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,iBAAiB,iBAAiB,eAAe,IAAI;gBACrD,WAAW,aAAa;YAC1B;YAEA,8DAA8D;YAC9D,MAAM,EAAE,mBAAmB,EAAE,GAAG;YAChC,MAAM,cAAc,MAAM,oBAAoB;gBAC5C,cAAc,iBAAiB,YAAY;gBAC3C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,UAAU,iBAAiB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBACpD,aAAa,iBAAiB,WAAW,IAAI;gBAC7C,cAAc,iBAAiB,YAAY,IAAI;gBAC/C,WAAW,aAAa;gBACxB,mBAAmB,aAAa,MAAM;YACxC;YAEA,mDAAmD;YACnD,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,YAAY,QAAQ;gBAC9B,SAAS;gBACT,UAAU,EAAE;gBACZ,QAAQ,aAAa,MAAM;gBAC3B,aAAa,YAAY,WAAW;gBACpC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO;YAC9B;QAEF,EAAE,OAAO,OAAO;YAEd,4BAA4B;YAC5B,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU;gBACV,SAAS,OAAO,QAAQ,SAAS,KAAK,WACpC,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,WAAW;gBACnD,UAAU,EAAE;YACd;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAgC,EAAW;QACjE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,YAAY,EAAE;YACvE,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO;QACT;QAEA,0CAA0C;QAC1C,6DAA6D;QAE7D,kCAAkC;QAClC,IAAI,QAAQ,oBAAoB,EAAE,CAClC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,OAAoB,EAAU;QAC1D,IAAI,QAAQ,GAAG,kEAAkE;QAEjF,yEAAyE;QACzE,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS,KAAK,4CAA4C;QAC5D;QAEA,kBAAkB;QAClB,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,IAAI;YAClD,SAAS;QACX;QAEA,oBAAoB;QACpB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS;QACX;QAEA,sCAAsC;QACtC,IAAI,QAAQ,QAAQ,EAAE;YACpB,SAAS;QACX;QAEA,gFAAgF;QAChF,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,wCAAwC;YACxC,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAoB;gBAAC;gBAAa;gBAAY;gBAAW;aAAW;YACpE,uBAAuB;gBAAC;aAAM;YAC9B,uBAAuB;YACvB,cAAc;YACd,mBAAmB;YACnB,YAAY;QACd;IACF;IAEA;;GAEC,GACD,uBAAuB;QACrB,OAAO;YACL,cAAc;gBAAC;aAAM;YACrB,aAAa;YACb,kBAAkB;YAClB,eAAe;YACf,oBAAoB;YACpB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;YAClB,gBAAgB;YAChB,sBAAsB,KAAK,8CAA8C;QAC3E;IACF;AACF", "debugId": null}}, {"offset": {"line": 4920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.0/index.ts"], "sourcesContent": ["/**\r\n * Revo 1.0 Model Implementation\r\n * Standard Model - Stable Foundation\r\n */\r\n\r\nimport type {\r\n  IModelImplementation,\r\n  IContentGenerator,\r\n  IDesignGenerator,\r\n  ContentGenerationRequest,\r\n  DesignGenerationRequest,\r\n  GenerationResponse\r\n} from '../../types/model-types';\r\nimport { getModelConfig } from '../../config/model-configs';\r\nimport { Revo10ContentGenerator } from './content-generator';\r\nimport { Revo10DesignGenerator } from './design-generator';\r\n\r\nexport class Revo10Implementation implements IModelImplementation {\r\n  public readonly model;\r\n  public readonly contentGenerator: IContentGenerator;\r\n  public readonly designGenerator: IDesignGenerator;\r\n\r\n  constructor() {\r\n    try {\r\n      this.model = getModelConfig('revo-1.0');\r\n      \r\n      this.contentGenerator = new Revo10ContentGenerator();\r\n      \r\n      this.designGenerator = new Revo10DesignGenerator();\r\n      \r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if the model is available and ready to use\r\n   */\r\n  async isAvailable(): Promise<boolean> {\r\n    try {\r\n      // Check if the underlying AI service (Gemini 2.5 Flash Image Preview) is available\r\n      // For now, we'll assume it's available if we have the API key\r\n      const hasApiKey = !!(\r\n        process.env.GEMINI_API_KEY ||\r\n        process.env.GOOGLE_API_KEY ||\r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n\r\n      return hasApiKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate a generation request for this model\r\n   */\r\n  validateRequest(request: ContentGenerationRequest | DesignGenerationRequest): boolean {\r\n    try {\r\n      // Basic validation\r\n      if (!request || !request.modelId) {\r\n        return false;\r\n      }\r\n\r\n      // Check if this is the correct model\r\n      if (request.modelId !== 'revo-1.0') {\r\n        return false;\r\n      }\r\n\r\n      // Content generation validation\r\n      if ('profile' in request) {\r\n        const contentRequest = request as ContentGenerationRequest;\r\n        return !!(\r\n          contentRequest.profile &&\r\n          contentRequest.platform &&\r\n          contentRequest.profile.businessType\r\n        );\r\n      }\r\n\r\n      // Design generation validation\r\n      if ('businessType' in request) {\r\n        const designRequest = request as DesignGenerationRequest;\r\n        return !!(\r\n          designRequest.businessType &&\r\n          designRequest.platform &&\r\n          designRequest.visualStyle &&\r\n          designRequest.brandProfile\r\n        );\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get model-specific information\r\n   */\r\n  getModelInfo() {\r\n    return {\r\n      id: this.model.id,\r\n      name: this.model.name,\r\n      version: this.model.version,\r\n      description: this.model.description,\r\n      status: this.model.status,\r\n      capabilities: this.model.capabilities,\r\n      pricing: this.model.pricing,\r\n      features: this.model.features,\r\n      strengths: [\r\n        'Reliable and stable performance',\r\n        'Cost-effective for basic needs',\r\n        'Proven track record',\r\n        'Fast processing times',\r\n        'Consistent quality',\r\n        'Enhanced AI capabilities with Gemini 2.5 Flash Image Preview',\r\n        'Perfect text rendering',\r\n        'High-resolution 2048x2048 output',\r\n        'Advanced image generation'\r\n      ],\r\n      limitations: [\r\n        'Limited to 1:1 aspect ratio',\r\n        'No artifact support',\r\n        'Basic brand consistency',\r\n        'No real-time context',\r\n        'No video generation'\r\n      ],\r\n      bestUseCases: [\r\n        'Small businesses starting out',\r\n        'Personal brands',\r\n        'Budget-conscious users',\r\n        'Basic social media content',\r\n        'Consistent daily posting'\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get performance metrics for this model\r\n   */\r\n  async getPerformanceMetrics() {\r\n    return {\r\n      modelId: this.model.id,\r\n      averageProcessingTime: 30000, // 30 seconds (upgraded from 15s)\r\n      successRate: 0.97, // 97% success rate (upgraded from 95%)\r\n      averageQualityScore: 8.5, // Upgraded from 7.2\r\n      costEfficiency: 'high',\r\n      reliability: 'excellent',\r\n      userSatisfaction: 4.5, // out of 5 (upgraded from 4.1)\r\n      lastUpdated: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Health check for this specific model\r\n   */\r\n  async healthCheck(): Promise<{ healthy: boolean; details: any }> {\r\n    try {\r\n      const isAvailable = await this.isAvailable();\r\n      const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;\r\n      const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;\r\n\r\n      const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;\r\n\r\n      return {\r\n        healthy,\r\n        details: {\r\n          modelAvailable: isAvailable,\r\n          contentGenerator: contentGeneratorHealthy,\r\n          designGenerator: designGeneratorHealthy,\r\n          timestamp: new Date().toISOString()\r\n        }\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        healthy: false,\r\n        details: {\r\n          error: error instanceof Error ? error.message : 'Unknown error',\r\n          timestamp: new Date().toISOString()\r\n        }\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export generators for direct use if needed\r\nexport { Revo10ContentGenerator } from './content-generator';\r\nexport { Revo10DesignGenerator } from './design-generator';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAUD;AACA;AACA;;;;AAEO,MAAM;IACK,MAAM;IACN,iBAAoC;IACpC,gBAAkC;IAElD,aAAc;QACZ,IAAI;YACF,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE;YAE5B,IAAI,CAAC,gBAAgB,GAAG,IAAI,yKAAA,CAAA,yBAAsB;YAElD,IAAI,CAAC,eAAe,GAAG,IAAI,wKAAA,CAAA,wBAAqB;QAElD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,mFAAmF;YACnF,8DAA8D;YAC9D,MAAM,YAAY,CAAC,CAAC,CAClB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,gBAAgB,OAA2D,EAAW;QACpF,IAAI;YACF,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAAE;gBAChC,OAAO;YACT;YAEA,qCAAqC;YACrC,IAAI,QAAQ,OAAO,KAAK,YAAY;gBAClC,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAI,aAAa,SAAS;gBACxB,MAAM,iBAAiB;gBACvB,OAAO,CAAC,CAAC,CACP,eAAe,OAAO,IACtB,eAAe,QAAQ,IACvB,eAAe,OAAO,CAAC,YAAY,AACrC;YACF;YAEA,+BAA+B;YAC/B,IAAI,kBAAkB,SAAS;gBAC7B,MAAM,gBAAgB;gBACtB,OAAO,CAAC,CAAC,CACP,cAAc,YAAY,IAC1B,cAAc,QAAQ,IACtB,cAAc,WAAW,IACzB,cAAc,YAAY,AAC5B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,eAAe;QACb,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,cAAc,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB;QAC5B,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YACtB,uBAAuB;YACvB,aAAa;YACb,qBAAqB;YACrB,gBAAgB;YAChB,aAAa;YACb,kBAAkB;YAClB,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,cAA2D;QAC/D,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW;YAC1C,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ;YAC/E,MAAM,yBAAyB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,QAAQ;YAE7E,MAAM,UAAU,eAAe,2BAA2B;YAE1D,OAAO;gBACL;gBACA,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;oBACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF;IACF;AACF", "debugId": null}}]}