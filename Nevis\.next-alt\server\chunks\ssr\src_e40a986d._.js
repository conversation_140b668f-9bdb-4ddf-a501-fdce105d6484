module.exports = {

"[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const labelVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(labelVariants(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 8,
        columnNumber: 7
    }, this);
});
Textarea.displayName = 'Textarea';
;
}}),
"[project]/src/app/data:06016b [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78a2cc90f0c309202520d68173137d83d08ea32806":"generateContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentAction": (()=>generateContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("78a2cc90f0c309202520d68173137d83d08ea32806", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:91530e [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7002a50d713c2964d62133b2af4480664176bd8291":"generateVideoContentAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateVideoContentAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7002a50d713c2964d62133b2af4480664176bd8291", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateVideoContentAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/components/ui/tabs.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tabs": (()=>Tabs),
    "TabsContent": (()=>TabsContent),
    "TabsList": (()=>TabsList),
    "TabsTrigger": (()=>TabsTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-tabs/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Tabs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"];
const TabsList = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["List"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 14,
        columnNumber: 3
    }, this));
TabsList.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["List"].displayName;
const TabsTrigger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Trigger"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 29,
        columnNumber: 3
    }, this));
TabsTrigger.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Trigger"].displayName;
const TabsContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Content"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tabs.tsx",
        lineNumber: 44,
        columnNumber: 3
    }, this));
TabsContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Content"].displayName;
;
}}),
"[project]/src/components/dashboard/post-card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/dashboard/post-card.tsx
__turbopack_context__.s({
    "PostCard": (()=>PostCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/facebook.js [app-ssr] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/instagram.js [app-ssr] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-ssr] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js [app-ssr] (ecmascript) <export default as MoreVertical>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pen.js [app-ssr] (ecmascript) <export default as Pen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/twitter.js [app-ssr] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as CalendarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-ssr] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image-off.js [app-ssr] (ecmascript) <export default as ImageOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-ssr] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-ssr] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html-to-image/es/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$06016b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:06016b [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$91530e__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:91530e [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Helper function to validate URLs
const isValidUrl = (url)=>{
    if (!url || typeof url !== 'string') {
        return false;
    }
    // Handle compression placeholders
    if (url === '[COMPRESSED_IMAGE]' || url === '[TRUNCATED]' || url.includes('[') && url.includes(']')) {
        return false;
    }
    try {
        // Check for data URLs (base64 images)
        if (url.startsWith('data:')) {
            return url.includes('base64,') || url.includes('charset=');
        }
        // Check for HTTP/HTTPS URLs
        const parsedUrl = new URL(url);
        return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch (error) {
        // Don't log compression placeholders as errors
        if (!url.includes('[') || !url.includes(']')) {}
        return false;
    }
};
/**
 * Utility function to detect image format from data URL
 */ function getImageFormatFromDataUrl(dataUrl) {
    if (dataUrl.startsWith('data:image/svg+xml')) {
        return {
            format: 'svg',
            extension: 'svg'
        };
    } else if (dataUrl.startsWith('data:image/png;base64,')) {
        return {
            format: 'png',
            extension: 'png'
        };
    } else if (dataUrl.startsWith('data:image/jpeg;base64,') || dataUrl.startsWith('data:image/jpg;base64,')) {
        return {
            format: 'jpeg',
            extension: 'jpg'
        };
    } else if (dataUrl.startsWith('data:image/webp;base64,')) {
        return {
            format: 'webp',
            extension: 'webp'
        };
    }
    return {
        format: 'png',
        extension: 'png'
    }; // default fallback
}
const platformIcons = {
    Facebook: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 87,
        columnNumber: 13
    }, this),
    Instagram: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 88,
        columnNumber: 14
    }, this),
    LinkedIn: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 89,
        columnNumber: 13
    }, this),
    Twitter: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"], {
        className: "h-4 w-4"
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/post-card.tsx",
        lineNumber: 90,
        columnNumber: 12
    }, this)
};
function PostCard({ post, brandProfile, onPostUpdated }) {
    const [isEditing, setIsEditing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isRegenerating, setIsRegenerating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isGeneratingVideo, setIsGeneratingVideo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editedContent, setEditedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(post.content);
    const [editedHashtags, setEditedHashtags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(post.hashtags);
    const [videoUrl, setVideoUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(post.videoUrl);
    const [showVideoDialog, setShowVideoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showImagePreview, setShowImagePreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [previewImageUrl, setPreviewImageUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Ensure variants array exists and has at least one item
    const safeVariants = post.variants && post.variants.length > 0 ? post.variants : [
        {
            platform: post.platform || 'instagram',
            imageUrl: post.imageUrl || ''
        }
    ];
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(safeVariants[0]?.platform || 'instagram');
    const downloadRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
    // Check if this is a Revo 2.0 post (single platform)
    const isRevo2Post = post.id?.startsWith('revo2-') || safeVariants.length === 1;
    const formattedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        try {
            const date = new Date(post.date);
            if (isNaN(date.getTime())) {
                // If date is invalid, use current date
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'MMM d, yyyy');
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'MMM d, yyyy');
        } catch (error) {
            // Fallback to current date if any error occurs
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'MMM d, yyyy');
        }
    }, [
        post.date
    ]);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    // Platform-specific dimensions - MUST match backend Revo 2.0 generation
    const getPlatformDimensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((platform)=>{
        switch(platform.toLowerCase()){
            case 'instagram':
                return {
                    width: 1080,
                    height: 1080,
                    aspectClass: 'aspect-square'
                };
            case 'facebook':
                return {
                    width: 1200,
                    height: 675,
                    aspectClass: 'aspect-[16/9]'
                };
            case 'twitter':
                return {
                    width: 1200,
                    height: 675,
                    aspectClass: 'aspect-[16/9]'
                };
            case 'linkedin':
                return {
                    width: 1200,
                    height: 675,
                    aspectClass: 'aspect-[16/9]'
                };
            case 'tiktok':
                return {
                    width: 1080,
                    height: 1920,
                    aspectClass: 'aspect-[9/16]'
                };
            default:
                return {
                    width: 1080,
                    height: 1080,
                    aspectClass: 'aspect-square'
                };
        }
    }, []);
    // Copy functionality
    const handleCopyCaption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            await navigator.clipboard.writeText(post.content);
            toast({
                title: "Caption Copied!",
                description: "The caption has been copied to your clipboard."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Copy Failed",
                description: "Could not copy the caption. Please try again."
            });
        }
    }, [
        post.content,
        toast
    ]);
    const handleCopyHashtags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const hashtagsText = typeof post.hashtags === 'string' ? post.hashtags : post.hashtags?.join(' ') || '';
            await navigator.clipboard.writeText(hashtagsText);
            toast({
                title: "Hashtags Copied!",
                description: "The hashtags have been copied to your clipboard."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Copy Failed",
                description: "Could not copy the hashtags. Please try again."
            });
        }
    }, [
        post.hashtags,
        toast
    ]);
    // Image preview functionality
    const handleImagePreview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((imageUrl)=>{
        setPreviewImageUrl(imageUrl);
        setShowImagePreview(true);
    }, []);
    const handleDownload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        const activeVariant = safeVariants.find((v)=>v.platform === activeTab);
        // First try to download the original HD image directly if URL is valid
        if (activeVariant?.imageUrl && isValidUrl(activeVariant.imageUrl)) {
            try {
                // Check if it's a data URL (base64 encoded image)
                if (activeVariant.imageUrl.startsWith('data:')) {
                    const { format, extension } = getImageFormatFromDataUrl(activeVariant.imageUrl);
                    // For social media posts, we need raster images (PNG/JPEG), not SVG
                    if (format === 'svg') {
                    // Fall through to the canvas conversion method below
                    // This will convert the SVG to a high-quality PNG
                    } else {
                        // Handle other data URL formats (PNG, JPEG, etc.) directly
                        const link = document.createElement('a');
                        link.href = activeVariant.imageUrl;
                        link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        toast({
                            title: "Social Media Image Ready",
                            description: `High-definition ${format.toUpperCase()} image downloaded successfully.`
                        });
                        return;
                    }
                } else {
                    // Handle regular HTTP/HTTPS URLs (not data URLs)
                    try {
                        const response = await fetch(activeVariant.imageUrl);
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        // Determine file extension based on content type
                        const contentType = response.headers.get('content-type') || blob.type;
                        let extension = 'png'; // default
                        if (contentType.includes('jpeg') || contentType.includes('jpg')) {
                            extension = 'jpg';
                        } else if (contentType.includes('webp')) {
                            extension = 'webp';
                        }
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                        toast({
                            title: "Social Media Image Ready",
                            description: "High-definition image downloaded successfully."
                        });
                        return;
                    } catch (error) {
                    // Fall through to canvas conversion
                    }
                }
            } catch (error) {}
        }
        // Fallback: Capture the displayed image with maximum quality settings
        const nodeToCapture = downloadRefs.current[activeTab];
        if (!nodeToCapture) {
            toast({
                variant: "destructive",
                title: "Download Failed",
                description: "Could not find the image element to download."
            });
            return;
        }
        try {
            // Check if we're converting an SVG enhanced design
            const activeVariant = safeVariants.find((v)=>v.platform === activeTab);
            const isSvgDataUrl = activeVariant?.imageUrl?.startsWith('data:image/svg+xml');
            const platformDimensions = getPlatformDimensions(activeTab);
            // Platform-specific optimized settings for social media posts
            const socialMediaSettings = {
                cacheBust: true,
                canvasWidth: platformDimensions.width,
                canvasHeight: platformDimensions.height,
                pixelRatio: 3,
                quality: 1.0,
                backgroundColor: '#ffffff',
                style: {
                    borderRadius: '0',
                    border: 'none'
                }
            };
            // Enhanced settings for SVG conversion
            if (isSvgDataUrl) {
                socialMediaSettings.canvasWidth = platformDimensions.width;
                socialMediaSettings.canvasHeight = platformDimensions.height;
                socialMediaSettings.pixelRatio = 4; // Extra high DPI for SVG conversion
            }
            const dataUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$to$2d$image$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toPng"])(nodeToCapture, socialMediaSettings);
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = `nevis-social-${post.id}-${activeTab}.png`;
            link.click();
            // Provide specific feedback based on content type
            const successMessage = isSvgDataUrl ? "Enhanced design converted to PNG for social media use." : "High-definition image ready for social media posting.";
            toast({
                title: "Social Media Image Ready",
                description: successMessage
            });
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Download Failed",
                description: `Could not download the image. Please try again. Error: ${err.message}`
            });
        }
    }, [
        post.id,
        activeTab,
        toast
    ]);
    const handleSaveChanges = async ()=>{
        const updatedPost = {
            ...post,
            content: editedContent,
            hashtags: editedHashtags,
            status: 'edited'
        };
        await onPostUpdated(updatedPost);
        setIsEditing(false);
        toast({
            title: "Post Updated",
            description: "Your changes have been saved."
        });
    };
    const handleRegenerate = async ()=>{
        setIsRegenerating(true);
        try {
            const platform = safeVariants[0].platform;
            const newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$06016b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"])(brandProfile, platform);
            onPostUpdated({
                ...newPost,
                id: post.id
            }); // Keep old id for replacement
            toast({
                title: "Post Regenerated!",
                description: "A new version of your post has been generated."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Regeneration Failed",
                description: error.message
            });
        } finally{
            setIsRegenerating(false);
        }
    };
    const handleGenerateVideo = async ()=>{
        if (!post.catchyWords) {
            toast({
                variant: "destructive",
                title: "Cannot Generate Video",
                description: "The post is missing the required catchy words."
            });
            return;
        }
        setIsGeneratingVideo(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$91530e__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateVideoContentAction"])(brandProfile, post.catchyWords, post.content);
            const newVideoUrl = result.videoUrl;
            setVideoUrl(newVideoUrl);
            await onPostUpdated({
                ...post,
                videoUrl: newVideoUrl
            });
            setShowVideoDialog(true);
            toast({
                title: "Video Generated!",
                description: "Your video is ready to be viewed."
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Video Generation Failed",
                description: error.message
            });
        } finally{
            setIsGeneratingVideo(false);
        }
    };
    const activeVariant = safeVariants.find((v)=>v.platform === activeTab) || safeVariants[0];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                className: "flex flex-col w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                        className: "flex-row items-center justify-between gap-4 p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 text-sm font-medium text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__["CalendarIcon"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 399,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: formattedDate
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 400,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 398,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            size: "icon",
                                            variant: "ghost",
                                            className: "h-6 w-6",
                                            disabled: isRegenerating || isGeneratingVideo,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__["MoreVertical"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 405,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 404,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 403,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        align: "end",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>setIsEditing(true),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Pen$3e$__["Pen"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 410,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Edit Text"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 409,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleRegenerate,
                                                disabled: isRegenerating,
                                                children: [
                                                    isRegenerating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 415,
                                                        columnNumber: 19
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 417,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Regenerate Image"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 413,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleGenerateVideo,
                                                disabled: isGeneratingVideo,
                                                children: [
                                                    isGeneratingVideo ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 423,
                                                        columnNumber: 19
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 425,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Generate Video"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 421,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: handleDownload,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 430,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Download Image"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 429,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 408,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 402,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 397,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "flex-grow space-y-4 p-4 pt-0",
                        children: [
                            isRevo2Post ? // Revo 2.0 single-platform layout with platform icon at top left
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-start p-3 bg-muted/30 rounded-lg",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: platformIcons[safeVariants[0]?.platform || 'instagram']
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 442,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 441,
                                        columnNumber: 15
                                    }, this),
                                    (()=>{
                                        const variant = safeVariants[0];
                                        const dimensions = getPlatformDimensions(variant?.platform || 'instagram');
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `relative ${dimensions.aspectClass} w-full overflow-hidden`,
                                            children: [
                                                (isRegenerating || isGeneratingVideo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 z-10 flex items-center justify-center bg-card/80",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                            className: "h-8 w-8 animate-spin text-primary"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 456,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: isRegenerating ? 'Regenerating image...' : 'Generating video...'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 457,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 455,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: (el)=>downloadRefs.current[variant?.platform || 'instagram'] = el,
                                                    className: `relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`,
                                                    children: variant?.imageUrl && isValidUrl(variant.imageUrl) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative h-full w-full cursor-pointer",
                                                        onClick: ()=>handleImagePreview(variant.imageUrl),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                alt: `Generated post image for ${variant.platform}`,
                                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('h-full w-full object-cover transition-opacity', isRegenerating || isGeneratingVideo ? 'opacity-50' : 'opacity-100'),
                                                                height: dimensions.height,
                                                                src: variant.imageUrl,
                                                                "data-ai-hint": "social media post",
                                                                width: dimensions.width,
                                                                crossOrigin: "anonymous",
                                                                unoptimized: variant.imageUrl.startsWith('data:')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 466,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "bg-white/90 rounded-full p-2",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                        className: "h-5 w-5 text-gray-700"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 479,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 478,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 477,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 462,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex h-full w-full items-center justify-center bg-muted flex-col gap-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__["ImageOff"], {
                                                                className: "h-12 w-12 text-muted-foreground"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 485,
                                                                columnNumber: 27
                                                            }, this),
                                                            variant?.imageUrl && !isValidUrl(variant.imageUrl) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute bottom-2 left-2 right-2",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-red-500 bg-white/90 p-2 rounded",
                                                                    children: variant.imageUrl.includes('[') && variant.imageUrl.includes(']') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "font-medium",
                                                                                children: "Image temporarily unavailable"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                                lineNumber: 491,
                                                                                columnNumber: 37
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-gray-600 mt-1",
                                                                                children: variant.imageUrl.includes('Large image data removed') ? 'Image was too large for storage. Try regenerating.' : 'Image data was optimized for storage.'
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                                lineNumber: 492,
                                                                                columnNumber: 37
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 490,
                                                                        columnNumber: 35
                                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        children: "Invalid image URL"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 500,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 488,
                                                                    columnNumber: 31
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 487,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 484,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 460,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 453,
                                            columnNumber: 19
                                        }, this);
                                    })()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 439,
                                columnNumber: 13
                            }, this) : // Multi-platform tab layout for Revo 1.0/1.5
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tabs"], {
                                value: activeTab,
                                onValueChange: (v)=>setActiveTab(v),
                                className: "w-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsList"], {
                                        className: "grid w-full grid-cols-4",
                                        children: safeVariants.map((variant)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                                value: variant.platform,
                                                children: platformIcons[variant.platform]
                                            }, variant.platform, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 517,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 515,
                                        columnNumber: 15
                                    }, this),
                                    safeVariants.map((variant)=>{
                                        const dimensions = getPlatformDimensions(variant.platform);
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                                            value: variant.platform,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `relative ${dimensions.aspectClass} w-full overflow-hidden`,
                                                children: [
                                                    (isRegenerating || isGeneratingVideo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "absolute inset-0 z-10 flex items-center justify-center bg-card/80",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                                className: "h-8 w-8 animate-spin text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 529,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "sr-only",
                                                                children: isRegenerating ? 'Regenerating image...' : 'Generating video...'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                lineNumber: 530,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 528,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        ref: (el)=>downloadRefs.current[variant.platform] = el,
                                                        className: `relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`,
                                                        children: variant.imageUrl && isValidUrl(variant.imageUrl) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative h-full w-full cursor-pointer",
                                                            onClick: ()=>handleImagePreview(variant.imageUrl),
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    alt: `Generated post image for ${variant.platform}`,
                                                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('h-full w-full object-cover transition-opacity', isRegenerating || isGeneratingVideo ? 'opacity-50' : 'opacity-100'),
                                                                    height: dimensions.height,
                                                                    src: variant.imageUrl,
                                                                    "data-ai-hint": "social media post",
                                                                    width: dimensions.width,
                                                                    crossOrigin: "anonymous",
                                                                    unoptimized: variant.imageUrl.startsWith('data:')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 539,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-white/90 rounded-full p-2",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                            className: "h-5 w-5 text-gray-700"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                            lineNumber: 552,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 551,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 550,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 535,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex h-full w-full items-center justify-center bg-muted",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ImageOff$3e$__["ImageOff"], {
                                                                    className: "h-12 w-12 text-muted-foreground"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 558,
                                                                    columnNumber: 29
                                                                }, this),
                                                                variant.imageUrl && !isValidUrl(variant.imageUrl) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute bottom-2 left-2 right-2",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-xs text-red-500 bg-white/90 p-1 rounded",
                                                                        children: "Invalid image URL"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                        lineNumber: 561,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                                    lineNumber: 560,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                            lineNumber: 557,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                        lineNumber: 533,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 526,
                                                columnNumber: 21
                                            }, this)
                                        }, variant.platform, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 525,
                                            columnNumber: 19
                                        }, this);
                                    })
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 514,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-start justify-between gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-foreground line-clamp-4 flex-1",
                                            children: post.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 578,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            onClick: handleCopyCaption,
                                            className: "h-8 w-8 p-0 flex-shrink-0",
                                            title: "Copy caption",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                                className: "h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                lineNumber: 586,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 579,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 577,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 576,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 436,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardFooter"], {
                        className: "p-4 pt-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start justify-between gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-1 flex-1",
                                    children: [
                                        post.hashtags && (()=>{
                                            // Handle both string and array formats for hashtags
                                            const hashtagsArray = typeof post.hashtags === 'string' ? post.hashtags.split(" ") : Array.isArray(post.hashtags) ? post.hashtags : [];
                                            return hashtagsArray.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: "secondary",
                                                    className: "font-normal",
                                                    children: tag
                                                }, index, false, {
                                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                                    lineNumber: 604,
                                                    columnNumber: 19
                                                }, this));
                                        })(),
                                        !post.hashtags && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "secondary",
                                            className: "font-normal",
                                            children: "#enhanced #ai #design"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 610,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 594,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "sm",
                                    onClick: handleCopyHashtags,
                                    className: "h-8 w-8 p-0 flex-shrink-0",
                                    title: "Copy hashtags",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                                        lineNumber: 622,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 615,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 593,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/post-card.tsx",
                        lineNumber: 592,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 396,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Dialog"], {
                open: isEditing,
                onOpenChange: setIsEditing,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-[600px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Edit Post"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 632,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Make changes to your post content and hashtags below. Click save when you're done."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 633,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 631,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid gap-4 py-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                            htmlFor: "content",
                                            children: "Content"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 639,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Textarea"], {
                                            id: "content",
                                            value: editedContent,
                                            onChange: (e)=>setEditedContent(e.target.value),
                                            className: "h-32"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 640,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 638,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                            htmlFor: "hashtags",
                                            children: "Hashtags"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 648,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                            id: "hashtags",
                                            value: editedHashtags,
                                            onChange: (e)=>setEditedHashtags(e.target.value)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                                            lineNumber: 649,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 647,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 637,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    onClick: ()=>setIsEditing(false),
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 657,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    onClick: handleSaveChanges,
                                    children: "Save Changes"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 658,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 656,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 630,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 629,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Dialog"], {
                open: showVideoDialog,
                onOpenChange: setShowVideoDialog,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-[600px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Generated Video"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 667,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Here is the video generated for your post. You can download it from here."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 668,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 666,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "my-4",
                            children: videoUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                controls: true,
                                autoPlay: true,
                                src: videoUrl,
                                className: "w-full rounded-md"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 674,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "No video available."
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 676,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 672,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: ()=>setShowVideoDialog(false),
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 680,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 679,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 665,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 664,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Dialog"], {
                open: showImagePreview,
                onOpenChange: setShowImagePreview,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-4xl max-h-[90vh] p-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            className: "pb-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    children: "Image Preview"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 689,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    children: "Click and drag to pan, scroll to zoom"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                                    lineNumber: 690,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 688,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center max-h-[70vh] overflow-hidden",
                            children: previewImageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                src: previewImageUrl,
                                alt: "Post image preview",
                                className: "max-w-full max-h-full object-contain rounded-lg"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 696,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 694,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: ()=>setShowImagePreview(false),
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/post-card.tsx",
                                lineNumber: 704,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/post-card.tsx",
                            lineNumber: 703,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/post-card.tsx",
                    lineNumber: 687,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/post-card.tsx",
                lineNumber: 686,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),
"[project]/src/app/data:6663fb [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f184a7753a1c29638132401afe2bdafb4cd96f602":"generateContentWithArtifactsAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateContentWithArtifactsAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f184a7753a1c29638132401afe2bdafb4cd96f602", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateContentWithArtifactsAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/hooks/use-brand-profiles.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Hook for managing brand profiles with Firestore
__turbopack_context__.s({
    "useBrandProfiles": (()=>useBrandProfiles),
    "useCurrentBrandProfile": (()=>useCurrentBrandProfile),
    "useHasCompleteBrandProfile": (()=>useHasCompleteBrandProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/services/brand-profile-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-ssr] (ecmascript)");
;
;
;
function useBrandProfiles() {
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserId"])();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        profiles: [],
        currentProfile: null,
        loading: true,
        error: null,
        saving: false
    });
    // Load brand profiles
    const loadProfiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!userId) {
            setState((prev)=>({
                    ...prev,
                    loading: false,
                    profiles: [],
                    currentProfile: null
                }));
            return;
        }
        try {
            setState((prev)=>({
                    ...prev,
                    loading: true,
                    error: null
                }));
            // Try to load from Firestore, fallback to localStorage
            let profiles = [];
            try {
                profiles = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].getUserBrandProfiles(userId);
            } catch (firebaseError) {
                // Fallback to localStorage
                const stored = localStorage.getItem('completeBrandProfile');
                if (stored) {
                    const profile = JSON.parse(stored);
                    profiles = [
                        profile
                    ];
                }
            }
            const currentProfile = profiles.length > 0 ? profiles[0] : null;
            setState((prev)=>({
                    ...prev,
                    profiles,
                    currentProfile,
                    loading: false
                }));
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    loading: false,
                    error: error instanceof Error ? error.message : 'Failed to load profiles'
                }));
        }
    }, [
        userId
    ]);
    // Save brand profile
    const saveProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (profile)=>{
        if (!userId) {
            throw new Error('User must be authenticated to save profile');
        }
        try {
            setState((prev)=>({
                    ...prev,
                    saving: true,
                    error: null
                }));
            const profileId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].saveBrandProfile(profile, userId);
            // Reload profiles to get the updated list
            await loadProfiles();
            setState((prev)=>({
                    ...prev,
                    saving: false
                }));
            return profileId;
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    saving: false,
                    error: error instanceof Error ? error.message : 'Failed to save profile'
                }));
            throw error;
        }
    }, [
        userId,
        loadProfiles
    ]);
    // Update brand profile
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (profileId, updates)=>{
        if (!userId) {
            throw new Error('User must be authenticated to update profile');
        }
        try {
            setState((prev)=>({
                    ...prev,
                    saving: true,
                    error: null
                }));
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].updateBrandProfile(profileId, updates);
            // Update local state optimistically
            setState((prev)=>({
                    ...prev,
                    profiles: prev.profiles.map((p)=>p.id === profileId ? {
                            ...p,
                            ...updates
                        } : p),
                    currentProfile: prev.currentProfile?.id === profileId ? {
                        ...prev.currentProfile,
                        ...updates
                    } : prev.currentProfile,
                    saving: false
                }));
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    saving: false,
                    error: error instanceof Error ? error.message : 'Failed to update profile'
                }));
            throw error;
        }
    }, [
        userId
    ]);
    // Delete brand profile
    const deleteProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (profileId)=>{
        if (!userId) {
            throw new Error('User must be authenticated to delete profile');
        }
        try {
            setState((prev)=>({
                    ...prev,
                    error: null
                }));
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].delete(profileId);
            // Update local state
            setState((prev)=>({
                    ...prev,
                    profiles: prev.profiles.filter((p)=>p.id !== profileId),
                    currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile
                }));
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    error: error instanceof Error ? error.message : 'Failed to delete profile'
                }));
            throw error;
        }
    }, [
        userId
    ]);
    // Set current profile
    const setCurrentProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((profile)=>{
        setState((prev)=>{
            return {
                ...prev,
                currentProfile: profile
            };
        });
    }, []);
    // Get profile by ID
    const getProfileById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (profileId)=>{
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].getBrandProfileById(profileId);
        } catch (error) {
            return null;
        }
    }, []);
    // Load profiles when userId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadProfiles();
    }, [
        loadProfiles
    ]);
    // Set up real-time listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!userId) return;
        const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$brand$2d$profile$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandProfileFirebaseService"].onUserDocumentsChange(userId, (profiles)=>{
            setState((prev)=>{
                // Preserve the current profile if it still exists in the updated profiles
                let preservedCurrentProfile = prev.currentProfile;
                if (prev.currentProfile) {
                    // Check if current profile still exists in the updated list
                    const stillExists = profiles.find((p)=>p.id === prev.currentProfile?.id);
                    if (!stillExists) {
                        preservedCurrentProfile = null;
                    } else {
                        // Update with the latest version of the current profile
                        const updatedProfile = profiles.find((p)=>p.id === prev.currentProfile?.id);
                        if (updatedProfile) {
                            preservedCurrentProfile = updatedProfile;
                        }
                    }
                }
                // Only auto-select first profile if there's no current profile at all AND this is the initial load
                const finalCurrentProfile = preservedCurrentProfile || (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);
                if (finalCurrentProfile && !prev.currentProfile) {}
                return {
                    ...prev,
                    profiles,
                    currentProfile: finalCurrentProfile
                };
            });
        }, {
            orderBy: 'updatedAt',
            orderDirection: 'desc'
        });
        return unsubscribe;
    }, [
        userId
    ]);
    return {
        ...state,
        saveProfile,
        updateProfile,
        deleteProfile,
        setCurrentProfile,
        getProfileById,
        reload: loadProfiles
    };
}
function useCurrentBrandProfile() {
    const { currentProfile, loading, error } = useBrandProfiles();
    return {
        profile: currentProfile,
        loading,
        error
    };
}
function useHasCompleteBrandProfile() {
    const { currentProfile, loading } = useBrandProfiles();
    if (loading || !currentProfile) return false;
    // Check if profile has required fields
    const requiredFields = [
        'businessName',
        'businessType',
        'location',
        'description',
        'services'
    ];
    return requiredFields.every((field)=>{
        const value = currentProfile[field];
        return value && (typeof value === 'string' ? value.trim().length > 0 : Array.isArray(value) ? value.length > 0 : true);
    });
}
}}),
"[project]/src/hooks/use-generated-posts.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Hook for managing generated posts with Firestore
__turbopack_context__.s({
    "useGeneratedPosts": (()=>useGeneratedPosts),
    "useGeneratedPostsForBrand": (()=>useGeneratedPostsForBrand),
    "usePostStatistics": (()=>usePostStatistics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/services/generated-post-service.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$brand$2d$profiles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-brand-profiles.ts [app-ssr] (ecmascript)");
;
;
;
;
function useGeneratedPosts(limit = 10) {
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserId"])();
    const { profile: currentProfile } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$brand$2d$profiles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCurrentBrandProfile"])();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        posts: [],
        loading: true,
        error: null,
        saving: false
    });
    // Load generated posts
    const loadPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!userId) {
            setState((prev)=>({
                    ...prev,
                    loading: false,
                    posts: []
                }));
            return;
        }
        try {
            setState((prev)=>({
                    ...prev,
                    loading: true,
                    error: null
                }));
            const posts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getUserGeneratedPosts(userId, {
                limit
            });
            setState((prev)=>({
                    ...prev,
                    posts,
                    loading: false
                }));
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    loading: false,
                    error: error instanceof Error ? error.message : 'Failed to load posts'
                }));
        }
    }, [
        userId,
        limit
    ]);
    // Save generated post
    const savePost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (post)=>{
        if (!userId || !currentProfile) {
            throw new Error('User must be authenticated and have a brand profile to save posts');
        }
        try {
            setState((prev)=>({
                    ...prev,
                    saving: true,
                    error: null
                }));
            const postId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].saveGeneratedPost(post, userId, currentProfile.id);
            // Add to local state optimistically
            setState((prev)=>({
                    ...prev,
                    posts: [
                        {
                            ...post,
                            id: postId
                        },
                        ...prev.posts
                    ].slice(0, limit),
                    saving: false
                }));
            return postId;
        } catch (error) {
            setState((prev)=>({
                    ...prev,
                    saving: false,
                    error: error instanceof Error ? error.message : 'Failed to save post'
                }));
            throw error;
        }
    }, [
        userId,
        currentProfile,
        limit
    ]);
    // Update post analytics
    const updatePostAnalytics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (postId, analytics)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].updatePostAnalytics(postId, analytics);
            // Update local state
            setState((prev)=>({
                    ...prev,
                    posts: prev.posts.map((post)=>post.id === postId ? {
                            ...post,
                            ...analytics
                        } : post)
                }));
        } catch (error) {
            throw error;
        }
    }, []);
    // Update post status
    const updatePostStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (postId, status)=>{
        try {
            const firestoreStatus = status === 'posted' ? 'published' : 'draft';
            const publishedAt = status === 'posted' ? new Date() : undefined;
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].updatePostStatus(postId, firestoreStatus, undefined, publishedAt);
            // Update local state
            setState((prev)=>({
                    ...prev,
                    posts: prev.posts.map((post)=>post.id === postId ? {
                            ...post,
                            status
                        } : post)
                }));
        } catch (error) {
            throw error;
        }
    }, []);
    // Delete post
    const deletePost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (postId)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].delete(postId);
            // Update local state
            setState((prev)=>({
                    ...prev,
                    posts: prev.posts.filter((post)=>post.id !== postId)
                }));
        } catch (error) {
            throw error;
        }
    }, []);
    // Get posts by platform
    const getPostsByPlatform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (platform)=>{
        if (!userId) return [];
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getUserGeneratedPosts(userId, {
                platform,
                limit
            });
        } catch (error) {
            return [];
        }
    }, [
        userId,
        limit
    ]);
    // Get posts by status
    const getPostsByStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (status)=>{
        if (!userId) return [];
        try {
            const firestoreStatus = status === 'posted' ? 'published' : 'draft';
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getPostsByStatus(userId, firestoreStatus);
        } catch (error) {
            return [];
        }
    }, [
        userId
    ]);
    // Load posts when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadPosts();
    }, [
        loadPosts
    ]);
    // Set up real-time listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!userId) return;
        const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].onUserDocumentsChange(userId, (posts)=>{
            setState((prev)=>({
                    ...prev,
                    posts: posts.slice(0, limit)
                }));
        }, {
            limit,
            orderBy: 'createdAt',
            orderDirection: 'desc'
        });
        return unsubscribe;
    }, [
        userId,
        limit
    ]);
    return {
        ...state,
        savePost,
        updatePostAnalytics,
        updatePostStatus,
        deletePost,
        getPostsByPlatform,
        getPostsByStatus,
        reload: loadPosts
    };
}
function useGeneratedPostsForBrand(brandProfileId, limit = 10) {
    const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserId"])();
    const [posts, setPosts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const loadPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!userId || !brandProfileId) {
            setPosts([]);
            setLoading(false);
            return;
        }
        try {
            setLoading(true);
            setError(null);
            const brandPosts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$services$2f$generated$2d$post$2d$service$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatedPostFirebaseService"].getRecentPostsForBrand(userId, brandProfileId, limit);
            setPosts(brandPosts);
            setLoading(false);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load posts');
            setLoading(false);
        }
    }, [
        userId,
        brandProfileId,
        limit
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadPosts();
    }, [
        loadPosts
    ]);
    return {
        posts,
        loading,
        error,
        reload: loadPosts
    };
}
function usePostStatistics() {
    const { posts } = useGeneratedPosts(100); // Get more posts for statistics
    const statistics = {
        total: posts.length,
        byPlatform: posts.reduce((acc, post)=>{
            acc[post.platform] = (acc[post.platform] || 0) + 1;
            return acc;
        }, {}),
        byStatus: posts.reduce((acc, post)=>{
            acc[post.status] = (acc[post.status] || 0) + 1;
            return acc;
        }, {}),
        averageQuality: posts.length > 0 ? posts.reduce((sum, post)=>sum + (post.qualityScore || 0), 0) / posts.length : 0,
        averageEngagement: posts.length > 0 ? posts.reduce((sum, post)=>sum + (post.engagementPrediction || 0), 0) / posts.length : 0
    };
    return statistics;
}
}}),
"[project]/src/components/ui/switch.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Switch": (()=>Switch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-switch/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Switch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input", className),
        ...props,
        ref: ref,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Thumb"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")
        }, void 0, false, {
            fileName: "[project]/src/components/ui/switch.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/switch.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
Switch.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/dashboard/content-calendar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/dashboard/content-calendar.tsx
__turbopack_context__.s({
    "ContentCalendar": (()=>ContentCalendar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/facebook.js [app-ssr] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/instagram.js [app-ssr] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-ssr] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/twitter.js [app-ssr] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-ssr] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-ssr] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$post$2d$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/post-card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$06016b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:06016b [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$6663fb__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:6663fb [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-generated-posts.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/switch.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const platforms = [
    {
        name: 'Instagram',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"]
    },
    {
        name: 'Facebook',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"]
    },
    {
        name: 'Twitter',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"]
    },
    {
        name: 'LinkedIn',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"]
    }
];
function ContentCalendar({ brandProfile, posts, onPostGenerated, onPostUpdated }) {
    const [isGenerating, setIsGenerating] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFirebaseAuth"])();
    const { savePost, saving } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useGeneratedPosts"])();
    // Brand consistency preferences - default to consistent if design examples exist
    const [brandConsistency, setBrandConsistency] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState({
        strictConsistency: !!(brandProfile.designExamples && brandProfile.designExamples.length > 0),
        followBrandColors: true
    });
    // Revo model selection
    const [selectedRevoModel, setSelectedRevoModel] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState('revo-1.5');
    // Artifact selection for content generation
    const [selectedArtifacts, setSelectedArtifacts] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState([]);
    // Include people in designs toggle
    const [includePeopleInDesigns, setIncludePeopleInDesigns] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(true);
    // Use local language toggle
    const [useLocalLanguage, setUseLocalLanguage] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    // Save preferences to localStorage
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        const savedPreferences = localStorage.getItem('brandConsistencyPreferences');
        if (savedPreferences) {
            setBrandConsistency(JSON.parse(savedPreferences));
        }
        const savedRevoModel = localStorage.getItem('selectedRevoModel');
        if (savedRevoModel) {
            setSelectedRevoModel(savedRevoModel);
        }
        const savedIncludePeople = localStorage.getItem('includePeopleInDesigns');
        if (savedIncludePeople !== null) {
            setIncludePeopleInDesigns(JSON.parse(savedIncludePeople));
        }
        const savedUseLocalLanguage = localStorage.getItem('useLocalLanguage');
        if (savedUseLocalLanguage !== null) {
            setUseLocalLanguage(JSON.parse(savedUseLocalLanguage));
        }
    }, []);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        localStorage.setItem('brandConsistencyPreferences', JSON.stringify(brandConsistency));
    }, [
        brandConsistency
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        localStorage.setItem('selectedRevoModel', selectedRevoModel);
    }, [
        selectedRevoModel
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        localStorage.setItem('includePeopleInDesigns', JSON.stringify(includePeopleInDesigns));
    }, [
        includePeopleInDesigns
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        localStorage.setItem('useLocalLanguage', JSON.stringify(useLocalLanguage));
    }, [
        useLocalLanguage
    ]);
    const handleGenerateClick = async (platform)=>{
        setIsGenerating(platform);
        try {
            let newPost;
            // Check if artifacts are enabled (simple toggle approach)
            const artifactsEnabled = selectedArtifacts.length > 0;
            const useEnhancedGeneration = artifactsEnabled || selectedRevoModel === 'revo-1.5' || selectedRevoModel === 'revo-2.0';
            if (selectedRevoModel === 'revo-2.0') {
                // Use server action to avoid client-side imports
                const response = await fetch('/api/generate-revo-2.0', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        businessType: brandProfile.businessType || 'Business',
                        platform: platform.toLowerCase(),
                        visualStyle: brandProfile.visualStyle || 'modern',
                        imageText: `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,
                        brandProfile,
                        aspectRatio: '1:1',
                        includePeopleInDesigns,
                        useLocalLanguage
                    })
                });
                if (!response.ok) {
                    throw new Error(`Revo 2.0 generation failed: ${response.statusText}`);
                }
                const revo20Result = await response.json();
                newPost = {
                    id: `revo-2.0-${Date.now()}`,
                    content: revo20Result.caption || `🚀 Generated with Revo 2.0 (Gemini 2.5 Flash Image)\n\n${brandProfile.businessName || brandProfile.businessType} - Premium Content`,
                    hashtags: revo20Result.hashtags || [
                        '#NextGen',
                        '#AI',
                        '#Innovation'
                    ],
                    imageUrl: revo20Result.imageUrl,
                    platform: platform,
                    date: new Date().toISOString(),
                    analytics: {
                        views: 0,
                        likes: 0,
                        shares: 0,
                        comments: 0,
                        engagementPrediction: 85,
                        brandAlignmentScore: 95,
                        qualityScore: revo20Result.qualityScore || 10
                    },
                    metadata: {
                        aiModel: revo20Result.model || 'Revo 2.0',
                        generationPrompt: 'Revo 2.0 Native Generation',
                        processingTime: revo20Result.processingTime || 0,
                        enhancementsApplied: revo20Result.enhancementsApplied || []
                    }
                };
            } else if (useEnhancedGeneration) {
                // Use artifact-enhanced generation - will automatically use active artifacts from artifacts page
                newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$6663fb__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentWithArtifactsAction"])(brandProfile, platform, brandConsistency, [], selectedRevoModel === 'revo-1.5', includePeopleInDesigns, useLocalLanguage);
            } else {
                // Use standard content generation
                newPost = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$06016b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateContentAction"])(brandProfile, platform, brandConsistency);
            }
            // Save to Firestore database first
            try {
                const postId = await savePost(newPost);
                // Update the post with the Firestore ID
                const savedPost = {
                    ...newPost,
                    id: postId
                };
                onPostGenerated(savedPost);
            } catch (saveError) {
                // Fallback to localStorage if Firestore fails
                onPostGenerated(newPost);
            }
            // Dynamic toast message based on generation type
            let title = "Content Generated!";
            let description = `A new ${platform} post has been saved to your database.`;
            if (selectedArtifacts.length > 0) {
                title = "Content Generated with References! 📎";
                description = `A new ${platform} post using ${selectedArtifacts.length} reference${selectedArtifacts.length !== 1 ? 's' : ''} has been saved.`;
            } else if (selectedRevoModel === 'revo-1.5') {
                title = "Enhanced Content Generated! ✨";
                description = `A new enhanced ${platform} post with ${selectedRevoModel} has been saved.`;
            } else {
                title = "Content Generated! 🚀";
                description = `A new ${platform} post with ${selectedRevoModel} has been saved.`;
            }
            toast({
                title,
                description
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Generation Failed",
                description: error.message
            });
        } finally{
            setIsGenerating(null);
        }
    };
    // Ensure this component is always full-bleed inside the app shell and does not cause horizontal overflow.
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full max-w-[100vw] box-border overflow-x-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full px-6 py-10 lg:py-16 lg:px-12",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full box-border space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 border border-gray-200 rounded-lg p-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                className: "h-4 w-4 text-blue-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 225,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium text-sm",
                                                children: "Brand Consistency"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 226,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"], {
                                                        className: "h-3 w-3 text-gray-500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 230,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "Strict"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 231,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: brandConsistency.strictConsistency,
                                                        onCheckedChange: (checked)=>setBrandConsistency((prev)=>({
                                                                    ...prev,
                                                                    strictConsistency: checked
                                                                }))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 232,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 229,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"], {
                                                        className: "h-3 w-3 text-gray-500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "Colors"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 241,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: brandConsistency.followBrandColors,
                                                        onCheckedChange: (checked)=>setBrandConsistency((prev)=>({
                                                                    ...prev,
                                                                    followBrandColors: checked
                                                                }))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 242,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 239,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "👥 People"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 250,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: includePeopleInDesigns,
                                                        onCheckedChange: setIncludePeopleInDesigns
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 251,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 249,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "🌍 Local"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 257,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: useLocalLanguage,
                                                        onCheckedChange: setUseLocalLanguage
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 258,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 256,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {
                                                orientation: "vertical",
                                                className: "h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 263,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-600",
                                                        children: "AI Model:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 265,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                        value: selectedRevoModel,
                                                        onChange: (e)=>setSelectedRevoModel(e.target.value),
                                                        className: "appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-1.0",
                                                                children: "Revo 1.0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 271,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-1.5",
                                                                children: "Revo 1.5"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 272,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                value: "revo-2.0",
                                                                children: "Revo 2.0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                                lineNumber: 273,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 266,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 264,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 228,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 223,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500 mt-2",
                                children: selectedRevoModel === 'revo-2.0' ? `🚀 ${selectedRevoModel}: Next-Gen AI with native image generation, character consistency & intelligent editing` : selectedRevoModel === 'revo-1.5' ? `✨ ${selectedRevoModel}: Enhanced AI with professional design principles + ${brandConsistency.strictConsistency ? "strict consistency" : "brand colors"}` : selectedRevoModel === 'revo-1.0' ? `🚀 ${selectedRevoModel}: Standard reliable AI + ${brandConsistency.strictConsistency ? "strict consistency" : "brand colors"}` : `🌟 ${selectedRevoModel}: Next-generation AI (coming soon)`
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 278,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 222,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                className: "p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                                        className: "text-sm font-medium",
                                                        children: "Use Artifacts"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 296,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs text-muted-foreground",
                                                        children: "Enable to use your uploaded reference materials and exact-use content"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 297,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 295,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                                        checked: selectedArtifacts.length > 0,
                                                        onCheckedChange: (checked)=>{
                                                            if (checked) {
                                                                // Enable artifacts - this will use active artifacts from the artifacts page
                                                                setSelectedArtifacts([
                                                                    'active'
                                                                ]);
                                                            } else {
                                                                // Disable artifacts
                                                                setSelectedArtifacts([]);
                                                            }
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 302,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>window.open('/artifacts', '_blank'),
                                                        className: "text-xs",
                                                        children: "Manage"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 314,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 301,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 294,
                                        columnNumber: 17
                                    }, this),
                                    selectedArtifacts.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-blue-700",
                                            children: "✓ Artifacts enabled - Content will use your reference materials and exact-use items from the Artifacts page"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                            lineNumber: 326,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 325,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 293,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                            lineNumber: 292,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 291,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold tracking-tight font-headline",
                                        children: "Content Calendar"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 339,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-muted-foreground",
                                        children: "Here's your generated content. Click a post to edit or regenerate."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 340,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 338,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            disabled: !!isGenerating,
                                            children: isGenerating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "mr-2 h-4 w-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 349,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Generating for ",
                                                    isGenerating,
                                                    "..."
                                                ]
                                            }, void 0, true) : "✨ Generate New Post"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                            lineNumber: 346,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 345,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        children: platforms.map((p)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>handleGenerateClick(p.name),
                                                disabled: !!isGenerating,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(p.icon, {
                                                        className: "mr-2 h-4 w-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 360,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: p.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                        lineNumber: 361,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, p.name, true, {
                                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                                lineNumber: 359,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                        lineNumber: 357,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 344,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 337,
                        columnNumber: 11
                    }, this),
                    posts.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full max-w-none",
                        children: posts.map((post)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$post$2d$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PostCard"], {
                                post: post,
                                brandProfile: brandProfile,
                                onPostUpdated: onPostUpdated
                            }, post.id, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 371,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 369,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/30 bg-card p-12 text-center w-full",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold",
                                children: "Your calendar is empty"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 381,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground mt-2",
                                children: 'Click the "Generate" button to create your first social media post!'
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                                lineNumber: 382,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                        lineNumber: 380,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/content-calendar.tsx",
                lineNumber: 220,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/content-calendar.tsx",
            lineNumber: 219,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/content-calendar.tsx",
        lineNumber: 218,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/layout/unified-brand-layout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BrandContent": (()=>BrandContent),
    "BrandSwitchingStatus": (()=>BrandSwitchingStatus),
    "ConditionalBrandContent": (()=>ConditionalBrandContent),
    "UnifiedBrandLayout": (()=>UnifiedBrandLayout),
    "useBrandAware": (()=>useBrandAware),
    "useBrandScopedData": (()=>useBrandScopedData),
    "withBrandAware": (()=>withBrandAware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
// Inner component that uses the unified brand context
function UnifiedBrandLayoutContent({ children }) {
    const { currentBrand, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Listen for brand changes and log them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((brand)=>{
        // Mark as initialized once we have a brand or finished loading
        if (!isInitialized && (!loading || brand)) {
            setIsInitialized(true);
        }
    });
    // Show loading state while initializing
    if (!isInitialized && loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 30,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Loading brand profiles..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 29,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 28,
            columnNumber: 7
        }, this);
    }
    // Show error state if there's an error
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-600 text-2xl",
                            children: "⚠️"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                            lineNumber: 43,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 42,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-red-900 mb-2",
                        children: "Error Loading Brands"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 45,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 41,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "unified-brand-layout",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: "🔥 Unified Brand System"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 63,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "Brand: ",
                            currentBrand?.businessName || currentBrand?.name || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "ID: ",
                            currentBrand?.id || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 62,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
function UnifiedBrandLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnifiedBrandProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(UnifiedBrandLayoutContent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 78,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
function useBrandAware() {
    const { currentBrand, selectBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    return {
        currentBrand,
        selectBrand,
        loading,
        isReady: !loading && currentBrand !== null,
        brandId: currentBrand?.id || null,
        brandName: currentBrand?.businessName || currentBrand?.name || null
    };
}
function withBrandAware(Component) {
    return function BrandAwareComponent(props) {
        const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            brand: currentBrand
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 106,
            columnNumber: 12
        }, this);
    };
}
function BrandContent({ children, fallback }) {
    const { currentBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 122,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 121,
            columnNumber: 7
        }, this);
    }
    if (!currentBrand) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-gray-400 text-2xl",
                        children: "🏢"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 131,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 130,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "No Brand Selected"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 133,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Please select a brand to continue."
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 129,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children(currentBrand)
    }, void 0, false);
}
function ConditionalBrandContent({ brandId, brandName, children, fallback }) {
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const shouldRender = (!brandId || currentBrand?.id === brandId) && (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);
    if (shouldRender) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    return fallback || null;
}
function useBrandScopedData(feature, defaultValue, loader) {
    const { currentBrand, getBrandStorage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!currentBrand?.id) {
            setData(defaultValue);
            return;
        }
        const storage = getBrandStorage(feature);
        if (!storage) {
            setData(defaultValue);
            return;
        }
        setLoading(true);
        try {
            if (loader) {
                // Use custom loader
                const result = loader(currentBrand.id);
                if (result instanceof Promise) {
                    result.then((loadedData)=>{
                        setData(loadedData);
                        setLoading(false);
                    }).catch((error)=>{
                        setData(defaultValue);
                        setLoading(false);
                    });
                } else {
                    setData(result);
                    setLoading(false);
                }
            } else {
                // Use storage
                const storedData = storage.getItem();
                setData(storedData || defaultValue);
                setLoading(false);
            }
        } catch (error) {
            setData(defaultValue);
            setLoading(false);
        }
    }, [
        currentBrand?.id,
        feature,
        defaultValue,
        loader,
        getBrandStorage
    ]);
    // Save data function
    const saveData = (newData)=>{
        setData(newData);
        if (currentBrand?.id) {
            const storage = getBrandStorage(feature);
            if (storage) {
                storage.setItem(newData);
            }
        }
    };
    return [
        data,
        saveData,
        loading
    ];
}
function BrandSwitchingStatus() {
    const { loading, currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [switching, setSwitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((brand)=>{
        setSwitching(true);
        const timer = setTimeout(()=>setSwitching(false), 1000);
        return ()=>clearTimeout(timer);
    });
    if (!switching && !loading) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 253,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm",
                    children: switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 254,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 252,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 251,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/utils/enable-firebase-storage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enable Firebase Storage Utility
 * Helper to re-enable Firebase Storage after rules are deployed
 */ __turbopack_context__.s({
    "CODE_TO_UNCOMMENT": (()=>CODE_TO_UNCOMMENT),
    "FIREBASE_STORAGE_INSTRUCTIONS": (()=>FIREBASE_STORAGE_INSTRUCTIONS),
    "enableFirebaseStorage": (()=>enableFirebaseStorage),
    "testFirebaseStorageRules": (()=>testFirebaseStorageRules)
});
const FIREBASE_STORAGE_INSTRUCTIONS = `
🔥 FIREBASE STORAGE RULES DEPLOYMENT INSTRUCTIONS

1. Go to Firebase Console: https://console.firebase.google.com/
2. Select your project: localbuzz-mpkuv
3. Go to Storage → Rules
4. Replace the current rules with:

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and manage their own generated content
    match /generated-content/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and manage their own artifacts
    match /artifacts/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and manage their own brand assets
    match /brand-assets/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}

5. Click "Publish"
6. Wait 2-3 minutes for rules to propagate
7. Come back and run: enableFirebaseStorage()
`;
const CODE_TO_UNCOMMENT = `
After deploying Firebase Storage rules, go to:
src/app/quick-content/page.tsx

Find this section around line 209:
// TEMPORARY: Skip Firebase Storage upload until rules are deployed

Replace the entire processPostImages function with the commented code below it.

Or simply run: enableFirebaseStorage() in the browser console.
`;
function enableFirebaseStorage() {
    return {
        instructions: FIREBASE_STORAGE_INSTRUCTIONS,
        codeInstructions: CODE_TO_UNCOMMENT,
        status: 'Instructions displayed - manual code update required'
    };
}
async function testFirebaseStorageRules() {
    try {
        // This would need to be implemented with actual Firebase Storage test
        return {
            success: false,
            message: 'Manual test required - generate content to test'
        };
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
// Make functions available globally for easy access
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
// Auto-display instructions on load
}
}}),
"[project]/src/app/quick-content/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/app/content-calendar/page.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$content$2d$calendar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/content-calendar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$close$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftClose$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/panel-left-close.js [app-ssr] (ecmascript) <export default as PanelLeftClose>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftOpen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/panel-left-open.js [app-ssr] (ecmascript) <export default as PanelLeftOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/unified-brand-layout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/brand-scoped-storage.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-firebase-auth.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-generated-posts.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$enable$2d$firebase$2d$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/enable-firebase-storage.ts [app-ssr] (ecmascript)"); // Load Firebase Storage utilities
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// No limit on posts - store all generated content
// Brand-scoped storage cleanup utility
const cleanupBrandScopedStorage = (brandStorage)=>{
    try {
        const posts = brandStorage.getItem() || [];
        // Fix invalid dates in existing posts
        const fixedPosts = posts.map((post)=>{
            if (!post.date || isNaN(new Date(post.date).getTime())) {
                return {
                    ...post,
                    date: new Date().toISOString()
                };
            }
            return post;
        });
        if (fixedPosts.length > 5) {
            // Keep only the 5 most recent posts
            const recentPosts = fixedPosts.slice(0, 5);
            brandStorage.setItem(recentPosts);
            return recentPosts;
        } else {
            // Save the fixed posts back
            brandStorage.setItem(fixedPosts);
            return fixedPosts;
        }
    } catch (error) {}
    return null;
};
function QuickContentPage() {
    const { currentBrand, brands, loading: brandLoading, selectBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const postsStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandStorage"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_FEATURES"].QUICK_CONTENT);
    const [generatedPosts, setGeneratedPosts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const { open: sidebarOpen, toggleSidebar } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSidebar"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$firebase$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFirebaseAuth"])();
    const { savePost, saving } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$generated$2d$posts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useGeneratedPosts"])();
    // Inline brand restoration function
    const forceBrandRestore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        try {
            // Try to restore from full brand data first
            const savedBrandData = localStorage.getItem('currentBrandData');
            if (savedBrandData) {
                const parsedData = JSON.parse(savedBrandData);
                // Find matching brand in current brands list
                const matchingBrand = brands.find((b)=>b.id === parsedData.id);
                if (matchingBrand) {
                    selectBrand(matchingBrand);
                    return true;
                }
            }
            // Fallback to brand ID restoration
            const savedBrandId = localStorage.getItem('selectedBrandId');
            if (savedBrandId && brands.length > 0) {
                const savedBrand = brands.find((b)=>b.id === savedBrandId);
                if (savedBrand) {
                    selectBrand(savedBrand);
                    return true;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }, [
        brands,
        selectBrand
    ]);
    // Load posts when brand changes using unified brand system
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((brand)=>{
        const brandName = brand?.businessName || brand?.name || 'none';
        if (!brand) {
            setGeneratedPosts([]);
            setIsLoading(false);
            return;
        }
        setIsLoading(true);
        try {
            if (postsStorage) {
                const posts = postsStorage.getItem() || [];
                // Check if any posts have invalid dates
                const hasInvalidDates = posts.some((post)=>!post.date || isNaN(new Date(post.date).getTime()));
                if (hasInvalidDates) {
                    postsStorage.removeItem();
                    setGeneratedPosts([]);
                } else {
                    setGeneratedPosts(posts);
                }
            } else {
                setGeneratedPosts([]);
            }
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Failed to load data",
                description: "Could not read your posts data. It might be corrupted."
            });
        } finally{
            setIsLoading(false);
        }
    }, [
        postsStorage,
        toast
    ]));
    // Enhanced brand selection logic with persistence recovery
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!brandLoading) {
            // Add a small delay to ensure brands have time to load
            const timer = setTimeout(()=>{
                if (brands.length === 0) {
                    // No brands exist, redirect to brand setup
                    try {
                        router.prefetch('/brand-profile');
                    } catch  {}
                    router.push('/brand-profile');
                } else if (brands.length > 0 && !currentBrand) {
                    // Try to restore from persistence first
                    const restored = forceBrandRestore();
                    if (!restored) {
                        // If restoration failed, auto-select the first brand
                        selectBrand(brands[0]);
                    }
                }
            }, 1000); // 1 second delay
            return ()=>clearTimeout(timer);
        }
    }, [
        currentBrand,
        brands.length,
        brandLoading,
        router,
        selectBrand,
        forceBrandRestore
    ]);
    // Process generated post with Firebase Storage upload and database fallback
    const processPostImages = async (post)=>{
        try {
            // Check if user is authenticated for Firebase Storage
            if (!user) {
                toast({
                    title: "Content Saved",
                    description: "Content saved to database. Sign in to save images permanently in the cloud.",
                    variant: "default"
                });
                return post; // Return original post with data URLs
            }
            // TEMPORARY: Skip Firebase Storage upload until rules are deployed
            // Save to database with data URLs (temporary solution)
            toast({
                title: "Content Saved to Database",
                description: "Content saved successfully. Deploy Firebase Storage rules for permanent image URLs.",
                variant: "default"
            });
            return post; // Return original post with data URLs
        /* UNCOMMENT THIS AFTER DEPLOYING FIREBASE STORAGE RULES:
      try {
        // Try Firebase Storage first
        const processedPost = await processGeneratedPost(post, user.uid);


        // Show success message
        toast({
          title: "Images Saved to Cloud",
          description: "Images have been permanently saved to Firebase Storage.",
          variant: "default",
        });

        return processedPost;
      } catch (storageError) {

        // Fallback: Save to database with data URLs (temporary)
        toast({
          title: "Content Saved to Database",
          description: "Images stored temporarily. Please update Firebase Storage rules for permanent cloud storage.",
          variant: "default",
        });

        return post; // Return original post with data URLs
      }
      */ } catch (error) {
            toast({
                title: "Content Saved Locally",
                description: "Content generated successfully but stored locally only.",
                variant: "default"
            });
            return post; // Return original post if all processing fails
        }
    };
    const handlePostGenerated = async (post)=>{
        // Process images with Firebase Storage upload
        let processedPost = await processPostImages(post);
        // Add the processed post to the beginning of the array (no limit)
        const newPosts = [
            processedPost,
            ...generatedPosts
        ];
        setGeneratedPosts(newPosts);
        if (!postsStorage) {
            toast({
                title: "Storage Unavailable",
                description: "Post generated but couldn't be saved. Please select a brand.",
                variant: "destructive"
            });
            return;
        }
        try {
            // Save to localStorage first (immediate)
            postsStorage.setItem(newPosts);
            // Also save to Firestore database (permanent backup)
            if (user) {
                try {
                    const postId = await savePost(processedPost);
                    // Update the post with the Firestore ID
                    const savedPost = {
                        ...processedPost,
                        id: postId
                    };
                    const updatedPosts = [
                        savedPost,
                        ...generatedPosts
                    ];
                    setGeneratedPosts(updatedPosts);
                    postsStorage.setItem(updatedPosts);
                    toast({
                        title: "Content Saved Successfully",
                        description: "Your content has been saved to both local storage and the database.",
                        variant: "default"
                    });
                } catch (firestoreError) {
                    toast({
                        title: "Content Saved Locally",
                        description: "Content saved locally. Database save failed but content is secure.",
                        variant: "default"
                    });
                }
            } else {
                toast({
                    title: "Content Saved Locally",
                    description: "Content saved locally. Sign in to save to database permanently.",
                    variant: "default"
                });
            }
        } catch (error) {
            // Show user-friendly error message
            toast({
                title: "Storage Issue",
                description: "Post generated successfully but couldn't be saved. Storage may be full.",
                variant: "destructive"
            });
        // Keep the post in memory even if storage fails
        }
    };
    // Debug function to clear all posts for current brand
    const clearAllPosts = ()=>{
        if (!postsStorage) {
            return;
        }
        try {
            postsStorage.removeItem();
            setGeneratedPosts([]);
            toast({
                title: "Posts Cleared",
                description: `All stored posts have been cleared for ${currentBrand?.businessName || currentBrand?.name}.`
            });
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Clear Failed",
                description: "Could not clear stored posts."
            });
        }
    };
    const handlePostUpdated = async (updatedPost)=>{
        if (!postsStorage) {
            return;
        }
        try {
            const updatedPosts = generatedPosts.map((post)=>post.id === updatedPost.id ? updatedPost : post);
            setGeneratedPosts(updatedPosts);
            // Check storage size before saving
            const postsData = JSON.stringify(updatedPosts);
            const maxSize = 5 * 1024 * 1024; // 5MB limit
            if (postsData.length > maxSize) {
                // If too large, keep fewer posts
                const reducedPosts = updatedPosts.slice(0, Math.max(1, Math.floor(MAX_POSTS_TO_STORE / 2)));
                postsStorage.setItem(reducedPosts);
                setGeneratedPosts(reducedPosts);
                toast({
                    title: "Storage Optimized",
                    description: "Reduced stored posts to prevent storage overflow. Some older posts were removed."
                });
            } else {
                postsStorage.setItem(updatedPosts);
            }
        } catch (error) {
            toast({
                variant: "destructive",
                title: "Failed to update post",
                description: "Unable to save post updates. Your browser storage may be full."
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SidebarInset"], {
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-14 items-center justify-between gap-4 border-b bg-card px-4 lg:h-[60px] lg:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: toggleSidebar,
                                className: "h-8 w-8",
                                title: sidebarOpen ? "Hide sidebar for full-screen mode" : "Show sidebar",
                                children: sidebarOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$close$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftClose$3e$__["PanelLeftClose"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 374,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$panel$2d$left$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PanelLeftOpen$3e$__["PanelLeftOpen"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 376,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 366,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-muted-foreground",
                                children: sidebarOpen ? "Sidebar visible" : "Full-screen mode"
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 379,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 365,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "secondary",
                                    size: "icon",
                                    className: "rounded-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Avatar"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                    src: "https://placehold.co/40x40.png",
                                                    alt: "User",
                                                    "data-ai-hint": "user avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                                    lineNumber: 388,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {}, void 0, false, {
                                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                                        lineNumber: 389,
                                                        columnNumber: 33
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                                    lineNumber: 389,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/quick-content/page.tsx",
                                            lineNumber: 387,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Toggle user menu"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/quick-content/page.tsx",
                                            lineNumber: 391,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 386,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 385,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                align: "end",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                                        children: "My Account"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 395,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 396,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                        onClick: ()=>{
                                            if (postsStorage) {
                                                const cleaned = cleanupBrandScopedStorage(postsStorage);
                                                if (cleaned) {
                                                    setGeneratedPosts(cleaned);
                                                    toast({
                                                        title: "Storage Cleaned",
                                                        description: `Removed older posts for ${currentBrand?.businessName || currentBrand?.name}.`
                                                    });
                                                } else {
                                                    toast({
                                                        title: "Storage Clean",
                                                        description: "Storage is already optimized."
                                                    });
                                                }
                                            } else {
                                                toast({
                                                    variant: "destructive",
                                                    title: "No Brand Selected",
                                                    description: "Please select a brand first."
                                                });
                                            }
                                        },
                                        children: "Clear Old Posts"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 397,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 394,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 384,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 364,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "min-h-full bg-gradient-to-br from-blue-50 to-indigo-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto",
                            children: isLoading || brandLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex w-full min-h-[300px] items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full max-w-3xl text-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Loading Quick Content..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 434,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 433,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 432,
                                columnNumber: 17
                            }, this) : !currentBrand ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-center min-h-[400px] space-y-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold",
                                        children: "Select a Brand"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 439,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-muted-foreground text-center",
                                        children: "Please select a brand to start generating content."
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 440,
                                        columnNumber: 19
                                    }, this),
                                    brands.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-wrap gap-2",
                                        children: brands.map((brand)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                onClick: ()=>selectBrand(brand),
                                                variant: "outline",
                                                children: brand.businessName || brand.name
                                            }, brand.id, false, {
                                                fileName: "[project]/src/app/quick-content/page.tsx",
                                                lineNumber: 446,
                                                columnNumber: 25
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 444,
                                        columnNumber: 21
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        onMouseEnter: ()=>router.prefetch('/brand-profile'),
                                        onFocus: ()=>router.prefetch('/brand-profile'),
                                        onClick: ()=>router.push('/brand-profile'),
                                        children: "Create Brand Profile"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/quick-content/page.tsx",
                                        lineNumber: 456,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 438,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: currentBrand && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$content$2d$calendar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentCalendar"], {
                                    brandProfile: {
                                        businessName: currentBrand.businessName,
                                        businessType: currentBrand.businessType || '',
                                        location: currentBrand.location || '',
                                        logoDataUrl: currentBrand.logoDataUrl || '',
                                        visualStyle: currentBrand.visualStyle || '',
                                        writingTone: currentBrand.writingTone || '',
                                        contentThemes: currentBrand.contentThemes || '',
                                        websiteUrl: currentBrand.websiteUrl || '',
                                        description: currentBrand.description || '',
                                        // Convert services array to newline-separated string to match BrandProfile.services
                                        services: Array.isArray(currentBrand.services) ? currentBrand.services.map((s)=>s.name).join('\n') : currentBrand.services || '',
                                        targetAudience: currentBrand.targetAudience || '',
                                        keyFeatures: currentBrand.keyFeatures || '',
                                        competitiveAdvantages: currentBrand.competitiveAdvantages || '',
                                        contactInfo: {
                                            phone: currentBrand.contactPhone || '',
                                            email: currentBrand.contactEmail || '',
                                            address: currentBrand.contactAddress || ''
                                        },
                                        socialMedia: {
                                            facebook: currentBrand.facebookUrl || '',
                                            instagram: currentBrand.instagramUrl || '',
                                            twitter: currentBrand.twitterUrl || '',
                                            linkedin: currentBrand.linkedinUrl || ''
                                        },
                                        primaryColor: currentBrand.primaryColor || undefined,
                                        accentColor: currentBrand.accentColor || undefined,
                                        backgroundColor: currentBrand.backgroundColor || undefined,
                                        designExamples: currentBrand.designExamples || []
                                    },
                                    posts: generatedPosts,
                                    onPostGenerated: handlePostGenerated,
                                    onPostUpdated: handlePostUpdated
                                }, void 0, false, {
                                    fileName: "[project]/src/app/quick-content/page.tsx",
                                    lineNumber: 477,
                                    columnNumber: 21
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/quick-content/page.tsx",
                                lineNumber: 462,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/quick-content/page.tsx",
                            lineNumber: 430,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/quick-content/page.tsx",
                        lineNumber: 429,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/quick-content/page.tsx",
                    lineNumber: 428,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 427,
                columnNumber: 7
            }, this)
        ]
    }, currentBrand?.id || 'no-brand', true, {
        fileName: "[project]/src/app/quick-content/page.tsx",
        lineNumber: 363,
        columnNumber: 5
    }, this);
}
function QuickContentPageWithUnifiedBrand() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnifiedBrandLayout"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(QuickContentPage, {}, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 529,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrandSwitchingStatus"], {}, void 0, false, {
                fileName: "[project]/src/app/quick-content/page.tsx",
                lineNumber: 530,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/quick-content/page.tsx",
        lineNumber: 528,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = QuickContentPageWithUnifiedBrand;
}}),

};

//# sourceMappingURL=src_e40a986d._.js.map