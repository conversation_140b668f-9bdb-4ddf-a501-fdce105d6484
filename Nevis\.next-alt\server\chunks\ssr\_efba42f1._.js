module.exports = {

"[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript)");
    });
});
}}),

};