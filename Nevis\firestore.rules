rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all reads and writes for testing
    // TODO: Restore proper authentication rules after testing
    match /{document=**} {
      allow read, write: if true;
    }

    // COMMENTED OUT - Original authentication rules
    // Users can read and write their own user document
    // match /users/{userId} {
    //   allow read, write: if request.auth != null && request.auth.uid == userId;
    // }

    // Brand profiles - users can manage their own profiles
    // match /brandProfiles/{profileId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }

    // Generated posts - users can manage their own posts
    // match /generatedPosts/{postId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }

    // Artifacts - users can manage their own artifacts
    // match /artifacts/{artifactId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }

    // Analytics - users can read/write their own analytics
    // match /analytics/{analyticsId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }

    // Content calendar - users can manage their own calendar
    // match /contentCalendar/{calendarId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }

    // Design analytics - users can read/write their own design analytics
    // match /designAnalytics/{designId} {
    //   allow read, write: if request.auth != null &&
    //     (resource == null || resource.data.userId == request.auth.uid);
    // }
  }
}
