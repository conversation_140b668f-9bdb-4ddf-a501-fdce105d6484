module.exports = {

"[project]/node_modules/@genkit-ai/ai/lib/check-operation.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var check_operation_exports = {};
__export(check_operation_exports, {
    checkOperation: ()=>checkOperation
});
module.exports = __toCommonJS(check_operation_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
async function checkOperation(registry, operation) {
    if (!operation.action) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: "Provided operation is missing original request information"
        });
    }
    const backgroundAction = await registry.lookupBackgroundAction(operation.action);
    if (!backgroundAction) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: `Failed to resolve background action from original request: ${operation.action}`
        });
    }
    return await backgroundAction.check(operation);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    checkOperation
}); //# sourceMappingURL=check-operation.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var document_exports = {};
__export(document_exports, {
    CustomPartSchema: ()=>CustomPartSchema,
    DataPartSchema: ()=>DataPartSchema,
    Document: ()=>Document,
    DocumentDataSchema: ()=>DocumentDataSchema,
    MediaPartSchema: ()=>MediaPartSchema,
    MediaSchema: ()=>MediaSchema,
    PartSchema: ()=>PartSchema,
    ReasoningPartSchema: ()=>ReasoningPartSchema,
    ResourcePartSchema: ()=>ResourcePartSchema,
    TextPartSchema: ()=>TextPartSchema,
    ToolRequestPartSchema: ()=>ToolRequestPartSchema,
    ToolRequestSchema: ()=>ToolRequestSchema,
    ToolResponsePartSchema: ()=>ToolResponsePartSchema,
    ToolResponseSchema: ()=>ToolResponseSchema,
    checkUniqueDocuments: ()=>checkUniqueDocuments
});
module.exports = __toCommonJS(document_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
const EmptyPartSchema = import_core.z.object({
    text: import_core.z.never().optional(),
    media: import_core.z.never().optional(),
    toolRequest: import_core.z.never().optional(),
    toolResponse: import_core.z.never().optional(),
    data: import_core.z.unknown().optional(),
    metadata: import_core.z.record(import_core.z.unknown()).optional(),
    custom: import_core.z.record(import_core.z.unknown()).optional(),
    reasoning: import_core.z.never().optional(),
    resource: import_core.z.never().optional()
});
const TextPartSchema = EmptyPartSchema.extend({
    /** The text of the message. */ text: import_core.z.string()
});
const ReasoningPartSchema = EmptyPartSchema.extend({
    /** The reasoning text of the message. */ reasoning: import_core.z.string()
});
const MediaSchema = import_core.z.object({
    /** The media content type. Inferred from data uri if not provided. */ contentType: import_core.z.string().optional(),
    /** A `data:` or `https:` uri containing the media content.  */ url: import_core.z.string()
});
const MediaPartSchema = EmptyPartSchema.extend({
    media: MediaSchema
});
const ToolRequestSchema = import_core.z.object({
    /** The call id or reference for a specific request. */ ref: import_core.z.string().optional(),
    /** The name of the tool to call. */ name: import_core.z.string(),
    /** The input parameters for the tool, usually a JSON object. */ input: import_core.z.unknown().optional()
});
const ToolRequestPartSchema = EmptyPartSchema.extend({
    /** A request for a tool to be executed, usually provided by a model. */ toolRequest: ToolRequestSchema
});
const ToolResponseSchema = import_core.z.object({
    /** The call id or reference for a specific request. */ ref: import_core.z.string().optional(),
    /** The name of the tool. */ name: import_core.z.string(),
    /** The output data returned from the tool, usually a JSON object. */ output: import_core.z.unknown().optional()
});
const ToolResponsePartSchema = EmptyPartSchema.extend({
    /** A provided response to a tool call. */ toolResponse: ToolResponseSchema
});
const DataPartSchema = EmptyPartSchema.extend({
    data: import_core.z.unknown()
});
const CustomPartSchema = EmptyPartSchema.extend({
    custom: import_core.z.record(import_core.z.any())
});
const ResourcePartSchema = EmptyPartSchema.extend({
    resource: import_core.z.object({
        uri: import_core.z.string()
    })
});
const PartSchema = import_core.z.union([
    TextPartSchema,
    MediaPartSchema
]);
const DocumentDataSchema = import_core.z.object({
    content: import_core.z.array(PartSchema),
    metadata: import_core.z.record(import_core.z.string(), import_core.z.any()).optional()
});
function deepCopy(value) {
    if (value === void 0) {
        return value;
    }
    return JSON.parse(JSON.stringify(value));
}
class Document {
    content;
    metadata;
    constructor(data){
        this.content = deepCopy(data.content);
        this.metadata = deepCopy(data.metadata);
    }
    static fromText(text, metadata) {
        return new Document({
            content: [
                {
                    text
                }
            ],
            metadata
        });
    }
    // Construct a Document from a single media item
    static fromMedia(url, contentType, metadata) {
        return new Document({
            content: [
                {
                    media: {
                        contentType,
                        url
                    }
                }
            ],
            metadata
        });
    }
    // Construct a Document from content
    static fromData(data, dataType, metadata) {
        if (dataType === "text") {
            return this.fromText(data, metadata);
        }
        return this.fromMedia(data, dataType, metadata);
    }
    /**
   * Concatenates all `text` parts present in the document with no delimiter.
   * @returns A string of all concatenated text parts.
   */ get text() {
        return this.content.map((part)=>part.text || "").join("");
    }
    /**
   * Media array getter.
   * @returns the array of media parts.
   */ get media() {
        return this.content.filter((part)=>part.media && !part.text).map((part)=>part.media);
    }
    /**
   * Gets the first item in the document. Either text or media url.
   */ get data() {
        if (this.text) {
            return this.text;
        }
        if (this.media) {
            return this.media[0].url;
        }
        return "";
    }
    /**
   * Gets the contentType of the data that is returned by data()
   */ get dataType() {
        if (this.text) {
            return "text";
        }
        if (this.media && this.media[0].contentType) {
            return this.media[0].contentType;
        }
        return void 0;
    }
    toJSON() {
        return {
            content: deepCopy(this.content),
            metadata: deepCopy(this.metadata)
        };
    }
    /**
   * Embedders may return multiple embeddings for a single document.
   * But storage still requires a 1:1 relationship. So we create an
   * array of Documents from a single document - one per embedding.
   * @param embeddings The embeddings to create the documents from.
   * @returns an array of documents based on this document and the embeddings.
   */ getEmbeddingDocuments(embeddings) {
        const documents = [];
        for (const embedding of embeddings){
            const jsonDoc = this.toJSON();
            if (embedding.metadata) {
                if (!jsonDoc.metadata) {
                    jsonDoc.metadata = {};
                }
                jsonDoc.metadata.embedMetadata = embedding.metadata;
            }
            documents.push(new Document(jsonDoc));
        }
        checkUniqueDocuments(documents);
        return documents;
    }
}
function checkUniqueDocuments(documents) {
    const seen = /* @__PURE__ */ new Set();
    for (const doc of documents){
        const serialized = JSON.stringify(doc);
        if (seen.has(serialized)) {
            console.warn("Warning: embedding documents are not unique. Are you missing embed metadata?");
            return false;
        }
        seen.add(serialized);
    }
    return true;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CustomPartSchema,
    DataPartSchema,
    Document,
    DocumentDataSchema,
    MediaPartSchema,
    MediaSchema,
    PartSchema,
    ReasoningPartSchema,
    ResourcePartSchema,
    TextPartSchema,
    ToolRequestPartSchema,
    ToolRequestSchema,
    ToolResponsePartSchema,
    ToolResponseSchema,
    checkUniqueDocuments
}); //# sourceMappingURL=document.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/embedder.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var embedder_exports = {};
__export(embedder_exports, {
    EmbedderInfoSchema: ()=>EmbedderInfoSchema,
    EmbeddingSchema: ()=>EmbeddingSchema,
    defineEmbedder: ()=>defineEmbedder,
    embed: ()=>embed,
    embedMany: ()=>embedMany,
    embedderActionMetadata: ()=>embedderActionMetadata,
    embedderRef: ()=>embedderRef
});
module.exports = __toCommonJS(embedder_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
const EmbeddingSchema = import_core.z.object({
    embedding: import_core.z.array(import_core.z.number()),
    metadata: import_core.z.record(import_core.z.string(), import_core.z.unknown()).optional()
});
const EmbedRequestSchema = import_core.z.object({
    input: import_core.z.array(import_document.DocumentDataSchema),
    options: import_core.z.any().optional()
});
const EmbedResponseSchema = import_core.z.object({
    embeddings: import_core.z.array(EmbeddingSchema)
});
function withMetadata(embedder, configSchema) {
    const withMeta = embedder;
    withMeta.__configSchema = configSchema;
    return withMeta;
}
function defineEmbedder(registry, options, runner) {
    const embedder = (0, import_core.defineAction)(registry, {
        actionType: "embedder",
        name: options.name,
        inputSchema: options.configSchema ? EmbedRequestSchema.extend({
            options: options.configSchema.optional()
        }) : EmbedRequestSchema,
        outputSchema: EmbedResponseSchema,
        metadata: {
            type: "embedder",
            info: options.info,
            embedder: {
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0
            }
        }
    }, (i)=>runner(i.input.map((dd)=>new import_document.Document(dd)), i.options));
    const ewm = withMetadata(embedder, options.configSchema);
    return ewm;
}
async function embed(registry, params) {
    const embedder = await resolveEmbedder(registry, params);
    if (!embedder.embedderAction) {
        let embedderId;
        if (typeof params.embedder === "string") {
            embedderId = params.embedder;
        } else if (params.embedder?.__action?.name) {
            embedderId = params.embedder.__action.name;
        } else {
            embedderId = params.embedder.name;
        }
        throw new Error(`Unable to resolve embedder ${embedderId}`);
    }
    const response = await embedder.embedderAction({
        input: typeof params.content === "string" ? [
            import_document.Document.fromText(params.content, params.metadata)
        ] : [
            params.content
        ],
        options: {
            version: embedder.version,
            ...embedder.config,
            ...params.options
        }
    });
    return response.embeddings;
}
async function resolveEmbedder(registry, params) {
    if (typeof params.embedder === "string") {
        return {
            embedderAction: await registry.lookupAction(`/embedder/${params.embedder}`)
        };
    } else if (Object.hasOwnProperty.call(params.embedder, "__action")) {
        return {
            embedderAction: params.embedder
        };
    } else if (Object.hasOwnProperty.call(params.embedder, "name")) {
        const ref = params.embedder;
        return {
            embedderAction: await registry.lookupAction(`/embedder/${params.embedder.name}`),
            config: {
                ...ref.config
            },
            version: ref.version
        };
    }
    throw new Error(`failed to resolve embedder ${params.embedder}`);
}
async function embedMany(registry, params) {
    let embedder;
    if (typeof params.embedder === "string") {
        embedder = await registry.lookupAction(`/embedder/${params.embedder}`);
    } else if (Object.hasOwnProperty.call(params.embedder, "info")) {
        embedder = await registry.lookupAction(`/embedder/${params.embedder.name}`);
    } else {
        embedder = params.embedder;
    }
    if (!embedder) {
        throw new Error("Unable to utilize the provided embedder");
    }
    const response = await embedder({
        input: params.content.map((i)=>typeof i === "string" ? import_document.Document.fromText(i, params.metadata) : i),
        options: params.options
    });
    return response.embeddings;
}
const EmbedderInfoSchema = import_core.z.object({
    /** Friendly label for this model (e.g. "Google AI - Gemini Pro") */ label: import_core.z.string().optional(),
    /** Supported model capabilities. */ supports: import_core.z.object({
        /** Model can input this type of data. */ input: import_core.z.array(import_core.z.enum([
            "text",
            "image",
            "video"
        ])).optional(),
        /** Model can support multiple languages */ multilingual: import_core.z.boolean().optional()
    }).optional(),
    /** Embedding dimension */ dimensions: import_core.z.number().optional()
});
function embedderRef(options) {
    return {
        ...options
    };
}
function embedderActionMetadata({ name, info, configSchema }) {
    return {
        actionType: "embedder",
        name,
        inputJsonSchema: (0, import_schema.toJsonSchema)({
            schema: EmbedRequestSchema
        }),
        outputJsonSchema: (0, import_schema.toJsonSchema)({
            schema: EmbedResponseSchema
        }),
        metadata: {
            embedder: {
                ...info,
                customOptions: configSchema ? (0, import_schema.toJsonSchema)({
                    schema: configSchema
                }) : void 0
            }
        }
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    EmbedderInfoSchema,
    EmbeddingSchema,
    defineEmbedder,
    embed,
    embedMany,
    embedderActionMetadata,
    embedderRef
}); //# sourceMappingURL=embedder.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/evaluator.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var evaluator_exports = {};
__export(evaluator_exports, {
    ATTR_PREFIX: ()=>ATTR_PREFIX,
    BaseDataPointSchema: ()=>BaseDataPointSchema,
    BaseEvalDataPointSchema: ()=>BaseEvalDataPointSchema,
    EVALUATOR_METADATA_KEY_DEFINITION: ()=>EVALUATOR_METADATA_KEY_DEFINITION,
    EVALUATOR_METADATA_KEY_DISPLAY_NAME: ()=>EVALUATOR_METADATA_KEY_DISPLAY_NAME,
    EVALUATOR_METADATA_KEY_IS_BILLED: ()=>EVALUATOR_METADATA_KEY_IS_BILLED,
    EvalResponseSchema: ()=>EvalResponseSchema,
    EvalResponsesSchema: ()=>EvalResponsesSchema,
    EvalStatusEnum: ()=>EvalStatusEnum,
    EvaluatorInfoSchema: ()=>EvaluatorInfoSchema,
    SPAN_STATE_ATTR: ()=>SPAN_STATE_ATTR,
    ScoreSchema: ()=>ScoreSchema,
    defineEvaluator: ()=>defineEvaluator,
    evaluate: ()=>evaluate,
    evaluatorRef: ()=>evaluatorRef
});
module.exports = __toCommonJS(evaluator_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-route] (ecmascript)");
var import_crypto = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)");
const ATTR_PREFIX = "genkit";
const SPAN_STATE_ATTR = ATTR_PREFIX + ":state";
const BaseDataPointSchema = import_core.z.object({
    input: import_core.z.unknown(),
    output: import_core.z.unknown().optional(),
    context: import_core.z.array(import_core.z.unknown()).optional(),
    reference: import_core.z.unknown().optional(),
    testCaseId: import_core.z.string().optional(),
    traceIds: import_core.z.array(import_core.z.string()).optional()
});
const BaseEvalDataPointSchema = BaseDataPointSchema.extend({
    testCaseId: import_core.z.string()
});
const EvalStatusEnumSchema = import_core.z.enum([
    "UNKNOWN",
    "PASS",
    "FAIL"
]);
var EvalStatusEnum = /* @__PURE__ */ ((EvalStatusEnum2)=>{
    EvalStatusEnum2["UNKNOWN"] = "UNKNOWN";
    EvalStatusEnum2["PASS"] = "PASS";
    EvalStatusEnum2["FAIL"] = "FAIL";
    return EvalStatusEnum2;
})(EvalStatusEnum || {});
const ScoreSchema = import_core.z.object({
    id: import_core.z.string().describe("Optional ID to differentiate different scores if applying in a single evaluation").optional(),
    score: import_core.z.union([
        import_core.z.number(),
        import_core.z.string(),
        import_core.z.boolean()
    ]).optional(),
    status: EvalStatusEnumSchema.optional(),
    error: import_core.z.string().optional(),
    details: import_core.z.object({
        reasoning: import_core.z.string().optional()
    }).passthrough().optional()
});
const EVALUATOR_METADATA_KEY_DISPLAY_NAME = "evaluatorDisplayName";
const EVALUATOR_METADATA_KEY_DEFINITION = "evaluatorDefinition";
const EVALUATOR_METADATA_KEY_IS_BILLED = "evaluatorIsBilled";
const EvalResponseSchema = import_core.z.object({
    sampleIndex: import_core.z.number().optional(),
    testCaseId: import_core.z.string(),
    traceId: import_core.z.string().optional(),
    spanId: import_core.z.string().optional(),
    evaluation: import_core.z.union([
        ScoreSchema,
        import_core.z.array(ScoreSchema)
    ])
});
const EvalResponsesSchema = import_core.z.array(EvalResponseSchema);
function withMetadata(evaluator, dataPointType, configSchema) {
    const withMeta = evaluator;
    withMeta.__dataPointType = dataPointType;
    withMeta.__configSchema = configSchema;
    return withMeta;
}
const EvalRequestSchema = import_core.z.object({
    dataset: import_core.z.array(BaseDataPointSchema),
    evalRunId: import_core.z.string(),
    options: import_core.z.unknown()
});
function defineEvaluator(registry, options, runner) {
    const evalMetadata = {};
    evalMetadata[EVALUATOR_METADATA_KEY_IS_BILLED] = options.isBilled == void 0 ? true : options.isBilled;
    evalMetadata[EVALUATOR_METADATA_KEY_DISPLAY_NAME] = options.displayName;
    evalMetadata[EVALUATOR_METADATA_KEY_DEFINITION] = options.definition;
    if (options.configSchema) {
        evalMetadata["customOptions"] = (0, import_schema.toJsonSchema)({
            schema: options.configSchema
        });
    }
    const evaluator = (0, import_core.defineAction)(registry, {
        actionType: "evaluator",
        name: options.name,
        inputSchema: EvalRequestSchema.extend({
            dataset: options.dataPointType ? import_core.z.array(options.dataPointType) : import_core.z.array(BaseDataPointSchema),
            options: options.configSchema ?? import_core.z.unknown(),
            evalRunId: import_core.z.string(),
            batchSize: import_core.z.number().optional()
        }),
        outputSchema: EvalResponsesSchema,
        metadata: {
            type: "evaluator",
            evaluator: evalMetadata
        }
    }, async (i)=>{
        const evalResponses = [];
        const batches = getBatchedArray(i.dataset, i.batchSize);
        for(let batchIndex = 0; batchIndex < batches.length; batchIndex++){
            const batch = batches[batchIndex];
            try {
                await (0, import_tracing.runInNewSpan)(registry, {
                    metadata: {
                        name: i.batchSize ? `Batch ${batchIndex}` : `Test Case ${batch[0].testCaseId}`,
                        metadata: {
                            "evaluator:evalRunId": i.evalRunId
                        }
                    },
                    labels: {
                        [import_tracing.SPAN_TYPE_ATTR]: "evaluator"
                    }
                }, async (metadata, otSpan)=>{
                    const spanId = otSpan.spanContext().spanId;
                    const traceId = otSpan.spanContext().traceId;
                    const evalRunPromises = batch.map((d, index)=>{
                        const sampleIndex = i.batchSize ? i.batchSize * batchIndex + index : batchIndex;
                        const datapoint = d;
                        metadata.input = {
                            input: datapoint.input,
                            output: datapoint.output,
                            context: datapoint.context
                        };
                        const evalOutputPromise = runner(datapoint, i.options).then((result)=>({
                                ...result,
                                traceId,
                                spanId,
                                sampleIndex
                            })).catch((error)=>{
                            return {
                                sampleIndex,
                                spanId,
                                traceId,
                                testCaseId: datapoint.testCaseId,
                                evaluation: {
                                    error: `Evaluation of test case ${datapoint.testCaseId} failed: 
${error}`
                                }
                            };
                        });
                        return evalOutputPromise;
                    });
                    const allResults = await Promise.all(evalRunPromises);
                    metadata.output = allResults.length === 1 ? allResults[0] : allResults;
                    allResults.map((result)=>{
                        evalResponses.push(result);
                    });
                });
            } catch (e) {
                import_logging.logger.error(`Evaluation of batch ${batchIndex} failed: 
${e.stack}`);
                continue;
            }
        }
        return evalResponses;
    });
    const ewm = withMetadata(evaluator, options.dataPointType, options.configSchema);
    return ewm;
}
async function evaluate(registry, params) {
    let evaluator;
    if (typeof params.evaluator === "string") {
        evaluator = await registry.lookupAction(`/evaluator/${params.evaluator}`);
    } else if (Object.hasOwnProperty.call(params.evaluator, "info")) {
        evaluator = await registry.lookupAction(`/evaluator/${params.evaluator.name}`);
    } else {
        evaluator = params.evaluator;
    }
    if (!evaluator) {
        throw new Error("Unable to utilize the provided evaluator");
    }
    return await evaluator({
        dataset: params.dataset,
        options: params.options,
        evalRunId: params.evalRunId ?? (0, import_crypto.randomUUID)()
    });
}
const EvaluatorInfoSchema = import_core.z.object({
    /** Friendly label for this evaluator */ label: import_core.z.string().optional(),
    metrics: import_core.z.array(import_core.z.string())
});
function evaluatorRef(options) {
    return {
        ...options
    };
}
function getBatchedArray(arr, batchSize) {
    let size;
    if (!batchSize) {
        size = 1;
    } else {
        size = batchSize;
    }
    const batches = [];
    for(var i = 0; i < arr.length; i += size){
        batches.push(arr.slice(i, i + size).map((d)=>({
                ...d,
                testCaseId: d.testCaseId ?? (0, import_crypto.randomUUID)()
            })));
    }
    return batches;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ATTR_PREFIX,
    BaseDataPointSchema,
    BaseEvalDataPointSchema,
    EVALUATOR_METADATA_KEY_DEFINITION,
    EVALUATOR_METADATA_KEY_DISPLAY_NAME,
    EVALUATOR_METADATA_KEY_IS_BILLED,
    EvalResponseSchema,
    EvalResponsesSchema,
    EvalStatusEnum,
    EvaluatorInfoSchema,
    SPAN_STATE_ATTR,
    ScoreSchema,
    defineEvaluator,
    evaluate,
    evaluatorRef
}); //# sourceMappingURL=evaluator.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var extract_exports = {};
__export(extract_exports, {
    extractItems: ()=>extractItems,
    extractJson: ()=>extractJson,
    parsePartialJson: ()=>parsePartialJson
});
module.exports = __toCommonJS(extract_exports);
var import_json5 = __toESM(__turbopack_context__.r("[project]/node_modules/json5/dist/index.mjs [app-route] (ecmascript)"));
var import_partial_json = __turbopack_context__.r("[project]/node_modules/partial-json/dist/index.js [app-route] (ecmascript)");
function parsePartialJson(jsonString) {
    return import_json5.default.parse(JSON.stringify((0, import_partial_json.parse)(jsonString, import_partial_json.Allow.ALL)));
}
function extractJson(text, throwOnBadJson) {
    let openingChar;
    let closingChar;
    let startPos;
    let nestingCount = 0;
    let inString = false;
    let escapeNext = false;
    for(let i = 0; i < text.length; i++){
        const char = text[i].replace(/\u00A0/g, " ");
        if (escapeNext) {
            escapeNext = false;
            continue;
        }
        if (char === "\\") {
            escapeNext = true;
            continue;
        }
        if (char === '"') {
            inString = !inString;
            continue;
        }
        if (inString) {
            continue;
        }
        if (!openingChar && (char === "{" || char === "[")) {
            openingChar = char;
            closingChar = char === "{" ? "}" : "]";
            startPos = i;
            nestingCount++;
        } else if (char === openingChar) {
            nestingCount++;
        } else if (char === closingChar) {
            nestingCount--;
            if (!nestingCount) {
                return import_json5.default.parse(text.substring(startPos || 0, i + 1));
            }
        }
    }
    if (startPos !== void 0 && nestingCount > 0) {
        try {
            return parsePartialJson(text.substring(startPos));
        } catch  {
            if (throwOnBadJson) {
                throw new Error(`Invalid JSON extracted from model output: ${text}`);
            }
            return null;
        }
    }
    if (throwOnBadJson) {
        throw new Error(`Invalid JSON extracted from model output: ${text}`);
    }
    return null;
}
function extractItems(text, cursor = 0) {
    const items = [];
    let currentCursor = cursor;
    if (cursor === 0) {
        const arrayStart = text.indexOf("[");
        if (arrayStart === -1) {
            return {
                items: [],
                cursor: text.length
            };
        }
        currentCursor = arrayStart + 1;
    }
    let objectStart = -1;
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;
    for(let i = currentCursor; i < text.length; i++){
        const char = text[i];
        if (escapeNext) {
            escapeNext = false;
            continue;
        }
        if (char === "\\") {
            escapeNext = true;
            continue;
        }
        if (char === '"') {
            inString = !inString;
            continue;
        }
        if (inString) {
            continue;
        }
        if (char === "{") {
            if (braceCount === 0) {
                objectStart = i;
            }
            braceCount++;
        } else if (char === "}") {
            braceCount--;
            if (braceCount === 0 && objectStart !== -1) {
                try {
                    const obj = import_json5.default.parse(text.substring(objectStart, i + 1));
                    items.push(obj);
                    currentCursor = i + 1;
                    objectStart = -1;
                } catch  {}
            }
        } else if (char === "]" && braceCount === 0) {
            break;
        }
    }
    return {
        items,
        cursor: currentCursor
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    extractItems,
    extractJson,
    parsePartialJson
}); //# sourceMappingURL=extract.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/array.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var array_exports = {};
__export(array_exports, {
    arrayFormatter: ()=>arrayFormatter
});
module.exports = __toCommonJS(array_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_extract = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)");
const arrayFormatter = {
    name: "array",
    config: {
        contentType: "application/json",
        constrained: true
    },
    handler: (schema)=>{
        if (schema && schema.type !== "array") {
            throw new import_core.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `Must supply an 'array' schema type when using the 'items' parser format.`
            });
        }
        let instructions;
        if (schema) {
            instructions = `Output should be a JSON array conforming to the following schema:
    
\`\`\`
${JSON.stringify(schema)}
\`\`\`
    `;
        }
        return {
            parseChunk: (chunk)=>{
                const cursor = chunk.previousChunks?.length ? (0, import_extract.extractItems)(chunk.previousText).cursor : 0;
                const { items } = (0, import_extract.extractItems)(chunk.accumulatedText, cursor);
                return items;
            },
            parseMessage: (message)=>{
                const { items } = (0, import_extract.extractItems)(message.text, 0);
                return items;
            },
            instructions
        };
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    arrayFormatter
}); //# sourceMappingURL=array.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/enum.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var enum_exports = {};
__export(enum_exports, {
    enumFormatter: ()=>enumFormatter
});
module.exports = __toCommonJS(enum_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
const enumFormatter = {
    name: "enum",
    config: {
        contentType: "text/enum",
        constrained: true
    },
    handler: (schema)=>{
        if (schema && schema.type !== "string" && schema.type !== "enum") {
            throw new import_core.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `Must supply a 'string' or 'enum' schema type when using the enum parser format.`
            });
        }
        let instructions;
        if (schema?.enum) {
            instructions = `Output should be ONLY one of the following enum values. Do not output any additional information or add quotes.

${schema.enum.map((v)=>v.toString()).join("\n")}`;
        }
        return {
            parseMessage: (message)=>{
                return message.text.replace(/['"]/g, "").trim();
            },
            instructions
        };
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    enumFormatter
}); //# sourceMappingURL=enum.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/json.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var json_exports = {};
__export(json_exports, {
    jsonFormatter: ()=>jsonFormatter
});
module.exports = __toCommonJS(json_exports);
var import_extract = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)");
const jsonFormatter = {
    name: "json",
    config: {
        format: "json",
        contentType: "application/json",
        constrained: true,
        defaultInstructions: false
    },
    handler: (schema)=>{
        let instructions;
        if (schema) {
            instructions = `Output should be in JSON format and conform to the following schema:

\`\`\`
${JSON.stringify(schema)}
\`\`\`
`;
        }
        return {
            parseChunk: (chunk)=>{
                return (0, import_extract.extractJson)(chunk.accumulatedText);
            },
            parseMessage: (message)=>{
                return (0, import_extract.extractJson)(message.text);
            },
            instructions
        };
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    jsonFormatter
}); //# sourceMappingURL=json.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/jsonl.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var jsonl_exports = {};
__export(jsonl_exports, {
    jsonlFormatter: ()=>jsonlFormatter
});
module.exports = __toCommonJS(jsonl_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_json5 = __toESM(__turbopack_context__.r("[project]/node_modules/json5/dist/index.mjs [app-route] (ecmascript)"));
var import_extract = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)");
function objectLines(text) {
    return text.split("\n").map((line)=>line.trim()).filter((line)=>line.startsWith("{"));
}
const jsonlFormatter = {
    name: "jsonl",
    config: {
        contentType: "application/jsonl"
    },
    handler: (schema)=>{
        if (schema && (schema.type !== "array" || schema.items?.type !== "object")) {
            throw new import_core.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `Must supply an 'array' schema type containing 'object' items when using the 'jsonl' parser format.`
            });
        }
        let instructions;
        if (schema?.items) {
            instructions = `Output should be JSONL format, a sequence of JSON objects (one per line) separated by a newline \`\\n\` character. Each line should be a JSON object conforming to the following schema:

\`\`\`
${JSON.stringify(schema.items)}
\`\`\`
    `;
        }
        return {
            parseChunk: (chunk)=>{
                const results = [];
                const text = chunk.accumulatedText;
                let startIndex = 0;
                if (chunk.previousChunks?.length) {
                    const lastNewline = chunk.previousText.lastIndexOf("\n");
                    if (lastNewline !== -1) {
                        startIndex = lastNewline + 1;
                    }
                }
                const lines = text.slice(startIndex).split("\n");
                for (const line of lines){
                    const trimmed = line.trim();
                    if (trimmed.startsWith("{")) {
                        try {
                            const result = import_json5.default.parse(trimmed);
                            if (result) {
                                results.push(result);
                            }
                        } catch (e) {
                            break;
                        }
                    }
                }
                return results;
            },
            parseMessage: (message)=>{
                const items = objectLines(message.text).map((l)=>(0, import_extract.extractJson)(l)).filter((l)=>!!l);
                return items;
            },
            instructions
        };
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    jsonlFormatter
}); //# sourceMappingURL=jsonl.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/text.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var text_exports = {};
__export(text_exports, {
    textFormatter: ()=>textFormatter
});
module.exports = __toCommonJS(text_exports);
const textFormatter = {
    name: "text",
    config: {
        contentType: "text/plain"
    },
    handler: ()=>{
        return {
            parseChunk: (chunk)=>{
                return chunk.text;
            },
            parseMessage: (message)=>{
                return message.text;
            }
        };
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    textFormatter
}); //# sourceMappingURL=text.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/formats/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var formats_exports = {};
__export(formats_exports, {
    DEFAULT_FORMATS: ()=>DEFAULT_FORMATS,
    configureFormats: ()=>configureFormats,
    defineFormat: ()=>defineFormat,
    injectInstructions: ()=>injectInstructions,
    resolveFormat: ()=>resolveFormat,
    resolveInstructions: ()=>resolveInstructions
});
module.exports = __toCommonJS(formats_exports);
var import_array = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/array.js [app-route] (ecmascript)");
var import_enum = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/enum.js [app-route] (ecmascript)");
var import_json = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/json.js [app-route] (ecmascript)");
var import_jsonl = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/jsonl.js [app-route] (ecmascript)");
var import_text = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/text.js [app-route] (ecmascript)");
function defineFormat(registry, options, handler) {
    const { name, ...config } = options;
    const formatter = {
        config,
        handler
    };
    registry.registerValue("format", name, formatter);
    return formatter;
}
async function resolveFormat(registry, outputOpts) {
    if (!outputOpts) return void 0;
    if ((outputOpts.jsonSchema || outputOpts.schema) && !outputOpts.format) {
        return registry.lookupValue("format", "json");
    }
    if (outputOpts.format) {
        return registry.lookupValue("format", outputOpts.format);
    }
    return void 0;
}
function resolveInstructions(format, schema, instructionsOption) {
    if (typeof instructionsOption === "string") return instructionsOption;
    if (instructionsOption === false) return void 0;
    if (!format) return void 0;
    return format.handler(schema).instructions;
}
function injectInstructions(messages, instructions) {
    if (!instructions) return messages;
    if (messages.find((m2)=>m2.content.find((p)=>p.metadata?.purpose === "output" && !p.metadata?.pending))) {
        return messages;
    }
    const newPart = {
        text: instructions,
        metadata: {
            purpose: "output"
        }
    };
    let targetIndex = messages.findIndex((m2)=>m2.role === "system");
    if (targetIndex < 0) targetIndex = messages.map((m2)=>m2.role).lastIndexOf("user");
    if (targetIndex < 0) return messages;
    const m = {
        ...messages[targetIndex],
        content: [
            ...messages[targetIndex].content
        ]
    };
    const partIndex = m.content.findIndex((p)=>p.metadata?.purpose === "output" && p.metadata?.pending);
    if (partIndex > 0) {
        m.content.splice(partIndex, 1, newPart);
    } else {
        m.content.push(newPart);
    }
    const outMessages = [
        ...messages
    ];
    outMessages.splice(targetIndex, 1, m);
    return outMessages;
}
const DEFAULT_FORMATS = [
    import_json.jsonFormatter,
    import_array.arrayFormatter,
    import_text.textFormatter,
    import_enum.enumFormatter,
    import_jsonl.jsonlFormatter
];
function configureFormats(registry) {
    for (const format of DEFAULT_FORMATS){
        defineFormat(registry, {
            name: format.name,
            ...format.config
        }, format.handler);
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    DEFAULT_FORMATS,
    configureFormats,
    defineFormat,
    injectInstructions,
    resolveFormat,
    resolveInstructions
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/generate/chunk.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var chunk_exports = {};
__export(chunk_exports, {
    GenerateResponseChunk: ()=>GenerateResponseChunk
});
module.exports = __toCommonJS(chunk_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_extract = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)");
class GenerateResponseChunk {
    /** The index of the message this chunk corresponds to, starting with `0` for the first model response of the generation. */ index;
    /** The role of the message this chunk corresponds to. Will always be `model` or `tool`. */ role;
    /** The content generated in this chunk. */ content;
    /** Custom model-specific data for this chunk. */ custom;
    /** Accumulated chunks for partial output extraction. */ previousChunks;
    /** The parser to be used to parse `output` from this chunk. */ parser;
    constructor(data, options){
        this.content = data.content || [];
        this.custom = data.custom;
        this.previousChunks = options.previousChunks ? [
            ...options.previousChunks
        ] : void 0;
        this.index = options.index;
        this.role = options.role;
        this.parser = options.parser;
    }
    /**
   * Concatenates all `text` parts present in the chunk with no delimiter.
   * @returns A string of all concatenated text parts.
   */ get text() {
        return this.content.map((part)=>part.text || "").join("");
    }
    /**
   * Concatenates all `reasoning` parts present in the chunk with no delimiter.
   * @returns A string of all concatenated reasoning parts.
   */ get reasoning() {
        return this.content.map((part)=>part.reasoning || "").join("");
    }
    /**
   * Concatenates all `text` parts of all chunks from the response thus far.
   * @returns A string of all concatenated chunk text content.
   */ get accumulatedText() {
        return this.previousText + this.text;
    }
    /**
   * Concatenates all `text` parts of all preceding chunks.
   */ get previousText() {
        if (!this.previousChunks) throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: "Cannot compose accumulated text without previous chunks."
        });
        return this.previousChunks?.map((c)=>c.content.map((p)=>p.text || "").join("")).join("");
    }
    /**
   * Returns the first media part detected in the chunk. Useful for extracting
   * (for example) an image from a generation expected to create one.
   * @returns The first detected `media` part in the chunk.
   */ get media() {
        return this.content.find((part)=>part.media)?.media || null;
    }
    /**
   * Returns the first detected `data` part of a chunk.
   * @returns The first `data` part detected in the chunk (if any).
   */ get data() {
        return this.content.find((part)=>part.data)?.data;
    }
    /**
   * Returns all tool request found in this chunk.
   * @returns Array of all tool request found in this chunk.
   */ get toolRequests() {
        return this.content.filter((part)=>!!part.toolRequest);
    }
    /**
   * Parses the chunk into the desired output format using the parser associated
   * with the generate request, or falls back to naive JSON parsing otherwise.
   */ get output() {
        if (this.parser) return this.parser(this);
        return this.data || (0, import_extract.extractJson)(this.accumulatedText);
    }
    toJSON() {
        const data = {
            role: this.role,
            index: this.index,
            content: this.content
        };
        if (this.custom) {
            data.custom = this.custom;
        }
        return data;
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    GenerateResponseChunk
}); //# sourceMappingURL=chunk.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var model_types_exports = {};
__export(model_types_exports, {
    CandidateErrorSchema: ()=>CandidateErrorSchema,
    CandidateSchema: ()=>CandidateSchema,
    FinishReasonSchema: ()=>FinishReasonSchema,
    GenerateActionOptionsSchema: ()=>GenerateActionOptionsSchema,
    GenerateActionOutputConfig: ()=>GenerateActionOutputConfig,
    GenerateRequestSchema: ()=>GenerateRequestSchema,
    GenerateResponseChunkSchema: ()=>GenerateResponseChunkSchema,
    GenerateResponseSchema: ()=>GenerateResponseSchema,
    GenerationCommonConfigDescriptions: ()=>GenerationCommonConfigDescriptions,
    GenerationCommonConfigSchema: ()=>GenerationCommonConfigSchema,
    GenerationUsageSchema: ()=>GenerationUsageSchema,
    MessageSchema: ()=>MessageSchema,
    ModelInfoSchema: ()=>ModelInfoSchema,
    ModelRequestSchema: ()=>ModelRequestSchema,
    ModelResponseChunkSchema: ()=>ModelResponseChunkSchema,
    ModelResponseSchema: ()=>ModelResponseSchema,
    OutputConfigSchema: ()=>OutputConfigSchema,
    PartSchema: ()=>PartSchema,
    RoleSchema: ()=>RoleSchema,
    ToolDefinitionSchema: ()=>ToolDefinitionSchema
});
module.exports = __toCommonJS(model_types_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
const PartSchema = import_core.z.union([
    import_document.TextPartSchema,
    import_document.MediaPartSchema,
    import_document.ToolRequestPartSchema,
    import_document.ToolResponsePartSchema,
    import_document.DataPartSchema,
    import_document.CustomPartSchema,
    import_document.ReasoningPartSchema,
    import_document.ResourcePartSchema
]);
const RoleSchema = import_core.z.enum([
    "system",
    "user",
    "model",
    "tool"
]);
const MessageSchema = import_core.z.object({
    role: RoleSchema,
    content: import_core.z.array(PartSchema),
    metadata: import_core.z.record(import_core.z.unknown()).optional()
});
const ModelInfoSchema = import_core.z.object({
    /** Acceptable names for this model (e.g. different versions). */ versions: import_core.z.array(import_core.z.string()).optional(),
    /** Friendly label for this model (e.g. "Google AI - Gemini Pro") */ label: import_core.z.string().optional(),
    /** Model Specific configuration. */ configSchema: import_core.z.record(import_core.z.any()).optional(),
    /** Supported model capabilities. */ supports: import_core.z.object({
        /** Model can process historical messages passed with a prompt. */ multiturn: import_core.z.boolean().optional(),
        /** Model can process media as part of the prompt (multimodal input). */ media: import_core.z.boolean().optional(),
        /** Model can perform tool calls. */ tools: import_core.z.boolean().optional(),
        /** Model can accept messages with role "system". */ systemRole: import_core.z.boolean().optional(),
        /** Model can output this type of data. */ output: import_core.z.array(import_core.z.string()).optional(),
        /** Model supports output in these content types. */ contentType: import_core.z.array(import_core.z.string()).optional(),
        /** Model can natively support document-based context grounding. */ context: import_core.z.boolean().optional(),
        /** Model can natively support constrained generation. */ constrained: import_core.z.enum([
            "none",
            "all",
            "no-tools"
        ]).optional(),
        /** Model supports controlling tool choice, e.g. forced tool calling. */ toolChoice: import_core.z.boolean().optional()
    }).optional(),
    /** At which stage of development this model is.
   * - `featured` models are recommended for general use.
   * - `stable` models are well-tested and reliable.
   * - `unstable` models are experimental and may change.
   * - `legacy` models are no longer recommended for new projects.
   * - `deprecated` models are deprecated by the provider and may be removed in future versions.
   */ stage: import_core.z.enum([
        "featured",
        "stable",
        "unstable",
        "legacy",
        "deprecated"
    ]).optional()
});
const ToolDefinitionSchema = import_core.z.object({
    name: import_core.z.string(),
    description: import_core.z.string(),
    inputSchema: import_core.z.record(import_core.z.any()).describe("Valid JSON Schema representing the input of the tool.").nullish(),
    outputSchema: import_core.z.record(import_core.z.any()).describe("Valid JSON Schema describing the output of the tool.").nullish(),
    metadata: import_core.z.record(import_core.z.any()).describe("additional metadata for this tool definition").optional()
});
const GenerationCommonConfigDescriptions = {
    temperature: "Controls the degree of randomness in token selection. A lower value is good for a more predictable response. A higher value leads to more diverse or unexpected results.",
    maxOutputTokens: "The maximum number of tokens to include in the response.",
    topK: "The maximum number of tokens to consider when sampling.",
    topP: "Decides how many possible words to consider. A higher value means that the model looks at more possible words, even the less likely ones, which makes the generated text more diverse."
};
const GenerationCommonConfigSchema = import_core.z.object({
    version: import_core.z.string().describe("A specific version of a model family, e.g. `gemini-2.0-flash` for the `googleai` family.").optional(),
    temperature: import_core.z.number().describe(GenerationCommonConfigDescriptions.temperature).optional(),
    maxOutputTokens: import_core.z.number().describe(GenerationCommonConfigDescriptions.maxOutputTokens).optional(),
    topK: import_core.z.number().describe(GenerationCommonConfigDescriptions.topK).optional(),
    topP: import_core.z.number().describe(GenerationCommonConfigDescriptions.topP).optional(),
    stopSequences: import_core.z.array(import_core.z.string()).max(5).describe("Set of character sequences (up to 5) that will stop output generation.").optional()
}).passthrough();
const OutputConfigSchema = import_core.z.object({
    format: import_core.z.string().optional(),
    schema: import_core.z.record(import_core.z.any()).optional(),
    constrained: import_core.z.boolean().optional(),
    contentType: import_core.z.string().optional()
});
const ModelRequestSchema = import_core.z.object({
    messages: import_core.z.array(MessageSchema),
    config: import_core.z.any().optional(),
    tools: import_core.z.array(ToolDefinitionSchema).optional(),
    toolChoice: import_core.z.enum([
        "auto",
        "required",
        "none"
    ]).optional(),
    output: OutputConfigSchema.optional(),
    docs: import_core.z.array(import_document.DocumentDataSchema).optional()
});
const GenerateRequestSchema = ModelRequestSchema.extend({
    /** @deprecated All responses now return a single candidate. This will always be `undefined`. */ candidates: import_core.z.number().optional()
});
const GenerationUsageSchema = import_core.z.object({
    inputTokens: import_core.z.number().optional(),
    outputTokens: import_core.z.number().optional(),
    totalTokens: import_core.z.number().optional(),
    inputCharacters: import_core.z.number().optional(),
    outputCharacters: import_core.z.number().optional(),
    inputImages: import_core.z.number().optional(),
    outputImages: import_core.z.number().optional(),
    inputVideos: import_core.z.number().optional(),
    outputVideos: import_core.z.number().optional(),
    inputAudioFiles: import_core.z.number().optional(),
    outputAudioFiles: import_core.z.number().optional(),
    custom: import_core.z.record(import_core.z.number()).optional(),
    thoughtsTokens: import_core.z.number().optional(),
    cachedContentTokens: import_core.z.number().optional()
});
const FinishReasonSchema = import_core.z.enum([
    "stop",
    "length",
    "blocked",
    "interrupted",
    "other",
    "unknown"
]);
const CandidateSchema = import_core.z.object({
    index: import_core.z.number(),
    message: MessageSchema,
    usage: GenerationUsageSchema.optional(),
    finishReason: FinishReasonSchema,
    finishMessage: import_core.z.string().optional(),
    custom: import_core.z.unknown()
});
const CandidateErrorSchema = import_core.z.object({
    index: import_core.z.number(),
    code: import_core.z.enum([
        "blocked",
        "other",
        "unknown"
    ]),
    message: import_core.z.string().optional()
});
const ModelResponseSchema = import_core.z.object({
    message: MessageSchema.optional(),
    finishReason: FinishReasonSchema,
    finishMessage: import_core.z.string().optional(),
    latencyMs: import_core.z.number().optional(),
    usage: GenerationUsageSchema.optional(),
    /** @deprecated use `raw` instead */ custom: import_core.z.unknown(),
    raw: import_core.z.unknown(),
    request: GenerateRequestSchema.optional(),
    operation: import_core.OperationSchema.optional()
});
const GenerateResponseSchema = ModelResponseSchema.extend({
    /** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. Return `message`, `finishReason`, and `finishMessage` instead. */ candidates: import_core.z.array(CandidateSchema).optional(),
    finishReason: FinishReasonSchema.optional()
});
const ModelResponseChunkSchema = import_core.z.object({
    role: RoleSchema.optional(),
    /** index of the message this chunk belongs to. */ index: import_core.z.number().optional(),
    /** The chunk of content to stream right now. */ content: import_core.z.array(PartSchema),
    /** Model-specific extra information attached to this chunk. */ custom: import_core.z.unknown().optional(),
    /** If true, the chunk includes all data from previous chunks. Otherwise, considered to be incremental. */ aggregated: import_core.z.boolean().optional()
});
const GenerateResponseChunkSchema = ModelResponseChunkSchema;
const GenerateActionOutputConfig = import_core.z.object({
    format: import_core.z.string().optional(),
    contentType: import_core.z.string().optional(),
    instructions: import_core.z.union([
        import_core.z.boolean(),
        import_core.z.string()
    ]).optional(),
    jsonSchema: import_core.z.any().optional(),
    constrained: import_core.z.boolean().optional()
});
const GenerateActionOptionsSchema = import_core.z.object({
    /** A model name (e.g. `vertexai/gemini-1.0-pro`). */ model: import_core.z.string(),
    /** Retrieved documents to be used as context for this generation. */ docs: import_core.z.array(import_document.DocumentDataSchema).optional(),
    /** Conversation history for multi-turn prompting when supported by the underlying model. */ messages: import_core.z.array(MessageSchema),
    /** List of registered tool names for this generation if supported by the underlying model. */ tools: import_core.z.array(import_core.z.string()).optional(),
    /** Tool calling mode. `auto` lets the model decide whether to use tools, `required` forces the model to choose a tool, and `none` forces the model not to use any tools. Defaults to `auto`.  */ toolChoice: import_core.z.enum([
        "auto",
        "required",
        "none"
    ]).optional(),
    /** Configuration for the generation request. */ config: import_core.z.any().optional(),
    /** Configuration for the desired output of the request. Defaults to the model's default output if unspecified. */ output: GenerateActionOutputConfig.optional(),
    /** Options for resuming an interrupted generation. */ resume: import_core.z.object({
        respond: import_core.z.array(import_document.ToolResponsePartSchema).optional(),
        restart: import_core.z.array(import_document.ToolRequestPartSchema).optional(),
        metadata: import_core.z.record(import_core.z.any()).optional()
    }).optional(),
    /** When true, return tool calls for manual processing instead of automatically resolving them. */ returnToolRequests: import_core.z.boolean().optional(),
    /** Maximum number of tool call iterations that can be performed in a single generate call (default 5). */ maxTurns: import_core.z.number().optional()
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CandidateErrorSchema,
    CandidateSchema,
    FinishReasonSchema,
    GenerateActionOptionsSchema,
    GenerateActionOutputConfig,
    GenerateRequestSchema,
    GenerateResponseChunkSchema,
    GenerateResponseSchema,
    GenerationCommonConfigDescriptions,
    GenerationCommonConfigSchema,
    GenerationUsageSchema,
    MessageSchema,
    ModelInfoSchema,
    ModelRequestSchema,
    ModelResponseChunkSchema,
    ModelResponseSchema,
    OutputConfigSchema,
    PartSchema,
    RoleSchema,
    ToolDefinitionSchema
}); //# sourceMappingURL=model-types.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/model/middleware.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var middleware_exports = {};
__export(middleware_exports, {
    CONTEXT_PREFACE: ()=>CONTEXT_PREFACE,
    augmentWithContext: ()=>augmentWithContext,
    downloadRequestMedia: ()=>downloadRequestMedia,
    simulateConstrainedGeneration: ()=>simulateConstrainedGeneration,
    simulateSystemPrompt: ()=>simulateSystemPrompt,
    validateSupport: ()=>validateSupport
});
module.exports = __toCommonJS(middleware_exports);
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var import_formats = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/index.js [app-route] (ecmascript)");
function downloadRequestMedia(options) {
    return async (req, next)=>{
        const { default: fetch } = await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const newReq = {
            ...req,
            messages: await Promise.all(req.messages.map(async (message)=>{
                const content = await Promise.all(message.content.map(async (part)=>{
                    if (!part.media || !part.media.url.startsWith("http") || options?.filter && !options?.filter(part)) {
                        return part;
                    }
                    const response = await fetch(part.media.url, {
                        size: options?.maxBytes
                    });
                    if (response.status !== 200) throw new Error(`HTTP error downloading media '${part.media.url}': ${await response.text()}`);
                    const contentType = part.media.contentType || response.headers.get("content-type") || "";
                    return {
                        media: {
                            contentType,
                            url: `data:${contentType};base64,${Buffer.from(await response.arrayBuffer()).toString("base64")}`
                        }
                    };
                }));
                return {
                    ...message,
                    content
                };
            }))
        };
        return next(newReq);
    };
}
function validateSupport(options) {
    const supports = options.supports || {};
    return async (req, next)=>{
        function invalid(message) {
            throw new Error(`Model '${options.name}' does not support ${message}. Request: ${JSON.stringify(req, null, 2)}`);
        }
        if (supports.media === false && req.messages.some((message)=>message.content.some((part)=>part.media))) invalid("media, but media was provided");
        if (supports.tools === false && req.tools?.length) invalid("tool use, but tools were provided");
        if (supports.multiturn === false && req.messages.length > 1) invalid(`multiple messages, but ${req.messages.length} were provided`);
        return next();
    };
}
function lastUserMessage(messages) {
    for(let i = messages.length - 1; i >= 0; i--){
        if (messages[i].role === "user") {
            return messages[i];
        }
    }
    return void 0;
}
function simulateSystemPrompt(options) {
    const preface = options?.preface || "SYSTEM INSTRUCTIONS:\n";
    const acknowledgement = options?.acknowledgement || "Understood.";
    return (req, next)=>{
        const messages = [
            ...req.messages
        ];
        for(let i = 0; i < messages.length; i++){
            if (req.messages[i].role === "system") {
                const systemPrompt = messages[i].content;
                messages.splice(i, 1, {
                    role: "user",
                    content: [
                        {
                            text: preface
                        },
                        ...systemPrompt
                    ]
                }, {
                    role: "model",
                    content: [
                        {
                            text: acknowledgement
                        }
                    ]
                });
                break;
            }
        }
        return next({
            ...req,
            messages
        });
    };
}
const CONTEXT_PREFACE = "\n\nUse the following information to complete your task:\n\n";
const CONTEXT_ITEM_TEMPLATE = (d, index, options)=>{
    let out = "- ";
    if (options?.citationKey) {
        out += `[${d.metadata[options.citationKey]}]: `;
    } else if (options?.citationKey === void 0) {
        out += `[${d.metadata?.["ref"] || d.metadata?.["id"] || index}]: `;
    }
    out += d.text + "\n";
    return out;
};
function augmentWithContext(options) {
    const preface = typeof options?.preface === "undefined" ? CONTEXT_PREFACE : options.preface;
    const itemTemplate = options?.itemTemplate || CONTEXT_ITEM_TEMPLATE;
    return (req, next)=>{
        if (!req.docs?.length) return next(req);
        const userMessage = lastUserMessage(req.messages);
        if (!userMessage) return next(req);
        const contextPartIndex = userMessage?.content.findIndex((p)=>p.metadata?.purpose === "context");
        const contextPart = contextPartIndex >= 0 && userMessage.content[contextPartIndex];
        if (contextPart && !contextPart.metadata?.pending) {
            return next(req);
        }
        let out = `${preface || ""}`;
        req.docs?.forEach((d, i)=>{
            out += itemTemplate(new import_document.Document(d), i, options);
        });
        out += "\n";
        if (contextPartIndex >= 0) {
            userMessage.content[contextPartIndex] = {
                ...contextPart,
                text: out,
                metadata: {
                    purpose: "context"
                }
            };
        } else {
            userMessage.content.push({
                text: out,
                metadata: {
                    purpose: "context"
                }
            });
        }
        return next(req);
    };
}
const DEFAULT_CONSTRAINED_GENERATION_INSTRUCTIONS = (schema)=>`Output should be in JSON format and conform to the following schema:

\`\`\`
${JSON.stringify(schema)}
\`\`\`
`;
function simulateConstrainedGeneration(options) {
    return (req, next)=>{
        let instructions;
        if (req.output?.constrained && req.output?.schema) {
            instructions = (options?.instructionsRenderer ?? DEFAULT_CONSTRAINED_GENERATION_INSTRUCTIONS)(req.output?.schema);
            req = {
                ...req,
                messages: (0, import_formats.injectInstructions)(req.messages, instructions),
                output: {
                    ...req.output,
                    // we're simulating it, so to the underlying model it's unconstrained.
                    constrained: false,
                    format: void 0,
                    contentType: void 0,
                    schema: void 0
                }
            };
        }
        return next(req);
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CONTEXT_PREFACE,
    augmentWithContext,
    downloadRequestMedia,
    simulateConstrainedGeneration,
    simulateSystemPrompt,
    validateSupport
}); //# sourceMappingURL=middleware.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/model.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var model_exports = {};
__export(model_exports, {
    CustomPartSchema: ()=>import_document.CustomPartSchema,
    DataPartSchema: ()=>import_document.DataPartSchema,
    MediaPartSchema: ()=>import_document.MediaPartSchema,
    TextPartSchema: ()=>import_document.TextPartSchema,
    ToolRequestPartSchema: ()=>import_document.ToolRequestPartSchema,
    ToolResponsePartSchema: ()=>import_document.ToolResponsePartSchema,
    defineBackgroundModel: ()=>defineBackgroundModel,
    defineGenerateAction: ()=>import_action.defineGenerateAction,
    defineModel: ()=>defineModel,
    getBasicUsageStats: ()=>getBasicUsageStats,
    modelActionMetadata: ()=>modelActionMetadata,
    modelRef: ()=>modelRef,
    resolveModel: ()=>resolveModel,
    simulateConstrainedGeneration: ()=>import_middleware.simulateConstrainedGeneration
});
module.exports = __toCommonJS(model_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_node_perf_hooks = __turbopack_context__.r("[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var import_model_types = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)");
var import_middleware = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model/middleware.js [app-route] (ecmascript)");
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/action.js [app-route] (ecmascript)");
__reExport(model_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)"), module.exports);
function defineModel(registry, options, runner) {
    const label = options.label || options.name;
    const middleware = getModelMiddleware(options);
    const act = (0, import_core.defineAction)(registry, {
        actionType: "model",
        name: options.name,
        description: label,
        inputSchema: import_model_types.GenerateRequestSchema,
        outputSchema: import_model_types.GenerateResponseSchema,
        metadata: {
            model: {
                label,
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0,
                versions: options.versions,
                supports: options.supports
            }
        },
        use: middleware
    }, (input, ctx)=>{
        const startTimeMs = import_node_perf_hooks.performance.now();
        const secondParam = options.apiVersion === "v2" ? ctx : ctx.streamingRequested ? ctx.sendChunk : void 0;
        return runner(input, secondParam).then((response)=>{
            const timedResponse = {
                ...response,
                latencyMs: import_node_perf_hooks.performance.now() - startTimeMs
            };
            return timedResponse;
        });
    });
    Object.assign(act, {
        __configSchema: options.configSchema || import_core.z.unknown()
    });
    return act;
}
function defineBackgroundModel(registry, options) {
    const label = options.label || options.name;
    const middleware = getModelMiddleware(options);
    const act = (0, import_core.defineBackgroundAction)(registry, {
        actionType: "background-model",
        name: options.name,
        description: label,
        inputSchema: import_model_types.GenerateRequestSchema,
        outputSchema: import_model_types.GenerateResponseSchema,
        metadata: {
            model: {
                label,
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0,
                versions: options.versions,
                supports: options.supports
            }
        },
        use: middleware,
        async start (request) {
            const startTimeMs = import_node_perf_hooks.performance.now();
            const response = await options.start(request);
            Object.assign(response, {
                latencyMs: import_node_perf_hooks.performance.now() - startTimeMs
            });
            return response;
        },
        async check (op) {
            return options.check(op);
        },
        cancel: options.cancel ? async (op)=>{
            if (!options.cancel) {
                throw new import_core.GenkitError({
                    status: "UNIMPLEMENTED",
                    message: "cancel not implemented"
                });
            }
            return options.cancel(op);
        } : void 0
    });
    Object.assign(act, {
        __configSchema: options.configSchema || import_core.z.unknown()
    });
    return act;
}
function getModelMiddleware(options) {
    const middleware = [
        ...options.use || [],
        (0, import_middleware.validateSupport)(options)
    ];
    if (!options?.supports?.context) middleware.push((0, import_middleware.augmentWithContext)());
    const constratedSimulator = (0, import_middleware.simulateConstrainedGeneration)();
    middleware.push((req, next)=>{
        if (!options?.supports?.constrained || options?.supports?.constrained === "none" || options?.supports?.constrained === "no-tools" && (req.tools?.length ?? 0) > 0) {
            return constratedSimulator(req, next);
        }
        return next(req);
    });
    return middleware;
}
function modelActionMetadata({ name, info, configSchema, background }) {
    return {
        actionType: background ? "background-model" : "model",
        name,
        inputJsonSchema: (0, import_schema.toJsonSchema)({
            schema: import_model_types.GenerateRequestSchema
        }),
        outputJsonSchema: background ? (0, import_schema.toJsonSchema)({
            schema: import_core.OperationSchema
        }) : (0, import_schema.toJsonSchema)({
            schema: import_model_types.GenerateResponseSchema
        }),
        metadata: {
            model: {
                ...info,
                customOptions: configSchema ? (0, import_schema.toJsonSchema)({
                    schema: configSchema
                }) : void 0
            }
        }
    };
}
function modelRef(options) {
    const ref = {
        ...options
    };
    ref.withConfig = (cfg)=>{
        return modelRef({
            ...options,
            config: cfg
        });
    };
    ref.withVersion = (version)=>{
        return modelRef({
            ...options,
            version
        });
    };
    return ref;
}
function getBasicUsageStats(input, response) {
    const inputCounts = getPartCounts(input.flatMap((md)=>md.content));
    const outputCounts = getPartCounts(Array.isArray(response) ? response.flatMap((c)=>c.message.content) : response.content);
    return {
        inputCharacters: inputCounts.characters,
        inputImages: inputCounts.images,
        inputVideos: inputCounts.videos,
        inputAudioFiles: inputCounts.audio,
        outputCharacters: outputCounts.characters,
        outputImages: outputCounts.images,
        outputVideos: outputCounts.videos,
        outputAudioFiles: outputCounts.audio
    };
}
function getPartCounts(parts) {
    return parts.reduce((counts, part)=>{
        const isImage = part.media?.contentType?.startsWith("image") || part.media?.url?.startsWith("data:image");
        const isVideo = part.media?.contentType?.startsWith("video") || part.media?.url?.startsWith("data:video");
        const isAudio = part.media?.contentType?.startsWith("audio") || part.media?.url?.startsWith("data:audio");
        return {
            characters: counts.characters + (part.text?.length || 0),
            images: counts.images + (isImage ? 1 : 0),
            videos: counts.videos + (isVideo ? 1 : 0),
            audio: counts.audio + (isAudio ? 1 : 0)
        };
    }, {
        characters: 0,
        images: 0,
        videos: 0,
        audio: 0
    });
}
async function resolveModel(registry, model, options) {
    let out;
    let modelId;
    if (!model) {
        model = await registry.lookupValue("defaultModel", "defaultModel");
    }
    if (!model) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: "Must supply a `model` to `generate()` calls."
        });
    }
    if (typeof model === "string") {
        modelId = model;
        out = {
            modelAction: await lookupModel(registry, model)
        };
    } else if (model.hasOwnProperty("__action")) {
        modelId = model.__action.name;
        out = {
            modelAction: model
        };
    } else {
        const ref = model;
        modelId = ref.name;
        out = {
            modelAction: await lookupModel(registry, ref.name),
            config: {
                ...ref.config
            },
            version: ref.version
        };
    }
    if (!out.modelAction) {
        throw new import_core.GenkitError({
            status: "NOT_FOUND",
            message: `Model '${modelId}' not found`
        });
    }
    if (options?.warnDeprecated && out.modelAction.__action.metadata?.model?.stage === "deprecated") {
        import_logging.logger.warn(`Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`);
    }
    return out;
}
async function lookupModel(registry, model) {
    return await registry.lookupAction(`/model/${model}`) || await registry.lookupAction(`/background-model/${model}`);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CustomPartSchema,
    DataPartSchema,
    MediaPartSchema,
    TextPartSchema,
    ToolRequestPartSchema,
    ToolResponsePartSchema,
    defineBackgroundModel,
    defineGenerateAction,
    defineModel,
    getBasicUsageStats,
    modelActionMetadata,
    modelRef,
    resolveModel,
    simulateConstrainedGeneration,
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)")
}); //# sourceMappingURL=model.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/resource.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var resource_exports = {};
__export(resource_exports, {
    ResourceInputSchema: ()=>ResourceInputSchema,
    ResourceOutputSchema: ()=>ResourceOutputSchema,
    defineResource: ()=>defineResource,
    dynamicResource: ()=>dynamicResource,
    findMatchingResource: ()=>findMatchingResource,
    isDynamicResourceAction: ()=>isDynamicResourceAction
});
module.exports = __toCommonJS(resource_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_uri_templates = __toESM(__turbopack_context__.r("[project]/node_modules/uri-templates/uri-templates.js [app-route] (ecmascript)"));
var import_model_types = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)");
const ResourceInputSchema = import_core.z.object({
    uri: import_core.z.string()
});
const ResourceOutputSchema = import_core.z.object({
    content: import_core.z.array(import_model_types.PartSchema)
});
function defineResource(registry, opts, fn) {
    const action = dynamicResource(opts, fn).attach(registry);
    delete action.__action.metadata?.dynamic;
    action.matches = createMatcher(opts.uri, opts.template);
    registry.registerAction("resource", action);
    return action;
}
async function findMatchingResource(registry, input) {
    for (const actKeys of Object.keys(await registry.listResolvableActions())){
        if (actKeys.startsWith("/resource/")) {
            const resource = await registry.lookupAction(actKeys);
            if (resource.matches(input)) {
                return resource;
            }
        }
    }
    return void 0;
}
function isDynamicResourceAction(t) {
    return ((0, import_core.isDetachedAction)(t) || (0, import_core.isAction)(t)) && t.__action.metadata?.type === "resource" && !!t.__action.metadata?.dynamic;
}
function dynamicResource(opts, fn) {
    const uri = opts.uri ?? opts.template;
    if (!uri) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: `must specify either url or template options`
        });
    }
    const matcher = createMatcher(opts.uri, opts.template);
    const action = (0, import_core.detachedAction)({
        actionType: "resource",
        name: opts.name ?? uri,
        description: opts.description,
        inputSchema: ResourceInputSchema,
        outputSchema: ResourceOutputSchema,
        metadata: {
            resource: {
                uri: opts.uri,
                template: opts.template
            },
            ...opts.metadata,
            type: "resource",
            dynamic: true
        }
    }, async (input, ctx)=>{
        const templateMatch = matcher(input);
        if (!templateMatch) {
            throw new import_core.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `input ${input} did not match template ${uri}`
            });
        }
        const parts = await fn(input, ctx);
        parts.content.map((p)=>{
            if (!p.metadata) {
                p.metadata = {};
            }
            if (p.metadata?.resource) {
                if (!p.metadata.resource.parent) {
                    p.metadata.resource.parent = {
                        uri: input.uri
                    };
                    if (opts.template) {
                        p.metadata.resource.parent.template = opts.template;
                    }
                }
            } else {
                p.metadata.resource = {
                    uri: input.uri
                };
                if (opts.template) {
                    p.metadata.resource.template = opts.template;
                }
            }
            return p;
        });
        return parts;
    });
    action.matches = matcher;
    return {
        __action: {
            ...action.__action,
            metadata: {
                ...action.__action.metadata,
                type: "resource"
            }
        },
        attach (registry) {
            const bound = action.attach(registry);
            bound.matches = matcher;
            return bound;
        },
        matches: matcher
    };
}
function createMatcher(uriOpt, templateOpt) {
    if (uriOpt) {
        return (input)=>input.uri === uriOpt;
    }
    if (templateOpt) {
        const template = (0, import_uri_templates.default)(templateOpt);
        return (input)=>template.fromUri(input.uri) !== void 0;
    }
    throw new import_core.GenkitError({
        status: "INVALID_ARGUMENT",
        message: "must specify either url or template options"
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ResourceInputSchema,
    ResourceOutputSchema,
    defineResource,
    dynamicResource,
    findMatchingResource,
    isDynamicResourceAction
}); //# sourceMappingURL=resource.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/message.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var message_exports = {};
__export(message_exports, {
    Message: ()=>Message
});
module.exports = __toCommonJS(message_exports);
var import_extract = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/extract.js [app-route] (ecmascript)");
class Message {
    role;
    content;
    metadata;
    parser;
    static parseData(lenientMessage, defaultRole = "user") {
        if (typeof lenientMessage === "string") {
            return {
                role: defaultRole,
                content: [
                    {
                        text: lenientMessage
                    }
                ]
            };
        }
        return {
            ...lenientMessage,
            content: Message.parseContent(lenientMessage.content)
        };
    }
    static parse(lenientMessage) {
        return new Message(Message.parseData(lenientMessage));
    }
    static parseContent(lenientPart) {
        if (typeof lenientPart === "string") {
            return [
                {
                    text: lenientPart
                }
            ];
        } else if (Array.isArray(lenientPart)) {
            return lenientPart.map((p)=>typeof p === "string" ? {
                    text: p
                } : p);
        } else {
            return [
                lenientPart
            ];
        }
    }
    constructor(message, options){
        this.role = message.role;
        this.content = message.content;
        this.metadata = message.metadata;
        this.parser = options?.parser;
    }
    /**
   * Attempts to parse the content of the message according to the supplied
   * output parser. Without a parser, returns `data` contained in the message or
   * tries to parse JSON from the text of the message.
   *
   * @returns The structured output contained in the message.
   */ get output() {
        return this.parser?.(this) || this.data || (0, import_extract.extractJson)(this.text);
    }
    toolResponseParts() {
        const res = this.content.filter((part)=>!!part.toolResponse);
        return res;
    }
    /**
   * Concatenates all `text` parts present in the message with no delimiter.
   * @returns A string of all concatenated text parts.
   */ get text() {
        return this.content.map((part)=>part.text || "").join("");
    }
    /**
   * Concatenates all `reasoning` parts present in the message with no delimiter.
   * @returns A string of all concatenated reasoning parts.
   */ get reasoning() {
        return this.content.map((part)=>part.reasoning || "").join("");
    }
    /**
   * Returns the first media part detected in the message. Useful for extracting
   * (for example) an image from a generation expected to create one.
   * @returns The first detected `media` part in the message.
   */ get media() {
        return this.content.find((part)=>part.media)?.media || null;
    }
    /**
   * Returns the first detected `data` part of a message.
   * @returns The first `data` part detected in the message (if any).
   */ get data() {
        return this.content.find((part)=>part.data)?.data;
    }
    /**
   * Returns all tool request found in this message.
   * @returns Array of all tool request found in this message.
   */ get toolRequests() {
        return this.content.filter((part)=>!!part.toolRequest);
    }
    /**
   * Returns all tool requests annotated with interrupt metadata.
   * @returns Array of all interrupt tool requests.
   */ get interrupts() {
        return this.toolRequests.filter((t)=>!!t.metadata?.interrupt);
    }
    /**
   * Converts the Message to a plain JS object.
   * @returns Plain JS object representing the data contained in the message.
   */ toJSON() {
        const out = {
            role: this.role,
            content: [
                ...this.content
            ]
        };
        if (this.metadata) out.metadata = this.metadata;
        return out;
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Message
}); //# sourceMappingURL=message.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/chat.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var chat_exports = {};
__export(chat_exports, {
    Chat: ()=>Chat,
    MAIN_THREAD: ()=>MAIN_THREAD,
    SESSION_ID_ATTR: ()=>SESSION_ID_ATTR,
    THREAD_NAME_ATTR: ()=>THREAD_NAME_ATTR
});
module.exports = __toCommonJS(chat_exports);
var import_async = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async.js [app-route] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-route] (ecmascript)");
var import_index = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/index.js [app-route] (ecmascript)");
var import_session = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/session.js [app-route] (ecmascript)");
const MAIN_THREAD = "main";
const SESSION_ID_ATTR = `${import_tracing.ATTR_PREFIX}:sessionId`;
const THREAD_NAME_ATTR = `${import_tracing.ATTR_PREFIX}:threadName`;
class Chat {
    constructor(session, requestBase, options){
        this.session = session;
        this.sessionId = options.id;
        this.threadName = options.thread;
        this.requestBase = requestBase?.then((rb)=>{
            const requestBase2 = {
                ...rb
            };
            if (requestBase2 && requestBase2["prompt"]) {
                const basePrompt = requestBase2["prompt"];
                let promptMessage;
                if (typeof basePrompt === "string") {
                    promptMessage = {
                        role: "user",
                        content: [
                            {
                                text: basePrompt
                            }
                        ]
                    };
                } else if (Array.isArray(basePrompt)) {
                    promptMessage = {
                        role: "user",
                        content: basePrompt
                    };
                } else {
                    promptMessage = {
                        role: "user",
                        content: [
                            basePrompt
                        ]
                    };
                }
                requestBase2.messages = [
                    ...requestBase2.messages ?? [],
                    promptMessage
                ];
            }
            if (hasPreamble(requestBase2.messages)) {
                requestBase2.messages = [
                    // if request base contains a preamble, always put it first
                    ...getPreamble(requestBase2.messages) ?? [],
                    // strip out the preamble from history
                    ...stripPreamble(options.messages) ?? [],
                    // add whatever non-preamble remains from request
                    ...stripPreamble(requestBase2.messages) ?? []
                ];
            } else {
                requestBase2.messages = [
                    ...options.messages ?? [],
                    ...requestBase2.messages ?? []
                ];
            }
            this._messages = requestBase2.messages;
            return requestBase2;
        });
        this._messages = options.messages;
    }
    requestBase;
    sessionId;
    _messages;
    threadName;
    async send(options) {
        return (0, import_session.runWithSession)(this.session.registry, this.session, ()=>(0, import_tracing.runInNewSpan)(this.session.registry, {
                metadata: {
                    name: "send"
                },
                labels: {
                    [import_tracing.SPAN_TYPE_ATTR]: "helper",
                    [SESSION_ID_ATTR]: this.session.id,
                    [THREAD_NAME_ATTR]: this.threadName
                }
            }, async (metadata)=>{
                const resolvedOptions = resolveSendOptions(options);
                let streamingCallback = void 0;
                if (resolvedOptions.onChunk || resolvedOptions.streamingCallback) {
                    streamingCallback = resolvedOptions.onChunk ?? resolvedOptions.streamingCallback;
                }
                const request = {
                    ...await this.requestBase,
                    messages: this.messages,
                    ...resolvedOptions
                };
                metadata.input = resolvedOptions;
                const response = await (0, import_index.generate)(this.session.registry, {
                    ...request,
                    onChunk: streamingCallback
                });
                this.requestBase = Promise.resolve({
                    ...await this.requestBase,
                    // these things may get changed by tools calling within generate.
                    tools: response?.request?.tools?.map((td)=>td.name),
                    toolChoice: response?.request?.toolChoice,
                    config: response?.request?.config
                });
                await this.updateMessages(response.messages);
                metadata.output = JSON.stringify(response);
                return response;
            }));
    }
    sendStream(options) {
        const channel = new import_async.Channel();
        const resolvedOptions = resolveSendOptions(options);
        const sent = this.send({
            ...resolvedOptions,
            onChunk: (chunk)=>channel.send(chunk)
        });
        sent.then(()=>channel.close(), (err)=>channel.error(err));
        return {
            response: sent,
            stream: channel
        };
    }
    get messages() {
        return this._messages ?? [];
    }
    async updateMessages(messages) {
        this._messages = messages;
        await this.session.updateMessages(this.threadName, messages);
    }
}
function hasPreamble(msgs) {
    return !!msgs?.find((m)=>m.metadata?.preamble);
}
function getPreamble(msgs) {
    return msgs?.filter((m)=>m.metadata?.preamble);
}
function stripPreamble(msgs) {
    return msgs?.filter((m)=>!m.metadata?.preamble);
}
function resolveSendOptions(options) {
    let resolvedOptions;
    if (typeof options === "string") {
        resolvedOptions = {
            prompt: options
        };
    } else if (Array.isArray(options)) {
        resolvedOptions = {
            prompt: options
        };
    } else {
        resolvedOptions = options;
    }
    return resolvedOptions;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Chat,
    MAIN_THREAD,
    SESSION_ID_ATTR,
    THREAD_NAME_ATTR
}); //# sourceMappingURL=chat.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/session.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var session_exports = {};
__export(session_exports, {
    Session: ()=>Session,
    SessionError: ()=>SessionError,
    getCurrentSession: ()=>getCurrentSession,
    inMemorySessionStore: ()=>inMemorySessionStore,
    runWithSession: ()=>runWithSession
});
module.exports = __toCommonJS(session_exports);
var import_uuid = __turbopack_context__.r("[project]/node_modules/uuid/dist/esm-node/index.js [app-route] (ecmascript)");
var import_chat = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/chat.js [app-route] (ecmascript)");
var import_index = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/index.js [app-route] (ecmascript)");
class Session {
    constructor(registry, options){
        this.registry = registry;
        this.id = options?.id ?? (0, import_uuid.v4)();
        this.sessionData = options?.sessionData ?? {
            id: this.id
        };
        if (!this.sessionData) {
            this.sessionData = {
                id: this.id
            };
        }
        if (!this.sessionData.threads) {
            this.sessionData.threads = {};
        }
        this.store = options?.store ?? new InMemorySessionStore();
    }
    id;
    sessionData;
    store;
    get state() {
        return this.sessionData.state;
    }
    /**
   * Update session state data.
   */ async updateState(data) {
        let sessionData = this.sessionData;
        if (!sessionData) {
            sessionData = {};
        }
        sessionData.state = data;
        this.sessionData = sessionData;
        await this.store.save(this.id, sessionData);
    }
    /**
   * Update messages for a given thread.
   */ async updateMessages(thread, messages) {
        let sessionData = this.sessionData;
        if (!sessionData) {
            sessionData = {};
        }
        if (!sessionData.threads) {
            sessionData.threads = {};
        }
        sessionData.threads[thread] = messages.map((m)=>m.toJSON ? m.toJSON() : m);
        this.sessionData = sessionData;
        await this.store.save(this.id, sessionData);
    }
    chat(optionsOrPreambleOrThreadName, maybeOptionsOrPreamble, maybeOptions) {
        return runWithSession(this.registry, this, ()=>{
            let options;
            let threadName = import_chat.MAIN_THREAD;
            let preamble;
            if (optionsOrPreambleOrThreadName) {
                if (typeof optionsOrPreambleOrThreadName === "string") {
                    threadName = optionsOrPreambleOrThreadName;
                } else if ((0, import_index.isExecutablePrompt)(optionsOrPreambleOrThreadName)) {
                    preamble = optionsOrPreambleOrThreadName;
                } else {
                    options = optionsOrPreambleOrThreadName;
                }
            }
            if (maybeOptionsOrPreamble) {
                if ((0, import_index.isExecutablePrompt)(maybeOptionsOrPreamble)) {
                    preamble = maybeOptionsOrPreamble;
                } else {
                    options = maybeOptionsOrPreamble;
                }
            }
            if (maybeOptions) {
                options = maybeOptions;
            }
            let requestBase;
            if (preamble) {
                const renderOptions = options;
                requestBase = preamble.render(renderOptions?.input, renderOptions).then((rb)=>{
                    return {
                        ...rb,
                        messages: (0, import_index.tagAsPreamble)(rb?.messages)
                    };
                });
            } else {
                const baseOptions = {
                    ...options
                };
                const messages = [];
                if (baseOptions.system) {
                    messages.push({
                        role: "system",
                        content: import_index.Message.parseContent(baseOptions.system)
                    });
                }
                delete baseOptions.system;
                if (baseOptions.messages) {
                    messages.push(...baseOptions.messages);
                }
                baseOptions.messages = (0, import_index.tagAsPreamble)(messages);
                requestBase = Promise.resolve(baseOptions);
            }
            return new import_chat.Chat(this, requestBase, {
                thread: threadName,
                id: this.id,
                messages: (this.sessionData?.threads && this.sessionData?.threads[threadName]) ?? []
            });
        });
    }
    /**
   * Executes provided function within this session context allowing calling
   * `ai.currentSession().state`
   */ run(fn) {
        return runWithSession(this.registry, this, fn);
    }
    toJSON() {
        return this.sessionData;
    }
}
const sessionAlsKey = "ai.session";
function runWithSession(registry, session, fn) {
    return registry.asyncStore.run(sessionAlsKey, session, fn);
}
function getCurrentSession(registry) {
    return registry.asyncStore.getStore(sessionAlsKey);
}
class SessionError extends Error {
    constructor(msg){
        super(msg);
    }
}
function inMemorySessionStore() {
    return new InMemorySessionStore();
}
class InMemorySessionStore {
    data = {};
    async get(sessionId) {
        return this.data[sessionId];
    }
    async save(sessionId, sessionData) {
        this.data[sessionId] = sessionData;
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Session,
    SessionError,
    getCurrentSession,
    inMemorySessionStore,
    runWithSession
}); //# sourceMappingURL=session.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/prompt.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var prompt_exports = {};
__export(prompt_exports, {
    defineHelper: ()=>defineHelper,
    definePartial: ()=>definePartial,
    definePrompt: ()=>definePrompt,
    isExecutablePrompt: ()=>isExecutablePrompt,
    isPromptAction: ()=>isPromptAction,
    loadPromptFolder: ()=>loadPromptFolder,
    loadPromptFolderRecursively: ()=>loadPromptFolderRecursively,
    prompt: ()=>prompt
});
module.exports = __toCommonJS(prompt_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_async = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async.js [app-route] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-route] (ecmascript)");
var import_fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
var import_path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
var import_generate = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate.js [app-route] (ecmascript)");
var import_message = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/message.js [app-route] (ecmascript)");
var import_model = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model.js [app-route] (ecmascript)");
var import_session = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/session.js [app-route] (ecmascript)");
function isPromptAction(action) {
    return action.__action.metadata?.type === "prompt";
}
function definePrompt(registry, options) {
    return definePromptAsync(registry, `${options.name}${options.variant ? `.${options.variant}` : ""}`, Promise.resolve(options), options.metadata);
}
function definePromptAsync(registry, name, optionsPromise, metadata) {
    const promptCache = {};
    const renderOptionsFn = async (input, renderOptions)=>{
        return await (0, import_tracing.runInNewSpan)(registry, {
            metadata: {
                name: "render",
                input
            },
            labels: {
                [import_tracing.SPAN_TYPE_ATTR]: "promptTemplate"
            }
        }, async (metadata2)=>{
            const messages = [];
            renderOptions = {
                ...renderOptions
            };
            const session = (0, import_session.getCurrentSession)(registry);
            const resolvedOptions = await optionsPromise;
            await renderSystemPrompt(registry, session, input, messages, resolvedOptions, promptCache, renderOptions);
            await renderMessages(registry, session, input, messages, resolvedOptions, renderOptions, promptCache);
            await renderUserPrompt(registry, session, input, messages, resolvedOptions, promptCache, renderOptions);
            let docs;
            if (typeof resolvedOptions.docs === "function") {
                docs = await resolvedOptions.docs(input, {
                    state: session?.state,
                    context: renderOptions?.context || (0, import_core.getContext)(registry) || {}
                });
            } else {
                docs = resolvedOptions.docs;
            }
            const opts = (0, import_core.stripUndefinedProps)({
                model: resolvedOptions.model,
                maxTurns: resolvedOptions.maxTurns,
                messages,
                docs,
                tools: resolvedOptions.tools,
                returnToolRequests: resolvedOptions.returnToolRequests,
                toolChoice: resolvedOptions.toolChoice,
                context: resolvedOptions.context,
                output: resolvedOptions.output,
                use: resolvedOptions.use,
                ...(0, import_core.stripUndefinedProps)(renderOptions),
                config: {
                    ...resolvedOptions?.config,
                    ...renderOptions?.config
                }
            });
            if (Object.keys(opts.config).length === 0 && !renderOptions?.config) {
                delete opts.config;
            }
            metadata2.output = opts;
            return opts;
        });
    };
    const rendererActionConfig = (0, import_async.lazy)(()=>optionsPromise.then((options)=>{
            const metadata2 = promptMetadata(options);
            return {
                name: `${options.name}${options.variant ? `.${options.variant}` : ""}`,
                inputJsonSchema: options.input?.jsonSchema,
                inputSchema: options.input?.schema,
                description: options.description,
                actionType: "prompt",
                metadata: metadata2,
                fn: async (input)=>{
                    return (0, import_generate.toGenerateRequest)(registry, await renderOptionsFn(input, void 0));
                }
            };
        }));
    const rendererAction = (0, import_core.defineActionAsync)(registry, "prompt", name, rendererActionConfig, (action)=>{
        action.__executablePrompt = executablePrompt;
    });
    const executablePromptActionConfig = (0, import_async.lazy)(()=>optionsPromise.then((options)=>{
            const metadata2 = promptMetadata(options);
            return {
                name: `${options.name}${options.variant ? `.${options.variant}` : ""}`,
                inputJsonSchema: options.input?.jsonSchema,
                inputSchema: options.input?.schema,
                outputSchema: import_model.GenerateActionOptionsSchema,
                description: options.description,
                actionType: "executable-prompt",
                metadata: metadata2,
                fn: async (input)=>{
                    return await (0, import_generate.toGenerateActionOptions)(registry, await renderOptionsFn(input, void 0));
                }
            };
        }));
    (0, import_core.defineActionAsync)(registry, "executable-prompt", name, executablePromptActionConfig, (action)=>{
        action.__executablePrompt = executablePrompt;
    });
    const executablePrompt = wrapInExecutablePrompt({
        registry,
        name,
        renderOptionsFn,
        rendererAction,
        metadata
    });
    return executablePrompt;
}
function promptMetadata(options) {
    const metadata = {
        ...options.metadata,
        prompt: {
            ...options.metadata?.prompt,
            config: options.config,
            input: {
                schema: options.input ? (0, import_schema.toJsonSchema)(options.input) : void 0
            },
            name: options.name.includes(".") ? options.name.split(".")[0] : options.name,
            model: modelName(options.model)
        },
        type: "prompt"
    };
    if (options.variant) {
        metadata.prompt.variant = options.variant;
    }
    return metadata;
}
function wrapInExecutablePrompt(wrapOpts) {
    const executablePrompt = async (input, opts)=>{
        return await (0, import_tracing.runInNewSpan)(wrapOpts.registry, {
            metadata: {
                name: (await wrapOpts.rendererAction).__action.name,
                input
            },
            labels: {
                [import_tracing.SPAN_TYPE_ATTR]: "dotprompt"
            }
        }, async (metadata)=>{
            const output = await (0, import_generate.generate)(wrapOpts.registry, {
                ...await wrapOpts.renderOptionsFn(input, opts)
            });
            metadata.output = output;
            return output;
        });
    };
    executablePrompt.ref = {
        name: wrapOpts.name,
        metadata: wrapOpts.metadata
    };
    executablePrompt.render = async (input, opts)=>{
        return {
            ...await wrapOpts.renderOptionsFn(input, opts)
        };
    };
    executablePrompt.stream = (input, opts)=>{
        return (0, import_generate.generateStream)(wrapOpts.registry, wrapOpts.renderOptionsFn(input, opts));
    };
    executablePrompt.asTool = async ()=>{
        return await wrapOpts.rendererAction;
    };
    return executablePrompt;
}
async function renderSystemPrompt(registry, session, input, messages, options, promptCache, renderOptions) {
    if (typeof options.system === "function") {
        messages.push({
            role: "system",
            content: normalizeParts(await options.system(input, {
                state: session?.state,
                context: renderOptions?.context || (0, import_core.getContext)(registry) || {}
            }))
        });
    } else if (typeof options.system === "string") {
        if (!promptCache.system) {
            promptCache.system = await registry.dotprompt.compile(options.system);
        }
        messages.push({
            role: "system",
            content: await renderDotpromptToParts(registry, promptCache.system, input, session, options, renderOptions)
        });
    } else if (options.system) {
        messages.push({
            role: "system",
            content: normalizeParts(options.system)
        });
    }
}
async function renderMessages(registry, session, input, messages, options, renderOptions, promptCache) {
    if (options.messages) {
        if (typeof options.messages === "function") {
            messages.push(...await options.messages(input, {
                state: session?.state,
                context: renderOptions?.context || (0, import_core.getContext)(registry) || {},
                history: renderOptions?.messages
            }));
        } else if (typeof options.messages === "string") {
            if (!promptCache.messages) {
                promptCache.messages = await registry.dotprompt.compile(options.messages);
            }
            const rendered = await promptCache.messages({
                input,
                context: {
                    ...renderOptions?.context || (0, import_core.getContext)(registry),
                    state: session?.state
                },
                messages: renderOptions?.messages?.map((m)=>import_message.Message.parseData(m))
            });
            messages.push(...rendered.messages);
        } else {
            messages.push(...options.messages);
        }
    } else {
        if (renderOptions.messages) {
            messages.push(...renderOptions.messages);
        }
    }
    if (renderOptions?.messages) {
        delete renderOptions.messages;
    }
}
async function renderUserPrompt(registry, session, input, messages, options, promptCache, renderOptions) {
    if (typeof options.prompt === "function") {
        messages.push({
            role: "user",
            content: normalizeParts(await options.prompt(input, {
                state: session?.state,
                context: renderOptions?.context || (0, import_core.getContext)(registry) || {}
            }))
        });
    } else if (typeof options.prompt === "string") {
        if (!promptCache.userPrompt) {
            promptCache.userPrompt = await registry.dotprompt.compile(options.prompt);
        }
        messages.push({
            role: "user",
            content: await renderDotpromptToParts(registry, promptCache.userPrompt, input, session, options, renderOptions)
        });
    } else if (options.prompt) {
        messages.push({
            role: "user",
            content: normalizeParts(options.prompt)
        });
    }
}
function modelName(modelArg) {
    if (modelArg === void 0) {
        return void 0;
    }
    if (typeof modelArg === "string") {
        return modelArg;
    }
    if (modelArg.name) {
        return modelArg.name;
    }
    return modelArg.__action.name;
}
function normalizeParts(parts) {
    if (Array.isArray(parts)) return parts;
    if (typeof parts === "string") {
        return [
            {
                text: parts
            }
        ];
    }
    return [
        parts
    ];
}
async function renderDotpromptToParts(registry, promptFn, input, session, options, renderOptions) {
    const renderred = await promptFn({
        input,
        context: {
            ...renderOptions?.context || (0, import_core.getContext)(registry),
            state: session?.state
        }
    });
    if (renderred.messages.length !== 1) {
        throw new Error("parts tempate must produce only one message");
    }
    return renderred.messages[0].content;
}
function isExecutablePrompt(obj) {
    return !!obj?.render && !!obj?.asTool && !!obj?.stream;
}
function loadPromptFolder(registry, dir = "./prompts", ns) {
    const promptsPath = (0, import_path.resolve)(dir);
    if ((0, import_fs.existsSync)(promptsPath)) {
        loadPromptFolderRecursively(registry, dir, ns, "");
    }
}
function loadPromptFolderRecursively(registry, dir, ns, subDir) {
    const promptsPath = (0, import_path.resolve)(dir);
    const dirEnts = (0, import_fs.readdirSync)((0, import_path.join)(promptsPath, subDir), {
        withFileTypes: true
    });
    for (const dirEnt of dirEnts){
        const parentPath = (0, import_path.join)(promptsPath, subDir);
        const fileName = dirEnt.name;
        if (dirEnt.isFile() && fileName.endsWith(".prompt")) {
            if (fileName.startsWith("_")) {
                const partialName = fileName.substring(1, fileName.length - 7);
                definePartial(registry, partialName, (0, import_fs.readFileSync)((0, import_path.join)(parentPath, fileName), {
                    encoding: "utf8"
                }));
                import_logging.logger.debug(`Registered Dotprompt partial "${partialName}" from "${(0, import_path.join)(parentPath, fileName)}"`);
            } else {
                loadPrompt(registry, promptsPath, fileName, subDir ? `${subDir}/` : "", ns);
            }
        } else if (dirEnt.isDirectory()) {
            loadPromptFolderRecursively(registry, dir, ns, (0, import_path.join)(subDir, fileName));
        }
    }
}
function definePartial(registry, name, source) {
    registry.dotprompt.definePartial(name, source);
}
function defineHelper(registry, name, fn) {
    registry.dotprompt.defineHelper(name, fn);
}
function loadPrompt(registry, path, filename, prefix = "", ns = "dotprompt") {
    let name = `${prefix ?? ""}${(0, import_path.basename)(filename, ".prompt")}`;
    let variant = null;
    if (name.includes(".")) {
        const parts = name.split(".");
        name = parts[0];
        variant = parts[1];
    }
    const source = (0, import_fs.readFileSync)((0, import_path.join)(path, prefix ?? "", filename), "utf8");
    const parsedPrompt = registry.dotprompt.parse(source);
    definePromptAsync(registry, registryDefinitionKey(name, variant ?? void 0, ns), // We use a lazy promise here because we only want prompt loaded when it's first used.
    // This is important because otherwise the loading may happen before the user has configured
    // all the schemas, etc., which will result in dotprompt.renderMetadata errors.
    (0, import_async.lazy)(async ()=>{
        const promptMetadata2 = await registry.dotprompt.renderMetadata(parsedPrompt);
        if (variant) {
            promptMetadata2.variant = variant;
        }
        if (promptMetadata2.output?.schema?.description === null) {
            delete promptMetadata2.output.schema.description;
        }
        if (promptMetadata2.input?.schema?.description === null) {
            delete promptMetadata2.input.schema.description;
        }
        return {
            name: registryDefinitionKey(name, variant ?? void 0, ns),
            model: promptMetadata2.model,
            config: promptMetadata2.config,
            tools: promptMetadata2.tools,
            description: promptMetadata2.description,
            output: {
                jsonSchema: promptMetadata2.output?.schema,
                format: promptMetadata2.output?.format
            },
            input: {
                jsonSchema: promptMetadata2.input?.schema
            },
            metadata: {
                ...promptMetadata2.metadata,
                type: "prompt",
                prompt: {
                    ...promptMetadata2,
                    template: parsedPrompt.template
                }
            },
            maxTurns: promptMetadata2.raw?.["maxTurns"],
            toolChoice: promptMetadata2.raw?.["toolChoice"],
            returnToolRequests: promptMetadata2.raw?.["returnToolRequests"],
            messages: parsedPrompt.template
        };
    }));
}
async function prompt(registry, name, options) {
    return await lookupPrompt(registry, name, options?.variant);
}
function registryLookupKey(name, variant, ns) {
    return `/prompt/${registryDefinitionKey(name, variant, ns)}`;
}
async function lookupPrompt(registry, name, variant) {
    const registryPrompt = await registry.lookupAction(registryLookupKey(name, variant));
    if (registryPrompt) {
        return registryPrompt.__executablePrompt;
    }
    throw new import_core.GenkitError({
        status: "NOT_FOUND",
        message: `Prompt ${name + (variant ? ` (variant ${variant})` : "")} not found`
    });
}
function registryDefinitionKey(name, variant, ns) {
    return `${ns ? `${ns}/` : ""}${name}${variant ? `.${variant}` : ""}`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    defineHelper,
    definePartial,
    definePrompt,
    isExecutablePrompt,
    isPromptAction,
    loadPromptFolder,
    loadPromptFolderRecursively,
    prompt
}); //# sourceMappingURL=prompt.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/tool.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var tool_exports = {};
__export(tool_exports, {
    ToolInterruptError: ()=>ToolInterruptError,
    asTool: ()=>asTool,
    defineInterrupt: ()=>defineInterrupt,
    defineTool: ()=>defineTool,
    dynamicTool: ()=>dynamicTool,
    isDynamicTool: ()=>isDynamicTool,
    isToolRequest: ()=>isToolRequest,
    isToolResponse: ()=>isToolResponse,
    lookupToolByName: ()=>lookupToolByName,
    resolveTools: ()=>resolveTools,
    toToolDefinition: ()=>toToolDefinition
});
module.exports = __toCommonJS(tool_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-route] (ecmascript)");
var import_prompt = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/prompt.js [app-route] (ecmascript)");
function asTool(registry, action) {
    if (action.__action?.metadata?.type === "tool") {
        return action;
    }
    const fn = (input)=>{
        (0, import_tracing.setCustomMetadataAttributes)(registry, {
            subtype: "tool"
        });
        return action(input);
    };
    fn.__action = {
        ...action.__action,
        metadata: {
            ...action.__action.metadata,
            type: "tool"
        }
    };
    return fn;
}
async function resolveTools(registry, tools) {
    if (!tools || tools.length === 0) {
        return [];
    }
    return await Promise.all(tools.map(async (ref)=>{
        if (typeof ref === "string") {
            return await lookupToolByName(registry, ref);
        } else if ((0, import_core.isAction)(ref)) {
            return asTool(registry, ref);
        } else if ((0, import_prompt.isExecutablePrompt)(ref)) {
            return await ref.asTool();
        } else if (ref.name) {
            return await lookupToolByName(registry, ref.metadata?.originalName || ref.name);
        }
        throw new Error("Tools must be strings, tool definitions, or actions.");
    }));
}
async function lookupToolByName(registry, name) {
    const tool = await registry.lookupAction(name) || await registry.lookupAction(`/tool/${name}`) || await registry.lookupAction(`/prompt/${name}`);
    if (!tool) {
        throw new Error(`Tool ${name} not found`);
    }
    return tool;
}
function toToolDefinition(tool) {
    const originalName = tool.__action.name;
    let name = originalName;
    if (originalName.includes("/")) {
        name = originalName.substring(originalName.lastIndexOf("/") + 1);
    }
    const out = {
        name,
        description: tool.__action.description || "",
        outputSchema: (0, import_schema.toJsonSchema)({
            schema: tool.__action.outputSchema ?? import_core.z.void(),
            jsonSchema: tool.__action.outputJsonSchema
        }),
        inputSchema: (0, import_schema.toJsonSchema)({
            schema: tool.__action.inputSchema ?? import_core.z.void(),
            jsonSchema: tool.__action.inputJsonSchema
        })
    };
    if (originalName !== name) {
        out.metadata = {
            originalName
        };
    }
    return out;
}
function defineTool(registry, config, fn) {
    const a = (0, import_core.defineAction)(registry, {
        ...config,
        actionType: "tool",
        metadata: {
            ...config.metadata || {},
            type: "tool"
        }
    }, (i, runOptions)=>{
        return fn(i, {
            ...runOptions,
            context: {
                ...runOptions.context
            },
            interrupt: interruptTool(registry)
        });
    });
    implementTool(a, config, registry);
    return a;
}
function implementTool(a, config, registry) {
    a.respond = (interrupt, responseData, options)=>{
        if (registry) {
            (0, import_core.assertUnstable)(registry, "beta", "The 'tool.reply' method is part of the 'interrupts' beta feature.");
        }
        (0, import_schema.parseSchema)(responseData, {
            jsonSchema: config.outputJsonSchema,
            schema: config.outputSchema
        });
        return {
            toolResponse: (0, import_core.stripUndefinedProps)({
                name: interrupt.toolRequest.name,
                ref: interrupt.toolRequest.ref,
                output: responseData
            }),
            metadata: {
                interruptResponse: options?.metadata || true
            }
        };
    };
    a.restart = (interrupt, resumedMetadata, options)=>{
        if (registry) {
            (0, import_core.assertUnstable)(registry, "beta", "The 'tool.restart' method is part of the 'interrupts' beta feature.");
        }
        let replaceInput = options?.replaceInput;
        if (replaceInput) {
            replaceInput = (0, import_schema.parseSchema)(replaceInput, {
                schema: config.inputSchema,
                jsonSchema: config.inputJsonSchema
            });
        }
        return {
            toolRequest: (0, import_core.stripUndefinedProps)({
                name: interrupt.toolRequest.name,
                ref: interrupt.toolRequest.ref,
                input: replaceInput || interrupt.toolRequest.input
            }),
            metadata: (0, import_core.stripUndefinedProps)({
                ...interrupt.metadata,
                resumed: resumedMetadata || true,
                // annotate the original input if replacing it
                replacedInput: replaceInput ? interrupt.toolRequest.input : void 0
            })
        };
    };
}
function isToolRequest(part) {
    return !!part.toolRequest;
}
function isToolResponse(part) {
    return !!part.toolResponse;
}
function isDynamicTool(t) {
    return ((0, import_core.isDetachedAction)(t) || (0, import_core.isAction)(t)) && t.__action.metadata?.type === "tool" && t.__action.metadata?.dynamic;
}
function defineInterrupt(registry, config) {
    const { requestMetadata, ...toolConfig } = config;
    return defineTool(registry, toolConfig, async (input, { interrupt })=>{
        if (!config.requestMetadata) interrupt();
        else if (typeof config.requestMetadata === "object") interrupt(config.requestMetadata);
        else interrupt(await Promise.resolve(config.requestMetadata(input)));
    });
}
class ToolInterruptError extends Error {
    constructor(metadata){
        super();
        this.metadata = metadata;
        this.name = "ToolInterruptError";
    }
}
function interruptTool(registry) {
    return (metadata)=>{
        (0, import_core.assertUnstable)(registry, "beta", "Tool interrupts are a beta feature.");
        throw new ToolInterruptError(metadata);
    };
}
function dynamicTool(config, fn) {
    const a = (0, import_core.detachedAction)({
        ...config,
        actionType: "tool",
        metadata: {
            ...config.metadata || {},
            type: "tool",
            dynamic: true
        }
    }, (i, runOptions)=>{
        const interrupt = interruptTool(runOptions.registry);
        if (fn) {
            return fn(i, {
                ...runOptions,
                context: {
                    ...runOptions.context
                },
                interrupt
            });
        }
        return interrupt();
    });
    implementTool(a, config);
    return {
        __action: {
            ...a.__action,
            metadata: {
                ...a.__action.metadata,
                type: "tool"
            }
        },
        attach (registry) {
            const bound = a.attach(registry);
            implementTool(bound, config);
            return bound;
        }
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ToolInterruptError,
    asTool,
    defineInterrupt,
    defineTool,
    dynamicTool,
    isDynamicTool,
    isToolRequest,
    isToolResponse,
    lookupToolByName,
    resolveTools,
    toToolDefinition
}); //# sourceMappingURL=tool.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/generate/resolve-tool-requests.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var resolve_tool_requests_exports = {};
__export(resolve_tool_requests_exports, {
    assertValidToolNames: ()=>assertValidToolNames,
    resolveRestartedTools: ()=>resolveRestartedTools,
    resolveResumeOption: ()=>resolveResumeOption,
    resolveToolRequest: ()=>resolveToolRequest,
    resolveToolRequests: ()=>resolveToolRequests,
    toPendingOutput: ()=>toPendingOutput,
    toToolMap: ()=>toToolMap
});
module.exports = __toCommonJS(resolve_tool_requests_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-route] (ecmascript)");
var import_prompt = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/prompt.js [app-route] (ecmascript)");
var import_tool = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/tool.js [app-route] (ecmascript)");
function toToolMap(tools) {
    assertValidToolNames(tools);
    const out = {};
    for (const tool of tools){
        const name = tool.__action.name;
        const shortName = name.substring(name.lastIndexOf("/") + 1);
        out[shortName] = tool;
    }
    return out;
}
function assertValidToolNames(tools) {
    const nameMap = {};
    for (const tool of tools){
        const name = tool.__action.name;
        const shortName = name.substring(name.lastIndexOf("/") + 1);
        if (nameMap[shortName]) {
            throw new import_core.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `Cannot provide two tools with the same name: '${name}' and '${nameMap[shortName]}'`
            });
        }
        nameMap[shortName] = name;
    }
}
function toRunOptions(part) {
    const out = {
        metadata: part.metadata
    };
    if (part.metadata?.resumed) out.resumed = part.metadata.resumed;
    return out;
}
function toPendingOutput(part, response) {
    return {
        ...part,
        metadata: {
            ...part.metadata,
            pendingOutput: response.toolResponse.output
        }
    };
}
async function resolveToolRequest(rawRequest, part, toolMap, runOptions) {
    const tool = toolMap[part.toolRequest.name];
    if (!tool) {
        throw new import_core.GenkitError({
            status: "NOT_FOUND",
            message: `Tool ${part.toolRequest.name} not found`,
            detail: {
                request: rawRequest
            }
        });
    }
    if ((0, import_prompt.isPromptAction)(tool)) {
        const preamble = await tool(part.toolRequest.input);
        const response = {
            toolResponse: {
                name: part.toolRequest.name,
                ref: part.toolRequest.ref,
                output: `transferred to ${part.toolRequest.name}`
            }
        };
        return {
            preamble,
            response
        };
    }
    try {
        const output = await tool(part.toolRequest.input, toRunOptions(part));
        const response = (0, import_core.stripUndefinedProps)({
            toolResponse: {
                name: part.toolRequest.name,
                ref: part.toolRequest.ref,
                output
            }
        });
        return {
            response
        };
    } catch (e) {
        if (e instanceof import_tool.ToolInterruptError || // There's an inexplicable case when the above type check fails, only in tests.
        e.name === "ToolInterruptError") {
            const ie = e;
            import_logging.logger.debug(`tool '${toolMap[part.toolRequest?.name].__action.name}' triggered an interrupt${ie.metadata ? `: ${JSON.stringify(ie.metadata)}` : ""}`);
            const interrupt = {
                toolRequest: part.toolRequest,
                metadata: {
                    ...part.metadata,
                    interrupt: ie.metadata || true
                }
            };
            return {
                interrupt
            };
        }
        throw e;
    }
}
async function resolveToolRequests(registry, rawRequest, generatedMessage) {
    const toolMap = toToolMap(await (0, import_tool.resolveTools)(registry, rawRequest.tools));
    const responseParts = [];
    let hasInterrupts = false;
    let transferPreamble;
    const revisedModelMessage = {
        ...generatedMessage,
        content: [
            ...generatedMessage.content
        ]
    };
    await Promise.all(revisedModelMessage.content.map(async (part, i)=>{
        if (!part.toolRequest) return;
        const { preamble, response, interrupt } = await resolveToolRequest(rawRequest, part, toolMap);
        if (preamble) {
            if (transferPreamble) {
                throw new import_core.GenkitError({
                    status: "INVALID_ARGUMENT",
                    message: `Model attempted to transfer to multiple prompt tools.`
                });
            }
            transferPreamble = preamble;
        }
        if (response) {
            responseParts.push(response);
            revisedModelMessage.content.splice(i, 1, toPendingOutput(part, response));
        }
        if (interrupt) {
            revisedModelMessage.content.splice(i, 1, interrupt);
            hasInterrupts = true;
        }
    }));
    if (hasInterrupts) {
        return {
            revisedModelMessage
        };
    }
    return {
        toolMessage: {
            role: "tool",
            content: responseParts
        },
        transferPreamble
    };
}
function findCorrespondingToolRequest(parts, part) {
    const name = part.toolRequest?.name || part.toolResponse?.name;
    const ref = part.toolRequest?.ref || part.toolResponse?.ref;
    return parts.find((p)=>p.toolRequest?.name === name && p.toolRequest?.ref === ref);
}
function findCorrespondingToolResponse(parts, part) {
    const name = part.toolRequest?.name || part.toolResponse?.name;
    const ref = part.toolRequest?.ref || part.toolResponse?.ref;
    return parts.find((p)=>p.toolResponse?.name === name && p.toolResponse?.ref === ref);
}
async function resolveResumedToolRequest(rawRequest, part, toolMap) {
    if (part.metadata?.pendingOutput) {
        const { pendingOutput, ...metadata } = part.metadata;
        const toolResponse = {
            toolResponse: {
                name: part.toolRequest.name,
                ref: part.toolRequest.ref,
                output: pendingOutput
            },
            metadata: {
                ...metadata,
                source: "pending"
            }
        };
        return (0, import_core.stripUndefinedProps)({
            toolResponse,
            toolRequest: {
                ...part,
                metadata
            }
        });
    }
    const providedResponse = findCorrespondingToolResponse(rawRequest.resume?.respond || [], part);
    if (providedResponse) {
        const toolResponse = providedResponse;
        const { interrupt, ...metadata } = part.metadata || {};
        return (0, import_core.stripUndefinedProps)({
            toolResponse,
            toolRequest: {
                ...part,
                metadata: {
                    ...metadata,
                    resolvedInterrupt: interrupt
                }
            }
        });
    }
    const restartRequest = findCorrespondingToolRequest(rawRequest.resume?.restart || [], part);
    if (restartRequest) {
        const { response, interrupt, preamble } = await resolveToolRequest(rawRequest, restartRequest, toolMap);
        if (preamble) {
            throw new import_core.GenkitError({
                status: "INTERNAL",
                message: `Prompt tool '${restartRequest.toolRequest.name}' executed inside 'restart' resolution. This should never happen.`
            });
        }
        if (interrupt) return {
            interrupt
        };
        if (response) {
            const toolResponse = response;
            const { interrupt: interrupt2, ...metadata } = part.metadata || {};
            return (0, import_core.stripUndefinedProps)({
                toolResponse,
                toolRequest: {
                    ...part,
                    metadata: {
                        ...metadata,
                        resolvedInterrupt: interrupt2
                    }
                }
            });
        }
    }
    throw new import_core.GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Unresolved tool request '${part.toolRequest.name}${part.toolRequest.ref ? `#${part.toolRequest.ref}` : ""}' was not handled by the 'resume' argument. You must supply replies or restarts for all interrupted tool requests.'`
    });
}
async function resolveResumeOption(registry, rawRequest) {
    if (!rawRequest.resume) return {
        revisedRequest: rawRequest
    };
    const toolMap = toToolMap(await (0, import_tool.resolveTools)(registry, rawRequest.tools));
    const messages = rawRequest.messages;
    const lastMessage = messages.at(-1);
    if (!lastMessage || lastMessage.role !== "model" || !lastMessage.content.find((p)=>p.toolRequest)) {
        throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: `Cannot 'resume' generation unless the previous message is a model message with at least one tool request.`
        });
    }
    const toolResponses = [];
    let interrupted = false;
    lastMessage.content = await Promise.all(lastMessage.content.map(async (part)=>{
        if (!(0, import_tool.isToolRequest)(part)) return part;
        const resolved = await resolveResumedToolRequest(rawRequest, part, toolMap);
        if (resolved.interrupt) {
            interrupted = true;
            return resolved.interrupt;
        }
        toolResponses.push(resolved.toolResponse);
        return resolved.toolRequest;
    }));
    if (interrupted) {
        return {
            interruptedResponse: {
                finishReason: "interrupted",
                finishMessage: "One or more tools triggered interrupts while resuming generation. The model was not called.",
                message: lastMessage
            }
        };
    }
    const numToolRequests = lastMessage.content.filter((p)=>!!p.toolRequest).length;
    if (toolResponses.length !== numToolRequests) {
        throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: `Expected ${numToolRequests} tool responses but resolved to ${toolResponses.length}.`,
            detail: {
                toolResponses,
                message: lastMessage
            }
        });
    }
    const toolMessage = {
        role: "tool",
        content: toolResponses,
        metadata: {
            resumed: rawRequest.resume.metadata || true
        }
    };
    return (0, import_core.stripUndefinedProps)({
        revisedRequest: {
            ...rawRequest,
            resume: void 0,
            messages: [
                ...messages,
                toolMessage
            ]
        },
        toolMessage
    });
}
async function resolveRestartedTools(registry, rawRequest) {
    const toolMap = toToolMap(await (0, import_tool.resolveTools)(registry, rawRequest.tools));
    const lastMessage = rawRequest.messages.at(-1);
    if (!lastMessage || lastMessage.role !== "model") return [];
    const restarts = lastMessage.content.filter((p)=>p.toolRequest && p.metadata?.resumed);
    return await Promise.all(restarts.map(async (p)=>{
        const { response, interrupt } = await resolveToolRequest(rawRequest, p, toolMap);
        if (interrupt) return interrupt;
        return toPendingOutput(p, response);
    }));
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    assertValidToolNames,
    resolveRestartedTools,
    resolveResumeOption,
    resolveToolRequest,
    resolveToolRequests,
    toPendingOutput,
    toToolMap
}); //# sourceMappingURL=resolve-tool-requests.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/generate/action.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var action_exports = {};
__export(action_exports, {
    defineGenerateAction: ()=>defineGenerateAction,
    generateHelper: ()=>generateHelper,
    inferRoleFromParts: ()=>inferRoleFromParts,
    shouldInjectFormatInstructions: ()=>shouldInjectFormatInstructions
});
module.exports = __toCommonJS(action_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-route] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-route] (ecmascript)");
var import_formats = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/index.js [app-route] (ecmascript)");
var import_generate = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate.js [app-route] (ecmascript)");
var import_chunk = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/chunk.js [app-route] (ecmascript)");
var import_model = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model.js [app-route] (ecmascript)");
var import_resource = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/resource.js [app-route] (ecmascript)");
var import_tool = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/tool.js [app-route] (ecmascript)");
var import_resolve_tool_requests = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/resolve-tool-requests.js [app-route] (ecmascript)");
function defineGenerateAction(registry) {
    return (0, import_core.defineAction)(registry, {
        actionType: "util",
        name: "generate",
        inputSchema: import_model.GenerateActionOptionsSchema,
        outputSchema: import_model.GenerateResponseSchema,
        streamSchema: import_model.GenerateResponseChunkSchema
    }, async (request, { streamingRequested, sendChunk })=>{
        const generateFn = (sendChunk2)=>generate(registry, {
                rawRequest: request,
                currentTurn: 0,
                messageIndex: 0,
                // Generate util action does not support middleware. Maybe when we add named/registered middleware....
                middleware: [],
                streamingCallback: sendChunk2
            });
        return streamingRequested ? generateFn((c)=>sendChunk(c.toJSON ? c.toJSON() : c)) : generateFn();
    });
}
async function generateHelper(registry, options) {
    const currentTurn = options.currentTurn ?? 0;
    const messageIndex = options.messageIndex ?? 0;
    return await (0, import_tracing.runInNewSpan)(registry, {
        metadata: {
            name: "generate"
        },
        labels: {
            [import_tracing.SPAN_TYPE_ATTR]: "util"
        }
    }, async (metadata)=>{
        metadata.name = "generate";
        metadata.input = options.rawRequest;
        const output = await generate(registry, {
            rawRequest: options.rawRequest,
            middleware: options.middleware,
            currentTurn,
            messageIndex,
            abortSignal: options.abortSignal,
            streamingCallback: options.streamingCallback
        });
        metadata.output = JSON.stringify(output);
        return output;
    });
}
async function resolveParameters(registry, request) {
    const [model, tools, format] = await Promise.all([
        (0, import_model.resolveModel)(registry, request.model, {
            warnDeprecated: true
        }).then((r)=>r.modelAction),
        (0, import_tool.resolveTools)(registry, request.tools),
        (0, import_formats.resolveFormat)(registry, request.output)
    ]);
    return {
        model,
        tools,
        format
    };
}
function applyFormat(rawRequest, resolvedFormat) {
    const outRequest = {
        ...rawRequest
    };
    if (rawRequest.output?.jsonSchema && !rawRequest.output?.format) {
        outRequest.output = {
            ...rawRequest.output,
            format: "json"
        };
    }
    const instructions = (0, import_formats.resolveInstructions)(resolvedFormat, outRequest.output?.jsonSchema, outRequest?.output?.instructions);
    if (resolvedFormat) {
        if (shouldInjectFormatInstructions(resolvedFormat.config, rawRequest?.output)) {
            outRequest.messages = (0, import_formats.injectInstructions)(outRequest.messages, instructions);
        }
        outRequest.output = {
            // use output config from the format
            ...resolvedFormat.config,
            // if anything is set explicitly, use that
            ...outRequest.output
        };
    }
    return outRequest;
}
function shouldInjectFormatInstructions(formatConfig, rawRequestConfig) {
    return formatConfig?.defaultInstructions !== false || rawRequestConfig?.instructions;
}
function applyTransferPreamble(rawRequest, transferPreamble) {
    if (!transferPreamble) {
        return rawRequest;
    }
    return (0, import_core.stripUndefinedProps)({
        ...rawRequest,
        messages: [
            ...(0, import_generate.tagAsPreamble)(transferPreamble.messages),
            ...rawRequest.messages.filter((m)=>!m.metadata?.preamble)
        ],
        toolChoice: transferPreamble.toolChoice || rawRequest.toolChoice,
        tools: transferPreamble.tools || rawRequest.tools,
        config: transferPreamble.config || rawRequest.config
    });
}
async function generate(registry, { rawRequest, middleware, currentTurn, messageIndex, abortSignal, streamingCallback }) {
    const { model, tools, format } = await resolveParameters(registry, rawRequest);
    rawRequest = applyFormat(rawRequest, format);
    rawRequest = await applyResources(registry, rawRequest);
    await (0, import_resolve_tool_requests.assertValidToolNames)(tools);
    const { revisedRequest, interruptedResponse, toolMessage: resumedToolMessage } = await (0, import_resolve_tool_requests.resolveResumeOption)(registry, rawRequest);
    if (interruptedResponse) {
        throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: "One or more tools triggered an interrupt during a restarted execution.",
            detail: {
                message: interruptedResponse.message
            }
        });
    }
    rawRequest = revisedRequest;
    const request = await actionToGenerateRequest(rawRequest, tools, format, model);
    const previousChunks = [];
    let chunkRole = "model";
    const makeChunk = (role, chunk)=>{
        if (role !== chunkRole && previousChunks.length) messageIndex++;
        chunkRole = role;
        const prevToSend = [
            ...previousChunks
        ];
        previousChunks.push(chunk);
        return new import_chunk.GenerateResponseChunk(chunk, {
            index: messageIndex,
            role,
            previousChunks: prevToSend,
            parser: format?.handler(request.output?.schema).parseChunk
        });
    };
    if (resumedToolMessage && streamingCallback) {
        streamingCallback(makeChunk("tool", resumedToolMessage));
    }
    var response;
    const dispatch = async (index, req)=>{
        if (!middleware || index === middleware.length) {
            return await model(req, {
                abortSignal,
                onChunk: streamingCallback && ((chunk)=>streamingCallback && streamingCallback(makeChunk("model", chunk)))
            });
        }
        const currentMiddleware = middleware[index];
        return currentMiddleware(req, async (modifiedReq)=>dispatch(index + 1, modifiedReq || req));
    };
    const modelResponse = await dispatch(0, request);
    if (model.__action.actionType === "background-model") {
        response = new import_generate.GenerateResponse({
            operation: modelResponse
        }, {
            request,
            parser: format?.handler(request.output?.schema).parseMessage
        });
    } else {
        response = new import_generate.GenerateResponse(modelResponse, {
            request,
            parser: format?.handler(request.output?.schema).parseMessage
        });
    }
    if (model.__action.actionType === "background-model") {
        return response.toJSON();
    }
    response.assertValid();
    const generatedMessage = response.message;
    const toolRequests = generatedMessage.content.filter((part)=>!!part.toolRequest);
    if (rawRequest.returnToolRequests || toolRequests.length === 0) {
        if (toolRequests.length === 0) response.assertValidSchema(request);
        return response.toJSON();
    }
    const maxIterations = rawRequest.maxTurns ?? 5;
    if (currentTurn + 1 > maxIterations) {
        throw new import_generate.GenerationResponseError(response, `Exceeded maximum tool call iterations (${maxIterations})`, "ABORTED", {
            request
        });
    }
    const { revisedModelMessage, toolMessage, transferPreamble } = await (0, import_resolve_tool_requests.resolveToolRequests)(registry, rawRequest, generatedMessage);
    if (revisedModelMessage) {
        return {
            ...response.toJSON(),
            finishReason: "interrupted",
            finishMessage: "One or more tool calls resulted in interrupts.",
            message: revisedModelMessage
        };
    }
    streamingCallback?.(makeChunk("tool", {
        content: toolMessage.content
    }));
    let nextRequest = {
        ...rawRequest,
        messages: [
            ...rawRequest.messages,
            generatedMessage.toJSON(),
            toolMessage
        ]
    };
    nextRequest = applyTransferPreamble(nextRequest, transferPreamble);
    return await generateHelper(registry, {
        rawRequest: nextRequest,
        middleware,
        currentTurn: currentTurn + 1,
        messageIndex: messageIndex + 1,
        streamingCallback
    });
}
async function actionToGenerateRequest(options, resolvedTools, resolvedFormat, model) {
    const modelInfo = model.__action.metadata?.model;
    if ((options.tools?.length ?? 0) > 0 && modelInfo?.supports && !modelInfo?.supports?.tools) {
        import_logging.logger.warn(`The model '${model.__action.name}' does not support tools (you set: ${options.tools?.length} tools). The model may not behave the way you expect.`);
    }
    if (options.toolChoice && modelInfo?.supports && !modelInfo?.supports?.toolChoice) {
        import_logging.logger.warn(`The model '${model.__action.name}' does not support the 'toolChoice' option (you set: ${options.toolChoice}). The model may not behave the way you expect.`);
    }
    const out = {
        messages: options.messages,
        config: options.config,
        docs: options.docs,
        tools: resolvedTools?.map(import_tool.toToolDefinition) || [],
        output: (0, import_core.stripUndefinedProps)({
            constrained: options.output?.constrained,
            contentType: options.output?.contentType,
            format: options.output?.format,
            schema: options.output?.jsonSchema
        })
    };
    if (options.toolChoice) {
        out.toolChoice = options.toolChoice;
    }
    if (out.output && !out.output.schema) delete out.output.schema;
    return out;
}
function inferRoleFromParts(parts) {
    const uniqueRoles = /* @__PURE__ */ new Set();
    for (const part of parts){
        const role = getRoleFromPart(part);
        uniqueRoles.add(role);
        if (uniqueRoles.size > 1) {
            throw new Error("Contents contain mixed roles");
        }
    }
    return Array.from(uniqueRoles)[0];
}
function getRoleFromPart(part) {
    if (part.toolRequest !== void 0) return "model";
    if (part.toolResponse !== void 0) return "tool";
    if (part.text !== void 0) return "user";
    if (part.media !== void 0) return "user";
    if (part.data !== void 0) return "user";
    throw new Error("No recognized fields in content");
}
async function applyResources(registry, rawRequest) {
    if (!rawRequest.messages.find((m)=>!!m.content.find((c)=>c.resource))) {
        return rawRequest;
    }
    const updatedMessages = [];
    for (const m of rawRequest.messages){
        if (!m.content.find((c)=>c.resource)) {
            updatedMessages.push(m);
            continue;
        }
        const updatedContent = [];
        for (const p of m.content){
            if (!p.resource) {
                updatedContent.push(p);
                continue;
            }
            const resource = await (0, import_resource.findMatchingResource)(registry, p.resource);
            if (!resource) {
                throw new import_core.GenkitError({
                    status: "NOT_FOUND",
                    message: `failed to find matching resource for ${p.resource.uri}`
                });
            }
            const resourceParts = await resource(p.resource);
            updatedContent.push(...resourceParts.content);
        }
        updatedMessages.push({
            ...m,
            content: updatedContent
        });
    }
    return {
        ...rawRequest,
        messages: updatedMessages
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    defineGenerateAction,
    generateHelper,
    inferRoleFromParts,
    shouldInjectFormatInstructions
}); //# sourceMappingURL=action.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/generate/response.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var response_exports = {};
__export(response_exports, {
    GenerateResponse: ()=>GenerateResponse
});
module.exports = __toCommonJS(response_exports);
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_generate = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate.js [app-route] (ecmascript)");
var import_message = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/message.js [app-route] (ecmascript)");
class GenerateResponse {
    /** The generated message. */ message;
    /** The reason generation stopped for this request. */ finishReason;
    /** Additional information about why the model stopped generating, if any. */ finishMessage;
    /** Usage information. */ usage;
    /** Provider-specific response data. */ custom;
    /** Provider-specific response data. */ raw;
    /** The request that generated this response. */ request;
    /** Model generation long running operation. */ operation;
    /** Name of the model used. */ model;
    /** The parser for output parsing of this response. */ parser;
    constructor(response, options){
        const generatedMessage = response.message || response.candidates?.[0]?.message;
        if (generatedMessage) {
            this.message = new import_message.Message(generatedMessage, {
                parser: options?.parser
            });
        }
        this.finishReason = response.finishReason || response.candidates?.[0]?.finishReason;
        this.finishMessage = response.finishMessage || response.candidates?.[0]?.finishMessage;
        this.usage = response.usage || {};
        this.custom = response.custom || {};
        this.raw = response.raw || this.custom;
        this.request = options?.request;
        this.operation = response?.operation;
    }
    /**
   * Throws an error if the response does not contain valid output.
   */ assertValid() {
        if (this.finishReason === "blocked") {
            throw new import_generate.GenerationBlockedError(this, `Generation blocked${this.finishMessage ? `: ${this.finishMessage}` : "."}`);
        }
        if (!this.message && !this.operation) {
            throw new import_generate.GenerationResponseError(this, `Model did not generate a message. Finish reason: '${this.finishReason}': ${this.finishMessage}`);
        }
    }
    /**
   * Throws an error if the response does not conform to expected schema.
   */ assertValidSchema(request) {
        if (request?.output?.schema || this.request?.output?.schema) {
            const o = this.output;
            (0, import_schema.parseSchema)(o, {
                jsonSchema: request?.output?.schema || this.request?.output?.schema
            });
        }
    }
    isValid(request) {
        try {
            this.assertValid();
            this.assertValidSchema(request);
            return true;
        } catch (e) {
            return false;
        }
    }
    /**
   * If the generated message contains a `data` part, it is returned. Otherwise,
   * the `output()` method extracts the first valid JSON object or array from the text
   * contained in the selected candidate's message and returns it.
   *
   * @returns The structured output contained in the selected candidate.
   */ get output() {
        return this.message?.output || null;
    }
    /**
   * Concatenates all `text` parts present in the generated message with no delimiter.
   * @returns A string of all concatenated text parts.
   */ get text() {
        return this.message?.text || "";
    }
    /**
   * Concatenates all `reasoning` parts present in the generated message with no delimiter.
   * @returns A string of all concatenated reasoning parts.
   */ get reasoning() {
        return this.message?.reasoning || "";
    }
    /**
   * Returns the first detected media part in the generated message. Useful for
   * extracting (for example) an image from a generation expected to create one.
   * @returns The first detected `media` part in the candidate.
   */ get media() {
        return this.message?.media || null;
    }
    /**
   * Returns the first detected `data` part of the generated message.
   * @returns The first `data` part detected in the candidate (if any).
   */ get data() {
        return this.message?.data || null;
    }
    /**
   * Returns all tool request found in the generated message.
   * @returns Array of all tool request found in the candidate.
   */ get toolRequests() {
        return this.message?.toolRequests || [];
    }
    /**
   * Returns all tool requests annotated as interrupts found in the generated message.
   * @returns A list of ToolRequestParts.
   */ get interrupts() {
        return this.message?.interrupts || [];
    }
    /**
   * Returns the message history for the request by concatenating the model
   * response to the list of messages from the request. The result of this
   * method can be safely serialized to JSON for persistence in a database.
   * @returns A serializable list of messages compatible with `generate({history})`.
   */ get messages() {
        if (!this.request) throw new Error("Can't construct history for response without request reference.");
        if (!this.message) throw new Error("Can't construct history for response without generated message.");
        return [
            ...this.request?.messages,
            this.message.toJSON()
        ];
    }
    toJSON() {
        const out = {
            message: this.message?.toJSON(),
            finishReason: this.finishReason,
            finishMessage: this.finishMessage,
            usage: this.usage,
            custom: this.custom.toJSON?.() || this.custom,
            request: this.request,
            operation: this.operation
        };
        if (!out.finishMessage) delete out.finishMessage;
        if (!out.request) delete out.request;
        if (!out.operation) delete out.operation;
        return out;
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    GenerateResponse
}); //# sourceMappingURL=response.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/generate.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var generate_exports = {};
__export(generate_exports, {
    GenerateResponse: ()=>import_response.GenerateResponse,
    GenerateResponseChunk: ()=>import_chunk.GenerateResponseChunk,
    GenerationBlockedError: ()=>GenerationBlockedError,
    GenerationResponseError: ()=>GenerationResponseError,
    generate: ()=>generate,
    generateOperation: ()=>generateOperation,
    generateStream: ()=>generateStream,
    tagAsPreamble: ()=>tagAsPreamble,
    toGenerateActionOptions: ()=>toGenerateActionOptions,
    toGenerateRequest: ()=>toGenerateRequest
});
module.exports = __toCommonJS(generate_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_async = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async.js [app-route] (ecmascript)");
var import_registry = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/registry.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_formats = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/formats/index.js [app-route] (ecmascript)");
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/action.js [app-route] (ecmascript)");
var import_chunk = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/chunk.js [app-route] (ecmascript)");
var import_response = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate/response.js [app-route] (ecmascript)");
var import_message = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/message.js [app-route] (ecmascript)");
var import_model = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model.js [app-route] (ecmascript)");
var import_prompt = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/prompt.js [app-route] (ecmascript)");
var import_resource = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/resource.js [app-route] (ecmascript)");
var import_tool = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/tool.js [app-route] (ecmascript)");
async function toGenerateRequest(registry, options) {
    const messages = [];
    if (options.system) {
        messages.push({
            role: "system",
            content: import_message.Message.parseContent(options.system)
        });
    }
    if (options.messages) {
        messages.push(...options.messages.map((m)=>import_message.Message.parseData(m)));
    }
    if (options.prompt) {
        messages.push({
            role: "user",
            content: import_message.Message.parseContent(options.prompt)
        });
    }
    if (messages.length === 0) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: "at least one message is required in generate request"
        });
    }
    if (options.resume && !(messages.at(-1)?.role === "model" && messages.at(-1)?.content.find((p)=>!!p.toolRequest))) {
        throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: `Last message must be a 'model' role with at least one tool request to 'resume' generation.`,
            detail: messages.at(-1)
        });
    }
    let tools;
    if (options.tools) {
        tools = await (0, import_tool.resolveTools)(registry, options.tools);
    }
    const resolvedSchema = (0, import_schema.toJsonSchema)({
        schema: options.output?.schema,
        jsonSchema: options.output?.jsonSchema
    });
    const resolvedFormat = await (0, import_formats.resolveFormat)(registry, options.output);
    const instructions = (0, import_formats.resolveInstructions)(resolvedFormat, resolvedSchema, options?.output?.instructions);
    const out = {
        messages: (0, import_action.shouldInjectFormatInstructions)(resolvedFormat?.config, options.output) ? (0, import_formats.injectInstructions)(messages, instructions) : messages,
        config: options.config,
        docs: options.docs,
        tools: tools?.map(import_tool.toToolDefinition) || [],
        output: {
            ...resolvedFormat?.config || {},
            ...options.output,
            schema: resolvedSchema
        }
    };
    if (!out?.output?.schema) delete out?.output?.schema;
    return out;
}
class GenerationResponseError extends import_core.GenkitError {
    detail;
    constructor(response, message, status, detail){
        super({
            status: status || "FAILED_PRECONDITION",
            message
        });
        this.detail = {
            response,
            ...detail
        };
    }
}
async function toolsToActionRefs(registry, toolOpt) {
    if (!toolOpt) return;
    const tools = [];
    for (const t of toolOpt){
        if (typeof t === "string") {
            tools.push(await resolveFullToolName(registry, t));
        } else if ((0, import_core.isAction)(t) || (0, import_tool.isDynamicTool)(t)) {
            tools.push(`/${t.__action.metadata?.type}/${t.__action.name}`);
        } else if ((0, import_prompt.isExecutablePrompt)(t)) {
            const promptToolAction = await t.asTool();
            tools.push(`/prompt/${promptToolAction.__action.name}`);
        } else {
            throw new Error(`Unable to determine type of tool: ${JSON.stringify(t)}`);
        }
    }
    return tools;
}
function messagesFromOptions(options) {
    const messages = [];
    if (options.system) {
        messages.push({
            role: "system",
            content: import_message.Message.parseContent(options.system)
        });
    }
    if (options.messages) {
        messages.push(...options.messages);
    }
    if (options.prompt) {
        messages.push({
            role: "user",
            content: import_message.Message.parseContent(options.prompt)
        });
    }
    if (messages.length === 0) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: "at least one message is required in generate request"
        });
    }
    return messages;
}
class GenerationBlockedError extends GenerationResponseError {
}
async function generate(registry, options) {
    const resolvedOptions = {
        ...await Promise.resolve(options)
    };
    const resolvedFormat = await (0, import_formats.resolveFormat)(registry, resolvedOptions.output);
    registry = maybeRegisterDynamicTools(registry, resolvedOptions);
    registry = maybeRegisterDynamicResources(registry, resolvedOptions);
    const params = await toGenerateActionOptions(registry, resolvedOptions);
    const tools = await toolsToActionRefs(registry, resolvedOptions.tools);
    const streamingCallback = stripNoop(resolvedOptions.onChunk ?? resolvedOptions.streamingCallback);
    const response = await (0, import_core.runWithContext)(registry, resolvedOptions.context, ()=>(0, import_action.generateHelper)(registry, {
            rawRequest: params,
            middleware: resolvedOptions.use,
            abortSignal: resolvedOptions.abortSignal,
            streamingCallback
        }));
    const request = await toGenerateRequest(registry, {
        ...resolvedOptions,
        tools
    });
    return new import_response.GenerateResponse(response, {
        request: response.request ?? request,
        parser: resolvedFormat?.handler(request.output?.schema).parseMessage
    });
}
async function generateOperation(registry, options) {
    (0, import_core.assertUnstable)(registry, "beta", "generateOperation is a beta feature.");
    options = await options;
    const resolvedModel = await (0, import_model.resolveModel)(registry, options.model);
    if (!resolvedModel.modelAction.__action.metadata?.model.supports?.longRunning) {
        throw new import_core.GenkitError({
            status: "INVALID_ARGUMENT",
            message: `Model '${resolvedModel.modelAction.__action.name}' does not support long running operations.`
        });
    }
    const { operation } = await generate(registry, options);
    if (!operation) {
        throw new import_core.GenkitError({
            status: "FAILED_PRECONDITION",
            message: `Model '${resolvedModel.modelAction.__action.name}' did not return an operation.`
        });
    }
    return operation;
}
function maybeRegisterDynamicTools(registry, options) {
    let hasDynamicTools = false;
    options?.tools?.forEach((t)=>{
        if ((0, import_tool.isDynamicTool)(t)) {
            if ((0, import_core.isDetachedAction)(t)) {
                t = t.attach(registry);
            }
            if (!hasDynamicTools) {
                hasDynamicTools = true;
                registry = import_registry.Registry.withParent(registry);
            }
            registry.registerAction("tool", t);
        }
    });
    return registry;
}
function maybeRegisterDynamicResources(registry, options) {
    let hasDynamicResources = false;
    options?.resources?.forEach((r)=>{
        if ((0, import_resource.isDynamicResourceAction)(r)) {
            const attached = r.attach(registry);
            if (!hasDynamicResources) {
                hasDynamicResources = true;
                registry = import_registry.Registry.withParent(registry);
            }
            registry.registerAction("resource", attached);
        }
    });
    return registry;
}
async function toGenerateActionOptions(registry, options) {
    const resolvedModel = await (0, import_model.resolveModel)(registry, options.model);
    const tools = await toolsToActionRefs(registry, options.tools);
    const messages = messagesFromOptions(options);
    const resolvedSchema = (0, import_schema.toJsonSchema)({
        schema: options.output?.schema,
        jsonSchema: options.output?.jsonSchema
    });
    if ((options.output?.schema || options.output?.jsonSchema) && !options.output?.format) {
        options.output.format = "json";
    }
    const params = {
        model: resolvedModel.modelAction.__action.name,
        docs: options.docs,
        messages,
        tools,
        toolChoice: options.toolChoice,
        config: {
            version: resolvedModel.version,
            ...stripUndefinedOptions(resolvedModel.config),
            ...stripUndefinedOptions(options.config)
        },
        output: options.output && {
            ...options.output,
            format: options.output.format,
            jsonSchema: resolvedSchema
        },
        // coerce reply and restart into arrays for the action schema
        resume: options.resume && {
            respond: [
                options.resume.respond || []
            ].flat(),
            restart: [
                options.resume.restart || []
            ].flat(),
            metadata: options.resume.metadata
        },
        returnToolRequests: options.returnToolRequests,
        maxTurns: options.maxTurns
    };
    if (Object.keys(params.config).length === 0 && !options.config) {
        delete params.config;
    }
    return params;
}
function stripNoop(callback) {
    if (callback === import_core.sentinelNoopStreamingCallback) {
        return void 0;
    }
    return callback;
}
function stripUndefinedOptions(input) {
    if (!input) return input;
    const copy = {
        ...input
    };
    Object.keys(input).forEach((key)=>{
        if (copy[key] === void 0) {
            delete copy[key];
        }
    });
    return copy;
}
async function resolveFullToolName(registry, name) {
    if (await registry.lookupAction(`/tool/${name}`)) {
        return `/tool/${name}`;
    } else if (await registry.lookupAction(`/prompt/${name}`)) {
        return `/prompt/${name}`;
    } else {
        throw new Error(`Unable to determine type of of tool: ${name}`);
    }
}
function generateStream(registry, options) {
    const channel = new import_async.Channel();
    const generated = Promise.resolve(options).then((resolvedOptions)=>generate(registry, {
            ...resolvedOptions,
            onChunk: (chunk)=>channel.send(chunk)
        }));
    generated.then(()=>channel.close(), (err)=>channel.error(err));
    return {
        response: generated,
        stream: channel
    };
}
function tagAsPreamble(msgs) {
    if (!msgs) {
        return void 0;
    }
    return msgs.map((m)=>({
            ...m,
            metadata: {
                ...m.metadata,
                preamble: true
            }
        }));
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    GenerateResponse,
    GenerateResponseChunk,
    GenerationBlockedError,
    GenerationResponseError,
    generate,
    generateOperation,
    generateStream,
    tagAsPreamble,
    toGenerateActionOptions,
    toGenerateRequest
}); //# sourceMappingURL=generate.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/retriever.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var retriever_exports = {};
__export(retriever_exports, {
    CommonRetrieverOptionsSchema: ()=>CommonRetrieverOptionsSchema,
    Document: ()=>import_document2.Document,
    DocumentDataSchema: ()=>import_document2.DocumentDataSchema,
    IndexerInfoSchema: ()=>IndexerInfoSchema,
    RetrieverInfoSchema: ()=>RetrieverInfoSchema,
    defineIndexer: ()=>defineIndexer,
    defineRetriever: ()=>defineRetriever,
    defineSimpleRetriever: ()=>defineSimpleRetriever,
    index: ()=>index,
    indexerRef: ()=>indexerRef,
    retrieve: ()=>retrieve,
    retrieverRef: ()=>retrieverRef
});
module.exports = __toCommonJS(retriever_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var import_document2 = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
const RetrieverRequestSchema = import_core.z.object({
    query: import_document.DocumentDataSchema,
    options: import_core.z.any().optional()
});
const RetrieverResponseSchema = import_core.z.object({
    documents: import_core.z.array(import_document.DocumentDataSchema)
});
const IndexerRequestSchema = import_core.z.object({
    documents: import_core.z.array(import_document.DocumentDataSchema),
    options: import_core.z.any().optional()
});
const RetrieverInfoSchema = import_core.z.object({
    label: import_core.z.string().optional(),
    /** Supported model capabilities. */ supports: import_core.z.object({
        /** Model can process media as part of the prompt (multimodal input). */ media: import_core.z.boolean().optional()
    }).optional()
});
function retrieverWithMetadata(retriever, configSchema) {
    const withMeta = retriever;
    withMeta.__configSchema = configSchema;
    return withMeta;
}
function indexerWithMetadata(indexer, configSchema) {
    const withMeta = indexer;
    withMeta.__configSchema = configSchema;
    return withMeta;
}
function defineRetriever(registry, options, runner) {
    const retriever = (0, import_core.defineAction)(registry, {
        actionType: "retriever",
        name: options.name,
        inputSchema: options.configSchema ? RetrieverRequestSchema.extend({
            options: options.configSchema.optional()
        }) : RetrieverRequestSchema,
        outputSchema: RetrieverResponseSchema,
        metadata: {
            type: "retriever",
            info: options.info,
            retriever: {
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0
            }
        }
    }, (i)=>runner(new import_document.Document(i.query), i.options));
    const rwm = retrieverWithMetadata(retriever, options.configSchema);
    return rwm;
}
function defineIndexer(registry, options, runner) {
    const indexer = (0, import_core.defineAction)(registry, {
        actionType: "indexer",
        name: options.name,
        inputSchema: options.configSchema ? IndexerRequestSchema.extend({
            options: options.configSchema.optional()
        }) : IndexerRequestSchema,
        outputSchema: import_core.z.void(),
        metadata: {
            type: "indexer",
            embedderInfo: options.embedderInfo,
            indexer: {
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0
            }
        }
    }, (i)=>runner(i.documents.map((dd)=>new import_document.Document(dd)), i.options));
    const iwm = indexerWithMetadata(indexer, options.configSchema);
    return iwm;
}
async function retrieve(registry, params) {
    let retriever;
    if (typeof params.retriever === "string") {
        retriever = await registry.lookupAction(`/retriever/${params.retriever}`);
    } else if (Object.hasOwnProperty.call(params.retriever, "info")) {
        retriever = await registry.lookupAction(`/retriever/${params.retriever.name}`);
    } else {
        retriever = params.retriever;
    }
    if (!retriever) {
        throw new Error("Unable to resolve the retriever");
    }
    const response = await retriever({
        query: typeof params.query === "string" ? import_document.Document.fromText(params.query) : params.query,
        options: params.options
    });
    return response.documents.map((d)=>new import_document.Document(d));
}
async function index(registry, params) {
    let indexer;
    if (typeof params.indexer === "string") {
        indexer = await registry.lookupAction(`/indexer/${params.indexer}`);
    } else if (Object.hasOwnProperty.call(params.indexer, "info")) {
        indexer = await registry.lookupAction(`/indexer/${params.indexer.name}`);
    } else {
        indexer = params.indexer;
    }
    if (!indexer) {
        throw new Error("Unable to utilize the provided indexer");
    }
    return await indexer({
        documents: params.documents,
        options: params.options
    });
}
const CommonRetrieverOptionsSchema = import_core.z.object({
    k: import_core.z.number().describe("Number of documents to retrieve").optional()
});
function retrieverRef(options) {
    return {
        ...options
    };
}
const IndexerInfoSchema = RetrieverInfoSchema;
function indexerRef(options) {
    return {
        ...options
    };
}
function itemToDocument(item, options) {
    if (!item) throw new import_core.GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Items returned from simple retriever must be non-null.`
    });
    if (typeof item === "string") return import_document.Document.fromText(item);
    if (typeof options.content === "function") {
        const transformed = options.content(item);
        return typeof transformed === "string" ? import_document.Document.fromText(transformed) : new import_document.Document({
            content: transformed
        });
    }
    if (typeof options.content === "string" && typeof item === "object") return import_document.Document.fromText(item[options.content]);
    throw new import_core.GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Cannot convert item to document without content option. Item: ${JSON.stringify(item)}`
    });
}
function itemToMetadata(item, options) {
    if (typeof item === "string") return void 0;
    if (Array.isArray(options.metadata) && typeof item === "object") {
        const out = {};
        options.metadata.forEach((key)=>out[key] = item[key]);
        return out;
    }
    if (typeof options.metadata === "function") return options.metadata(item);
    if (!options.metadata && typeof item === "object") {
        const out = {
            ...item
        };
        if (typeof options.content === "string") delete out[options.content];
        return out;
    }
    throw new import_core.GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Unable to extract metadata from item with supplied options. Item: ${JSON.stringify(item)}`
    });
}
function defineSimpleRetriever(registry, options, handler) {
    return defineRetriever(registry, {
        name: options.name,
        configSchema: options.configSchema
    }, async (query, config)=>{
        const result = await handler(query, config);
        return {
            documents: result.map((item)=>{
                const doc = itemToDocument(item, options);
                if (typeof item !== "string") doc.metadata = itemToMetadata(item, options);
                return doc;
            })
        };
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CommonRetrieverOptionsSchema,
    Document,
    DocumentDataSchema,
    IndexerInfoSchema,
    RetrieverInfoSchema,
    defineIndexer,
    defineRetriever,
    defineSimpleRetriever,
    index,
    indexerRef,
    retrieve,
    retrieverRef
}); //# sourceMappingURL=retriever.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/reranker.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var reranker_exports = {};
__export(reranker_exports, {
    CommonRerankerOptionsSchema: ()=>CommonRerankerOptionsSchema,
    RankedDocument: ()=>RankedDocument,
    RankedDocumentDataSchema: ()=>RankedDocumentDataSchema,
    RankedDocumentMetadataSchema: ()=>RankedDocumentMetadataSchema,
    RerankerInfoSchema: ()=>RerankerInfoSchema,
    defineReranker: ()=>defineReranker,
    rerank: ()=>rerank,
    rerankerRef: ()=>rerankerRef
});
module.exports = __toCommonJS(reranker_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var import_retriever = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/retriever.js [app-route] (ecmascript)");
const RankedDocumentMetadataSchema = import_core.z.object({
    score: import_core.z.number()
}).passthrough();
const RankedDocumentDataSchema = import_core.z.object({
    content: import_core.z.array(import_document.PartSchema),
    metadata: RankedDocumentMetadataSchema
});
class RankedDocument extends import_retriever.Document {
    content;
    metadata;
    constructor(data){
        super(data);
        this.content = data.content;
        this.metadata = data.metadata;
    }
    /**
   * Returns the score of the document.
   * @returns The score of the document.
   */ score() {
        return this.metadata.score;
    }
}
const RerankerRequestSchema = import_core.z.object({
    query: import_retriever.DocumentDataSchema,
    documents: import_core.z.array(import_retriever.DocumentDataSchema),
    options: import_core.z.any().optional()
});
const RerankerResponseSchema = import_core.z.object({
    documents: import_core.z.array(RankedDocumentDataSchema)
});
const RerankerInfoSchema = import_core.z.object({
    label: import_core.z.string().optional(),
    /** Supported model capabilities. */ supports: import_core.z.object({
        /** Model can process media as part of the prompt (multimodal input). */ media: import_core.z.boolean().optional()
    }).optional()
});
function rerankerWithMetadata(reranker, configSchema) {
    const withMeta = reranker;
    withMeta.__configSchema = configSchema;
    return withMeta;
}
function defineReranker(registry, options, runner) {
    const reranker = (0, import_core.defineAction)(registry, {
        actionType: "reranker",
        name: options.name,
        inputSchema: options.configSchema ? RerankerRequestSchema.extend({
            options: options.configSchema.optional()
        }) : RerankerRequestSchema,
        outputSchema: RerankerResponseSchema,
        metadata: {
            type: "reranker",
            info: options.info,
            reranker: {
                customOptions: options.configSchema ? (0, import_schema.toJsonSchema)({
                    schema: options.configSchema
                }) : void 0
            }
        }
    }, (i)=>runner(new import_retriever.Document(i.query), i.documents.map((d)=>new import_retriever.Document(d)), i.options));
    const rwm = rerankerWithMetadata(reranker, options.configSchema);
    return rwm;
}
async function rerank(registry, params) {
    let reranker;
    if (typeof params.reranker === "string") {
        reranker = await registry.lookupAction(`/reranker/${params.reranker}`);
    } else if (Object.hasOwnProperty.call(params.reranker, "info")) {
        reranker = await registry.lookupAction(`/reranker/${params.reranker.name}`);
    } else {
        reranker = params.reranker;
    }
    if (!reranker) {
        throw new Error("Unable to resolve the reranker");
    }
    const response = await reranker({
        query: typeof params.query === "string" ? import_retriever.Document.fromText(params.query) : params.query,
        documents: params.documents,
        options: params.options
    });
    return response.documents.map((d)=>new RankedDocument(d));
}
const CommonRerankerOptionsSchema = import_core.z.object({
    k: import_core.z.number().describe("Number of documents to rerank").optional()
});
function rerankerRef(options) {
    return {
        ...options
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    CommonRerankerOptionsSchema,
    RankedDocument,
    RankedDocumentDataSchema,
    RankedDocumentMetadataSchema,
    RerankerInfoSchema,
    defineReranker,
    rerank,
    rerankerRef
}); //# sourceMappingURL=reranker.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/types.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var types_exports = {};
__export(types_exports, {
    LlmResponseSchema: ()=>LlmResponseSchema,
    LlmStatsSchema: ()=>LlmStatsSchema,
    ToolCallSchema: ()=>ToolCallSchema,
    ToolSchema: ()=>ToolSchema,
    toToolWireFormat: ()=>toToolWireFormat
});
module.exports = __toCommonJS(types_exports);
var import_core = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-route] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-route] (ecmascript)");
const LlmStatsSchema = import_core.z.object({
    latencyMs: import_core.z.number().optional(),
    inputTokenCount: import_core.z.number().optional(),
    outputTokenCount: import_core.z.number().optional()
});
const ToolSchema = import_core.z.object({
    name: import_core.z.string(),
    description: import_core.z.string().optional(),
    schema: import_core.z.any()
});
const ToolCallSchema = import_core.z.object({
    toolName: import_core.z.string(),
    arguments: import_core.z.any()
});
const LlmResponseSchema = import_core.z.object({
    completion: import_core.z.string(),
    toolCalls: import_core.z.array(ToolCallSchema).optional(),
    stats: LlmStatsSchema
});
function toToolWireFormat(actions) {
    if (!actions) return void 0;
    return actions.map((a)=>{
        return {
            name: a.__action.name,
            description: a.__action.description,
            schema: {
                input: (0, import_schema.toJsonSchema)({
                    schema: a.__action.inputSchema,
                    jsonSchema: a.__action.inputJsonSchema
                }),
                output: (0, import_schema.toJsonSchema)({
                    schema: a.__action.outputSchema,
                    jsonSchema: a.__action.outputJsonSchema
                })
            }
        };
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    LlmResponseSchema,
    LlmStatsSchema,
    ToolCallSchema,
    ToolSchema,
    toToolWireFormat
}); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var index_exports = {};
__export(index_exports, {
    BaseDataPointSchema: ()=>import_evaluator.BaseDataPointSchema,
    Document: ()=>import_document.Document,
    DocumentDataSchema: ()=>import_document.DocumentDataSchema,
    EvalStatusEnum: ()=>import_evaluator.EvalStatusEnum,
    GenerateResponse: ()=>import_generate.GenerateResponse,
    GenerateResponseChunk: ()=>import_generate.GenerateResponseChunk,
    GenerateResponseChunkSchema: ()=>import_model.GenerateResponseChunkSchema,
    GenerationBlockedError: ()=>import_generate.GenerationBlockedError,
    GenerationCommonConfigSchema: ()=>import_model.GenerationCommonConfigSchema,
    GenerationResponseError: ()=>import_generate.GenerationResponseError,
    Message: ()=>import_message.Message,
    MessageSchema: ()=>import_model.MessageSchema,
    ModelRequestSchema: ()=>import_model.ModelRequestSchema,
    ModelResponseSchema: ()=>import_model.ModelResponseSchema,
    PartSchema: ()=>import_model.PartSchema,
    ResourceInputSchema: ()=>import_resource.ResourceInputSchema,
    ResourceOutputSchema: ()=>import_resource.ResourceOutputSchema,
    RoleSchema: ()=>import_model.RoleSchema,
    ToolInterruptError: ()=>import_tool.ToolInterruptError,
    asTool: ()=>import_tool.asTool,
    checkOperation: ()=>import_check_operation.checkOperation,
    defineHelper: ()=>import_prompt.defineHelper,
    defineInterrupt: ()=>import_tool.defineInterrupt,
    definePartial: ()=>import_prompt.definePartial,
    definePrompt: ()=>import_prompt.definePrompt,
    defineResource: ()=>import_resource.defineResource,
    defineTool: ()=>import_tool.defineTool,
    dynamicResource: ()=>import_resource.dynamicResource,
    embed: ()=>import_embedder.embed,
    embedderActionMetadata: ()=>import_embedder.embedderActionMetadata,
    embedderRef: ()=>import_embedder.embedderRef,
    evaluate: ()=>import_evaluator.evaluate,
    evaluatorRef: ()=>import_evaluator.evaluatorRef,
    generate: ()=>import_generate.generate,
    generateOperation: ()=>import_generate.generateOperation,
    generateStream: ()=>import_generate.generateStream,
    index: ()=>import_retriever.index,
    indexerRef: ()=>import_retriever.indexerRef,
    isDynamicResourceAction: ()=>import_resource.isDynamicResourceAction,
    isExecutablePrompt: ()=>import_prompt.isExecutablePrompt,
    loadPromptFolder: ()=>import_prompt.loadPromptFolder,
    modelActionMetadata: ()=>import_model.modelActionMetadata,
    modelRef: ()=>import_model.modelRef,
    prompt: ()=>import_prompt.prompt,
    rerank: ()=>import_reranker.rerank,
    rerankerRef: ()=>import_reranker.rerankerRef,
    retrieve: ()=>import_retriever.retrieve,
    retrieverRef: ()=>import_retriever.retrieverRef,
    tagAsPreamble: ()=>import_generate.tagAsPreamble,
    toGenerateRequest: ()=>import_generate.toGenerateRequest
});
module.exports = __toCommonJS(index_exports);
var import_check_operation = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/check-operation.js [app-route] (ecmascript)");
var import_document = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var import_embedder = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/embedder.js [app-route] (ecmascript)");
var import_evaluator = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/evaluator.js [app-route] (ecmascript)");
var import_generate = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/generate.js [app-route] (ecmascript)");
var import_message = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/message.js [app-route] (ecmascript)");
var import_model = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/model.js [app-route] (ecmascript)");
var import_prompt = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/prompt.js [app-route] (ecmascript)");
var import_reranker = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/reranker.js [app-route] (ecmascript)");
var import_resource = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/resource.js [app-route] (ecmascript)");
var import_retriever = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/retriever.js [app-route] (ecmascript)");
var import_tool = __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/tool.js [app-route] (ecmascript)");
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/types.js [app-route] (ecmascript)"), module.exports);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    BaseDataPointSchema,
    Document,
    DocumentDataSchema,
    EvalStatusEnum,
    GenerateResponse,
    GenerateResponseChunk,
    GenerateResponseChunkSchema,
    GenerationBlockedError,
    GenerationCommonConfigSchema,
    GenerationResponseError,
    Message,
    MessageSchema,
    ModelRequestSchema,
    ModelResponseSchema,
    PartSchema,
    ResourceInputSchema,
    ResourceOutputSchema,
    RoleSchema,
    ToolInterruptError,
    asTool,
    checkOperation,
    defineHelper,
    defineInterrupt,
    definePartial,
    definePrompt,
    defineResource,
    defineTool,
    dynamicResource,
    embed,
    embedderActionMetadata,
    embedderRef,
    evaluate,
    evaluatorRef,
    generate,
    generateOperation,
    generateStream,
    index,
    indexerRef,
    isDynamicResourceAction,
    isExecutablePrompt,
    loadPromptFolder,
    modelActionMetadata,
    modelRef,
    prompt,
    rerank,
    rerankerRef,
    retrieve,
    retrieverRef,
    tagAsPreamble,
    toGenerateRequest,
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/ai/lib/types.js [app-route] (ecmascript)")
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/model.mjs [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defineBackgroundModel": (()=>defineBackgroundModel),
    "defineModel": (()=>defineModel),
    "getBasicUsageStats": (()=>getBasicUsageStats),
    "modelActionMetadata": (()=>modelActionMetadata),
    "modelRef": (()=>modelRef),
    "resolveModel": (()=>resolveModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-route] (ecmascript) <exports>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/logging.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2f$middleware$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/model/middleware.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$generate$2f$action$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/generate/action.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
function defineModel(registry, options, runner) {
    const label = options.label || options.name;
    const middleware = getModelMiddleware(options);
    const act = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["defineAction"])(registry, {
        actionType: "model",
        name: options.name,
        description: label,
        inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateRequestSchema"],
        outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateResponseSchema"],
        metadata: {
            model: {
                label,
                customOptions: options.configSchema ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
                    schema: options.configSchema
                }) : void 0,
                versions: options.versions,
                supports: options.supports
            }
        },
        use: middleware
    }, (input, ctx)=>{
        const startTimeMs = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__["performance"].now();
        const secondParam = options.apiVersion === "v2" ? ctx : ctx.streamingRequested ? ctx.sendChunk : void 0;
        return runner(input, secondParam).then((response)=>{
            const timedResponse = {
                ...response,
                latencyMs: __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__["performance"].now() - startTimeMs
            };
            return timedResponse;
        });
    });
    Object.assign(act, {
        __configSchema: options.configSchema || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].unknown()
    });
    return act;
}
function defineBackgroundModel(registry, options) {
    const label = options.label || options.name;
    const middleware = getModelMiddleware(options);
    const act = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defineBackgroundAction"])(registry, {
        actionType: "background-model",
        name: options.name,
        description: label,
        inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateRequestSchema"],
        outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateResponseSchema"],
        metadata: {
            model: {
                label,
                customOptions: options.configSchema ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
                    schema: options.configSchema
                }) : void 0,
                versions: options.versions,
                supports: options.supports
            }
        },
        use: middleware,
        async start (request) {
            const startTimeMs = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__["performance"].now();
            const response = await options.start(request);
            Object.assign(response, {
                latencyMs: __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__["performance"].now() - startTimeMs
            });
            return response;
        },
        async check (op) {
            return options.check(op);
        },
        cancel: options.cancel ? async (op)=>{
            if (!options.cancel) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenkitError"]({
                    status: "UNIMPLEMENTED",
                    message: "cancel not implemented"
                });
            }
            return options.cancel(op);
        } : void 0
    });
    Object.assign(act, {
        __configSchema: options.configSchema || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].unknown()
    });
    return act;
}
function getModelMiddleware(options) {
    const middleware = [
        ...options.use || [],
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2f$middleware$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateSupport"])(options)
    ];
    if (!options?.supports?.context) middleware.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2f$middleware$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["augmentWithContext"])());
    const constratedSimulator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2f$middleware$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateConstrainedGeneration"])();
    middleware.push((req, next)=>{
        if (!options?.supports?.constrained || options?.supports?.constrained === "none" || options?.supports?.constrained === "no-tools" && (req.tools?.length ?? 0) > 0) {
            return constratedSimulator(req, next);
        }
        return next(req);
    });
    return middleware;
}
function modelActionMetadata({ name, info, configSchema, background }) {
    return {
        actionType: background ? "background-model" : "model",
        name,
        inputJsonSchema: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
            schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateRequestSchema"]
        }),
        outputJsonSchema: background ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
            schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OperationSchema"]
        }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
            schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerateResponseSchema"]
        }),
        metadata: {
            model: {
                ...info,
                customOptions: configSchema ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])({
                    schema: configSchema
                }) : void 0
            }
        }
    };
}
function modelRef(options) {
    const ref = {
        ...options
    };
    ref.withConfig = (cfg)=>{
        return modelRef({
            ...options,
            config: cfg
        });
    };
    ref.withVersion = (version)=>{
        return modelRef({
            ...options,
            version
        });
    };
    return ref;
}
function getBasicUsageStats(input, response) {
    const inputCounts = getPartCounts(input.flatMap((md)=>md.content));
    const outputCounts = getPartCounts(Array.isArray(response) ? response.flatMap((c)=>c.message.content) : response.content);
    return {
        inputCharacters: inputCounts.characters,
        inputImages: inputCounts.images,
        inputVideos: inputCounts.videos,
        inputAudioFiles: inputCounts.audio,
        outputCharacters: outputCounts.characters,
        outputImages: outputCounts.images,
        outputVideos: outputCounts.videos,
        outputAudioFiles: outputCounts.audio
    };
}
function getPartCounts(parts) {
    return parts.reduce((counts, part)=>{
        const isImage = part.media?.contentType?.startsWith("image") || part.media?.url?.startsWith("data:image");
        const isVideo = part.media?.contentType?.startsWith("video") || part.media?.url?.startsWith("data:video");
        const isAudio = part.media?.contentType?.startsWith("audio") || part.media?.url?.startsWith("data:audio");
        return {
            characters: counts.characters + (part.text?.length || 0),
            images: counts.images + (isImage ? 1 : 0),
            videos: counts.videos + (isVideo ? 1 : 0),
            audio: counts.audio + (isAudio ? 1 : 0)
        };
    }, {
        characters: 0,
        images: 0,
        videos: 0,
        audio: 0
    });
}
async function resolveModel(registry, model, options) {
    let out;
    let modelId;
    if (!model) {
        model = await registry.lookupValue("defaultModel", "defaultModel");
    }
    if (!model) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenkitError"]({
            status: "INVALID_ARGUMENT",
            message: "Must supply a `model` to `generate()` calls."
        });
    }
    if (typeof model === "string") {
        modelId = model;
        out = {
            modelAction: await lookupModel(registry, model)
        };
    } else if (model.hasOwnProperty("__action")) {
        modelId = model.__action.name;
        out = {
            modelAction: model
        };
    } else {
        const ref = model;
        modelId = ref.name;
        out = {
            modelAction: await lookupModel(registry, ref.name),
            config: {
                ...ref.config
            },
            version: ref.version
        };
    }
    if (!out.modelAction) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenkitError"]({
            status: "NOT_FOUND",
            message: `Model '${modelId}' not found`
        });
    }
    if (options?.warnDeprecated && out.modelAction.__action.metadata?.model?.stage === "deprecated") {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warn(`Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`);
    }
    return out;
}
async function lookupModel(registry, model) {
    return await registry.lookupAction(`/model/${model}`) || await registry.lookupAction(`/background-model/${model}`);
}
;
 //# sourceMappingURL=model.mjs.map
}}),
"[project]/node_modules/@genkit-ai/ai/lib/model.mjs [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/logging.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$perf_hooks__$5b$external$5d$__$28$node$3a$perf_hooks$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$document$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/document.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2d$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/model-types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2f$middleware$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/model/middleware.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$generate$2f$action$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/generate/action.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$ai$2f$lib$2f$model$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/ai/lib/model.mjs [app-route] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_%40genkit-ai_ai_lib_d9ad1ab4._.js.map