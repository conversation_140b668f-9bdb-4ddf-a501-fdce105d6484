{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Enhanced brand profile data extraction\r\n    const enhancedProfile = {\r\n      ...profile,\r\n      // Ensure brand colors are available\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      // Extract services information\r\n      servicesArray: typeof profile.services === 'string'\r\n        ? profile.services.split('\\n').filter(s => s.trim())\r\n        : Array.isArray(profile.services)\r\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\r\n          : [],\r\n      // Extract contact information for brand context\r\n      contactInfo: profile.contactInfo || {},\r\n      socialMedia: profile.socialMedia || {},\r\n    };\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    // Ensure model registry is initialized\r\n    if (!modelRegistry.isInitialized()) {\r\n      await modelRegistry.initialize();\r\n    }\r\n\r\n\r\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\r\n    const revo10Model = modelRegistry.getModel('revo-1.0');\r\n    if (!revo10Model) {\r\n      throw new Error('Revo 1.0 model not available');\r\n    }\r\n\r\n\r\n    const generationRequest = {\r\n      modelId: 'revo-1.0',\r\n      profile: enhancedProfile,\r\n      platform: platform,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\r\n      contentThemes: enhancedProfile.contentThemes || [],\r\n      writingTone: enhancedProfile.writingTone || 'professional',\r\n      targetAudience: enhancedProfile.targetAudience || 'General',\r\n      keyFeatures: enhancedProfile.keyFeatures || [],\r\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\r\n      services: enhancedProfile.services || [],\r\n      visualStyle: enhancedProfile.visualStyle || 'modern',\r\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\r\n      accentColor: enhancedProfile.accentColor || '#10B981',\r\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\r\n      logoDataUrl: enhancedProfile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples,\r\n      dayOfWeek: dayOfWeek,\r\n      currentDate: currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }]\r\n    };\r\n\r\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error || 'Content generation failed');\r\n    }\r\n\r\n    const postDetails = result.data;\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline || '',\r\n      callToAction: postDetails.callToAction || '',\r\n      // Revo 1.0 doesn't include these advanced features\r\n      contentVariants: undefined,\r\n      hashtagAnalysis: undefined,\r\n      marketIntelligence: undefined,\r\n      localContext: undefined,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n        includePeopleInDesigns,\r\n        useLocalLanguage,\r\n      });\r\n\r\n\r\n    } catch (gemini25Error) {\r\n\r\n      try {\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      } catch (openaiError) {\r\n\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      }\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n\r\n    if (targetArtifacts.length === 0) {\r\n    } else {\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgHsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Enhanced brand profile data extraction\r\n    const enhancedProfile = {\r\n      ...profile,\r\n      // Ensure brand colors are available\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      // Extract services information\r\n      servicesArray: typeof profile.services === 'string'\r\n        ? profile.services.split('\\n').filter(s => s.trim())\r\n        : Array.isArray(profile.services)\r\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\r\n          : [],\r\n      // Extract contact information for brand context\r\n      contactInfo: profile.contactInfo || {},\r\n      socialMedia: profile.socialMedia || {},\r\n    };\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    // Ensure model registry is initialized\r\n    if (!modelRegistry.isInitialized()) {\r\n      await modelRegistry.initialize();\r\n    }\r\n\r\n\r\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\r\n    const revo10Model = modelRegistry.getModel('revo-1.0');\r\n    if (!revo10Model) {\r\n      throw new Error('Revo 1.0 model not available');\r\n    }\r\n\r\n\r\n    const generationRequest = {\r\n      modelId: 'revo-1.0',\r\n      profile: enhancedProfile,\r\n      platform: platform,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\r\n      contentThemes: enhancedProfile.contentThemes || [],\r\n      writingTone: enhancedProfile.writingTone || 'professional',\r\n      targetAudience: enhancedProfile.targetAudience || 'General',\r\n      keyFeatures: enhancedProfile.keyFeatures || [],\r\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\r\n      services: enhancedProfile.services || [],\r\n      visualStyle: enhancedProfile.visualStyle || 'modern',\r\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\r\n      accentColor: enhancedProfile.accentColor || '#10B981',\r\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\r\n      logoDataUrl: enhancedProfile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples,\r\n      dayOfWeek: dayOfWeek,\r\n      currentDate: currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }]\r\n    };\r\n\r\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error || 'Content generation failed');\r\n    }\r\n\r\n    const postDetails = result.data;\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline || '',\r\n      callToAction: postDetails.callToAction || '',\r\n      // Revo 1.0 doesn't include these advanced features\r\n      contentVariants: undefined,\r\n      hashtagAnalysis: undefined,\r\n      marketIntelligence: undefined,\r\n      localContext: undefined,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n        includePeopleInDesigns,\r\n        useLocalLanguage,\r\n      });\r\n\r\n\r\n    } catch (gemini25Error) {\r\n\r\n      try {\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      } catch (openaiError) {\r\n\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      }\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n\r\n    if (targetArtifacts.length === 0) {\r\n    } else {\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4OsB,6BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/post-card.tsx"], "sourcesContent": ["// src/components/dashboard/post-card.tsx\r\n\"use client\";\r\n\r\nimport * as React from 'react';\r\nimport Image from \"next/image\";\r\nimport { Facebook, Instagram, Linkedin, MoreVertical, Pen, RefreshCw, Twitter, CalendarIcon, Download, Loader2, Video, ChevronLeft, ChevronRight, ImageOff, Copy, Eye } from \"lucide-react\";\r\nimport { toPng } from 'html-to-image';\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n} from \"@/components/ui/card\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport type { BrandProfile, GeneratedPost, Platform, PostVariant } from \"@/lib/types\";\r\nimport { format } from 'date-fns';\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Label } from '../ui/label';\r\nimport { Textarea } from '../ui/textarea';\r\nimport { Input } from '../ui/input';\r\nimport { generateContentAction, generateVideoContentAction } from '@/app/actions';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { cn } from '@/lib/utils';\r\nimport { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '../ui/carousel';\r\n\r\n// Helper function to validate URLs\r\nconst isValidUrl = (url: string): boolean => {\r\n  if (!url || typeof url !== 'string') {\r\n    return false;\r\n  }\r\n\r\n  // Handle compression placeholders\r\n  if (url === '[COMPRESSED_IMAGE]' || url === '[TRUNCATED]' || url.includes('[') && url.includes(']')) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    // Check for data URLs (base64 images)\r\n    if (url.startsWith('data:')) {\r\n      return url.includes('base64,') || url.includes('charset=');\r\n    }\r\n\r\n    // Check for HTTP/HTTPS URLs\r\n    const parsedUrl = new URL(url);\r\n    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';\r\n  } catch (error) {\r\n    // Don't log compression placeholders as errors\r\n    if (!url.includes('[') || !url.includes(']')) {\r\n    }\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to detect image format from data URL\r\n */\r\nfunction getImageFormatFromDataUrl(dataUrl: string): { format: string; extension: string } {\r\n  if (dataUrl.startsWith('data:image/svg+xml')) {\r\n    return { format: 'svg', extension: 'svg' };\r\n  } else if (dataUrl.startsWith('data:image/png;base64,')) {\r\n    return { format: 'png', extension: 'png' };\r\n  } else if (dataUrl.startsWith('data:image/jpeg;base64,') || dataUrl.startsWith('data:image/jpg;base64,')) {\r\n    return { format: 'jpeg', extension: 'jpg' };\r\n  } else if (dataUrl.startsWith('data:image/webp;base64,')) {\r\n    return { format: 'webp', extension: 'webp' };\r\n  }\r\n  return { format: 'png', extension: 'png' }; // default fallback\r\n}\r\n\r\nconst platformIcons: { [key in Platform]: React.ReactElement } = {\r\n  Facebook: <Facebook className=\"h-4 w-4\" />,\r\n  Instagram: <Instagram className=\"h-4 w-4\" />,\r\n  LinkedIn: <Linkedin className=\"h-4 w-4\" />,\r\n  Twitter: <Twitter className=\"h-4 w-4\" />,\r\n};\r\n\r\ntype PostCardProps = {\r\n  post: GeneratedPost;\r\n  brandProfile: BrandProfile;\r\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\r\n};\r\n\r\nexport function PostCard({ post, brandProfile, onPostUpdated }: PostCardProps) {\r\n  const [isEditing, setIsEditing] = React.useState(false);\r\n  const [isRegenerating, setIsRegenerating] = React.useState(false);\r\n  const [isGeneratingVideo, setIsGeneratingVideo] = React.useState(false);\r\n  const [editedContent, setEditedContent] = React.useState(post.content);\r\n  const [editedHashtags, setEditedHashtags] = React.useState(post.hashtags);\r\n  const [videoUrl, setVideoUrl] = React.useState<string | undefined>(post.videoUrl);\r\n  const [showVideoDialog, setShowVideoDialog] = React.useState(false);\r\n  const [showImagePreview, setShowImagePreview] = React.useState(false);\r\n  const [previewImageUrl, setPreviewImageUrl] = React.useState<string>('');\r\n  // Ensure variants array exists and has at least one item\r\n  const safeVariants = post.variants && post.variants.length > 0 ? post.variants : [{\r\n    platform: (post.platform || 'instagram') as Platform,\r\n    imageUrl: post.imageUrl || ''\r\n  }];\r\n\r\n  const [activeTab, setActiveTab] = React.useState<Platform>(safeVariants[0]?.platform || 'instagram');\r\n  const downloadRefs = React.useRef<Record<Platform, HTMLDivElement | null>>({} as Record<Platform, HTMLDivElement | null>);\r\n\r\n  // Check if this is a Revo 2.0 post (single platform)\r\n  const isRevo2Post = post.id?.startsWith('revo2-') || safeVariants.length === 1;\r\n\r\n  const formattedDate = React.useMemo(() => {\r\n    try {\r\n      const date = new Date(post.date);\r\n      if (isNaN(date.getTime())) {\r\n        // If date is invalid, use current date\r\n        return format(new Date(), 'MMM d, yyyy');\r\n      }\r\n      return format(date, 'MMM d, yyyy');\r\n    } catch (error) {\r\n      // Fallback to current date if any error occurs\r\n      return format(new Date(), 'MMM d, yyyy');\r\n    }\r\n  }, [post.date]);\r\n  const { toast } = useToast();\r\n\r\n  // Platform-specific dimensions - MUST match backend Revo 2.0 generation\r\n  const getPlatformDimensions = React.useCallback((platform: Platform) => {\r\n    switch (platform.toLowerCase()) {\r\n      case 'instagram':\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n      case 'facebook':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'twitter':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'linkedin':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'tiktok':\r\n        return { width: 1080, height: 1920, aspectClass: 'aspect-[9/16]' };\r\n      default:\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n    }\r\n  }, []);\r\n\r\n  // Copy functionality\r\n  const handleCopyCaption = React.useCallback(async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(post.content);\r\n      toast({\r\n        title: \"Caption Copied!\",\r\n        description: \"The caption has been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the caption. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.content, toast]);\r\n\r\n  const handleCopyHashtags = React.useCallback(async () => {\r\n    try {\r\n      const hashtagsText = typeof post.hashtags === 'string' ? post.hashtags : post.hashtags?.join(' ') || '';\r\n      await navigator.clipboard.writeText(hashtagsText);\r\n      toast({\r\n        title: \"Hashtags Copied!\",\r\n        description: \"The hashtags have been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the hashtags. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.hashtags, toast]);\r\n\r\n  // Image preview functionality\r\n  const handleImagePreview = React.useCallback((imageUrl: string) => {\r\n    setPreviewImageUrl(imageUrl);\r\n    setShowImagePreview(true);\r\n  }, []);\r\n\r\n  const handleDownload = React.useCallback(async () => {\r\n    const activeVariant = safeVariants.find(v => v.platform === activeTab);\r\n\r\n    // First try to download the original HD image directly if URL is valid\r\n    if (activeVariant?.imageUrl && isValidUrl(activeVariant.imageUrl)) {\r\n      try {\r\n        // Check if it's a data URL (base64 encoded image)\r\n        if (activeVariant.imageUrl.startsWith('data:')) {\r\n          const { format, extension } = getImageFormatFromDataUrl(activeVariant.imageUrl);\r\n\r\n          // For social media posts, we need raster images (PNG/JPEG), not SVG\r\n          if (format === 'svg') {\r\n            // Fall through to the canvas conversion method below\r\n            // This will convert the SVG to a high-quality PNG\r\n          } else {\r\n            // Handle other data URL formats (PNG, JPEG, etc.) directly\r\n            const link = document.createElement('a');\r\n            link.href = activeVariant.imageUrl;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: `High-definition ${format.toUpperCase()} image downloaded successfully.`,\r\n            });\r\n            return;\r\n          }\r\n        } else {\r\n          // Handle regular HTTP/HTTPS URLs (not data URLs)\r\n          try {\r\n            const response = await fetch(activeVariant.imageUrl);\r\n            const blob = await response.blob();\r\n            const url = window.URL.createObjectURL(blob);\r\n\r\n            // Determine file extension based on content type\r\n            const contentType = response.headers.get('content-type') || blob.type;\r\n            let extension = 'png'; // default\r\n            if (contentType.includes('jpeg') || contentType.includes('jpg')) {\r\n              extension = 'jpg';\r\n            } else if (contentType.includes('webp')) {\r\n              extension = 'webp';\r\n            }\r\n\r\n            const link = document.createElement('a');\r\n            link.href = url;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n            window.URL.revokeObjectURL(url);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: \"High-definition image downloaded successfully.\",\r\n            });\r\n            return;\r\n          } catch (error) {\r\n            // Fall through to canvas conversion\r\n          }\r\n        }\r\n      } catch (error) {\r\n      }\r\n    }\r\n\r\n    // Fallback: Capture the displayed image with maximum quality settings\r\n    const nodeToCapture = downloadRefs.current[activeTab];\r\n    if (!nodeToCapture) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: \"Could not find the image element to download.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Check if we're converting an SVG enhanced design\r\n      const activeVariant = safeVariants.find(v => v.platform === activeTab);\r\n      const isSvgDataUrl = activeVariant?.imageUrl?.startsWith('data:image/svg+xml');\r\n      const platformDimensions = getPlatformDimensions(activeTab);\r\n\r\n      // Platform-specific optimized settings for social media posts\r\n      const socialMediaSettings = {\r\n        cacheBust: true,\r\n        canvasWidth: platformDimensions.width,\r\n        canvasHeight: platformDimensions.height,\r\n        pixelRatio: 3, // High DPI for crisp images\r\n        quality: 1.0, // Maximum quality\r\n        backgroundColor: '#ffffff', // White background for transparency\r\n        style: {\r\n          borderRadius: '0',\r\n          border: 'none',\r\n        }\r\n      };\r\n\r\n      // Enhanced settings for SVG conversion\r\n      if (isSvgDataUrl) {\r\n        socialMediaSettings.canvasWidth = platformDimensions.width;\r\n        socialMediaSettings.canvasHeight = platformDimensions.height;\r\n        socialMediaSettings.pixelRatio = 4; // Extra high DPI for SVG conversion\r\n      }\r\n\r\n      const dataUrl = await toPng(nodeToCapture, socialMediaSettings);\r\n\r\n      const link = document.createElement('a');\r\n      link.href = dataUrl;\r\n      link.download = `nevis-social-${post.id}-${activeTab}.png`;\r\n      link.click();\r\n\r\n      // Provide specific feedback based on content type\r\n      const successMessage = isSvgDataUrl\r\n        ? \"Enhanced design converted to PNG for social media use.\"\r\n        : \"High-definition image ready for social media posting.\";\r\n\r\n      toast({\r\n        title: \"Social Media Image Ready\",\r\n        description: successMessage,\r\n      });\r\n\r\n    } catch (err) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: `Could not download the image. Please try again. Error: ${(err as Error).message}`,\r\n      });\r\n    }\r\n  }, [post.id, activeTab, toast]);\r\n\r\n\r\n  const handleSaveChanges = async () => {\r\n    const updatedPost = {\r\n      ...post,\r\n      content: editedContent,\r\n      hashtags: editedHashtags,\r\n      status: 'edited' as const,\r\n    };\r\n    await onPostUpdated(updatedPost);\r\n    setIsEditing(false);\r\n    toast({\r\n      title: \"Post Updated\",\r\n      description: \"Your changes have been saved.\",\r\n    });\r\n  };\r\n\r\n  const handleRegenerate = async () => {\r\n    setIsRegenerating(true);\r\n    try {\r\n      const platform = safeVariants[0].platform;\r\n      const newPost = await generateContentAction(brandProfile, platform);\r\n      onPostUpdated({ ...newPost, id: post.id }); // Keep old id for replacement\r\n      toast({\r\n        title: \"Post Regenerated!\",\r\n        description: \"A new version of your post has been generated.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Regeneration Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsRegenerating(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateVideo = async () => {\r\n    if (!post.catchyWords) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Cannot Generate Video\",\r\n        description: \"The post is missing the required catchy words.\",\r\n      });\r\n      return;\r\n    }\r\n    setIsGeneratingVideo(true);\r\n    try {\r\n      const result = await generateVideoContentAction(brandProfile, post.catchyWords, post.content);\r\n      const newVideoUrl = result.videoUrl;\r\n      setVideoUrl(newVideoUrl);\r\n      await onPostUpdated({ ...post, videoUrl: newVideoUrl });\r\n      setShowVideoDialog(true);\r\n      toast({\r\n        title: \"Video Generated!\",\r\n        description: \"Your video is ready to be viewed.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Video Generation Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsGeneratingVideo(false);\r\n    }\r\n  };\r\n\r\n  const activeVariant = safeVariants.find(v => v.platform === activeTab) || safeVariants[0];\r\n\r\n  return (\r\n    <>\r\n      <Card className=\"flex flex-col w-full\">\r\n        <CardHeader className=\"flex-row items-center justify-between gap-4 p-4\">\r\n          <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n            <CalendarIcon className=\"h-4 w-4\" />\r\n            <span>{formattedDate}</span>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button size=\"icon\" variant=\"ghost\" className=\"h-6 w-6\" disabled={isRegenerating || isGeneratingVideo}>\r\n                <MoreVertical className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={() => setIsEditing(true)}>\r\n                <Pen className=\"mr-2 h-4 w-4\" />\r\n                Edit Text\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleRegenerate} disabled={isRegenerating}>\r\n                {isRegenerating ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Regenerate Image\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleGenerateVideo} disabled={isGeneratingVideo}>\r\n                {isGeneratingVideo ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <Video className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Generate Video\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleDownload}>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download Image\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </CardHeader>\r\n        <CardContent className=\"flex-grow space-y-4 p-4 pt-0\">\r\n          {isRevo2Post ? (\r\n            // Revo 2.0 single-platform layout with platform icon at top left\r\n            <div className=\"space-y-4\">\r\n              {/* Platform Icon Header - Left aligned */}\r\n              <div className=\"flex items-center justify-start p-3 bg-muted/30 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  {platformIcons[safeVariants[0]?.platform || 'instagram']}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Single Image Display - Platform-specific dimensions */}\r\n              {(() => {\r\n                const variant = safeVariants[0];\r\n                const dimensions = getPlatformDimensions(variant?.platform || 'instagram');\r\n\r\n                return (\r\n                  <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                    {(isRegenerating || isGeneratingVideo) && (\r\n                      <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                        <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                        <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                      </div>\r\n                    )}\r\n                    <div ref={el => (downloadRefs.current[variant?.platform || 'instagram'] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                      {variant?.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                        <div\r\n                          className=\"relative h-full w-full cursor-pointer\"\r\n                          onClick={() => handleImagePreview(variant.imageUrl)}\r\n                        >\r\n                          <Image\r\n                            alt={`Generated post image for ${variant.platform}`}\r\n                            className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                            height={dimensions.height}\r\n                            src={variant.imageUrl}\r\n                            data-ai-hint=\"social media post\"\r\n                            width={dimensions.width}\r\n                            crossOrigin=\"anonymous\"\r\n                            unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                          />\r\n                          {/* Preview overlay */}\r\n                          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                            <div className=\"bg-white/90 rounded-full p-2\">\r\n                              <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex h-full w-full items-center justify-center bg-muted flex-col gap-2\">\r\n                          <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                          {variant?.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                            <div className=\"absolute bottom-2 left-2 right-2\">\r\n                              <div className=\"text-xs text-red-500 bg-white/90 p-2 rounded\">\r\n                                {variant.imageUrl.includes('[') && variant.imageUrl.includes(']') ? (\r\n                                  <div>\r\n                                    <p className=\"font-medium\">Image temporarily unavailable</p>\r\n                                    <p className=\"text-gray-600 mt-1\">\r\n                                      {variant.imageUrl.includes('Large image data removed')\r\n                                        ? 'Image was too large for storage. Try regenerating.'\r\n                                        : 'Image data was optimized for storage.'\r\n                                      }\r\n                                    </p>\r\n                                  </div>\r\n                                ) : (\r\n                                  <p>Invalid image URL</p>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          ) : (\r\n            // Multi-platform tab layout for Revo 1.0/1.5\r\n            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as Platform)} className=\"w-full\">\r\n              <TabsList className=\"grid w-full grid-cols-4\">\r\n                {safeVariants.map(variant => (\r\n                  <TabsTrigger key={variant.platform} value={variant.platform}>\r\n                    {platformIcons[variant.platform]}\r\n                  </TabsTrigger>\r\n                ))}\r\n              </TabsList>\r\n              {safeVariants.map(variant => {\r\n                const dimensions = getPlatformDimensions(variant.platform);\r\n                return (\r\n                  <TabsContent key={variant.platform} value={variant.platform}>\r\n                    <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                      {(isRegenerating || isGeneratingVideo) && (\r\n                        <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                          <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                        </div>\r\n                      )}\r\n                      <div ref={el => (downloadRefs.current[variant.platform] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                        {variant.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                          <div\r\n                            className=\"relative h-full w-full cursor-pointer\"\r\n                            onClick={() => handleImagePreview(variant.imageUrl)}\r\n                          >\r\n                            <Image\r\n                              alt={`Generated post image for ${variant.platform}`}\r\n                              className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                              height={dimensions.height}\r\n                              src={variant.imageUrl}\r\n                              data-ai-hint=\"social media post\"\r\n                              width={dimensions.width}\r\n                              crossOrigin=\"anonymous\"\r\n                              unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                            />\r\n                            {/* Preview overlay */}\r\n                            <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                              <div className=\"bg-white/90 rounded-full p-2\">\r\n                                <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex h-full w-full items-center justify-center bg-muted\">\r\n                            <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                            {variant.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                              <div className=\"absolute bottom-2 left-2 right-2\">\r\n                                <p className=\"text-xs text-red-500 bg-white/90 p-1 rounded\">\r\n                                  Invalid image URL\r\n                                </p>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n                );\r\n              })}\r\n            </Tabs>\r\n          )}\r\n\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-start justify-between gap-2\">\r\n              <p className=\"text-sm text-foreground line-clamp-4 flex-1\">{post.content}</p>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={handleCopyCaption}\r\n                className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n                title=\"Copy caption\"\r\n              >\r\n                <Copy className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter className=\"p-4 pt-0\">\r\n          <div className=\"flex items-start justify-between gap-2\">\r\n            <div className=\"flex flex-wrap gap-1 flex-1\">\r\n              {post.hashtags && (() => {\r\n                // Handle both string and array formats for hashtags\r\n                const hashtagsArray = typeof post.hashtags === 'string'\r\n                  ? post.hashtags.split(\" \")\r\n                  : Array.isArray(post.hashtags)\r\n                    ? post.hashtags\r\n                    : [];\r\n\r\n                return hashtagsArray.map((tag, index) => (\r\n                  <Badge key={index} variant=\"secondary\" className=\"font-normal\">\r\n                    {tag}\r\n                  </Badge>\r\n                ));\r\n              })()}\r\n              {!post.hashtags && (\r\n                <Badge variant=\"secondary\" className=\"font-normal\">\r\n                  #enhanced #ai #design\r\n                </Badge>\r\n              )}\r\n            </div>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopyHashtags}\r\n              className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n              title=\"Copy hashtags\"\r\n            >\r\n              <Copy className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Edit Post Dialog */}\r\n      <Dialog open={isEditing} onOpenChange={setIsEditing}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Post</DialogTitle>\r\n            <DialogDescription>\r\n              Make changes to your post content and hashtags below. Click save when you're done.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"content\">Content</Label>\r\n              <Textarea\r\n                id=\"content\"\r\n                value={editedContent}\r\n                onChange={(e) => setEditedContent(e.target.value)}\r\n                className=\"h-32\"\r\n              />\r\n            </div>\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"hashtags\">Hashtags</Label>\r\n              <Input\r\n                id=\"hashtags\"\r\n                value={editedHashtags}\r\n                onChange={(e) => setEditedHashtags(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsEditing(false)}>Cancel</Button>\r\n            <Button onClick={handleSaveChanges}>Save Changes</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* View Video Dialog */}\r\n      <Dialog open={showVideoDialog} onOpenChange={setShowVideoDialog}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Generated Video</DialogTitle>\r\n            <DialogDescription>\r\n              Here is the video generated for your post. You can download it from here.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"my-4\">\r\n            {videoUrl ? (\r\n              <video controls autoPlay src={videoUrl} className=\"w-full rounded-md\" />\r\n            ) : (\r\n              <p>No video available.</p>\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowVideoDialog(false)}>Close</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Image Preview Modal */}\r\n      <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[90vh] p-2\">\r\n          <DialogHeader className=\"pb-2\">\r\n            <DialogTitle>Image Preview</DialogTitle>\r\n            <DialogDescription>\r\n              Click and drag to pan, scroll to zoom\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"flex items-center justify-center max-h-[70vh] overflow-hidden\">\r\n            {previewImageUrl && (\r\n              <img\r\n                src={previewImageUrl}\r\n                alt=\"Post image preview\"\r\n                className=\"max-w-full max-h-full object-contain rounded-lg\"\r\n              />\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowImagePreview(false)}>\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAMA;AAQA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AArCA;;;;;;;;;;;;;;;;;;AAwCA,mCAAmC;AACnC,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,kCAAkC;IAClC,IAAI,QAAQ,wBAAwB,QAAQ,iBAAiB,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM;QACnG,OAAO;IACT;IAEA,IAAI;QACF,sCAAsC;QACtC,IAAI,IAAI,UAAU,CAAC,UAAU;YAC3B,OAAO,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC;QACjD;QAEA,4BAA4B;QAC5B,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;IAClE,EAAE,OAAO,OAAO;QACd,+CAA+C;QAC/C,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,CAC9C;QACA,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,OAAe;IAChD,IAAI,QAAQ,UAAU,CAAC,uBAAuB;QAC5C,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,2BAA2B;QACvD,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,8BAA8B,QAAQ,UAAU,CAAC,2BAA2B;QACxG,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAM;IAC5C,OAAO,IAAI,QAAQ,UAAU,CAAC,4BAA4B;QACxD,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAO;IAC7C;IACA,OAAO;QAAE,QAAQ;QAAO,WAAW;IAAM,GAAG,mBAAmB;AACjE;AAEA,MAAM,gBAA2D;IAC/D,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,yBAAW,6LAAC,+MAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IAChC,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,uBAAS,6LAAC,2MAAA,CAAA,UAAO;QAAC,WAAU;;;;;;AAC9B;AAQO,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAiB;;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,KAAK,OAAO;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,KAAK,QAAQ;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAsB,KAAK,QAAQ;IAChF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAU;IACrE,yDAAyD;IACzD,MAAM,eAAe,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,KAAK,QAAQ,GAAG;QAAC;YAChF,UAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;QAC7B;KAAE;IAEF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAY,YAAY,CAAC,EAAE,EAAE,YAAY;IACxF,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAA2C,CAAC;IAE5E,qDAAqD;IACrD,MAAM,cAAc,KAAK,EAAE,EAAE,WAAW,aAAa,aAAa,MAAM,KAAK;IAE7E,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;2CAAE;YAClC,IAAI;gBACF,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;gBAC/B,IAAI,MAAM,KAAK,OAAO,KAAK;oBACzB,uCAAuC;oBACvC,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;gBAC5B;gBACA,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,+CAA+C;gBAC/C,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;YAC5B;QACF;0CAAG;QAAC,KAAK,IAAI;KAAC;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,wEAAwE;IACxE,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;uDAAE,CAAC;YAC/C,OAAQ,SAAS,WAAW;gBAC1B,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;gBACnE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;gBACnE;oBACE,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;YACrE;QACF;sDAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;mDAAE;YAC1C,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAChD,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF;QACF;kDAAG;QAAC,KAAK,OAAO;QAAE;KAAM;IAExB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;oDAAE;YAC3C,IAAI;gBACF,MAAM,eAAe,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG,KAAK,QAAQ,EAAE,KAAK,QAAQ;gBACrG,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF;QACF;mDAAG;QAAC,KAAK,QAAQ;QAAE;KAAM;IAEzB,8BAA8B;IAC9B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;oDAAE,CAAC;YAC5C,mBAAmB;YACnB,oBAAoB;QACtB;mDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAAE;YACvC,MAAM,gBAAgB,aAAa,IAAI;sEAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;YAE5D,uEAAuE;YACvE,IAAI,eAAe,YAAY,WAAW,cAAc,QAAQ,GAAG;gBACjE,IAAI;oBACF,kDAAkD;oBAClD,IAAI,cAAc,QAAQ,CAAC,UAAU,CAAC,UAAU;wBAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,0BAA0B,cAAc,QAAQ;wBAE9E,oEAAoE;wBACpE,IAAI,WAAW,OAAO;wBACpB,qDAAqD;wBACrD,kDAAkD;wBACpD,OAAO;4BACL,2DAA2D;4BAC3D,MAAM,OAAO,SAAS,aAAa,CAAC;4BACpC,KAAK,IAAI,GAAG,cAAc,QAAQ;4BAClC,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;4BACnE,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,KAAK,KAAK;4BACV,SAAS,IAAI,CAAC,WAAW,CAAC;4BAE1B,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,gBAAgB,EAAE,OAAO,WAAW,GAAG,+BAA+B,CAAC;4BACvF;4BACA;wBACF;oBACF,OAAO;wBACL,iDAAiD;wBACjD,IAAI;4BACF,MAAM,WAAW,MAAM,MAAM,cAAc,QAAQ;4BACnD,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;4BAEvC,iDAAiD;4BACjD,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,IAAI;4BACrE,IAAI,YAAY,OAAO,UAAU;4BACjC,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,QAAQ;gCAC/D,YAAY;4BACd,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;gCACvC,YAAY;4BACd;4BAEA,MAAM,OAAO,SAAS,aAAa,CAAC;4BACpC,KAAK,IAAI,GAAG;4BACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;4BACnE,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,KAAK,KAAK;4BACV,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;4BAE3B,MAAM;gCACJ,OAAO;gCACP,aAAa;4BACf;4BACA;wBACF,EAAE,OAAO,OAAO;wBACd,oCAAoC;wBACtC;oBACF;gBACF,EAAE,OAAO,OAAO,CAChB;YACF;YAEA,sEAAsE;YACtE,MAAM,gBAAgB,aAAa,OAAO,CAAC,UAAU;YACrD,IAAI,CAAC,eAAe;gBAClB,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;YAEA,IAAI;gBACF,mDAAmD;gBACnD,MAAM,gBAAgB,aAAa,IAAI;0EAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;gBAC5D,MAAM,eAAe,eAAe,UAAU,WAAW;gBACzD,MAAM,qBAAqB,sBAAsB;gBAEjD,8DAA8D;gBAC9D,MAAM,sBAAsB;oBAC1B,WAAW;oBACX,aAAa,mBAAmB,KAAK;oBACrC,cAAc,mBAAmB,MAAM;oBACvC,YAAY;oBACZ,SAAS;oBACT,iBAAiB;oBACjB,OAAO;wBACL,cAAc;wBACd,QAAQ;oBACV;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,cAAc;oBAChB,oBAAoB,WAAW,GAAG,mBAAmB,KAAK;oBAC1D,oBAAoB,YAAY,GAAG,mBAAmB,MAAM;oBAC5D,oBAAoB,UAAU,GAAG,GAAG,oCAAoC;gBAC1E;gBAEA,MAAM,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,QAAK,AAAD,EAAE,eAAe;gBAE3C,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC;gBAC1D,KAAK,KAAK;gBAEV,kDAAkD;gBAClD,MAAM,iBAAiB,eACnB,2DACA;gBAEJ,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YAEF,EAAE,OAAO,KAAK;gBACZ,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa,CAAC,uDAAuD,EAAE,AAAC,IAAc,OAAO,EAAE;gBACjG;YACF;QACF;+CAAG;QAAC,KAAK,EAAE;QAAE;QAAW;KAAM;IAG9B,MAAM,oBAAoB;QACxB,MAAM,cAAc;YAClB,GAAG,IAAI;YACP,SAAS;YACT,UAAU;YACV,QAAQ;QACV;QACA,MAAM,cAAc;QACpB,aAAa;QACb,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,YAAY,CAAC,EAAE,CAAC,QAAQ;YACzC,MAAM,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;YAC1D,cAAc;gBAAE,GAAG,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC,IAAI,8BAA8B;YAC1E,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QACA,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,6BAA0B,AAAD,EAAE,cAAc,KAAK,WAAW,EAAE,KAAK,OAAO;YAC5F,MAAM,cAAc,OAAO,QAAQ;YACnC,YAAY;YACZ,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY;YACrD,mBAAmB;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,YAAY,CAAC,EAAE;IAEzF,qBACE;;0BACE,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;kDAAM;;;;;;;;;;;;0CAET,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAQ,WAAU;4CAAU,UAAU,kBAAkB;sDAClF,cAAA,6LAAC,6NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,aAAa;;kEAC5C,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAkB,UAAU;;oDACpD,+BACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACrB;;;;;;;0DAGJ,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAqB,UAAU;;oDACvD,kCACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACjB;;;;;;;0DAGJ,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,cACC,iEAAiE;0CACjE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,YAAY;;;;;;;;;;;oCAK3D,CAAC;wCACA,MAAM,UAAU,YAAY,CAAC,EAAE;wCAC/B,MAAM,aAAa,sBAAsB,SAAS,YAAY;wCAE9D,qBACE,6LAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;gDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAW,iBAAiB,0BAA0B;;;;;;;;;;;;8DAG1E,6LAAC;oDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,SAAS,YAAY,YAAY,GAAG;oDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;8DAC1K,SAAS,YAAY,WAAW,QAAQ,QAAQ,kBAC/C,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;0EAElD,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;gEACnD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;gEACtH,QAAQ,WAAW,MAAM;gEACzB,KAAK,QAAQ,QAAQ;gEACrB,gBAAa;gEACb,OAAO,WAAW,KAAK;gEACvB,aAAY;gEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;0EAG3C,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;6EAKrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,SAAS,YAAY,CAAC,WAAW,QAAQ,QAAQ,mBAChD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,qBAC3D,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,6LAAC;gFAAE,WAAU;0FACV,QAAQ,QAAQ,CAAC,QAAQ,CAAC,8BACvB,uDACA;;;;;;;;;;;6FAKR,6LAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAUvB,CAAC;;;;;;uCAGH,6CAA6C;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAW,eAAe,CAAC,IAAM,aAAa;gCAAgB,WAAU;;kDACnF,6LAAC,mIAAA,CAAA,WAAQ;wCAAC,WAAU;kDACjB,aAAa,GAAG,CAAC,CAAA,wBAChB,6LAAC,mIAAA,CAAA,cAAW;gDAAwB,OAAO,QAAQ,QAAQ;0DACxD,aAAa,CAAC,QAAQ,QAAQ,CAAC;+CADhB,QAAQ,QAAQ;;;;;;;;;;oCAKrC,aAAa,GAAG,CAAC,CAAA;wCAChB,MAAM,aAAa,sBAAsB,QAAQ,QAAQ;wCACzD,qBACE,6LAAC,mIAAA,CAAA,cAAW;4CAAwB,OAAO,QAAQ,QAAQ;sDACzD,cAAA,6LAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;oDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;gEAAK,WAAU;0EAAW,iBAAiB,0BAA0B;;;;;;;;;;;;kEAG1E,6LAAC;wDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,QAAQ,QAAQ,CAAC,GAAG;wDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;kEAC1J,QAAQ,QAAQ,IAAI,WAAW,QAAQ,QAAQ,kBAC9C,6LAAC;4DACC,WAAU;4DACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;8EAElD,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;oEACnD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;oEACtH,QAAQ,WAAW,MAAM;oEACzB,KAAK,QAAQ,QAAQ;oEACrB,gBAAa;oEACb,OAAO,WAAW,KAAK;oEACvB,aAAY;oEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;8EAG3C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;iFAKrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,QAAQ,QAAQ,IAAI,CAAC,WAAW,QAAQ,QAAQ,mBAC/C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CApCxD,QAAQ,QAAQ;;;;;oCA+CtC;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA+C,KAAK,OAAO;;;;;;sDACxE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxB,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ,IAAI,CAAC;4CACjB,oDAAoD;4CACpD,MAAM,gBAAgB,OAAO,KAAK,QAAQ,KAAK,WAC3C,KAAK,QAAQ,CAAC,KAAK,CAAC,OACpB,MAAM,OAAO,CAAC,KAAK,QAAQ,IACzB,KAAK,QAAQ,GACb,EAAE;4CAER,OAAO,cAAc,GAAG,CAAC,CAAC,KAAK,sBAC7B,6LAAC,oIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;wCAIhB,CAAC;wCACA,CAAC,KAAK,QAAQ,kBACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAc;;;;;;;;;;;;8CAKvD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAW,cAAc;0BACrC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAIvD,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,aAAa;8CAAQ;;;;;;8CAC9D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAiB,cAAc;0BAC3C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACZ,yBACC,6LAAC;gCAAM,QAAQ;gCAAC,QAAQ;gCAAC,KAAK;gCAAU,WAAU;;;;;qDAElD,6LAAC;0CAAE;;;;;;;;;;;sCAGP,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAM1E,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACZ,iCACC,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAIhB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,oBAAoB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;GArmBgB;;QAmCI,+HAAA,CAAA,WAAQ;;;KAnCZ", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { modelRegistry } from \"@/ai/models/registry/model-registry\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\";\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Enhanced brand profile data extraction\r\n    const enhancedProfile = {\r\n      ...profile,\r\n      // Ensure brand colors are available\r\n      primaryColor: profile.primaryColor || '#3B82F6',\r\n      accentColor: profile.accentColor || '#10B981',\r\n      backgroundColor: profile.backgroundColor || '#F8FAFC',\r\n      // Extract services information\r\n      servicesArray: typeof profile.services === 'string'\r\n        ? profile.services.split('\\n').filter(s => s.trim())\r\n        : Array.isArray(profile.services)\r\n          ? profile.services.map(s => typeof s === 'string' ? s : s.name || s.description || '')\r\n          : [],\r\n      // Extract contact information for brand context\r\n      contactInfo: profile.contactInfo || {},\r\n      socialMedia: profile.socialMedia || {},\r\n    };\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    // Ensure model registry is initialized\r\n    if (!modelRegistry.isInitialized()) {\r\n      await modelRegistry.initialize();\r\n    }\r\n\r\n\r\n    // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview\r\n    const revo10Model = modelRegistry.getModel('revo-1.0');\r\n    if (!revo10Model) {\r\n      throw new Error('Revo 1.0 model not available');\r\n    }\r\n\r\n\r\n    const generationRequest = {\r\n      modelId: 'revo-1.0',\r\n      profile: enhancedProfile,\r\n      platform: platform,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      artifactIds: [], // Revo 1.0 doesn't support artifacts\r\n      contentThemes: enhancedProfile.contentThemes || [],\r\n      writingTone: enhancedProfile.writingTone || 'professional',\r\n      targetAudience: enhancedProfile.targetAudience || 'General',\r\n      keyFeatures: enhancedProfile.keyFeatures || [],\r\n      competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],\r\n      services: enhancedProfile.services || [],\r\n      visualStyle: enhancedProfile.visualStyle || 'modern',\r\n      primaryColor: enhancedProfile.primaryColor || '#3B82F6',\r\n      accentColor: enhancedProfile.accentColor || '#10B981',\r\n      backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',\r\n      logoDataUrl: enhancedProfile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples,\r\n      dayOfWeek: dayOfWeek,\r\n      currentDate: currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }]\r\n    };\r\n\r\n    const result = await revo10Model.contentGenerator.generateContent(generationRequest);\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error || 'Content generation failed');\r\n    }\r\n\r\n    const postDetails = result.data;\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline || '',\r\n      callToAction: postDetails.callToAction || '',\r\n      // Revo 1.0 doesn't include these advanced features\r\n      contentVariants: undefined,\r\n      hashtagAnalysis: undefined,\r\n      marketIntelligence: undefined,\r\n      localContext: undefined,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n        includePeopleInDesigns,\r\n        useLocalLanguage,\r\n      });\r\n\r\n\r\n    } catch (gemini25Error) {\r\n\r\n      try {\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      } catch (openaiError) {\r\n\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n      }\r\n    }\r\n\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true,\r\n  includePeopleInDesigns: boolean = true,\r\n  useLocalLanguage: boolean = false\r\n): Promise<GeneratedPost> {\r\n  try {\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n\r\n    if (targetArtifacts.length === 0) {\r\n    } else {\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsbsB,qCAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-brand-profiles.ts"], "sourcesContent": ["// Hook for managing brand profiles with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { brandProfileFirebaseService } from '@/lib/firebase/services/brand-profile-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport interface BrandProfilesState {\r\n  profiles: CompleteBrandProfile[];\r\n  currentProfile: CompleteBrandProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useBrandProfiles() {\r\n  const userId = useUserId();\r\n  const [state, setState] = useState<BrandProfilesState>({\r\n    profiles: [],\r\n    currentProfile: null,\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load brand profiles\r\n  const loadProfiles = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, profiles: [], currentProfile: null }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      // Try to load from Firestore, fallback to localStorage\r\n      let profiles: CompleteBrandProfile[] = [];\r\n      try {\r\n        profiles = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n      } catch (firebaseError) {\r\n        // Fallback to localStorage\r\n        const stored = localStorage.getItem('completeBrandProfile');\r\n        if (stored) {\r\n          const profile = JSON.parse(stored);\r\n          profiles = [profile];\r\n        }\r\n      }\r\n\r\n      const currentProfile = profiles.length > 0 ? profiles[0] : null;\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles,\r\n        currentProfile,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\r\n      }));\r\n    }\r\n  }, [userId]);\r\n\r\n  // Save brand profile\r\n  const saveProfile = useCallback(async (profile: CompleteBrandProfile): Promise<string> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to save profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      const profileId = await brandProfileFirebaseService.saveBrandProfile(profile, userId);\r\n\r\n      // Reload profiles to get the updated list\r\n      await loadProfiles();\r\n\r\n      setState(prev => ({ ...prev, saving: false }));\r\n      return profileId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, loadProfiles]);\r\n\r\n  // Update brand profile\r\n  const updateProfile = useCallback(async (\r\n    profileId: string,\r\n    updates: Partial<CompleteBrandProfile>\r\n  ): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to update profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      await brandProfileFirebaseService.updateBrandProfile(profileId, updates);\r\n\r\n      // Update local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.map(p =>\r\n          p.id === profileId ? { ...p, ...updates } : p\r\n        ),\r\n        currentProfile: prev.currentProfile?.id === profileId\r\n          ? { ...prev.currentProfile, ...updates }\r\n          : prev.currentProfile,\r\n        saving: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to update profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Delete brand profile\r\n  const deleteProfile = useCallback(async (profileId: string): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to delete profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, error: null }));\r\n\r\n      await brandProfileFirebaseService.delete(profileId);\r\n\r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.filter(p => p.id !== profileId),\r\n        currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Set current profile\r\n  const setCurrentProfile = useCallback((profile: CompleteBrandProfile | null) => {\r\n    setState(prev => {\r\n      return { ...prev, currentProfile: profile };\r\n    });\r\n  }, []);\r\n\r\n  // Get profile by ID\r\n  const getProfileById = useCallback(async (profileId: string): Promise<CompleteBrandProfile | null> => {\r\n    try {\r\n      return await brandProfileFirebaseService.getBrandProfileById(profileId);\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Load profiles when userId changes\r\n  useEffect(() => {\r\n    loadProfiles();\r\n  }, [loadProfiles]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = brandProfileFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (profiles) => {\r\n        setState(prev => {\r\n          // Preserve the current profile if it still exists in the updated profiles\r\n          let preservedCurrentProfile = prev.currentProfile;\r\n\r\n          if (prev.currentProfile) {\r\n            // Check if current profile still exists in the updated list\r\n            const stillExists = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n            if (!stillExists) {\r\n              preservedCurrentProfile = null;\r\n            } else {\r\n              // Update with the latest version of the current profile\r\n              const updatedProfile = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n              if (updatedProfile) {\r\n                preservedCurrentProfile = updatedProfile;\r\n              }\r\n            }\r\n          }\r\n\r\n          // Only auto-select first profile if there's no current profile at all AND this is the initial load\r\n          const finalCurrentProfile = preservedCurrentProfile ||\r\n            (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);\r\n\r\n          if (finalCurrentProfile && !prev.currentProfile) {\r\n          }\r\n\r\n          return {\r\n            ...prev,\r\n            profiles,\r\n            currentProfile: finalCurrentProfile,\r\n          };\r\n        });\r\n      },\r\n      { orderBy: 'updatedAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId]);\r\n\r\n  return {\r\n    ...state,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    getProfileById,\r\n    reload: loadProfiles,\r\n  };\r\n}\r\n\r\n// Hook for getting the current/latest brand profile\r\nexport function useCurrentBrandProfile() {\r\n  const { currentProfile, loading, error } = useBrandProfiles();\r\n\r\n  return {\r\n    profile: currentProfile,\r\n    loading,\r\n    error,\r\n  };\r\n}\r\n\r\n// Hook for checking if user has a complete brand profile\r\nexport function useHasCompleteBrandProfile(): boolean {\r\n  const { currentProfile, loading } = useBrandProfiles();\r\n\r\n  if (loading || !currentProfile) return false;\r\n\r\n  // Check if profile has required fields\r\n  const requiredFields = [\r\n    'businessName',\r\n    'businessType',\r\n    'location',\r\n    'description',\r\n    'services',\r\n  ];\r\n\r\n  return requiredFields.every(field => {\r\n    const value = currentProfile[field as keyof CompleteBrandProfile];\r\n    return value && (\r\n      typeof value === 'string' ? value.trim().length > 0 :\r\n        Array.isArray(value) ? value.length > 0 :\r\n          true\r\n    );\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;;;;;AAWO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,UAAU,EAAE;QACZ,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,IAAI,CAAC,QAAQ;gBACX;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,UAAU,EAAE;4BAAE,gBAAgB;wBAAK,CAAC;;gBACjF;YACF;YAEA,IAAI;gBACF;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,uDAAuD;gBACvD,IAAI,WAAmC,EAAE;gBACzC,IAAI;oBACF,WAAW,MAAM,oKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;gBACpE,EAAE,OAAO,eAAe;oBACtB,2BAA2B;oBAC3B,MAAM,SAAS,aAAa,OAAO,CAAC;oBACpC,IAAI,QAAQ;wBACV,MAAM,UAAU,KAAK,KAAK,CAAC;wBAC3B,WAAW;4BAAC;yBAAQ;oBACtB;gBACF;gBAEA,MAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;gBAE3D;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;qDAAG;QAAC;KAAO;IAEX,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACrC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,YAAY,MAAM,oKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,SAAS;gBAE9E,0CAA0C;gBAC1C,MAAM;gBAEN;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAM,CAAC;;gBAC5C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;oDAAG;QAAC;QAAQ;KAAa;IAEzB,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAChC,WACA;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,oKAAA,CAAA,8BAA2B,CAAC,kBAAkB,CAAC,WAAW;gBAEhE,oCAAoC;gBACpC;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,GAAG;+EAAC,CAAA,IAC1B,EAAE,EAAE,KAAK,YAAY;wCAAE,GAAG,CAAC;wCAAE,GAAG,OAAO;oCAAC,IAAI;;4BAE9C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YACxC;gCAAE,GAAG,KAAK,cAAc;gCAAE,GAAG,OAAO;4BAAC,IACrC,KAAK,cAAc;4BACvB,QAAQ;wBACV,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YACvC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO;wBAAK,CAAC;;gBAE1C,MAAM,oKAAA,CAAA,8BAA2B,CAAC,MAAM,CAAC;gBAEzC,qBAAqB;gBACrB;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,MAAM;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;4BAC7C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YAAY,OAAO,KAAK,cAAc;wBACpF,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACrC;mEAAS,CAAA;oBACP,OAAO;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;oBAAQ;gBAC5C;;QACF;0DAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACxC,IAAI;gBACF,OAAO,MAAM,oKAAA,CAAA,8BAA2B,CAAC,mBAAmB,CAAC;YAC/D,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;uDAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,oKAAA,CAAA,8BAA2B,CAAC,qBAAqB,CACnE;0DACA,CAAC;oBACC;kEAAS,CAAA;4BACP,0EAA0E;4BAC1E,IAAI,0BAA0B,KAAK,cAAc;4BAEjD,IAAI,KAAK,cAAc,EAAE;gCACvB,4DAA4D;gCAC5D,MAAM,cAAc,SAAS,IAAI;0FAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;gCAC9E,IAAI,CAAC,aAAa;oCAChB,0BAA0B;gCAC5B,OAAO;oCACL,wDAAwD;oCACxD,MAAM,iBAAiB,SAAS,IAAI;iGAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;oCACjF,IAAI,gBAAgB;wCAClB,0BAA0B;oCAC5B;gCACF;4BACF;4BAEA,mGAAmG;4BACnG,MAAM,sBAAsB,2BAC1B,CAAC,CAAC,KAAK,cAAc,IAAI,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,IAAI;4BAEnE,IAAI,uBAAuB,CAAC,KAAK,cAAc,EAAE,CACjD;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP;gCACA,gBAAgB;4BAClB;wBACF;;gBACF;yDACA;gBAAE,SAAS;gBAAa,gBAAgB;YAAO;YAGjD,OAAO;QACT;qCAAG;QAAC;KAAO;IAEX,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GApNgB;;QACC,0IAAA,CAAA,YAAS;;;AAsNnB,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS;QACT;QACA;IACF;AACF;IARgB;;QAC6B;;;AAUtC,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAEpC,IAAI,WAAW,CAAC,gBAAgB,OAAO;IAEvC,uCAAuC;IACvC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,eAAe,KAAK,CAAC,CAAA;QAC1B,MAAM,QAAQ,cAAc,CAAC,MAAoC;QACjE,OAAO,SAAS,CACd,OAAO,UAAU,WAAW,MAAM,IAAI,GAAG,MAAM,GAAG,IAChD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IACpC,IACN;IACF;AACF;IAtBgB;;QACsB", "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-generated-posts.ts"], "sourcesContent": ["// Hook for managing generated posts with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { generatedPostFirebaseService } from '@/lib/firebase/services/generated-post-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport { useCurrentBrandProfile } from './use-brand-profiles';\r\nimport type { GeneratedPost, Platform } from '@/lib/types';\r\n\r\nexport interface GeneratedPostsState {\r\n  posts: GeneratedPost[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useGeneratedPosts(limit: number = 10) {\r\n  const userId = useUserId();\r\n  const { profile: currentProfile } = useCurrentBrandProfile();\r\n  const [state, setState] = useState<GeneratedPostsState>({\r\n    posts: [],\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load generated posts\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, posts: [] }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const posts = await generatedPostFirebaseService.getUserGeneratedPosts(userId, { limit });\r\n      \r\n      setState(prev => ({\r\n        ...prev,\r\n        posts,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load posts',\r\n      }));\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Save generated post\r\n  const savePost = useCallback(async (post: GeneratedPost): Promise<string> => {\r\n    if (!userId || !currentProfile) {\r\n      throw new Error('User must be authenticated and have a brand profile to save posts');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n      \r\n      const postId = await generatedPostFirebaseService.saveGeneratedPost(post, userId, currentProfile.id);\r\n      \r\n      // Add to local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: [{ ...post, id: postId }, ...prev.posts].slice(0, limit),\r\n        saving: false,\r\n      }));\r\n      \r\n      return postId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save post',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, currentProfile, limit]);\r\n\r\n  // Update post analytics\r\n  const updatePostAnalytics = useCallback(async (\r\n    postId: string,\r\n    analytics: {\r\n      views?: number;\r\n      likes?: number;\r\n      shares?: number;\r\n      comments?: number;\r\n      qualityScore?: number;\r\n      engagementPrediction?: number;\r\n      brandAlignmentScore?: number;\r\n    }\r\n  ): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.updatePostAnalytics(postId, analytics);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, ...analytics }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Update post status\r\n  const updatePostStatus = useCallback(async (\r\n    postId: string,\r\n    status: 'generated' | 'edited' | 'posted'\r\n  ): Promise<void> => {\r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      const publishedAt = status === 'posted' ? new Date() : undefined;\r\n      \r\n      await generatedPostFirebaseService.updatePostStatus(postId, firestoreStatus, undefined, publishedAt);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, status }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Delete post\r\n  const deletePost = useCallback(async (postId: string): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.delete(postId);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.filter(post => post.id !== postId),\r\n      }));\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Get posts by platform\r\n  const getPostsByPlatform = useCallback(async (platform: Platform): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      return await generatedPostFirebaseService.getUserGeneratedPosts(userId, { platform, limit });\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Get posts by status\r\n  const getPostsByStatus = useCallback(async (status: 'generated' | 'edited' | 'posted'): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      return await generatedPostFirebaseService.getPostsByStatus(userId, firestoreStatus);\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  }, [userId]);\r\n\r\n  // Load posts when dependencies change\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = generatedPostFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (posts) => {\r\n        setState(prev => ({\r\n          ...prev,\r\n          posts: posts.slice(0, limit),\r\n        }));\r\n      },\r\n      { limit, orderBy: 'createdAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId, limit]);\r\n\r\n  return {\r\n    ...state,\r\n    savePost,\r\n    updatePostAnalytics,\r\n    updatePostStatus,\r\n    deletePost,\r\n    getPostsByPlatform,\r\n    getPostsByStatus,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for getting posts for a specific brand profile\r\nexport function useGeneratedPostsForBrand(brandProfileId: string, limit: number = 10) {\r\n  const userId = useUserId();\r\n  const [posts, setPosts] = useState<GeneratedPost[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId || !brandProfileId) {\r\n      setPosts([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const brandPosts = await generatedPostFirebaseService.getRecentPostsForBrand(\r\n        userId, \r\n        brandProfileId, \r\n        limit\r\n      );\r\n      \r\n      setPosts(brandPosts);\r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load posts');\r\n      setLoading(false);\r\n    }\r\n  }, [userId, brandProfileId, limit]);\r\n\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  return {\r\n    posts,\r\n    loading,\r\n    error,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for post statistics\r\nexport function usePostStatistics() {\r\n  const { posts } = useGeneratedPosts(100); // Get more posts for statistics\r\n  \r\n  const statistics = {\r\n    total: posts.length,\r\n    byPlatform: posts.reduce((acc, post) => {\r\n      acc[post.platform] = (acc[post.platform] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<Platform, number>),\r\n    byStatus: posts.reduce((acc, post) => {\r\n      acc[post.status] = (acc[post.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>),\r\n    averageQuality: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.qualityScore || 0), 0) / posts.length \r\n      : 0,\r\n    averageEngagement: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.engagementPrediction || 0), 0) / posts.length \r\n      : 0,\r\n  };\r\n\r\n  return statistics;\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;AACnD;AACA;AACA;AACA;;;;;;AAUO,SAAS,kBAAkB,QAAgB,EAAE;;IAClD,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,OAAO,EAAE;QACT,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,uBAAuB;IACvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC5B,IAAI,CAAC,QAAQ;gBACX;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,OAAO,EAAE;wBAAC,CAAC;;gBACxD;YACF;YAEA,IAAI;gBACF;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,MAAM,QAAQ,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;gBAAM;gBAEvF;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;mDAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YAClC,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;+DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,SAAS,MAAM,qKAAA,CAAA,+BAA4B,CAAC,iBAAiB,CAAC,MAAM,QAAQ,eAAe,EAAE;gBAEnG,oCAAoC;gBACpC;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO;gCAAC;oCAAE,GAAG,IAAI;oCAAE,IAAI;gCAAO;mCAAM,KAAK,KAAK;6BAAC,CAAC,KAAK,CAAC,GAAG;4BACzD,QAAQ;wBACV,CAAC;;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;kDAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OACtC,QACA;YAUA,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,mBAAmB,CAAC,QAAQ;gBAE/D,qBAAqB;gBACrB;0EAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;sFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE,GAAG,SAAS;oCAAC,IACxB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;6DAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OACnC,QACA;YAEA,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,MAAM,cAAc,WAAW,WAAW,IAAI,SAAS;gBAEvD,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ,iBAAiB,WAAW;gBAExF,qBAAqB;gBACrB;uEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;mFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE;oCAAO,IAClB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;0DAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACpC,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,MAAM,CAAC;gBAE1C,qBAAqB;gBACrB;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,MAAM;6EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;wBAC/C,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;oDAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YAC5C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;oBAAU;gBAAM;YAC5F,EAAE,OAAO,OAAO;gBACd,OAAO,EAAE;YACX;QACF;4DAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC1C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ;YACrE,EAAE,OAAO,OAAO;gBACd,OAAO,EAAE;YACX;QACF;0DAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAU;IAEd,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CACpE;2DACA,CAAC;oBACC;mEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,OAAO,MAAM,KAAK,CAAC,GAAG;4BACxB,CAAC;;gBACH;0DACA;gBAAE;gBAAO,SAAS;gBAAa,gBAAgB;YAAO;YAGxD,OAAO;QACT;sCAAG;QAAC;QAAQ;KAAM;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GA/LgB;;QACC,0IAAA,CAAA,YAAS;QACY,2IAAA,CAAA,yBAAsB;;;AAgMrD,SAAS,0BAA0B,cAAsB,EAAE,QAAgB,EAAE;;IAClF,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,SAAS,EAAE;gBACX,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,aAAa,MAAM,qKAAA,CAAA,+BAA4B,CAAC,sBAAsB,CAC1E,QACA,gBACA;gBAGF,SAAS;gBACT,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,WAAW;YACb;QACF;2DAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;QACF;8CAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,QAAQ;IACV;AACF;IAzCgB;;QACC,0IAAA,CAAA,YAAS;;;AA2CnB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,kBAAkB,MAAM,gCAAgC;IAE1E,MAAM,aAAa;QACjB,OAAO,MAAM,MAAM;QACnB,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;YAC7B,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QACJ,UAAU,MAAM,MAAM,CAAC,CAAC,KAAK;YAC3B,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QACJ,gBAAgB,MAAM,MAAM,GAAG,IAC3B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GAC7E;QACJ,mBAAmB,MAAM,MAAM,GAAG,IAC9B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,oBAAoB,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GACrF;IACN;IAEA,OAAO;AACT;IAtBgB;;QACI", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/content-calendar.tsx"], "sourcesContent": ["// src/components/dashboard/content-calendar.tsx\r\n\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Loader2, Facebook, Instagram, Linkedin, Twitter, Settings, Palette, Sparkles } from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { PostCard } from \"@/components/dashboard/post-card\";\r\nimport { generateContentAction, generateEnhancedDesignAction, generateContentWithArtifactsAction } from \"@/app/actions\";\r\n\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { useGeneratedPosts } from \"@/hooks/use-generated-posts\";\r\nimport { useFirebaseAuth } from \"@/hooks/use-firebase-auth\";\r\nimport type { BrandProfile, GeneratedPost, Platform, BrandConsistencyPreferences } from \"@/lib/types\";\r\n\r\ntype RevoModel = 'revo-1.0' | 'revo-1.5';\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\nimport { Card, CardContent, CardDes<PERSON>, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { ArtifactSelector } from \"@/components/artifacts/artifact-selector\";\r\n\r\ntype ContentCalendarProps = {\r\n  brandProfile: BrandProfile;\r\n  posts: GeneratedPost[];\r\n  onPostGenerated: (post: GeneratedPost) => void;\r\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\r\n};\r\n\r\nconst platforms: { name: Platform; icon: React.ElementType }[] = [\r\n  { name: 'Instagram', icon: Instagram },\r\n  { name: 'Facebook', icon: Facebook },\r\n  { name: 'Twitter', icon: Twitter },\r\n  { name: 'LinkedIn', icon: Linkedin },\r\n];\r\n\r\nexport function ContentCalendar({ brandProfile, posts, onPostGenerated, onPostUpdated }: ContentCalendarProps) {\r\n  const [isGenerating, setIsGenerating] = React.useState<Platform | null>(null);\r\n  const { toast } = useToast();\r\n  const { user } = useFirebaseAuth();\r\n  const { savePost, saving } = useGeneratedPosts();\r\n\r\n  // Brand consistency preferences - default to consistent if design examples exist\r\n  const [brandConsistency, setBrandConsistency] = React.useState<BrandConsistencyPreferences>({\r\n    strictConsistency: !!(brandProfile.designExamples && brandProfile.designExamples.length > 0), // Auto-check if design examples exist\r\n    followBrandColors: true, // Always follow brand colors\r\n  });\r\n\r\n  // Revo model selection\r\n  const [selectedRevoModel, setSelectedRevoModel] = React.useState<RevoModel>('revo-1.5');\r\n\r\n  // Artifact selection for content generation\r\n  const [selectedArtifacts, setSelectedArtifacts] = React.useState<string[]>([]);\r\n\r\n  // Include people in designs toggle\r\n  const [includePeopleInDesigns, setIncludePeopleInDesigns] = React.useState<boolean>(true);\r\n\r\n  // Use local language toggle\r\n  const [useLocalLanguage, setUseLocalLanguage] = React.useState<boolean>(false);\r\n\r\n  // Save preferences to localStorage\r\n  React.useEffect(() => {\r\n    const savedPreferences = localStorage.getItem('brandConsistencyPreferences');\r\n    if (savedPreferences) {\r\n      setBrandConsistency(JSON.parse(savedPreferences));\r\n    }\r\n\r\n    const savedRevoModel = localStorage.getItem('selectedRevoModel');\r\n    if (savedRevoModel) {\r\n      setSelectedRevoModel(savedRevoModel as RevoModel);\r\n    }\r\n\r\n    const savedIncludePeople = localStorage.getItem('includePeopleInDesigns');\r\n    if (savedIncludePeople !== null) {\r\n      setIncludePeopleInDesigns(JSON.parse(savedIncludePeople));\r\n    }\r\n\r\n    const savedUseLocalLanguage = localStorage.getItem('useLocalLanguage');\r\n    if (savedUseLocalLanguage !== null) {\r\n      setUseLocalLanguage(JSON.parse(savedUseLocalLanguage));\r\n    }\r\n  }, []);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('brandConsistencyPreferences', JSON.stringify(brandConsistency));\r\n  }, [brandConsistency]);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('selectedRevoModel', selectedRevoModel);\r\n  }, [selectedRevoModel]);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('includePeopleInDesigns', JSON.stringify(includePeopleInDesigns));\r\n  }, [includePeopleInDesigns]);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('useLocalLanguage', JSON.stringify(useLocalLanguage));\r\n  }, [useLocalLanguage]);\r\n\r\n  const handleGenerateClick = async (platform: Platform) => {\r\n    setIsGenerating(platform);\r\n    try {\r\n\r\n      let newPost;\r\n\r\n      // Check if artifacts are enabled (simple toggle approach)\r\n      const artifactsEnabled = selectedArtifacts.length > 0;\r\n\r\n      const useEnhancedGeneration = artifactsEnabled || selectedRevoModel === 'revo-1.5' || selectedRevoModel === 'revo-2.0';\r\n\r\n      if (selectedRevoModel === 'revo-2.0') {\r\n\r\n        // Use server action to avoid client-side imports\r\n        const response = await fetch('/api/generate-revo-2.0', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            businessType: brandProfile.businessType || 'Business',\r\n            platform: platform.toLowerCase(),\r\n            visualStyle: brandProfile.visualStyle || 'modern',\r\n            imageText: `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,\r\n            brandProfile,\r\n            aspectRatio: '1:1',\r\n            includePeopleInDesigns,\r\n            useLocalLanguage\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Revo 2.0 generation failed: ${response.statusText}`);\r\n        }\r\n\r\n        const revo20Result = await response.json();\r\n\r\n        newPost = {\r\n          id: `revo-2.0-${Date.now()}`,\r\n          content: revo20Result.caption || `🚀 Generated with Revo 2.0 (Gemini 2.5 Flash Image)\\n\\n${brandProfile.businessName || brandProfile.businessType} - Premium Content`,\r\n          hashtags: revo20Result.hashtags || ['#NextGen', '#AI', '#Innovation'],\r\n          imageUrl: revo20Result.imageUrl,\r\n          platform: platform,\r\n          date: new Date().toISOString(),\r\n          analytics: {\r\n            views: 0,\r\n            likes: 0,\r\n            shares: 0,\r\n            comments: 0,\r\n            engagementPrediction: 85,\r\n            brandAlignmentScore: 95,\r\n            qualityScore: revo20Result.qualityScore || 10\r\n          },\r\n          metadata: {\r\n            aiModel: revo20Result.model || 'Revo 2.0',\r\n            generationPrompt: 'Revo 2.0 Native Generation',\r\n            processingTime: revo20Result.processingTime || 0,\r\n            enhancementsApplied: revo20Result.enhancementsApplied || []\r\n          }\r\n        };\r\n      } else if (useEnhancedGeneration) {\r\n        // Use artifact-enhanced generation - will automatically use active artifacts from artifacts page\r\n        newPost = await generateContentWithArtifactsAction(\r\n          brandProfile,\r\n          platform,\r\n          brandConsistency,\r\n          [], // Empty array - let the action use active artifacts from artifacts service\r\n          selectedRevoModel === 'revo-1.5', // Enhanced design for Revo 1.5\r\n          includePeopleInDesigns,\r\n          useLocalLanguage\r\n        );\r\n      } else {\r\n        // Use standard content generation\r\n        newPost = await generateContentAction(brandProfile, platform, brandConsistency);\r\n      }\r\n\r\n\r\n      // Save to Firestore database first\r\n      try {\r\n        const postId = await savePost(newPost);\r\n\r\n        // Update the post with the Firestore ID\r\n        const savedPost = { ...newPost, id: postId };\r\n        onPostGenerated(savedPost);\r\n      } catch (saveError) {\r\n        // Fallback to localStorage if Firestore fails\r\n        onPostGenerated(newPost);\r\n      }\r\n\r\n      // Dynamic toast message based on generation type\r\n      let title = \"Content Generated!\";\r\n      let description = `A new ${platform} post has been saved to your database.`;\r\n\r\n      if (selectedArtifacts.length > 0) {\r\n        title = \"Content Generated with References! 📎\";\r\n        description = `A new ${platform} post using ${selectedArtifacts.length} reference${selectedArtifacts.length !== 1 ? 's' : ''} has been saved.`;\r\n      } else if (selectedRevoModel === 'revo-1.5') {\r\n        title = \"Enhanced Content Generated! ✨\";\r\n        description = `A new enhanced ${platform} post with ${selectedRevoModel} has been saved.`;\r\n      } else {\r\n        title = \"Content Generated! 🚀\";\r\n        description = `A new ${platform} post with ${selectedRevoModel} has been saved.`;\r\n      }\r\n\r\n      toast({ title, description });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Generation Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsGenerating(null);\r\n    }\r\n  };\r\n\r\n  // Ensure this component is always full-bleed inside the app shell and does not cause horizontal overflow.\r\n  return (\r\n    <div className=\"w-full max-w-[100vw] box-border overflow-x-hidden\">\r\n      <div className=\"w-full px-6 py-10 lg:py-16 lg:px-12\">\r\n        <div className=\"w-full box-border space-y-6\">\r\n          {/* Compact Brand Consistency Controls */}\r\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Settings className=\"h-4 w-4 text-blue-600\" />\r\n                <span className=\"font-medium text-sm\">Brand Consistency</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Palette className=\"h-3 w-3 text-gray-500\" />\r\n                  <span className=\"text-xs text-gray-600\">Strict</span>\r\n                  <Switch\r\n                    checked={brandConsistency.strictConsistency}\r\n                    onCheckedChange={(checked) =>\r\n                      setBrandConsistency(prev => ({ ...prev, strictConsistency: checked }))\r\n                    }\r\n                  />\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Sparkles className=\"h-3 w-3 text-gray-500\" />\r\n                  <span className=\"text-xs text-gray-600\">Colors</span>\r\n                  <Switch\r\n                    checked={brandConsistency.followBrandColors}\r\n                    onCheckedChange={(checked) =>\r\n                      setBrandConsistency(prev => ({ ...prev, followBrandColors: checked }))\r\n                    }\r\n                  />\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"text-xs text-gray-600\">👥 People</span>\r\n                  <Switch\r\n                    checked={includePeopleInDesigns}\r\n                    onCheckedChange={setIncludePeopleInDesigns}\r\n                  />\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"text-xs text-gray-600\">🌍 Local</span>\r\n                  <Switch\r\n                    checked={useLocalLanguage}\r\n                    onCheckedChange={setUseLocalLanguage}\r\n                  />\r\n                </div>\r\n                <Separator orientation=\"vertical\" className=\"h-4\" />\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"text-xs text-gray-600\">AI Model:</span>\r\n                  <select\r\n                    value={selectedRevoModel}\r\n                    onChange={(e) => setSelectedRevoModel(e.target.value as RevoModel)}\r\n                    className=\"appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  >\r\n                    <option value=\"revo-1.0\">Revo 1.0</option>\r\n                    <option value=\"revo-1.5\">Revo 1.5</option>\r\n                    <option value=\"revo-2.0\">Revo 2.0</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <p className=\"text-xs text-gray-500 mt-2\">\r\n              {selectedRevoModel === 'revo-2.0'\r\n                ? `🚀 ${selectedRevoModel}: Next-Gen AI with native image generation, character consistency & intelligent editing`\r\n                : selectedRevoModel === 'revo-1.5'\r\n                  ? `✨ ${selectedRevoModel}: Enhanced AI with professional design principles + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\r\n                  : selectedRevoModel === 'revo-1.0'\r\n                    ? `🚀 ${selectedRevoModel}: Standard reliable AI + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\r\n                    : `🌟 ${selectedRevoModel}: Next-generation AI (coming soon)`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Simple Artifacts Toggle */}\r\n          <div className=\"mb-6\">\r\n            <Card>\r\n              <CardContent className=\"p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"space-y-1\">\r\n                    <Label className=\"text-sm font-medium\">Use Artifacts</Label>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Enable to use your uploaded reference materials and exact-use content\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Switch\r\n                      checked={selectedArtifacts.length > 0}\r\n                      onCheckedChange={(checked) => {\r\n                        if (checked) {\r\n                          // Enable artifacts - this will use active artifacts from the artifacts page\r\n                          setSelectedArtifacts(['active']);\r\n                        } else {\r\n                          // Disable artifacts\r\n                          setSelectedArtifacts([]);\r\n                        }\r\n                      }}\r\n                    />\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => window.open('/artifacts', '_blank')}\r\n                      className=\"text-xs\"\r\n                    >\r\n                      Manage\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {selectedArtifacts.length > 0 && (\r\n                  <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md\">\r\n                    <p className=\"text-xs text-blue-700\">\r\n                      ✓ Artifacts enabled - Content will use your reference materials and exact-use items from the Artifacts page\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold tracking-tight font-headline\">Content Calendar</h1>\r\n              <p className=\"text-muted-foreground\">\r\n                Here's your generated content. Click a post to edit or regenerate.\r\n              </p>\r\n            </div>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button disabled={!!isGenerating}>\r\n                  {isGenerating ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Generating for {isGenerating}...\r\n                    </>\r\n                  ) : (\r\n                    \"✨ Generate New Post\"\r\n                  )}\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent>\r\n                {platforms.map((p) => (\r\n                  <DropdownMenuItem key={p.name} onClick={() => handleGenerateClick(p.name)} disabled={!!isGenerating}>\r\n                    <p.icon className=\"mr-2 h-4 w-4\" />\r\n                    <span>{p.name}</span>\r\n                  </DropdownMenuItem>\r\n                ))}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n\r\n          {posts.length > 0 ? (\r\n            <div className=\"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full max-w-none\">\r\n              {posts.map((post) => (\r\n                <PostCard\r\n                  key={post.id}\r\n                  post={post}\r\n                  brandProfile={brandProfile}\r\n                  onPostUpdated={onPostUpdated}\r\n                />\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/30 bg-card p-12 text-center w-full\">\r\n              <h3 className=\"text-xl font-semibold\">Your calendar is empty</h3>\r\n              <p className=\"text-muted-foreground mt-2\">\r\n                Click the \"Generate\" button to create your first social media post!\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAGhD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;AA4BA,MAAM,YAA2D;IAC/D;QAAE,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACrC;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACnC;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;IAAC;IACjC;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACpC;AAEM,SAAS,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAwB;;IAC3G,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB;IACxE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD;IAE7C,iFAAiF;IACjF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAA8B;QAC1F,mBAAmB,CAAC,CAAC,CAAC,aAAa,cAAc,IAAI,aAAa,cAAc,CAAC,MAAM,GAAG,CAAC;QAC3F,mBAAmB;IACrB;IAEA,uBAAuB;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAY;IAE5E,4CAA4C;IAC5C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAW,EAAE;IAE7E,mCAAmC;IACnC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAU;IAEpF,4BAA4B;IAC5B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAU;IAExE,mCAAmC;IACnC,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,MAAM,mBAAmB,aAAa,OAAO,CAAC;YAC9C,IAAI,kBAAkB;gBACpB,oBAAoB,KAAK,KAAK,CAAC;YACjC;YAEA,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,qBAAqB;YACvB;YAEA,MAAM,qBAAqB,aAAa,OAAO,CAAC;YAChD,IAAI,uBAAuB,MAAM;gBAC/B,0BAA0B,KAAK,KAAK,CAAC;YACvC;YAEA,MAAM,wBAAwB,aAAa,OAAO,CAAC;YACnD,IAAI,0BAA0B,MAAM;gBAClC,oBAAoB,KAAK,KAAK,CAAC;YACjC;QACF;oCAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,+BAA+B,KAAK,SAAS,CAAC;QACrE;oCAAG;QAAC;KAAiB;IAErB,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,qBAAqB;QAC5C;oCAAG;QAAC;KAAkB;IAEtB,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;QAChE;oCAAG;QAAC;KAAuB;IAE3B,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAC1D;oCAAG;QAAC;KAAiB;IAErB,MAAM,sBAAsB,OAAO;QACjC,gBAAgB;QAChB,IAAI;YAEF,IAAI;YAEJ,0DAA0D;YAC1D,MAAM,mBAAmB,kBAAkB,MAAM,GAAG;YAEpD,MAAM,wBAAwB,oBAAoB,sBAAsB,cAAc,sBAAsB;YAE5G,IAAI,sBAAsB,YAAY;gBAEpC,iDAAiD;gBACjD,MAAM,WAAW,MAAM,MAAM,0BAA0B;oBACrD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,cAAc,aAAa,YAAY,IAAI;wBAC3C,UAAU,SAAS,WAAW;wBAC9B,aAAa,aAAa,WAAW,IAAI;wBACzC,WAAW,GAAG,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,kBAAkB,CAAC;wBACxF;wBACA,aAAa;wBACb;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;gBACtE;gBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;gBAExC,UAAU;oBACR,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;oBAC5B,SAAS,aAAa,OAAO,IAAI,CAAC,uDAAuD,EAAE,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,kBAAkB,CAAC;oBACrK,UAAU,aAAa,QAAQ,IAAI;wBAAC;wBAAY;wBAAO;qBAAc;oBACrE,UAAU,aAAa,QAAQ;oBAC/B,UAAU;oBACV,MAAM,IAAI,OAAO,WAAW;oBAC5B,WAAW;wBACT,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,sBAAsB;wBACtB,qBAAqB;wBACrB,cAAc,aAAa,YAAY,IAAI;oBAC7C;oBACA,UAAU;wBACR,SAAS,aAAa,KAAK,IAAI;wBAC/B,kBAAkB;wBAClB,gBAAgB,aAAa,cAAc,IAAI;wBAC/C,qBAAqB,aAAa,mBAAmB,IAAI,EAAE;oBAC7D;gBACF;YACF,OAAO,IAAI,uBAAuB;gBAChC,iGAAiG;gBACjG,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,qCAAkC,AAAD,EAC/C,cACA,UACA,kBACA,EAAE,EACF,sBAAsB,YACtB,wBACA;YAEJ,OAAO;gBACL,kCAAkC;gBAClC,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,UAAU;YAChE;YAGA,mCAAmC;YACnC,IAAI;gBACF,MAAM,SAAS,MAAM,SAAS;gBAE9B,wCAAwC;gBACxC,MAAM,YAAY;oBAAE,GAAG,OAAO;oBAAE,IAAI;gBAAO;gBAC3C,gBAAgB;YAClB,EAAE,OAAO,WAAW;gBAClB,8CAA8C;gBAC9C,gBAAgB;YAClB;YAEA,iDAAiD;YACjD,IAAI,QAAQ;YACZ,IAAI,cAAc,CAAC,MAAM,EAAE,SAAS,sCAAsC,CAAC;YAE3E,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,YAAY,EAAE,kBAAkB,MAAM,CAAC,UAAU,EAAE,kBAAkB,MAAM,KAAK,IAAI,MAAM,GAAG,gBAAgB,CAAC;YAChJ,OAAO,IAAI,sBAAsB,YAAY;gBAC3C,QAAQ;gBACR,cAAc,CAAC,eAAe,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAC3F,OAAO;gBACL,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAClF;YAEA,MAAM;gBAAE;gBAAO;YAAY;QAC7B,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0GAA0G;IAC1G,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,iBAAiB;;;;;;;;;;;;0DAGrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,iBAAiB;;;;;;;;;;;;0DAGrB,6LAAC,wIAAA,CAAA,YAAS;gDAAC,aAAY;gDAAW,WAAU;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,6LAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,6LAAC;gEAAO,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,6LAAC;gCAAE,WAAU;0CACV,sBAAsB,aACnB,CAAC,GAAG,EAAE,kBAAkB,uFAAuF,CAAC,GAChH,sBAAsB,aACpB,CAAC,EAAE,EAAE,kBAAkB,oDAAoD,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GACzJ,sBAAsB,aACpB,CAAC,GAAG,EAAE,kBAAkB,yBAAyB,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GAC/H,CAAC,GAAG,EAAE,kBAAkB,kCAAkC,CAAC;;;;;;;;;;;;kCAMvE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,kBAAkB,MAAM,GAAG;wDACpC,iBAAiB,CAAC;4DAChB,IAAI,SAAS;gEACX,4EAA4E;gEAC5E,qBAAqB;oEAAC;iEAAS;4DACjC,OAAO;gEACL,oBAAoB;gEACpB,qBAAqB,EAAE;4DACzB;wDACF;;;;;;kEAEF,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;wDACzC,WAAU;kEACX;;;;;;;;;;;;;;;;;;oCAKJ,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,UAAU,CAAC,CAAC;sDACjB,6BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;oDACjC;oDAAa;;+DAG/B;;;;;;;;;;;kDAIN,6LAAC,+IAAA,CAAA,sBAAmB;kDACjB,UAAU,GAAG,CAAC,CAAC,kBACd,6LAAC,+IAAA,CAAA,mBAAgB;gDAAc,SAAS,IAAM,oBAAoB,EAAE,IAAI;gDAAG,UAAU,CAAC,CAAC;;kEACrF,6LAAC,EAAE,IAAI;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,EAAE,IAAI;;;;;;;+CAFQ,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;oBASpC,MAAM,MAAM,GAAG,kBACd,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,kJAAA,CAAA,WAAQ;gCAEP,MAAM;gCACN,cAAc;gCACd,eAAe;+BAHV,KAAK,EAAE;;;;;;;;;6CAQlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAlWgB;;QAEI,+HAAA,CAAA,WAAQ;QACT,0IAAA,CAAA,kBAAe;QACH,4IAAA,CAAA,oBAAiB;;;KAJhC", "debugId": null}}, {"offset": {"line": 3046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/unified-brand-layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { UnifiedBrandProvider, useUnifiedBrand, useBrandChangeListener } from '@/contexts/unified-brand-context';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\ninterface UnifiedBrandLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Inner component that uses the unified brand context\r\nfunction UnifiedBrandLayoutContent({ children }: UnifiedBrandLayoutProps) {\r\n  const { currentBrand, loading, error } = useUnifiedBrand();\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Listen for brand changes and log them\r\n  useBrandChangeListener((brand) => {\r\n    \r\n    // Mark as initialized once we have a brand or finished loading\r\n    if (!isInitialized && (!loading || brand)) {\r\n      setIsInitialized(true);\r\n    }\r\n  });\r\n\r\n  // Show loading state while initializing\r\n  if (!isInitialized && loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading brand profiles...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n            <span className=\"text-red-600 text-2xl\">⚠️</span>\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-red-900 mb-2\">Error Loading Brands</h2>\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"unified-brand-layout\">\r\n      {/* Debug info in development */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl\">\r\n          <div>🔥 Unified Brand System</div>\r\n          <div>Brand: {currentBrand?.businessName || currentBrand?.name || 'None'}</div>\r\n          <div>ID: {currentBrand?.id || 'None'}</div>\r\n        </div>\r\n      )}\r\n      \r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Main layout component that provides the unified brand context\r\nexport function UnifiedBrandLayout({ children }: UnifiedBrandLayoutProps) {\r\n  return (\r\n    <UnifiedBrandProvider>\r\n      <UnifiedBrandLayoutContent>\r\n        {children}\r\n      </UnifiedBrandLayoutContent>\r\n    </UnifiedBrandProvider>\r\n  );\r\n}\r\n\r\n// Hook to make any component brand-aware\r\nexport function useBrandAware() {\r\n  const { currentBrand, selectBrand, loading } = useUnifiedBrand();\r\n  \r\n  return {\r\n    currentBrand,\r\n    selectBrand,\r\n    loading,\r\n    isReady: !loading && currentBrand !== null,\r\n    brandId: currentBrand?.id || null,\r\n    brandName: currentBrand?.businessName || currentBrand?.name || null,\r\n  };\r\n}\r\n\r\n// Higher-order component to make any component brand-aware\r\nexport function withBrandAware<P extends object>(\r\n  Component: React.ComponentType<P & { brand: CompleteBrandProfile | null }>\r\n) {\r\n  return function BrandAwareComponent(props: P) {\r\n    const { currentBrand } = useUnifiedBrand();\r\n    \r\n    return <Component {...props} brand={currentBrand} />;\r\n  };\r\n}\r\n\r\n// Component to show brand-specific content\r\ninterface BrandContentProps {\r\n  children: (brand: CompleteBrandProfile) => React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function BrandContent({ children, fallback }: BrandContentProps) {\r\n  const { currentBrand, loading } = useUnifiedBrand();\r\n  \r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  if (!currentBrand) {\r\n    return fallback || (\r\n      <div className=\"text-center p-8\">\r\n        <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <span className=\"text-gray-400 text-2xl\">🏢</span>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Brand Selected</h3>\r\n        <p className=\"text-gray-600\">Please select a brand to continue.</p>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  return <>{children(currentBrand)}</>;\r\n}\r\n\r\n// Component to conditionally render content based on brand\r\ninterface ConditionalBrandContentProps {\r\n  brandId?: string;\r\n  brandName?: string;\r\n  children: React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function ConditionalBrandContent({ \r\n  brandId, \r\n  brandName, \r\n  children, \r\n  fallback \r\n}: ConditionalBrandContentProps) {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  \r\n  const shouldRender = \r\n    (!brandId || currentBrand?.id === brandId) &&\r\n    (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);\r\n  \r\n  if (shouldRender) {\r\n    return <>{children}</>;\r\n  }\r\n  \r\n  return fallback || null;\r\n}\r\n\r\n// Hook to get brand-scoped data with automatic updates\r\nexport function useBrandScopedData<T>(\r\n  feature: string,\r\n  defaultValue: T,\r\n  loader?: (brandId: string) => T | Promise<T>\r\n): [T, (data: T) => void, boolean] {\r\n  const { currentBrand, getBrandStorage } = useUnifiedBrand();\r\n  const [data, setData] = useState<T>(defaultValue);\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // Load data when brand changes\r\n  useEffect(() => {\r\n    if (!currentBrand?.id) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    const storage = getBrandStorage(feature);\r\n    if (!storage) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      if (loader) {\r\n        // Use custom loader\r\n        const result = loader(currentBrand.id);\r\n        if (result instanceof Promise) {\r\n          result.then(loadedData => {\r\n            setData(loadedData);\r\n            setLoading(false);\r\n          }).catch(error => {\r\n            setData(defaultValue);\r\n            setLoading(false);\r\n          });\r\n        } else {\r\n          setData(result);\r\n          setLoading(false);\r\n        }\r\n      } else {\r\n        // Use storage\r\n        const storedData = storage.getItem<T>();\r\n        setData(storedData || defaultValue);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      setData(defaultValue);\r\n      setLoading(false);\r\n    }\r\n  }, [currentBrand?.id, feature, defaultValue, loader, getBrandStorage]);\r\n  \r\n  // Save data function\r\n  const saveData = (newData: T) => {\r\n    setData(newData);\r\n    \r\n    if (currentBrand?.id) {\r\n      const storage = getBrandStorage(feature);\r\n      if (storage) {\r\n        storage.setItem(newData);\r\n      }\r\n    }\r\n  };\r\n  \r\n  return [data, saveData, loading];\r\n}\r\n\r\n// Component to display brand switching status\r\nexport function BrandSwitchingStatus() {\r\n  const { loading, currentBrand } = useUnifiedBrand();\r\n  const [switching, setSwitching] = useState(false);\r\n  \r\n  useBrandChangeListener((brand) => {\r\n    setSwitching(true);\r\n    const timer = setTimeout(() => setSwitching(false), 1000);\r\n    return () => clearTimeout(timer);\r\n  });\r\n  \r\n  if (!switching && !loading) return null;\r\n  \r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n        <span className=\"text-sm\">\r\n          {switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AA4DO;;AA1DP;AACA;;;AAHA;;;AAUA,sDAAsD;AACtD,SAAS,0BAA0B,EAAE,QAAQ,EAA2B;;IACtE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;4DAAE,CAAC;YAEtB,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,KAAK,GAAG;gBACzC,iBAAiB;YACnB;QACF;;IAEA,wCAAwC;IACxC,IAAI,CAAC,iBAAiB,SAAS;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;kCAE1C,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAI;;;;;;kCACL,6LAAC;;4BAAI;4BAAQ,cAAc,gBAAgB,cAAc,QAAQ;;;;;;;kCACjE,6LAAC;;4BAAI;4BAAK,cAAc,MAAM;;;;;;;;;;;;;YAIjC;;;;;;;AAGP;GA5DS;;QACkC,kJAAA,CAAA,kBAAe;QAIxD,kJAAA,CAAA,yBAAsB;;;KALf;AA+DF,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,qBACE,6LAAC,kJAAA,CAAA,uBAAoB;kBACnB,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAWT,SAAS;;IACd,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,WAAW,iBAAiB;QACtC,SAAS,cAAc,MAAM;QAC7B,WAAW,cAAc,gBAAgB,cAAc,QAAQ;IACjE;AACF;IAXgB;;QACiC,kJAAA,CAAA,kBAAe;;;AAazD,SAAS,eACd,SAA0E;;IAE1E,UAAO,SAAS,oBAAoB,KAAQ;;QAC1C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;QAEvC,qBAAO,6LAAC;YAAW,GAAG,KAAK;YAAE,OAAO;;;;;;IACtC;;YAH2B,kJAAA,CAAA,kBAAe;;;AAI5C;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAqB;;IACpE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEhD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,0BACL,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAyB;;;;;;;;;;;8BAE3C,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBAAO;kBAAG,SAAS;;AACrB;IAxBgB;;QACoB,kJAAA,CAAA,kBAAe;;;MADnC;AAkCT,SAAS,wBAAwB,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACqB;;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEvC,MAAM,eACJ,CAAC,CAAC,WAAW,cAAc,OAAO,OAAO,KACzC,CAAC,CAAC,aAAa,cAAc,iBAAiB,aAAa,cAAc,SAAS,SAAS;IAE7F,IAAI,cAAc;QAChB,qBAAO;sBAAG;;IACZ;IAEA,OAAO,YAAY;AACrB;IAjBgB;;QAMW,kJAAA,CAAA,kBAAe;;;MAN1B;AAoBT,SAAS,mBACd,OAAe,EACf,YAAe,EACf,MAA4C;;IAE5C,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,cAAc,IAAI;gBACrB,QAAQ;gBACR;YACF;YAEA,MAAM,UAAU,gBAAgB;YAChC,IAAI,CAAC,SAAS;gBACZ,QAAQ;gBACR;YACF;YAEA,WAAW;YAEX,IAAI;gBACF,IAAI,QAAQ;oBACV,oBAAoB;oBACpB,MAAM,SAAS,OAAO,aAAa,EAAE;oBACrC,IAAI,kBAAkB,SAAS;wBAC7B,OAAO,IAAI;4DAAC,CAAA;gCACV,QAAQ;gCACR,WAAW;4BACb;2DAAG,KAAK;4DAAC,CAAA;gCACP,QAAQ;gCACR,WAAW;4BACb;;oBACF,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF,OAAO;oBACL,cAAc;oBACd,MAAM,aAAa,QAAQ,OAAO;oBAClC,QAAQ,cAAc;oBACtB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ;gBACR,WAAW;YACb;QACF;uCAAG;QAAC,cAAc;QAAI;QAAS;QAAc;QAAQ;KAAgB;IAErE,qBAAqB;IACrB,MAAM,WAAW,CAAC;QAChB,QAAQ;QAER,IAAI,cAAc,IAAI;YACpB,MAAM,UAAU,gBAAgB;YAChC,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC;YAClB;QACF;IACF;IAEA,OAAO;QAAC;QAAM;QAAU;KAAQ;AAClC;IAjEgB;;QAK4B,kJAAA,CAAA,kBAAe;;;AA+DpD,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;uDAAE,CAAC;YACtB,aAAa;YACb,MAAM,QAAQ;qEAAW,IAAM,aAAa;oEAAQ;YACpD;+DAAO,IAAM,aAAa;;QAC5B;;IAEA,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BACb,YAAY,CAAC,aAAa,EAAE,cAAc,gBAAgB,cAAc,KAAK,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;AAK/F;IAtBgB;;QACoB,kJAAA,CAAA,kBAAe;QAGjD,kJAAA,CAAA,yBAAsB;;;MAJR", "debugId": null}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/lib/utils/enable-firebase-storage.ts"], "sourcesContent": ["/**\r\n * Enable Firebase Storage Utility\r\n * Helper to re-enable Firebase Storage after rules are deployed\r\n */\r\n\r\nexport const FIREBASE_STORAGE_INSTRUCTIONS = `\r\n🔥 FIREBASE STORAGE RULES DEPLOYMENT INSTRUCTIONS\r\n\r\n1. Go to Firebase Console: https://console.firebase.google.com/\r\n2. Select your project: localbuzz-mpkuv\r\n3. Go to Storage → Rules\r\n4. Replace the current rules with:\r\n\r\nrules_version = '2';\r\nservice firebase.storage {\r\n  match /b/{bucket}/o {\r\n    // Users can upload and manage their own generated content\r\n    match /generated-content/{userId}/{allPaths=**} {\r\n      allow read, write: if request.auth != null && request.auth.uid == userId;\r\n    }\r\n    \r\n    // Users can upload and manage their own artifacts\r\n    match /artifacts/{userId}/{allPaths=**} {\r\n      allow read, write: if request.auth != null && request.auth.uid == userId;\r\n    }\r\n    \r\n    // Users can upload and manage their own brand assets\r\n    match /brand-assets/{userId}/{allPaths=**} {\r\n      allow read, write: if request.auth != null && request.auth.uid == userId;\r\n    }\r\n    \r\n    // Temporary uploads (for processing)\r\n    match /temp/{userId}/{allPaths=**} {\r\n      allow read, write: if request.auth != null && request.auth.uid == userId;\r\n    }\r\n  }\r\n}\r\n\r\n5. Click \"Publish\"\r\n6. Wait 2-3 minutes for rules to propagate\r\n7. Come back and run: enableFirebaseStorage()\r\n`;\r\n\r\nexport const CODE_TO_UNCOMMENT = `\r\nAfter deploying Firebase Storage rules, go to:\r\nsrc/app/quick-content/page.tsx\r\n\r\nFind this section around line 209:\r\n// TEMPORARY: Skip Firebase Storage upload until rules are deployed\r\n\r\nReplace the entire processPostImages function with the commented code below it.\r\n\r\nOr simply run: enableFirebaseStorage() in the browser console.\r\n`;\r\n\r\n/**\r\n * Enable Firebase Storage by updating the code\r\n */\r\nexport function enableFirebaseStorage() {\r\n  \r\n  return {\r\n    instructions: FIREBASE_STORAGE_INSTRUCTIONS,\r\n    codeInstructions: CODE_TO_UNCOMMENT,\r\n    status: 'Instructions displayed - manual code update required'\r\n  };\r\n}\r\n\r\n/**\r\n * Check if Firebase Storage rules are working\r\n */\r\nexport async function testFirebaseStorageRules() {\r\n  try {\r\n    // This would need to be implemented with actual Firebase Storage test\r\n    \r\n    return {\r\n      success: false,\r\n      message: 'Manual test required - generate content to test'\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error'\r\n    };\r\n  }\r\n}\r\n\r\n// Make functions available globally for easy access\r\nif (typeof window !== 'undefined') {\r\n  (window as any).enableFirebaseStorage = enableFirebaseStorage;\r\n  (window as any).testFirebaseStorageRules = testFirebaseStorageRules;\r\n  \r\n  // Auto-display instructions on load\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAEM,MAAM,gCAAgC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoC9C,CAAC;AAEM,MAAM,oBAAoB,CAAC;;;;;;;;;;AAUlC,CAAC;AAKM,SAAS;IAEd,OAAO;QACL,cAAc;QACd,kBAAkB;QAClB,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,sEAAsE;QAEtE,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA,oDAAoD;AACpD,wCAAmC;IAChC,OAAe,qBAAqB,GAAG;IACvC,OAAe,wBAAwB,GAAG;AAE3C,oCAAoC;AACtC", "debugId": null}}, {"offset": {"line": 3605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/quick-content/page.tsx"], "sourcesContent": ["// src/app/content-calendar/page.tsx\r\n\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { SidebarInset, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { ContentCalendar } from \"@/components/dashboard/content-calendar\";\r\n// TODO: Re-enable once ActiveArtifactsIndicator is properly set up\r\n// import { ActiveArtifactsIndicator } from \"@/components/artifacts/active-artifacts-indicator\";\r\nimport type { BrandProfile, GeneratedPost } from \"@/lib/types\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { User, PanelLeftClose, PanelLeftOpen } from \"lucide-react\";\r\nimport { useUnifiedBrand, useBrandStorage, useBrandChangeListener } from \"@/contexts/unified-brand-context\";\r\nimport { UnifiedBrandLayout, BrandContent, BrandSwitchingStatus } from \"@/components/layout/unified-brand-layout\";\r\nimport { STORAGE_FEATURES, getStorageUsage, cleanupAllStorage } from \"@/lib/services/brand-scoped-storage\";\r\nimport { processGeneratedPost } from \"@/lib/services/generated-post-storage\";\r\nimport { useFirebaseAuth } from \"@/hooks/use-firebase-auth\";\r\nimport { useGeneratedPosts } from \"@/hooks/use-generated-posts\";\r\nimport \"@/lib/utils/enable-firebase-storage\"; // Load Firebase Storage utilities\r\n\r\n// No limit on posts - store all generated content\r\n\r\n// Brand-scoped storage cleanup utility\r\nconst cleanupBrandScopedStorage = (brandStorage: any) => {\r\n  try {\r\n    const posts = brandStorage.getItem() || [];\r\n\r\n    // Fix invalid dates in existing posts\r\n    const fixedPosts = posts.map((post: GeneratedPost) => {\r\n      if (!post.date || isNaN(new Date(post.date).getTime())) {\r\n        return {\r\n          ...post,\r\n          date: new Date().toISOString()\r\n        };\r\n      }\r\n      return post;\r\n    });\r\n\r\n    if (fixedPosts.length > 5) {\r\n      // Keep only the 5 most recent posts\r\n      const recentPosts = fixedPosts.slice(0, 5);\r\n      brandStorage.setItem(recentPosts);\r\n      return recentPosts;\r\n    } else {\r\n      // Save the fixed posts back\r\n      brandStorage.setItem(fixedPosts);\r\n      return fixedPosts;\r\n    }\r\n  } catch (error) {\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction QuickContentPage() {\r\n  const { currentBrand, brands, loading: brandLoading, selectBrand } = useUnifiedBrand();\r\n  const postsStorage = useBrandStorage(STORAGE_FEATURES.QUICK_CONTENT);\r\n  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n  const { open: sidebarOpen, toggleSidebar } = useSidebar();\r\n  const { user } = useFirebaseAuth();\r\n  const { savePost, saving } = useGeneratedPosts();\r\n\r\n  // Inline brand restoration function\r\n  const forceBrandRestore = React.useCallback(() => {\r\n    try {\r\n      // Try to restore from full brand data first\r\n      const savedBrandData = localStorage.getItem('currentBrandData');\r\n      if (savedBrandData) {\r\n        const parsedData = JSON.parse(savedBrandData);\r\n\r\n        // Find matching brand in current brands list\r\n        const matchingBrand = brands.find(b => b.id === parsedData.id);\r\n        if (matchingBrand) {\r\n          selectBrand(matchingBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // Fallback to brand ID restoration\r\n      const savedBrandId = localStorage.getItem('selectedBrandId');\r\n      if (savedBrandId && brands.length > 0) {\r\n        const savedBrand = brands.find(b => b.id === savedBrandId);\r\n        if (savedBrand) {\r\n          selectBrand(savedBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }, [brands, selectBrand]);\r\n\r\n  // Load posts when brand changes using unified brand system\r\n  useBrandChangeListener(React.useCallback((brand) => {\r\n    const brandName = brand?.businessName || brand?.name || 'none';\r\n\r\n    if (!brand) {\r\n      setGeneratedPosts([]);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      if (postsStorage) {\r\n        const posts = postsStorage.getItem<GeneratedPost[]>() || [];\r\n\r\n        // Check if any posts have invalid dates\r\n        const hasInvalidDates = posts.some((post: GeneratedPost) =>\r\n          !post.date || isNaN(new Date(post.date).getTime())\r\n        );\r\n\r\n        if (hasInvalidDates) {\r\n          postsStorage.removeItem();\r\n          setGeneratedPosts([]);\r\n        } else {\r\n          setGeneratedPosts(posts);\r\n        }\r\n\r\n      } else {\r\n        setGeneratedPosts([]);\r\n      }\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to load data\",\r\n        description: \"Could not read your posts data. It might be corrupted.\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [postsStorage, toast]));\r\n\r\n  // Enhanced brand selection logic with persistence recovery\r\n  useEffect(() => {\r\n\r\n    if (!brandLoading) {\r\n      // Add a small delay to ensure brands have time to load\r\n      const timer = setTimeout(() => {\r\n        if (brands.length === 0) {\r\n          // No brands exist, redirect to brand setup\r\n          try { router.prefetch('/brand-profile'); } catch { }\r\n          router.push('/brand-profile');\r\n        } else if (brands.length > 0 && !currentBrand) {\r\n          // Try to restore from persistence first\r\n          const restored = forceBrandRestore();\r\n\r\n          if (!restored) {\r\n            // If restoration failed, auto-select the first brand\r\n            selectBrand(brands[0]);\r\n          }\r\n        }\r\n      }, 1000); // 1 second delay\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [currentBrand, brands.length, brandLoading, router, selectBrand, forceBrandRestore]);\r\n\r\n\r\n  // Process generated post with Firebase Storage upload and database fallback\r\n  const processPostImages = async (post: GeneratedPost): Promise<GeneratedPost> => {\r\n    try {\r\n      // Check if user is authenticated for Firebase Storage\r\n      if (!user) {\r\n        toast({\r\n          title: \"Content Saved\",\r\n          description: \"Content saved to database. Sign in to save images permanently in the cloud.\",\r\n          variant: \"default\",\r\n        });\r\n        return post; // Return original post with data URLs\r\n      }\r\n\r\n\r\n      // TEMPORARY: Skip Firebase Storage upload until rules are deployed\r\n\r\n      // Save to database with data URLs (temporary solution)\r\n      toast({\r\n        title: \"Content Saved to Database\",\r\n        description: \"Content saved successfully. Deploy Firebase Storage rules for permanent image URLs.\",\r\n        variant: \"default\",\r\n      });\r\n\r\n      return post; // Return original post with data URLs\r\n\r\n      /* UNCOMMENT THIS AFTER DEPLOYING FIREBASE STORAGE RULES:\r\n      try {\r\n        // Try Firebase Storage first\r\n        const processedPost = await processGeneratedPost(post, user.uid);\r\n\r\n\r\n        // Show success message\r\n        toast({\r\n          title: \"Images Saved to Cloud\",\r\n          description: \"Images have been permanently saved to Firebase Storage.\",\r\n          variant: \"default\",\r\n        });\r\n\r\n        return processedPost;\r\n      } catch (storageError) {\r\n\r\n        // Fallback: Save to database with data URLs (temporary)\r\n        toast({\r\n          title: \"Content Saved to Database\",\r\n          description: \"Images stored temporarily. Please update Firebase Storage rules for permanent cloud storage.\",\r\n          variant: \"default\",\r\n        });\r\n\r\n        return post; // Return original post with data URLs\r\n      }\r\n      */\r\n    } catch (error) {\r\n      toast({\r\n        title: \"Content Saved Locally\",\r\n        description: \"Content generated successfully but stored locally only.\",\r\n        variant: \"default\",\r\n      });\r\n      return post; // Return original post if all processing fails\r\n    }\r\n  };\r\n\r\n  const handlePostGenerated = async (post: GeneratedPost) => {\r\n\r\n    // Process images with Firebase Storage upload\r\n    let processedPost = await processPostImages(post);\r\n\r\n    // Add the processed post to the beginning of the array (no limit)\r\n    const newPosts = [processedPost, ...generatedPosts];\r\n    setGeneratedPosts(newPosts);\r\n\r\n    if (!postsStorage) {\r\n      toast({\r\n        title: \"Storage Unavailable\",\r\n        description: \"Post generated but couldn't be saved. Please select a brand.\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Save to localStorage first (immediate)\r\n      postsStorage.setItem(newPosts);\r\n\r\n      // Also save to Firestore database (permanent backup)\r\n      if (user) {\r\n        try {\r\n          const postId = await savePost(processedPost);\r\n\r\n          // Update the post with the Firestore ID\r\n          const savedPost = { ...processedPost, id: postId };\r\n          const updatedPosts = [savedPost, ...generatedPosts];\r\n          setGeneratedPosts(updatedPosts);\r\n          postsStorage.setItem(updatedPosts);\r\n\r\n          toast({\r\n            title: \"Content Saved Successfully\",\r\n            description: \"Your content has been saved to both local storage and the database.\",\r\n            variant: \"default\",\r\n          });\r\n        } catch (firestoreError) {\r\n          toast({\r\n            title: \"Content Saved Locally\",\r\n            description: \"Content saved locally. Database save failed but content is secure.\",\r\n            variant: \"default\",\r\n          });\r\n        }\r\n      } else {\r\n        toast({\r\n          title: \"Content Saved Locally\",\r\n          description: \"Content saved locally. Sign in to save to database permanently.\",\r\n          variant: \"default\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n\r\n      // Show user-friendly error message\r\n      toast({\r\n        title: \"Storage Issue\",\r\n        description: \"Post generated successfully but couldn't be saved. Storage may be full.\",\r\n        variant: \"destructive\",\r\n      });\r\n\r\n      // Keep the post in memory even if storage fails\r\n    }\r\n  };\r\n\r\n  // Debug function to clear all posts for current brand\r\n  const clearAllPosts = () => {\r\n    if (!postsStorage) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      postsStorage.removeItem();\r\n      setGeneratedPosts([]);\r\n      toast({\r\n        title: \"Posts Cleared\",\r\n        description: `All stored posts have been cleared for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Clear Failed\",\r\n        description: \"Could not clear stored posts.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handlePostUpdated = async (updatedPost: GeneratedPost) => {\r\n    if (!postsStorage) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const updatedPosts = generatedPosts.map((post) =>\r\n        post.id === updatedPost.id ? updatedPost : post\r\n      );\r\n      setGeneratedPosts(updatedPosts);\r\n\r\n      // Check storage size before saving\r\n      const postsData = JSON.stringify(updatedPosts);\r\n      const maxSize = 5 * 1024 * 1024; // 5MB limit\r\n\r\n      if (postsData.length > maxSize) {\r\n        // If too large, keep fewer posts\r\n        const reducedPosts = updatedPosts.slice(0, Math.max(1, Math.floor(MAX_POSTS_TO_STORE / 2)));\r\n        postsStorage.setItem(reducedPosts);\r\n        setGeneratedPosts(reducedPosts);\r\n\r\n        toast({\r\n          title: \"Storage Optimized\",\r\n          description: \"Reduced stored posts to prevent storage overflow. Some older posts were removed.\",\r\n        });\r\n      } else {\r\n        postsStorage.setItem(updatedPosts);\r\n      }\r\n\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to update post\",\r\n        description: \"Unable to save post updates. Your browser storage may be full.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <SidebarInset key={currentBrand?.id || 'no-brand'} fullWidth>\r\n      <header className=\"flex h-14 items-center justify-between gap-4 border-b bg-card px-4 lg:h-[60px] lg:px-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={toggleSidebar}\r\n            className=\"h-8 w-8\"\r\n            title={sidebarOpen ? \"Hide sidebar for full-screen mode\" : \"Show sidebar\"}\r\n          >\r\n            {sidebarOpen ? (\r\n              <PanelLeftClose className=\"h-4 w-4\" />\r\n            ) : (\r\n              <PanelLeftOpen className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {sidebarOpen ? \"Sidebar visible\" : \"Full-screen mode\"}\r\n          </span>\r\n        </div>\r\n\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"secondary\" size=\"icon\" className=\"rounded-full\">\r\n              <Avatar>\r\n                <AvatarImage src=\"https://placehold.co/40x40.png\" alt=\"User\" data-ai-hint=\"user avatar\" />\r\n                <AvatarFallback><User /></AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"sr-only\">Toggle user menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuLabel>My Account</DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem\r\n              onClick={() => {\r\n                if (postsStorage) {\r\n                  const cleaned = cleanupBrandScopedStorage(postsStorage);\r\n                  if (cleaned) {\r\n                    setGeneratedPosts(cleaned);\r\n                    toast({\r\n                      title: \"Storage Cleaned\",\r\n                      description: `Removed older posts for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n                    });\r\n                  } else {\r\n                    toast({\r\n                      title: \"Storage Clean\",\r\n                      description: \"Storage is already optimized.\",\r\n                    });\r\n                  }\r\n                } else {\r\n                  toast({\r\n                    variant: \"destructive\",\r\n                    title: \"No Brand Selected\",\r\n                    description: \"Please select a brand first.\",\r\n                  });\r\n                }\r\n              }}\r\n            >\r\n              Clear Old Posts\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </header>\r\n      <main className=\"flex-1 overflow-auto\">\r\n        <div className=\"min-h-full bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              {isLoading || brandLoading ? (\r\n                <div className=\"flex w-full min-h-[300px] items-center justify-center\">\r\n                  <div className=\"w-full max-w-3xl text-center\">\r\n                    <p>Loading Quick Content...</p>\r\n                  </div>\r\n                </div>\r\n              ) : !currentBrand ? (\r\n                <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n                  <h2 className=\"text-xl font-semibold\">Select a Brand</h2>\r\n                  <p className=\"text-muted-foreground text-center\">\r\n                    Please select a brand to start generating content.\r\n                  </p>\r\n                  {brands.length > 0 ? (\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                      {brands.map((brand) => (\r\n                        <Button\r\n                          key={brand.id}\r\n                          onClick={() => selectBrand(brand)}\r\n                          variant=\"outline\"\r\n                        >\r\n                          {brand.businessName || brand.name}\r\n                        </Button>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <Button onMouseEnter={() => router.prefetch('/brand-profile')} onFocus={() => router.prefetch('/brand-profile')} onClick={() => router.push('/brand-profile')}>\r\n                      Create Brand Profile\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {/* TODO: Re-enable Active Artifacts Indicator once component is set up */}\r\n                  {/* <ActiveArtifactsIndicator\r\n              onArtifactDeactivate={() => {\r\n                // Refresh content when artifacts are deactivated\r\n              }}\r\n              onManageArtifacts={() => {\r\n                // Navigate to artifacts page\r\n                window.open('/artifacts', '_blank');\r\n              }}\r\n            /> */}\r\n\r\n                  {/* Content Calendar */}\r\n                  {/* Map unified CompleteBrandProfile to the simplified BrandProfile expected by ContentCalendar */}\r\n                  {currentBrand && (\r\n                    <ContentCalendar\r\n                      brandProfile={{\r\n                        businessName: currentBrand.businessName,\r\n                        businessType: currentBrand.businessType || '',\r\n                        location: currentBrand.location || '',\r\n                        logoDataUrl: currentBrand.logoDataUrl || '',\r\n                        visualStyle: currentBrand.visualStyle || '',\r\n                        writingTone: currentBrand.writingTone || '',\r\n                        contentThemes: currentBrand.contentThemes || '',\r\n                        websiteUrl: currentBrand.websiteUrl || '',\r\n                        description: currentBrand.description || '',\r\n                        // Convert services array to newline-separated string to match BrandProfile.services\r\n                        services: Array.isArray((currentBrand as any).services)\r\n                          ? (currentBrand as any).services.map((s: any) => s.name).join('\\n')\r\n                          : (currentBrand as any).services || '',\r\n                        targetAudience: currentBrand.targetAudience || '',\r\n                        keyFeatures: currentBrand.keyFeatures || '',\r\n                        competitiveAdvantages: currentBrand.competitiveAdvantages || '',\r\n                        contactInfo: {\r\n                          phone: currentBrand.contactPhone || '',\r\n                          email: currentBrand.contactEmail || '',\r\n                          address: currentBrand.contactAddress || '',\r\n                        },\r\n                        socialMedia: {\r\n                          facebook: currentBrand.facebookUrl || '',\r\n                          instagram: currentBrand.instagramUrl || '',\r\n                          twitter: currentBrand.twitterUrl || '',\r\n                          linkedin: currentBrand.linkedinUrl || '',\r\n                        },\r\n                        primaryColor: currentBrand.primaryColor || undefined,\r\n                        accentColor: currentBrand.accentColor || undefined,\r\n                        backgroundColor: currentBrand.backgroundColor || undefined,\r\n                        designExamples: currentBrand.designExamples || [],\r\n                      }}\r\n                      posts={generatedPosts}\r\n                      onPostGenerated={handlePostGenerated}\r\n                      onPostUpdated={handlePostUpdated}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </SidebarInset>\r\n  );\r\n}\r\n\r\nfunction QuickContentPageWithUnifiedBrand() {\r\n  return (\r\n    <UnifiedBrandLayout>\r\n      <QuickContentPage />\r\n      <BrandSwitchingStatus />\r\n    </UnifiedBrandLayout>\r\n  );\r\n}\r\n\r\nexport default QuickContentPageWithUnifiedBrand;\r\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AAGpC;AAEA;AAQA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA,uQAA8C,kCAAkC;;;AA5BhF;;;;;;;;;;;;;;;;;AA8BA,kDAAkD;AAElD,uCAAuC;AACvC,MAAM,4BAA4B,CAAC;IACjC,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,MAAM,EAAE;QAE1C,sCAAsC;QACtC,MAAM,aAAa,MAAM,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK;gBACtD,OAAO;oBACL,GAAG,IAAI;oBACP,MAAM,IAAI,OAAO,WAAW;gBAC9B;YACF;YACA,OAAO;QACT;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,oCAAoC;YACpC,MAAM,cAAc,WAAW,KAAK,CAAC,GAAG;YACxC,aAAa,OAAO,CAAC;YACrB,OAAO;QACT,OAAO;YACL,4BAA4B;YAC5B,aAAa,OAAO,CAAC;YACrB,OAAO;QACT;IACF,EAAE,OAAO,OAAO,CAChB;IACA,OAAO;AACT;AAEA,SAAS;;IACP,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACnF,MAAM,eAAe,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE,uJAAA,CAAA,mBAAgB,CAAC,aAAa;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,MAAM,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD;IAE7C,oCAAoC;IACpC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;2DAAE;YAC1C,IAAI;gBACF,4CAA4C;gBAC5C,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAC5C,IAAI,gBAAgB;oBAClB,MAAM,aAAa,KAAK,KAAK,CAAC;oBAE9B,6CAA6C;oBAC7C,MAAM,gBAAgB,OAAO,IAAI;yFAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;;oBAC7D,IAAI,eAAe;wBACjB,YAAY;wBACZ,OAAO;oBACT;gBACF;gBAEA,mCAAmC;gBACnC,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,gBAAgB,OAAO,MAAM,GAAG,GAAG;oBACrC,MAAM,aAAa,OAAO,IAAI;sFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oBAC7C,IAAI,YAAY;wBACd,YAAY;wBACZ,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;0DAAG;QAAC;QAAQ;KAAY;IAExB,2DAA2D;IAC3D,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;+DAAE,CAAC;YACxC,MAAM,YAAY,OAAO,gBAAgB,OAAO,QAAQ;YAExD,IAAI,CAAC,OAAO;gBACV,kBAAkB,EAAE;gBACpB,aAAa;gBACb;YACF;YAEA,aAAa;YAEb,IAAI;gBACF,IAAI,cAAc;oBAChB,MAAM,QAAQ,aAAa,OAAO,MAAuB,EAAE;oBAE3D,wCAAwC;oBACxC,MAAM,kBAAkB,MAAM,IAAI;+FAAC,CAAC,OAClC,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO;;oBAGjD,IAAI,iBAAiB;wBACnB,aAAa,UAAU;wBACvB,kBAAkB,EAAE;oBACtB,OAAO;wBACL,kBAAkB;oBACpB;gBAEF,OAAO;oBACL,kBAAkB,EAAE;gBACtB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF,SAAU;gBACR,aAAa;YACf;QACF;8DAAG;QAAC;QAAc;KAAM;IAExB,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YAER,IAAI,CAAC,cAAc;gBACjB,uDAAuD;gBACvD,MAAM,QAAQ;wDAAW;wBACvB,IAAI,OAAO,MAAM,KAAK,GAAG;4BACvB,2CAA2C;4BAC3C,IAAI;gCAAE,OAAO,QAAQ,CAAC;4BAAmB,EAAE,OAAM,CAAE;4BACnD,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,OAAO,MAAM,GAAG,KAAK,CAAC,cAAc;4BAC7C,wCAAwC;4BACxC,MAAM,WAAW;4BAEjB,IAAI,CAAC,UAAU;gCACb,qDAAqD;gCACrD,YAAY,MAAM,CAAC,EAAE;4BACvB;wBACF;oBACF;uDAAG,OAAO,iBAAiB;gBAE3B;kDAAO,IAAM,aAAa;;YAC5B;QACF;qCAAG;QAAC;QAAc,OAAO,MAAM;QAAE;QAAc;QAAQ;QAAa;KAAkB;IAGtF,4EAA4E;IAC5E,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,sDAAsD;YACtD,IAAI,CAAC,MAAM;gBACT,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA,OAAO,MAAM,sCAAsC;YACrD;YAGA,mEAAmE;YAEnE,uDAAuD;YACvD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YAEA,OAAO,MAAM,sCAAsC;QAEnD;;;;;;;;;;;;;;;;;;;;;;;;;MAyBA,GACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA,OAAO,MAAM,+CAA+C;QAC9D;IACF;IAEA,MAAM,sBAAsB,OAAO;QAEjC,8CAA8C;QAC9C,IAAI,gBAAgB,MAAM,kBAAkB;QAE5C,kEAAkE;QAClE,MAAM,WAAW;YAAC;eAAkB;SAAe;QACnD,kBAAkB;QAElB,IAAI,CAAC,cAAc;YACjB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,yCAAyC;YACzC,aAAa,OAAO,CAAC;YAErB,qDAAqD;YACrD,IAAI,MAAM;gBACR,IAAI;oBACF,MAAM,SAAS,MAAM,SAAS;oBAE9B,wCAAwC;oBACxC,MAAM,YAAY;wBAAE,GAAG,aAAa;wBAAE,IAAI;oBAAO;oBACjD,MAAM,eAAe;wBAAC;2BAAc;qBAAe;oBACnD,kBAAkB;oBAClB,aAAa,OAAO,CAAC;oBAErB,MAAM;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;oBACX;gBACF,EAAE,OAAO,gBAAgB;oBACvB,MAAM;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,mCAAmC;YACnC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QAEA,gDAAgD;QAClD;IACF;IAEA,sDAAsD;IACtD,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;YACjB;QACF;QAEA,IAAI;YACF,aAAa,UAAU;YACvB,kBAAkB,EAAE;YACpB,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,uCAAuC,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;YAC5G;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,cAAc;YACjB;QACF;QAEA,IAAI;YACF,MAAM,eAAe,eAAe,GAAG,CAAC,CAAC,OACvC,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;YAE7C,kBAAkB;YAElB,mCAAmC;YACnC,MAAM,YAAY,KAAK,SAAS,CAAC;YACjC,MAAM,UAAU,IAAI,OAAO,MAAM,YAAY;YAE7C,IAAI,UAAU,MAAM,GAAG,SAAS;gBAC9B,iCAAiC;gBACjC,MAAM,eAAe,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,qBAAqB;gBACvF,aAAa,OAAO,CAAC;gBACrB,kBAAkB;gBAElB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,aAAa,OAAO,CAAC;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,eAAY;QAAsC,SAAS;;0BAC1D,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO,cAAc,sCAAsC;0CAE1D,4BACC,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;yDAE1B,6LAAC,+NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,6LAAC;gCAAK,WAAU;0CACb,cAAc,oBAAoB;;;;;;;;;;;;kCAIvC,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAO,WAAU;;sDAChD,6LAAC,qIAAA,CAAA,SAAM;;8DACL,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAiC,KAAI;oDAAO,gBAAa;;;;;;8DAC1E,6LAAC,qIAAA,CAAA,iBAAc;8DAAC,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sDAEvB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;;kDACzB,6LAAC,+IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;4CACP,IAAI,cAAc;gDAChB,MAAM,UAAU,0BAA0B;gDAC1C,IAAI,SAAS;oDACX,kBAAkB;oDAClB,MAAM;wDACJ,OAAO;wDACP,aAAa,CAAC,wBAAwB,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;oDAC7F;gDACF,OAAO;oDACL,MAAM;wDACJ,OAAO;wDACP,aAAa;oDACf;gDACF;4CACF,OAAO;gDACL,MAAM;oDACJ,SAAS;oDACT,OAAO;oDACP,aAAa;gDACf;4CACF;wCACF;kDACD;;;;;;;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,aAAa,6BACZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;uCAGL,CAAC,6BACH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAGhD,OAAO,MAAM,GAAG,kBACf,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,IAAM,YAAY;gDAC3B,SAAQ;0DAEP,MAAM,YAAY,IAAI,MAAM,IAAI;+CAJ5B,MAAM,EAAE;;;;;;;;;6DASnB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,cAAc,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAmB;;;;;;;;;;;qDAMnK,6LAAC;gCAAI,WAAU;0CAcZ,8BACC,6LAAC,yJAAA,CAAA,kBAAe;oCACd,cAAc;wCACZ,cAAc,aAAa,YAAY;wCACvC,cAAc,aAAa,YAAY,IAAI;wCAC3C,UAAU,aAAa,QAAQ,IAAI;wCACnC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,eAAe,aAAa,aAAa,IAAI;wCAC7C,YAAY,aAAa,UAAU,IAAI;wCACvC,aAAa,aAAa,WAAW,IAAI;wCACzC,oFAAoF;wCACpF,UAAU,MAAM,OAAO,CAAC,AAAC,aAAqB,QAAQ,IAClD,AAAC,aAAqB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,IAAI,EAAE,IAAI,CAAC,QAC5D,AAAC,aAAqB,QAAQ,IAAI;wCACtC,gBAAgB,aAAa,cAAc,IAAI;wCAC/C,aAAa,aAAa,WAAW,IAAI;wCACzC,uBAAuB,aAAa,qBAAqB,IAAI;wCAC7D,aAAa;4CACX,OAAO,aAAa,YAAY,IAAI;4CACpC,OAAO,aAAa,YAAY,IAAI;4CACpC,SAAS,aAAa,cAAc,IAAI;wCAC1C;wCACA,aAAa;4CACX,UAAU,aAAa,WAAW,IAAI;4CACtC,WAAW,aAAa,YAAY,IAAI;4CACxC,SAAS,aAAa,UAAU,IAAI;4CACpC,UAAU,aAAa,WAAW,IAAI;wCACxC;wCACA,cAAc,aAAa,YAAY,IAAI;wCAC3C,aAAa,aAAa,WAAW,IAAI;wCACzC,iBAAiB,aAAa,eAAe,IAAI;wCACjD,gBAAgB,aAAa,cAAc,IAAI,EAAE;oCACnD;oCACA,OAAO;oCACP,iBAAiB;oCACjB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAtJd,cAAc,MAAM;;;;;AAiK3C;GA3cS;;QAC8D,kJAAA,CAAA,kBAAe;QAC/D,kJAAA,CAAA,kBAAe;QAGrB,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;QACmB,sIAAA,CAAA,aAAU;QACtC,0IAAA,CAAA,kBAAe;QACH,4IAAA,CAAA,oBAAiB;QAmC9C,kJAAA,CAAA,yBAAsB;;;KA5Cf;AA6cT,SAAS;IACP,qBACE,6LAAC,6JAAA,CAAA,qBAAkB;;0BACjB,6LAAC;;;;;0BACD,6LAAC,6JAAA,CAAA,uBAAoB;;;;;;;;;;;AAG3B;MAPS;uCASM", "debugId": null}}]}