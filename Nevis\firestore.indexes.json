{"indexes": [{"collectionGroup": "generatedPosts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "brandProfiles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "artifacts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "designAnalytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "businessType", "order": "ASCENDING"}, {"fieldPath": "generatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}