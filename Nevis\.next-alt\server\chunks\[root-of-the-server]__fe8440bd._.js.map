{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/test-hashtags/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const businessType = searchParams.get('businessType') || 'restaurant';\r\n    const businessName = searchParams.get('businessName') || 'Samaki Cookies';\r\n    const location = searchParams.get('location') || 'Kenya';\r\n    const platform = searchParams.get('platform') || 'instagram';\r\n    const services = searchParams.get('services') || 'food production';\r\n    const targetAudience = searchParams.get('targetAudience') || 'local community';\r\n\r\n    // Try to import the viral hashtag engine dynamically to catch import errors\r\n    let hashtagStrategy;\r\n    try {\r\n      const { viralHashtagEngine } = await import('@/ai/viral-hashtag-engine');\r\n\r\n      // Test the new advanced hashtag system\r\n      hashtagStrategy = await viralHashtagEngine.generateViralHashtags(\r\n        businessType,\r\n        businessName,\r\n        location,\r\n        platform,\r\n        services,\r\n        targetAudience\r\n      );\r\n    } catch (importError) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Import error with viral hashtag engine',\r\n        details: (importError as Error).message,\r\n        stack: (importError as Error).stack,\r\n        timestamp: new Date().toISOString()\r\n      }, { status: 500 });\r\n    }\r\n\r\n    console.log('✅ Generated hashtag strategy:', {\r\n      totalHashtags: hashtagStrategy.total.length,\r\n      trending: hashtagStrategy.trending.length,\r\n      viral: hashtagStrategy.viral.length,\r\n      niche: hashtagStrategy.niche.length,\r\n      location: hashtagStrategy.location.length,\r\n      analytics: hashtagStrategy.analytics\r\n    });\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Advanced hashtag system test completed',\r\n      input: {\r\n        businessType,\r\n        businessName,\r\n        location,\r\n        platform,\r\n        services,\r\n        targetAudience\r\n      },\r\n      results: {\r\n        totalHashtags: hashtagStrategy.total,\r\n        categories: {\r\n          trending: hashtagStrategy.trending,\r\n          viral: hashtagStrategy.viral,\r\n          niche: hashtagStrategy.niche,\r\n          location: hashtagStrategy.location,\r\n          community: hashtagStrategy.community,\r\n          seasonal: hashtagStrategy.seasonal,\r\n          platform: hashtagStrategy.platform\r\n        },\r\n        analytics: hashtagStrategy.analytics\r\n      },\r\n      timestamp: new Date().toISOString()\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('❌ Hashtag test failed:', error);\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: (error as Error).message,\r\n      stack: (error as Error).stack,\r\n      timestamp: new Date().toISOString()\r\n    }, { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,eAAe,aAAa,GAAG,CAAC,mBAAmB;QACzD,MAAM,eAAe,aAAa,GAAG,CAAC,mBAAmB;QACzD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,aAAa,GAAG,CAAC,qBAAqB;QAE7D,4EAA4E;QAC5E,IAAI;QACJ,IAAI;YACF,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAE/B,uCAAuC;YACvC,kBAAkB,MAAM,mBAAmB,qBAAqB,CAC9D,cACA,cACA,UACA,UACA,UACA;QAEJ,EAAE,OAAO,aAAa;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS,AAAC,YAAsB,OAAO;gBACvC,OAAO,AAAC,YAAsB,KAAK;gBACnC,WAAW,IAAI,OAAO,WAAW;YACnC,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,iCAAiC;YAC3C,eAAe,gBAAgB,KAAK,CAAC,MAAM;YAC3C,UAAU,gBAAgB,QAAQ,CAAC,MAAM;YACzC,OAAO,gBAAgB,KAAK,CAAC,MAAM;YACnC,OAAO,gBAAgB,KAAK,CAAC,MAAM;YACnC,UAAU,gBAAgB,QAAQ,CAAC,MAAM;YACzC,WAAW,gBAAgB,SAAS;QACtC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA,SAAS;gBACP,eAAe,gBAAgB,KAAK;gBACpC,YAAY;oBACV,UAAU,gBAAgB,QAAQ;oBAClC,OAAO,gBAAgB,KAAK;oBAC5B,OAAO,gBAAgB,KAAK;oBAC5B,UAAU,gBAAgB,QAAQ;oBAClC,WAAW,gBAAgB,SAAS;oBACpC,UAAU,gBAAgB,QAAQ;oBAClC,UAAU,gBAAgB,QAAQ;gBACpC;gBACA,WAAW,gBAAgB,SAAS;YACtC;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,AAAC,MAAgB,OAAO;YAC/B,OAAO,AAAC,MAAgB,KAAK;YAC7B,WAAW,IAAI,OAAO,WAAW;QACnC,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}