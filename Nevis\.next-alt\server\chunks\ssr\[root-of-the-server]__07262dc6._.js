module.exports = {

"[externals]/perf_hooks [external] (perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("perf_hooks", () => require("perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/async_hooks [external] (async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("async_hooks", () => require("async_hooks"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/require-in-the-middle [external] (require-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("require-in-the-middle", () => require("require-in-the-middle"));

module.exports = mod;
}}),
"[externals]/import-in-the-middle [external] (import-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("import-in-the-middle", () => require("import-in-the-middle"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/http2 [external] (http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http2", () => require("http2"));

module.exports = mod;
}}),
"[externals]/dns [external] (dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("dns", () => require("dns"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/express [external] (express, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("express", () => require("express"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/ai/genkit.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ai": (()=>ai)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/genkit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-rsc] (ecmascript) <locals>");
;
;
// Get API key from environment variables
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
if (!apiKey) {}
const ai = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["genkit"])({
    plugins: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["googleAI"])({
            apiKey
        })
    ],
    model: 'googleai/gemini-2.0-flash'
});
}}),
"[project]/src/ai/flows/analyze-brand.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"409349e0fc889b36f6229f35c5a5e8162fd5a0204d":"analyzeBrand"},"",""] */ __turbopack_context__.s({
    "analyzeBrand": (()=>analyzeBrand)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview Analyzes a brand's website and design examples to extract brand voice, visual style, and other key business details.
 *
 * - analyzeBrand - A function that initiates the brand analysis process.
 * - AnalyzeBrandInput - The input type for the analyzeBrand function.
 * - AnalyzeBrandOutput - The return type for the analyzeBrand function.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
const AnalyzeBrandInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    websiteUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The URL of the brand\'s website to analyze.'),
    designImageUris: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe("A list of data URIs of previous design examples. Each must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."),
    websiteContent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The scraped content from the website for analysis.')
});
const AnalyzeBrandOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    // Core Business Information
    businessName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The EXACT business name, company name, or brand name as it appears on the website. This should be the PROPER NAME like "Apple Inc.", "Microsoft Corporation", "Joe\'s Pizza", NOT a description of what they do. Look for the company name in headers, logos, titles, "About Us" sections, or anywhere the business identifies itself. Extract the precise name they use, not their business type or industry.'),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A comprehensive, detailed summary of the business that includes: what they do, how they do it, their mission/values, their approach, their history, and what makes them unique. Combine information from multiple website sections to create a thorough description. Minimum 3-4 sentences using the company\'s own words.'),
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The specific type/category of business like "Software Company", "Restaurant", "Consulting Firm", "E-commerce Store" - this describes WHAT they do, not WHO they are. This is different from the business name.'),
    industry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The specific industry sector the business operates in using their own terminology.'),
    targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('DETAILED description of the specific target audience, customer base, client types, demographics, business types, industries, or customer characteristics this company mentions they serve. Be very specific and comprehensive. Include customer examples, business sizes, industries, or any specific customer details mentioned on the website.'),
    // Services and Products
    services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A comprehensive newline-separated list of ALL services, products, packages, plans, or offerings this specific company provides. Search the entire website content thoroughly. Format each as "Service Name: Detailed description as written on their website including features, benefits, what\'s included". Extract the company\'s own descriptions, not generic ones. Include pricing, packages, service tiers, features, or any details mentioned. Be comprehensive and don\'t miss any services.'),
    keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('ALL the SPECIFIC key features, benefits, or unique selling propositions that THIS company highlights about their offerings. Use their exact wording and claims. Be comprehensive and detailed.'),
    competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('What THIS specific company says makes them different from competitors. Extract their own competitive claims and differentiators, not generic industry advantages. Use their exact wording.'),
    // Brand Identity and Voice
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A detailed description of THIS company\'s specific visual style based on their actual design examples and website. Describe the exact colors, typography, layout patterns, imagery style, and aesthetic choices THEY use. Reference specific design elements visible in their materials.'),
    writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The SPECIFIC writing tone and voice THIS company uses in their actual website content. Analyze their actual text, headlines, and copy to describe their unique communication style. Use examples from their content.'),
    contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The SPECIFIC themes, topics, and messaging patterns THIS company focuses on in their actual content. Extract the exact topics they discuss and how they position themselves.'),
    brandPersonality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('THIS company\'s specific brand personality as expressed through their actual content and design choices. Base this on their real communications, not generic assumptions.'),
    // Visual Design Analysis
    colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Primary brand color in hex format extracted from the uploaded design examples. Look carefully at the most prominent color used in logos, headers, buttons, or main design elements in the images.'),
        secondary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Secondary brand color in hex format extracted from the uploaded design examples. Look for the second most used color in the designs.'),
        accent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Accent color in hex format extracted from the uploaded design examples. Look for colors used for highlights, calls-to-action, or accent elements in the images.'),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Detailed description of the overall color scheme and palette used in the design examples. Describe the colors you can actually see in the uploaded images and the mood/feeling they create.')
    }).optional().describe('Color palette analysis extracted from the uploaded design examples. Analyze the actual colors visible in the design images provided.'),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Typography style (e.g., modern, classic, playful, professional).'),
        characteristics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Font characteristics and typography choices observed.')
    }).optional().describe('Typography analysis from design examples and website.'),
    // Contact and Location Information
    contactInfo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main contact phone number.'),
        email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main contact email address.'),
        address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The physical business address.'),
        website: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Additional website URLs or domains mentioned.'),
        hours: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Business hours if mentioned on the website.')
    }).describe('The contact information for the business, extracted from the website.'),
    // Social Media and Online Presence
    socialMedia: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Facebook page URL if found on the website.'),
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Instagram profile URL if found on the website.'),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Twitter profile URL if found on the website.'),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('LinkedIn profile URL if found on the website.'),
        youtube: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('YouTube channel URL if found on the website.'),
        other: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Other social media or platform URLs found.')
    }).optional().describe('Social media presence and URLs found on the website.'),
    // Additional Business Details
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Geographic location or service area of the business.'),
    establishedYear: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Year the business was established if mentioned.'),
    teamSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Information about team size or company size if mentioned.'),
    certifications: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Professional certifications, awards, or credentials mentioned.'),
    // Content and Marketing Insights
    contentStrategy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Insights into their content marketing strategy based on website content.'),
    callsToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Common calls-to-action used throughout the website.'),
    valueProposition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main value proposition or promise to customers.')
});
async function analyzeBrand(input) {
    return analyzeBrandFlow(input);
}
const analyzeBrandPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeBrandPrompt',
    input: {
        schema: AnalyzeBrandInputSchema
    },
    output: {
        schema: AnalyzeBrandOutputSchema
    },
    prompt: `You are an expert brand strategist, business analyst, and design consultant with deep expertise in brand identity, visual design, and digital marketing. Your task is to perform an extremely comprehensive and detailed analysis of THIS SPECIFIC BUSINESS based on its website and design examples.

  **CRITICAL INSTRUCTION: BE COMPANY-SPECIFIC, NOT GENERIC**
  - Extract ONLY information that is specifically mentioned on THIS company's website
  - Use the EXACT wording and terminology that THIS company uses
  - Do NOT provide generic industry descriptions or assumptions
  - Focus on what makes THIS specific business unique and different
  - Extract the company's OWN words about their services, not generic descriptions

  **Source Information:**
  - Website URL: {{{websiteUrl}}}
  - Website Content: {{{websiteContent}}}
  - Design Examples: These are crucial for understanding visual style, color palette, typography, and brand aesthetic.
  {{#each designImageUris}}
  Design Example: {{media url=this}}
  {{/each}}

  **COMPANY-SPECIFIC ANALYSIS REQUIREMENTS:**

  **🏢 THIS COMPANY'S BUSINESS DETAILS (Extract from Website Content Above):**
  1. **Business Name:** Extract the EXACT business name, company name, or brand name as it appears on the website. Look for the company name in headers, logos, page titles, "About Us" sections, contact information, or anywhere the business identifies itself. Extract the precise name they use - this is critical for brand identity.
  2. **Business Description:** Find and extract a COMPREHENSIVE and DETAILED description of this company from the website content. Look for "About Us", "Who We Are", "Our Story", "Mission", "Vision" sections. Combine multiple sections to create a thorough description that includes: what they do, how they do it, their mission/values, their history, their approach, and what makes them unique. Use their own words but create a complete picture. Minimum 3-4 sentences.
  3. **Business Type & Industry:** Use the SPECIFIC terms this company uses in their website content to describe their business type and industry. Be precise and specific.
  4. **Target Audience:** This is CRITICAL - Extract EXACTLY who this company says they serve from the website content. Look for "Who We Serve", "Our Clients", "Target Market", "Perfect For", "Ideal Customers", "We Help" sections. Also look for customer testimonials, case studies, or examples that indicate their target market. Include demographics, business types, industries, or specific customer characteristics they mention. Be very detailed and specific.
  5. **Services/Products:** Extract EVERY service and product this company specifically offers from the website content. Look in "Services", "What We Do", "Products", "Solutions", "Offerings", "Packages", "Plans", "Pricing" sections. Use their EXACT service names and descriptions as written. Include ALL services, packages, tiers, or offerings mentioned. Format as "Service Name: Detailed description as written on their website" on separate lines. Don't miss any services.
  6. **Key Features & Benefits:** Extract ALL the SPECIFIC features and benefits this company highlights about their offerings from the website content. Look in "Features", "Benefits", "Why Choose Us" sections. Use their exact wording and claims. Be comprehensive.
  7. **Competitive Advantages:** Extract what THIS company specifically says makes them different or better from the website content. Look for "Why Choose Us", "What Makes Us Different", "Our Advantage", "Why We're Better" sections. Use their own competitive claims and differentiators.
  8. **Value Proposition:** Extract the EXACT value proposition or promises this company makes to their customers from the website content. What do they promise to deliver?

  **🎨 VISUAL DESIGN DEEP ANALYSIS (Analyze the Design Examples Carefully):**
  8. **Visual Style:** Provide a detailed analysis of the overall visual aesthetic, design approach, imagery style, layout patterns, and visual hierarchy. Base this primarily on the design examples provided. Describe the specific design elements you can see in the uploaded images.
  9. **Color Palette Analysis - CRITICAL:**
     - CAREFULLY examine each design example image to identify the EXACT colors used
     - Extract specific colors in hex format from the designs (look at backgrounds, text, buttons, accents, logos)
     - Identify the primary brand color (most prominent color in the designs)
     - Identify secondary colors and accent colors used in the designs
     - Describe the overall color scheme and mood it creates
     - Be very specific about the colors you can see in the uploaded design examples
  10. **Typography Analysis:**
     - Examine the design examples to describe the actual font styles and typography choices used
     - Identify if fonts are modern, classic, playful, professional, etc. based on what you see in the images
     - Note any distinctive typographic elements visible in the design examples

  **✍️ BRAND VOICE & CONTENT ANALYSIS:**
  11. **Writing Tone:** Analyze the brand's communication style in detail (formal, casual, witty, professional, friendly, authoritative, conversational, etc.).
  12. **Content Themes:** Identify recurring themes, topics, and messaging patterns throughout the website and designs.
  13. **Brand Personality:** Describe the overall brand character and personality as expressed through content and design.
  14. **Content Strategy:** Analyze their approach to content marketing and communication.
  15. **Calls to Action:** Extract common CTAs and action-oriented language used.

  **📞 CONTACT & BUSINESS DETAILS:**
  16. **Complete Contact Information:** Extract phone numbers, email addresses, physical addresses, business hours, and any additional contact methods.
  17. **Location & Service Area:** Identify geographic location and areas they serve.
  18. **Business Details:** Look for establishment year, team size, company history, certifications, awards, or credentials.

  **🌐 DIGITAL PRESENCE ANALYSIS:**
  19. **Social Media Presence:** Extract ALL social media URLs found (Facebook, Instagram, Twitter, LinkedIn, YouTube, TikTok, etc.).
  20. **Additional Websites:** Note any additional domains, subdomains, or related websites mentioned.

  **CRITICAL ANALYSIS INSTRUCTIONS - COMPANY-SPECIFIC EXTRACTION:**

  **FOR SERVICES/PRODUCTS (Search ALL Sections in Website Content Above):**
  - Search the website content for "Services", "What We Do", "Our Services", "Products", "Solutions", "Offerings", "Packages", "Plans", "Pricing" sections
  - Extract EVERY service name as the company lists them in their website content - don't miss any
  - Include the company's OWN descriptions of each service from their website text
  - Look for pricing information, package details, service tiers, features included in each service
  - Look in multiple sections - services might be mentioned in different parts of the website
  - Format as: "Service Name: Company's exact description of what this service includes, features, benefits, etc."
  - Include any pricing tiers, packages, or service levels mentioned in the content
  - Be comprehensive - extract ALL services, not just the main ones

  **FOR TARGET AUDIENCE (Search ALL Sections for Customer Information):**
  - Search the website content for "Who We Serve", "Our Clients", "Target Market", "Perfect For", "Ideal Customers", "We Help", "Client Types" sections
  - Look for customer testimonials, case studies, or examples that indicate their target market
  - Extract specific demographics, business types, industries, company sizes, or customer characteristics they mention
  - Look for phrases like "small businesses", "enterprise clients", "startups", "restaurants", "healthcare providers", etc.
  - Include any specific customer examples or client types mentioned
  - Be very detailed and specific about who they serve

  **FOR BUSINESS DESCRIPTION (Create Comprehensive Description):**
  - Search the website content for "About Us", "Who We Are", "Our Story", "Mission", "Vision", "Company" sections
  - Combine information from multiple sections to create a thorough, detailed description
  - Include what they do, how they do it, their mission/values, their approach, their history, what makes them unique
  - Use their own words but create a complete, comprehensive picture
  - Make it detailed and informative - minimum 3-4 sentences

  **FOR TARGET AUDIENCE:**
  - Look for "Who We Serve", "Our Clients", "Target Market" information
  - Extract the SPECIFIC customer types they mention
  - Use their exact terminology for their customer base

  **FOR COMPETITIVE ADVANTAGES:**
  - Find "Why Choose Us", "What Makes Us Different", "Our Advantage" sections
  - Extract their SPECIFIC claims about what makes them unique
  - Use their exact competitive positioning statements

  **GENERAL EXTRACTION RULES:**
  - Be extremely thorough and detailed in your analysis
  - Extract every piece of relevant information you can find
  - For design analysis, pay close attention to the uploaded design examples
  - Look for subtle details like color codes, font choices, layout patterns
  - Extract contact information from headers, footers, contact pages, and anywhere else it appears
  - Look for social media links in headers, footers, and throughout the site
  - If information is not available, leave those fields empty rather than guessing
  - NEVER use generic industry descriptions - only use company-specific information
  - Quote the company's exact wording whenever possible

  **FINAL REQUIREMENTS:**
  - Be EXTREMELY thorough and comprehensive in your analysis
  - Extract EVERY piece of relevant information from the website content
  - Don't miss any services, features, or customer details
  - Analyze the design examples carefully for exact colors
  - Create detailed, informative descriptions using the company's own words
  - Target audience description must be specific and detailed
  - Services list must be comprehensive and complete
  - Color analysis must be based on actual colors visible in the design examples

  **OUTPUT FORMAT:**
  Provide a complete, detailed analysis in the required JSON format with all available information extracted and organized according to the schema.
  `
});
// Website scraping function with enhanced content extraction
async function scrapeWebsiteContent(url) {
    try {
        // Import cheerio for HTML parsing
        const cheerio = await __turbopack_context__.r("[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use fetch to get the website content
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const html = await response.text();
        const $ = cheerio.load(html);
        // Remove unwanted elements
        $('script, style, nav, footer, header, .cookie-banner, .popup, .modal').remove();
        // Extract structured content
        const extractedContent = {
            title: $('title').text().trim(),
            metaDescription: $('meta[name="description"]').attr('content') || '',
            headings: {
                h1: $('h1').map((_, el)=>$(el).text().trim()).get(),
                h2: $('h2').map((_, el)=>$(el).text().trim()).get(),
                h3: $('h3').map((_, el)=>$(el).text().trim()).get()
            },
            // Look for common business sections with more comprehensive selectors
            aboutSection: $('section:contains("About"), div:contains("About Us"), .about, #about, section:contains("Who We Are"), div:contains("Our Story"), .story, #story').text().trim(),
            servicesSection: $('section:contains("Services"), div:contains("Services"), .services, #services, section:contains("What We Do"), div:contains("What We Do"), section:contains("Solutions"), div:contains("Solutions"), .solutions, #solutions, section:contains("Offerings"), div:contains("Offerings")').text().trim(),
            contactSection: $('section:contains("Contact"), div:contains("Contact"), .contact, #contact, section:contains("Get in Touch"), div:contains("Reach Us")').text().trim(),
            // Enhanced target audience extraction
            targetAudienceSection: $('section:contains("Who We Serve"), div:contains("Who We Serve"), section:contains("Our Clients"), div:contains("Our Clients"), section:contains("Target"), div:contains("Target"), section:contains("For"), div:contains("Perfect For"), .clients, #clients, .audience, #audience').text().trim(),
            // More comprehensive service extraction
            featuresSection: $('section:contains("Features"), div:contains("Features"), .features, #features, section:contains("Benefits"), div:contains("Benefits"), .benefits, #benefits').text().trim(),
            packagesSection: $('section:contains("Packages"), div:contains("Packages"), .packages, #packages, section:contains("Plans"), div:contains("Plans"), .plans, #plans, section:contains("Pricing"), div:contains("Pricing"), .pricing, #pricing').text().trim(),
            // Extract all paragraph text
            paragraphs: $('p').map((_, el)=>$(el).text().trim()).get().filter((text)=>text.length > 20),
            // Extract list items (often contain services/features)
            listItems: $('li').map((_, el)=>$(el).text().trim()).get().filter((text)=>text.length > 10),
            // Extract any text that might contain business info
            mainContent: $('main, .main, .content, .container').text().trim()
        };
        // Combine all extracted content into a structured format
        let structuredContent = '';
        if (extractedContent.title) {
            structuredContent += `WEBSITE TITLE: ${extractedContent.title}\n\n`;
        }
        if (extractedContent.metaDescription) {
            structuredContent += `META DESCRIPTION: ${extractedContent.metaDescription}\n\n`;
        }
        if (extractedContent.headings.h1.length > 0) {
            structuredContent += `MAIN HEADINGS: ${extractedContent.headings.h1.join(' | ')}\n\n`;
        }
        if (extractedContent.aboutSection) {
            structuredContent += `ABOUT SECTION: ${extractedContent.aboutSection}\n\n`;
        }
        if (extractedContent.servicesSection) {
            structuredContent += `SERVICES SECTION: ${extractedContent.servicesSection}\n\n`;
        }
        if (extractedContent.targetAudienceSection) {
            structuredContent += `TARGET AUDIENCE SECTION: ${extractedContent.targetAudienceSection}\n\n`;
        }
        if (extractedContent.featuresSection) {
            structuredContent += `FEATURES/BENEFITS SECTION: ${extractedContent.featuresSection}\n\n`;
        }
        if (extractedContent.packagesSection) {
            structuredContent += `PACKAGES/PRICING SECTION: ${extractedContent.packagesSection}\n\n`;
        }
        if (extractedContent.contactSection) {
            structuredContent += `CONTACT SECTION: ${extractedContent.contactSection}\n\n`;
        }
        if (extractedContent.listItems.length > 0) {
            structuredContent += `KEY POINTS/SERVICES: ${extractedContent.listItems.slice(0, 20).join(' | ')}\n\n`;
        }
        if (extractedContent.paragraphs.length > 0) {
            structuredContent += `MAIN CONTENT: ${extractedContent.paragraphs.slice(0, 10).join(' ')}\n\n`;
        }
        // Fallback to main content if structured extraction didn't work well
        if (structuredContent.length < 500 && extractedContent.mainContent) {
            structuredContent += `FULL CONTENT: ${extractedContent.mainContent}`;
        }
        // Clean up and limit content length
        structuredContent = structuredContent.replace(/\s+/g, ' ').trim();
        // Limit content length to avoid token limits (increased for better analysis)
        if (structuredContent.length > 15000) {
            structuredContent = structuredContent.substring(0, 15000) + '...';
        }
        return structuredContent;
    } catch (error) {
        throw new Error(`Failed to scrape website content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
const analyzeBrandFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'analyzeBrandFlow',
    inputSchema: AnalyzeBrandInputSchema,
    outputSchema: AnalyzeBrandOutputSchema
}, async (input)=>{
    // First, scrape the website content
    const websiteContent = await scrapeWebsiteContent(input.websiteUrl);
    // Create enhanced input with website content
    const enhancedInput = {
        ...input,
        websiteContent
    };
    const { output } = await analyzeBrandPrompt(enhancedInput);
    return output;
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    analyzeBrand
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(analyzeBrand, "409349e0fc889b36f6229f35c5a5e8162fd5a0204d", null);
}}),
"[project]/src/ai/models/registry/model-registry.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Registry
 * Central registry for managing all Revo model implementations
 */ __turbopack_context__.s({
    "ModelRegistry": (()=>ModelRegistry),
    "modelRegistry": (()=>modelRegistry)
});
class ModelRegistry {
    models = new Map();
    initialized = false;
    /**
   * Register a model implementation
   */ registerModel(implementation) {
        const modelId = implementation.model.id;
        if (this.models.has(modelId)) {}
        this.models.set(modelId, implementation);
    }
    /**
   * Get a specific model implementation
   */ getModel(id) {
        return this.models.get(id) || null;
    }
    /**
   * Get all registered models
   */ getAllModels() {
        return Array.from(this.models.values());
    }
    /**
   * Get only available models (those that pass availability check)
   */ async getAvailableModels() {
        const allModels = this.getAllModels();
        const availabilityChecks = await Promise.allSettled(allModels.map(async (model)=>({
                model,
                available: await model.isAvailable()
            })));
        return availabilityChecks.filter((result)=>result.status === 'fulfilled' && result.value.available).map((result)=>result.value.model);
    }
    /**
   * Get models by status
   */ getModelsByStatus(status) {
        return this.getAllModels().filter((impl)=>impl.model.status === status);
    }
    /**
   * Get models by capability
   */ getModelsByCapability(capability) {
        return this.getAllModels().filter((impl)=>impl.model.capabilities[capability]);
    }
    /**
   * Find the best model based on selection criteria
   */ async selectBestModel(criteria) {
        const availableModels = await this.getAvailableModels();
        if (availableModels.length === 0) {
            return null;
        }
        // If user has a preference, try to use it
        if (criteria.userPreference) {
            const preferredModel = availableModels.find((m)=>m.model.id === criteria.userPreference);
            if (preferredModel && this.meetsRequirements(preferredModel, criteria)) {
                return preferredModel;
            }
        }
        // Score models based on criteria
        const scoredModels = availableModels.map((model)=>({
                model,
                score: this.scoreModel(model, criteria)
            })).filter(({ score })=>score > 0) // Only models that meet minimum requirements
        .sort((a, b)=>b.score - a.score); // Sort by score descending
        return scoredModels.length > 0 ? scoredModels[0].model : null;
    }
    /**
   * Check if a model meets the minimum requirements
   */ meetsRequirements(model, criteria) {
        // Check required capabilities
        if (criteria.requiredCapabilities) {
            for (const capability of criteria.requiredCapabilities){
                if (!model.model.capabilities[capability]) {
                    return false;
                }
            }
        }
        // Check credit limit
        if (criteria.maxCredits && model.model.pricing.creditsPerGeneration > criteria.maxCredits) {
            return false;
        }
        // Check platform support
        if (criteria.platform && !model.model.capabilities.supportedPlatforms.includes(criteria.platform)) {
            return false;
        }
        return true;
    }
    /**
   * Score a model based on selection criteria
   */ scoreModel(model, criteria) {
        let score = 0;
        // Base score for meeting requirements
        if (!this.meetsRequirements(model, criteria)) {
            return 0;
        }
        score += 50; // Base score for meeting requirements
        // Quality preference scoring
        if (criteria.qualityPreference) {
            switch(criteria.qualityPreference){
                case 'quality':
                    score += model.model.capabilities.maxQuality * 2;
                    break;
                case 'speed':
                    // Prefer models with faster processing (lower tier = faster)
                    score += model.model.pricing.tier === 'basic' ? 20 : model.model.pricing.tier === 'premium' ? 10 : 5;
                    break;
                case 'balanced':
                    score += model.model.capabilities.maxQuality;
                    score += model.model.pricing.tier === 'premium' ? 15 : 10;
                    break;
            }
        }
        // Tier preference scoring
        if (criteria.preferredTier) {
            if (model.model.pricing.tier === criteria.preferredTier) {
                score += 20;
            }
        }
        // Credit efficiency scoring
        if (criteria.maxCredits) {
            const efficiency = criteria.maxCredits / model.model.pricing.creditsPerGeneration;
            score += Math.min(efficiency * 5, 15); // Cap at 15 points
        }
        // Status bonus
        switch(model.model.status){
            case 'stable':
                score += 10;
                break;
            case 'enhanced':
                score += 15;
                break;
            case 'development':
                score += 5;
                break;
            case 'beta':
                score += 3;
                break;
        }
        return score;
    }
    /**
   * Get model statistics
   */ getRegistryStats() {
        const models = this.getAllModels();
        const statusCounts = models.reduce((acc, model)=>{
            acc[model.model.status] = (acc[model.model.status] || 0) + 1;
            return acc;
        }, {});
        const tierCounts = models.reduce((acc, model)=>{
            acc[model.model.pricing.tier] = (acc[model.model.pricing.tier] || 0) + 1;
            return acc;
        }, {});
        return {
            totalModels: models.length,
            statusDistribution: statusCounts,
            tierDistribution: tierCounts,
            averageCreditsPerGeneration: models.reduce((sum, m)=>sum + m.model.pricing.creditsPerGeneration, 0) / models.length,
            supportedPlatforms: [
                ...new Set(models.flatMap((m)=>m.model.capabilities.supportedPlatforms))
            ]
        };
    }
    /**
   * Initialize the registry with default models
   */ async initialize() {
        if (this.initialized) {
            return;
        }
        try {
            // Import and register all model implementations
            const { Revo10Implementation } = await __turbopack_context__.r("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const { Revo15Implementation } = await __turbopack_context__.r("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            this.registerModel(new Revo10Implementation());
            this.registerModel(new Revo15Implementation());
            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }
    /**
   * Check if registry is initialized
   */ isInitialized() {
        return this.initialized;
    }
    /**
   * Reset the registry (mainly for testing)
   */ reset() {
        this.models.clear();
        this.initialized = false;
    }
}
const modelRegistry = new ModelRegistry();
;
}}),
"[project]/src/ai/flows/generate-video-post.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-video-post.ts
/* __next_internal_action_entry_do_not_use__ [{"40717d26a7d7c1a30abc155a8a414736898b2471bb":"generateVideoPost"},"",""] */ __turbopack_context__.s({
    "generateVideoPost": (()=>generateVideoPost)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview A Genkit flow for generating a short promotional video for a social media post.
 *
 * This flow utilizes a text-to-video model to create dynamic content based on brand information,
 * local context, and specific post details.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
// Define the input schema for the video generation flow.
const GenerateVideoInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The type of business (e.g., restaurant, salon).'),
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The location of the business (city, state).'),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The visual style of the brand (e.g., modern, vintage).'),
    imageText: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A brief, catchy headline for the video.'),
    postContent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The full text content of the social media post for additional context.')
});
// Define the output schema for the video generation flow.
const GenerateVideoOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The data URI of the generated video file.')
});
async function generateVideoPost(input) {
    return generateVideoPostFlow(input);
}
/**
 * Helper function to download video and convert to data URI
 */ async function videoToDataURI(videoPart) {
    if (!videoPart.media || !videoPart.media.url) {
        throw new Error('Media URL not found in video part.');
    }
    const fetch = (await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    // Add API key before fetching the video.
    const videoDownloadResponse = await fetch(`${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`);
    if (!videoDownloadResponse.ok) {
        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);
    }
    const videoBuffer = await videoDownloadResponse.arrayBuffer();
    const base64Video = Buffer.from(videoBuffer).toString('base64');
    // Default to video/mp4 if contentType is not provided
    const contentType = videoPart.media.contentType || 'video/mp4';
    return `data:${contentType};base64,${base64Video}`;
}
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
/**
 * The core Genkit flow for generating a video post.
 */ const generateVideoPostFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generateVideoPostFlow',
    inputSchema: GenerateVideoInputSchema,
    outputSchema: GenerateVideoOutputSchema
}, async (input)=>{
    const videoPrompt = `Create a short, engaging promotional video with sound for a ${input.businessType} in ${input.location}.
The visual style should be ${input.visualStyle}.
The video should be visually appealing and suitable for a social media post.

The main headline for the video is: "${input.imageText}".

For additional context, here is the full post content that will accompany the video: "${input.postContent}".

Generate a video that is cinematically interesting, has relevant sound, and captures the essence of the post content.`;
    try {
        const result = await generateWithRetry({
            model: 'googleai/veo-3.0-generate-preview',
            prompt: videoPrompt
        });
        let operation = result.operation;
        if (!operation) {
            throw new Error('Expected the model to return an operation');
        }
        // Poll for completion
        while(!operation.done){
            await new Promise((resolve)=>setTimeout(resolve, 5000)); // wait 5s
            operation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].checkOperation(operation);
        }
        if (operation.error) {
            throw new Error(`Video generation failed. Please try again. Error: ${operation.error.message}`);
        }
        // Relaxed check for the video part
        const videoPart = operation.output?.message?.content.find((p)=>!!p.media);
        if (!videoPart || !videoPart.media) {
            throw new Error('No video was generated in the operation result.');
        }
        const videoDataUrl = await videoToDataURI(videoPart);
        return {
            videoUrl: videoDataUrl
        };
    } catch (e) {
        throw new Error(e.message || "Video generation failed. The model may be overloaded. Please try again in a few moments.");
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateVideoPost
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateVideoPost, "40717d26a7d7c1a30abc155a8a414736898b2471bb", null);
}}),
"[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Design Generation Prompts
 * 
 * Professional-grade prompts incorporating design principles, composition rules,
 * typography best practices, color theory, and modern design trends.
 */ __turbopack_context__.s({
    "ADVANCED_DESIGN_PRINCIPLES": (()=>ADVANCED_DESIGN_PRINCIPLES),
    "BUSINESS_TYPE_DESIGN_DNA": (()=>BUSINESS_TYPE_DESIGN_DNA),
    "PLATFORM_SPECIFIC_GUIDELINES": (()=>PLATFORM_SPECIFIC_GUIDELINES),
    "QUALITY_ENHANCEMENT_INSTRUCTIONS": (()=>QUALITY_ENHANCEMENT_INSTRUCTIONS)
});
const ADVANCED_DESIGN_PRINCIPLES = `
**COMPOSITION & VISUAL HIERARCHY:**
- Apply the Rule of Thirds: Position key elements along the grid lines or intersections
- Create clear visual hierarchy using size, contrast, and positioning
- Establish a strong focal point that draws the eye immediately
- Use negative space strategically to create breathing room and emphasis
- Balance elements using symmetrical or asymmetrical composition
- Guide the viewer's eye through the design with leading lines and flow

**TYPOGRAPHY EXCELLENCE:**
- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)
- Use maximum 2-3 font families with strong contrast between them
- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)
- Apply proper letter spacing, line height, and text alignment
- Scale typography appropriately for the platform and viewing distance
- Use typography as a design element, not just information delivery

**COLOR THEORY & HARMONY:**
- Apply color psychology appropriate to the business type and message
- Use complementary colors for high contrast and attention
- Apply analogous colors for harmony and cohesion
- Implement triadic color schemes for vibrant, balanced designs
- Ensure sufficient contrast between text and background
- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent

**MODERN DESIGN TRENDS:**
- Embrace minimalism with purposeful use of white space
- Use bold, geometric shapes and clean lines
- Apply subtle gradients and depth effects when appropriate
- Incorporate authentic, diverse photography when using people
- Use consistent border radius and spacing throughout
- Apply subtle shadows and depth for modern dimensionality
`;
const PLATFORM_SPECIFIC_GUIDELINES = {
    instagram: `
**INSTAGRAM OPTIMIZATION:**
- Design for mobile-first viewing with bold, clear elements
- Use high contrast colors that pop on small screens
- Keep text large and readable (minimum 24px equivalent)
- Center important elements for square crop compatibility
- Use Instagram's native color palette trends
- Design for both feed and story formats
- Optimize for thumb-stopping power in fast scroll feeds
- Logo placement: Bottom right corner or integrated naturally into design
- Ensure logo is visible but doesn't overwhelm the main content
`,
    facebook: `
**FACEBOOK OPTIMIZATION:**
- Design for both desktop and mobile viewing
- Use Facebook blue (#1877F2) strategically for CTAs
- Optimize for news feed algorithm preferences
- Include clear value proposition in visual hierarchy
- Design for engagement and shareability
- Use authentic, relatable imagery
- Optimize for both organic and paid placement
- Logo placement: Top left or bottom right corner for brand recognition
- Ensure logo works well in both desktop and mobile formats
`,
    twitter: `
**TWITTER/X OPTIMIZATION:**
- Design for rapid consumption and high engagement
- Use bold, contrasting colors that stand out in timeline
- Keep text minimal and impactful
- Design for retweet and quote tweet functionality
- Use trending visual styles and memes appropriately
- Optimize for both light and dark mode viewing
- Create thumb-stopping visuals for fast-scrolling feeds
- Logo placement: Small, subtle placement that doesn't interfere with content
- Ensure logo is readable in both light and dark modes
`,
    linkedin: `
**LINKEDIN OPTIMIZATION:**
- Use professional, business-appropriate color schemes
- Apply corporate design standards and clean aesthetics
- Include clear value proposition for business audience
- Use professional photography and imagery
- Design for thought leadership and expertise positioning
- Apply subtle, sophisticated design elements
- Optimize for professional networking context
- Logo placement: Prominent placement for brand authority and recognition
- Ensure logo conveys professionalism and trustworthiness
`
};
const BUSINESS_TYPE_DESIGN_DNA = {
    restaurant: `
**RESTAURANT DESIGN DNA:**
- Use warm, appetizing colors (reds, oranges, warm yellows)
- Include high-quality food photography with proper lighting
- Apply rustic or modern clean aesthetics based on restaurant type
- Use food-focused typography (script for upscale, bold sans for casual)
- Include appetite-triggering visual elements
- Apply golden hour lighting effects for food imagery
- Use complementary colors that enhance food appeal
- Show diverse people enjoying meals in authentic, social settings
- Include cultural food elements that reflect local cuisine traditions
- Display chefs, staff, and customers from the local community
- Use table settings and dining environments that feel culturally authentic
`,
    fitness: `
**FITNESS DESIGN DNA:**
- Use energetic, motivational color schemes (bright blues, oranges, greens)
- Include dynamic action shots and movement
- Apply bold, strong typography with impact
- Use high-contrast designs for motivation and energy
- Include progress and achievement visual metaphors
- Apply athletic and performance-focused imagery
- Use inspiring and empowering visual language
- Show diverse athletes and fitness enthusiasts in action
- Include people of different body types, ages, and fitness levels
- Display authentic workout environments and community settings
- Use culturally relevant sports and fitness activities for the region
`,
    beauty: `
**BEAUTY DESIGN DNA:**
- Use sophisticated, elegant color palettes (pastels, metallics)
- Include high-quality beauty photography with perfect lighting
- Apply clean, minimalist aesthetics with luxury touches
- Use elegant, refined typography
- Include aspirational and transformational imagery
- Apply soft, flattering lighting effects
- Use premium and luxurious visual elements
- Show diverse models representing different skin tones, ages, and beauty standards
- Include authentic beauty routines and self-care moments
- Display culturally relevant beauty practices and aesthetics
- Use inclusive representation that celebrates natural beauty diversity
`,
    tech: `
**TECH DESIGN DNA (CANVA-QUALITY):**
- Use sophisticated, professional color schemes (modern blues, elegant grays, clean whites)
- Include polished, well-designed layouts with strategic geometric elements and refined shapes
- Apply professional business visual metaphors with premium stock photography quality
- Use modern, bold typography with clear hierarchy (multiple font weights and sizes)
- Include high-quality business imagery: professional office spaces, authentic workplace scenarios
- Apply elegant design effects: subtle gradients, refined shadows, tasteful borders
- Use trustworthy and sophisticated visual language that matches premium Canva templates
- Show diverse tech professionals in polished, well-lit business environments
- Include people using technology in professional, aspirational business contexts
- Display modern office spaces, premium remote work setups, and sophisticated business environments
- Use strategic design elements: elegant shapes, professional patterns, refined layouts
- Create designs that look intentionally crafted and professionally designed
- FOCUS: Premium stock photography quality, sophisticated layouts, Canva-level polish
`,
    ecommerce: `
**E-COMMERCE DESIGN DNA:**
- Use conversion-focused color schemes (trust blues, urgency reds, success greens)
- Include high-quality product photography with lifestyle context
- Apply clean, scannable layouts with clear hierarchy
- Use action-oriented typography and compelling CTAs
- Include social proof and trust signals
- Apply mobile-first responsive design principles
- Use persuasive and benefit-focused visual language
- Show diverse customers using products in real-life situations
- Include authentic unboxing and product experience moments
- Display culturally relevant usage scenarios and lifestyle contexts
`,
    healthcare: `
**HEALTHCARE DESIGN DNA:**
- Use calming, trustworthy color palettes (soft blues, greens, whites)
- Include professional medical imagery with human warmth
- Apply clean, accessible design with clear information hierarchy
- Use readable, professional typography
- Include caring and compassionate visual elements
- Apply medical accuracy with approachable aesthetics
- Use reassuring and professional visual language
- Show diverse healthcare professionals and patients
- Include authentic care moments and medical environments
- Display culturally sensitive healthcare interactions and settings
`,
    education: `
**EDUCATION DESIGN DNA:**
- Use inspiring, growth-focused color schemes (blues, greens, warm oranges)
- Include diverse learning environments and educational moments
- Apply organized, structured layouts with clear learning paths
- Use friendly, accessible typography
- Include knowledge and achievement visual metaphors
- Apply bright, optimistic design elements
- Use encouraging and empowering visual language
- Show students and educators from diverse backgrounds
- Include authentic classroom and learning environments
- Display culturally relevant educational practices and settings
`,
    default: `
**UNIVERSAL DESIGN DNA:**
- Use brand-appropriate color psychology
- Include authentic, high-quality imagery
- Apply clean, professional aesthetics
- Use readable, accessible typography
- Include relevant industry visual metaphors
- Apply consistent brand visual language
- Use trustworthy and professional design elements
- Show diverse people in authentic, relevant contexts
- Include culturally appropriate imagery and design elements
- Display real human connections and authentic moments
`
};
const QUALITY_ENHANCEMENT_INSTRUCTIONS = `
**DESIGN QUALITY STANDARDS:**
- Ensure all text is perfectly readable with sufficient contrast
- Apply consistent spacing and alignment throughout
- Use high-resolution imagery without pixelation or artifacts
- Maintain visual balance and proper proportions
- Ensure brand elements are prominently but naturally integrated
- Apply professional color grading and visual polish
- Create designs that work across different screen sizes
- Ensure accessibility compliance for color contrast and readability

**TECHNICAL EXCELLENCE:**
- Generate crisp, high-resolution images suitable for social media
- Apply proper aspect ratios for platform requirements
- Ensure text overlay is perfectly positioned and readable
- Use consistent visual style throughout the design
- Apply professional lighting and shadow effects
- Ensure logo integration feels natural and branded
- Create designs that maintain quality when compressed for social media
`;
}}),
"[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Analysis Utilities
 * 
 * Intelligent analysis and processing of design examples for better AI generation
 */ __turbopack_context__.s({
    "DesignAnalysisSchema": (()=>DesignAnalysisSchema),
    "analyzeDesignExample": (()=>analyzeDesignExample),
    "extractDesignDNA": (()=>extractDesignDNA),
    "selectOptimalDesignExamples": (()=>selectOptimalDesignExamples)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignAnalysisSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary color in hex format'),
        secondary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Secondary color in hex format'),
        accent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Accent color in hex format'),
        colorHarmony: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'complementary',
            'analogous',
            'triadic',
            'monochromatic',
            'split-complementary'
        ]).describe('Type of color harmony used'),
        colorMood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood conveyed by the color scheme')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        layout: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'centered',
            'left-aligned',
            'right-aligned',
            'asymmetrical',
            'grid-based'
        ]).describe('Primary layout structure'),
        visualHierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How visual hierarchy is established'),
        focalPoint: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary focal point and how it\'s created'),
        balance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'symmetrical',
            'asymmetrical',
            'radial'
        ]).describe('Type of visual balance'),
        whitespace: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'minimal',
            'moderate',
            'generous'
        ]).describe('Use of negative space')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primaryFont: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary font style/category'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Typographic hierarchy structure'),
        textTreatment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Special text treatments or effects'),
        readability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'stylized'
        ]).describe('Text readability level')
    }),
    style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        aesthetic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Emotional mood and feeling'),
        sophistication: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'casual',
            'professional',
            'luxury',
            'playful'
        ]).describe('Level of sophistication'),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Current design trends incorporated')
    }),
    effectiveness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        attention: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),
        clarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Message clarity (1-10)'),
        brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand alignment strength (1-10)'),
        platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform optimization (1-10)')
    })
});
// Design analysis prompt
const designAnalysisPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignExample',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignAnalysisSchema
    },
    prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.

Analyze the provided design image and extract detailed insights about its design elements and effectiveness.

Business Context: {{businessType}}
Platform: {{platform}}
Context: {{designContext}}

Provide a comprehensive analysis covering:

1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact
2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space
3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment
4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation
5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization

Be specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`
});
async function analyzeDesignExample(designImageUrl, businessType, platform, context) {
    try {
        // For now, return a mock analysis to avoid API issues
        // This can be replaced with actual AI analysis once the prompt system is stable
        return {
            colorPalette: {
                primary: '#FF6B6B',
                secondary: '#4ECDC4',
                accent: '#45B7D1',
                colorHarmony: 'complementary',
                colorMood: 'Energetic and modern'
            },
            composition: {
                layout: 'centered',
                visualHierarchy: 'Clear size-based hierarchy with strong focal point',
                focalPoint: 'Central logo and headline combination',
                balance: 'symmetrical',
                whitespace: 'moderate'
            },
            typography: {
                primaryFont: 'Modern sans-serif',
                hierarchy: 'Large headline, medium subtext, small details',
                textTreatment: 'Bold headlines with subtle shadows',
                readability: 'high'
            },
            style: {
                aesthetic: 'Modern minimalist',
                mood: 'Professional and approachable',
                sophistication: 'professional',
                trends: [
                    'Bold typography',
                    'Minimalist design',
                    'High contrast'
                ]
            },
            effectiveness: {
                attention: 8,
                clarity: 9,
                brandAlignment: 8,
                platformOptimization: 7
            }
        };
    } catch (error) {
        throw new Error('Failed to analyze design example');
    }
}
function selectOptimalDesignExamples(designExamples, analyses, contentType, platform, maxExamples = 3) {
    if (!analyses.length || !designExamples.length) {
        return designExamples.slice(0, maxExamples);
    }
    // Score each design based on relevance and effectiveness
    const scoredExamples = designExamples.map((example, index)=>{
        const analysis = analyses[index];
        if (!analysis) return {
            example,
            score: 0
        };
        let score = 0;
        // Weight effectiveness metrics
        score += analysis.effectiveness.attention * 0.3;
        score += analysis.effectiveness.clarity * 0.25;
        score += analysis.effectiveness.brandAlignment * 0.25;
        score += analysis.effectiveness.platformOptimization * 0.2;
        // Bonus for sophisticated designs
        if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {
            score += 1;
        }
        // Bonus for modern trends
        score += analysis.style.trends.length * 0.5;
        return {
            example,
            score,
            analysis
        };
    });
    // Sort by score and return top examples
    return scoredExamples.sort((a, b)=>b.score - a.score).slice(0, maxExamples).map((item)=>item.example);
}
function extractDesignDNA(analyses) {
    if (!analyses.length) return '';
    const commonElements = {
        colors: analyses.map((a)=>a.colorPalette.colorHarmony),
        layouts: analyses.map((a)=>a.composition.layout),
        aesthetics: analyses.map((a)=>a.style.aesthetic),
        moods: analyses.map((a)=>a.style.mood)
    };
    // Find most common elements
    const mostCommonColor = getMostCommon(commonElements.colors);
    const mostCommonLayout = getMostCommon(commonElements.layouts);
    const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);
    const mostCommonMood = getMostCommon(commonElements.moods);
    return `
**EXTRACTED DESIGN DNA:**
- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes
- **Layout Pattern**: Favors ${mostCommonLayout} compositions
- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach
- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout
- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation
- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure
`;
}
/**
 * Helper function to find most common element in array
 */ function getMostCommon(arr) {
    const counts = arr.reduce((acc, item)=>{
        acc[item] = (acc[item] || 0) + 1;
        return acc;
    }, {});
    return Object.entries(counts).reduce((a, b)=>counts[a[0]] > counts[b[0]] ? a : b)[0];
}
}}),
"[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Quality Validation and Enhancement
 * 
 * System for validating, scoring, and iteratively improving generated designs
 */ __turbopack_context__.s({
    "DesignQualitySchema": (()=>DesignQualitySchema),
    "assessDesignQuality": (()=>assessDesignQuality),
    "calculateWeightedScore": (()=>calculateWeightedScore),
    "generateImprovementPrompt": (()=>generateImprovementPrompt),
    "meetsQualityStandards": (()=>meetsQualityStandards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignQualitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    overall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Overall design quality score (1-10)'),
        grade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'A+',
            'A',
            'B+',
            'B',
            'C+',
            'C',
            'D',
            'F'
        ]).describe('Letter grade for design quality'),
        summary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Brief summary of design strengths and weaknesses')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Composition and layout quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on composition'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested composition improvements')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Typography quality and readability (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on typography'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested typography improvements')
    }),
    colorDesign: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Color usage and harmony (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on color choices'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested color improvements')
    }),
    brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on brand alignment'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested brand alignment improvements')
    }),
    platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform-specific optimization (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on platform optimization'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested platform optimization improvements')
    }),
    technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Technical execution quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on technical aspects'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested technical improvements')
    }),
    recommendedActions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        priority: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('Priority level of the action'),
        action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific action to take'),
        expectedImpact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Expected impact of the action')
    })).describe('Prioritized list of recommended improvements')
});
// Design quality assessment prompt
const designQualityPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'assessDesignQuality',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            brandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designGoals: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignQualitySchema
    },
    prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.

Evaluate the provided design image with the highest professional standards.

**Context:**
- Business Type: {{businessType}}
- Platform: {{platform}}
- Visual Style Goal: {{visualStyle}}
- Brand Colors: {{brandColors}}
- Design Goals: {{designGoals}}

**Assessment Criteria:**

1. **Composition & Layout** (25%):
   - Visual hierarchy and flow
   - Balance and proportion
   - Use of negative space
   - Rule of thirds application
   - Focal point effectiveness

2. **Typography** (20%):
   - Readability and legibility
   - Hierarchy and contrast
   - Font choice appropriateness
   - Text positioning and spacing
   - Accessibility compliance

3. **Color Design** (20%):
   - Color harmony and theory
   - Brand color integration
   - Contrast and accessibility
   - Psychological impact
   - Platform appropriateness

4. **Brand Alignment** (15%):
   - Brand consistency
   - Logo integration
   - Visual style adherence
   - Brand personality expression
   - Professional presentation

5. **Platform Optimization** (10%):
   - Platform-specific best practices
   - Mobile optimization
   - Engagement potential
   - Algorithm friendliness
   - Format appropriateness

6. **Technical Quality** (10%):
   - Image resolution and clarity
   - Professional finish
   - Technical execution
   - Scalability
   - Print/digital readiness

Provide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`
});
async function assessDesignQuality(designImageUrl, businessType, platform, visualStyle, brandColors, designGoals) {
    try {
        // For now, return a mock quality assessment to avoid API issues
        // This provides realistic quality scores while the system is being tested
        const baseScore = 7 + Math.random() * 2; // Random score between 7-9
        return {
            overall: {
                score: Math.round(baseScore * 10) / 10,
                grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',
                summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`
            },
            composition: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Strong visual hierarchy with balanced composition',
                improvements: baseScore < 8 ? [
                    'Improve focal point clarity',
                    'Enhance visual balance'
                ] : []
            },
            typography: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Clear, readable typography with appropriate hierarchy',
                improvements: baseScore < 8 ? [
                    'Increase text contrast',
                    'Improve font pairing'
                ] : []
            },
            colorDesign: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',
                improvements: baseScore < 8 ? [
                    'Enhance color harmony',
                    'Improve contrast ratios'
                ] : []
            },
            brandAlignment: {
                score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,
                feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',
                improvements: !brandColors ? [
                    'Integrate brand elements',
                    'Improve brand consistency'
                ] : []
            },
            platformOptimization: {
                score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,
                feedback: `Well optimized for ${platform} format and audience`,
                improvements: baseScore < 8 ? [
                    'Optimize for mobile viewing',
                    'Improve platform-specific elements'
                ] : []
            },
            technicalQuality: {
                score: Math.round((baseScore + 0.2) * 10) / 10,
                feedback: 'High resolution with professional finish',
                improvements: baseScore < 8 ? [
                    'Improve image resolution',
                    'Enhance visual polish'
                ] : []
            },
            recommendedActions: [
                {
                    priority: baseScore < 7.5 ? 'high' : 'medium',
                    action: 'Enhance visual impact through stronger focal points',
                    expectedImpact: 'Improved attention and engagement'
                },
                {
                    priority: 'medium',
                    action: 'Optimize typography for better readability',
                    expectedImpact: 'Clearer message communication'
                }
            ].filter((action)=>baseScore < 8.5 || action.priority === 'medium')
        };
    } catch (error) {
        throw new Error('Failed to assess design quality');
    }
}
function generateImprovementPrompt(quality) {
    const highPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'high').map((action)=>action.action);
    const mediumPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'medium').map((action)=>action.action);
    let improvementPrompt = `
**DESIGN IMPROVEMENT INSTRUCTIONS:**

Based on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):

**CRITICAL IMPROVEMENTS (High Priority):**
${highPriorityActions.map((action)=>`- ${action}`).join('\n')}

**RECOMMENDED ENHANCEMENTS (Medium Priority):**
${mediumPriorityActions.map((action)=>`- ${action}`).join('\n')}

**SPECIFIC AREA FEEDBACK:**
`;
    if (quality.composition.score < 7) {
        improvementPrompt += `
**Composition Issues to Address:**
${quality.composition.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.typography.score < 7) {
        improvementPrompt += `
**Typography Issues to Address:**
${quality.typography.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.colorDesign.score < 7) {
        improvementPrompt += `
**Color Design Issues to Address:**
${quality.colorDesign.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.brandAlignment.score < 7) {
        improvementPrompt += `
**Brand Alignment Issues to Address:**
${quality.brandAlignment.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    return improvementPrompt;
}
function meetsQualityStandards(quality, minimumScore = 7) {
    return quality.overall.score >= minimumScore && quality.composition.score >= minimumScore - 1 && quality.typography.score >= minimumScore - 1 && quality.brandAlignment.score >= minimumScore - 1;
}
function calculateWeightedScore(quality) {
    const weights = {
        composition: 0.25,
        typography: 0.20,
        colorDesign: 0.20,
        brandAlignment: 0.15,
        platformOptimization: 0.10,
        technicalQuality: 0.10
    };
    return quality.composition.score * weights.composition + quality.typography.score * weights.typography + quality.colorDesign.score * weights.colorDesign + quality.brandAlignment.score * weights.brandAlignment + quality.platformOptimization.score * weights.platformOptimization + quality.technicalQuality.score * weights.technicalQuality;
}
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
/* __next_internal_action_entry_do_not_use__ [{"409d8d4d6ee48a33e913d651c38aa0005eeae0dac6":"generateCreativeAsset"},"",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview A Genkit flow for generating a creative asset (image or video)
 * based on a user's prompt, an optional reference image, and brand profile settings.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Define the input schema for the creative asset generation flow.
const CreativeAssetInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The main text prompt describing the desired asset.'),
    outputType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
        'image',
        'video'
    ]).describe('The type of asset to generate.'),
    referenceAssetUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('An optional reference image or video as a data URI.'),
    useBrandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().describe('Whether to apply the brand profile.'),
    brandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].custom().nullable().describe('The brand profile object.'),
    maskDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),
    aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
        '16:9',
        '9:16'
    ]).optional().describe('The aspect ratio for video generation.'),
    preferredModel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Preferred model for generation (e.g., gemini-2.5-flash-image-preview).')
});
// Define the output schema for the creative asset generation flow.
const CreativeAssetOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated image, if applicable.'),
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated video, if applicable.'),
    aiExplanation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A brief explanation from the AI about what it created.')
});
async function generateCreativeAsset(input) {
    return generateCreativeAssetFlow(input);
}
/**
 * Helper function to download video and convert to data URI
 */ async function videoToDataURI(videoPart) {
    if (!videoPart.media || !videoPart.media.url) {
        throw new Error('Media URL not found in video part.');
    }
    const fetch = (await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    const videoDownloadResponse = await fetch(`${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`);
    if (!videoDownloadResponse.ok) {
        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);
    }
    const videoBuffer = await videoDownloadResponse.arrayBuffer();
    const base64Video = Buffer.from(videoBuffer).toString('base64');
    const contentType = videoPart.media.contentType || 'video/mp4';
    return `data:${contentType};base64,${base64Video}`;
}
/**
 * Extracts text in quotes and the remaining prompt.
 */ const extractQuotedText = (prompt)=>{
    const quoteRegex = /"([^"]*)"/;
    const match = prompt.match(quoteRegex);
    if (match) {
        return {
            imageText: match[1],
            remainingPrompt: prompt.replace(quoteRegex, '').trim()
        };
    }
    return {
        imageText: null,
        remainingPrompt: prompt
    };
};
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
/**
 * The core Genkit flow for generating a creative asset.
 */ const generateCreativeAssetFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generateCreativeAssetFlow',
    inputSchema: CreativeAssetInputSchema,
    outputSchema: CreativeAssetOutputSchema
}, async (input)=>{
    const promptParts = [];
    let textPrompt = '';
    const { imageText, remainingPrompt } = extractQuotedText(input.prompt);
    if (input.maskDataUrl && input.referenceAssetUrl) {
        // This is an inpainting request.
        textPrompt = `You are an expert image editor performing a precise inpainting task.
You will be given an original image, a mask, and a text prompt.
Your task is to modify the original image *only* in the areas designated by the black region of the mask.
The rest of the image must remain absolutely unchanged.
If the prompt is a "remove" or "delete" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.
The user's instruction for the masked area is: "${remainingPrompt}".
Recreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;
        promptParts.push({
            text: textPrompt
        });
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
        promptParts.push({
            media: {
                url: input.maskDataUrl,
                contentType: getMimeTypeFromDataURI(input.maskDataUrl)
            }
        });
    } else if (input.referenceAssetUrl) {
        // This is a generation prompt with a reference asset (image or video).
        let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.
Your task is to generate a new asset that is inspired by the reference asset and follows the new instructions.

Your primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.
Analyze the user's prompt for common editing terminology and apply it creatively. For example:
- If asked to "change the background," intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.
- If asked to "make the logo bigger" or "change the text color," perform those specific edits while maintaining the overall composition.
- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.

The user's instruction is: "${remainingPrompt}"`;
        if (imageText) {
            referencePrompt += `\n\n**Explicit Text Overlay:** The user has provided specific text in quotes: "${imageText}". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`;
        }
        if (input.outputType === 'video') {
            referencePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (imageText) {
                referencePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
        }
        if (input.useBrandProfile && input.brandProfile) {
            const bp = input.brandProfile;
            let brandGuidelines = '\n\n**Brand Guidelines:**';
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
                brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`;
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`;
            }
            referencePrompt += brandGuidelines;
        }
        textPrompt = referencePrompt;
        if (textPrompt) {
            promptParts.push({
                text: textPrompt
            });
        }
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
    } else if (input.useBrandProfile && input.brandProfile) {
        // This is a new, on-brand asset generation with advanced design principles.
        const bp = input.brandProfile;
        // Get business-specific design DNA
        const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][bp.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
        let onBrandPrompt = `Create a stunning, professional social media ${input.outputType} for ${bp.businessName || 'this business'}.

BUSINESS: ${bp.businessName || 'Professional Business'} (${bp.businessType})
CONTENT: "${remainingPrompt}"
STYLE: ${bp.visualStyle}, modern, clean, professional

FORMAT: ${input.aspectRatio ? `${input.aspectRatio} aspect ratio` : 'Square 1:1 format'}

BRAND COLORS (use prominently):
${bp.primaryColor ? `- Primary: ${bp.primaryColor}` : ''}
${bp.accentColor ? `- Accent: ${bp.accentColor}` : ''}
${bp.backgroundColor ? `- Background: ${bp.backgroundColor}` : ''}

REQUIREMENTS:
- High-quality, professional design
- ${bp.visualStyle} aesthetic
- Clean, modern layout
- Perfect for ${bp.businessType} business
- Brand colors prominently featured
- Professional social media appearance`;
        // Intelligent design examples processing
        let designDNA = '';
        let selectedExamples = [];
        if (bp.designExamples && bp.designExamples.length > 0) {
            try {
                // Analyze design examples for intelligent processing
                const analyses = [];
                for (const example of bp.designExamples.slice(0, 3)){
                    try {
                        const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, bp.businessType, 'creative-studio', `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`);
                        analyses.push(analysis);
                    } catch (error) {}
                }
                if (analyses.length > 0) {
                    // Extract design DNA from analyzed examples
                    designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                    // Select optimal examples based on analysis
                    selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(bp.designExamples, analyses, remainingPrompt, 'creative-studio', 2);
                } else {
                    selectedExamples = bp.designExamples.slice(0, 2);
                }
            } catch (error) {
                selectedExamples = bp.designExamples.slice(0, 2);
            }
            onBrandPrompt += `\n**STYLE REFERENCE:**
Use the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.

${designDNA}`;
        }
        if (input.outputType === 'image') {
            onBrandPrompt += `\n- **Text Overlay Requirements:** ${imageText ? `
                  * Display this EXACT text: "${imageText}"
                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters
                  * Make text LARGE and BOLD for mobile readability
                  * Apply high contrast (minimum 4.5:1 ratio) between text and background
                  * Add text shadows, outlines, or semi-transparent backgrounds for readability
                  * Position text using rule of thirds for optimal composition
                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;
            onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;
            onBrandPrompt += `\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            }
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            onBrandPrompt += `\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                onBrandPrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                onBrandPrompt += `\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Brand Identity:** Create a design that represents the brand identity and style.`;
            }
            // Add selected design examples as reference
            selectedExamples.forEach((designExample)=>{
                promptParts.push({
                    media: {
                        url: designExample,
                        contentType: getMimeTypeFromDataURI(designExample)
                    }
                });
            });
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    } else {
        // This is a new, un-branded, creative prompt.
        let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: "${remainingPrompt}".

⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:
- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)
- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes
- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small
- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness
- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions
- SHARP DETAILS: Crystal-clear textures, no blur or artifacts
- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows
- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance
- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;
        if (input.outputType === 'image' && imageText) {
            creativePrompt += `

🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨

⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:
- NEVER add "Flex Your Finances" or any financial terms
- NEVER add "Payroll Banking Simplified" or banking phrases
- NEVER add "Banking Made Easy" or similar taglines
- NEVER add company descriptions or service explanations
- NEVER add marketing copy or promotional text
- NEVER add placeholder text or sample content
- NEVER create fake headlines or taglines
- NEVER add descriptive text about the business
- NEVER add ANY text except what is specified below

🎯 ONLY THIS TEXT IS ALLOWED: "${imageText}"
🎯 REPEAT: ONLY THIS TEXT: "${imageText}"
🎯 NO OTHER TEXT PERMITTED: "${imageText}"

🌍 ENGLISH ONLY REQUIREMENT:
- ALL text must be in clear, readable English
- NO foreign languages (Arabic, Chinese, Hindi, etc.)
- NO special characters, symbols, or corrupted text
- NO accents or diacritical marks

Overlay ONLY the following text onto the asset: "${imageText}".
DO NOT ADD ANY OTHER TEXT.
Ensure the text is readable and well-composed.`;
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            creativePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                creativePrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                creativePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    }
    const aiExplanationPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
        name: 'creativeAssetExplanationPrompt',
        prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: "I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo."`
    });
    const explanationResult = await aiExplanationPrompt();
    try {
        if (input.outputType === 'image') {
            // Generate image with quality validation
            let finalImageUrl = null;
            let attempts = 0;
            const maxAttempts = 2;
            while(attempts < maxAttempts && !finalImageUrl){
                attempts++;
                // Determine which model to use based on preferred model parameter
                let modelToUse = 'googleai/gemini-2.0-flash-preview-image-generation'; // Default
                if (input.preferredModel) {
                    // Map Gemini model names to Genkit model identifiers
                    const modelMapping = {
                        'gemini-2.5-flash-image-preview': 'googleai/gemini-2.5-flash-image-preview',
                        'gemini-2.0-flash-preview-image-generation': 'googleai/gemini-2.0-flash-preview-image-generation',
                        'gemini-2.5-flash': 'googleai/gemini-2.5-flash'
                    };
                    modelToUse = modelMapping[input.preferredModel] || modelToUse;
                }
                const { media } = await generateWithRetry({
                    model: modelToUse,
                    prompt: promptParts,
                    config: {
                        responseModalities: [
                            'TEXT',
                            'IMAGE'
                        ]
                    }
                });
                let imageUrl = media?.url ?? null;
                if (!imageUrl) {
                    if (attempts === maxAttempts) {
                        throw new Error('Failed to generate image');
                    }
                    continue;
                }
                // Apply aspect ratio correction if needed
                if (input.aspectRatio && input.aspectRatio !== '1:1') {
                    try {
                        const { cropImageFromUrl } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                        // Map aspect ratio to platform for cropping
                        const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' : input.aspectRatio === '9:16' ? 'story' : 'instagram';
                        imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);
                    } catch (cropError) {
                    // Continue with original image if cropping fails
                    }
                }
                // Quality validation for brand profile designs
                if (input.useBrandProfile && input.brandProfile && attempts === 1) {
                    try {
                        const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.brandProfile.businessType, 'creative-studio', input.brandProfile.visualStyle, undefined, `Creative asset: ${remainingPrompt}`);
                        // If quality is acceptable, use this design
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 6)) {
                            finalImageUrl = imageUrl;
                            break;
                        }
                        // If quality is poor and we have attempts left, try to improve
                        if (attempts < maxAttempts) {
                            // Add improvement instructions to prompt
                            const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                            const improvedPrompt = `${promptParts[0].text}\n\n${improvementInstructions}`;
                            promptParts[0] = {
                                text: improvedPrompt
                            };
                            continue;
                        } else {
                            finalImageUrl = imageUrl;
                            break;
                        }
                    } catch (qualityError) {
                        finalImageUrl = imageUrl;
                        break;
                    }
                } else {
                    finalImageUrl = imageUrl;
                    break;
                }
            }
            return {
                imageUrl: finalImageUrl,
                videoUrl: null,
                aiExplanation: explanationResult.output ?? "Here is the generated image based on your prompt."
            };
        } else {
            const isVertical = input.aspectRatio === '9:16';
            const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';
            const config = {};
            if (isVertical) {
                config.aspectRatio = '9:16';
                config.durationSeconds = 8;
            }
            const result = await generateWithRetry({
                model,
                prompt: promptParts,
                config
            });
            let operation = result.operation;
            if (!operation) {
                throw new Error('The video generation process did not start correctly. Please try again.');
            }
            // Poll for completion
            while(!operation.done){
                await new Promise((resolve)=>setTimeout(resolve, 5000)); // wait 5s
                operation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].checkOperation(operation);
            }
            if (operation.error) {
                throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);
            }
            const videoPart = operation.output?.message?.content.find((p)=>!!p.media);
            if (!videoPart || !videoPart.media) {
                throw new Error('Video generation completed, but the final video file could not be found.');
            }
            const videoDataUrl = await videoToDataURI(videoPart);
            return {
                imageUrl: null,
                videoUrl: videoDataUrl,
                aiExplanation: explanationResult.output ?? "Here is the generated video based on your prompt."
            };
        }
    } catch (e) {
        // Ensure a user-friendly error is thrown
        const message = e.message || "An unknown error occurred during asset generation.";
        throw new Error(message);
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateCreativeAsset
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateCreativeAsset, "409d8d4d6ee48a33e913d651c38aa0005eeae0dac6", null);
}}),
"[project]/src/lib/services/artifacts-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/services/artifacts-service.ts
/**
 * Service for managing artifacts - upload, storage, retrieval, and metadata management
 */ __turbopack_context__.s({
    "artifactsService": (()=>artifactsService),
    "default": (()=>__TURBOPACK__default__export__)
});
// Default upload configuration
const DEFAULT_UPLOAD_CONFIG = {
    maxFileSize: 20 * 1024 * 1024,
    allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/svg+xml',
        'application/pdf',
        'text/plain'
    ],
    generateThumbnails: true,
    extractMetadata: true,
    performImageAnalysis: true,
    storage: {
        provider: 'local',
        basePath: '/uploads/artifacts',
        publicUrl: '/api/artifacts'
    }
};
class ArtifactsService {
    artifacts = new Map();
    folders = new Map();
    config = DEFAULT_UPLOAD_CONFIG;
    // Temporary file storage for previews (not persisted to avoid quota issues)
    fileCache = new Map();
    constructor(config){
        if (config) {
            this.config = {
                ...DEFAULT_UPLOAD_CONFIG,
                ...config
            };
        }
        this.loadArtifactsFromStorage();
        this.initializeDefaultFolders();
    }
    /**
   * Upload and process new artifacts with enhanced configuration
   */ async uploadArtifacts(files, category, options) {
        const uploadedArtifacts = [];
        for (const file of files){
            try {
                // Validate file
                this.validateFile(file);
                // Generate unique ID
                const id = this.generateId();
                // Process file and extract metadata
                const metadata = await this.extractMetadata(file);
                // Generate file path
                const filePath = await this.saveFile(file, id);
                // Generate thumbnail path (don't store actual data to avoid quota issues)
                const thumbnailPath = metadata.mimeType.startsWith('image/') ? `/uploads/artifacts/thumbnails/${id}_thumb.jpg` : undefined;
                // Auto-generate directives based on file analysis
                const directives = this.config.performImageAnalysis ? await this.generateDirectives(file, metadata) : [];
                // Create artifact with enhanced configuration
                const artifact = {
                    id,
                    name: options?.customName?.trim() || file.name,
                    type: this.determineArtifactType(file),
                    category: category || this.determineCategoryFromFile(file),
                    usageType: options?.usageType || 'reference',
                    uploadType: options?.uploadType || this.determineUploadType(file),
                    folderId: options?.folderId || this.getDefaultFolderId(file),
                    isActive: options?.isActive || false,
                    instructions: options?.instructions,
                    textOverlay: options?.textOverlay,
                    filePath,
                    thumbnailPath,
                    metadata,
                    directives,
                    tags: this.generateTags(file, metadata),
                    usage: {
                        usageCount: 0,
                        usedInContexts: []
                    },
                    timestamps: {
                        created: new Date(),
                        modified: new Date(),
                        uploaded: new Date()
                    }
                };
                // Add artifact to folder if specified
                if (artifact.folderId) {
                    const folder = this.folders.get(artifact.folderId);
                    if (folder) {
                        folder.artifactIds.push(id);
                        folder.metadata.modified = new Date();
                        this.folders.set(artifact.folderId, folder);
                    }
                }
                // Store artifact
                this.artifacts.set(id, artifact);
                uploadedArtifacts.push(artifact);
                // Cache the file temporarily for preview generation (not persisted)
                if (file.type.startsWith('image/')) {
                    this.fileCache.set(id, file);
                }
            } catch (error) {
                throw new Error(`Failed to upload ${file.name}: ${error.message}`);
            }
        }
        // Save to persistent storage
        await this.saveArtifactsToStorage();
        return uploadedArtifacts;
    }
    /**
   * Search artifacts with filters
   */ searchArtifacts(filters) {
        const startTime = Date.now();
        let results = Array.from(this.artifacts.values());
        // Apply filters
        if (filters.types?.length) {
            results = results.filter((a)=>filters.types.includes(a.type));
        }
        if (filters.categories?.length) {
            results = results.filter((a)=>filters.categories.includes(a.category));
        }
        if (filters.tags?.length) {
            results = results.filter((a)=>filters.tags.some((tag)=>a.tags.includes(tag)));
        }
        if (filters.searchText) {
            const searchLower = filters.searchText.toLowerCase();
            results = results.filter((a)=>a.name.toLowerCase().includes(searchLower) || a.description?.toLowerCase().includes(searchLower) || a.tags.some((tag)=>tag.toLowerCase().includes(searchLower)));
        }
        if (filters.usageContext) {
            results = results.filter((a)=>a.usage.usedInContexts.includes(filters.usageContext));
        }
        if (filters.dateRange) {
            results = results.filter((a)=>a.timestamps.created >= filters.dateRange.start && a.timestamps.created <= filters.dateRange.end);
        }
        if (filters.fileSizeRange) {
            results = results.filter((a)=>a.metadata.fileSize >= filters.fileSizeRange.min && a.metadata.fileSize <= filters.fileSizeRange.max);
        }
        if (filters.dimensionsRange && filters.dimensionsRange.minWidth) {
            results = results.filter((a)=>a.metadata.dimensions && a.metadata.dimensions.width >= filters.dimensionsRange.minWidth && (!filters.dimensionsRange.maxWidth || a.metadata.dimensions.width <= filters.dimensionsRange.maxWidth) && (!filters.dimensionsRange.minHeight || a.metadata.dimensions.height >= filters.dimensionsRange.minHeight) && (!filters.dimensionsRange.maxHeight || a.metadata.dimensions.height <= filters.dimensionsRange.maxHeight));
        }
        const executionTime = Date.now() - startTime;
        return {
            artifacts: results,
            totalCount: results.length,
            searchMetadata: {
                query: filters,
                executionTime,
                suggestions: this.generateSearchSuggestions(filters, results)
            }
        };
    }
    /**
   * Get artifact by ID
   */ getArtifact(id) {
        return this.artifacts.get(id);
    }
    /**
   * Update artifact
   */ async updateArtifact(id, updates) {
        const artifact = this.artifacts.get(id);
        if (!artifact) {
            throw new Error(`Artifact ${id} not found`);
        }
        const updatedArtifact = {
            ...artifact,
            ...updates,
            timestamps: {
                ...artifact.timestamps,
                modified: new Date()
            }
        };
        this.artifacts.set(id, updatedArtifact);
        await this.saveArtifactsToStorage();
        return updatedArtifact;
    }
    /**
   * Delete artifact
   */ async deleteArtifact(id) {
        const artifact = this.artifacts.get(id);
        if (!artifact) {
            throw new Error(`Artifact ${id} not found`);
        }
        // Delete files
        await this.deleteFile(artifact.filePath);
        if (artifact.thumbnailPath) {
            await this.deleteFile(artifact.thumbnailPath);
        }
        // Remove from memory
        this.artifacts.delete(id);
        // Save to storage
        await this.saveArtifactsToStorage();
    }
    /**
   * Track artifact usage
   */ async trackUsage(id, context) {
        const artifact = this.artifacts.get(id);
        if (!artifact) return;
        artifact.usage.usageCount++;
        artifact.usage.lastUsed = new Date();
        if (!artifact.usage.usedInContexts.includes(context)) {
            artifact.usage.usedInContexts.push(context);
        }
        await this.saveArtifactsToStorage();
    }
    /**
   * Get all artifacts
   */ getAllArtifacts() {
        return Array.from(this.artifacts.values());
    }
    /**
   * Get artifacts by category
   */ getArtifactsByCategory(category) {
        return Array.from(this.artifacts.values()).filter((a)=>a.category === category);
    }
    /**
   * Get recently used artifacts
   */ getRecentlyUsed(limit = 10) {
        return Array.from(this.artifacts.values()).filter((a)=>a.usage.lastUsed).sort((a, b)=>b.usage.lastUsed.getTime() - a.usage.lastUsed.getTime()).slice(0, limit);
    }
    /**
   * Create a text-only artifact
   */ async createTextArtifact(options) {
        const id = this.generateId();
        // Parse structured text content if it's JSON
        let textOverlay;
        let instructions;
        try {
            const parsedContent = JSON.parse(options.content);
            if (parsedContent.headline || parsedContent.message) {
                textOverlay = {
                    headline: parsedContent.headline,
                    message: parsedContent.message,
                    cta: parsedContent.cta,
                    contact: parsedContent.contact,
                    discount: parsedContent.discount,
                    instructions: parsedContent.instructions
                };
                // Use provided instructions or auto-generate from content
                instructions = parsedContent.instructions?.trim() || this.generateInstructionsFromTextOverlay(parsedContent);
            }
        } catch  {
        // Not JSON, treat as plain text
        }
        const artifact = {
            id,
            name: options.name,
            type: 'text',
            category: options.category || 'uncategorized',
            usageType: options.usageType,
            uploadType: 'text',
            folderId: options.folderId || '',
            isActive: options.isActive || false,
            instructions,
            textOverlay,
            filePath: '',
            metadata: {
                fileSize: new Blob([
                    options.content
                ]).size,
                mimeType: 'text/plain',
                extractedText: options.content
            },
            directives: [],
            tags: this.generateTagsFromText(options.content),
            usage: {
                usageCount: 0,
                usedInContexts: []
            },
            timestamps: {
                created: new Date(),
                modified: new Date(),
                uploaded: new Date()
            }
        };
        this.artifacts.set(id, artifact);
        await this.saveArtifactsToStorage();
        return artifact;
    }
    // Private helper methods
    validateFile(file) {
        if (file.size > this.config.maxFileSize) {
            throw new Error(`File size exceeds maximum allowed size of ${this.config.maxFileSize} bytes`);
        }
        if (!this.config.allowedTypes.includes(file.type)) {
            throw new Error(`File type ${file.type} is not allowed`);
        }
    }
    generateId() {
        return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async extractMetadata(file) {
        const metadata = {
            fileSize: file.size,
            mimeType: file.type
        };
        // Extract image dimensions and colors for images
        if (file.type.startsWith('image/')) {
            const dimensions = await this.getImageDimensions(file);
            metadata.dimensions = dimensions;
            if (this.config.performImageAnalysis) {
                metadata.colorPalette = await this.extractColorPalette(file);
                metadata.imageAnalysis = await this.analyzeImage(file);
            }
        }
        // Extract text for text files or OCR for images
        if (file.type === 'text/plain') {
            metadata.extractedText = await file.text();
        } else if (file.type.startsWith('image/') && this.config.performImageAnalysis) {
            metadata.extractedText = await this.performOCR(file);
        }
        return metadata;
    }
    async getImageDimensions(file) {
        return new Promise((resolve, reject)=>{
            const img = new Image();
            img.onload = ()=>{
                resolve({
                    width: img.width,
                    height: img.height
                });
            };
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    }
    async extractColorPalette(file) {
        // Simplified color extraction - in production, use a proper library
        return [
            '#FF6B6B',
            '#4ECDC4',
            '#45B7D1',
            '#96CEB4',
            '#FFEAA7'
        ];
    }
    async analyzeImage(file) {
        // Simplified image analysis - in production, use AI vision APIs
        return {
            hasText: Math.random() > 0.5,
            hasPeople: Math.random() > 0.7,
            hasProducts: Math.random() > 0.6,
            style: 'modern',
            mood: 'professional'
        };
    }
    async performOCR(file) {
        // Placeholder for OCR functionality
        return '';
    }
    async saveFile(file, id) {
        // In production, implement actual file saving logic
        return `/uploads/artifacts/${id}_${file.name}`;
    }
    async generateThumbnail(file, id) {
        // Return a placeholder path - actual thumbnails will be generated on-demand
        return `/uploads/artifacts/thumbnails/${id}_thumb.jpg`;
    }
    /**
   * Generate a thumbnail data URL from a file (for display purposes)
   * This is called on-demand to avoid localStorage quota issues
   */ async generateThumbnailDataUrl(file) {
        try {
            return new Promise((resolve, reject)=>{
                const reader = new FileReader();
                reader.onload = (e)=>{
                    const result = e.target?.result;
                    resolve(result);
                };
                reader.onerror = ()=>reject(new Error('Failed to read file'));
                reader.readAsDataURL(file);
            });
        } catch (error) {
            throw error;
        }
    }
    /**
   * Get thumbnail data URL for an artifact (on-demand generation)
   */ async getArtifactThumbnail(artifactId) {
        const file = this.fileCache.get(artifactId);
        if (!file) {
            return null;
        }
        try {
            return await this.generateThumbnailDataUrl(file);
        } catch (error) {
            return null;
        }
    }
    /**
   * Generate instructions from text overlay content
   */ generateInstructionsFromTextOverlay(content) {
        const instructions = [];
        if (content.headline?.trim()) {
            instructions.push(`Use "${content.headline.trim()}" as the main headline with large, bold text`);
        }
        if (content.message?.trim()) {
            instructions.push(`Include the message "${content.message.trim()}" as supporting text`);
        }
        if (content.cta?.trim()) {
            instructions.push(`Add a prominent call-to-action button with "${content.cta.trim()}"`);
        }
        if (content.contact?.trim()) {
            instructions.push(`Display contact information "${content.contact.trim()}" clearly`);
        }
        if (content.discount?.trim()) {
            instructions.push(`Highlight the discount offer "${content.discount.trim()}" prominently`);
        }
        return instructions.length > 0 ? instructions.join(', ') : 'Use this text content in the design as appropriate';
    }
    /**
   * Fix existing artifacts that don't have instructions
   */ fixMissingInstructions() {
        let fixedCount = 0;
        for (const [id, artifact] of this.artifacts.entries()){
            if (artifact.type === 'text' && artifact.textOverlay && (!artifact.instructions || artifact.instructions.trim() === '')) {
                const generatedInstructions = this.generateInstructionsFromTextOverlay(artifact.textOverlay);
                artifact.instructions = generatedInstructions;
                this.artifacts.set(id, artifact);
                fixedCount++;
            }
        }
        if (fixedCount > 0) {
            this.saveArtifactsToStorage();
        } else {}
    }
    /**
   * Set an artifact as active or inactive
   */ setArtifactActive(artifactId, isActive) {
        const artifact = this.artifacts.get(artifactId);
        if (artifact) {
            artifact.isActive = isActive;
            this.artifacts.set(artifactId, artifact);
            this.saveArtifactsToStorage();
        } else {}
    }
    async deleteFile(filePath) {
    // In production, implement file deletion
    }
    determineArtifactType(file) {
        if (file.type.startsWith('image/')) {
            if (file.name.toLowerCase().includes('logo')) return 'logo';
            if (file.name.toLowerCase().includes('screenshot')) return 'screenshot';
            return 'image';
        }
        return 'document';
    }
    determineCategoryFromFile(file) {
        const name = file.name.toLowerCase();
        if (name.includes('logo')) return 'logos';
        if (name.includes('screenshot')) return 'screenshots';
        if (name.includes('template')) return 'templates';
        if (name.includes('product')) return 'product-images';
        if (name.includes('brand')) return 'brand-assets';
        return 'uncategorized';
    }
    generateTags(file, metadata) {
        const tags = [];
        // Add type-based tags
        if (file.type.startsWith('image/')) tags.push('image');
        if (metadata.dimensions) {
            if (metadata.dimensions.width > metadata.dimensions.height) tags.push('landscape');
            else if (metadata.dimensions.height > metadata.dimensions.width) tags.push('portrait');
            else tags.push('square');
        }
        // Add size-based tags
        if (metadata.fileSize > 5 * 1024 * 1024) tags.push('large');
        else if (metadata.fileSize > 1024 * 1024) tags.push('medium');
        else tags.push('small');
        return tags;
    }
    generateTagsFromText(content) {
        const tags = [
            'text'
        ];
        // Try to parse structured content
        try {
            const parsed = JSON.parse(content);
            if (parsed.headline) tags.push('headline');
            if (parsed.message) tags.push('message');
            if (parsed.cta) tags.push('call-to-action');
            if (parsed.contact) tags.push('contact-info');
            if (parsed.discount) tags.push('discount');
        } catch  {
            // Plain text
            tags.push('plain-text');
        }
        return tags;
    }
    getDefaultFolderId(file) {
        // Return the ID of the default folder for the artifact type
        const defaultFolder = Array.from(this.folders.values()).find((f)=>f.isDefault);
        return defaultFolder?.id || '';
    }
    async generateDirectives(file, metadata) {
        const directives = [];
        // Generate style reference directive for images
        if (file.type.startsWith('image/')) {
            const styleDirective = {
                id: `style_${this.generateId()}`,
                type: 'style-reference',
                label: 'Use as style reference',
                instruction: `Use this image as a style reference for layout, color scheme, and overall aesthetic`,
                priority: 7,
                active: true,
                styleElements: {
                    layout: true,
                    colorScheme: true,
                    composition: true,
                    mood: true
                },
                adaptationLevel: 'moderate'
            };
            directives.push(styleDirective);
        }
        // Generate text overlay directive if text is detected
        if (metadata.extractedText && metadata.extractedText.trim()) {
            const textDirective = {
                id: `text_${this.generateId()}`,
                type: 'text-overlay',
                label: 'Include extracted text',
                instruction: `Include this exact text in the generated design: "${metadata.extractedText.trim()}"`,
                priority: 9,
                active: false,
                exactText: metadata.extractedText.trim(),
                positioning: {
                    horizontal: 'center',
                    vertical: 'center'
                },
                styling: {
                    fontSize: 'large',
                    fontWeight: 'bold'
                }
            };
            directives.push(textDirective);
        }
        return directives;
    }
    generateSearchSuggestions(filters, results) {
        // Generate helpful search suggestions based on current results
        const suggestions = [];
        if (results.length === 0) {
            suggestions.push('Try removing some filters');
            suggestions.push('Check different categories');
        } else if (results.length > 50) {
            suggestions.push('Add more specific filters');
            suggestions.push('Filter by category or type');
        }
        return suggestions;
    }
    loadArtifactsFromStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            const stored = undefined;
        } catch (error) {}
    }
    async saveArtifactsToStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            const artifacts = undefined;
            const folders = undefined;
        } catch (error) {}
    }
    // === NEW ENHANCED METHODS ===
    /**
   * Initialize default folders
   */ initializeDefaultFolders() {
        const defaultFolders = [
            {
                id: 'previous-posts',
                name: 'Previous Posts',
                description: 'Previously created social media posts for reference',
                color: '#3B82F6'
            },
            {
                id: 'products',
                name: 'Products',
                description: 'Product images and screenshots for exact use',
                color: '#10B981'
            },
            {
                id: 'discounts',
                name: 'Discounts',
                description: 'Discount and promotional materials',
                color: '#F59E0B'
            },
            {
                id: 'references',
                name: 'References',
                description: 'Style references and inspiration materials',
                color: '#8B5CF6'
            }
        ];
        defaultFolders.forEach((folderData)=>{
            if (!this.folders.has(folderData.id)) {
                const folder = {
                    ...folderData,
                    type: 'default',
                    artifactIds: [],
                    metadata: {
                        created: new Date(),
                        modified: new Date()
                    },
                    isDefault: true
                };
                this.folders.set(folder.id, folder);
            }
        });
        this.loadFoldersFromStorage();
    }
    /**
   * Load folders from storage
   */ loadFoldersFromStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            const stored = undefined;
        } catch (error) {}
    }
    /**
   * Determine upload type from file
   */ determineUploadType(file) {
        if (file.type.startsWith('image/')) return 'image';
        if (file.type === 'text/plain') return 'text';
        return 'document';
    }
    /**
   * Get default folder ID based on file type
   */ getDefaultFolderId(file) {
        if (file.name.toLowerCase().includes('product')) return 'products';
        if (file.name.toLowerCase().includes('discount') || file.name.toLowerCase().includes('sale')) return 'discounts';
        if (file.name.toLowerCase().includes('post')) return 'previous-posts';
        return 'references';
    }
    // === FOLDER MANAGEMENT METHODS ===
    /**
   * Create a new folder
   */ async createFolder(request) {
        const id = this.generateId();
        const folder = {
            id,
            name: request.name,
            description: request.description,
            type: request.type,
            color: request.color,
            artifactIds: [],
            metadata: {
                created: new Date(),
                modified: new Date()
            },
            isDefault: false
        };
        this.folders.set(id, folder);
        await this.saveArtifactsToStorage();
        return folder;
    }
    /**
   * Update folder
   */ async updateFolder(folderId, request) {
        const folder = this.folders.get(folderId);
        if (!folder) return null;
        if (request.name) folder.name = request.name;
        if (request.description !== undefined) folder.description = request.description;
        if (request.color) folder.color = request.color;
        folder.metadata.modified = new Date();
        this.folders.set(folderId, folder);
        await this.saveArtifactsToStorage();
        return folder;
    }
    /**
   * Delete folder (only custom folders)
   */ async deleteFolder(folderId) {
        const folder = this.folders.get(folderId);
        if (!folder || folder.isDefault) return false;
        // Move artifacts to references folder
        const referencesFolder = this.folders.get('references');
        if (referencesFolder) {
            folder.artifactIds.forEach((artifactId)=>{
                const artifact = this.artifacts.get(artifactId);
                if (artifact) {
                    artifact.folderId = 'references';
                    this.artifacts.set(artifactId, artifact);
                }
                referencesFolder.artifactIds.push(artifactId);
            });
            this.folders.set('references', referencesFolder);
        }
        this.folders.delete(folderId);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Get all folders
   */ getFolders() {
        return Array.from(this.folders.values());
    }
    /**
   * Get folder by ID
   */ getFolder(folderId) {
        return this.folders.get(folderId) || null;
    }
    /**
   * Move artifact to folder
   */ async moveArtifactToFolder(artifactId, folderId) {
        const artifact = this.artifacts.get(artifactId);
        const folder = this.folders.get(folderId);
        if (!artifact || !folder) return false;
        // Remove from old folder
        if (artifact.folderId) {
            const oldFolder = this.folders.get(artifact.folderId);
            if (oldFolder) {
                oldFolder.artifactIds = oldFolder.artifactIds.filter((id)=>id !== artifactId);
                this.folders.set(artifact.folderId, oldFolder);
            }
        }
        // Add to new folder
        artifact.folderId = folderId;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        if (!folder.artifactIds.includes(artifactId)) {
            folder.artifactIds.push(artifactId);
            folder.metadata.modified = new Date();
            this.folders.set(folderId, folder);
        }
        await this.saveArtifactsToStorage();
        return true;
    }
    // === ACTIVATION MANAGEMENT METHODS ===
    /**
   * Activate artifact for content generation
   */ async activateArtifact(artifactId, context) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = true;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Deactivate artifact
   */ async deactivateArtifact(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = false;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Toggle artifact activation
   */ async toggleArtifactActivation(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = !artifact.isActive;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return artifact.isActive;
    }
    /**
   * Get all active artifacts
   */ getActiveArtifacts() {
        const allArtifacts = Array.from(this.artifacts.values());
        const activeArtifacts = allArtifacts.filter((artifact)=>artifact.isActive);
        return activeArtifacts;
    }
    /**
   * Get active artifacts by usage type
   */ getActiveArtifactsByUsageType(usageType) {
        return this.getActiveArtifacts().filter((artifact)=>artifact.usageType === usageType);
    }
    /**
   * Deactivate all artifacts
   */ async deactivateAllArtifacts() {
        const artifacts = Array.from(this.artifacts.values());
        for (const artifact of artifacts){
            if (artifact.isActive) {
                artifact.isActive = false;
                artifact.timestamps.modified = new Date();
                this.artifacts.set(artifact.id, artifact);
            }
        }
        await this.saveArtifactsToStorage();
    }
    // === TEXT OVERLAY MANAGEMENT ===
    /**
   * Update artifact text overlay
   */ async updateArtifactTextOverlay(artifactId, textOverlay) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.textOverlay = textOverlay;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Remove artifact text overlay
   */ async removeArtifactTextOverlay(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.textOverlay = undefined;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Update artifact usage type
   */ async updateArtifactUsageType(artifactId, usageType) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.usageType = usageType;
        artifact.timestamps.modified = new Date();
        // Clear text overlay if changing from exact-use to reference
        if (usageType === 'reference') {
            artifact.textOverlay = undefined;
        }
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    // === ENHANCED SEARCH METHODS ===
    /**
   * Enhanced search with new filters
   */ async searchArtifactsEnhanced(filters) {
        const startTime = Date.now();
        let artifacts = Array.from(this.artifacts.values());
        // Apply existing filters (from base search)
        if (filters.query) {
            const query = filters.query.toLowerCase();
            artifacts = artifacts.filter((artifact)=>artifact.name.toLowerCase().includes(query) || artifact.description?.toLowerCase().includes(query) || artifact.tags.some((tag)=>tag.toLowerCase().includes(query)));
        }
        if (filters.type && filters.type !== 'all') {
            artifacts = artifacts.filter((artifact)=>artifact.type === filters.type);
        }
        if (filters.category && filters.category !== 'all') {
            artifacts = artifacts.filter((artifact)=>artifact.category === filters.category);
        }
        // Apply new enhanced filters
        if (filters.usageType) {
            artifacts = artifacts.filter((artifact)=>artifact.usageType === filters.usageType);
        }
        if (filters.uploadType) {
            artifacts = artifacts.filter((artifact)=>artifact.uploadType === filters.uploadType);
        }
        if (filters.folderId) {
            artifacts = artifacts.filter((artifact)=>artifact.folderId === filters.folderId);
        }
        if (filters.isActive !== undefined) {
            artifacts = artifacts.filter((artifact)=>artifact.isActive === filters.isActive);
        }
        if (filters.hasTextOverlay !== undefined) {
            artifacts = artifacts.filter((artifact)=>filters.hasTextOverlay ? !!artifact.textOverlay : !artifact.textOverlay);
        }
        // Apply pagination
        const totalCount = artifacts.length;
        if (filters.limit) {
            const offset = filters.offset || 0;
            artifacts = artifacts.slice(offset, offset + filters.limit);
        }
        const executionTime = Date.now() - startTime;
        return {
            artifacts,
            totalCount,
            searchMetadata: {
                query: filters,
                executionTime,
                suggestions: [] // Could implement search suggestions here
            }
        };
    }
    /**
   * Get artifacts by folder
   */ getArtifactsByFolder(folderId) {
        return Array.from(this.artifacts.values()).filter((artifact)=>artifact.folderId === folderId);
    }
}
const artifactsService = new ArtifactsService();
const __TURBOPACK__default__export__ = ArtifactsService;
}}),
"[project]/src/ai/google-ai-direct.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Direct Google AI API Service for Gemini 2.5
 * Bypasses Genkit to access latest Gemini 2.5 models directly
 */ __turbopack_context__.s({
    "GEMINI_2_5_MODELS": (()=>GEMINI_2_5_MODELS),
    "generateImage": (()=>generateImage),
    "generateMultimodal": (()=>generateMultimodal),
    "generateText": (()=>generateText),
    "getAvailableModels": (()=>getAvailableModels),
    "testConnection": (()=>testConnection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
;
// Initialize Google AI with API key
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
if (!apiKey) {}
const genAI = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](apiKey);
const GEMINI_2_5_MODELS = {
    FLASH: 'gemini-2.5-flash',
    PRO: 'gemini-2.5-pro',
    FLASH_LITE: 'gemini-2.5-flash-lite',
    FLASH_IMAGE_PREVIEW: 'gemini-2.5-flash-image-preview'
};
async function generateText(prompt, options = {}) {
    try {
        const { model = GEMINI_2_5_MODELS.FLASH, temperature = 0.7, maxOutputTokens = 2048, topK = 40, topP = 0.95 } = options;
        const geminiModel = genAI.getGenerativeModel({
            model,
            generationConfig: {
                temperature,
                maxOutputTokens,
                topK,
                topP
            }
        });
        const result = await geminiModel.generateContent(prompt);
        const response = await result.response;
        const text = response.text();
        return {
            text,
            finishReason: response.candidates?.[0]?.finishReason,
            safetyRatings: response.candidates?.[0]?.safetyRatings
        };
    } catch (error) {
        throw new Error(`Gemini 2.5 text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateImage(prompt, options = {}) {
    try {
        const { model = GEMINI_2_5_MODELS.FLASH, temperature = 0.8, maxOutputTokens = 1024 } = options;
        const geminiModel = genAI.getGenerativeModel({
            model,
            generationConfig: {
                temperature,
                maxOutputTokens
            }
        });
        // For now, Gemini 2.5 doesn't have direct image generation
        // This is a placeholder for when it becomes available
        // We'll use text generation to create detailed design specifications
        const designPrompt = `Create a detailed visual design specification for: ${prompt}

Please provide:
1. Color palette (specific hex codes)
2. Layout composition details
3. Typography specifications
4. Visual elements and their positioning
5. Style and mood descriptors
6. Technical implementation details

Format as JSON for easy parsing.`;
        const result = await geminiModel.generateContent(designPrompt);
        const response = await result.response;
        const designSpecs = response.text();
        // Return design specifications as "image data" for now
        // This will be used to generate actual images via other services
        return {
            imageData: Buffer.from(designSpecs).toString('base64'),
            mimeType: 'application/json',
            finishReason: response.candidates?.[0]?.finishReason,
            safetyRatings: response.candidates?.[0]?.safetyRatings
        };
    } catch (error) {
        throw new Error(`Gemini 2.5 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateMultimodal(textPrompt, imageData, options = {}) {
    try {
        const { model = GEMINI_2_5_MODELS.FLASH, temperature = 0.7, maxOutputTokens = 2048 } = options;
        const geminiModel = genAI.getGenerativeModel({
            model,
            generationConfig: {
                temperature,
                maxOutputTokens
            }
        });
        let parts = [
            {
                text: textPrompt
            }
        ];
        // Add image if provided
        if (imageData) {
            parts.push({
                inlineData: {
                    mimeType: 'image/jpeg',
                    data: imageData
                }
            });
        }
        const result = await geminiModel.generateContent(parts);
        const response = await result.response;
        const text = response.text();
        return {
            text,
            finishReason: response.candidates?.[0]?.finishReason,
            safetyRatings: response.candidates?.[0]?.safetyRatings
        };
    } catch (error) {
        throw new Error(`Gemini 2.5 multimodal generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function testConnection() {
    try {
        const response = await generateText('Hello, this is a test message. Please respond with "Connection successful!"', {
            model: GEMINI_2_5_MODELS.FLASH,
            maxOutputTokens: 50
        });
        const isSuccessful = response.text.toLowerCase().includes('connection successful') || response.text.toLowerCase().includes('hello') || response.text.length > 0;
        if (isSuccessful) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        return false;
    }
}
function getAvailableModels() {
    return {
        models: GEMINI_2_5_MODELS,
        capabilities: {
            [GEMINI_2_5_MODELS.FLASH]: {
                description: 'Fast and efficient for most tasks',
                bestFor: [
                    'content generation',
                    'design specifications',
                    'quick responses'
                ],
                costEfficiency: 'high'
            },
            [GEMINI_2_5_MODELS.PRO]: {
                description: 'Most capable model for complex reasoning',
                bestFor: [
                    'complex analysis',
                    'detailed design planning',
                    'sophisticated content'
                ],
                costEfficiency: 'medium'
            },
            [GEMINI_2_5_MODELS.FLASH_LITE]: {
                description: 'Lightweight and cost-effective',
                bestFor: [
                    'simple tasks',
                    'quick responses',
                    'high-volume requests'
                ],
                costEfficiency: 'very high'
            }
        }
    };
}
}}),
"[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Gemini 2.5 Enhanced Design Generation Service
 * Uses direct Google AI API for superior design capabilities
 */ __turbopack_context__.s({
    "generateDesignSpecs": (()=>generateDesignSpecs),
    "generateEnhancedDesign": (()=>generateEnhancedDesign)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$google$2d$ai$2d$direct$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/google-ai-direct.ts [app-rsc] (ecmascript)");
;
/**
 * Clean website URL by removing https://, http://, and www.
 */ function cleanWebsiteUrl(url) {
    if (!url) return '';
    return url.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, ''); // Remove trailing slash
}
async function generateDesignSpecs(input) {
    const startTime = Date.now();
    try {
        const designPrompt = `You are an expert graphic designer and brand strategist with deep expertise in 2024-2025 design trends, visual design, color theory, typography, and brand identity. Create a comprehensive ultra-modern design specification for a ${input.platform} post.

BUSINESS CONTEXT:
- Business: ${input.brandProfile.businessName}
- Industry: ${input.businessType}
- Target Audience: ${input.brandProfile.targetAudience}
- Brand Voice: ${input.brandProfile.writingTone}
- Services: ${Array.isArray(input.brandProfile.services) ? input.brandProfile.services.join(', ') : input.brandProfile.services || 'Various services'}

BRAND COLORS:
- Primary Color: ${input.brandProfile.primaryColor}
- Accent Color: ${input.brandProfile.accentColor}
- Background Color: ${input.brandProfile.backgroundColor}

DESIGN REQUIREMENTS:
- Platform: ${input.platform} (1080x1080px)
- Visual Style: ${input.visualStyle} with cutting-edge 2024-2025 trends
- Text to Include: "${input.imageText}"
- Brand Consistency: ${input.brandConsistency?.strictConsistency ? 'Strict' : 'Flexible'}

MODERN DESIGN TRENDS TO IMPLEMENT:
- Glassmorphism: Semi-transparent backgrounds with blur effects
- Neumorphism: Subtle shadows and highlights for depth
- Bold typography: Oversized, modern fonts with creative spacing
- Gradient meshes: Complex, multi-directional gradients
- Organic shapes: Fluid, natural forms mixed with geometric elements
- Modern color palettes: Vibrant, saturated colors with high contrast
- Contemporary layouts: Asymmetrical, dynamic compositions
- Advanced shadows: Soft, realistic multi-layered shadows
- Modern iconography: Minimal, line-based icons
- Subtle textures: Noise, grain, or organic patterns

${input.artifactInstructions ? `SPECIAL INSTRUCTIONS: ${input.artifactInstructions}` : ''}

Please create a detailed design specification that includes:

1. **LAYOUT COMPOSITION:**
   - Overall layout structure and grid system
   - Visual hierarchy and focal points
   - Text placement and sizing
   - Logo/brand element positioning
   - White space and balance

2. **COLOR PALETTE - MAXIMUM 3 COLORS ONLY, NO LINES:**
   - Primary: ${input.brandProfile.primaryColor} (DOMINANT 60-70% usage)
   - Accent: ${input.brandProfile.accentColor} (HIGHLIGHTS 20-30% usage)
   - Background: ${input.brandProfile.backgroundColor} (BASE 10-20% usage)
   - Text: High contrast white or black only for readability
   - ABSOLUTE LIMITS: These 3 colors only - NO 4th color allowed, NO LINES
   - FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers

3. **TYPOGRAPHY:**
   - Font families and weights
   - Text sizes and line heights
   - Text alignment and spacing
   - Hierarchy (headlines, subheads, body)

4. **VISUAL ELEMENTS:**
   - Geometric shapes and patterns
   - Icons or illustrations needed
   - Background textures or effects
   - Border and frame elements
   - Shadow and depth effects

5. **BRAND INTEGRATION:**
   - Logo treatment and placement
   - Brand color application
   - Consistent visual language
   - Brand personality expression

6. **TECHNICAL SPECS:**
   - Exact dimensions and positioning
   - Color codes (hex, RGB)
   - Export settings and formats
   - Responsive considerations

Format your response as a detailed JSON object with all specifications clearly organized. Be specific with measurements, colors, and positioning.`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$google$2d$ai$2d$direct$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateText"])(designPrompt, {
            model: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$google$2d$ai$2d$direct$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GEMINI_2_5_MODELS"].PRO,
            temperature: 0.8,
            maxOutputTokens: 4096 // More tokens for detailed specs
        });
        // Parse the JSON response
        let designSpecs;
        try {
            // Extract JSON from the response
            const jsonMatch = response.text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                designSpecs = JSON.parse(jsonMatch[0]);
            } else {
                // Fallback: create structured specs from text
                designSpecs = parseDesignSpecsFromText(response.text, input);
            }
        } catch (parseError) {
            designSpecs = parseDesignSpecsFromText(response.text, input);
        }
        return designSpecs;
    } catch (error) {
        throw new Error(`Gemini 2.5 design specs generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateEnhancedDesign(input) {
    const startTime = Date.now();
    const enhancementsApplied = [
        'Gemini 2.5 Flash Image Preview Generation',
        'Professional Design Principles',
        'Brand Integration'
    ];
    try {
        // Use the superior Gemini 2.5 Flash Image Preview generation
        const { generateCreativeAsset } = await __turbopack_context__.r("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Build comprehensive AI prompt for image generation
        const imagePrompt = buildComprehensiveImagePrompt(input);
        enhancementsApplied.push('Comprehensive AI Prompting');
        // Generate image with Gemini 2.5 Flash Image Preview (superior text rendering)
        const creativeResult = await generateCreativeAsset({
            prompt: imagePrompt,
            outputType: 'image',
            referenceAssetUrl: null,
            useBrandProfile: true,
            brandProfile: input.brandProfile,
            maskDataUrl: null,
            preferredModel: 'gemini-2.5-flash-image-preview'
        });
        const imageUrl = creativeResult.imageUrl;
        if (!imageUrl) {
            throw new Error('No image URL returned from Gemini 2.5 Flash Image Preview');
        }
        enhancementsApplied.push('Gemini 2.5 Flash Image Preview HD Generation', 'Ultra-High Quality Settings', 'Perfect Text Rendering', 'Professional Design Generation', 'Brand Color Compliance', 'Platform Optimization');
        const result = {
            imageUrl,
            designSpecs: {
                prompt: imagePrompt
            },
            qualityScore: 9.5,
            enhancementsApplied,
            processingTime: Date.now() - startTime,
            model: 'gemini-2.0-flash-image'
        };
        return result;
    } catch (error) {
        throw new Error(`Real AI enhanced design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Parse design specifications from text response
 */ function parseDesignSpecsFromText(text, input) {
    // Extract key information from the text response
    const colorRegex = /#[0-9A-Fa-f]{6}/g;
    const colors = text.match(colorRegex) || [];
    return {
        layout: {
            style: input.visualStyle || 'modern-professional',
            dimensions: {
                width: 1080,
                height: 1080
            },
            textPlacement: 'center'
        },
        colors: {
            primary: input.brandProfile.primaryColor || colors[0] || '#1e40af',
            secondary: input.brandProfile.accentColor || colors[1] || '#3b82f6',
            background: input.brandProfile.backgroundColor || '#ffffff',
            text: '#333333'
        },
        typography: {
            headline: {
                size: 36,
                weight: 'bold',
                family: 'Arial, sans-serif'
            },
            subheadline: {
                size: 24,
                weight: 'normal',
                family: 'Arial, sans-serif'
            },
            body: {
                size: 16,
                weight: 'normal',
                family: 'Arial, sans-serif'
            }
        },
        elements: {
            logo: {
                position: 'top-left',
                size: 80
            },
            shapes: [
                'gradient-background',
                'accent-shapes'
            ],
            effects: [
                'subtle-shadow',
                'modern-gradient'
            ]
        },
        concept: text.substring(0, 200) + '...'
    };
}
/**
 * Create DYNAMIC SVG design that actually uses the AI specifications
 * This replaces the hardcoded template with spec-driven generation
 */ async function createDynamicSVGFromSpecs(specs, input) {
    const { colors = {}, layout = {}, typography = {}, elements = {} } = specs || {};
    const { width = 1080, height = 1080 } = layout?.dimensions || {};
    // Extract design style from specs
    const designStyle = layout?.style || input.visualStyle || 'modern';
    const layoutType = layout?.textPlacement || 'center';
    // Dynamic color palette based on specs and brand
    const primaryColor = colors.primary || input.brandProfile.primaryColor || '#6366f1';
    const secondaryColor = colors.secondary || input.brandProfile.accentColor || '#8b5cf6';
    const backgroundColor = colors.background || input.brandProfile.backgroundColor || '#ffffff';
    const textColor = colors.text || '#1f2937';
    // Parse text content dynamically
    const textLines = input.imageText.split('\n').filter((line)=>line.trim());
    const mainText = textLines[0] || input.brandProfile.businessName || 'Business';
    const subText = textLines[1] || '';
    const ctaText = textLines[2] || 'Learn More';
    // Generate layout based on business type and style
    return generateLayoutBasedOnSpecs(designStyle, {
        width,
        height,
        primaryColor,
        secondaryColor,
        backgroundColor,
        textColor,
        mainText,
        subText,
        ctaText,
        businessType: input.businessType,
        brandName: input.brandProfile.businessName,
        elements: elements.shapes || [],
        typography
    });
}
/**
 * Generate different layouts based on design specifications
 */ function generateLayoutBasedOnSpecs(designStyle, params) {
    const { width, height, primaryColor, secondaryColor, backgroundColor, textColor, mainText, subText, ctaText, businessType, brandName, elements, typography } = params;
    // Choose layout based on design style and business type
    if (designStyle.includes('minimal') || businessType.includes('tech')) {
        return generateMinimalLayout(params);
    } else if (designStyle.includes('bold') || businessType.includes('fitness') || businessType.includes('food')) {
        return generateBoldLayout(params);
    } else if (designStyle.includes('elegant') || businessType.includes('fashion') || businessType.includes('beauty')) {
        return generateElegantLayout(params);
    } else if (businessType.includes('medical') || businessType.includes('professional')) {
        return generateProfessionalLayout(params);
    } else {
        return generateModernLayout(params);
    }
}
/**
 * Generate minimal tech-focused layout
 */ function generateMinimalLayout(params) {
    const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="minimalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.1" />
          <stop offset="100%" style="stop-color:${secondaryColor};stop-opacity:0.05" />
        </linearGradient>
      </defs>

      <!-- Clean background -->
      <rect width="100%" height="100%" fill="#ffffff" />
      <rect width="100%" height="100%" fill="url(#minimalGrad)" />

      <!-- Minimal geometric accent -->
      <rect x="80" y="80" width="4" height="200" fill="${primaryColor}" />
      <rect x="80" y="800" width="200" height="4" fill="${primaryColor}" />

      <!-- Content area -->
      <g transform="translate(540, 400)">
        <!-- Brand initial -->
        <rect x="-25" y="-120" width="50" height="50" fill="${primaryColor}" />
        <text x="0" y="-85" text-anchor="middle" fill="white" font-family="system-ui" font-size="20" font-weight="600">
          ${brandName?.charAt(0) || 'B'}
        </text>

        <!-- Main text -->
        <text x="0" y="0" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="48" font-weight="300" letter-spacing="-0.02em">
          ${mainText}
        </text>

        ${subText ? `
        <text x="0" y="60" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="16" font-weight="400" opacity="0.7">
          ${subText}
        </text>
        ` : ''}

        <!-- Minimal CTA -->
        <text x="0" y="140" text-anchor="middle" fill="${primaryColor}" font-family="system-ui" font-size="14" font-weight="500" text-decoration="underline">
          ${ctaText}
        </text>
      </g>
    </svg>
  `;
}
/**
 * Generate bold, energetic layout for fitness/food businesses
 */ function generateBoldLayout(params) {
    const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="boldGrad" cx="50%" cy="50%" r="70%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.8" />
          <stop offset="100%" style="stop-color:${secondaryColor};stop-opacity:0.4" />
        </radialGradient>
        <filter id="boldShadow">
          <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="rgba(0,0,0,0.3)"/>
        </filter>
      </defs>

      <!-- Dynamic background -->
      <rect width="100%" height="100%" fill="url(#boldGrad)" />

      <!-- Bold geometric shapes -->
      <polygon points="0,0 300,0 200,200" fill="${primaryColor}" opacity="0.2" />
      <polygon points="1080,1080 780,1080 880,880" fill="${secondaryColor}" opacity="0.2" />

      <!-- Content area -->
      <g transform="translate(540, 540)">
        <!-- Bold brand circle -->
        <circle cx="0" cy="-150" r="50" fill="${primaryColor}" filter="url(#boldShadow)" />
        <text x="0" y="-135" text-anchor="middle" fill="white" font-family="system-ui" font-size="32" font-weight="900">
          ${brandName?.charAt(0) || 'B'}
        </text>

        <!-- Bold main text -->
        <text x="0" y="0" text-anchor="middle" fill="white" font-family="system-ui" font-size="56" font-weight="900" letter-spacing="-0.03em" filter="url(#boldShadow)">
          ${mainText.toUpperCase()}
        </text>

        ${subText ? `
        <text x="0" y="80" text-anchor="middle" fill="white" font-family="system-ui" font-size="20" font-weight="600" opacity="0.9">
          ${subText}
        </text>
        ` : ''}

        <!-- Bold CTA button -->
        <rect x="-100" y="120" width="200" height="60" rx="30" fill="white" filter="url(#boldShadow)" />
        <text x="0" y="160" text-anchor="middle" fill="${primaryColor}" font-family="system-ui" font-size="18" font-weight="700">
          ${ctaText.toUpperCase()}
        </text>
      </g>
    </svg>
  `;
}
/**
 * Generate elegant layout for fashion/beauty businesses
 */ function generateElegantLayout(params) {
    const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="elegantGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
          <stop offset="100%" style="stop-color:${primaryColor};stop-opacity:0.1" />
        </linearGradient>
      </defs>

      <!-- Elegant background -->
      <rect width="100%" height="100%" fill="url(#elegantGrad)" />

      <!-- Elegant decorative elements -->
      <circle cx="150" cy="150" r="80" fill="none" stroke="${primaryColor}" stroke-width="1" opacity="0.3" />
      <circle cx="930" cy="930" r="60" fill="none" stroke="${secondaryColor}" stroke-width="1" opacity="0.3" />

      <!-- Content area -->
      <g transform="translate(540, 450)">
        <!-- Elegant brand mark -->
        <rect x="-30" y="-120" width="60" height="60" fill="none" stroke="${primaryColor}" stroke-width="2" />
        <text x="0" y="-75" text-anchor="middle" fill="${primaryColor}" font-family="serif" font-size="24" font-weight="400" font-style="italic">
          ${brandName?.charAt(0) || 'B'}
        </text>

        <!-- Elegant main text -->
        <text x="0" y="0" text-anchor="middle" fill="${textColor}" font-family="serif" font-size="44" font-weight="300" letter-spacing="0.02em">
          ${mainText}
        </text>

        ${subText ? `
        <text x="0" y="60" text-anchor="middle" fill="${textColor}" font-family="serif" font-size="16" font-weight="300" opacity="0.8" font-style="italic">
          ${subText}
        </text>
        ` : ''}

        <!-- Elegant CTA -->
        <rect x="-80" y="120" width="160" height="40" fill="none" stroke="${primaryColor}" stroke-width="1" />
        <text x="0" y="145" text-anchor="middle" fill="${primaryColor}" font-family="serif" font-size="14" font-weight="400" letter-spacing="0.1em">
          ${ctaText.toUpperCase()}
        </text>
      </g>
    </svg>
  `;
}
/**
 * Generate professional layout for medical/corporate businesses
 */ function generateProfessionalLayout(params) {
    const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="profGrad" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
          <stop offset="100%" style="stop-color:${primaryColor};stop-opacity:0.05" />
        </linearGradient>
      </defs>

      <!-- Professional background -->
      <rect width="100%" height="100%" fill="url(#profGrad)" />

      <!-- Professional header bar -->
      <rect x="0" y="0" width="100%" height="120" fill="${primaryColor}" />

      <!-- Content area -->
      <g transform="translate(540, 500)">
        <!-- Professional logo area -->
        <rect x="-40" y="-200" width="80" height="80" fill="white" />
        <text x="0" y="-145" text-anchor="middle" fill="${primaryColor}" font-family="system-ui" font-size="28" font-weight="600">
          ${brandName?.charAt(0) || 'B'}
        </text>

        <!-- Professional main text -->
        <text x="0" y="0" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="40" font-weight="600" letter-spacing="-0.01em">
          ${mainText}
        </text>

        ${subText ? `
        <text x="0" y="50" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="18" font-weight="400" opacity="0.8">
          ${subText}
        </text>
        ` : ''}

        <!-- Professional CTA -->
        <rect x="-120" y="100" width="240" height="50" fill="${primaryColor}" />
        <text x="0" y="130" text-anchor="middle" fill="white" font-family="system-ui" font-size="16" font-weight="500">
          ${ctaText}
        </text>
      </g>
    </svg>
  `;
}
/**
 * Generate modern default layout
 */ function generateModernLayout(params) {
    const { width, height, primaryColor, secondaryColor, textColor, mainText, subText, ctaText, brandName } = params;
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="modernGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.1" />
          <stop offset="100%" style="stop-color:${secondaryColor};stop-opacity:0.05" />
        </linearGradient>
      </defs>

      <!-- Modern background -->
      <rect width="100%" height="100%" fill="#ffffff" />
      <rect width="100%" height="100%" fill="url(#modernGrad)" />

      <!-- Modern accent shapes -->
      <circle cx="200" cy="200" r="100" fill="${primaryColor}" opacity="0.1" />
      <rect x="700" y="700" width="200" height="200" rx="20" fill="${secondaryColor}" opacity="0.1" />

      <!-- Content area -->
      <g transform="translate(540, 450)">
        <!-- Modern brand mark -->
        <circle cx="0" cy="-100" r="40" fill="${primaryColor}" />
        <text x="0" y="-90" text-anchor="middle" fill="white" font-family="system-ui" font-size="24" font-weight="600">
          ${brandName?.charAt(0) || 'B'}
        </text>

        <!-- Modern main text -->
        <text x="0" y="0" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="46" font-weight="700" letter-spacing="-0.02em">
          ${mainText}
        </text>

        ${subText ? `
        <text x="0" y="60" text-anchor="middle" fill="${textColor}" font-family="system-ui" font-size="18" font-weight="400" opacity="0.8">
          ${subText}
        </text>
        ` : ''}

        <!-- Modern CTA -->
        <rect x="-100" y="120" width="200" height="50" rx="25" fill="${primaryColor}" />
        <text x="0" y="150" text-anchor="middle" fill="white" font-family="system-ui" font-size="16" font-weight="600">
          ${ctaText}
        </text>
      </g>
    </svg>
  `;
}
/**
 * Create SVG design from specifications (LEGACY - keeping for compatibility)
 */ async function createSVGFromSpecs(specs, input) {
    const { colors = {}, layout = {}, typography = {}, elements = {} } = specs || {};
    const { width = 1080, height = 1080 } = layout?.dimensions || {};
    // Modern color palette with enhanced gradients
    const primaryColor = colors.primary || input.brandProfile.primaryColor || '#6366f1';
    const secondaryColor = colors.secondary || input.brandProfile.accentColor || '#8b5cf6';
    const accentColor = '#f59e0b';
    const textColor = colors.text || '#1f2937';
    const glassColor = 'rgba(255,255,255,0.1)';
    // Parse image text components
    const textLines = input.imageText.split('\n').filter((line)=>line.trim());
    const mainText = textLines[0] || 'Modern Business';
    const subText = textLines[1] || '';
    const ctaText = textLines[2] || 'Get Started';
    return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <!-- Modern gradient definitions -->
        <linearGradient id="modernGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:1" />
          <stop offset="50%" style="stop-color:${secondaryColor};stop-opacity:0.9" />
          <stop offset="100%" style="stop-color:${accentColor};stop-opacity:0.8" />
        </linearGradient>

        <radialGradient id="glowGradient" cx="50%" cy="30%" r="70%">
          <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.3" />
          <stop offset="100%" style="stop-color:${secondaryColor};stop-opacity:0.1" />
        </radialGradient>

        <linearGradient id="glassmorphism" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:rgba(255,255,255,0.25);stop-opacity:1" />
          <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
        </linearGradient>

        <!-- Modern shadow filters -->
        <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="20" stdDeviation="25" flood-color="rgba(0,0,0,0.1)"/>
        </filter>

        <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        <filter id="glassmorphismBlur" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="10"/>
        </filter>
      </defs>

      <!-- Modern background with gradient mesh -->
      <rect width="100%" height="100%" fill="url(#modernGradient)" />

      <!-- Glow overlay -->
      <rect width="100%" height="100%" fill="url(#glowGradient)" />

      <!-- Modern geometric elements -->
      <circle cx="900" cy="200" r="150" fill="${primaryColor}" opacity="0.1" filter="url(#glowEffect)" />
      <circle cx="200" cy="800" r="100" fill="${secondaryColor}" opacity="0.15" />

      <!-- Organic shapes -->
      <path d="M0,0 Q300,100 600,50 T1080,80 L1080,0 Z" fill="${accentColor}" opacity="0.08" />
      <path d="M0,1080 Q400,950 800,1000 T1080,950 L1080,1080 Z" fill="${primaryColor}" opacity="0.12" />

      <!-- Main glassmorphism card -->
      <rect x="120" y="250" width="840" height="580" rx="32"
            fill="url(#glassmorphism)"
            stroke="rgba(255,255,255,0.2)"
            stroke-width="1"
            filter="url(#modernShadow)" />

      <!-- Content area with modern layout -->
      <g transform="translate(540, 400)">
        <!-- Brand mark -->
        <circle cx="0" cy="-80" r="35" fill="${primaryColor}" filter="url(#glowEffect)" />
        <text x="0" y="-70" text-anchor="middle" fill="white"
              font-family="system-ui, -apple-system, sans-serif"
              font-size="24" font-weight="700">
          ${input.brandProfile.businessName?.charAt(0) || 'B'}
        </text>

        <!-- Main headline with modern typography -->
        <text x="0" y="0" text-anchor="middle" fill="${textColor}"
              font-family="system-ui, -apple-system, sans-serif"
              font-size="42" font-weight="800" letter-spacing="-0.02em">
          ${mainText}
        </text>

        ${subText ? `
        <!-- Subheadline -->
        <text x="0" y="50" text-anchor="middle" fill="${textColor}"
              font-family="system-ui, -apple-system, sans-serif"
              font-size="18" font-weight="400" opacity="0.8">
          ${subText}
        </text>
        ` : ''}

        <!-- Modern CTA button -->
        <g transform="translate(0, 120)">
          <rect x="-120" y="-25" width="240" height="50" rx="25"
                fill="${primaryColor}" filter="url(#modernShadow)" />
          <rect x="-120" y="-25" width="240" height="50" rx="25"
                fill="url(#glowGradient)" opacity="0.3" />
          <text x="0" y="5" text-anchor="middle" fill="white"
                font-family="system-ui, -apple-system, sans-serif"
                font-size="16" font-weight="600">
            ${ctaText}
          </text>
        </g>
      </g>

      <!-- Modern decorative elements -->
      <circle cx="150" cy="150" r="3" fill="${accentColor}" opacity="0.6" />
      <circle cx="930" cy="930" r="4" fill="${primaryColor}" opacity="0.5" />
      <circle cx="200" cy="900" r="2" fill="${secondaryColor}" opacity="0.7" />

      <!-- Subtle grid pattern -->
      <defs>
        <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
          <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
    </svg>
  `;
}
/**
 * Build comprehensive AI image prompt for Imagen 4
 * This creates extremely detailed prompts that leverage Imagen 4's instruction-following capabilities
 * for high-quality, professional designs with perfect text readability
 */ function buildComprehensiveImagePrompt(input) {
    const { businessType, platform, visualStyle, imageText, brandProfile, includePeopleInDesigns = true, useLocalLanguage = false } = input;
    // People inclusion logic with creative variety
    const peopleInstructions = includePeopleInDesigns ? `- CREATIVE VARIETY: Include diverse, authentic people in VARIED settings:
  * Professional environments (offices, studios, workshops)
  * Lifestyle settings (homes, cafes, outdoor spaces, community areas)
  * Industry-specific contexts (retail spaces, service areas, creative studios)
  * Cultural celebrations and modern community gatherings
  * Contemporary urban settings (co-working spaces, tech hubs)
  * Traditional meets modern (cultural heritage with contemporary life)
- Show real people in natural, engaging situations that vary by design
- Ensure representation reflects the target demographic and cultural values
- Use photography styles ranging from candid to professional to artistic
- Vary the mood: energetic, calm, celebratory, focused, collaborative` : `- Focus on products, services, and brand elements without people
- Use lifestyle imagery, product showcases, and brand-focused visuals
- Create compelling designs through typography, graphics, and product photography
- Maintain professional aesthetic through clean, modern design elements`;
    // Simplified, focused prompt that works better for AI image generation
    const prompt = `Create a stunning, professional ${platform} social media design for ${brandProfile.businessName || businessType}.

BUSINESS: ${brandProfile.businessName || businessType} (${businessType})
TEXT TO INCLUDE: "${imageText}"
STYLE: ${visualStyle}, modern, clean, professional

MAXIMUM 3 COLORS ONLY, NO LINES - BRAND COLORS:
- Primary: ${brandProfile.primaryColor || '#2563eb'} (DOMINANT 60-70%)
- Accent: ${brandProfile.accentColor || '#7c3aed'} (HIGHLIGHTS 20-30%)
- Background: ${brandProfile.backgroundColor || '#ffffff'} (BASE 10-20%)
- ABSOLUTE LIMITS: These 3 colors only - NO 4th color allowed, NO LINES
- FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers

VISUAL APPROACH:
${peopleInstructions}

LANGUAGE INSTRUCTIONS:
${useLocalLanguage ? `- You may use local language text when 100% certain of accuracy
- Mix local language with English naturally (1-2 local words maximum)
- Only use commonly known local words that add cultural connection` : `- USE ONLY ENGLISH for all text in the design
- Do not use any local language words or phrases
- Keep all text elements in clear, professional English`}

CREATIVE DESIGN VARIETY:
- DESIGN STYLES (rotate for variety):
  * Ultra-modern minimalist with bold typography
  * Dynamic geometric patterns with vibrant colors
  * Sophisticated gradient overlays with premium feel
  * Clean photography-focused with subtle overlays
  * Artistic illustration style with contemporary elements
  * Bold graphic design with strong visual hierarchy
  * Elegant luxury aesthetic with refined typography

- LAYOUT VARIATIONS:
  * Split-screen compositions (text/visual balance)
  * Centered hero with surrounding elements
  * Asymmetrical modern layouts with visual balance
  * Grid-based structured designs
  * Organic flowing compositions
  * Layered depth with foreground/background elements

REQUIREMENTS:
- Square 1:1 aspect ratio for ${platform}
- High-quality, professional design
- Clear, readable text with good contrast
- Modern typography (sans-serif fonts)
- Clean composition with proper spacing
- Brand colors prominently featured
- ${visualStyle} aesthetic
- Perfect for ${businessType} business
${brandProfile.websiteUrl ? `- Website available for CTAs when contextually appropriate: ${cleanWebsiteUrl(brandProfile.websiteUrl)}` : ''}

STYLE DETAILS:
- Clean, modern layout with creative variety
- Professional appearance with artistic flair
- Eye-catching but not cluttered
- Perfect text readability
- Brand-appropriate imagery with creative contexts
- High contrast for mobile viewing
- Sophisticated color harmony with dynamic elements

Create a beautiful, professional design that represents ${brandProfile.businessName || businessType} perfectly.`;
    return prompt;
}
/**
 * Build comprehensive brand color system with specific usage instructions
 */ function buildBrandColorSystem(brandProfile) {
    const primaryColor = brandProfile.primaryColor || '#2563eb';
    const accentColor = brandProfile.accentColor || '#7c3aed';
    const backgroundColor = brandProfile.backgroundColor || '#ffffff';
    return `- Primary Brand Color: ${primaryColor} (DOMINANT 60-70% of design - main brand elements, headlines, key accents)
- Secondary/Accent Color: ${accentColor} (HIGHLIGHTS 20-30% of design - call-to-action elements, highlights, buttons)
- Background Color: ${backgroundColor} (BASE 10-20% of design - primary background)
- MAXIMUM 3 COLORS TOTAL: These are the ONLY colors allowed in the entire design
- ABSOLUTE LIMITS: No 4th, 5th, or additional colors beyond these 3, NO LINES of any kind
- FORBIDDEN: Any design using more than 3 colors total, any lines/borders/dividers/linear elements
- STRICT RULES: Maximum 3 colors, no lines in entire design - NO exceptions
- Text colors: Use high contrast white or black text only when needed for readability`;
}
/**
 * Build advanced typography system for perfect readability
 */ function buildTypographySystem(businessType, platform) {
    const isLandscape = platform.toLowerCase() === 'twitter' || platform.toLowerCase() === 'linkedin';
    return `- Headline: Large, bold, highly readable font (${isLandscape ? '48-64px' : '36-48px'})
- Subheadline: Medium weight, clear font (${isLandscape ? '24-32px' : '20-28px'})
- Body Text: Clean, readable font (${isLandscape ? '16-20px' : '14-18px'})
- Font Family: Modern sans-serif (Helvetica, Arial, Roboto, or similar)
- Text Color: High contrast against background (dark text on light backgrounds, light text on dark backgrounds)
- Text Effects: Subtle shadows, outlines, or background overlays to ensure readability
- Letter Spacing: Optimal spacing for digital readability
- Line Height: 1.2-1.4 for optimal readability`;
}
/**
 * Build layout guidance for optimal composition
 */ function buildLayoutGuidance(platform, businessType) {
    const isLandscape = platform.toLowerCase() === 'twitter' || platform.toLowerCase() === 'linkedin';
    return `- Composition: ${isLandscape ? 'Horizontal layout with left-right or center focus' : 'Vertical layout with top-bottom hierarchy'}
- Visual Hierarchy: Clear focal points with proper element sizing
- White Space: Generous use of negative space for clean, professional appearance
- Alignment: Perfect alignment of all elements using invisible grid system
- Balance: Harmonious distribution of visual weight across the design
- Focus Areas: Clear primary and secondary focus points
- Margins: Adequate padding from edges (${isLandscape ? '60-80px' : '40-60px'} minimum)
- Grid System: Use professional grid-based layout for perfect alignment`;
}
/**
 * Get platform-specific specifications for image generation
 */ function getPlatformSpecifications(platform) {
    const specs = {
        instagram: {
            name: 'Instagram',
            dimensions: '1080x1080px (square)',
            description: 'Instagram feed post optimized for mobile viewing'
        },
        facebook: {
            name: 'Facebook',
            dimensions: '1200x630px (landscape)',
            description: 'Facebook post optimized for news feed'
        },
        twitter: {
            name: 'Twitter/X',
            dimensions: '1200x675px (landscape)',
            description: 'Twitter post optimized for timeline viewing'
        },
        linkedin: {
            name: 'LinkedIn',
            dimensions: '1200x627px (landscape)',
            description: 'LinkedIn post optimized for professional networking'
        }
    };
    return specs[platform.toLowerCase()] || specs.instagram;
}
/**
 * Get business type-specific design guidance with industry elements
 */ function getBusinessTypeGuidance(businessType) {
    const guidance = {
        'restaurant': `- Warm, appetizing color palette (oranges, reds, warm browns)
- Food-related imagery and culinary elements
- Inviting, cozy atmosphere in design
- Focus on creating hunger appeal and comfort
- Use textures that evoke freshness and quality`,
        'fitness': `- Energetic, dynamic color palette (vibrant oranges, reds, blues)
- Strong, powerful visual elements
- Motion and energy in design composition
- Emphasis on strength, vitality, and achievement
- Athletic and motivational visual language`,
        'technology': `- Clean, futuristic design with tech-inspired elements
- Cool color palette (blues, grays, whites)
- Modern geometric shapes and digital aesthetics
- Emphasis on innovation and cutting-edge feel
- Sleek, high-tech visual language`,
        'healthcare': `- Calming, trustworthy color palette (blues, greens, whites)
- Clean, sterile aesthetic with medical professionalism
- Focus on trust, care, and reliability
- Soothing visual elements that inspire confidence
- Professional medical imagery and symbols`,
        'education': `- Inspiring, knowledge-focused design
- Warm, encouraging color palette
- Elements that suggest growth and learning
- Professional yet approachable aesthetic
- Symbols of knowledge, growth, and achievement`,
        'retail': `- Attractive, product-focused design
- Commercial appeal with shopping psychology
- Colors that encourage purchasing decisions
- Focus on quality, value, and desirability
- Professional retail presentation standards`,
        'finance': `- Professional, trustworthy design language
- Conservative color palette (blues, grays, whites)
- Emphasis on security, stability, and reliability
- Corporate-level visual standards
- Symbols of growth, security, and trust`,
        'real estate': `- Sophisticated, aspirational design aesthetic
- Premium color palette suggesting luxury and quality
- Focus on lifestyle and investment appeal
- Professional property presentation standards
- Elements suggesting home, security, and success`,
        'beauty': `- Elegant, attractive design with aesthetic appeal
- Soft, beautiful color palette (pinks, golds, whites)
- Focus on transformation and enhancement
- Luxurious, premium visual language
- Elements suggesting beauty, care, and self-improvement`,
        'automotive': `- Strong, reliable design with performance appeal
- Bold color palette (reds, blacks, metallics)
- Focus on power, performance, and quality
- Dynamic visual elements suggesting speed and strength
- Professional automotive presentation standards`
    };
    const type = businessType.toLowerCase();
    for (const [key, value] of Object.entries(guidance)){
        if (type.includes(key)) {
            return value;
        }
    }
    return `- Professional, versatile design appropriate for business
- Balanced color palette suitable for general business use
- Clean, modern aesthetic with broad appeal
- Trustworthy and professional appearance
- Industry-neutral visual elements`;
}
/**
 * Get advanced visual style guidance with 2024-2025 trends
 */ function getAdvancedVisualStyleGuidance(visualStyle) {
    const styles = {
        'modern': `- Clean, minimalist design with contemporary 2024-2025 aesthetics
- Subtle gradients and soft shadows for depth
- Modern geometric shapes and clean lines
- Sophisticated color transitions and overlays
- Contemporary typography with perfect spacing`,
        'professional': `- Sophisticated, business-appropriate design
- Premium quality appearance with polished finish
- Corporate-level visual standards
- Trustworthy and authoritative visual language
- Executive-level presentation quality`,
        'creative': `- Artistic and innovative design elements
- Unique compositions with creative flair
- Bold color combinations and artistic effects
- Creative typography and layout experimentation
- Inspiring and visually striking appearance`,
        'elegant': `- Refined, luxurious aesthetic with premium feel
- Sophisticated typography and perfect spacing
- Subtle, high-end color palette
- Graceful design elements and smooth transitions
- Timeless elegance with modern touches`,
        'bold': `- Strong visual impact with dynamic energy
- Vibrant, attention-grabbing color schemes
- Powerful typography and dramatic compositions
- High-contrast elements for maximum impact
- Confident and assertive visual language`,
        'minimalist': `- Ultra-clean design with generous white space
- Minimal elements with maximum impact
- Perfect typography hierarchy and spacing
- Subtle color palette with strategic accents
- Zen-like simplicity with purposeful design`,
        'playful': `- Fun, engaging design with creative energy
- Bright, cheerful color combinations
- Casual, approachable typography
- Interactive visual elements and patterns
- Youthful and energetic aesthetic`,
        'luxury': `- Premium, high-end aesthetic with sophisticated appeal
- Rich color palette with metallic accents
- Elegant typography with refined spacing
- Luxurious textures and premium materials feel
- Exclusive, aspirational visual language`
    };
    const style = visualStyle.toLowerCase();
    for (const [key, value] of Object.entries(styles)){
        if (style.includes(key)) {
            return value;
        }
    }
    return `- Modern, professional design with clean aesthetics
- Contemporary visual language with 2024-2025 trends
- Balanced composition with good visual hierarchy
- Sophisticated color palette and typography
- Versatile design suitable for professional use`;
}
/**
 * Get industry-specific visual elements and symbols
 */ function getIndustrySpecificElements(businessType) {
    const elements = {
        'restaurant': `- Subtle food-related icons or patterns
- Warm lighting effects and cozy atmosphere
- Natural textures (wood, stone) if appropriate
- Appetizing color gradients and warm tones`,
        'technology': `- Geometric patterns and digital elements
- Circuit board inspired subtle patterns
- Modern icons and tech symbols
- Futuristic lighting effects and gradients`,
        'healthcare': `- Medical cross or health symbols (subtle)
- Clean, sterile visual elements
- Calming nature elements (leaves, water)
- Professional medical color schemes`,
        'fitness': `- Dynamic shapes suggesting movement
- Strength and energy visual metaphors
- Athletic-inspired design elements
- Motivational visual language`,
        'education': `- Book, graduation, or learning symbols (subtle)
- Growth metaphors (trees, arrows, stairs)
- Knowledge-inspired visual elements
- Academic color schemes and typography`,
        'retail': `- Shopping and commerce visual elements
- Product showcase design principles
- Commercial appeal aesthetics
- Quality and value visual indicators`,
        'finance': `- Growth charts, arrows, or financial symbols (subtle)
- Professional corporate design elements
- Trust and security visual metaphors
- Conservative, reliable aesthetic choices`,
        'real estate': `- Home, building, or property symbols (subtle)
- Architectural elements and clean lines
- Luxury and quality visual indicators
- Professional property presentation elements`,
        'beauty': `- Elegant, aesthetic design elements
- Soft, beautiful patterns and textures
- Luxury and premium visual indicators
- Transformation and enhancement metaphors`,
        'automotive': `- Speed and performance visual elements
- Dynamic lines and powerful shapes
- Metallic textures and bold contrasts
- Professional automotive presentation standards`
    };
    const type = businessType.toLowerCase();
    for (const [key, value] of Object.entries(elements)){
        if (type.includes(key)) {
            return value;
        }
    }
    return `- Universal business symbols and elements
- Professional geometric patterns
- Versatile design elements suitable for any industry
- Clean, modern visual language`;
}
}}),
"[project]/src/app/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/app/actions.ts
/* __next_internal_action_entry_do_not_use__ [{"60e429b11b311c0b871a717d8e7e036936b64edea0":"analyzeBrandAction","7002a50d713c2964d62133b2af4480664176bd8291":"generateVideoContentAction","78a2cc90f0c309202520d68173137d83d08ea32806":"generateContentAction","7f1495e5c9392c71a0638d8ee09c874aef3fff06f4":"generateGeminiHDDesignAction","7f184a7753a1c29638132401afe2bdafb4cd96f602":"generateContentWithArtifactsAction","7f5d637b281692c3f27909f9e0d661b29f958881f8":"generateCreativeAssetAction","7f7ba9144001954daaaa5cea5934ab233d76c6e00d":"generateEnhancedDesignAction"},"",""] */ __turbopack_context__.s({
    "analyzeBrandAction": (()=>analyzeBrandAction),
    "generateContentAction": (()=>generateContentAction),
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction),
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction),
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction),
    "generateGeminiHDDesignAction": (()=>generateGeminiHDDesignAction),
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$analyze$2d$brand$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/analyze-brand.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$registry$2f$model$2d$registry$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/registry/model-registry.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$video$2d$post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-video-post.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/artifacts-service.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$gemini$2d$2$2e$5$2d$design$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/gemini-2.5-design.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
async function analyzeBrandAction(websiteUrl, designImageUris) {
    try {
        // Validate URL format
        if (!websiteUrl || !websiteUrl.trim()) {
            return {
                success: false,
                error: "Website URL is required",
                errorType: 'error'
            };
        }
        // Ensure URL has protocol
        let validUrl = websiteUrl.trim();
        if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {
            validUrl = 'https://' + validUrl;
        }
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$analyze$2d$brand$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeBrand"])({
            websiteUrl: validUrl,
            designImageUris: designImageUris || []
        });
        if (!result) {
            return {
                success: false,
                error: "Analysis returned empty result",
                errorType: 'error'
            };
        }
        return {
            success: true,
            data: result
        };
    } catch (error) {
        // Return structured error response instead of throwing
        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
        if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {
            return {
                success: false,
                error: "Website blocks automated access. This is common for security reasons.",
                errorType: 'blocked'
            };
        } else if (errorMessage.includes('timeout')) {
            return {
                success: false,
                error: "Website analysis timed out. Please try again or check if the website is accessible.",
                errorType: 'timeout'
            };
        } else if (errorMessage.includes('CORS')) {
            return {
                success: false,
                error: "Website blocks automated access. This is common for security reasons.",
                errorType: 'blocked'
            };
        } else {
            return {
                success: false,
                error: `Analysis failed: ${errorMessage}`,
                errorType: 'error'
            };
        }
    }
}
const getAspectRatioForPlatform = (platform)=>{
    switch(platform){
        case 'Instagram':
            return '1:1'; // Square
        case 'Facebook':
            return '16:9'; // Landscape - Facebook posts are landscape format
        case 'Twitter':
            return '16:9'; // Landscape
        case 'LinkedIn':
            return '16:9'; // Landscape - LinkedIn posts are landscape format
        default:
            return '1:1';
    }
};
async function generateContentAction(profile, platform, brandConsistency, useLocalLanguage = false) {
    try {
        const today = new Date();
        const dayOfWeek = today.toLocaleDateString('en-US', {
            weekday: 'long'
        });
        const currentDate = today.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        // Apply brand consistency logic
        const effectiveDesignExamples = brandConsistency?.strictConsistency ? profile.designExamples || [] : []; // Don't use design examples if not strict consistency
        // Enhanced brand profile data extraction
        const enhancedProfile = {
            ...profile,
            // Ensure brand colors are available
            primaryColor: profile.primaryColor || '#3B82F6',
            accentColor: profile.accentColor || '#10B981',
            backgroundColor: profile.backgroundColor || '#F8FAFC',
            // Extract services information
            servicesArray: typeof profile.services === 'string' ? profile.services.split('\n').filter((s)=>s.trim()) : Array.isArray(profile.services) ? profile.services.map((s)=>typeof s === 'string' ? s : s.name || s.description || '') : [],
            // Extract contact information for brand context
            contactInfo: profile.contactInfo || {},
            socialMedia: profile.socialMedia || {}
        };
        // Convert arrays to newline-separated strings for AI processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        // Convert services array to newline-separated string
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        // Ensure model registry is initialized
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$registry$2f$model$2d$registry$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelRegistry"].isInitialized()) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$registry$2f$model$2d$registry$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelRegistry"].initialize();
        }
        // Use Revo 1.0 model through the registry for enhanced Gemini 2.5 Flash Image Preview
        const revo10Model = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$registry$2f$model$2d$registry$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelRegistry"].getModel('revo-1.0');
        if (!revo10Model) {
            throw new Error('Revo 1.0 model not available');
        }
        const generationRequest = {
            modelId: 'revo-1.0',
            profile: enhancedProfile,
            platform: platform,
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            },
            artifactIds: [],
            contentThemes: enhancedProfile.contentThemes || [],
            writingTone: enhancedProfile.writingTone || 'professional',
            targetAudience: enhancedProfile.targetAudience || 'General',
            keyFeatures: enhancedProfile.keyFeatures || [],
            competitiveAdvantages: enhancedProfile.competitiveAdvantages || [],
            services: enhancedProfile.services || [],
            visualStyle: enhancedProfile.visualStyle || 'modern',
            primaryColor: enhancedProfile.primaryColor || '#3B82F6',
            accentColor: enhancedProfile.accentColor || '#10B981',
            backgroundColor: enhancedProfile.backgroundColor || '#F8FAFC',
            logoDataUrl: enhancedProfile.logoDataUrl,
            designExamples: effectiveDesignExamples,
            dayOfWeek: dayOfWeek,
            currentDate: currentDate,
            variants: [
                {
                    platform: platform,
                    aspectRatio: getAspectRatioForPlatform(platform)
                }
            ]
        };
        const result = await revo10Model.contentGenerator.generateContent(generationRequest);
        if (!result.success) {
            throw new Error(result.error || 'Content generation failed');
        }
        const postDetails = result.data;
        const newPost = {
            id: new Date().toISOString(),
            date: today.toISOString(),
            content: postDetails.content,
            hashtags: postDetails.hashtags,
            status: 'generated',
            variants: postDetails.variants,
            catchyWords: postDetails.catchyWords,
            subheadline: postDetails.subheadline || '',
            callToAction: postDetails.callToAction || '',
            // Revo 1.0 doesn't include these advanced features
            contentVariants: undefined,
            hashtagAnalysis: undefined,
            marketIntelligence: undefined,
            localContext: undefined
        };
        return newPost;
    } catch (error) {
        throw new Error("Failed to generate content. Please try again later.");
    }
}
async function generateVideoContentAction(profile, catchyWords, postContent) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$video$2d$post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateVideoPost"])({
            businessType: profile.businessType,
            location: profile.location,
            visualStyle: profile.visualStyle,
            imageText: catchyWords,
            postContent: postContent
        });
        return {
            videoUrl: result.videoUrl
        };
    } catch (error) {
        // Pass the specific error message from the flow to the client
        throw new Error(error.message);
    }
}
async function generateCreativeAssetAction(prompt, outputType, referenceAssetUrl, useBrandProfile, brandProfile, maskDataUrl, aspectRatio) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAsset"])({
            prompt,
            outputType,
            referenceAssetUrl,
            useBrandProfile,
            brandProfile: useBrandProfile ? brandProfile : null,
            maskDataUrl,
            aspectRatio
        });
        return result;
    } catch (error) {
        // Always pass the specific error message from the flow to the client.
        throw new Error(error.message);
    }
}
async function generateEnhancedDesignAction(businessType, platform, visualStyle, imageText, brandProfile, enableEnhancements = true, brandConsistency, artifactInstructions, includePeopleInDesigns = true, useLocalLanguage = false) {
    const startTime = Date.now();
    const enhancementsApplied = [];
    try {
        if (!brandProfile) {
            throw new Error('Brand profile is required for enhanced design generation');
        }
        // Handle both old string format and new object format
        let finalImageText;
        if (typeof imageText === 'string') {
            finalImageText = imageText;
        } else {
            // Combine catchy words, subheadline, and call-to-action
            const components = [
                imageText.catchyWords
            ];
            if (imageText.subheadline && imageText.subheadline.trim()) {
                components.push(imageText.subheadline.trim());
            }
            if (imageText.callToAction && imageText.callToAction.trim()) {
                components.push(imageText.callToAction.trim());
            }
            finalImageText = components.join('\n');
        }
        // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD
        let result;
        try {
            result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$gemini$2d$2$2e$5$2d$design$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateEnhancedDesign"])({
                businessType,
                platform,
                visualStyle,
                imageText: finalImageText,
                brandProfile,
                brandConsistency,
                artifactInstructions,
                includePeopleInDesigns,
                useLocalLanguage
            });
        } catch (gemini25Error) {
            try {
                const { generateEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                result = await generateEnhancedDesignWithFallback({
                    businessType,
                    platform,
                    visualStyle,
                    imageText: finalImageText,
                    brandProfile,
                    brandConsistency,
                    artifactInstructions
                });
            } catch (openaiError) {
                const { generateGeminiHDEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                result = await generateGeminiHDEnhancedDesignWithFallback({
                    businessType,
                    platform,
                    visualStyle,
                    imageText: finalImageText,
                    brandProfile,
                    brandConsistency,
                    artifactInstructions
                });
            }
        }
        return {
            imageUrl: result.imageUrl,
            qualityScore: result.qualityScore,
            enhancementsApplied: result.enhancementsApplied,
            processingTime: result.processingTime
        };
    } catch (error) {
        throw new Error(error.message);
    }
}
async function generateGeminiHDDesignAction(businessType, platform, visualStyle, imageText, brandProfile, brandConsistency, artifactInstructions) {
    try {
        if (!brandProfile) {
            throw new Error('Brand profile is required for Gemini HD design generation');
        }
        const { generateGeminiHDEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        const result = await generateGeminiHDEnhancedDesignWithFallback({
            businessType,
            platform,
            visualStyle,
            imageText,
            brandProfile,
            brandConsistency,
            artifactInstructions
        });
        return {
            platform,
            imageUrl: result.imageUrl,
            caption: imageText,
            hashtags: []
        };
    } catch (error) {
        throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateContentWithArtifactsAction(profile, platform, brandConsistency, artifactIds = [], useEnhancedDesign = true, includePeopleInDesigns = true, useLocalLanguage = false) {
    try {
        // Get active artifacts if no specific artifacts provided
        let targetArtifacts = [];
        if (artifactIds.length > 0) {
            // Use specified artifacts
            for (const artifactId of artifactIds){
                const artifact = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getArtifact(artifactId);
                if (artifact) {
                    targetArtifacts.push(artifact);
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifactId, 'quick-content');
                }
            }
        } else {
            // Use active artifacts, prioritizing exact-use
            const activeArtifacts = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getActiveArtifacts();
            const exactUseArtifacts = activeArtifacts.filter((a)=>a.usageType === 'exact-use');
            const referenceArtifacts = activeArtifacts.filter((a)=>a.usageType === 'reference');
            // Prioritize exact-use artifacts
            targetArtifacts = [
                ...exactUseArtifacts,
                ...referenceArtifacts.slice(0, 3)
            ];
            // Track usage for active artifacts
            for (const artifact of targetArtifacts){
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifact.id, 'quick-content');
            }
        }
        // Generate base content first
        const basePost = await generateContentAction(profile, platform, brandConsistency);
        // If enhanced design is disabled, return base content
        if (!useEnhancedDesign) {
            return basePost;
        }
        // Enhanced design is enabled - always use enhanced generation regardless of artifacts
        if (targetArtifacts.length === 0) {} else {}
        // Separate exact-use and reference artifacts
        const exactUseArtifacts = targetArtifacts.filter((a)=>a.usageType === 'exact-use');
        const referenceArtifacts = targetArtifacts.filter((a)=>a.usageType === 'reference');
        // Create enhanced image text structure from post components
        let enhancedImageText = {
            catchyWords: basePost.catchyWords || 'Engaging Content',
            subheadline: basePost.subheadline,
            callToAction: basePost.callToAction
        };
        let enhancedContent = basePost.content;
        // Collect usage instructions from artifacts
        const artifactInstructions = targetArtifacts.filter((a)=>a.instructions && a.instructions.trim()).map((a)=>`- ${a.name}: ${a.instructions}`).join('\n');
        // Collect text overlay instructions from text artifacts
        const textOverlayInstructions = exactUseArtifacts.filter((a)=>a.textOverlay?.instructions && a.textOverlay.instructions.trim()).map((a)=>`- ${a.name}: ${a.textOverlay.instructions}`).join('\n');
        // Process exact-use artifacts first (higher priority)
        if (exactUseArtifacts.length > 0) {
            const primaryExactUse = exactUseArtifacts[0];
            // Use text overlay if available
            if (primaryExactUse.textOverlay) {
                if (primaryExactUse.textOverlay.headline) {
                    enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;
                }
                if (primaryExactUse.textOverlay.message) {
                    enhancedContent = primaryExactUse.textOverlay.message;
                }
                // Use CTA from artifact if available
                if (primaryExactUse.textOverlay.cta) {
                    enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;
                }
            }
        }
        // Process reference artifacts for style guidance
        const activeDirectives = referenceArtifacts.flatMap((artifact)=>artifact.directives.filter((directive)=>directive.active));
        // Apply style reference directives
        const styleDirectives = activeDirectives.filter((d)=>d.type === 'style-reference');
        let visualStyleOverride = profile.visualStyle || 'modern';
        if (styleDirectives.length > 0) {
            const primaryStyleDirective = styleDirectives.find((d)=>d.priority >= 7);
            if (primaryStyleDirective) {
                visualStyleOverride = 'artifact-inspired';
            }
        }
        // Combine all instructions
        const allInstructions = [
            artifactInstructions,
            textOverlayInstructions
        ].filter(Boolean).join('\n');
        // Generate enhanced design with artifact context
        const enhancedResult = await generateEnhancedDesignAction(profile.businessType || 'business', platform.toLowerCase(), visualStyleOverride, enhancedImageText, profile, true, brandConsistency, allInstructions || undefined, includePeopleInDesigns, useLocalLanguage);
        // Create enhanced post with artifact metadata
        const enhancedPost = {
            ...basePost,
            id: Date.now().toString(),
            variants: [
                {
                    platform: platform,
                    imageUrl: enhancedResult.imageUrl
                }
            ],
            content: targetArtifacts.length > 0 ? `${enhancedContent}\n\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)` : `${enhancedContent}\n\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,
            date: new Date().toISOString(),
            // Add artifact metadata
            metadata: {
                ...basePost.metadata,
                referencedArtifacts: targetArtifacts.map((a)=>({
                        id: a.id,
                        name: a.name,
                        type: a.type,
                        category: a.category
                    })),
                activeDirectives: activeDirectives.map((d)=>({
                        id: d.id,
                        type: d.type,
                        label: d.label,
                        priority: d.priority
                    }))
            }
        };
        return enhancedPost;
    } catch (error) {
        throw new Error(error.message);
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    analyzeBrandAction,
    generateContentAction,
    generateVideoContentAction,
    generateCreativeAssetAction,
    generateEnhancedDesignAction,
    generateGeminiHDDesignAction,
    generateContentWithArtifactsAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(analyzeBrandAction, "60e429b11b311c0b871a717d8e7e036936b64edea0", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateContentAction, "78a2cc90f0c309202520d68173137d83d08ea32806", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateVideoContentAction, "7002a50d713c2964d62133b2af4480664176bd8291", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateCreativeAssetAction, "7f5d637b281692c3f27909f9e0d661b29f958881f8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateEnhancedDesignAction, "7f7ba9144001954daaaa5cea5934ab233d76c6e00d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateGeminiHDDesignAction, "7f1495e5c9392c71a0638d8ee09c874aef3fff06f4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateContentWithArtifactsAction, "7f184a7753a1c29638132401afe2bdafb4cd96f602", null);
}}),
"[project]/src/ai/revo-2.0-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Service - Next-Generation AI Content Creation
 * Uses Gemini 2.5 Flash Image Preview for enhanced content generation
 */ __turbopack_context__.s({
    "generateWithRevo20": (()=>generateWithRevo20),
    "testRevo20Availability": (()=>testRevo20Availability)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-rsc] (ecmascript) <export OpenAI as default>");
;
;
// Initialize AI clients
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](process.env.GEMINI_API_KEY);
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
// Revo 2.0 uses Gemini 2.5 Flash Image Preview (same as Revo 1.0 but with enhanced prompting)
const REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';
/**
 * Generate enhanced creative concept using GPT-4
 */ async function generateCreativeConcept(options) {
    const { businessType, platform, brandProfile, visualStyle = 'modern' } = options;
    const prompt = `You are a world-class creative director specializing in ${businessType} businesses. 
Create an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.

Business Context:
- Type: ${businessType}
- Platform: ${platform}
- Style: ${visualStyle}
- Location: ${brandProfile.location || 'Global'}
- Brand: ${brandProfile.businessName || businessType}

Create a concept that:
1. Feels authentic and locally relevant
2. Uses relatable human experiences
3. Connects emotionally with the target audience
4. Incorporates cultural nuances naturally
5. Avoids generic corporate messaging

Return your response in this exact JSON format:
{
  "concept": "Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)",
  "catchwords": ["word1", "word2", "word3", "word4", "word5"],
  "visualDirection": "Authentic visual direction that feels real and community-focused (2-3 sentences)",
  "designElements": ["element1", "element2", "element3", "element4"],
  "colorSuggestions": ["#color1", "#color2", "#color3"],
  "moodKeywords": ["mood1", "mood2", "mood3", "mood4"],
  "targetEmotions": ["emotion1", "emotion2", "emotion3"]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.8,
        max_tokens: 1000
    });
    try {
        const content = response.choices[0].message.content || '{}';
        // Remove markdown code blocks if present
        const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        return JSON.parse(cleanContent);
    } catch (error) {
        return {
            concept: `Professional ${businessType} content for ${platform}`,
            catchwords: [
                'quality',
                'professional',
                'trusted',
                'local',
                'expert'
            ],
            visualDirection: 'Clean, professional design with modern aesthetics',
            designElements: [
                'clean typography',
                'professional imagery',
                'brand colors',
                'modern layout'
            ],
            colorSuggestions: [
                '#2563eb',
                '#1f2937',
                '#f8fafc'
            ],
            moodKeywords: [
                'professional',
                'trustworthy',
                'modern',
                'clean'
            ],
            targetEmotions: [
                'trust',
                'confidence',
                'reliability'
            ]
        };
    }
}
async function generateWithRevo20(options) {
    const startTime = Date.now();
    try {
        // Step 1: Generate creative concept
        const concept = await generateCreativeConcept(options);
        // Step 2: Build enhanced prompt
        const enhancedPrompt = buildEnhancedPrompt(options, concept);
        // Step 3: Generate image with Gemini 2.5 Flash Image Preview
        const imageResult = await generateImageWithGemini(enhancedPrompt, options);
        // Step 4: Generate caption and hashtags
        const contentResult = await generateCaptionAndHashtags(options, concept);
        const processingTime = Date.now() - startTime;
        return {
            imageUrl: imageResult.imageUrl,
            model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',
            qualityScore: 9.2,
            processingTime,
            enhancementsApplied: [
                'Creative concept generation',
                'Enhanced prompt engineering',
                'Brand consistency optimization',
                'Platform-specific formatting',
                'Cultural relevance integration'
            ],
            caption: contentResult.caption,
            hashtags: contentResult.hashtags
        };
    } catch (error) {
        throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Build enhanced prompt for Revo 2.0
 */ function buildEnhancedPrompt(options, concept) {
    const { businessType, platform, brandProfile, aspectRatio = '1:1', visualStyle = 'modern' } = options;
    return `Create a high-quality, professional ${businessType} design for ${platform}.

CREATIVE CONCEPT: ${concept.concept}

VISUAL DIRECTION: ${concept.visualDirection}

DESIGN REQUIREMENTS:
- Style: ${visualStyle}, premium quality
- Aspect Ratio: ${aspectRatio}
- Platform: ${platform} optimized
- Business: ${brandProfile.businessName || businessType}
- Location: ${brandProfile.location || 'Professional setting'}

DESIGN ELEMENTS:
${concept.designElements.map((element)=>`- ${element}`).join('\n')}

MOOD & EMOTIONS:
- Target emotions: ${concept.targetEmotions.join(', ')}
- Mood keywords: ${concept.moodKeywords.join(', ')}

BRAND INTEGRATION:
- Colors: ${brandProfile.primaryColor ? `Primary: ${brandProfile.primaryColor}, Accent: ${brandProfile.accentColor}, Background: ${brandProfile.backgroundColor}` : concept.colorSuggestions.join(', ')}
- Business name: ${brandProfile.businessName || businessType}
- Logo: ${brandProfile.logoDataUrl ? 'Include provided brand logo prominently' : 'No logo provided'}
- Professional, trustworthy appearance

QUALITY STANDARDS:
- Ultra-high resolution and clarity
- Professional composition
- Perfect typography and text rendering
- MAXIMUM 3 COLORS ONLY (use brand colors if provided)
- NO LINES: no decorative lines, borders, dividers, or linear elements
- Platform-optimized dimensions
- Brand consistency throughout
- Clean, minimalist design with 50%+ white space

Create a stunning, professional design that captures the essence of this ${businessType} business.`;
}
/**
 * Generate image using Gemini 2.5 Flash Image Preview with logo support
 */ async function generateImageWithGemini(prompt, options) {
    const maxRetries = 3;
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            const model = ai.getGenerativeModel({
                model: REVO_2_0_MODEL,
                generationConfig: {
                    temperature: 0.7,
                    topP: 0.9,
                    topK: 40,
                    maxOutputTokens: 2048
                }
            });
            // Prepare the generation request with logo if available
            const generationParts = [
                'You are an expert graphic designer using Gemini 2.5 Flash Image Preview. Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.',
                prompt
            ];
            // If logo is provided, include it in the generation
            if (options.brandProfile.logoDataUrl) {
                // Extract the base64 data and mime type from the data URL
                const logoMatch = options.brandProfile.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);
                if (logoMatch) {
                    const [, mimeType, base64Data] = logoMatch;
                    generationParts.push({
                        inlineData: {
                            data: base64Data,
                            mimeType: mimeType
                        }
                    });
                    // Update the prompt to reference the provided logo
                    const logoPrompt = `\n\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;
                    generationParts[1] = prompt + logoPrompt;
                } else {}
            }
            const result = await model.generateContent(generationParts);
            const response = await result.response;
            // Extract image data from Gemini response (same as Revo 1.0)
            const parts = response.candidates?.[0]?.content?.parts || [];
            let imageUrl = '';
            for (const part of parts){
                if (part.inlineData) {
                    const imageData = part.inlineData.data;
                    const mimeType = part.inlineData.mimeType;
                    imageUrl = `data:${mimeType};base64,${imageData}`;
                    break;
                }
            }
            if (!imageUrl) {
                throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');
            }
            return {
                imageUrl
            };
        } catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                break;
            }
            const waitTime = Math.pow(2, attempt) * 1000;
            await new Promise((resolve)=>setTimeout(resolve, waitTime));
        }
    }
    throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);
}
/**
 * Generate caption and hashtags with AI-powered contextual generation
 */ async function generateCaptionAndHashtags(options, concept) {
    const { businessType, platform, brandProfile } = options;
    const prompt = `Create engaging ${platform} content for a ${businessType} business.

Business Details:
- Name: ${brandProfile.businessName || businessType}
- Type: ${businessType}
- Location: ${brandProfile.location || 'Local area'}
- Concept: ${concept.concept}
- Catchwords: ${concept.catchwords.join(', ')}

Create:
1. A catchy, engaging caption (2-3 sentences max) that incorporates the concept and catchwords naturally
2. 10 highly relevant, specific hashtags that are:
   - Specific to this business and location
   - Mix of business-specific, location-based, industry-relevant, and platform-optimized
   - Avoid generic hashtags like #business, #professional, #quality, #local
   - Discoverable and relevant to the target audience
   - Appropriate for ${platform}

Make the content authentic, locally relevant, and engaging for ${platform}.

Format as JSON:
{
  "caption": "Your engaging caption here",
  "hashtags": ["#SpecificHashtag1", "#LocationBasedHashtag", "#IndustryRelevant", ...]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 600
    });
    try {
        let responseContent = response.choices[0].message.content || '{}';
        // Remove markdown code blocks if present
        responseContent = responseContent.replace(/```json\s*|\s*```/g, '').trim();
        const result = JSON.parse(responseContent);
        // Validate the response
        if (result.caption && Array.isArray(result.hashtags) && result.hashtags.length > 0) {
            return {
                caption: result.caption,
                hashtags: result.hashtags.slice(0, 10) // Ensure max 10 hashtags
            };
        }
    } catch (error) {
        console.warn('Failed to parse AI content response:', error);
    }
    // Fallback with contextual generation (no hardcoded placeholders)
    return generateContextualFallback(businessType, brandProfile, platform, concept);
}
/**
 * Generate contextual fallback content without hardcoded placeholders
 */ function generateContextualFallback(businessType, brandProfile, platform, concept) {
    const businessName = brandProfile.businessName || businessType;
    const location = brandProfile.location || 'your area';
    // Generate contextual caption
    const caption = `${concept.catchwords[0] || 'Discover'} what makes ${businessName} special in ${location}! ${concept.concept || 'Experience the difference with our exceptional service.'}`;
    // Generate contextual hashtags
    const hashtags = [];
    // Business-specific
    hashtags.push(`#${businessName.replace(/\s+/g, '')}`);
    hashtags.push(`#${businessType.replace(/\s+/g, '')}Business`);
    // Location-based
    const locationParts = location.split(',').map((part)=>part.trim());
    locationParts.forEach((part)=>{
        if (part.length > 2) {
            hashtags.push(`#${part.replace(/\s+/g, '')}`);
        }
    });
    // Platform-specific contextual
    if (platform === 'instagram') {
        hashtags.push('#InstagramContent', '#VisualStory');
    } else if (platform === 'facebook') {
        hashtags.push('#FacebookPost', '#CommunityBusiness');
    } else if (platform === 'linkedin') {
        hashtags.push('#LinkedInBusiness', '#ProfessionalServices');
    } else if (platform === 'tiktok') {
        hashtags.push('#TikTokBusiness', '#CreativeContent');
    }
    // Add current date context
    const today = new Date();
    const dayName = today.toLocaleDateString('en-US', {
        weekday: 'long'
    });
    hashtags.push(`#${dayName}Vibes`);
    return {
        caption,
        hashtags: [
            ...new Set(hashtags)
        ].slice(0, 10) // Remove duplicates and limit to 10
    };
}
async function testRevo20Availability() {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_2_0_MODEL
        });
        const response = await model.generateContent('Create a simple test image with the text "Revo 2.0 Test" on a modern gradient background');
        const parts = response.candidates?.[0]?.content?.parts || [];
        let hasImage = false;
        for (const part of parts){
            if (part.inlineData) {
                hasImage = true;
            }
        }
        if (hasImage) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        return false;
    }
}
}}),
"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0037d3e7ff65b1183d2bcacde3402ff02b8ba14d6b":"getRevo2CapabilitiesAction","00d9590babdbfa43b881000504149fb80e78d24b15":"testRevo2AvailabilityAction","789496ee83d803e1e9644583e4a6f21523a907741c":"generateRevo2CreativeAssetAction","7c82525f1359ec90b6289e6277fb03b2b441c24044":"generateRevo2ContentAction"},"",""] */ __turbopack_context__.s({
    "generateRevo2ContentAction": (()=>generateRevo2ContentAction),
    "generateRevo2CreativeAssetAction": (()=>generateRevo2CreativeAssetAction),
    "getRevo2CapabilitiesAction": (()=>getRevo2CapabilitiesAction),
    "testRevo2AvailabilityAction": (()=>testRevo2AvailabilityAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * Revo 2.0 Server Actions
 * Next-generation content creation with Gemini 2.5 Flash Image Preview
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-2.0-service.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function generateRevo2ContentAction(brandProfile, platform, brandConsistency, prompt, options) {
    try {
        // Prepare Revo 2.0 generation options
        const revo2Options = {
            businessType: brandProfile.businessType || 'Business',
            platform,
            visualStyle: options?.visualStyle || 'modern',
            imageText: prompt || `${brandProfile.businessName || brandProfile.businessType} - Premium Content`,
            brandProfile,
            aspectRatio: options?.aspectRatio || '1:1',
            includePeopleInDesigns: options?.includePeopleInDesigns || false,
            useLocalLanguage: options?.useLocalLanguage || false
        };
        // Generate with Revo 2.0
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateWithRevo20"])(revo2Options);
        // Convert to GeneratedPost format
        const generatedPost = {
            id: `revo2-${Date.now()}`,
            date: new Date().toISOString(),
            platform: platform.toLowerCase(),
            postType: 'post',
            imageUrl: result.imageUrl,
            content: result.caption,
            hashtags: result.hashtags,
            status: 'generated',
            variants: [
                {
                    platform: platform.toLowerCase(),
                    imageUrl: result.imageUrl
                }
            ],
            metadata: {
                model: result.model,
                qualityScore: result.qualityScore,
                processingTime: result.processingTime,
                enhancementsApplied: result.enhancementsApplied
            }
        };
        return generatedPost;
    } catch (error) {
        throw new Error(`Revo 2.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo2CreativeAssetAction(brandProfile, platform, prompt, options) {
    try {
        const revo2Options = {
            businessType: brandProfile.businessType || 'Business',
            platform,
            visualStyle: options?.visualStyle || 'modern',
            imageText: prompt,
            brandProfile,
            aspectRatio: options?.aspectRatio || '1:1',
            includePeopleInDesigns: options?.includePeopleInDesigns || false,
            useLocalLanguage: false
        };
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateWithRevo20"])(revo2Options);
        return {
            success: true,
            imageUrl: result.imageUrl,
            model: result.model,
            qualityScore: result.qualityScore,
            processingTime: result.processingTime,
            enhancementsApplied: result.enhancementsApplied
        };
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
async function getRevo2CapabilitiesAction() {
    return {
        name: 'Revo 2.0',
        description: 'Next-generation AI content creation with Gemini 2.5 Flash Image Preview',
        features: [
            'Gemini 2.5 Flash Image Preview integration',
            'Multi-aspect ratio support (1:1, 16:9, 9:16, 21:9, 4:5)',
            'Advanced style control (6 styles)',
            'Professional mood settings',
            'Brand color integration',
            'Platform-optimized generation',
            'Enhanced prompt engineering',
            'Ultra-high quality output',
            'Smart caption generation',
            'Intelligent hashtag strategy'
        ],
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        qualityRange: '8.0-10.0/10',
        status: 'Next-Generation'
    };
}
async function testRevo2AvailabilityAction() {
    try {
        const isAvailable = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["testRevo20Availability"])();
        return {
            available: isAvailable,
            model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',
            message: isAvailable ? 'Revo 2.0 is available and ready!' : 'Revo 2.0 is not available. Check API key and model access.'
        };
    } catch (error) {
        return {
            available: false,
            model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',
            message: `Revo 2.0 test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateRevo2ContentAction,
    generateRevo2CreativeAssetAction,
    getRevo2CapabilitiesAction,
    testRevo2AvailabilityAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateRevo2ContentAction, "7c82525f1359ec90b6289e6277fb03b2b441c24044", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateRevo2CreativeAssetAction, "789496ee83d803e1e9644583e4a6f21523a907741c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getRevo2CapabilitiesAction, "0037d3e7ff65b1183d2bcacde3402ff02b8ba14d6b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(testRevo2AvailabilityAction, "00d9590babdbfa43b881000504149fb80e78d24b15", null);
}}),
"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "409d8d4d6ee48a33e913d651c38aa0005eeae0dac6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAsset"]),
    "60e429b11b311c0b871a717d8e7e036936b64edea0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeBrandAction"]),
    "7002a50d713c2964d62133b2af4480664176bd8291": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateVideoContentAction"]),
    "789496ee83d803e1e9644583e4a6f21523a907741c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo2CreativeAssetAction"]),
    "78a2cc90f0c309202520d68173137d83d08ea32806": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateContentAction"]),
    "7f1495e5c9392c71a0638d8ee09c874aef3fff06f4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGeminiHDDesignAction"]),
    "7f184a7753a1c29638132401afe2bdafb4cd96f602": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateContentWithArtifactsAction"]),
    "7f5d637b281692c3f27909f9e0d661b29f958881f8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAssetAction"]),
    "7f7ba9144001954daaaa5cea5934ab233d76c6e00d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateEnhancedDesignAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "409d8d4d6ee48a33e913d651c38aa0005eeae0dac6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["409d8d4d6ee48a33e913d651c38aa0005eeae0dac6"]),
    "60e429b11b311c0b871a717d8e7e036936b64edea0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e429b11b311c0b871a717d8e7e036936b64edea0"]),
    "7002a50d713c2964d62133b2af4480664176bd8291": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7002a50d713c2964d62133b2af4480664176bd8291"]),
    "789496ee83d803e1e9644583e4a6f21523a907741c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["789496ee83d803e1e9644583e4a6f21523a907741c"]),
    "78a2cc90f0c309202520d68173137d83d08ea32806": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["78a2cc90f0c309202520d68173137d83d08ea32806"]),
    "7f1495e5c9392c71a0638d8ee09c874aef3fff06f4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f1495e5c9392c71a0638d8ee09c874aef3fff06f4"]),
    "7f184a7753a1c29638132401afe2bdafb4cd96f602": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f184a7753a1c29638132401afe2bdafb4cd96f602"]),
    "7f5d637b281692c3f27909f9e0d661b29f958881f8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f5d637b281692c3f27909f9e0d661b29f958881f8"]),
    "7f7ba9144001954daaaa5cea5934ab233d76c6e00d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f7ba9144001954daaaa5cea5934ab233d76c6e00d"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$quick$2d$content$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/icon--metadata.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/quick-content/page.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/quick-content/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/quick-content/page.tsx <module evaluation>", "default");
}}),
"[project]/src/app/quick-content/page.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/quick-content/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/quick-content/page.tsx", "default");
}}),
"[project]/src/app/quick-content/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$quick$2d$content$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/quick-content/page.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$quick$2d$content$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/quick-content/page.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$quick$2d$content$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/quick-content/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/quick-content/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__07262dc6._.js.map