module.exports = {

"[externals]/sharp [external] (sharp, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("sharp", () => require("sharp"));

module.exports = mod;
}}),
"[project]/src/lib/image-processing.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Image processing utilities for aspect ratio correction
 * Crops Gemini-generated 1024x1024 images to platform-specific aspect ratios
 * Server-side implementation using Sharp
 */ __turbopack_context__.s({
    "cropImageFromUrl": (()=>cropImageFromUrl),
    "cropImageToAspectRatio": (()=>cropImageToAspectRatio),
    "getAspectRatioDescription": (()=>getAspectRatioDescription),
    "getPlatformDimensions": (()=>getPlatformDimensions),
    "needsAspectRatioCorrection": (()=>needsAspectRatioCorrection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sharp [external] (sharp, cjs)");
;
function getPlatformDimensions(platform) {
    const platformLower = platform.toLowerCase();
    // LinkedIn - 16:9 landscape (matches Revo 2.0 backend)
    if (platformLower.includes('linkedin')) {
        return {
            width: 1200,
            height: 675,
            aspectRatio: '16:9'
        };
    }
    // Facebook - 16:9 landscape (matches Revo 2.0 backend)
    if (platformLower.includes('facebook')) {
        return {
            width: 1200,
            height: 675,
            aspectRatio: '16:9'
        };
    }
    // Twitter - 16:9 landscape (matches Revo 2.0 backend)
    if (platformLower.includes('twitter')) {
        return {
            width: 1200,
            height: 675,
            aspectRatio: '16:9'
        };
    }
    // Instagram Stories - 9:16 portrait
    if (platformLower.includes('story') || platformLower.includes('reel')) {
        return {
            width: 1080,
            height: 1920,
            aspectRatio: '9:16'
        };
    }
    // Instagram Feed - 1:1 square (keep original)
    return {
        width: 1024,
        height: 1024,
        aspectRatio: '1:1'
    };
}
async function cropImageToAspectRatio(imageDataUrl, platform, options = {}) {
    try {
        const dimensions = getPlatformDimensions(platform);
        // If it's already square (Instagram), return as-is
        if (dimensions.aspectRatio === '1:1') {
            return imageDataUrl;
        }
        // Extract base64 data from data URL
        const base64Data = imageDataUrl.split(',')[1];
        if (!base64Data) {
            throw new Error('Invalid data URL format');
        }
        const imageBuffer = Buffer.from(base64Data, 'base64');
        // Get image metadata
        const image = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(imageBuffer);
        const metadata = await image.metadata();
        if (!metadata.width || !metadata.height) {
            throw new Error('Could not get image dimensions');
        }
        // Calculate crop dimensions from center
        const sourceWidth = metadata.width;
        const sourceHeight = metadata.height;
        let cropWidth;
        let cropHeight;
        let left;
        let top;
        // No cropping - return original image
        return imageDataUrl;
        "TURBOPACK unreachable";
        // Crop and resize the image using Sharp
        const processedImageBuffer = undefined;
        // Convert back to data URL
        const base64Image = undefined;
        const croppedDataUrl = undefined;
    } catch (error) {
        // Return original image if cropping fails
        return imageDataUrl;
    }
}
async function cropImageFromUrl(imageUrl, platform, options = {}) {
    try {
        // If it's already a data URL, use it directly
        if (imageUrl.startsWith('data:')) {
            return cropImageToAspectRatio(imageUrl, platform, options);
        }
        // Fetch the image
        const response = await fetch(imageUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const imageBuffer = Buffer.from(arrayBuffer);
        // Get dimensions and check if cropping is needed
        const dimensions = getPlatformDimensions(platform);
        if (dimensions.aspectRatio === '1:1') {
            // Convert to data URL and return
            const base64 = imageBuffer.toString('base64');
            const mimeType = response.headers.get('content-type') || 'image/jpeg';
            return `data:${mimeType};base64,${base64}`;
        }
        // Process with Sharp directly from buffer
        const image = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(imageBuffer);
        const metadata = await image.metadata();
        if (!metadata.width || !metadata.height) {
            throw new Error('Could not get image dimensions');
        }
        // Calculate crop dimensions
        const sourceWidth = metadata.width;
        const sourceHeight = metadata.height;
        let cropWidth;
        let cropHeight;
        let left;
        let top;
        // No cropping - return original image
        const base64 = imageBuffer.toString('base64');
        const mimeType = response.headers.get('content-type') || 'image/jpeg';
        return `data:${mimeType};base64,${base64}`;
        "TURBOPACK unreachable";
        // Crop and resize
        const processedImageBuffer = undefined;
        // Convert to data URL
        const base64Image = undefined;
        const croppedDataUrl = undefined;
    } catch (error) {
        // Return original URL if processing fails
        return imageUrl;
    }
}
function needsAspectRatioCorrection(platform) {
    const dimensions = getPlatformDimensions(platform);
    return dimensions.aspectRatio !== '1:1';
}
function getAspectRatioDescription(platform) {
    const dimensions = getPlatformDimensions(platform);
    return `${dimensions.width}x${dimensions.height} (${dimensions.aspectRatio})`;
}
/**
 * Validate data URL format
 */ function validateDataUrl(dataUrl) {
    return dataUrl.startsWith('data:image/') && dataUrl.includes('base64,');
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d9ed9975._.js.map