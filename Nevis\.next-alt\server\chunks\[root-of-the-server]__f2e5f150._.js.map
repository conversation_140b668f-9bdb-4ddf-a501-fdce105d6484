{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/rss-feed-service.ts"], "sourcesContent": ["/**\r\n * RSS Feed Service for Trending Content & Social Media Insights\r\n * Fetches and parses RSS feeds to extract trending topics, keywords, and themes\r\n */\r\n\r\nimport { parseStringPromise } from 'xml2js';\r\n\r\nexport interface RSSArticle {\r\n  title: string;\r\n  description: string;\r\n  link: string;\r\n  pubDate: Date;\r\n  category?: string;\r\n  keywords: string[];\r\n  source: string;\r\n}\r\n\r\nexport interface TrendingData {\r\n  keywords: string[];\r\n  hashtags: string[];         // Generated hashtags from keywords\r\n  topics: string[];\r\n  themes: string[];\r\n  articles: RSSArticle[];\r\n  lastUpdated: Date;\r\n\r\n  // Enhanced hashtag data\r\n  hashtagAnalytics?: {\r\n    trending: Array<{ hashtag: string; frequency: number; momentum: 'rising' | 'stable' | 'declining' }>;\r\n    byCategory: Record<string, string[]>;\r\n    byLocation: Record<string, string[]>;\r\n    byIndustry: Record<string, string[]>;\r\n    sentiment: Record<string, 'positive' | 'neutral' | 'negative'>;\r\n  };\r\n}\r\n\r\nexport class RSSFeedService {\r\n  private cache: Map<string, { data: RSSArticle[]; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000; // 30 minutes default\r\n\r\n  private readonly feedUrls = {\r\n    // Social Media & Marketing Trends\r\n    socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,\r\n    socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,\r\n    bufferBlog: process.env.RSS_BUFFER_BLOG,\r\n    hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,\r\n    sproutSocial: process.env.RSS_SPROUT_SOCIAL,\r\n    laterBlog: process.env.RSS_LATER_BLOG,\r\n\r\n    // Trending Topics & News\r\n    googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,\r\n    redditPopular: process.env.RSS_REDDIT_POPULAR,\r\n    buzzfeed: process.env.RSS_BUZZFEED,\r\n    twitterTrending: process.env.RSS_TWITTER_TRENDING,\r\n\r\n    // Business & Marketing\r\n    hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,\r\n    contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,\r\n    marketingProfs: process.env.RSS_MARKETING_PROFS,\r\n    marketingLand: process.env.RSS_MARKETING_LAND,\r\n    neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,\r\n\r\n    // Industry News\r\n    techCrunch: process.env.RSS_TECHCRUNCH,\r\n    mashable: process.env.RSS_MASHABLE,\r\n    theVerge: process.env.RSS_THE_VERGE,\r\n    wired: process.env.RSS_WIRED,\r\n\r\n    // Platform-Specific\r\n    instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,\r\n    facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,\r\n    linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,\r\n    youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,\r\n    tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,\r\n\r\n    // Analytics & Data\r\n    googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,\r\n    hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,\r\n\r\n    // Design & Creative\r\n    canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,\r\n    adobeBlog: process.env.RSS_ADOBE_BLOG,\r\n    creativeBloq: process.env.RSS_CREATIVE_BLOQ,\r\n\r\n    // Seasonal & Events\r\n    eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG,\r\n  };\r\n\r\n  /**\r\n   * Fetch and parse a single RSS feed\r\n   */\r\n  private async fetchRSSFeed(url: string, sourceName: string): Promise<RSSArticle[]> {\r\n    try {\r\n      // Check cache first\r\n      const cached = this.cache.get(url);\r\n      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n        return cached.data;\r\n      }\r\n\r\n\r\n      const response = await fetch(url, {\r\n        headers: {\r\n          'User-Agent': 'Nevis-AI-Content-Generator/1.0',\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n      }\r\n\r\n      const xmlData = await response.text();\r\n      const parsed = await parseStringPromise(xmlData);\r\n\r\n      const articles: RSSArticle[] = [];\r\n      const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];\r\n\r\n      const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');\r\n\r\n      for (const item of items.slice(0, maxArticles)) {\r\n        const article: RSSArticle = {\r\n          title: this.extractText(item.title),\r\n          description: this.extractText(item.description || item.summary),\r\n          link: this.extractText(item.link || item.id),\r\n          pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),\r\n          category: this.extractText(item.category),\r\n          keywords: this.extractKeywords(\r\n            this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)\r\n          ),\r\n          source: sourceName,\r\n        };\r\n\r\n        articles.push(article);\r\n      }\r\n\r\n      // Cache the results\r\n      this.cache.set(url, { data: articles, timestamp: Date.now() });\r\n\r\n      return articles;\r\n\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract text content from RSS item fields\r\n   */\r\n  private extractText(field: any): string {\r\n    if (!field) return '';\r\n\r\n    if (typeof field === 'string') return field;\r\n    if (Array.isArray(field) && field.length > 0) {\r\n      return typeof field[0] === 'string' ? field[0] : field[0]._ || '';\r\n    }\r\n    if (typeof field === 'object' && field._) return field._;\r\n\r\n    return '';\r\n  }\r\n\r\n  /**\r\n   * Extract keywords from text content\r\n   */\r\n  private extractKeywords(text: string): string[] {\r\n    if (!text) return [];\r\n\r\n    // Remove HTML tags and normalize text\r\n    const cleanText = text\r\n      .replace(/<[^>]*>/g, '')\r\n      .toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .replace(/\\s+/g, ' ')\r\n      .trim();\r\n\r\n    // Extract meaningful words (3+ characters, not common stop words)\r\n    const stopWords = new Set([\r\n      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'\r\n    ]);\r\n\r\n    const words = cleanText\r\n      .split(' ')\r\n      .filter(word => word.length >= 3 && !stopWords.has(word))\r\n      .slice(0, 10); // Limit to top 10 keywords per article\r\n\r\n    return Array.from(new Set(words)); // Remove duplicates\r\n  }\r\n\r\n  /**\r\n   * Fetch all RSS feeds and return trending data\r\n   */\r\n  public async getTrendingData(): Promise<TrendingData> {\r\n\r\n    const allArticles: RSSArticle[] = [];\r\n    const fetchPromises: Promise<RSSArticle[]>[] = [];\r\n\r\n    // Fetch all feeds concurrently\r\n    for (const [sourceName, url] of Object.entries(this.feedUrls)) {\r\n      if (url) {\r\n        fetchPromises.push(this.fetchRSSFeed(url, sourceName));\r\n      }\r\n    }\r\n\r\n    const results = await Promise.allSettled(fetchPromises);\r\n\r\n    // Collect all successful results\r\n    results.forEach((result) => {\r\n      if (result.status === 'fulfilled') {\r\n        allArticles.push(...result.value);\r\n      }\r\n    });\r\n\r\n    // Sort articles by publication date (newest first)\r\n    allArticles.sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());\r\n\r\n    // Extract trending keywords and topics\r\n    const allKeywords: string[] = [];\r\n    const allTopics: string[] = [];\r\n    const allThemes: string[] = [];\r\n\r\n    allArticles.forEach(article => {\r\n      allKeywords.push(...article.keywords);\r\n      if (article.title) allTopics.push(article.title);\r\n      if (article.category) allThemes.push(article.category);\r\n    });\r\n\r\n    // Count frequency and get top items\r\n    const keywordCounts = this.getTopItems(allKeywords, 50);\r\n    const topicCounts = this.getTopItems(allTopics, 30);\r\n    const themeCounts = this.getTopItems(allThemes, 20);\r\n\r\n\r\n    // 🚀 ENHANCED: Generate hashtag analytics\r\n    const hashtagAnalytics = this.generateHashtagAnalytics(allArticles, keywordCounts);\r\n\r\n    return {\r\n      keywords: keywordCounts,\r\n      hashtags: keywordCounts.map(keyword => `#${keyword.replace(/\\s+/g, '')}`), // Convert keywords to hashtags\r\n      topics: topicCounts,\r\n      themes: themeCounts,\r\n      articles: allArticles.slice(0, 100), // Return top 100 most recent articles\r\n      lastUpdated: new Date(),\r\n      hashtagAnalytics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get top items by frequency\r\n   */\r\n  private getTopItems(items: string[], limit: number): string[] {\r\n    const counts = new Map<string, number>();\r\n\r\n    items.forEach(item => {\r\n      const normalized = item.toLowerCase().trim();\r\n      if (normalized.length >= 3) {\r\n        counts.set(normalized, (counts.get(normalized) || 0) + 1);\r\n      }\r\n    });\r\n\r\n    return Array.from(counts.entries())\r\n      .sort((a, b) => b[1] - a[1])\r\n      .slice(0, limit)\r\n      .map(([item]) => item);\r\n  }\r\n\r\n  /**\r\n   * Get trending keywords for a specific category\r\n   */\r\n  public async getTrendingKeywordsByCategory(category: 'social' | 'business' | 'tech' | 'design'): Promise<string[]> {\r\n    const trendingData = await this.getTrendingData();\r\n\r\n    const categoryFeeds = {\r\n      social: ['socialMediaToday', 'socialMediaExaminer', 'bufferBlog', 'hootsuiteBlogs'],\r\n      business: ['hubspotMarketing', 'contentMarketingInstitute', 'marketingProfs'],\r\n      tech: ['techCrunch', 'theVerge', 'wired'],\r\n      design: ['canvaDesignSchool', 'adobeBlog', 'creativeBloq'],\r\n    };\r\n\r\n    const categoryArticles = trendingData.articles.filter(article =>\r\n      categoryFeeds[category].includes(article.source)\r\n    );\r\n\r\n    const keywords: string[] = [];\r\n    categoryArticles.forEach(article => keywords.push(...article.keywords));\r\n\r\n    return this.getTopItems(keywords, 20);\r\n  }\r\n\r\n  /**\r\n   * 🚀 ENHANCED: Generate comprehensive hashtag analytics from RSS data\r\n   */\r\n  private generateHashtagAnalytics(articles: RSSArticle[], keywords: string[]): TrendingData['hashtagAnalytics'] {\r\n    const hashtagFrequency = new Map<string, number>();\r\n    const hashtagsByCategory = new Map<string, Set<string>>();\r\n    const hashtagsByLocation = new Map<string, Set<string>>();\r\n    const hashtagsByIndustry = new Map<string, Set<string>>();\r\n    const hashtagSentiment = new Map<string, 'positive' | 'neutral' | 'negative'>();\r\n\r\n    // Process articles for hashtag analytics\r\n    articles.forEach(article => {\r\n      const content = `${article.title} ${article.description}`.toLowerCase();\r\n\r\n      // Extract hashtags from content\r\n      const hashtags = content.match(/#[a-zA-Z0-9_]+/g) || [];\r\n\r\n      // Generate hashtags from keywords\r\n      const keywordHashtags = keywords\r\n        .filter(keyword => content.includes(keyword.toLowerCase()))\r\n        .map(keyword => `#${keyword.replace(/\\s+/g, '')}`);\r\n\r\n      const allHashtags = [...hashtags, ...keywordHashtags];\r\n\r\n      allHashtags.forEach(hashtag => {\r\n        // Count frequency\r\n        hashtagFrequency.set(hashtag, (hashtagFrequency.get(hashtag) || 0) + 1);\r\n\r\n        // Categorize by article category\r\n        if (article.category) {\r\n          if (!hashtagsByCategory.has(article.category)) {\r\n            hashtagsByCategory.set(article.category, new Set());\r\n          }\r\n          hashtagsByCategory.get(article.category)!.add(hashtag);\r\n        }\r\n\r\n        // Categorize by source (as industry proxy)\r\n        if (article.source) {\r\n          const industry = this.mapSourceToIndustry(article.source);\r\n          if (!hashtagsByIndustry.has(industry)) {\r\n            hashtagsByIndustry.set(industry, new Set());\r\n          }\r\n          hashtagsByIndustry.get(industry)!.add(hashtag);\r\n        }\r\n\r\n        // Basic sentiment analysis\r\n        if (!hashtagSentiment.has(hashtag)) {\r\n          hashtagSentiment.set(hashtag, this.analyzeSentiment(content));\r\n        }\r\n      });\r\n    });\r\n\r\n    // Calculate trending hashtags with momentum\r\n    const trending = Array.from(hashtagFrequency.entries())\r\n      .sort(([, a], [, b]) => b - a)\r\n      .slice(0, 20)\r\n      .map(([hashtag, frequency]) => ({\r\n        hashtag,\r\n        frequency,\r\n        momentum: this.calculateMomentum(hashtag, articles) as 'rising' | 'stable' | 'declining'\r\n      }));\r\n\r\n    // Convert sets to arrays for the final result\r\n    const byCategory: Record<string, string[]> = {};\r\n    hashtagsByCategory.forEach((hashtags, category) => {\r\n      byCategory[category] = Array.from(hashtags).slice(0, 10);\r\n    });\r\n\r\n    const byLocation: Record<string, string[]> = {};\r\n    // Location analysis would require more sophisticated processing\r\n    // For now, we'll use a simple approach\r\n    byLocation['global'] = trending.slice(0, 10).map(t => t.hashtag);\r\n\r\n    const byIndustry: Record<string, string[]> = {};\r\n    hashtagsByIndustry.forEach((hashtags, industry) => {\r\n      byIndustry[industry] = Array.from(hashtags).slice(0, 8);\r\n    });\r\n\r\n    const sentiment: Record<string, 'positive' | 'neutral' | 'negative'> = {};\r\n    hashtagSentiment.forEach((sent, hashtag) => {\r\n      sentiment[hashtag] = sent;\r\n    });\r\n\r\n    return {\r\n      trending,\r\n      byCategory,\r\n      byLocation,\r\n      byIndustry,\r\n      sentiment\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Map RSS source to industry category\r\n   */\r\n  private mapSourceToIndustry(source: string): string {\r\n    const industryMap: Record<string, string> = {\r\n      'socialMediaToday': 'social_media',\r\n      'socialMediaExaminer': 'social_media',\r\n      'bufferBlog': 'social_media',\r\n      'hootsuiteBlogs': 'social_media',\r\n      'hubspotMarketing': 'marketing',\r\n      'contentMarketingInstitute': 'marketing',\r\n      'marketingProfs': 'marketing',\r\n      'techCrunch': 'technology',\r\n      'theVerge': 'technology',\r\n      'wired': 'technology',\r\n      'canvaDesignSchool': 'design',\r\n      'adobeBlog': 'design',\r\n      'creativeBloq': 'design'\r\n    };\r\n\r\n    return industryMap[source] || 'general';\r\n  }\r\n\r\n  /**\r\n   * Calculate hashtag momentum based on recent usage\r\n   */\r\n  private calculateMomentum(hashtag: string, articles: RSSArticle[]): string {\r\n    const now = Date.now();\r\n    const recentArticles = articles.filter(article => {\r\n      const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);\r\n      return hoursSincePublished <= 24;\r\n    });\r\n\r\n    const oldArticles = articles.filter(article => {\r\n      const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);\r\n      return hoursSincePublished > 24 && hoursSincePublished <= 72;\r\n    });\r\n\r\n    const recentMentions = recentArticles.filter(article =>\r\n      `${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())\r\n    ).length;\r\n\r\n    const oldMentions = oldArticles.filter(article =>\r\n      `${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())\r\n    ).length;\r\n\r\n    if (recentMentions > oldMentions * 1.5) return 'rising';\r\n    if (recentMentions < oldMentions * 0.5) return 'declining';\r\n    return 'stable';\r\n  }\r\n\r\n  /**\r\n   * Basic sentiment analysis for hashtags\r\n   */\r\n  private analyzeSentiment(content: string): 'positive' | 'neutral' | 'negative' {\r\n    const positiveWords = [\r\n      'amazing', 'awesome', 'great', 'excellent', 'fantastic', 'wonderful',\r\n      'love', 'best', 'perfect', 'incredible', 'outstanding', 'brilliant',\r\n      'success', 'win', 'achieve', 'growth', 'improve', 'boost'\r\n    ];\r\n\r\n    const negativeWords = [\r\n      'bad', 'terrible', 'awful', 'horrible', 'worst', 'hate',\r\n      'fail', 'problem', 'issue', 'crisis', 'decline', 'drop',\r\n      'loss', 'damage', 'risk', 'threat', 'concern', 'worry'\r\n    ];\r\n\r\n    const words = content.toLowerCase().split(/\\s+/);\r\n\r\n    const positiveCount = words.filter(word => positiveWords.includes(word)).length;\r\n    const negativeCount = words.filter(word => negativeWords.includes(word)).length;\r\n\r\n    if (positiveCount > negativeCount) return 'positive';\r\n    if (negativeCount > positiveCount) return 'negative';\r\n    return 'neutral';\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const rssService = new RSSFeedService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AA8BO,MAAM;IACH,QAAgE,IAAI,MAAM;IACjE,eAAe,SAAS,QAAQ,GAAG,CAAC,kBAAkB,IAAI,UAAU,KAAK;IAEzE,WAAW;QAC1B,kCAAkC;QAClC,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB;QACpD,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;QAC1D,YAAY,QAAQ,GAAG,CAAC,eAAe;QACvC,gBAAgB,QAAQ,GAAG,CAAC,kBAAkB;QAC9C,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAC3C,WAAW,QAAQ,GAAG,CAAC,cAAc;QAErC,yBAAyB;QACzB,oBAAoB,QAAQ,GAAG,CAAC,wBAAwB;QACxD,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QAEjD,uBAAuB;QACvB,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,2BAA2B,QAAQ,GAAG,CAAC,+BAA+B;QACtE,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,eAAe,QAAQ,GAAG,CAAC,mBAAmB;QAE9C,gBAAgB;QAChB,YAAY,QAAQ,GAAG,CAAC,cAAc;QACtC,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,UAAU,QAAQ,GAAG,CAAC,aAAa;QACnC,OAAO,QAAQ,GAAG,CAAC,SAAS;QAE5B,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAE/C,mBAAmB;QACnB,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QACjD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QAErD,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB;QACtD,WAAW,QAAQ,GAAG,CAAC,cAAc;QACrC,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAE3C,oBAAoB;QACpB,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;IACjD,EAAE;IAEF;;GAEC,GACD,MAAc,aAAa,GAAW,EAAE,UAAkB,EAAyB;QACjF,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC/D,OAAO,OAAO,IAAI;YACpB;YAGA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,MAAM,WAAyB,EAAE;YACjC,MAAM,QAAQ,OAAO,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS,EAAE;YAExE,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI;YAEtE,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,aAAc;gBAC9C,MAAM,UAAsB;oBAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;oBAClC,aAAa,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAC9D,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE;oBAC3C,SAAS,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK,KAAK,GAAG;oBAC9E,UAAU,IAAI,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACxC,UAAU,IAAI,CAAC,eAAe,CAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAExF,QAAQ;gBACV;gBAEA,SAAS,IAAI,CAAC;YAChB;YAEA,oBAAoB;YACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,MAAM;gBAAU,WAAW,KAAK,GAAG;YAAG;YAE5D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAU,EAAU;QACtC,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,OAAO,UAAU,UAAU,OAAO;QACtC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;YAC5C,OAAO,OAAO,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI;QACjE;QACA,IAAI,OAAO,UAAU,YAAY,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC;QAExD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBAAgB,IAAY,EAAY;QAC9C,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,sCAAsC;QACtC,MAAM,YAAY,KACf,OAAO,CAAC,YAAY,IACpB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,kEAAkE;QAClE,MAAM,YAAY,IAAI,IAAI;YACxB;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;SACvP;QAED,MAAM,QAAQ,UACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,OAClD,KAAK,CAAC,GAAG,KAAK,uCAAuC;QAExD,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,oBAAoB;IACzD;IAEA;;GAEC,GACD,MAAa,kBAAyC;QAEpD,MAAM,cAA4B,EAAE;QACpC,MAAM,gBAAyC,EAAE;QAEjD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,YAAY,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAG;YAC7D,IAAI,KAAK;gBACP,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;YAC5C;QACF;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;QAEzC,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,YAAY,IAAI,IAAI,OAAO,KAAK;YAClC;QACF;QAEA,mDAAmD;QACnD,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO,KAAK,EAAE,OAAO,CAAC,OAAO;QAElE,uCAAuC;QACvC,MAAM,cAAwB,EAAE;QAChC,MAAM,YAAsB,EAAE;QAC9B,MAAM,YAAsB,EAAE;QAE9B,YAAY,OAAO,CAAC,CAAA;YAClB,YAAY,IAAI,IAAI,QAAQ,QAAQ;YACpC,IAAI,QAAQ,KAAK,EAAE,UAAU,IAAI,CAAC,QAAQ,KAAK;YAC/C,IAAI,QAAQ,QAAQ,EAAE,UAAU,IAAI,CAAC,QAAQ,QAAQ;QACvD;QAEA,oCAAoC;QACpC,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;QACpD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAChD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAGhD,0CAA0C;QAC1C,MAAM,mBAAmB,IAAI,CAAC,wBAAwB,CAAC,aAAa;QAEpE,OAAO;YACL,UAAU;YACV,UAAU,cAAc,GAAG,CAAC,CAAA,UAAW,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK;YACxE,QAAQ;YACR,QAAQ;YACR,UAAU,YAAY,KAAK,CAAC,GAAG;YAC/B,aAAa,IAAI;YACjB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAe,EAAE,KAAa,EAAY;QAC5D,MAAM,SAAS,IAAI;QAEnB,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,aAAa,KAAK,WAAW,GAAG,IAAI;YAC1C,IAAI,WAAW,MAAM,IAAI,GAAG;gBAC1B,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI;YACzD;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,OAAO,OAAO,IAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IACrB;IAEA;;GAEC,GACD,MAAa,8BAA8B,QAAmD,EAAqB;QACjH,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;QAE/C,MAAM,gBAAgB;YACpB,QAAQ;gBAAC;gBAAoB;gBAAuB;gBAAc;aAAiB;YACnF,UAAU;gBAAC;gBAAoB;gBAA6B;aAAiB;YAC7E,MAAM;gBAAC;gBAAc;gBAAY;aAAQ;YACzC,QAAQ;gBAAC;gBAAqB;gBAAa;aAAe;QAC5D;QAEA,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UACpD,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAGjD,MAAM,WAAqB,EAAE;QAC7B,iBAAiB,OAAO,CAAC,CAAA,UAAW,SAAS,IAAI,IAAI,QAAQ,QAAQ;QAErE,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;IACpC;IAEA;;GAEC,GACD,AAAQ,yBAAyB,QAAsB,EAAE,QAAkB,EAAoC;QAC7G,MAAM,mBAAmB,IAAI;QAC7B,MAAM,qBAAqB,IAAI;QAC/B,MAAM,qBAAqB,IAAI;QAC/B,MAAM,qBAAqB,IAAI;QAC/B,MAAM,mBAAmB,IAAI;QAE7B,yCAAyC;QACzC,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW;YAErE,gCAAgC;YAChC,MAAM,WAAW,QAAQ,KAAK,CAAC,sBAAsB,EAAE;YAEvD,kCAAkC;YAClC,MAAM,kBAAkB,SACrB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,QAAQ,WAAW,KACtD,GAAG,CAAC,CAAA,UAAW,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK;YAEnD,MAAM,cAAc;mBAAI;mBAAa;aAAgB;YAErD,YAAY,OAAO,CAAC,CAAA;gBAClB,kBAAkB;gBAClB,iBAAiB,GAAG,CAAC,SAAS,CAAC,iBAAiB,GAAG,CAAC,YAAY,CAAC,IAAI;gBAErE,iCAAiC;gBACjC,IAAI,QAAQ,QAAQ,EAAE;oBACpB,IAAI,CAAC,mBAAmB,GAAG,CAAC,QAAQ,QAAQ,GAAG;wBAC7C,mBAAmB,GAAG,CAAC,QAAQ,QAAQ,EAAE,IAAI;oBAC/C;oBACA,mBAAmB,GAAG,CAAC,QAAQ,QAAQ,EAAG,GAAG,CAAC;gBAChD;gBAEA,2CAA2C;gBAC3C,IAAI,QAAQ,MAAM,EAAE;oBAClB,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,QAAQ,MAAM;oBACxD,IAAI,CAAC,mBAAmB,GAAG,CAAC,WAAW;wBACrC,mBAAmB,GAAG,CAAC,UAAU,IAAI;oBACvC;oBACA,mBAAmB,GAAG,CAAC,UAAW,GAAG,CAAC;gBACxC;gBAEA,2BAA2B;gBAC3B,IAAI,CAAC,iBAAiB,GAAG,CAAC,UAAU;oBAClC,iBAAiB,GAAG,CAAC,SAAS,IAAI,CAAC,gBAAgB,CAAC;gBACtD;YACF;QACF;QAEA,4CAA4C;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,OAAO,IACjD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,GAC3B,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,CAAC,SAAS,UAAU,GAAK,CAAC;gBAC9B;gBACA;gBACA,UAAU,IAAI,CAAC,iBAAiB,CAAC,SAAS;YAC5C,CAAC;QAEH,8CAA8C;QAC9C,MAAM,aAAuC,CAAC;QAC9C,mBAAmB,OAAO,CAAC,CAAC,UAAU;YACpC,UAAU,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG;QACvD;QAEA,MAAM,aAAuC,CAAC;QAC9C,gEAAgE;QAChE,uCAAuC;QACvC,UAAU,CAAC,SAAS,GAAG,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAE/D,MAAM,aAAuC,CAAC;QAC9C,mBAAmB,OAAO,CAAC,CAAC,UAAU;YACpC,UAAU,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG;QACvD;QAEA,MAAM,YAAiE,CAAC;QACxE,iBAAiB,OAAO,CAAC,CAAC,MAAM;YAC9B,SAAS,CAAC,QAAQ,GAAG;QACvB;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,cAAsC;YAC1C,oBAAoB;YACpB,uBAAuB;YACvB,cAAc;YACd,kBAAkB;YAClB,oBAAoB;YACpB,6BAA6B;YAC7B,kBAAkB;YAClB,cAAc;YACd,YAAY;YACZ,SAAS;YACT,qBAAqB;YACrB,aAAa;YACb,gBAAgB;QAClB;QAEA,OAAO,WAAW,CAAC,OAAO,IAAI;IAChC;IAEA;;GAEC,GACD,AAAQ,kBAAkB,OAAe,EAAE,QAAsB,EAAU;QACzE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA;YACrC,MAAM,sBAAsB,CAAC,MAAM,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;YAC/E,OAAO,uBAAuB;QAChC;QAEA,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA;YAClC,MAAM,sBAAsB,CAAC,MAAM,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;YAC/E,OAAO,sBAAsB,MAAM,uBAAuB;QAC5D;QAEA,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,UAC3C,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,KACpF,MAAM;QAER,MAAM,cAAc,YAAY,MAAM,CAAC,CAAA,UACrC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,KACpF,MAAM;QAER,IAAI,iBAAiB,cAAc,KAAK,OAAO;QAC/C,IAAI,iBAAiB,cAAc,KAAK,OAAO;QAC/C,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAe,EAAuC;QAC7E,MAAM,gBAAgB;YACpB;YAAW;YAAW;YAAS;YAAa;YAAa;YACzD;YAAQ;YAAQ;YAAW;YAAc;YAAe;YACxD;YAAW;YAAO;YAAW;YAAU;YAAW;SACnD;QAED,MAAM,gBAAgB;YACpB;YAAO;YAAY;YAAS;YAAY;YAAS;YACjD;YAAQ;YAAW;YAAS;YAAU;YAAW;YACjD;YAAQ;YAAU;YAAQ;YAAU;YAAW;SAChD;QAED,MAAM,QAAQ,QAAQ,WAAW,GAAG,KAAK,CAAC;QAE1C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,OAAO,MAAM;QAC/E,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,OAAO,MAAM;QAE/E,IAAI,gBAAgB,eAAe,OAAO;QAC1C,IAAI,gBAAgB,eAAe,OAAO;QAC1C,OAAO;IACT;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/trending-content-enhancer.ts"], "sourcesContent": ["/**\r\n * Trending Content Enhancer\r\n * Integrates RSS feed data to enhance content generation with trending topics\r\n */\r\n\r\nimport { rssService, TrendingData } from '../services/rss-feed-service';\r\n\r\nexport interface TrendingEnhancement {\r\n  keywords: string[];\r\n  topics: string[];\r\n  hashtags: string[];\r\n  seasonalThemes: string[];\r\n  industryBuzz: string[];\r\n}\r\n\r\nexport interface ContentContext {\r\n  businessType?: string;\r\n  platform?: string;\r\n  location?: string;\r\n  targetAudience?: string;\r\n}\r\n\r\nexport class TrendingContentEnhancer {\r\n  private trendingCache: TrendingData | null = null;\r\n  private lastCacheUpdate: number = 0;\r\n  private readonly cacheTimeout = 30 * 60 * 1000; // 30 minutes\r\n\r\n  /**\r\n   * Get fresh trending data with caching\r\n   */\r\n  private async getTrendingData(): Promise<TrendingData> {\r\n    const now = Date.now();\r\n\r\n    if (this.trendingCache && (now - this.lastCacheUpdate) < this.cacheTimeout) {\r\n      return this.trendingCache;\r\n    }\r\n\r\n    this.trendingCache = await rssService.getTrendingData();\r\n    this.lastCacheUpdate = now;\r\n\r\n    return this.trendingCache;\r\n  }\r\n\r\n  /**\r\n   * Get trending enhancement data for content generation\r\n   */\r\n  public async getTrendingEnhancement(context: ContentContext = {}): Promise<TrendingEnhancement> {\r\n    try {\r\n      const trendingData = await this.getTrendingData();\r\n\r\n      // Filter and prioritize based on context\r\n      const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);\r\n      const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);\r\n\r\n      // Generate hashtags from trending keywords\r\n      const hashtags = this.generateHashtags(relevantKeywords, context);\r\n\r\n      // Extract seasonal themes\r\n      const seasonalThemes = this.extractSeasonalThemes(trendingData);\r\n\r\n      // Extract industry-specific buzz\r\n      const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);\r\n\r\n\r\n      return {\r\n        keywords: relevantKeywords.slice(0, 15),\r\n        topics: relevantTopics.slice(0, 10),\r\n        hashtags: hashtags.slice(0, 10),\r\n        seasonalThemes: seasonalThemes.slice(0, 5),\r\n        industryBuzz: industryBuzz.slice(0, 8),\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Return contextual fallback data based on current context\r\n      return this.generateContextualFallback(context);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Filter keywords based on context relevance\r\n   */\r\n  private filterKeywordsByContext(keywords: string[], context: ContentContext): string[] {\r\n    const platformKeywords = {\r\n      instagram: ['visual', 'photo', 'story', 'reel', 'aesthetic', 'lifestyle'],\r\n      facebook: ['community', 'share', 'connect', 'family', 'local', 'event'],\r\n      twitter: ['news', 'update', 'breaking', 'discussion', 'opinion', 'thread'],\r\n      linkedin: ['professional', 'business', 'career', 'industry', 'networking', 'leadership'],\r\n      tiktok: ['viral', 'trend', 'challenge', 'creative', 'fun', 'entertainment'],\r\n      pinterest: ['inspiration', 'ideas', 'diy', 'design', 'home', 'style'],\r\n    };\r\n\r\n    const businessKeywords = {\r\n      restaurant: ['food', 'dining', 'menu', 'chef', 'cuisine', 'taste', 'fresh'],\r\n      retail: ['shopping', 'sale', 'fashion', 'style', 'product', 'deal', 'new'],\r\n      fitness: ['health', 'workout', 'training', 'wellness', 'strength', 'motivation'],\r\n      beauty: ['skincare', 'makeup', 'beauty', 'glow', 'treatment', 'style'],\r\n      tech: ['innovation', 'digital', 'technology', 'software', 'app', 'solution'],\r\n      healthcare: ['health', 'wellness', 'care', 'treatment', 'medical', 'patient'],\r\n    };\r\n\r\n    let filtered = [...keywords];\r\n\r\n    // Boost platform-relevant keywords\r\n    if (context.platform && platformKeywords[context.platform as keyof typeof platformKeywords]) {\r\n      const platformBoost = platformKeywords[context.platform as keyof typeof platformKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = platformBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = platformBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    // Boost business-relevant keywords\r\n    if (context.businessType && businessKeywords[context.businessType as keyof typeof businessKeywords]) {\r\n      const businessBoost = businessKeywords[context.businessType as keyof typeof businessKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = businessBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = businessBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Filter topics based on context relevance\r\n   */\r\n  private filterTopicsByContext(topics: string[], context: ContentContext): string[] {\r\n    // Remove topics that are too generic or not suitable for social media\r\n    const filtered = topics.filter(topic => {\r\n      const lower = topic.toLowerCase();\r\n      return !lower.includes('error') &&\r\n        !lower.includes('404') &&\r\n        !lower.includes('page not found') &&\r\n        lower.length > 10 &&\r\n        lower.length < 100;\r\n    });\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Generate relevant hashtags from keywords\r\n   */\r\n  private generateHashtags(keywords: string[], context: ContentContext): string[] {\r\n    const hashtags: string[] = [];\r\n\r\n    // Convert keywords to hashtags\r\n    keywords.forEach(keyword => {\r\n      const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\r\n      if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {\r\n        hashtags.push(`#${cleanKeyword}`);\r\n      }\r\n    });\r\n\r\n    // Add platform-specific hashtags\r\n    const platformHashtags = {\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#picoftheday'],\r\n      facebook: ['#community', '#local', '#share', '#connect'],\r\n      twitter: ['#news', '#update', '#discussion', '#trending'],\r\n      linkedin: ['#professional', '#business', '#career', '#networking'],\r\n      tiktok: ['#fyp', '#viral', '#trending', '#foryou'],\r\n      pinterest: ['#inspiration', '#ideas', '#diy', '#style'],\r\n    };\r\n\r\n    if (context.platform && platformHashtags[context.platform as keyof typeof platformHashtags]) {\r\n      hashtags.push(...platformHashtags[context.platform as keyof typeof platformHashtags]);\r\n    }\r\n\r\n    // Remove duplicates and return\r\n    return Array.from(new Set(hashtags));\r\n  }\r\n\r\n  /**\r\n   * Extract seasonal themes from trending data\r\n   */\r\n  private extractSeasonalThemes(trendingData: TrendingData): string[] {\r\n    const currentMonth = new Date().getMonth();\r\n    const seasonalKeywords = {\r\n      0: ['new year', 'resolution', 'fresh start', 'winter'], // January\r\n      1: ['valentine', 'love', 'romance', 'winter'], // February\r\n      2: ['spring', 'march madness', 'renewal', 'growth'], // March\r\n      3: ['easter', 'spring', 'bloom', 'fresh'], // April\r\n      4: ['mother\\'s day', 'spring', 'flowers', 'celebration'], // May\r\n      5: ['summer', 'graduation', 'father\\'s day', 'vacation'], // June\r\n      6: ['summer', 'july 4th', 'independence', 'freedom'], // July\r\n      7: ['summer', 'vacation', 'back to school', 'preparation'], // August\r\n      8: ['back to school', 'fall', 'autumn', 'harvest'], // September\r\n      9: ['halloween', 'october', 'spooky', 'fall'], // October\r\n      10: ['thanksgiving', 'gratitude', 'family', 'harvest'], // November\r\n      11: ['christmas', 'holiday', 'winter', 'celebration'], // December\r\n    };\r\n\r\n    const currentSeasonalKeywords = seasonalKeywords[currentMonth as keyof typeof seasonalKeywords] || [];\r\n\r\n    const seasonalThemes = trendingData.keywords.filter(keyword =>\r\n      currentSeasonalKeywords.some(seasonal =>\r\n        keyword.toLowerCase().includes(seasonal.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return seasonalThemes;\r\n  }\r\n\r\n  /**\r\n   * Extract industry-specific buzz from trending data\r\n   */\r\n  private extractIndustryBuzz(trendingData: TrendingData, businessType?: string): string[] {\r\n    if (!businessType) return [];\r\n\r\n    const industryKeywords = {\r\n      restaurant: ['food', 'dining', 'chef', 'cuisine', 'recipe', 'restaurant', 'menu'],\r\n      retail: ['shopping', 'fashion', 'style', 'product', 'brand', 'sale', 'deal'],\r\n      fitness: ['fitness', 'workout', 'health', 'gym', 'training', 'wellness', 'exercise'],\r\n      beauty: ['beauty', 'skincare', 'makeup', 'cosmetics', 'treatment', 'spa'],\r\n      tech: ['technology', 'tech', 'digital', 'software', 'app', 'innovation', 'ai'],\r\n      healthcare: ['health', 'medical', 'healthcare', 'wellness', 'treatment', 'care'],\r\n    };\r\n\r\n    const relevantKeywords = industryKeywords[businessType as keyof typeof industryKeywords] || [];\r\n\r\n    const industryBuzz = trendingData.keywords.filter(keyword =>\r\n      relevantKeywords.some(industry =>\r\n        keyword.toLowerCase().includes(industry.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return industryBuzz;\r\n  }\r\n\r\n  /**\r\n   * Generate contextual fallback data without hardcoded placeholders\r\n   */\r\n  private generateContextualFallback(context: ContentContext): TrendingEnhancement {\r\n    const today = new Date();\r\n    const currentMonth = today.toLocaleDateString('en-US', { month: 'long' });\r\n    const currentDay = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n\r\n    // Generate contextual keywords based on business type and current date\r\n    const keywords: string[] = [];\r\n    if (context.businessType) {\r\n      keywords.push(`${context.businessType} services`, `${context.businessType} solutions`);\r\n    }\r\n    keywords.push(`${currentDay} motivation`, `${currentMonth} opportunities`);\r\n\r\n    // Generate contextual topics\r\n    const topics: string[] = [];\r\n    if (context.businessType) {\r\n      topics.push(`${context.businessType} industry insights`);\r\n    }\r\n    if (context.location) {\r\n      topics.push(`${context.location} business community`);\r\n    }\r\n    topics.push(`${currentMonth} business trends`);\r\n\r\n    // Generate contextual hashtags\r\n    const hashtags: string[] = [];\r\n    if (context.businessType) {\r\n      hashtags.push(`#${context.businessType.replace(/\\s+/g, '')}Business`);\r\n    }\r\n    if (context.location) {\r\n      const locationParts = context.location.split(',').map(part => part.trim());\r\n      locationParts.forEach(part => {\r\n        if (part.length > 2) {\r\n          hashtags.push(`#${part.replace(/\\s+/g, '')}`);\r\n        }\r\n      });\r\n    }\r\n    hashtags.push(`#${currentDay}Motivation`, `#${currentMonth}${today.getFullYear()}`);\r\n\r\n    // Generate seasonal themes\r\n    const seasonalThemes: string[] = [];\r\n    const month = today.getMonth();\r\n    if (month >= 2 && month <= 4) seasonalThemes.push('Spring renewal', 'Fresh starts');\r\n    else if (month >= 5 && month <= 7) seasonalThemes.push('Summer energy', 'Outdoor activities');\r\n    else if (month >= 8 && month <= 10) seasonalThemes.push('Autumn preparation', 'Harvest season');\r\n    else seasonalThemes.push('Winter planning', 'Year-end reflection');\r\n\r\n    return {\r\n      keywords: keywords.slice(0, 15),\r\n      topics: topics.slice(0, 10),\r\n      hashtags: hashtags.slice(0, 10),\r\n      seasonalThemes: seasonalThemes.slice(0, 5),\r\n      industryBuzz: context.businessType ? [`${context.businessType} innovation`] : [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get trending prompt enhancement for AI content generation\r\n   */\r\n  public async getTrendingPromptEnhancement(context: ContentContext = {}): Promise<string> {\r\n    const enhancement = await this.getTrendingEnhancement(context);\r\n\r\n    const promptParts: string[] = [];\r\n\r\n    if (enhancement.keywords.length > 0) {\r\n      promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.seasonalThemes.length > 0) {\r\n      promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.industryBuzz.length > 0) {\r\n      promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.hashtags.length > 0) {\r\n      promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);\r\n    }\r\n\r\n    return promptParts.join('\\n');\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const trendingEnhancer = new TrendingContentEnhancer();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAiBO,MAAM;IACH,gBAAqC,KAAK;IAC1C,kBAA0B,EAAE;IACnB,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAc,kBAAyC;QACrD,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,IAAI,CAAC,aAAa,IAAI,AAAC,MAAM,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,YAAY,EAAE;YAC1E,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,IAAI,CAAC,aAAa,GAAG,MAAM,2IAAA,CAAA,aAAU,CAAC,eAAe;QACrD,IAAI,CAAC,eAAe,GAAG;QAEvB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA;;GAEC,GACD,MAAa,uBAAuB,UAA0B,CAAC,CAAC,EAAgC;QAC9F,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;YAE/C,yCAAyC;YACzC,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC,aAAa,QAAQ,EAAE;YAC7E,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,aAAa,MAAM,EAAE;YAEvE,2CAA2C;YAC3C,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;YAEzD,0BAA0B;YAC1B,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAElD,iCAAiC;YACjC,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,cAAc,QAAQ,YAAY;YAGhF,OAAO;gBACL,UAAU,iBAAiB,KAAK,CAAC,GAAG;gBACpC,QAAQ,eAAe,KAAK,CAAC,GAAG;gBAChC,UAAU,SAAS,KAAK,CAAC,GAAG;gBAC5B,gBAAgB,eAAe,KAAK,CAAC,GAAG;gBACxC,cAAc,aAAa,KAAK,CAAC,GAAG;YACtC;QAEF,EAAE,OAAO,OAAO;YAEd,2DAA2D;YAC3D,OAAO,IAAI,CAAC,0BAA0B,CAAC;QACzC;IACF;IAEA;;GAEC,GACD,AAAQ,wBAAwB,QAAkB,EAAE,OAAuB,EAAY;QACrF,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAU;gBAAS;gBAAS;gBAAQ;gBAAa;aAAY;YACzE,UAAU;gBAAC;gBAAa;gBAAS;gBAAW;gBAAU;gBAAS;aAAQ;YACvE,SAAS;gBAAC;gBAAQ;gBAAU;gBAAY;gBAAc;gBAAW;aAAS;YAC1E,UAAU;gBAAC;gBAAgB;gBAAY;gBAAU;gBAAY;gBAAc;aAAa;YACxF,QAAQ;gBAAC;gBAAS;gBAAS;gBAAa;gBAAY;gBAAO;aAAgB;YAC3E,WAAW;gBAAC;gBAAe;gBAAS;gBAAO;gBAAU;gBAAQ;aAAQ;QACvE;QAEA,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAQ;gBAAW;gBAAS;aAAQ;YAC3E,QAAQ;gBAAC;gBAAY;gBAAQ;gBAAW;gBAAS;gBAAW;gBAAQ;aAAM;YAC1E,SAAS;gBAAC;gBAAU;gBAAW;gBAAY;gBAAY;gBAAY;aAAa;YAChF,QAAQ;gBAAC;gBAAY;gBAAU;gBAAU;gBAAQ;gBAAa;aAAQ;YACtE,MAAM;gBAAC;gBAAc;gBAAW;gBAAc;gBAAY;gBAAO;aAAW;YAC5E,YAAY;gBAAC;gBAAU;gBAAY;gBAAQ;gBAAa;gBAAW;aAAU;QAC/E;QAEA,IAAI,WAAW;eAAI;SAAS;QAE5B,mCAAmC;QACnC,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;YACzF,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,mCAAmC;QACnC,IAAI,QAAQ,YAAY,IAAI,gBAAgB,CAAC,QAAQ,YAAY,CAAkC,EAAE;YACnG,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,YAAY,CAAkC;YAC7F,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAgB,EAAE,OAAuB,EAAY;QACjF,sEAAsE;QACtE,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA;YAC7B,MAAM,QAAQ,MAAM,WAAW;YAC/B,OAAO,CAAC,MAAM,QAAQ,CAAC,YACrB,CAAC,MAAM,QAAQ,CAAC,UAChB,CAAC,MAAM,QAAQ,CAAC,qBAChB,MAAM,MAAM,GAAG,MACf,MAAM,MAAM,GAAG;QACnB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAkB,EAAE,OAAuB,EAAY;QAC9E,MAAM,WAAqB,EAAE;QAE7B,+BAA+B;QAC/B,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,eAAe,QAAQ,OAAO,CAAC,iBAAiB,IAAI,WAAW;YACrE,IAAI,aAAa,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI;gBACzD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;YAClC;QACF;QAEA,iCAAiC;QACjC,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;aAAe;YAC1E,UAAU;gBAAC;gBAAc;gBAAU;gBAAU;aAAW;YACxD,SAAS;gBAAC;gBAAS;gBAAW;gBAAe;aAAY;YACzD,UAAU;gBAAC;gBAAiB;gBAAa;gBAAW;aAAc;YAClE,QAAQ;gBAAC;gBAAQ;gBAAU;gBAAa;aAAU;YAClD,WAAW;gBAAC;gBAAgB;gBAAU;gBAAQ;aAAS;QACzD;QAEA,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,SAAS,IAAI,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;QACtF;QAEA,+BAA+B;QAC/B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;IAC5B;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAA0B,EAAY;QAClE,MAAM,eAAe,IAAI,OAAO,QAAQ;QACxC,MAAM,mBAAmB;YACvB,GAAG;gBAAC;gBAAY;gBAAc;gBAAe;aAAS;YACtD,GAAG;gBAAC;gBAAa;gBAAQ;gBAAW;aAAS;YAC7C,GAAG;gBAAC;gBAAU;gBAAiB;gBAAW;aAAS;YACnD,GAAG;gBAAC;gBAAU;gBAAU;gBAAS;aAAQ;YACzC,GAAG;gBAAC;gBAAiB;gBAAU;gBAAW;aAAc;YACxD,GAAG;gBAAC;gBAAU;gBAAc;gBAAiB;aAAW;YACxD,GAAG;gBAAC;gBAAU;gBAAY;gBAAgB;aAAU;YACpD,GAAG;gBAAC;gBAAU;gBAAY;gBAAkB;aAAc;YAC1D,GAAG;gBAAC;gBAAkB;gBAAQ;gBAAU;aAAU;YAClD,GAAG;gBAAC;gBAAa;gBAAW;gBAAU;aAAO;YAC7C,IAAI;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;YACtD,IAAI;gBAAC;gBAAa;gBAAW;gBAAU;aAAc;QACvD;QAEA,MAAM,0BAA0B,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAErG,MAAM,iBAAiB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAClD,wBAAwB,IAAI,CAAC,CAAA,WAC3B,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAA0B,EAAE,YAAqB,EAAY;QACvF,IAAI,CAAC,cAAc,OAAO,EAAE;QAE5B,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAW;gBAAU;gBAAc;aAAO;YACjF,QAAQ;gBAAC;gBAAY;gBAAW;gBAAS;gBAAW;gBAAS;gBAAQ;aAAO;YAC5E,SAAS;gBAAC;gBAAW;gBAAW;gBAAU;gBAAO;gBAAY;gBAAY;aAAW;YACpF,QAAQ;gBAAC;gBAAU;gBAAY;gBAAU;gBAAa;gBAAa;aAAM;YACzE,MAAM;gBAAC;gBAAc;gBAAQ;gBAAW;gBAAY;gBAAO;gBAAc;aAAK;YAC9E,YAAY;gBAAC;gBAAU;gBAAW;gBAAc;gBAAY;gBAAa;aAAO;QAClF;QAEA,MAAM,mBAAmB,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAE9F,MAAM,eAAe,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAChD,iBAAiB,IAAI,CAAC,CAAA,WACpB,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAuB,EAAuB;QAC/E,MAAM,QAAQ,IAAI;QAClB,MAAM,eAAe,MAAM,kBAAkB,CAAC,SAAS;YAAE,OAAO;QAAO;QACvE,MAAM,aAAa,MAAM,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QAEvE,uEAAuE;QACvE,MAAM,WAAqB,EAAE;QAC7B,IAAI,QAAQ,YAAY,EAAE;YACxB,SAAS,IAAI,CAAC,GAAG,QAAQ,YAAY,CAAC,SAAS,CAAC,EAAE,GAAG,QAAQ,YAAY,CAAC,UAAU,CAAC;QACvF;QACA,SAAS,IAAI,CAAC,GAAG,WAAW,WAAW,CAAC,EAAE,GAAG,aAAa,cAAc,CAAC;QAEzE,6BAA6B;QAC7B,MAAM,SAAmB,EAAE;QAC3B,IAAI,QAAQ,YAAY,EAAE;YACxB,OAAO,IAAI,CAAC,GAAG,QAAQ,YAAY,CAAC,kBAAkB,CAAC;QACzD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,OAAO,IAAI,CAAC,GAAG,QAAQ,QAAQ,CAAC,mBAAmB,CAAC;QACtD;QACA,OAAO,IAAI,CAAC,GAAG,aAAa,gBAAgB,CAAC;QAE7C,+BAA+B;QAC/B,MAAM,WAAqB,EAAE;QAC7B,IAAI,QAAQ,YAAY,EAAE;YACxB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;QACtE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YACvE,cAAc,OAAO,CAAC,CAAA;gBACpB,IAAI,KAAK,MAAM,GAAG,GAAG;oBACnB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,KAAK;gBAC9C;YACF;QACF;QACA,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,MAAM,WAAW,IAAI;QAElF,2BAA2B;QAC3B,MAAM,iBAA2B,EAAE;QACnC,MAAM,QAAQ,MAAM,QAAQ;QAC5B,IAAI,SAAS,KAAK,SAAS,GAAG,eAAe,IAAI,CAAC,kBAAkB;aAC/D,IAAI,SAAS,KAAK,SAAS,GAAG,eAAe,IAAI,CAAC,iBAAiB;aACnE,IAAI,SAAS,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,sBAAsB;aACzE,eAAe,IAAI,CAAC,mBAAmB;QAE5C,OAAO;YACL,UAAU,SAAS,KAAK,CAAC,GAAG;YAC5B,QAAQ,OAAO,KAAK,CAAC,GAAG;YACxB,UAAU,SAAS,KAAK,CAAC,GAAG;YAC5B,gBAAgB,eAAe,KAAK,CAAC,GAAG;YACxC,cAAc,QAAQ,YAAY,GAAG;gBAAC,GAAG,QAAQ,YAAY,CAAC,WAAW,CAAC;aAAC,GAAG,EAAE;QAClF;IACF;IAEA;;GAEC,GACD,MAAa,6BAA6B,UAA0B,CAAC,CAAC,EAAmB;QACvF,MAAM,cAAc,MAAM,IAAI,CAAC,sBAAsB,CAAC;QAEtD,MAAM,cAAwB,EAAE;QAEhC,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,+BAA+B,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QAClG;QAEA,IAAI,YAAY,cAAc,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY,IAAI,CAAC,CAAC,yBAAyB,EAAE,YAAY,cAAc,CAAC,IAAI,CAAC,OAAO;QACtF;QAEA,IAAI,YAAY,YAAY,CAAC,MAAM,GAAG,GAAG;YACvC,YAAY,IAAI,CAAC,CAAC,0BAA0B,EAAE,YAAY,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACjG;QAEA,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,oBAAoB,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;QACtF;QAEA,OAAO,YAAY,IAAI,CAAC;IAC1B;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/advanced-trending-hashtag-analyzer.ts"], "sourcesContent": ["/**\r\n * Advanced Trending Hashtag Analyzer\r\n * Analyzes RSS feeds and trending data to extract the most relevant hashtags\r\n * with sophisticated contextual understanding and business relevance scoring\r\n */\r\n\r\nimport { rssService, TrendingData, RSSArticle } from '../services/rss-feed-service';\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\n\r\nexport interface HashtagAnalysis {\r\n  hashtag: string;\r\n  relevanceScore: number;\r\n  trendingScore: number;\r\n  businessRelevance: number;\r\n  platformOptimization: number;\r\n  locationRelevance: number;\r\n  engagementPotential: number;\r\n  sources: string[];\r\n  momentum: 'rising' | 'stable' | 'declining';\r\n  category: 'trending' | 'viral' | 'niche' | 'location' | 'business' | 'seasonal';\r\n}\r\n\r\nexport interface AdvancedHashtagStrategy {\r\n  topTrending: HashtagAnalysis[];\r\n  businessOptimized: HashtagAnalysis[];\r\n  locationSpecific: HashtagAnalysis[];\r\n  platformNative: HashtagAnalysis[];\r\n  emergingTrends: HashtagAnalysis[];\r\n  finalRecommendations: string[];\r\n}\r\n\r\nexport interface AnalysisContext {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  services?: string;\r\n  targetAudience?: string;\r\n  postContent?: string;\r\n  industry?: string;\r\n}\r\n\r\nexport class AdvancedTrendingHashtagAnalyzer {\r\n  private cache: Map<string, { data: AdvancedHashtagStrategy; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = 15 * 60 * 1000; // 15 minutes\r\n\r\n  /**\r\n   * Analyze trending data and generate advanced hashtag strategy\r\n   */\r\n  public async analyzeHashtagTrends(context: AnalysisContext): Promise<AdvancedHashtagStrategy> {\r\n    const cacheKey = this.generateCacheKey(context);\r\n    const cached = this.cache.get(cacheKey);\r\n    \r\n    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n      return cached.data;\r\n    }\r\n\r\n    try {\r\n      // Get comprehensive trending data\r\n      const [trendingData, enhancementData] = await Promise.all([\r\n        rssService.getTrendingData(),\r\n        trendingEnhancer.getTrendingEnhancement({\r\n          businessType: context.businessType,\r\n          location: context.location,\r\n          platform: context.platform,\r\n          targetAudience: context.targetAudience\r\n        })\r\n      ]);\r\n\r\n      // Extract and analyze hashtags from multiple sources\r\n      const hashtagAnalyses = await this.extractAndAnalyzeHashtags(trendingData, enhancementData, context);\r\n\r\n      // Categorize hashtags by type and relevance\r\n      const strategy = this.categorizeHashtags(hashtagAnalyses, context);\r\n\r\n      // Cache the results\r\n      this.cache.set(cacheKey, { data: strategy, timestamp: Date.now() });\r\n\r\n      return strategy;\r\n\r\n    } catch (error) {\r\n      return this.getFallbackStrategy(context);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract hashtags from RSS articles and trending data\r\n   */\r\n  private async extractAndAnalyzeHashtags(\r\n    trendingData: TrendingData,\r\n    enhancementData: any,\r\n    context: AnalysisContext\r\n  ): Promise<HashtagAnalysis[]> {\r\n    const hashtagMap = new Map<string, HashtagAnalysis>();\r\n\r\n    // Process RSS articles for hashtag extraction\r\n    for (const article of trendingData.articles) {\r\n      const extractedHashtags = this.extractHashtagsFromArticle(article, context);\r\n      \r\n      for (const hashtag of extractedHashtags) {\r\n        if (hashtagMap.has(hashtag)) {\r\n          const existing = hashtagMap.get(hashtag)!;\r\n          existing.sources.push(article.source);\r\n          existing.trendingScore += 1;\r\n        } else {\r\n          hashtagMap.set(hashtag, {\r\n            hashtag,\r\n            relevanceScore: this.calculateRelevanceScore(hashtag, context),\r\n            trendingScore: 1,\r\n            businessRelevance: this.calculateBusinessRelevance(hashtag, context),\r\n            platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),\r\n            locationRelevance: this.calculateLocationRelevance(hashtag, context.location),\r\n            engagementPotential: this.calculateEngagementPotential(hashtag),\r\n            sources: [article.source],\r\n            momentum: this.calculateMomentum(hashtag, trendingData),\r\n            category: this.categorizeHashtag(hashtag, context)\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    // Add hashtags from trending enhancement data\r\n    for (const hashtag of enhancementData.hashtags) {\r\n      if (hashtagMap.has(hashtag)) {\r\n        const existing = hashtagMap.get(hashtag)!;\r\n        existing.trendingScore += 2; // Enhancement data gets higher weight\r\n        existing.sources.push('trending_enhancer');\r\n      } else {\r\n        hashtagMap.set(hashtag, {\r\n          hashtag,\r\n          relevanceScore: this.calculateRelevanceScore(hashtag, context),\r\n          trendingScore: 2,\r\n          businessRelevance: this.calculateBusinessRelevance(hashtag, context),\r\n          platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),\r\n          locationRelevance: this.calculateLocationRelevance(hashtag, context.location),\r\n          engagementPotential: this.calculateEngagementPotential(hashtag),\r\n          sources: ['trending_enhancer'],\r\n          momentum: 'rising',\r\n          category: this.categorizeHashtag(hashtag, context)\r\n        });\r\n      }\r\n    }\r\n\r\n    // Add business-specific trending hashtags\r\n    const businessHashtags = this.generateBusinessTrendingHashtags(context);\r\n    for (const hashtag of businessHashtags) {\r\n      if (!hashtagMap.has(hashtag)) {\r\n        hashtagMap.set(hashtag, {\r\n          hashtag,\r\n          relevanceScore: this.calculateRelevanceScore(hashtag, context),\r\n          trendingScore: 1,\r\n          businessRelevance: 10, // High business relevance\r\n          platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),\r\n          locationRelevance: this.calculateLocationRelevance(hashtag, context.location),\r\n          engagementPotential: this.calculateEngagementPotential(hashtag),\r\n          sources: ['business_generator'],\r\n          momentum: 'stable',\r\n          category: 'business'\r\n        });\r\n      }\r\n    }\r\n\r\n    return Array.from(hashtagMap.values());\r\n  }\r\n\r\n  /**\r\n   * Extract hashtags from article content\r\n   */\r\n  private extractHashtagsFromArticle(article: RSSArticle, context: AnalysisContext): string[] {\r\n    const hashtags: string[] = [];\r\n    const content = `${article.title} ${article.description}`.toLowerCase();\r\n\r\n    // Extract existing hashtags\r\n    const hashtagMatches = content.match(/#[a-zA-Z0-9_]+/g) || [];\r\n    hashtags.push(...hashtagMatches);\r\n\r\n    // Generate hashtags from keywords\r\n    const keywords = article.keywords || [];\r\n    for (const keyword of keywords) {\r\n      const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\r\n      if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {\r\n        hashtags.push(`#${cleanKeyword}`);\r\n      }\r\n    }\r\n\r\n    // Generate contextual hashtags based on content relevance\r\n    const contextualHashtags = this.generateContextualHashtags(content, context);\r\n    hashtags.push(...contextualHashtags);\r\n\r\n    return Array.from(new Set(hashtags));\r\n  }\r\n\r\n  /**\r\n   * Generate contextual hashtags based on content analysis\r\n   */\r\n  private generateContextualHashtags(content: string, context: AnalysisContext): string[] {\r\n    const hashtags: string[] = [];\r\n\r\n    // Business type relevance\r\n    if (content.includes(context.businessType.toLowerCase())) {\r\n      hashtags.push(`#${context.businessType.replace(/\\s+/g, '')}`);\r\n    }\r\n\r\n    // Location relevance\r\n    if (content.includes(context.location.toLowerCase())) {\r\n      hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);\r\n    }\r\n\r\n    // Industry keywords\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    for (const keyword of industryKeywords) {\r\n      if (content.includes(keyword.toLowerCase())) {\r\n        hashtags.push(`#${keyword.replace(/\\s+/g, '')}`);\r\n      }\r\n    }\r\n\r\n    return hashtags;\r\n  }\r\n\r\n  /**\r\n   * Calculate relevance score for a hashtag\r\n   */\r\n  private calculateRelevanceScore(hashtag: string, context: AnalysisContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase();\r\n\r\n    // Business type relevance\r\n    if (hashtagLower.includes(context.businessType.toLowerCase())) score += 5;\r\n    \r\n    // Location relevance\r\n    if (hashtagLower.includes(context.location.toLowerCase().replace(/\\s+/g, ''))) score += 4;\r\n    \r\n    // Service relevance\r\n    if (context.services) {\r\n      const services = context.services.toLowerCase().split(/[,\\s]+/);\r\n      for (const service of services) {\r\n        if (hashtagLower.includes(service)) score += 3;\r\n      }\r\n    }\r\n\r\n    // Platform optimization\r\n    score += this.calculatePlatformOptimization(hashtag, context.platform);\r\n\r\n    return Math.min(score, 10); // Cap at 10\r\n  }\r\n\r\n  /**\r\n   * Calculate business relevance score\r\n   */\r\n  private calculateBusinessRelevance(hashtag: string, context: AnalysisContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase();\r\n\r\n    // Direct business name match\r\n    if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\\s+/g, ''))) score += 10;\r\n    \r\n    // Business type match\r\n    if (hashtagLower.includes(context.businessType.toLowerCase())) score += 8;\r\n    \r\n    // Industry keywords\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    for (const keyword of industryKeywords) {\r\n      if (hashtagLower.includes(keyword.toLowerCase())) score += 6;\r\n    }\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate platform optimization score\r\n   */\r\n  private calculatePlatformOptimization(hashtag: string, platform: string): number {\r\n    const platformHashtags = {\r\n      instagram: ['instagood', 'photooftheday', 'instadaily', 'reels', 'igers'],\r\n      facebook: ['community', 'local', 'share', 'connect', 'family'],\r\n      twitter: ['news', 'update', 'discussion', 'trending', 'breaking'],\r\n      linkedin: ['professional', 'business', 'career', 'networking', 'industry'],\r\n      tiktok: ['fyp', 'viral', 'trending', 'foryou', 'dance'],\r\n      pinterest: ['inspiration', 'ideas', 'diy', 'style', 'design']\r\n    };\r\n\r\n    const platformSpecific = platformHashtags[platform.toLowerCase() as keyof typeof platformHashtags] || [];\r\n    const hashtagLower = hashtag.toLowerCase();\r\n\r\n    for (const specific of platformSpecific) {\r\n      if (hashtagLower.includes(specific)) return 8;\r\n    }\r\n\r\n    return 2; // Base score for any hashtag\r\n  }\r\n\r\n  /**\r\n   * Calculate location relevance score\r\n   */\r\n  private calculateLocationRelevance(hashtag: string, location: string): number {\r\n    const hashtagLower = hashtag.toLowerCase();\r\n    const locationLower = location.toLowerCase();\r\n\r\n    if (hashtagLower.includes(locationLower.replace(/\\s+/g, ''))) return 10;\r\n    if (hashtagLower.includes('local') || hashtagLower.includes('community')) return 6;\r\n    \r\n    // Check for city/state/country keywords\r\n    const locationParts = location.split(/[,\\s]+/);\r\n    for (const part of locationParts) {\r\n      if (part.length > 2 && hashtagLower.includes(part.toLowerCase())) return 8;\r\n    }\r\n\r\n    return 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate engagement potential score\r\n   */\r\n  private calculateEngagementPotential(hashtag: string): number {\r\n    const highEngagementKeywords = [\r\n      'viral', 'trending', 'amazing', 'incredible', 'awesome', 'beautiful',\r\n      'love', 'best', 'new', 'hot', 'popular', 'top', 'must', 'perfect'\r\n    ];\r\n\r\n    const hashtagLower = hashtag.toLowerCase();\r\n    \r\n    for (const keyword of highEngagementKeywords) {\r\n      if (hashtagLower.includes(keyword)) return 9;\r\n    }\r\n\r\n    // Length-based scoring (shorter hashtags often perform better)\r\n    if (hashtag.length <= 10) return 7;\r\n    if (hashtag.length <= 15) return 5;\r\n    return 3;\r\n  }\r\n\r\n  /**\r\n   * Calculate momentum for hashtag trends\r\n   */\r\n  private calculateMomentum(hashtag: string, trendingData: TrendingData): 'rising' | 'stable' | 'declining' {\r\n    // Simple momentum calculation based on recency and frequency\r\n    const recentArticles = trendingData.articles\r\n      .filter(article => {\r\n        const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);\r\n        return hoursSincePublished <= 24;\r\n      });\r\n\r\n    const hashtagMentions = recentArticles.filter(article => \r\n      `${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())\r\n    ).length;\r\n\r\n    if (hashtagMentions >= 3) return 'rising';\r\n    if (hashtagMentions >= 1) return 'stable';\r\n    return 'declining';\r\n  }\r\n\r\n  /**\r\n   * Categorize hashtag by type\r\n   */\r\n  private categorizeHashtag(hashtag: string, context: AnalysisContext): HashtagAnalysis['category'] {\r\n    const hashtagLower = hashtag.toLowerCase();\r\n\r\n    if (hashtagLower.includes('viral') || hashtagLower.includes('trending')) return 'viral';\r\n    if (hashtagLower.includes(context.businessType.toLowerCase())) return 'business';\r\n    if (hashtagLower.includes(context.location.toLowerCase().replace(/\\s+/g, ''))) return 'location';\r\n    if (hashtagLower.includes('season') || hashtagLower.includes('holiday')) return 'seasonal';\r\n    if (this.isNicheHashtag(hashtag, context)) return 'niche';\r\n    \r\n    return 'trending';\r\n  }\r\n\r\n  /**\r\n   * Check if hashtag is niche-specific\r\n   */\r\n  private isNicheHashtag(hashtag: string, context: AnalysisContext): boolean {\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    const hashtagLower = hashtag.toLowerCase();\r\n\r\n    return industryKeywords.some(keyword => \r\n      hashtagLower.includes(keyword.toLowerCase())\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get industry-specific keywords\r\n   */\r\n  private getIndustryKeywords(businessType: string): string[] {\r\n    const industryMap: Record<string, string[]> = {\r\n      restaurant: ['food', 'dining', 'cuisine', 'chef', 'menu', 'delicious', 'taste'],\r\n      retail: ['shopping', 'fashion', 'style', 'sale', 'deals', 'boutique'],\r\n      healthcare: ['health', 'wellness', 'medical', 'care', 'treatment', 'doctor'],\r\n      fitness: ['workout', 'gym', 'fitness', 'health', 'training', 'exercise'],\r\n      beauty: ['beauty', 'skincare', 'makeup', 'salon', 'spa', 'treatment'],\r\n      technology: ['tech', 'digital', 'innovation', 'software', 'app', 'online'],\r\n      education: ['learning', 'education', 'training', 'course', 'skill', 'knowledge'],\r\n      automotive: ['car', 'auto', 'vehicle', 'repair', 'service', 'maintenance'],\r\n      realestate: ['property', 'home', 'house', 'real estate', 'investment'],\r\n      legal: ['law', 'legal', 'attorney', 'lawyer', 'justice', 'rights']\r\n    };\r\n\r\n    return industryMap[businessType.toLowerCase()] || ['business', 'service', 'professional'];\r\n  }\r\n\r\n  /**\r\n   * Generate business-specific trending hashtags\r\n   */\r\n  private generateBusinessTrendingHashtags(context: AnalysisContext): string[] {\r\n    const hashtags: string[] = [];\r\n\r\n    // Business name hashtag\r\n    hashtags.push(`#${context.businessName.replace(/[^a-zA-Z0-9]/g, '')}`);\r\n    \r\n    // Business type hashtag\r\n    hashtags.push(`#${context.businessType.replace(/\\s+/g, '')}`);\r\n    \r\n    // Location hashtag\r\n    hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);\r\n    \r\n    // Industry-specific hashtags\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    hashtags.push(...industryKeywords.slice(0, 3).map(keyword => `#${keyword.replace(/\\s+/g, '')}`));\r\n\r\n    return hashtags;\r\n  }\r\n\r\n  /**\r\n   * Categorize hashtags into strategy groups\r\n   */\r\n  private categorizeHashtags(analyses: HashtagAnalysis[], context: AnalysisContext): AdvancedHashtagStrategy {\r\n    // Sort by overall relevance score\r\n    const sortedAnalyses = analyses.sort((a, b) => {\r\n      const scoreA = (a.relevanceScore + a.trendingScore + a.businessRelevance + a.engagementPotential) / 4;\r\n      const scoreB = (b.relevanceScore + b.trendingScore + b.businessRelevance + b.engagementPotential) / 4;\r\n      return scoreB - scoreA;\r\n    });\r\n\r\n    const topTrending = sortedAnalyses\r\n      .filter(a => a.category === 'trending' || a.category === 'viral')\r\n      .slice(0, 8);\r\n\r\n    const businessOptimized = sortedAnalyses\r\n      .filter(a => a.businessRelevance >= 6)\r\n      .slice(0, 6);\r\n\r\n    const locationSpecific = sortedAnalyses\r\n      .filter(a => a.locationRelevance >= 6)\r\n      .slice(0, 4);\r\n\r\n    const platformNative = sortedAnalyses\r\n      .filter(a => a.platformOptimization >= 6)\r\n      .slice(0, 5);\r\n\r\n    const emergingTrends = sortedAnalyses\r\n      .filter(a => a.momentum === 'rising')\r\n      .slice(0, 6);\r\n\r\n    // Create final recommendations (top 15 hashtags)\r\n    const finalRecommendations = this.createFinalRecommendations(\r\n      topTrending,\r\n      businessOptimized,\r\n      locationSpecific,\r\n      platformNative,\r\n      emergingTrends\r\n    );\r\n\r\n    return {\r\n      topTrending,\r\n      businessOptimized,\r\n      locationSpecific,\r\n      platformNative,\r\n      emergingTrends,\r\n      finalRecommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create final hashtag recommendations\r\n   */\r\n  private createFinalRecommendations(\r\n    topTrending: HashtagAnalysis[],\r\n    businessOptimized: HashtagAnalysis[],\r\n    locationSpecific: HashtagAnalysis[],\r\n    platformNative: HashtagAnalysis[],\r\n    emergingTrends: HashtagAnalysis[]\r\n  ): string[] {\r\n    const recommendations = new Set<string>();\r\n\r\n    // Add top performers from each category\r\n    topTrending.slice(0, 4).forEach(h => recommendations.add(h.hashtag));\r\n    businessOptimized.slice(0, 3).forEach(h => recommendations.add(h.hashtag));\r\n    locationSpecific.slice(0, 2).forEach(h => recommendations.add(h.hashtag));\r\n    platformNative.slice(0, 3).forEach(h => recommendations.add(h.hashtag));\r\n    emergingTrends.slice(0, 3).forEach(h => recommendations.add(h.hashtag));\r\n\r\n    return Array.from(recommendations).slice(0, 15);\r\n  }\r\n\r\n  /**\r\n   * Generate cache key for analysis context\r\n   */\r\n  private generateCacheKey(context: AnalysisContext): string {\r\n    return `${context.businessType}-${context.location}-${context.platform}-${context.businessName}`.toLowerCase();\r\n  }\r\n\r\n  /**\r\n   * Get fallback strategy when analysis fails\r\n   */\r\n  private getFallbackStrategy(context: AnalysisContext): AdvancedHashtagStrategy {\r\n    const fallbackHashtags = [\r\n      '#trending', '#viral', '#business', '#local', '#community',\r\n      `#${context.businessType.replace(/\\s+/g, '')}`,\r\n      `#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`,\r\n      '#quality', '#professional', '#service'\r\n    ];\r\n\r\n    const fallbackAnalyses: HashtagAnalysis[] = fallbackHashtags.map(hashtag => ({\r\n      hashtag,\r\n      relevanceScore: 5,\r\n      trendingScore: 3,\r\n      businessRelevance: 5,\r\n      platformOptimization: 4,\r\n      locationRelevance: 3,\r\n      engagementPotential: 5,\r\n      sources: ['fallback'],\r\n      momentum: 'stable' as const,\r\n      category: 'trending' as const\r\n    }));\r\n\r\n    return {\r\n      topTrending: fallbackAnalyses.slice(0, 4),\r\n      businessOptimized: fallbackAnalyses.slice(0, 3),\r\n      locationSpecific: fallbackAnalyses.slice(0, 2),\r\n      platformNative: fallbackAnalyses.slice(0, 3),\r\n      emergingTrends: fallbackAnalyses.slice(0, 3),\r\n      finalRecommendations: fallbackHashtags\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const advancedHashtagAnalyzer = new AdvancedTrendingHashtagAnalyzer();\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;;;AAmCO,MAAM;IACH,QAA2E,IAAI,MAAM;IAC5E,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAa,qBAAqB,OAAwB,EAAoC;QAC5F,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;QACvC,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;YAC/D,OAAO,OAAO,IAAI;QACpB;QAEA,IAAI;YACF,kCAAkC;YAClC,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxD,2IAAA,CAAA,aAAU,CAAC,eAAe;gBAC1B,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;oBACtC,cAAc,QAAQ,YAAY;oBAClC,UAAU,QAAQ,QAAQ;oBAC1B,UAAU,QAAQ,QAAQ;oBAC1B,gBAAgB,QAAQ,cAAc;gBACxC;aACD;YAED,qDAAqD;YACrD,MAAM,kBAAkB,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,iBAAiB;YAE5F,4CAA4C;YAC5C,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;YAE1D,oBAAoB;YACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;gBAAE,MAAM;gBAAU,WAAW,KAAK,GAAG;YAAG;YAEjE,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC;IACF;IAEA;;GAEC,GACD,MAAc,0BACZ,YAA0B,EAC1B,eAAoB,EACpB,OAAwB,EACI;QAC5B,MAAM,aAAa,IAAI;QAEvB,8CAA8C;QAC9C,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;YAC3C,MAAM,oBAAoB,IAAI,CAAC,0BAA0B,CAAC,SAAS;YAEnE,KAAK,MAAM,WAAW,kBAAmB;gBACvC,IAAI,WAAW,GAAG,CAAC,UAAU;oBAC3B,MAAM,WAAW,WAAW,GAAG,CAAC;oBAChC,SAAS,OAAO,CAAC,IAAI,CAAC,QAAQ,MAAM;oBACpC,SAAS,aAAa,IAAI;gBAC5B,OAAO;oBACL,WAAW,GAAG,CAAC,SAAS;wBACtB;wBACA,gBAAgB,IAAI,CAAC,uBAAuB,CAAC,SAAS;wBACtD,eAAe;wBACf,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;wBAC5D,sBAAsB,IAAI,CAAC,6BAA6B,CAAC,SAAS,QAAQ,QAAQ;wBAClF,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS,QAAQ,QAAQ;wBAC5E,qBAAqB,IAAI,CAAC,4BAA4B,CAAC;wBACvD,SAAS;4BAAC,QAAQ,MAAM;yBAAC;wBACzB,UAAU,IAAI,CAAC,iBAAiB,CAAC,SAAS;wBAC1C,UAAU,IAAI,CAAC,iBAAiB,CAAC,SAAS;oBAC5C;gBACF;YACF;QACF;QAEA,8CAA8C;QAC9C,KAAK,MAAM,WAAW,gBAAgB,QAAQ,CAAE;YAC9C,IAAI,WAAW,GAAG,CAAC,UAAU;gBAC3B,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,SAAS,aAAa,IAAI,GAAG,sCAAsC;gBACnE,SAAS,OAAO,CAAC,IAAI,CAAC;YACxB,OAAO;gBACL,WAAW,GAAG,CAAC,SAAS;oBACtB;oBACA,gBAAgB,IAAI,CAAC,uBAAuB,CAAC,SAAS;oBACtD,eAAe;oBACf,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;oBAC5D,sBAAsB,IAAI,CAAC,6BAA6B,CAAC,SAAS,QAAQ,QAAQ;oBAClF,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS,QAAQ,QAAQ;oBAC5E,qBAAqB,IAAI,CAAC,4BAA4B,CAAC;oBACvD,SAAS;wBAAC;qBAAoB;oBAC9B,UAAU;oBACV,UAAU,IAAI,CAAC,iBAAiB,CAAC,SAAS;gBAC5C;YACF;QACF;QAEA,0CAA0C;QAC1C,MAAM,mBAAmB,IAAI,CAAC,gCAAgC,CAAC;QAC/D,KAAK,MAAM,WAAW,iBAAkB;YACtC,IAAI,CAAC,WAAW,GAAG,CAAC,UAAU;gBAC5B,WAAW,GAAG,CAAC,SAAS;oBACtB;oBACA,gBAAgB,IAAI,CAAC,uBAAuB,CAAC,SAAS;oBACtD,eAAe;oBACf,mBAAmB;oBACnB,sBAAsB,IAAI,CAAC,6BAA6B,CAAC,SAAS,QAAQ,QAAQ;oBAClF,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS,QAAQ,QAAQ;oBAC5E,qBAAqB,IAAI,CAAC,4BAA4B,CAAC;oBACvD,SAAS;wBAAC;qBAAqB;oBAC/B,UAAU;oBACV,UAAU;gBACZ;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,WAAW,MAAM;IACrC;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAmB,EAAE,OAAwB,EAAY;QAC1F,MAAM,WAAqB,EAAE;QAC7B,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW;QAErE,4BAA4B;QAC5B,MAAM,iBAAiB,QAAQ,KAAK,CAAC,sBAAsB,EAAE;QAC7D,SAAS,IAAI,IAAI;QAEjB,kCAAkC;QAClC,MAAM,WAAW,QAAQ,QAAQ,IAAI,EAAE;QACvC,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,eAAe,QAAQ,OAAO,CAAC,iBAAiB,IAAI,WAAW;YACrE,IAAI,aAAa,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI;gBACzD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;YAClC;QACF;QAEA,0DAA0D;QAC1D,MAAM,qBAAqB,IAAI,CAAC,0BAA0B,CAAC,SAAS;QACpE,SAAS,IAAI,IAAI;QAEjB,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;IAC5B;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAwB,EAAY;QACtF,MAAM,WAAqB,EAAE;QAE7B,0BAA0B;QAC1B,IAAI,QAAQ,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,KAAK;YACxD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;QAC9D;QAEA,qBAAqB;QACrB,IAAI,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW,KAAK;YACpD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,OAAO,CAAC,iBAAiB,KAAK;QACnE;QAEA,oBAAoB;QACpB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,KAAK,MAAM,WAAW,iBAAkB;YACtC,IAAI,QAAQ,QAAQ,CAAC,QAAQ,WAAW,KAAK;gBAC3C,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK;YACjD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,OAAe,EAAE,OAAwB,EAAU;QACjF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW;QAExC,0BAA0B;QAC1B,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,KAAK,SAAS;QAExE,qBAAqB;QACrB,IAAI,aAAa,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM,SAAS;QAExF,oBAAoB;QACpB,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,WAAW,QAAQ,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;YACtD,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI,aAAa,QAAQ,CAAC,UAAU,SAAS;YAC/C;QACF;QAEA,wBAAwB;QACxB,SAAS,IAAI,CAAC,6BAA6B,CAAC,SAAS,QAAQ,QAAQ;QAErE,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,YAAY;IAC1C;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAwB,EAAU;QACpF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW;QAExC,6BAA6B;QAC7B,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM,SAAS;QAE5F,sBAAsB;QACtB,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,KAAK,SAAS;QAExE,oBAAoB;QACpB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,KAAK,MAAM,WAAW,iBAAkB;YACtC,IAAI,aAAa,QAAQ,CAAC,QAAQ,WAAW,KAAK,SAAS;QAC7D;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,8BAA8B,OAAe,EAAE,QAAgB,EAAU;QAC/E,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAa;gBAAiB;gBAAc;gBAAS;aAAQ;YACzE,UAAU;gBAAC;gBAAa;gBAAS;gBAAS;gBAAW;aAAS;YAC9D,SAAS;gBAAC;gBAAQ;gBAAU;gBAAc;gBAAY;aAAW;YACjE,UAAU;gBAAC;gBAAgB;gBAAY;gBAAU;gBAAc;aAAW;YAC1E,QAAQ;gBAAC;gBAAO;gBAAS;gBAAY;gBAAU;aAAQ;YACvD,WAAW;gBAAC;gBAAe;gBAAS;gBAAO;gBAAS;aAAS;QAC/D;QAEA,MAAM,mBAAmB,gBAAgB,CAAC,SAAS,WAAW,GAAoC,IAAI,EAAE;QACxG,MAAM,eAAe,QAAQ,WAAW;QAExC,KAAK,MAAM,YAAY,iBAAkB;YACvC,IAAI,aAAa,QAAQ,CAAC,WAAW,OAAO;QAC9C;QAEA,OAAO,GAAG,6BAA6B;IACzC;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,QAAgB,EAAU;QAC5E,MAAM,eAAe,QAAQ,WAAW;QACxC,MAAM,gBAAgB,SAAS,WAAW;QAE1C,IAAI,aAAa,QAAQ,CAAC,cAAc,OAAO,CAAC,QAAQ,MAAM,OAAO;QACrE,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc,OAAO;QAEjF,wCAAwC;QACxC,MAAM,gBAAgB,SAAS,KAAK,CAAC;QACrC,KAAK,MAAM,QAAQ,cAAe;YAChC,IAAI,KAAK,MAAM,GAAG,KAAK,aAAa,QAAQ,CAAC,KAAK,WAAW,KAAK,OAAO;QAC3E;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,6BAA6B,OAAe,EAAU;QAC5D,MAAM,yBAAyB;YAC7B;YAAS;YAAY;YAAW;YAAc;YAAW;YACzD;YAAQ;YAAQ;YAAO;YAAO;YAAW;YAAO;YAAQ;SACzD;QAED,MAAM,eAAe,QAAQ,WAAW;QAExC,KAAK,MAAM,WAAW,uBAAwB;YAC5C,IAAI,aAAa,QAAQ,CAAC,UAAU,OAAO;QAC7C;QAEA,+DAA+D;QAC/D,IAAI,QAAQ,MAAM,IAAI,IAAI,OAAO;QACjC,IAAI,QAAQ,MAAM,IAAI,IAAI,OAAO;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,OAAe,EAAE,YAA0B,EAAqC;QACxG,6DAA6D;QAC7D,MAAM,iBAAiB,aAAa,QAAQ,CACzC,MAAM,CAAC,CAAA;YACN,MAAM,sBAAsB,CAAC,KAAK,GAAG,KAAK,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;YACtF,OAAO,uBAAuB;QAChC;QAEF,MAAM,kBAAkB,eAAe,MAAM,CAAC,CAAA,UAC5C,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,KACpF,MAAM;QAER,IAAI,mBAAmB,GAAG,OAAO;QACjC,IAAI,mBAAmB,GAAG,OAAO;QACjC,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,OAAe,EAAE,OAAwB,EAA+B;QAChG,MAAM,eAAe,QAAQ,WAAW;QAExC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,aAAa,OAAO;QAChF,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,KAAK,OAAO;QACtE,IAAI,aAAa,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO;QACtF,IAAI,aAAa,QAAQ,CAAC,aAAa,aAAa,QAAQ,CAAC,YAAY,OAAO;QAChF,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,UAAU,OAAO;QAElD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,eAAe,OAAe,EAAE,OAAwB,EAAW;QACzE,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,MAAM,eAAe,QAAQ,WAAW;QAExC,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAC3B,aAAa,QAAQ,CAAC,QAAQ,WAAW;IAE7C;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAAoB,EAAY;QAC1D,MAAM,cAAwC;YAC5C,YAAY;gBAAC;gBAAQ;gBAAU;gBAAW;gBAAQ;gBAAQ;gBAAa;aAAQ;YAC/E,QAAQ;gBAAC;gBAAY;gBAAW;gBAAS;gBAAQ;gBAAS;aAAW;YACrE,YAAY;gBAAC;gBAAU;gBAAY;gBAAW;gBAAQ;gBAAa;aAAS;YAC5E,SAAS;gBAAC;gBAAW;gBAAO;gBAAW;gBAAU;gBAAY;aAAW;YACxE,QAAQ;gBAAC;gBAAU;gBAAY;gBAAU;gBAAS;gBAAO;aAAY;YACrE,YAAY;gBAAC;gBAAQ;gBAAW;gBAAc;gBAAY;gBAAO;aAAS;YAC1E,WAAW;gBAAC;gBAAY;gBAAa;gBAAY;gBAAU;gBAAS;aAAY;YAChF,YAAY;gBAAC;gBAAO;gBAAQ;gBAAW;gBAAU;gBAAW;aAAc;YAC1E,YAAY;gBAAC;gBAAY;gBAAQ;gBAAS;gBAAe;aAAa;YACtE,OAAO;gBAAC;gBAAO;gBAAS;gBAAY;gBAAU;gBAAW;aAAS;QACpE;QAEA,OAAO,WAAW,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAY;YAAW;SAAe;IAC3F;IAEA;;GAEC,GACD,AAAQ,iCAAiC,OAAwB,EAAY;QAC3E,MAAM,WAAqB,EAAE;QAE7B,wBAAwB;QACxB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,iBAAiB,KAAK;QAErE,wBAAwB;QACxB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;QAE5D,mBAAmB;QACnB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,OAAO,CAAC,iBAAiB,KAAK;QAEjE,6BAA6B;QAC7B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,SAAS,IAAI,IAAI,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,UAAW,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK;QAE9F,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,QAA2B,EAAE,OAAwB,EAA2B;QACzG,kCAAkC;QAClC,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAC,GAAG;YACvC,MAAM,SAAS,CAAC,EAAE,cAAc,GAAG,EAAE,aAAa,GAAG,EAAE,iBAAiB,GAAG,EAAE,mBAAmB,IAAI;YACpG,MAAM,SAAS,CAAC,EAAE,cAAc,GAAG,EAAE,aAAa,GAAG,EAAE,iBAAiB,GAAG,EAAE,mBAAmB,IAAI;YACpG,OAAO,SAAS;QAClB;QAEA,MAAM,cAAc,eACjB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,EAAE,QAAQ,KAAK,SACxD,KAAK,CAAC,GAAG;QAEZ,MAAM,oBAAoB,eACvB,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,IAAI,GACnC,KAAK,CAAC,GAAG;QAEZ,MAAM,mBAAmB,eACtB,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,IAAI,GACnC,KAAK,CAAC,GAAG;QAEZ,MAAM,iBAAiB,eACpB,MAAM,CAAC,CAAA,IAAK,EAAE,oBAAoB,IAAI,GACtC,KAAK,CAAC,GAAG;QAEZ,MAAM,iBAAiB,eACpB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAC3B,KAAK,CAAC,GAAG;QAEZ,iDAAiD;QACjD,MAAM,uBAAuB,IAAI,CAAC,0BAA0B,CAC1D,aACA,mBACA,kBACA,gBACA;QAGF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,2BACN,WAA8B,EAC9B,iBAAoC,EACpC,gBAAmC,EACnC,cAAiC,EACjC,cAAiC,EACvB;QACV,MAAM,kBAAkB,IAAI;QAE5B,wCAAwC;QACxC,YAAY,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,OAAO;QAClE,kBAAkB,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,OAAO;QACxE,iBAAiB,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,OAAO;QACvE,eAAe,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,OAAO;QACrE,eAAe,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,gBAAgB,GAAG,CAAC,EAAE,OAAO;QAErE,OAAO,MAAM,IAAI,CAAC,iBAAiB,KAAK,CAAC,GAAG;IAC9C;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAwB,EAAU;QACzD,OAAO,GAAG,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE,CAAC,WAAW;IAC9G;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAwB,EAA2B;QAC7E,MAAM,mBAAmB;YACvB;YAAa;YAAU;YAAa;YAAU;YAC9C,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;YAC9C,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,OAAO,CAAC,iBAAiB,KAAK;YACnD;YAAY;YAAiB;SAC9B;QAED,MAAM,mBAAsC,iBAAiB,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3E;gBACA,gBAAgB;gBAChB,eAAe;gBACf,mBAAmB;gBACnB,sBAAsB;gBACtB,mBAAmB;gBACnB,qBAAqB;gBACrB,SAAS;oBAAC;iBAAW;gBACrB,UAAU;gBACV,UAAU;YACZ,CAAC;QAED,OAAO;YACL,aAAa,iBAAiB,KAAK,CAAC,GAAG;YACvC,mBAAmB,iBAAiB,KAAK,CAAC,GAAG;YAC7C,kBAAkB,iBAAiB,KAAK,CAAC,GAAG;YAC5C,gBAAgB,iBAAiB,KAAK,CAAC,GAAG;YAC1C,gBAAgB,iBAAiB,KAAK,CAAC,GAAG;YAC1C,sBAAsB;QACxB;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/realtime-hashtag-scorer.ts"], "sourcesContent": ["/**\r\n * Real-time Hashtag Relevance Scoring System\r\n * Advanced scoring algorithm that evaluates hashtag relevance based on\r\n * RSS trends, business context, location, platform, and engagement potential\r\n */\r\n\r\nimport { rssService, TrendingData } from '../services/rss-feed-service';\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\n\r\nexport interface HashtagScore {\r\n  hashtag: string;\r\n  totalScore: number;\r\n  breakdown: {\r\n    trendingScore: number;      // Based on RSS trend data (0-10)\r\n    businessRelevance: number;  // Business context relevance (0-10)\r\n    locationRelevance: number;  // Geographic relevance (0-10)\r\n    platformOptimization: number; // Platform-specific optimization (0-10)\r\n    engagementPotential: number;  // Predicted engagement (0-10)\r\n    temporalRelevance: number;    // Time-based relevance (0-10)\r\n    competitorAnalysis: number;   // Competitor usage analysis (0-10)\r\n    semanticRelevance: number;    // Content semantic matching (0-10)\r\n  };\r\n  confidence: number;           // Overall confidence in score (0-1)\r\n  recommendation: 'high' | 'medium' | 'low' | 'avoid';\r\n  reasoning: string[];\r\n}\r\n\r\nexport interface ScoringContext {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  postContent?: string;\r\n  targetAudience?: string;\r\n  services?: string;\r\n  industry?: string;\r\n  timeOfDay?: number;\r\n  dayOfWeek?: number;\r\n}\r\n\r\nexport class RealtimeHashtagScorer {\r\n  private scoreCache: Map<string, { score: HashtagScore; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = 10 * 60 * 1000; // 10 minutes\r\n\r\n  /**\r\n   * Score a single hashtag with comprehensive analysis\r\n   */\r\n  public async scoreHashtag(hashtag: string, context: ScoringContext): Promise<HashtagScore> {\r\n    const cacheKey = `${hashtag}-${this.generateContextKey(context)}`;\r\n    const cached = this.scoreCache.get(cacheKey);\r\n    \r\n    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n      return cached.score;\r\n    }\r\n\r\n    try {\r\n      // Get trending data for analysis\r\n      const trendingData = await rssService.getTrendingData();\r\n      \r\n      // Calculate individual score components\r\n      const breakdown = {\r\n        trendingScore: await this.calculateTrendingScore(hashtag, trendingData),\r\n        businessRelevance: this.calculateBusinessRelevance(hashtag, context),\r\n        locationRelevance: this.calculateLocationRelevance(hashtag, context),\r\n        platformOptimization: this.calculatePlatformOptimization(hashtag, context),\r\n        engagementPotential: this.calculateEngagementPotential(hashtag, context),\r\n        temporalRelevance: this.calculateTemporalRelevance(hashtag, context),\r\n        competitorAnalysis: await this.calculateCompetitorAnalysis(hashtag, context, trendingData),\r\n        semanticRelevance: this.calculateSemanticRelevance(hashtag, context)\r\n      };\r\n\r\n      // Calculate weighted total score\r\n      const totalScore = this.calculateWeightedScore(breakdown, context);\r\n      \r\n      // Determine confidence level\r\n      const confidence = this.calculateConfidence(breakdown, trendingData);\r\n      \r\n      // Generate recommendation\r\n      const recommendation = this.generateRecommendation(totalScore, confidence);\r\n      \r\n      // Generate reasoning\r\n      const reasoning = this.generateReasoning(breakdown, context);\r\n\r\n      const score: HashtagScore = {\r\n        hashtag,\r\n        totalScore,\r\n        breakdown,\r\n        confidence,\r\n        recommendation,\r\n        reasoning\r\n      };\r\n\r\n      // Cache the result\r\n      this.scoreCache.set(cacheKey, { score, timestamp: Date.now() });\r\n\r\n      return score;\r\n\r\n    } catch (error) {\r\n      return this.getFallbackScore(hashtag, context);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Score multiple hashtags and return sorted by relevance\r\n   */\r\n  public async scoreHashtags(hashtags: string[], context: ScoringContext): Promise<HashtagScore[]> {\r\n    const scores = await Promise.all(\r\n      hashtags.map(hashtag => this.scoreHashtag(hashtag, context))\r\n    );\r\n\r\n    return scores.sort((a, b) => b.totalScore - a.totalScore);\r\n  }\r\n\r\n  /**\r\n   * Calculate trending score based on RSS data\r\n   */\r\n  private async calculateTrendingScore(hashtag: string, trendingData: TrendingData): Promise<number> {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n\r\n    // Check direct mentions in RSS articles\r\n    const mentionCount = trendingData.articles.filter(article => {\r\n      const content = `${article.title} ${article.description}`.toLowerCase();\r\n      return content.includes(hashtagLower);\r\n    }).length;\r\n\r\n    // Score based on mention frequency\r\n    if (mentionCount >= 5) score += 10;\r\n    else if (mentionCount >= 3) score += 8;\r\n    else if (mentionCount >= 1) score += 6;\r\n    else score += 2;\r\n\r\n    // Check keyword relevance in trending topics\r\n    const keywordRelevance = trendingData.keywords.filter(keyword =>\r\n      keyword.toLowerCase().includes(hashtagLower) || hashtagLower.includes(keyword.toLowerCase())\r\n    ).length;\r\n\r\n    score += Math.min(keywordRelevance * 2, 4);\r\n\r\n    // Recency bonus (newer articles get higher weight)\r\n    const recentMentions = trendingData.articles.filter(article => {\r\n      const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);\r\n      const content = `${article.title} ${article.description}`.toLowerCase();\r\n      return hoursSincePublished <= 6 && content.includes(hashtagLower);\r\n    }).length;\r\n\r\n    if (recentMentions > 0) score += 2;\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate business relevance score\r\n   */\r\n  private calculateBusinessRelevance(hashtag: string, context: ScoringContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n\r\n    // Direct business name match\r\n    if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\\s+/g, ''))) {\r\n      score += 10;\r\n    }\r\n\r\n    // Business type relevance\r\n    if (hashtagLower.includes(context.businessType.toLowerCase())) {\r\n      score += 8;\r\n    }\r\n\r\n    // Services/expertise relevance\r\n    if (context.services) {\r\n      const services = context.services.toLowerCase().split(/[,\\s]+/);\r\n      const serviceMatches = services.filter(service => \r\n        hashtagLower.includes(service) || service.includes(hashtagLower)\r\n      ).length;\r\n      score += Math.min(serviceMatches * 3, 6);\r\n    }\r\n\r\n    // Industry keywords\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    const industryMatches = industryKeywords.filter(keyword =>\r\n      hashtagLower.includes(keyword.toLowerCase())\r\n    ).length;\r\n    score += Math.min(industryMatches * 2, 4);\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate location relevance score\r\n   */\r\n  private calculateLocationRelevance(hashtag: string, context: ScoringContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n    const locationLower = context.location.toLowerCase();\r\n\r\n    // Direct location match\r\n    if (hashtagLower.includes(locationLower.replace(/\\s+/g, ''))) {\r\n      score += 10;\r\n    }\r\n\r\n    // Location parts (city, state, country)\r\n    const locationParts = context.location.split(/[,\\s]+/).filter(part => part.length > 2);\r\n    const locationMatches = locationParts.filter(part =>\r\n      hashtagLower.includes(part.toLowerCase())\r\n    ).length;\r\n    score += Math.min(locationMatches * 4, 8);\r\n\r\n    // Local/community keywords\r\n    const localKeywords = ['local', 'community', 'neighborhood', 'area', 'town', 'city'];\r\n    if (localKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 6;\r\n    }\r\n\r\n    // Regional keywords\r\n    const regionalKeywords = ['regional', 'metro', 'downtown', 'uptown', 'district'];\r\n    if (regionalKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 4;\r\n    }\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate platform optimization score\r\n   */\r\n  private calculatePlatformOptimization(hashtag: string, context: ScoringContext): number {\r\n    const platformHashtags = {\r\n      instagram: {\r\n        high: ['instagood', 'photooftheday', 'instadaily', 'reels', 'igers', 'instamood'],\r\n        medium: ['picoftheday', 'instapic', 'instalike', 'followme', 'instagramhub']\r\n      },\r\n      facebook: {\r\n        high: ['community', 'local', 'share', 'connect', 'family', 'friends'],\r\n        medium: ['like', 'follow', 'page', 'group', 'event']\r\n      },\r\n      twitter: {\r\n        high: ['news', 'update', 'discussion', 'trending', 'breaking', 'thread'],\r\n        medium: ['tweet', 'retweet', 'follow', 'hashtag', 'viral']\r\n      },\r\n      linkedin: {\r\n        high: ['professional', 'business', 'career', 'networking', 'industry', 'leadership'],\r\n        medium: ['job', 'work', 'corporate', 'company', 'team']\r\n      },\r\n      tiktok: {\r\n        high: ['fyp', 'viral', 'trending', 'foryou', 'dance', 'challenge'],\r\n        medium: ['tiktok', 'video', 'funny', 'entertainment', 'music']\r\n      },\r\n      pinterest: {\r\n        high: ['inspiration', 'ideas', 'diy', 'style', 'design', 'home'],\r\n        medium: ['pinterest', 'pin', 'board', 'creative', 'art']\r\n      }\r\n    };\r\n\r\n    const platform = context.platform.toLowerCase();\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n    \r\n    const platformData = platformHashtags[platform as keyof typeof platformHashtags];\r\n    if (!platformData) return 5; // Default score for unknown platforms\r\n\r\n    // Check high-value platform hashtags\r\n    if (platformData.high.some(tag => hashtagLower.includes(tag))) {\r\n      return 10;\r\n    }\r\n\r\n    // Check medium-value platform hashtags\r\n    if (platformData.medium.some(tag => hashtagLower.includes(tag))) {\r\n      return 7;\r\n    }\r\n\r\n    // Platform-specific length optimization\r\n    const optimalLengths = {\r\n      instagram: { min: 5, max: 20 },\r\n      twitter: { min: 3, max: 15 },\r\n      tiktok: { min: 3, max: 12 },\r\n      linkedin: { min: 8, max: 25 },\r\n      facebook: { min: 5, max: 18 },\r\n      pinterest: { min: 6, max: 22 }\r\n    };\r\n\r\n    const lengthData = optimalLengths[platform as keyof typeof optimalLengths];\r\n    if (lengthData && hashtag.length >= lengthData.min && hashtag.length <= lengthData.max) {\r\n      return 6;\r\n    }\r\n\r\n    return 3; // Base score\r\n  }\r\n\r\n  /**\r\n   * Calculate engagement potential score\r\n   */\r\n  private calculateEngagementPotential(hashtag: string, context: ScoringContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n\r\n    // High-engagement keywords\r\n    const highEngagementKeywords = [\r\n      'viral', 'trending', 'amazing', 'incredible', 'awesome', 'beautiful',\r\n      'love', 'best', 'new', 'hot', 'popular', 'top', 'must', 'perfect',\r\n      'exclusive', 'limited', 'special', 'unique', 'rare'\r\n    ];\r\n\r\n    if (highEngagementKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 9;\r\n    }\r\n\r\n    // Emotional keywords\r\n    const emotionalKeywords = [\r\n      'happy', 'excited', 'proud', 'grateful', 'blessed', 'inspired',\r\n      'motivated', 'passionate', 'thrilled', 'delighted'\r\n    ];\r\n\r\n    if (emotionalKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 7;\r\n    }\r\n\r\n    // Action keywords\r\n    const actionKeywords = [\r\n      'discover', 'explore', 'experience', 'try', 'learn', 'create',\r\n      'build', 'grow', 'achieve', 'succeed'\r\n    ];\r\n\r\n    if (actionKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 6;\r\n    }\r\n\r\n    // Length-based scoring (optimal hashtag lengths)\r\n    if (hashtag.length >= 6 && hashtag.length <= 15) {\r\n      score += 5;\r\n    } else if (hashtag.length >= 4 && hashtag.length <= 20) {\r\n      score += 3;\r\n    } else {\r\n      score += 1;\r\n    }\r\n\r\n    // Avoid overly generic hashtags\r\n    const genericHashtags = ['good', 'nice', 'cool', 'great', 'ok', 'fine'];\r\n    if (genericHashtags.some(generic => hashtagLower === generic)) {\r\n      score -= 3;\r\n    }\r\n\r\n    return Math.min(Math.max(score, 0), 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate temporal relevance score\r\n   */\r\n  private calculateTemporalRelevance(hashtag: string, context: ScoringContext): number {\r\n    let score = 5; // Base score\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n    const now = new Date();\r\n    const currentHour = context.timeOfDay || now.getHours();\r\n    const currentDay = context.dayOfWeek || now.getDay();\r\n\r\n    // Time-of-day relevance\r\n    const timeKeywords = {\r\n      morning: ['morning', 'breakfast', 'coffee', 'start', 'fresh'],\r\n      afternoon: ['lunch', 'afternoon', 'work', 'business', 'professional'],\r\n      evening: ['dinner', 'evening', 'relax', 'unwind', 'family'],\r\n      night: ['night', 'late', 'weekend', 'party', 'fun']\r\n    };\r\n\r\n    let timeCategory = 'morning';\r\n    if (currentHour >= 12 && currentHour < 17) timeCategory = 'afternoon';\r\n    else if (currentHour >= 17 && currentHour < 21) timeCategory = 'evening';\r\n    else if (currentHour >= 21 || currentHour < 6) timeCategory = 'night';\r\n\r\n    if (timeKeywords[timeCategory as keyof typeof timeKeywords].some(keyword => \r\n      hashtagLower.includes(keyword)\r\n    )) {\r\n      score += 3;\r\n    }\r\n\r\n    // Day-of-week relevance\r\n    const dayKeywords = {\r\n      weekday: ['work', 'business', 'professional', 'office', 'meeting'],\r\n      weekend: ['weekend', 'fun', 'relax', 'family', 'leisure', 'party']\r\n    };\r\n\r\n    const isWeekend = currentDay === 0 || currentDay === 6;\r\n    const dayCategory = isWeekend ? 'weekend' : 'weekday';\r\n\r\n    if (dayKeywords[dayCategory].some(keyword => hashtagLower.includes(keyword))) {\r\n      score += 2;\r\n    }\r\n\r\n    // Seasonal relevance (basic implementation)\r\n    const month = now.getMonth();\r\n    const seasonalKeywords = {\r\n      spring: ['spring', 'fresh', 'new', 'bloom', 'growth'],\r\n      summer: ['summer', 'hot', 'vacation', 'beach', 'outdoor'],\r\n      fall: ['fall', 'autumn', 'harvest', 'cozy', 'warm'],\r\n      winter: ['winter', 'cold', 'holiday', 'celebration', 'indoor']\r\n    };\r\n\r\n    let season = 'spring';\r\n    if (month >= 5 && month <= 7) season = 'summer';\r\n    else if (month >= 8 && month <= 10) season = 'fall';\r\n    else if (month >= 11 || month <= 1) season = 'winter';\r\n\r\n    if (seasonalKeywords[season as keyof typeof seasonalKeywords].some(keyword => \r\n      hashtagLower.includes(keyword)\r\n    )) {\r\n      score += 2;\r\n    }\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate competitor analysis score\r\n   */\r\n  private async calculateCompetitorAnalysis(\r\n    hashtag: string, \r\n    context: ScoringContext, \r\n    trendingData: TrendingData\r\n  ): Promise<number> {\r\n    let score = 5; // Base score\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n\r\n    // Analyze if competitors in the same industry are using this hashtag\r\n    const industryKeywords = this.getIndustryKeywords(context.businessType);\r\n    const competitorMentions = trendingData.articles.filter(article => {\r\n      const content = `${article.title} ${article.description}`.toLowerCase();\r\n      return industryKeywords.some(keyword => content.includes(keyword.toLowerCase())) &&\r\n             content.includes(hashtagLower);\r\n    }).length;\r\n\r\n    // Score based on competitor usage\r\n    if (competitorMentions >= 3) {\r\n      score += 4; // High competitor usage indicates relevance\r\n    } else if (competitorMentions >= 1) {\r\n      score += 2; // Some competitor usage\r\n    }\r\n\r\n    // Check for oversaturation (too many competitors using the same hashtag)\r\n    if (competitorMentions >= 10) {\r\n      score -= 2; // Penalty for oversaturated hashtags\r\n    }\r\n\r\n    return Math.min(Math.max(score, 0), 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate semantic relevance score\r\n   */\r\n  private calculateSemanticRelevance(hashtag: string, context: ScoringContext): number {\r\n    let score = 0;\r\n    const hashtagLower = hashtag.toLowerCase().replace('#', '');\r\n\r\n    // Content relevance (if post content is provided)\r\n    if (context.postContent) {\r\n      const contentLower = context.postContent.toLowerCase();\r\n      const contentWords = contentLower.split(/\\s+/);\r\n      \r\n      // Direct word match\r\n      if (contentWords.some(word => word.includes(hashtagLower) || hashtagLower.includes(word))) {\r\n        score += 8;\r\n      }\r\n\r\n      // Semantic similarity (basic implementation)\r\n      const semanticKeywords = this.extractSemanticKeywords(context.postContent);\r\n      if (semanticKeywords.some(keyword => \r\n        hashtagLower.includes(keyword) || keyword.includes(hashtagLower)\r\n      )) {\r\n        score += 6;\r\n      }\r\n    }\r\n\r\n    // Target audience relevance\r\n    if (context.targetAudience) {\r\n      const audienceKeywords = context.targetAudience.toLowerCase().split(/[,\\s]+/);\r\n      if (audienceKeywords.some(keyword => hashtagLower.includes(keyword))) {\r\n        score += 5;\r\n      }\r\n    }\r\n\r\n    // Industry semantic relevance\r\n    const industrySemantics = this.getIndustrySemantics(context.businessType);\r\n    if (industrySemantics.some(semantic => hashtagLower.includes(semantic.toLowerCase()))) {\r\n      score += 4;\r\n    }\r\n\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Calculate weighted total score\r\n   */\r\n  private calculateWeightedScore(breakdown: HashtagScore['breakdown'], context: ScoringContext): number {\r\n    // Weights can be adjusted based on business priorities\r\n    const weights = {\r\n      trendingScore: 0.25,      // RSS trending data is highly important\r\n      businessRelevance: 0.20,  // Business relevance is crucial\r\n      engagementPotential: 0.15, // Engagement drives results\r\n      platformOptimization: 0.12, // Platform fit matters\r\n      locationRelevance: 0.10,   // Local relevance for local businesses\r\n      semanticRelevance: 0.08,   // Content matching\r\n      temporalRelevance: 0.06,   // Time-based relevance\r\n      competitorAnalysis: 0.04   // Competitive intelligence\r\n    };\r\n\r\n    let totalScore = 0;\r\n    totalScore += breakdown.trendingScore * weights.trendingScore;\r\n    totalScore += breakdown.businessRelevance * weights.businessRelevance;\r\n    totalScore += breakdown.engagementPotential * weights.engagementPotential;\r\n    totalScore += breakdown.platformOptimization * weights.platformOptimization;\r\n    totalScore += breakdown.locationRelevance * weights.locationRelevance;\r\n    totalScore += breakdown.semanticRelevance * weights.semanticRelevance;\r\n    totalScore += breakdown.temporalRelevance * weights.temporalRelevance;\r\n    totalScore += breakdown.competitorAnalysis * weights.competitorAnalysis;\r\n\r\n    return Math.round(totalScore * 10) / 10; // Round to 1 decimal place\r\n  }\r\n\r\n  /**\r\n   * Calculate confidence in the score\r\n   */\r\n  private calculateConfidence(breakdown: HashtagScore['breakdown'], trendingData: TrendingData): number {\r\n    let confidence = 0;\r\n    let factors = 0;\r\n\r\n    // RSS data quality factor\r\n    if (trendingData.articles.length >= 10) {\r\n      confidence += 0.3;\r\n      factors++;\r\n    } else if (trendingData.articles.length >= 5) {\r\n      confidence += 0.2;\r\n      factors++;\r\n    }\r\n\r\n    // Score consistency factor\r\n    const scores = Object.values(breakdown);\r\n    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;\r\n    const variance = scores.reduce((sum, score) => sum + Math.pow(score - avgScore, 2), 0) / scores.length;\r\n    \r\n    if (variance < 4) { // Low variance indicates consistent scoring\r\n      confidence += 0.3;\r\n      factors++;\r\n    } else if (variance < 9) {\r\n      confidence += 0.2;\r\n      factors++;\r\n    }\r\n\r\n    // High-scoring factors\r\n    const highScores = scores.filter(score => score >= 7).length;\r\n    if (highScores >= 4) {\r\n      confidence += 0.4;\r\n      factors++;\r\n    } else if (highScores >= 2) {\r\n      confidence += 0.3;\r\n      factors++;\r\n    }\r\n\r\n    return factors > 0 ? Math.min(confidence, 1) : 0.5;\r\n  }\r\n\r\n  /**\r\n   * Generate recommendation based on score and confidence\r\n   */\r\n  private generateRecommendation(totalScore: number, confidence: number): HashtagScore['recommendation'] {\r\n    if (totalScore >= 8 && confidence >= 0.7) return 'high';\r\n    if (totalScore >= 6 && confidence >= 0.5) return 'medium';\r\n    if (totalScore >= 4) return 'low';\r\n    return 'avoid';\r\n  }\r\n\r\n  /**\r\n   * Generate reasoning for the score\r\n   */\r\n  private generateReasoning(breakdown: HashtagScore['breakdown'], context: ScoringContext): string[] {\r\n    const reasoning: string[] = [];\r\n\r\n    if (breakdown.trendingScore >= 8) {\r\n      reasoning.push('Highly trending in RSS feeds and news sources');\r\n    } else if (breakdown.trendingScore >= 6) {\r\n      reasoning.push('Moderately trending in current news cycle');\r\n    }\r\n\r\n    if (breakdown.businessRelevance >= 8) {\r\n      reasoning.push('Highly relevant to your business type and services');\r\n    } else if (breakdown.businessRelevance >= 6) {\r\n      reasoning.push('Good business relevance for your industry');\r\n    }\r\n\r\n    if (breakdown.engagementPotential >= 8) {\r\n      reasoning.push('High potential for user engagement and interaction');\r\n    }\r\n\r\n    if (breakdown.platformOptimization >= 8) {\r\n      reasoning.push(`Optimized for ${context.platform} platform algorithms`);\r\n    }\r\n\r\n    if (breakdown.locationRelevance >= 8) {\r\n      reasoning.push('Strong local/geographic relevance');\r\n    }\r\n\r\n    if (breakdown.competitorAnalysis >= 7) {\r\n      reasoning.push('Successfully used by industry competitors');\r\n    }\r\n\r\n    if (reasoning.length === 0) {\r\n      reasoning.push('Basic hashtag with standard performance potential');\r\n    }\r\n\r\n    return reasoning;\r\n  }\r\n\r\n  /**\r\n   * Get industry-specific keywords\r\n   */\r\n  private getIndustryKeywords(businessType: string): string[] {\r\n    const industryMap: Record<string, string[]> = {\r\n      restaurant: ['food', 'dining', 'cuisine', 'chef', 'menu', 'delicious', 'taste', 'recipe'],\r\n      retail: ['shopping', 'fashion', 'style', 'sale', 'deals', 'boutique', 'store', 'brand'],\r\n      healthcare: ['health', 'wellness', 'medical', 'care', 'treatment', 'doctor', 'patient'],\r\n      fitness: ['workout', 'gym', 'fitness', 'health', 'training', 'exercise', 'strength'],\r\n      beauty: ['beauty', 'skincare', 'makeup', 'salon', 'spa', 'treatment', 'cosmetics'],\r\n      technology: ['tech', 'digital', 'innovation', 'software', 'app', 'online', 'data'],\r\n      education: ['learning', 'education', 'training', 'course', 'skill', 'knowledge', 'teach'],\r\n      automotive: ['car', 'auto', 'vehicle', 'repair', 'service', 'maintenance', 'drive'],\r\n      realestate: ['property', 'home', 'house', 'real estate', 'investment', 'buy', 'sell'],\r\n      legal: ['law', 'legal', 'attorney', 'lawyer', 'justice', 'rights', 'court']\r\n    };\r\n\r\n    return industryMap[businessType.toLowerCase()] || ['business', 'service', 'professional', 'quality'];\r\n  }\r\n\r\n  /**\r\n   * Get industry semantic keywords\r\n   */\r\n  private getIndustrySemantics(businessType: string): string[] {\r\n    const semanticMap: Record<string, string[]> = {\r\n      restaurant: ['culinary', 'gastronomy', 'hospitality', 'ambiance', 'flavor'],\r\n      retail: ['merchandise', 'consumer', 'lifestyle', 'trend', 'collection'],\r\n      healthcare: ['therapeutic', 'diagnosis', 'prevention', 'recovery', 'healing'],\r\n      fitness: ['performance', 'endurance', 'transformation', 'motivation', 'results'],\r\n      beauty: ['aesthetic', 'enhancement', 'rejuvenation', 'glamour', 'confidence'],\r\n      technology: ['automation', 'efficiency', 'connectivity', 'intelligence', 'solution'],\r\n      education: ['development', 'growth', 'achievement', 'mastery', 'expertise'],\r\n      automotive: ['performance', 'reliability', 'maintenance', 'transportation', 'mobility'],\r\n      realestate: ['investment', 'location', 'value', 'opportunity', 'lifestyle'],\r\n      legal: ['advocacy', 'representation', 'protection', 'resolution', 'compliance']\r\n    };\r\n\r\n    return semanticMap[businessType.toLowerCase()] || ['excellence', 'quality', 'service', 'professional'];\r\n  }\r\n\r\n  /**\r\n   * Extract semantic keywords from content\r\n   */\r\n  private extractSemanticKeywords(content: string): string[] {\r\n    // Simple keyword extraction (can be enhanced with NLP)\r\n    const words = content.toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .split(/\\s+/)\r\n      .filter(word => word.length > 3);\r\n\r\n    // Remove common stop words\r\n    const stopWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said'];\r\n    return words.filter(word => !stopWords.includes(word));\r\n  }\r\n\r\n  /**\r\n   * Generate context key for caching\r\n   */\r\n  private generateContextKey(context: ScoringContext): string {\r\n    return `${context.businessType}-${context.location}-${context.platform}`.toLowerCase();\r\n  }\r\n\r\n  /**\r\n   * Get fallback score when analysis fails\r\n   */\r\n  private getFallbackScore(hashtag: string, context: ScoringContext): HashtagScore {\r\n    return {\r\n      hashtag,\r\n      totalScore: 5.0,\r\n      breakdown: {\r\n        trendingScore: 5,\r\n        businessRelevance: 5,\r\n        locationRelevance: 5,\r\n        platformOptimization: 5,\r\n        engagementPotential: 5,\r\n        temporalRelevance: 5,\r\n        competitorAnalysis: 5,\r\n        semanticRelevance: 5\r\n      },\r\n      confidence: 0.3,\r\n      recommendation: 'medium',\r\n      reasoning: ['Fallback scoring due to analysis error']\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const realtimeHashtagScorer = new RealtimeHashtagScorer();\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;;AAkCO,MAAM;IACH,aAAsE,IAAI,MAAM;IACvE,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAa,aAAa,OAAe,EAAE,OAAuB,EAAyB;QACzF,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU;QACjE,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAEnC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;YAC/D,OAAO,OAAO,KAAK;QACrB;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,eAAe,MAAM,2IAAA,CAAA,aAAU,CAAC,eAAe;YAErD,wCAAwC;YACxC,MAAM,YAAY;gBAChB,eAAe,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS;gBAC1D,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;gBAC5D,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;gBAC5D,sBAAsB,IAAI,CAAC,6BAA6B,CAAC,SAAS;gBAClE,qBAAqB,IAAI,CAAC,4BAA4B,CAAC,SAAS;gBAChE,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;gBAC5D,oBAAoB,MAAM,IAAI,CAAC,2BAA2B,CAAC,SAAS,SAAS;gBAC7E,mBAAmB,IAAI,CAAC,0BAA0B,CAAC,SAAS;YAC9D;YAEA,iCAAiC;YACjC,MAAM,aAAa,IAAI,CAAC,sBAAsB,CAAC,WAAW;YAE1D,6BAA6B;YAC7B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,WAAW;YAEvD,0BAA0B;YAC1B,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,YAAY;YAE/D,qBAAqB;YACrB,MAAM,YAAY,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAEpD,MAAM,QAAsB;gBAC1B;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,mBAAmB;YACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU;gBAAE;gBAAO,WAAW,KAAK,GAAG;YAAG;YAE7D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;QACxC;IACF;IAEA;;GAEC,GACD,MAAa,cAAc,QAAkB,EAAE,OAAuB,EAA2B;QAC/F,MAAM,SAAS,MAAM,QAAQ,GAAG,CAC9B,SAAS,GAAG,CAAC,CAAA,UAAW,IAAI,CAAC,YAAY,CAAC,SAAS;QAGrD,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAC1D;IAEA;;GAEC,GACD,MAAc,uBAAuB,OAAe,EAAE,YAA0B,EAAmB;QACjG,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,wCAAwC;QACxC,MAAM,eAAe,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA;YAChD,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW;YACrE,OAAO,QAAQ,QAAQ,CAAC;QAC1B,GAAG,MAAM;QAET,mCAAmC;QACnC,IAAI,gBAAgB,GAAG,SAAS;aAC3B,IAAI,gBAAgB,GAAG,SAAS;aAChC,IAAI,gBAAgB,GAAG,SAAS;aAChC,SAAS;QAEd,6CAA6C;QAC7C,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UACpD,QAAQ,WAAW,GAAG,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,QAAQ,WAAW,KACzF,MAAM;QAER,SAAS,KAAK,GAAG,CAAC,mBAAmB,GAAG;QAExC,mDAAmD;QACnD,MAAM,iBAAiB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA;YAClD,MAAM,sBAAsB,CAAC,KAAK,GAAG,KAAK,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;YACtF,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW;YACrE,OAAO,uBAAuB,KAAK,QAAQ,QAAQ,CAAC;QACtD,GAAG,MAAM;QAET,IAAI,iBAAiB,GAAG,SAAS;QAEjC,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAuB,EAAU;QACnF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,6BAA6B;QAC7B,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;YACjF,SAAS;QACX;QAEA,0BAA0B;QAC1B,IAAI,aAAa,QAAQ,CAAC,QAAQ,YAAY,CAAC,WAAW,KAAK;YAC7D,SAAS;QACX;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,WAAW,QAAQ,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;YACtD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UACrC,aAAa,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,eACnD,MAAM;YACR,SAAS,KAAK,GAAG,CAAC,iBAAiB,GAAG;QACxC;QAEA,oBAAoB;QACpB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,UAC9C,aAAa,QAAQ,CAAC,QAAQ,WAAW,KACzC,MAAM;QACR,SAAS,KAAK,GAAG,CAAC,kBAAkB,GAAG;QAEvC,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAuB,EAAU;QACnF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QACxD,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,WAAW;QAElD,wBAAwB;QACxB,IAAI,aAAa,QAAQ,CAAC,cAAc,OAAO,CAAC,QAAQ,MAAM;YAC5D,SAAS;QACX;QAEA,wCAAwC;QACxC,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QACpF,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAA,OAC3C,aAAa,QAAQ,CAAC,KAAK,WAAW,KACtC,MAAM;QACR,SAAS,KAAK,GAAG,CAAC,kBAAkB,GAAG;QAEvC,2BAA2B;QAC3B,MAAM,gBAAgB;YAAC;YAAS;YAAa;YAAgB;YAAQ;YAAQ;SAAO;QACpF,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACjE,SAAS;QACX;QAEA,oBAAoB;QACpB,MAAM,mBAAmB;YAAC;YAAY;YAAS;YAAY;YAAU;SAAW;QAChF,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACpE,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,8BAA8B,OAAe,EAAE,OAAuB,EAAU;QACtF,MAAM,mBAAmB;YACvB,WAAW;gBACT,MAAM;oBAAC;oBAAa;oBAAiB;oBAAc;oBAAS;oBAAS;iBAAY;gBACjF,QAAQ;oBAAC;oBAAe;oBAAY;oBAAa;oBAAY;iBAAe;YAC9E;YACA,UAAU;gBACR,MAAM;oBAAC;oBAAa;oBAAS;oBAAS;oBAAW;oBAAU;iBAAU;gBACrE,QAAQ;oBAAC;oBAAQ;oBAAU;oBAAQ;oBAAS;iBAAQ;YACtD;YACA,SAAS;gBACP,MAAM;oBAAC;oBAAQ;oBAAU;oBAAc;oBAAY;oBAAY;iBAAS;gBACxE,QAAQ;oBAAC;oBAAS;oBAAW;oBAAU;oBAAW;iBAAQ;YAC5D;YACA,UAAU;gBACR,MAAM;oBAAC;oBAAgB;oBAAY;oBAAU;oBAAc;oBAAY;iBAAa;gBACpF,QAAQ;oBAAC;oBAAO;oBAAQ;oBAAa;oBAAW;iBAAO;YACzD;YACA,QAAQ;gBACN,MAAM;oBAAC;oBAAO;oBAAS;oBAAY;oBAAU;oBAAS;iBAAY;gBAClE,QAAQ;oBAAC;oBAAU;oBAAS;oBAAS;oBAAiB;iBAAQ;YAChE;YACA,WAAW;gBACT,MAAM;oBAAC;oBAAe;oBAAS;oBAAO;oBAAS;oBAAU;iBAAO;gBAChE,QAAQ;oBAAC;oBAAa;oBAAO;oBAAS;oBAAY;iBAAM;YAC1D;QACF;QAEA,MAAM,WAAW,QAAQ,QAAQ,CAAC,WAAW;QAC7C,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,MAAM,eAAe,gBAAgB,CAAC,SAA0C;QAChF,IAAI,CAAC,cAAc,OAAO,GAAG,sCAAsC;QAEnE,qCAAqC;QACrC,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,aAAa,QAAQ,CAAC,OAAO;YAC7D,OAAO;QACT;QAEA,uCAAuC;QACvC,IAAI,aAAa,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,aAAa,QAAQ,CAAC,OAAO;YAC/D,OAAO;QACT;QAEA,wCAAwC;QACxC,MAAM,iBAAiB;YACrB,WAAW;gBAAE,KAAK;gBAAG,KAAK;YAAG;YAC7B,SAAS;gBAAE,KAAK;gBAAG,KAAK;YAAG;YAC3B,QAAQ;gBAAE,KAAK;gBAAG,KAAK;YAAG;YAC1B,UAAU;gBAAE,KAAK;gBAAG,KAAK;YAAG;YAC5B,UAAU;gBAAE,KAAK;gBAAG,KAAK;YAAG;YAC5B,WAAW;gBAAE,KAAK;gBAAG,KAAK;YAAG;QAC/B;QAEA,MAAM,aAAa,cAAc,CAAC,SAAwC;QAC1E,IAAI,cAAc,QAAQ,MAAM,IAAI,WAAW,GAAG,IAAI,QAAQ,MAAM,IAAI,WAAW,GAAG,EAAE;YACtF,OAAO;QACT;QAEA,OAAO,GAAG,aAAa;IACzB;IAEA;;GAEC,GACD,AAAQ,6BAA6B,OAAe,EAAE,OAAuB,EAAU;QACrF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,2BAA2B;QAC3B,MAAM,yBAAyB;YAC7B;YAAS;YAAY;YAAW;YAAc;YAAW;YACzD;YAAQ;YAAQ;YAAO;YAAO;YAAW;YAAO;YAAQ;YACxD;YAAa;YAAW;YAAW;YAAU;SAC9C;QAED,IAAI,uBAAuB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,qBAAqB;QACrB,MAAM,oBAAoB;YACxB;YAAS;YAAW;YAAS;YAAY;YAAW;YACpD;YAAa;YAAc;YAAY;SACxC;QAED,IAAI,kBAAkB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACrE,SAAS;QACX;QAEA,kBAAkB;QAClB,MAAM,iBAAiB;YACrB;YAAY;YAAW;YAAc;YAAO;YAAS;YACrD;YAAS;YAAQ;YAAW;SAC7B;QAED,IAAI,eAAe,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YAClE,SAAS;QACX;QAEA,iDAAiD;QACjD,IAAI,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,IAAI;YAC/C,SAAS;QACX,OAAO,IAAI,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,IAAI;YACtD,SAAS;QACX,OAAO;YACL,SAAS;QACX;QAEA,gCAAgC;QAChC,MAAM,kBAAkB;YAAC;YAAQ;YAAQ;YAAQ;YAAS;YAAM;SAAO;QACvE,IAAI,gBAAgB,IAAI,CAAC,CAAA,UAAW,iBAAiB,UAAU;YAC7D,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI;IACtC;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAuB,EAAU;QACnF,IAAI,QAAQ,GAAG,aAAa;QAC5B,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QACxD,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,QAAQ,SAAS,IAAI,IAAI,QAAQ;QACrD,MAAM,aAAa,QAAQ,SAAS,IAAI,IAAI,MAAM;QAElD,wBAAwB;QACxB,MAAM,eAAe;YACnB,SAAS;gBAAC;gBAAW;gBAAa;gBAAU;gBAAS;aAAQ;YAC7D,WAAW;gBAAC;gBAAS;gBAAa;gBAAQ;gBAAY;aAAe;YACrE,SAAS;gBAAC;gBAAU;gBAAW;gBAAS;gBAAU;aAAS;YAC3D,OAAO;gBAAC;gBAAS;gBAAQ;gBAAW;gBAAS;aAAM;QACrD;QAEA,IAAI,eAAe;QACnB,IAAI,eAAe,MAAM,cAAc,IAAI,eAAe;aACrD,IAAI,eAAe,MAAM,cAAc,IAAI,eAAe;aAC1D,IAAI,eAAe,MAAM,cAAc,GAAG,eAAe;QAE9D,IAAI,YAAY,CAAC,aAA0C,CAAC,IAAI,CAAC,CAAA,UAC/D,aAAa,QAAQ,CAAC,WACrB;YACD,SAAS;QACX;QAEA,wBAAwB;QACxB,MAAM,cAAc;YAClB,SAAS;gBAAC;gBAAQ;gBAAY;gBAAgB;gBAAU;aAAU;YAClE,SAAS;gBAAC;gBAAW;gBAAO;gBAAS;gBAAU;gBAAW;aAAQ;QACpE;QAEA,MAAM,YAAY,eAAe,KAAK,eAAe;QACrD,MAAM,cAAc,YAAY,YAAY;QAE5C,IAAI,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YAC5E,SAAS;QACX;QAEA,4CAA4C;QAC5C,MAAM,QAAQ,IAAI,QAAQ;QAC1B,MAAM,mBAAmB;YACvB,QAAQ;gBAAC;gBAAU;gBAAS;gBAAO;gBAAS;aAAS;YACrD,QAAQ;gBAAC;gBAAU;gBAAO;gBAAY;gBAAS;aAAU;YACzD,MAAM;gBAAC;gBAAQ;gBAAU;gBAAW;gBAAQ;aAAO;YACnD,QAAQ;gBAAC;gBAAU;gBAAQ;gBAAW;gBAAe;aAAS;QAChE;QAEA,IAAI,SAAS;QACb,IAAI,SAAS,KAAK,SAAS,GAAG,SAAS;aAClC,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS;aACxC,IAAI,SAAS,MAAM,SAAS,GAAG,SAAS;QAE7C,IAAI,gBAAgB,CAAC,OAAwC,CAAC,IAAI,CAAC,CAAA,UACjE,aAAa,QAAQ,CAAC,WACrB;YACD,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAc,4BACZ,OAAe,EACf,OAAuB,EACvB,YAA0B,EACT;QACjB,IAAI,QAAQ,GAAG,aAAa;QAC5B,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,qEAAqE;QACrE,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,YAAY;QACtE,MAAM,qBAAqB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA;YACtD,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,CAAC,WAAW;YACrE,OAAO,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,QAAQ,WAAW,QACrE,QAAQ,QAAQ,CAAC;QAC1B,GAAG,MAAM;QAET,kCAAkC;QAClC,IAAI,sBAAsB,GAAG;YAC3B,SAAS,GAAG,4CAA4C;QAC1D,OAAO,IAAI,sBAAsB,GAAG;YAClC,SAAS,GAAG,wBAAwB;QACtC;QAEA,yEAAyE;QACzE,IAAI,sBAAsB,IAAI;YAC5B,SAAS,GAAG,qCAAqC;QACnD;QAEA,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI;IACtC;IAEA;;GAEC,GACD,AAAQ,2BAA2B,OAAe,EAAE,OAAuB,EAAU;QACnF,IAAI,QAAQ;QACZ,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;QAExD,kDAAkD;QAClD,IAAI,QAAQ,WAAW,EAAE;YACvB,MAAM,eAAe,QAAQ,WAAW,CAAC,WAAW;YACpD,MAAM,eAAe,aAAa,KAAK,CAAC;YAExC,oBAAoB;YACpB,IAAI,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,QAAQ;gBACzF,SAAS;YACX;YAEA,6CAA6C;YAC7C,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,WAAW;YACzE,IAAI,iBAAiB,IAAI,CAAC,CAAA,UACxB,aAAa,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,gBAClD;gBACD,SAAS;YACX;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,cAAc,EAAE;YAC1B,MAAM,mBAAmB,QAAQ,cAAc,CAAC,WAAW,GAAG,KAAK,CAAC;YACpE,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACpE,SAAS;YACX;QACF;QAEA,8BAA8B;QAC9B,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,YAAY;QACxE,IAAI,kBAAkB,IAAI,CAAC,CAAA,WAAY,aAAa,QAAQ,CAAC,SAAS,WAAW,MAAM;YACrF,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,uBAAuB,SAAoC,EAAE,OAAuB,EAAU;QACpG,uDAAuD;QACvD,MAAM,UAAU;YACd,eAAe;YACf,mBAAmB;YACnB,qBAAqB;YACrB,sBAAsB;YACtB,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB,KAAO,2BAA2B;QACxD;QAEA,IAAI,aAAa;QACjB,cAAc,UAAU,aAAa,GAAG,QAAQ,aAAa;QAC7D,cAAc,UAAU,iBAAiB,GAAG,QAAQ,iBAAiB;QACrE,cAAc,UAAU,mBAAmB,GAAG,QAAQ,mBAAmB;QACzE,cAAc,UAAU,oBAAoB,GAAG,QAAQ,oBAAoB;QAC3E,cAAc,UAAU,iBAAiB,GAAG,QAAQ,iBAAiB;QACrE,cAAc,UAAU,iBAAiB,GAAG,QAAQ,iBAAiB;QACrE,cAAc,UAAU,iBAAiB,GAAG,QAAQ,iBAAiB;QACrE,cAAc,UAAU,kBAAkB,GAAG,QAAQ,kBAAkB;QAEvE,OAAO,KAAK,KAAK,CAAC,aAAa,MAAM,IAAI,2BAA2B;IACtE;IAEA;;GAEC,GACD,AAAQ,oBAAoB,SAAoC,EAAE,YAA0B,EAAU;QACpG,IAAI,aAAa;QACjB,IAAI,UAAU;QAEd,0BAA0B;QAC1B,IAAI,aAAa,QAAQ,CAAC,MAAM,IAAI,IAAI;YACtC,cAAc;YACd;QACF,OAAO,IAAI,aAAa,QAAQ,CAAC,MAAM,IAAI,GAAG;YAC5C,cAAc;YACd;QACF;QAEA,2BAA2B;QAC3B,MAAM,SAAS,OAAO,MAAM,CAAC;QAC7B,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK,OAAO,MAAM;QAC9E,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,KAAK,GAAG,CAAC,QAAQ,UAAU,IAAI,KAAK,OAAO,MAAM;QAEtG,IAAI,WAAW,GAAG;YAChB,cAAc;YACd;QACF,OAAO,IAAI,WAAW,GAAG;YACvB,cAAc;YACd;QACF;QAEA,uBAAuB;QACvB,MAAM,aAAa,OAAO,MAAM,CAAC,CAAA,QAAS,SAAS,GAAG,MAAM;QAC5D,IAAI,cAAc,GAAG;YACnB,cAAc;YACd;QACF,OAAO,IAAI,cAAc,GAAG;YAC1B,cAAc;YACd;QACF;QAEA,OAAO,UAAU,IAAI,KAAK,GAAG,CAAC,YAAY,KAAK;IACjD;IAEA;;GAEC,GACD,AAAQ,uBAAuB,UAAkB,EAAE,UAAkB,EAAkC;QACrG,IAAI,cAAc,KAAK,cAAc,KAAK,OAAO;QACjD,IAAI,cAAc,KAAK,cAAc,KAAK,OAAO;QACjD,IAAI,cAAc,GAAG,OAAO;QAC5B,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,SAAoC,EAAE,OAAuB,EAAY;QACjG,MAAM,YAAsB,EAAE;QAE9B,IAAI,UAAU,aAAa,IAAI,GAAG;YAChC,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,UAAU,aAAa,IAAI,GAAG;YACvC,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,iBAAiB,IAAI,GAAG;YACpC,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,UAAU,iBAAiB,IAAI,GAAG;YAC3C,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,mBAAmB,IAAI,GAAG;YACtC,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,oBAAoB,IAAI,GAAG;YACvC,UAAU,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,QAAQ,CAAC,oBAAoB,CAAC;QACxE;QAEA,IAAI,UAAU,iBAAiB,IAAI,GAAG;YACpC,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,kBAAkB,IAAI,GAAG;YACrC,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,UAAU,IAAI,CAAC;QACjB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAAoB,EAAY;QAC1D,MAAM,cAAwC;YAC5C,YAAY;gBAAC;gBAAQ;gBAAU;gBAAW;gBAAQ;gBAAQ;gBAAa;gBAAS;aAAS;YACzF,QAAQ;gBAAC;gBAAY;gBAAW;gBAAS;gBAAQ;gBAAS;gBAAY;gBAAS;aAAQ;YACvF,YAAY;gBAAC;gBAAU;gBAAY;gBAAW;gBAAQ;gBAAa;gBAAU;aAAU;YACvF,SAAS;gBAAC;gBAAW;gBAAO;gBAAW;gBAAU;gBAAY;gBAAY;aAAW;YACpF,QAAQ;gBAAC;gBAAU;gBAAY;gBAAU;gBAAS;gBAAO;gBAAa;aAAY;YAClF,YAAY;gBAAC;gBAAQ;gBAAW;gBAAc;gBAAY;gBAAO;gBAAU;aAAO;YAClF,WAAW;gBAAC;gBAAY;gBAAa;gBAAY;gBAAU;gBAAS;gBAAa;aAAQ;YACzF,YAAY;gBAAC;gBAAO;gBAAQ;gBAAW;gBAAU;gBAAW;gBAAe;aAAQ;YACnF,YAAY;gBAAC;gBAAY;gBAAQ;gBAAS;gBAAe;gBAAc;gBAAO;aAAO;YACrF,OAAO;gBAAC;gBAAO;gBAAS;gBAAY;gBAAU;gBAAW;gBAAU;aAAQ;QAC7E;QAEA,OAAO,WAAW,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAY;YAAW;YAAgB;SAAU;IACtG;IAEA;;GAEC,GACD,AAAQ,qBAAqB,YAAoB,EAAY;QAC3D,MAAM,cAAwC;YAC5C,YAAY;gBAAC;gBAAY;gBAAc;gBAAe;gBAAY;aAAS;YAC3E,QAAQ;gBAAC;gBAAe;gBAAY;gBAAa;gBAAS;aAAa;YACvE,YAAY;gBAAC;gBAAe;gBAAa;gBAAc;gBAAY;aAAU;YAC7E,SAAS;gBAAC;gBAAe;gBAAa;gBAAkB;gBAAc;aAAU;YAChF,QAAQ;gBAAC;gBAAa;gBAAe;gBAAgB;gBAAW;aAAa;YAC7E,YAAY;gBAAC;gBAAc;gBAAc;gBAAgB;gBAAgB;aAAW;YACpF,WAAW;gBAAC;gBAAe;gBAAU;gBAAe;gBAAW;aAAY;YAC3E,YAAY;gBAAC;gBAAe;gBAAe;gBAAe;gBAAkB;aAAW;YACvF,YAAY;gBAAC;gBAAc;gBAAY;gBAAS;gBAAe;aAAY;YAC3E,OAAO;gBAAC;gBAAY;gBAAkB;gBAAc;gBAAc;aAAa;QACjF;QAEA,OAAO,WAAW,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAc;YAAW;YAAW;SAAe;IACxG;IAEA;;GAEC,GACD,AAAQ,wBAAwB,OAAe,EAAY;QACzD,uDAAuD;QACvD,MAAM,QAAQ,QAAQ,WAAW,GAC9B,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAEhC,2BAA2B;QAC3B,MAAM,YAAY;YAAC;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;QAClG,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,UAAU,QAAQ,CAAC;IAClD;IAEA;;GAEC,GACD,AAAQ,mBAAmB,OAAuB,EAAU;QAC1D,OAAO,GAAG,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,QAAQ,EAAE,CAAC,WAAW;IACtF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAe,EAAE,OAAuB,EAAgB;QAC/E,OAAO;YACL;YACA,YAAY;YACZ,WAAW;gBACT,eAAe;gBACf,mBAAmB;gBACnB,mBAAmB;gBACnB,sBAAsB;gBACtB,qBAAqB;gBACrB,mBAAmB;gBACnB,oBAAoB;gBACpB,mBAAmB;YACrB;YACA,YAAY;YACZ,gBAAgB;YAChB,WAAW;gBAAC;aAAyC;QACvD;IACF;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 2347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/intelligent-hashtag-mixer.ts"], "sourcesContent": ["/**\r\n * Intelligent Hashtag Mixing Algorithm\r\n * Advanced algorithm that combines trending RSS hashtags with business-specific tags\r\n * for optimal reach and relevance using machine learning-inspired scoring\r\n */\r\n\r\nimport { AdvancedHashtagStrategy } from './advanced-trending-hashtag-analyzer';\r\nimport { ViralHashtagStrategy } from './viral-hashtag-engine';\r\nimport { realtimeHashtagScorer, HashtagScore } from './realtime-hashtag-scorer';\r\n\r\nexport interface HashtagMixStrategy {\r\n  primary: string[];        // Top 5 most important hashtags\r\n  secondary: string[];      // Next 5 supporting hashtags  \r\n  tertiary: string[];       // Final 5 niche/specific hashtags\r\n  final: string[];          // Combined final 15 hashtags\r\n  \r\n  analytics: {\r\n    rssInfluence: number;     // Percentage of RSS-sourced hashtags (0-100)\r\n    businessRelevance: number; // Average business relevance score (0-10)\r\n    trendingScore: number;    // Average trending score (0-10)\r\n    diversityScore: number;   // Hashtag diversity score (0-10)\r\n    confidenceLevel: number;  // Overall confidence (0-10)\r\n    mixingStrategy: string;   // Description of mixing approach used\r\n  };\r\n}\r\n\r\nexport interface MixingContext {\r\n  businessType: string;\r\n  businessName: string;\r\n  location: string;\r\n  platform: string;\r\n  postContent?: string;\r\n  targetAudience?: string;\r\n  services?: string;\r\n  priority: 'reach' | 'relevance' | 'engagement' | 'balanced';\r\n  rssWeight: number;        // Weight for RSS data (0-1)\r\n  businessWeight: number;   // Weight for business relevance (0-1)\r\n}\r\n\r\nexport class IntelligentHashtagMixer {\r\n  private mixingCache: Map<string, { strategy: HashtagMixStrategy; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = 20 * 60 * 1000; // 20 minutes\r\n\r\n  /**\r\n   * Create intelligent hashtag mix using advanced algorithms\r\n   */\r\n  public async createIntelligentMix(\r\n    advancedStrategy: AdvancedHashtagStrategy,\r\n    viralStrategy: ViralHashtagStrategy,\r\n    context: MixingContext\r\n  ): Promise<HashtagMixStrategy> {\r\n    const cacheKey = this.generateCacheKey(context);\r\n    const cached = this.mixingCache.get(cacheKey);\r\n    \r\n    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n      return cached.strategy;\r\n    }\r\n\r\n    try {\r\n      // 🧠 STEP 1: Score all available hashtags\r\n      const allHashtags = this.collectAllHashtags(advancedStrategy, viralStrategy);\r\n      const scoredHashtags = await this.scoreAllHashtags(allHashtags, context);\r\n\r\n      // 🎯 STEP 2: Apply intelligent mixing algorithm\r\n      const mixedHashtags = this.applyMixingAlgorithm(scoredHashtags, context);\r\n\r\n      // 📊 STEP 3: Analyze the final mix\r\n      const analytics = this.analyzeMix(mixedHashtags, advancedStrategy, context);\r\n\r\n      // 🏗️ STEP 4: Structure the final strategy\r\n      const strategy = this.structureFinalStrategy(mixedHashtags, analytics);\r\n\r\n      // Cache the result\r\n      this.mixingCache.set(cacheKey, { strategy, timestamp: Date.now() });\r\n\r\n      return strategy;\r\n\r\n    } catch (error) {\r\n      return this.getFallbackMix(context);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Collect all hashtags from different sources\r\n   */\r\n  private collectAllHashtags(\r\n    advancedStrategy: AdvancedHashtagStrategy,\r\n    viralStrategy: ViralHashtagStrategy\r\n  ): Array<{ hashtag: string; source: string; priority: number }> {\r\n    const hashtags: Array<{ hashtag: string; source: string; priority: number }> = [];\r\n\r\n    // Advanced strategy hashtags (highest priority)\r\n    advancedStrategy.finalRecommendations.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'advanced_rss', priority: 10 });\r\n    });\r\n\r\n    advancedStrategy.topTrending.forEach(analysis => {\r\n      hashtags.push({ hashtag: analysis.hashtag, source: 'rss_trending', priority: 9 });\r\n    });\r\n\r\n    advancedStrategy.emergingTrends.forEach(analysis => {\r\n      hashtags.push({ hashtag: analysis.hashtag, source: 'rss_emerging', priority: 8 });\r\n    });\r\n\r\n    advancedStrategy.businessOptimized.forEach(analysis => {\r\n      hashtags.push({ hashtag: analysis.hashtag, source: 'rss_business', priority: 8 });\r\n    });\r\n\r\n    // Viral strategy hashtags (medium priority)\r\n    viralStrategy.trending.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'viral_trending', priority: 7 });\r\n    });\r\n\r\n    viralStrategy.viral.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'viral_engagement', priority: 7 });\r\n    });\r\n\r\n    viralStrategy.niche.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'viral_niche', priority: 6 });\r\n    });\r\n\r\n    viralStrategy.location.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'viral_location', priority: 6 });\r\n    });\r\n\r\n    viralStrategy.platform.forEach(hashtag => {\r\n      hashtags.push({ hashtag, source: 'viral_platform', priority: 5 });\r\n    });\r\n\r\n    // Remove duplicates while preserving highest priority\r\n    const uniqueHashtags = new Map<string, { hashtag: string; source: string; priority: number }>();\r\n    \r\n    hashtags.forEach(item => {\r\n      const existing = uniqueHashtags.get(item.hashtag);\r\n      if (!existing || item.priority > existing.priority) {\r\n        uniqueHashtags.set(item.hashtag, item);\r\n      }\r\n    });\r\n\r\n    return Array.from(uniqueHashtags.values());\r\n  }\r\n\r\n  /**\r\n   * Score all hashtags using the realtime scorer\r\n   */\r\n  private async scoreAllHashtags(\r\n    hashtags: Array<{ hashtag: string; source: string; priority: number }>,\r\n    context: MixingContext\r\n  ): Promise<Array<{ hashtag: string; source: string; priority: number; score: HashtagScore }>> {\r\n    const scoringContext = {\r\n      businessType: context.businessType,\r\n      businessName: context.businessName,\r\n      location: context.location,\r\n      platform: context.platform,\r\n      postContent: context.postContent,\r\n      targetAudience: context.targetAudience,\r\n      services: context.services\r\n    };\r\n\r\n    const scoredHashtags = await Promise.all(\r\n      hashtags.map(async item => ({\r\n        ...item,\r\n        score: await realtimeHashtagScorer.scoreHashtag(item.hashtag, scoringContext)\r\n      }))\r\n    );\r\n\r\n    return scoredHashtags;\r\n  }\r\n\r\n  /**\r\n   * Apply intelligent mixing algorithm based on context priority\r\n   */\r\n  private applyMixingAlgorithm(\r\n    scoredHashtags: Array<{ hashtag: string; source: string; priority: number; score: HashtagScore }>,\r\n    context: MixingContext\r\n  ): Array<{ hashtag: string; source: string; priority: number; score: HashtagScore; finalScore: number }> {\r\n    \r\n    return scoredHashtags.map(item => {\r\n      let finalScore = 0;\r\n\r\n      // Base score from realtime scorer\r\n      finalScore += item.score.totalScore * 0.4;\r\n\r\n      // Priority bonus from source\r\n      finalScore += item.priority * 0.2;\r\n\r\n      // Context-specific adjustments\r\n      switch (context.priority) {\r\n        case 'reach':\r\n          finalScore += item.score.breakdown.trendingScore * 0.3;\r\n          finalScore += item.score.breakdown.engagementPotential * 0.1;\r\n          break;\r\n        \r\n        case 'relevance':\r\n          finalScore += item.score.breakdown.businessRelevance * 0.3;\r\n          finalScore += item.score.breakdown.semanticRelevance * 0.1;\r\n          break;\r\n        \r\n        case 'engagement':\r\n          finalScore += item.score.breakdown.engagementPotential * 0.3;\r\n          finalScore += item.score.breakdown.platformOptimization * 0.1;\r\n          break;\r\n        \r\n        case 'balanced':\r\n        default:\r\n          finalScore += (\r\n            item.score.breakdown.trendingScore +\r\n            item.score.breakdown.businessRelevance +\r\n            item.score.breakdown.engagementPotential\r\n          ) * 0.1;\r\n          break;\r\n      }\r\n\r\n      // RSS weight adjustment\r\n      if (item.source.includes('rss')) {\r\n        finalScore += finalScore * context.rssWeight * 0.2;\r\n      }\r\n\r\n      // Business weight adjustment\r\n      if (item.source.includes('business') || item.source.includes('niche')) {\r\n        finalScore += finalScore * context.businessWeight * 0.2;\r\n      }\r\n\r\n      // Confidence bonus\r\n      finalScore += item.score.confidence * 2;\r\n\r\n      return {\r\n        ...item,\r\n        finalScore: Math.round(finalScore * 10) / 10\r\n      };\r\n    }).sort((a, b) => b.finalScore - a.finalScore);\r\n  }\r\n\r\n  /**\r\n   * Analyze the quality of the final mix\r\n   */\r\n  private analyzeMix(\r\n    mixedHashtags: Array<{ hashtag: string; source: string; priority: number; score: HashtagScore; finalScore: number }>,\r\n    advancedStrategy: AdvancedHashtagStrategy,\r\n    context: MixingContext\r\n  ): HashtagMixStrategy['analytics'] {\r\n    const top15 = mixedHashtags.slice(0, 15);\r\n\r\n    // Calculate RSS influence\r\n    const rssHashtags = top15.filter(item => item.source.includes('rss')).length;\r\n    const rssInfluence = Math.round((rssHashtags / 15) * 100);\r\n\r\n    // Calculate average business relevance\r\n    const avgBusinessRelevance = top15.reduce((sum, item) => \r\n      sum + item.score.breakdown.businessRelevance, 0) / 15;\r\n\r\n    // Calculate average trending score\r\n    const avgTrendingScore = top15.reduce((sum, item) => \r\n      sum + item.score.breakdown.trendingScore, 0) / 15;\r\n\r\n    // Calculate diversity score\r\n    const sources = new Set(top15.map(item => item.source));\r\n    const diversityScore = Math.min((sources.size / 6) * 10, 10); // Max 6 different sources\r\n\r\n    // Calculate overall confidence\r\n    const avgConfidence = top15.reduce((sum, item) => sum + item.score.confidence, 0) / 15;\r\n    const confidenceLevel = Math.round(avgConfidence * 10);\r\n\r\n    // Determine mixing strategy description\r\n    const mixingStrategy = this.describeMixingStrategy(context, rssInfluence, avgBusinessRelevance);\r\n\r\n    return {\r\n      rssInfluence,\r\n      businessRelevance: Math.round(avgBusinessRelevance * 10) / 10,\r\n      trendingScore: Math.round(avgTrendingScore * 10) / 10,\r\n      diversityScore: Math.round(diversityScore * 10) / 10,\r\n      confidenceLevel,\r\n      mixingStrategy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Structure the final hashtag strategy\r\n   */\r\n  private structureFinalStrategy(\r\n    mixedHashtags: Array<{ hashtag: string; source: string; priority: number; score: HashtagScore; finalScore: number }>,\r\n    analytics: HashtagMixStrategy['analytics']\r\n  ): HashtagMixStrategy {\r\n    const top15 = mixedHashtags.slice(0, 15);\r\n\r\n    return {\r\n      primary: top15.slice(0, 5).map(item => item.hashtag),\r\n      secondary: top15.slice(5, 10).map(item => item.hashtag),\r\n      tertiary: top15.slice(10, 15).map(item => item.hashtag),\r\n      final: top15.map(item => item.hashtag),\r\n      analytics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Describe the mixing strategy used\r\n   */\r\n  private describeMixingStrategy(\r\n    context: MixingContext,\r\n    rssInfluence: number,\r\n    businessRelevance: number\r\n  ): string {\r\n    let strategy = `${context.priority.charAt(0).toUpperCase() + context.priority.slice(1)}-focused mixing`;\r\n    \r\n    if (rssInfluence >= 70) {\r\n      strategy += ' with heavy RSS trending emphasis';\r\n    } else if (rssInfluence >= 40) {\r\n      strategy += ' with balanced RSS integration';\r\n    } else {\r\n      strategy += ' with minimal RSS influence';\r\n    }\r\n\r\n    if (businessRelevance >= 8) {\r\n      strategy += ' and high business relevance';\r\n    } else if (businessRelevance >= 6) {\r\n      strategy += ' and moderate business relevance';\r\n    } else {\r\n      strategy += ' and broad market appeal';\r\n    }\r\n\r\n    return strategy;\r\n  }\r\n\r\n  /**\r\n   * Generate cache key for mixing context\r\n   */\r\n  private generateCacheKey(context: MixingContext): string {\r\n    return `${context.businessType}-${context.location}-${context.platform}-${context.priority}-${context.rssWeight}-${context.businessWeight}`.toLowerCase();\r\n  }\r\n\r\n  /**\r\n   * Get fallback mix when algorithm fails\r\n   */\r\n  private getFallbackMix(context: MixingContext): HashtagMixStrategy {\r\n    const fallbackHashtags = [\r\n      '#trending', '#viral', `#${context.businessType.replace(/\\s+/g, '')}`,\r\n      '#local', '#community', '#business', '#quality', '#professional',\r\n      '#service', '#new', '#amazing', '#best', '#popular', '#love', '#today'\r\n    ];\r\n\r\n    return {\r\n      primary: fallbackHashtags.slice(0, 5),\r\n      secondary: fallbackHashtags.slice(5, 10),\r\n      tertiary: fallbackHashtags.slice(10, 15),\r\n      final: fallbackHashtags,\r\n      analytics: {\r\n        rssInfluence: 0,\r\n        businessRelevance: 5.0,\r\n        trendingScore: 3.0,\r\n        diversityScore: 4.0,\r\n        confidenceLevel: 3,\r\n        mixingStrategy: 'Fallback strategy due to algorithm failure'\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal mixing weights based on business type and platform\r\n   */\r\n  public getOptimalWeights(businessType: string, platform: string): { rssWeight: number; businessWeight: number } {\r\n    // Platform-specific weights\r\n    const platformWeights = {\r\n      instagram: { rssWeight: 0.7, businessWeight: 0.6 },\r\n      tiktok: { rssWeight: 0.8, businessWeight: 0.4 },\r\n      twitter: { rssWeight: 0.9, businessWeight: 0.5 },\r\n      linkedin: { rssWeight: 0.6, businessWeight: 0.8 },\r\n      facebook: { rssWeight: 0.5, businessWeight: 0.7 },\r\n      pinterest: { rssWeight: 0.6, businessWeight: 0.6 }\r\n    };\r\n\r\n    // Business type adjustments\r\n    const businessAdjustments = {\r\n      restaurant: { rssBoost: 0.1, businessBoost: 0.2 },\r\n      retail: { rssBoost: 0.2, businessBoost: 0.1 },\r\n      healthcare: { rssBoost: 0.0, businessBoost: 0.3 },\r\n      technology: { rssBoost: 0.3, businessBoost: 0.1 },\r\n      fitness: { rssBoost: 0.2, businessBoost: 0.2 },\r\n      beauty: { rssBoost: 0.2, businessBoost: 0.1 }\r\n    };\r\n\r\n    const platformWeight = platformWeights[platform.toLowerCase() as keyof typeof platformWeights] || \r\n                          { rssWeight: 0.6, businessWeight: 0.6 };\r\n    \r\n    const businessAdj = businessAdjustments[businessType.toLowerCase() as keyof typeof businessAdjustments] || \r\n                       { rssBoost: 0.1, businessBoost: 0.1 };\r\n\r\n    return {\r\n      rssWeight: Math.min(platformWeight.rssWeight + businessAdj.rssBoost, 1.0),\r\n      businessWeight: Math.min(platformWeight.businessWeight + businessAdj.businessBoost, 1.0)\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const intelligentHashtagMixer = new IntelligentHashtagMixer();\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;;AA+BO,MAAM;IACH,cAAgF,IAAI,MAAM;IACjF,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAa,qBACX,gBAAyC,EACzC,aAAmC,EACnC,OAAsB,EACO;QAC7B,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;QACvC,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QAEpC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;YAC/D,OAAO,OAAO,QAAQ;QACxB;QAEA,IAAI;YACF,0CAA0C;YAC1C,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;YAC9D,MAAM,iBAAiB,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa;YAEhE,gDAAgD;YAChD,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB;YAEhE,mCAAmC;YACnC,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,eAAe,kBAAkB;YAEnE,2CAA2C;YAC3C,MAAM,WAAW,IAAI,CAAC,sBAAsB,CAAC,eAAe;YAE5D,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU;gBAAE;gBAAU,WAAW,KAAK,GAAG;YAAG;YAEjE,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAQ,mBACN,gBAAyC,EACzC,aAAmC,EAC2B;QAC9D,MAAM,WAAyE,EAAE;QAEjF,gDAAgD;QAChD,iBAAiB,oBAAoB,CAAC,OAAO,CAAC,CAAA;YAC5C,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAgB,UAAU;YAAG;QAChE;QAEA,iBAAiB,WAAW,CAAC,OAAO,CAAC,CAAA;YACnC,SAAS,IAAI,CAAC;gBAAE,SAAS,SAAS,OAAO;gBAAE,QAAQ;gBAAgB,UAAU;YAAE;QACjF;QAEA,iBAAiB,cAAc,CAAC,OAAO,CAAC,CAAA;YACtC,SAAS,IAAI,CAAC;gBAAE,SAAS,SAAS,OAAO;gBAAE,QAAQ;gBAAgB,UAAU;YAAE;QACjF;QAEA,iBAAiB,iBAAiB,CAAC,OAAO,CAAC,CAAA;YACzC,SAAS,IAAI,CAAC;gBAAE,SAAS,SAAS,OAAO;gBAAE,QAAQ;gBAAgB,UAAU;YAAE;QACjF;QAEA,4CAA4C;QAC5C,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC7B,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAkB,UAAU;YAAE;QACjE;QAEA,cAAc,KAAK,CAAC,OAAO,CAAC,CAAA;YAC1B,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAoB,UAAU;YAAE;QACnE;QAEA,cAAc,KAAK,CAAC,OAAO,CAAC,CAAA;YAC1B,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAe,UAAU;YAAE;QAC9D;QAEA,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC7B,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAkB,UAAU;YAAE;QACjE;QAEA,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAA;YAC7B,SAAS,IAAI,CAAC;gBAAE;gBAAS,QAAQ;gBAAkB,UAAU;YAAE;QACjE;QAEA,sDAAsD;QACtD,MAAM,iBAAiB,IAAI;QAE3B,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,WAAW,eAAe,GAAG,CAAC,KAAK,OAAO;YAChD,IAAI,CAAC,YAAY,KAAK,QAAQ,GAAG,SAAS,QAAQ,EAAE;gBAClD,eAAe,GAAG,CAAC,KAAK,OAAO,EAAE;YACnC;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,eAAe,MAAM;IACzC;IAEA;;GAEC,GACD,MAAc,iBACZ,QAAsE,EACtE,OAAsB,EACsE;QAC5F,MAAM,iBAAiB;YACrB,cAAc,QAAQ,YAAY;YAClC,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,gBAAgB,QAAQ,cAAc;YACtC,UAAU,QAAQ,QAAQ;QAC5B;QAEA,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CACtC,SAAS,GAAG,CAAC,OAAM,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,OAAO,MAAM,4IAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC,KAAK,OAAO,EAAE;YAChE,CAAC;QAGH,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBACN,cAAiG,EACjG,OAAsB,EACiF;QAEvG,OAAO,eAAe,GAAG,CAAC,CAAA;YACxB,IAAI,aAAa;YAEjB,kCAAkC;YAClC,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG;YAEtC,6BAA6B;YAC7B,cAAc,KAAK,QAAQ,GAAG;YAE9B,+BAA+B;YAC/B,OAAQ,QAAQ,QAAQ;gBACtB,KAAK;oBACH,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG;oBACnD,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG;oBACzD;gBAEF,KAAK;oBACH,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;oBACvD,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;oBACvD;gBAEF,KAAK;oBACH,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG;oBACzD,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG;oBAC1D;gBAEF,KAAK;gBACL;oBACE,cAAc,CACZ,KAAK,KAAK,CAAC,SAAS,CAAC,aAAa,GAClC,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB,GACtC,KAAK,KAAK,CAAC,SAAS,CAAC,mBAAmB,AAC1C,IAAI;oBACJ;YACJ;YAEA,wBAAwB;YACxB,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ;gBAC/B,cAAc,aAAa,QAAQ,SAAS,GAAG;YACjD;YAEA,6BAA6B;YAC7B,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,eAAe,KAAK,MAAM,CAAC,QAAQ,CAAC,UAAU;gBACrE,cAAc,aAAa,QAAQ,cAAc,GAAG;YACtD;YAEA,mBAAmB;YACnB,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG;YAEtC,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY,KAAK,KAAK,CAAC,aAAa,MAAM;YAC5C;QACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAC/C;IAEA;;GAEC,GACD,AAAQ,WACN,aAAoH,EACpH,gBAAyC,EACzC,OAAsB,EACW;QACjC,MAAM,QAAQ,cAAc,KAAK,CAAC,GAAG;QAErC,0BAA0B;QAC1B,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAC5E,MAAM,eAAe,KAAK,KAAK,CAAC,AAAC,cAAc,KAAM;QAErD,uCAAuC;QACvC,MAAM,uBAAuB,MAAM,MAAM,CAAC,CAAC,KAAK,OAC9C,MAAM,KAAK,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE,KAAK;QAErD,mCAAmC;QACnC,MAAM,mBAAmB,MAAM,MAAM,CAAC,CAAC,KAAK,OAC1C,MAAM,KAAK,KAAK,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK;QAEjD,4BAA4B;QAC5B,MAAM,UAAU,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;QACrD,MAAM,iBAAiB,KAAK,GAAG,CAAC,AAAC,QAAQ,IAAI,GAAG,IAAK,IAAI,KAAK,0BAA0B;QAExF,+BAA+B;QAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,CAAC,UAAU,EAAE,KAAK;QACpF,MAAM,kBAAkB,KAAK,KAAK,CAAC,gBAAgB;QAEnD,wCAAwC;QACxC,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,SAAS,cAAc;QAE1E,OAAO;YACL;YACA,mBAAmB,KAAK,KAAK,CAAC,uBAAuB,MAAM;YAC3D,eAAe,KAAK,KAAK,CAAC,mBAAmB,MAAM;YACnD,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,MAAM;YAClD;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,uBACN,aAAoH,EACpH,SAA0C,EACtB;QACpB,MAAM,QAAQ,cAAc,KAAK,CAAC,GAAG;QAErC,OAAO;YACL,SAAS,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;YACnD,WAAW,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;YACtD,UAAU,MAAM,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;YACtD,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;YACrC;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,uBACN,OAAsB,EACtB,YAAoB,EACpB,iBAAyB,EACjB;QACR,IAAI,WAAW,GAAG,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC;QAEvG,IAAI,gBAAgB,IAAI;YACtB,YAAY;QACd,OAAO,IAAI,gBAAgB,IAAI;YAC7B,YAAY;QACd,OAAO;YACL,YAAY;QACd;QAEA,IAAI,qBAAqB,GAAG;YAC1B,YAAY;QACd,OAAO,IAAI,qBAAqB,GAAG;YACjC,YAAY;QACd,OAAO;YACL,YAAY;QACd;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,OAAsB,EAAU;QACvD,OAAO,GAAG,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,cAAc,EAAE,CAAC,WAAW;IACzJ;IAEA;;GAEC,GACD,AAAQ,eAAe,OAAsB,EAAsB;QACjE,MAAM,mBAAmB;YACvB;YAAa;YAAU,CAAC,CAAC,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC,QAAQ,KAAK;YACrE;YAAU;YAAc;YAAa;YAAY;YACjD;YAAY;YAAQ;YAAY;YAAS;YAAY;YAAS;SAC/D;QAED,OAAO;YACL,SAAS,iBAAiB,KAAK,CAAC,GAAG;YACnC,WAAW,iBAAiB,KAAK,CAAC,GAAG;YACrC,UAAU,iBAAiB,KAAK,CAAC,IAAI;YACrC,OAAO;YACP,WAAW;gBACT,cAAc;gBACd,mBAAmB;gBACnB,eAAe;gBACf,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;YAClB;QACF;IACF;IAEA;;GAEC,GACD,AAAO,kBAAkB,YAAoB,EAAE,QAAgB,EAAiD;QAC9G,4BAA4B;QAC5B,MAAM,kBAAkB;YACtB,WAAW;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;YACjD,QAAQ;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;YAC9C,SAAS;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;YAC/C,UAAU;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;YAChD,UAAU;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;YAChD,WAAW;gBAAE,WAAW;gBAAK,gBAAgB;YAAI;QACnD;QAEA,4BAA4B;QAC5B,MAAM,sBAAsB;YAC1B,YAAY;gBAAE,UAAU;gBAAK,eAAe;YAAI;YAChD,QAAQ;gBAAE,UAAU;gBAAK,eAAe;YAAI;YAC5C,YAAY;gBAAE,UAAU;gBAAK,eAAe;YAAI;YAChD,YAAY;gBAAE,UAAU;gBAAK,eAAe;YAAI;YAChD,SAAS;gBAAE,UAAU;gBAAK,eAAe;YAAI;YAC7C,QAAQ;gBAAE,UAAU;gBAAK,eAAe;YAAI;QAC9C;QAEA,MAAM,iBAAiB,eAAe,CAAC,SAAS,WAAW,GAAmC,IACxE;YAAE,WAAW;YAAK,gBAAgB;QAAI;QAE5D,MAAM,cAAc,mBAAmB,CAAC,aAAa,WAAW,GAAuC,IACpF;YAAE,UAAU;YAAK,eAAe;QAAI;QAEvD,OAAO;YACL,WAAW,KAAK,GAAG,CAAC,eAAe,SAAS,GAAG,YAAY,QAAQ,EAAE;YACrE,gBAAgB,KAAK,GAAG,CAAC,eAAe,cAAc,GAAG,YAAY,aAAa,EAAE;QACtF;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 2707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/hashtag-performance-tracker.ts"], "sourcesContent": ["/**\r\n * Hashtag Performance Tracking and Learning System\r\n * Tracks hashtag performance and learns from successful combinations\r\n * to improve future hashtag generation with machine learning insights\r\n */\r\n\r\nexport interface HashtagPerformanceData {\r\n  hashtag: string;\r\n  usageCount: number;\r\n  totalEngagement: number;\r\n  averageEngagement: number;\r\n  platforms: Record<string, {\r\n    usage: number;\r\n    engagement: number;\r\n    avgEngagement: number;\r\n  }>;\r\n  businessTypes: Record<string, {\r\n    usage: number;\r\n    engagement: number;\r\n    avgEngagement: number;\r\n  }>;\r\n  locations: Record<string, {\r\n    usage: number;\r\n    engagement: number;\r\n    avgEngagement: number;\r\n  }>;\r\n  timePatterns: {\r\n    hourly: Record<number, { usage: number; engagement: number }>;\r\n    daily: Record<number, { usage: number; engagement: number }>;\r\n    monthly: Record<number, { usage: number; engagement: number }>;\r\n  };\r\n  lastUsed: Date;\r\n  firstUsed: Date;\r\n  trendingScore: number;\r\n  successRate: number; // Percentage of successful posts using this hashtag\r\n}\r\n\r\nexport interface HashtagCombinationData {\r\n  combination: string[]; // Array of hashtags used together\r\n  usageCount: number;\r\n  totalEngagement: number;\r\n  averageEngagement: number;\r\n  successRate: number;\r\n  businessType: string;\r\n  platform: string;\r\n  location: string;\r\n  lastUsed: Date;\r\n  performanceScore: number;\r\n}\r\n\r\nexport interface PerformanceInsights {\r\n  topPerformingHashtags: Array<{\r\n    hashtag: string;\r\n    avgEngagement: number;\r\n    successRate: number;\r\n    recommendationStrength: 'high' | 'medium' | 'low';\r\n  }>;\r\n  \r\n  bestCombinations: Array<{\r\n    hashtags: string[];\r\n    avgEngagement: number;\r\n    successRate: number;\r\n    context: string;\r\n  }>;\r\n  \r\n  platformInsights: Record<string, {\r\n    bestHashtags: string[];\r\n    avgEngagement: number;\r\n    optimalCount: number;\r\n  }>;\r\n  \r\n  businessTypeInsights: Record<string, {\r\n    bestHashtags: string[];\r\n    avgEngagement: number;\r\n    successPatterns: string[];\r\n  }>;\r\n  \r\n  temporalInsights: {\r\n    bestTimes: Array<{ hour: number; day: number; performance: number }>;\r\n    seasonalTrends: Record<string, string[]>;\r\n  };\r\n  \r\n  learningRecommendations: string[];\r\n}\r\n\r\nexport interface PostPerformanceData {\r\n  postId: string;\r\n  hashtags: string[];\r\n  platform: string;\r\n  businessType: string;\r\n  location: string;\r\n  timestamp: Date;\r\n  engagement: {\r\n    likes: number;\r\n    comments: number;\r\n    shares: number;\r\n    views?: number;\r\n    clicks?: number;\r\n    total: number;\r\n  };\r\n  reach?: number;\r\n  impressions?: number;\r\n  success: boolean; // Whether the post met success criteria\r\n}\r\n\r\nexport class HashtagPerformanceTracker {\r\n  private performanceData: Map<string, HashtagPerformanceData> = new Map();\r\n  private combinationData: Map<string, HashtagCombinationData> = new Map();\r\n  private postHistory: PostPerformanceData[] = [];\r\n  \r\n  private readonly storageKey = 'hashtag_performance_data';\r\n  private readonly combinationStorageKey = 'hashtag_combination_data';\r\n  private readonly postHistoryKey = 'post_performance_history';\r\n\r\n  constructor() {\r\n    this.loadPerformanceData();\r\n  }\r\n\r\n  /**\r\n   * Track performance of a post with its hashtags\r\n   */\r\n  public trackPostPerformance(postData: PostPerformanceData): void {\r\n    // Store post data\r\n    this.postHistory.push(postData);\r\n    \r\n    // Update individual hashtag performance\r\n    postData.hashtags.forEach(hashtag => {\r\n      this.updateHashtagPerformance(hashtag, postData);\r\n    });\r\n\r\n    // Update combination performance\r\n    this.updateCombinationPerformance(postData);\r\n\r\n    // Save to storage\r\n    this.savePerformanceData();\r\n  }\r\n\r\n  /**\r\n   * Get performance insights for hashtag optimization\r\n   */\r\n  public getPerformanceInsights(\r\n    businessType?: string,\r\n    platform?: string,\r\n    location?: string\r\n  ): PerformanceInsights {\r\n    const filteredData = this.filterPerformanceData(businessType, platform, location);\r\n    \r\n    return {\r\n      topPerformingHashtags: this.getTopPerformingHashtags(filteredData),\r\n      bestCombinations: this.getBestCombinations(businessType, platform, location),\r\n      platformInsights: this.getPlatformInsights(),\r\n      businessTypeInsights: this.getBusinessTypeInsights(),\r\n      temporalInsights: this.getTemporalInsights(),\r\n      learningRecommendations: this.generateLearningRecommendations(filteredData)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get hashtag recommendations based on learning\r\n   */\r\n  public getLearnedRecommendations(\r\n    businessType: string,\r\n    platform: string,\r\n    location: string,\r\n    count: number = 10\r\n  ): Array<{ hashtag: string; confidence: number; reason: string }> {\r\n    const recommendations: Array<{ hashtag: string; confidence: number; reason: string }> = [];\r\n    \r\n    // Get hashtags that performed well for similar contexts\r\n    const contextualData = Array.from(this.performanceData.values())\r\n      .filter(data => {\r\n        const businessMatch = data.businessTypes[businessType]?.avgEngagement > 0;\r\n        const platformMatch = data.platforms[platform]?.avgEngagement > 0;\r\n        const locationMatch = data.locations[location]?.avgEngagement > 0;\r\n        \r\n        return businessMatch || platformMatch || locationMatch;\r\n      })\r\n      .sort((a, b) => b.averageEngagement - a.averageEngagement);\r\n\r\n    contextualData.slice(0, count).forEach(data => {\r\n      let confidence = 0;\r\n      let reason = '';\r\n\r\n      // Calculate confidence based on performance and context match\r\n      if (data.businessTypes[businessType]) {\r\n        confidence += 0.4 * (data.businessTypes[businessType].avgEngagement / 100);\r\n        reason += `Strong performance in ${businessType} (${data.businessTypes[businessType].avgEngagement.toFixed(1)} avg engagement). `;\r\n      }\r\n\r\n      if (data.platforms[platform]) {\r\n        confidence += 0.3 * (data.platforms[platform].avgEngagement / 100);\r\n        reason += `Good ${platform} performance (${data.platforms[platform].avgEngagement.toFixed(1)} avg). `;\r\n      }\r\n\r\n      if (data.locations[location]) {\r\n        confidence += 0.2 * (data.locations[location].avgEngagement / 100);\r\n        reason += `Local relevance in ${location}. `;\r\n      }\r\n\r\n      confidence += 0.1 * data.successRate;\r\n      reason += `${data.successRate.toFixed(1)}% success rate over ${data.usageCount} uses.`;\r\n\r\n      recommendations.push({\r\n        hashtag: data.hashtag,\r\n        confidence: Math.min(confidence, 1),\r\n        reason: reason.trim()\r\n      });\r\n    });\r\n\r\n    return recommendations.sort((a, b) => b.confidence - a.confidence);\r\n  }\r\n\r\n  /**\r\n   * Update individual hashtag performance\r\n   */\r\n  private updateHashtagPerformance(hashtag: string, postData: PostPerformanceData): void {\r\n    let data = this.performanceData.get(hashtag);\r\n    \r\n    if (!data) {\r\n      data = {\r\n        hashtag,\r\n        usageCount: 0,\r\n        totalEngagement: 0,\r\n        averageEngagement: 0,\r\n        platforms: {},\r\n        businessTypes: {},\r\n        locations: {},\r\n        timePatterns: {\r\n          hourly: {},\r\n          daily: {},\r\n          monthly: {}\r\n        },\r\n        lastUsed: postData.timestamp,\r\n        firstUsed: postData.timestamp,\r\n        trendingScore: 0,\r\n        successRate: 0\r\n      };\r\n    }\r\n\r\n    // Update basic metrics\r\n    data.usageCount++;\r\n    data.totalEngagement += postData.engagement.total;\r\n    data.averageEngagement = data.totalEngagement / data.usageCount;\r\n    data.lastUsed = postData.timestamp;\r\n\r\n    // Update platform performance\r\n    if (!data.platforms[postData.platform]) {\r\n      data.platforms[postData.platform] = { usage: 0, engagement: 0, avgEngagement: 0 };\r\n    }\r\n    data.platforms[postData.platform].usage++;\r\n    data.platforms[postData.platform].engagement += postData.engagement.total;\r\n    data.platforms[postData.platform].avgEngagement = \r\n      data.platforms[postData.platform].engagement / data.platforms[postData.platform].usage;\r\n\r\n    // Update business type performance\r\n    if (!data.businessTypes[postData.businessType]) {\r\n      data.businessTypes[postData.businessType] = { usage: 0, engagement: 0, avgEngagement: 0 };\r\n    }\r\n    data.businessTypes[postData.businessType].usage++;\r\n    data.businessTypes[postData.businessType].engagement += postData.engagement.total;\r\n    data.businessTypes[postData.businessType].avgEngagement = \r\n      data.businessTypes[postData.businessType].engagement / data.businessTypes[postData.businessType].usage;\r\n\r\n    // Update location performance\r\n    if (!data.locations[postData.location]) {\r\n      data.locations[postData.location] = { usage: 0, engagement: 0, avgEngagement: 0 };\r\n    }\r\n    data.locations[postData.location].usage++;\r\n    data.locations[postData.location].engagement += postData.engagement.total;\r\n    data.locations[postData.location].avgEngagement = \r\n      data.locations[postData.location].engagement / data.locations[postData.location].usage;\r\n\r\n    // Update time patterns\r\n    const hour = postData.timestamp.getHours();\r\n    const day = postData.timestamp.getDay();\r\n    const month = postData.timestamp.getMonth();\r\n\r\n    if (!data.timePatterns.hourly[hour]) {\r\n      data.timePatterns.hourly[hour] = { usage: 0, engagement: 0 };\r\n    }\r\n    data.timePatterns.hourly[hour].usage++;\r\n    data.timePatterns.hourly[hour].engagement += postData.engagement.total;\r\n\r\n    if (!data.timePatterns.daily[day]) {\r\n      data.timePatterns.daily[day] = { usage: 0, engagement: 0 };\r\n    }\r\n    data.timePatterns.daily[day].usage++;\r\n    data.timePatterns.daily[day].engagement += postData.engagement.total;\r\n\r\n    if (!data.timePatterns.monthly[month]) {\r\n      data.timePatterns.monthly[month] = { usage: 0, engagement: 0 };\r\n    }\r\n    data.timePatterns.monthly[month].usage++;\r\n    data.timePatterns.monthly[month].engagement += postData.engagement.total;\r\n\r\n    // Update success rate\r\n    const successfulPosts = this.postHistory.filter(post => \r\n      post.hashtags.includes(hashtag) && post.success\r\n    ).length;\r\n    data.successRate = (successfulPosts / data.usageCount) * 100;\r\n\r\n    this.performanceData.set(hashtag, data);\r\n  }\r\n\r\n  /**\r\n   * Update combination performance\r\n   */\r\n  private updateCombinationPerformance(postData: PostPerformanceData): void {\r\n    const combinationKey = postData.hashtags.sort().join('|');\r\n    let data = this.combinationData.get(combinationKey);\r\n\r\n    if (!data) {\r\n      data = {\r\n        combination: postData.hashtags.sort(),\r\n        usageCount: 0,\r\n        totalEngagement: 0,\r\n        averageEngagement: 0,\r\n        successRate: 0,\r\n        businessType: postData.businessType,\r\n        platform: postData.platform,\r\n        location: postData.location,\r\n        lastUsed: postData.timestamp,\r\n        performanceScore: 0\r\n      };\r\n    }\r\n\r\n    data.usageCount++;\r\n    data.totalEngagement += postData.engagement.total;\r\n    data.averageEngagement = data.totalEngagement / data.usageCount;\r\n    data.lastUsed = postData.timestamp;\r\n\r\n    // Calculate success rate for this combination\r\n    const combinationPosts = this.postHistory.filter(post => \r\n      post.hashtags.sort().join('|') === combinationKey\r\n    );\r\n    const successfulCombinationPosts = combinationPosts.filter(post => post.success);\r\n    data.successRate = (successfulCombinationPosts.length / combinationPosts.length) * 100;\r\n\r\n    // Calculate performance score (weighted average of engagement and success rate)\r\n    data.performanceScore = (data.averageEngagement * 0.7) + (data.successRate * 0.3);\r\n\r\n    this.combinationData.set(combinationKey, data);\r\n  }\r\n\r\n  /**\r\n   * Get top performing hashtags\r\n   */\r\n  private getTopPerformingHashtags(\r\n    data: HashtagPerformanceData[]\r\n  ): PerformanceInsights['topPerformingHashtags'] {\r\n    return data\r\n      .filter(d => d.usageCount >= 3) // Minimum usage for reliability\r\n      .sort((a, b) => b.averageEngagement - a.averageEngagement)\r\n      .slice(0, 20)\r\n      .map(d => ({\r\n        hashtag: d.hashtag,\r\n        avgEngagement: d.averageEngagement,\r\n        successRate: d.successRate,\r\n        recommendationStrength: this.getRecommendationStrength(d)\r\n      }));\r\n  }\r\n\r\n  /**\r\n   * Get best hashtag combinations\r\n   */\r\n  private getBestCombinations(\r\n    businessType?: string,\r\n    platform?: string,\r\n    location?: string\r\n  ): PerformanceInsights['bestCombinations'] {\r\n    return Array.from(this.combinationData.values())\r\n      .filter(data => {\r\n        if (businessType && data.businessType !== businessType) return false;\r\n        if (platform && data.platform !== platform) return false;\r\n        if (location && data.location !== location) return false;\r\n        return data.usageCount >= 2;\r\n      })\r\n      .sort((a, b) => b.performanceScore - a.performanceScore)\r\n      .slice(0, 10)\r\n      .map(data => ({\r\n        hashtags: data.combination,\r\n        avgEngagement: data.averageEngagement,\r\n        successRate: data.successRate,\r\n        context: `${data.businessType} on ${data.platform} in ${data.location}`\r\n      }));\r\n  }\r\n\r\n  /**\r\n   * Get platform-specific insights\r\n   */\r\n  private getPlatformInsights(): PerformanceInsights['platformInsights'] {\r\n    const insights: PerformanceInsights['platformInsights'] = {};\r\n    \r\n    // Group data by platform\r\n    const platformData: Record<string, HashtagPerformanceData[]> = {};\r\n    \r\n    this.performanceData.forEach(data => {\r\n      Object.keys(data.platforms).forEach(platform => {\r\n        if (!platformData[platform]) platformData[platform] = [];\r\n        platformData[platform].push(data);\r\n      });\r\n    });\r\n\r\n    // Generate insights for each platform\r\n    Object.entries(platformData).forEach(([platform, data]) => {\r\n      const sortedData = data\r\n        .filter(d => d.platforms[platform].usage >= 2)\r\n        .sort((a, b) => b.platforms[platform].avgEngagement - a.platforms[platform].avgEngagement);\r\n\r\n      insights[platform] = {\r\n        bestHashtags: sortedData.slice(0, 10).map(d => d.hashtag),\r\n        avgEngagement: sortedData.reduce((sum, d) => sum + d.platforms[platform].avgEngagement, 0) / sortedData.length,\r\n        optimalCount: this.calculateOptimalHashtagCount(platform)\r\n      };\r\n    });\r\n\r\n    return insights;\r\n  }\r\n\r\n  /**\r\n   * Get business type insights\r\n   */\r\n  private getBusinessTypeInsights(): PerformanceInsights['businessTypeInsights'] {\r\n    const insights: PerformanceInsights['businessTypeInsights'] = {};\r\n    \r\n    // Group data by business type\r\n    const businessData: Record<string, HashtagPerformanceData[]> = {};\r\n    \r\n    this.performanceData.forEach(data => {\r\n      Object.keys(data.businessTypes).forEach(businessType => {\r\n        if (!businessData[businessType]) businessData[businessType] = [];\r\n        businessData[businessType].push(data);\r\n      });\r\n    });\r\n\r\n    // Generate insights for each business type\r\n    Object.entries(businessData).forEach(([businessType, data]) => {\r\n      const sortedData = data\r\n        .filter(d => d.businessTypes[businessType].usage >= 2)\r\n        .sort((a, b) => b.businessTypes[businessType].avgEngagement - a.businessTypes[businessType].avgEngagement);\r\n\r\n      insights[businessType] = {\r\n        bestHashtags: sortedData.slice(0, 8).map(d => d.hashtag),\r\n        avgEngagement: sortedData.reduce((sum, d) => sum + d.businessTypes[businessType].avgEngagement, 0) / sortedData.length,\r\n        successPatterns: this.identifySuccessPatterns(businessType)\r\n      };\r\n    });\r\n\r\n    return insights;\r\n  }\r\n\r\n  /**\r\n   * Get temporal insights\r\n   */\r\n  private getTemporalInsights(): PerformanceInsights['temporalInsights'] {\r\n    // Analyze best posting times\r\n    const timePerformance: Array<{ hour: number; day: number; performance: number }> = [];\r\n    \r\n    for (let day = 0; day < 7; day++) {\r\n      for (let hour = 0; hour < 24; hour++) {\r\n        const posts = this.postHistory.filter(post => \r\n          post.timestamp.getDay() === day && post.timestamp.getHours() === hour\r\n        );\r\n        \r\n        if (posts.length >= 3) {\r\n          const avgEngagement = posts.reduce((sum, post) => sum + post.engagement.total, 0) / posts.length;\r\n          timePerformance.push({ hour, day, performance: avgEngagement });\r\n        }\r\n      }\r\n    }\r\n\r\n    const bestTimes = timePerformance\r\n      .sort((a, b) => b.performance - a.performance)\r\n      .slice(0, 10);\r\n\r\n    // Seasonal trends (simplified)\r\n    const seasonalTrends = {\r\n      spring: this.getSeasonalHashtags([2, 3, 4]),\r\n      summer: this.getSeasonalHashtags([5, 6, 7]),\r\n      fall: this.getSeasonalHashtags([8, 9, 10]),\r\n      winter: this.getSeasonalHashtags([11, 0, 1])\r\n    };\r\n\r\n    return {\r\n      bestTimes,\r\n      seasonalTrends\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate learning recommendations\r\n   */\r\n  private generateLearningRecommendations(data: HashtagPerformanceData[]): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    // Analyze performance patterns\r\n    const highPerformers = data.filter(d => d.averageEngagement > 50 && d.successRate > 70);\r\n    const lowPerformers = data.filter(d => d.averageEngagement < 10 || d.successRate < 30);\r\n\r\n    if (highPerformers.length > 0) {\r\n      recommendations.push(`Focus on high-performing hashtags like ${highPerformers.slice(0, 3).map(d => d.hashtag).join(', ')}`);\r\n    }\r\n\r\n    if (lowPerformers.length > 5) {\r\n      recommendations.push(`Consider replacing underperforming hashtags: ${lowPerformers.slice(0, 3).map(d => d.hashtag).join(', ')}`);\r\n    }\r\n\r\n    // Platform-specific recommendations\r\n    const platformPerformance = this.analyzePlatformPerformance();\r\n    Object.entries(platformPerformance).forEach(([platform, perf]) => {\r\n      if (perf.avgEngagement > 0) {\r\n        recommendations.push(`${platform} performs best with ${perf.optimalCount} hashtags, focus on ${perf.topHashtag}`);\r\n      }\r\n    });\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Helper methods\r\n   */\r\n  private filterPerformanceData(\r\n    businessType?: string,\r\n    platform?: string,\r\n    location?: string\r\n  ): HashtagPerformanceData[] {\r\n    return Array.from(this.performanceData.values()).filter(data => {\r\n      if (businessType && !data.businessTypes[businessType]) return false;\r\n      if (platform && !data.platforms[platform]) return false;\r\n      if (location && !data.locations[location]) return false;\r\n      return true;\r\n    });\r\n  }\r\n\r\n  private getRecommendationStrength(data: HashtagPerformanceData): 'high' | 'medium' | 'low' {\r\n    if (data.averageEngagement > 50 && data.successRate > 70) return 'high';\r\n    if (data.averageEngagement > 20 && data.successRate > 50) return 'medium';\r\n    return 'low';\r\n  }\r\n\r\n  private calculateOptimalHashtagCount(platform: string): number {\r\n    const platformPosts = this.postHistory.filter(post => post.platform === platform);\r\n    const countPerformance: Record<number, number[]> = {};\r\n\r\n    platformPosts.forEach(post => {\r\n      const count = post.hashtags.length;\r\n      if (!countPerformance[count]) countPerformance[count] = [];\r\n      countPerformance[count].push(post.engagement.total);\r\n    });\r\n\r\n    let bestCount = 10; // Default\r\n    let bestAvg = 0;\r\n\r\n    Object.entries(countPerformance).forEach(([count, engagements]) => {\r\n      if (engagements.length >= 3) { // Minimum sample size\r\n        const avg = engagements.reduce((sum, eng) => sum + eng, 0) / engagements.length;\r\n        if (avg > bestAvg) {\r\n          bestAvg = avg;\r\n          bestCount = parseInt(count);\r\n        }\r\n      }\r\n    });\r\n\r\n    return bestCount;\r\n  }\r\n\r\n  private identifySuccessPatterns(businessType: string): string[] {\r\n    const patterns: string[] = [];\r\n    const businessPosts = this.postHistory.filter(post => \r\n      post.businessType === businessType && post.success\r\n    );\r\n\r\n    // Analyze common hashtag patterns in successful posts\r\n    const hashtagFrequency: Record<string, number> = {};\r\n    businessPosts.forEach(post => {\r\n      post.hashtags.forEach(hashtag => {\r\n        hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;\r\n      });\r\n    });\r\n\r\n    const commonHashtags = Object.entries(hashtagFrequency)\r\n      .filter(([, count]) => count >= 3)\r\n      .sort(([, a], [, b]) => b - a)\r\n      .slice(0, 5)\r\n      .map(([hashtag]) => hashtag);\r\n\r\n    if (commonHashtags.length > 0) {\r\n      patterns.push(`Common successful hashtags: ${commonHashtags.join(', ')}`);\r\n    }\r\n\r\n    return patterns;\r\n  }\r\n\r\n  private getSeasonalHashtags(months: number[]): string[] {\r\n    const seasonalPosts = this.postHistory.filter(post => \r\n      months.includes(post.timestamp.getMonth())\r\n    );\r\n\r\n    const hashtagFrequency: Record<string, number> = {};\r\n    seasonalPosts.forEach(post => {\r\n      post.hashtags.forEach(hashtag => {\r\n        hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;\r\n      });\r\n    });\r\n\r\n    return Object.entries(hashtagFrequency)\r\n      .sort(([, a], [, b]) => b - a)\r\n      .slice(0, 10)\r\n      .map(([hashtag]) => hashtag);\r\n  }\r\n\r\n  private analyzePlatformPerformance(): Record<string, { avgEngagement: number; optimalCount: number; topHashtag: string }> {\r\n    const analysis: Record<string, { avgEngagement: number; optimalCount: number; topHashtag: string }> = {};\r\n\r\n    // Group by platform\r\n    const platformGroups: Record<string, PostPerformanceData[]> = {};\r\n    this.postHistory.forEach(post => {\r\n      if (!platformGroups[post.platform]) platformGroups[post.platform] = [];\r\n      platformGroups[post.platform].push(post);\r\n    });\r\n\r\n    Object.entries(platformGroups).forEach(([platform, posts]) => {\r\n      const avgEngagement = posts.reduce((sum, post) => sum + post.engagement.total, 0) / posts.length;\r\n      const optimalCount = this.calculateOptimalHashtagCount(platform);\r\n      \r\n      // Find top hashtag for this platform\r\n      const hashtagPerf: Record<string, number[]> = {};\r\n      posts.forEach(post => {\r\n        post.hashtags.forEach(hashtag => {\r\n          if (!hashtagPerf[hashtag]) hashtagPerf[hashtag] = [];\r\n          hashtagPerf[hashtag].push(post.engagement.total);\r\n        });\r\n      });\r\n\r\n      const topHashtag = Object.entries(hashtagPerf)\r\n        .filter(([, engagements]) => engagements.length >= 2)\r\n        .map(([hashtag, engagements]) => ({\r\n          hashtag,\r\n          avg: engagements.reduce((sum, eng) => sum + eng, 0) / engagements.length\r\n        }))\r\n        .sort((a, b) => b.avg - a.avg)[0]?.hashtag || '';\r\n\r\n      analysis[platform] = { avgEngagement, optimalCount, topHashtag };\r\n    });\r\n\r\n    return analysis;\r\n  }\r\n\r\n  /**\r\n   * Storage methods\r\n   */\r\n  private loadPerformanceData(): void {\r\n    try {\r\n      const performanceData = localStorage.getItem(this.storageKey);\r\n      if (performanceData) {\r\n        const parsed = JSON.parse(performanceData);\r\n        this.performanceData = new Map(Object.entries(parsed));\r\n      }\r\n\r\n      const combinationData = localStorage.getItem(this.combinationStorageKey);\r\n      if (combinationData) {\r\n        const parsed = JSON.parse(combinationData);\r\n        this.combinationData = new Map(Object.entries(parsed));\r\n      }\r\n\r\n      const postHistory = localStorage.getItem(this.postHistoryKey);\r\n      if (postHistory) {\r\n        this.postHistory = JSON.parse(postHistory).map((post: any) => ({\r\n          ...post,\r\n          timestamp: new Date(post.timestamp)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      // Initialize with empty data if loading fails\r\n    }\r\n  }\r\n\r\n  private savePerformanceData(): void {\r\n    try {\r\n      // Save performance data\r\n      const performanceObj = Object.fromEntries(this.performanceData);\r\n      localStorage.setItem(this.storageKey, JSON.stringify(performanceObj));\r\n\r\n      // Save combination data\r\n      const combinationObj = Object.fromEntries(this.combinationData);\r\n      localStorage.setItem(this.combinationStorageKey, JSON.stringify(combinationObj));\r\n\r\n      // Save post history (keep only last 1000 posts)\r\n      const recentHistory = this.postHistory.slice(-1000);\r\n      localStorage.setItem(this.postHistoryKey, JSON.stringify(recentHistory));\r\n    } catch (error) {\r\n      // Handle storage errors gracefully\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all performance data (for testing or reset)\r\n   */\r\n  public clearPerformanceData(): void {\r\n    this.performanceData.clear();\r\n    this.combinationData.clear();\r\n    this.postHistory = [];\r\n    \r\n    localStorage.removeItem(this.storageKey);\r\n    localStorage.removeItem(this.combinationStorageKey);\r\n    localStorage.removeItem(this.postHistoryKey);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const hashtagPerformanceTracker = new HashtagPerformanceTracker();\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAqGM,MAAM;IACH,kBAAuD,IAAI,MAAM;IACjE,kBAAuD,IAAI,MAAM;IACjE,cAAqC,EAAE,CAAC;IAE/B,aAAa,2BAA2B;IACxC,wBAAwB,2BAA2B;IACnD,iBAAiB,2BAA2B;IAE7D,aAAc;QACZ,IAAI,CAAC,mBAAmB;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAqB,QAA6B,EAAQ;QAC/D,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAEtB,wCAAwC;QACxC,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,wBAAwB,CAAC,SAAS;QACzC;QAEA,iCAAiC;QACjC,IAAI,CAAC,4BAA4B,CAAC;QAElC,kBAAkB;QAClB,IAAI,CAAC,mBAAmB;IAC1B;IAEA;;GAEC,GACD,AAAO,uBACL,YAAqB,EACrB,QAAiB,EACjB,QAAiB,EACI;QACrB,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,cAAc,UAAU;QAExE,OAAO;YACL,uBAAuB,IAAI,CAAC,wBAAwB,CAAC;YACrD,kBAAkB,IAAI,CAAC,mBAAmB,CAAC,cAAc,UAAU;YACnE,kBAAkB,IAAI,CAAC,mBAAmB;YAC1C,sBAAsB,IAAI,CAAC,uBAAuB;YAClD,kBAAkB,IAAI,CAAC,mBAAmB;YAC1C,yBAAyB,IAAI,CAAC,+BAA+B,CAAC;QAChE;IACF;IAEA;;GAEC,GACD,AAAO,0BACL,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAAE,EAC8C;QAChE,MAAM,kBAAkF,EAAE;QAE1F,wDAAwD;QACxD,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,IAC1D,MAAM,CAAC,CAAA;YACN,MAAM,gBAAgB,KAAK,aAAa,CAAC,aAAa,EAAE,gBAAgB;YACxE,MAAM,gBAAgB,KAAK,SAAS,CAAC,SAAS,EAAE,gBAAgB;YAChE,MAAM,gBAAgB,KAAK,SAAS,CAAC,SAAS,EAAE,gBAAgB;YAEhE,OAAO,iBAAiB,iBAAiB;QAC3C,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,iBAAiB,GAAG,EAAE,iBAAiB;QAE3D,eAAe,KAAK,CAAC,GAAG,OAAO,OAAO,CAAC,CAAA;YACrC,IAAI,aAAa;YACjB,IAAI,SAAS;YAEb,8DAA8D;YAC9D,IAAI,KAAK,aAAa,CAAC,aAAa,EAAE;gBACpC,cAAc,MAAM,CAAC,KAAK,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,GAAG;gBACzE,UAAU,CAAC,sBAAsB,EAAE,aAAa,EAAE,EAAE,KAAK,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,kBAAkB,CAAC;YACnI;YAEA,IAAI,KAAK,SAAS,CAAC,SAAS,EAAE;gBAC5B,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,SAAS,CAAC,aAAa,GAAG,GAAG;gBACjE,UAAU,CAAC,KAAK,EAAE,SAAS,cAAc,EAAE,KAAK,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;YACvG;YAEA,IAAI,KAAK,SAAS,CAAC,SAAS,EAAE;gBAC5B,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,SAAS,CAAC,aAAa,GAAG,GAAG;gBACjE,UAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC;YAC9C;YAEA,cAAc,MAAM,KAAK,WAAW;YACpC,UAAU,GAAG,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG,oBAAoB,EAAE,KAAK,UAAU,CAAC,MAAM,CAAC;YAEtF,gBAAgB,IAAI,CAAC;gBACnB,SAAS,KAAK,OAAO;gBACrB,YAAY,KAAK,GAAG,CAAC,YAAY;gBACjC,QAAQ,OAAO,IAAI;YACrB;QACF;QAEA,OAAO,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IACnE;IAEA;;GAEC,GACD,AAAQ,yBAAyB,OAAe,EAAE,QAA6B,EAAQ;QACrF,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAEpC,IAAI,CAAC,MAAM;YACT,OAAO;gBACL;gBACA,YAAY;gBACZ,iBAAiB;gBACjB,mBAAmB;gBACnB,WAAW,CAAC;gBACZ,eAAe,CAAC;gBAChB,WAAW,CAAC;gBACZ,cAAc;oBACZ,QAAQ,CAAC;oBACT,OAAO,CAAC;oBACR,SAAS,CAAC;gBACZ;gBACA,UAAU,SAAS,SAAS;gBAC5B,WAAW,SAAS,SAAS;gBAC7B,eAAe;gBACf,aAAa;YACf;QACF;QAEA,uBAAuB;QACvB,KAAK,UAAU;QACf,KAAK,eAAe,IAAI,SAAS,UAAU,CAAC,KAAK;QACjD,KAAK,iBAAiB,GAAG,KAAK,eAAe,GAAG,KAAK,UAAU;QAC/D,KAAK,QAAQ,GAAG,SAAS,SAAS;QAElC,8BAA8B;QAC9B,IAAI,CAAC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,EAAE;YACtC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,GAAG;gBAAE,OAAO;gBAAG,YAAY;gBAAG,eAAe;YAAE;QAClF;QACA,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,KAAK;QACvC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QACzE,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,aAAa,GAC7C,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,UAAU,GAAG,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,KAAK;QAExF,mCAAmC;QACnC,IAAI,CAAC,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,EAAE;YAC9C,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,GAAG;gBAAE,OAAO;gBAAG,YAAY;gBAAG,eAAe;YAAE;QAC1F;QACA,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,CAAC,KAAK;QAC/C,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QACjF,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,CAAC,aAAa,GACrD,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,CAAC,UAAU,GAAG,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,CAAC,KAAK;QAExG,8BAA8B;QAC9B,IAAI,CAAC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,EAAE;YACtC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,GAAG;gBAAE,OAAO;gBAAG,YAAY;gBAAG,eAAe;YAAE;QAClF;QACA,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,KAAK;QACvC,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QACzE,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,aAAa,GAC7C,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,UAAU,GAAG,KAAK,SAAS,CAAC,SAAS,QAAQ,CAAC,CAAC,KAAK;QAExF,uBAAuB;QACvB,MAAM,OAAO,SAAS,SAAS,CAAC,QAAQ;QACxC,MAAM,MAAM,SAAS,SAAS,CAAC,MAAM;QACrC,MAAM,QAAQ,SAAS,SAAS,CAAC,QAAQ;QAEzC,IAAI,CAAC,KAAK,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE;YACnC,KAAK,YAAY,CAAC,MAAM,CAAC,KAAK,GAAG;gBAAE,OAAO;gBAAG,YAAY;YAAE;QAC7D;QACA,KAAK,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;QACpC,KAAK,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QAEtE,IAAI,CAAC,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;YACjC,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;gBAAE,OAAO;gBAAG,YAAY;YAAE;QAC3D;QACA,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;QAClC,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QAEpE,IAAI,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE;YACrC,KAAK,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG;gBAAE,OAAO;gBAAG,YAAY;YAAE;QAC/D;QACA,KAAK,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;QACtC,KAAK,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,IAAI,SAAS,UAAU,CAAC,KAAK;QAExE,sBAAsB;QACtB,MAAM,kBAAkB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAC9C,KAAK,QAAQ,CAAC,QAAQ,CAAC,YAAY,KAAK,OAAO,EAC/C,MAAM;QACR,KAAK,WAAW,GAAG,AAAC,kBAAkB,KAAK,UAAU,GAAI;QAEzD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;IACpC;IAEA;;GAEC,GACD,AAAQ,6BAA6B,QAA6B,EAAQ;QACxE,MAAM,iBAAiB,SAAS,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;QACrD,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAEpC,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,aAAa,SAAS,QAAQ,CAAC,IAAI;gBACnC,YAAY;gBACZ,iBAAiB;gBACjB,mBAAmB;gBACnB,aAAa;gBACb,cAAc,SAAS,YAAY;gBACnC,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,SAAS;gBAC5B,kBAAkB;YACpB;QACF;QAEA,KAAK,UAAU;QACf,KAAK,eAAe,IAAI,SAAS,UAAU,CAAC,KAAK;QACjD,KAAK,iBAAiB,GAAG,KAAK,eAAe,GAAG,KAAK,UAAU;QAC/D,KAAK,QAAQ,GAAG,SAAS,SAAS;QAElC,8CAA8C;QAC9C,MAAM,mBAAmB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAC/C,KAAK,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;QAErC,MAAM,6BAA6B,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO;QAC/E,KAAK,WAAW,GAAG,AAAC,2BAA2B,MAAM,GAAG,iBAAiB,MAAM,GAAI;QAEnF,gFAAgF;QAChF,KAAK,gBAAgB,GAAG,AAAC,KAAK,iBAAiB,GAAG,MAAQ,KAAK,WAAW,GAAG;QAE7E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB;IAC3C;IAEA;;GAEC,GACD,AAAQ,yBACN,IAA8B,EACgB;QAC9C,OAAO,KACJ,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,GAAG,gCAAgC;SAC/D,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,EACxD,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,IAAK,CAAC;gBACT,SAAS,EAAE,OAAO;gBAClB,eAAe,EAAE,iBAAiB;gBAClC,aAAa,EAAE,WAAW;gBAC1B,wBAAwB,IAAI,CAAC,yBAAyB,CAAC;YACzD,CAAC;IACL;IAEA;;GAEC,GACD,AAAQ,oBACN,YAAqB,EACrB,QAAiB,EACjB,QAAiB,EACwB;QACzC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,IAC1C,MAAM,CAAC,CAAA;YACN,IAAI,gBAAgB,KAAK,YAAY,KAAK,cAAc,OAAO;YAC/D,IAAI,YAAY,KAAK,QAAQ,KAAK,UAAU,OAAO;YACnD,IAAI,YAAY,KAAK,QAAQ,KAAK,UAAU,OAAO;YACnD,OAAO,KAAK,UAAU,IAAI;QAC5B,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,gBAAgB,GAAG,EAAE,gBAAgB,EACtD,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,UAAU,KAAK,WAAW;gBAC1B,eAAe,KAAK,iBAAiB;gBACrC,aAAa,KAAK,WAAW;gBAC7B,SAAS,GAAG,KAAK,YAAY,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC,IAAI,EAAE,KAAK,QAAQ,EAAE;YACzE,CAAC;IACL;IAEA;;GAEC,GACD,AAAQ,sBAA+D;QACrE,MAAM,WAAoD,CAAC;QAE3D,yBAAyB;QACzB,MAAM,eAAyD,CAAC;QAEhE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC3B,OAAO,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,CAAA;gBAClC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,EAAE;gBACxD,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC;YAC9B;QACF;QAEA,sCAAsC;QACtC,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,UAAU,KAAK;YACpD,MAAM,aAAa,KAChB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,IAAI,GAC3C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,aAAa;YAE3F,QAAQ,CAAC,SAAS,GAAG;gBACnB,cAAc,WAAW,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;gBACxD,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,WAAW,MAAM;gBAC9G,cAAc,IAAI,CAAC,4BAA4B,CAAC;YAClD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,0BAAuE;QAC7E,MAAM,WAAwD,CAAC;QAE/D,8BAA8B;QAC9B,MAAM,eAAyD,CAAC;QAEhE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC3B,OAAO,IAAI,CAAC,KAAK,aAAa,EAAE,OAAO,CAAC,CAAA;gBACtC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,GAAG,EAAE;gBAChE,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC;YAClC;QACF;QAEA,2CAA2C;QAC3C,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,cAAc,KAAK;YACxD,MAAM,aAAa,KAChB,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,CAAC,aAAa,CAAC,KAAK,IAAI,GACnD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,EAAE,aAAa,CAAC,aAAa,CAAC,aAAa;YAE3G,QAAQ,CAAC,aAAa,GAAG;gBACvB,cAAc,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;gBACvD,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,WAAW,MAAM;gBACtH,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;YAChD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAA+D;QACrE,6BAA6B;QAC7B,MAAM,kBAA6E,EAAE;QAErF,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAChC,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,OAAQ;gBACpC,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OACpC,KAAK,SAAS,CAAC,MAAM,OAAO,OAAO,KAAK,SAAS,CAAC,QAAQ,OAAO;gBAGnE,IAAI,MAAM,MAAM,IAAI,GAAG;oBACrB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,KAAK,EAAE,KAAK,MAAM,MAAM;oBAChG,gBAAgB,IAAI,CAAC;wBAAE;wBAAM;wBAAK,aAAa;oBAAc;gBAC/D;YACF;QACF;QAEA,MAAM,YAAY,gBACf,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,KAAK,CAAC,GAAG;QAEZ,+BAA+B;QAC/B,MAAM,iBAAiB;YACrB,QAAQ,IAAI,CAAC,mBAAmB,CAAC;gBAAC;gBAAG;gBAAG;aAAE;YAC1C,QAAQ,IAAI,CAAC,mBAAmB,CAAC;gBAAC;gBAAG;gBAAG;aAAE;YAC1C,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBAAC;gBAAG;gBAAG;aAAG;YACzC,QAAQ,IAAI,CAAC,mBAAmB,CAAC;gBAAC;gBAAI;gBAAG;aAAE;QAC7C;QAEA,OAAO;YACL;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gCAAgC,IAA8B,EAAY;QAChF,MAAM,kBAA4B,EAAE;QAEpC,+BAA+B;QAC/B,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,GAAG,MAAM,EAAE,WAAW,GAAG;QACpF,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,GAAG,MAAM,EAAE,WAAW,GAAG;QAEnF,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,gBAAgB,IAAI,CAAC,CAAC,uCAAuC,EAAE,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;QAC5H;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,gBAAgB,IAAI,CAAC,CAAC,6CAA6C,EAAE,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;QACjI;QAEA,oCAAoC;QACpC,MAAM,sBAAsB,IAAI,CAAC,0BAA0B;QAC3D,OAAO,OAAO,CAAC,qBAAqB,OAAO,CAAC,CAAC,CAAC,UAAU,KAAK;YAC3D,IAAI,KAAK,aAAa,GAAG,GAAG;gBAC1B,gBAAgB,IAAI,CAAC,GAAG,SAAS,oBAAoB,EAAE,KAAK,YAAY,CAAC,oBAAoB,EAAE,KAAK,UAAU,EAAE;YAClH;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBACN,YAAqB,EACrB,QAAiB,EACjB,QAAiB,EACS;QAC1B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,MAAM,CAAC,CAAA;YACtD,IAAI,gBAAgB,CAAC,KAAK,aAAa,CAAC,aAAa,EAAE,OAAO;YAC9D,IAAI,YAAY,CAAC,KAAK,SAAS,CAAC,SAAS,EAAE,OAAO;YAClD,IAAI,YAAY,CAAC,KAAK,SAAS,CAAC,SAAS,EAAE,OAAO;YAClD,OAAO;QACT;IACF;IAEQ,0BAA0B,IAA4B,EAA6B;QACzF,IAAI,KAAK,iBAAiB,GAAG,MAAM,KAAK,WAAW,GAAG,IAAI,OAAO;QACjE,IAAI,KAAK,iBAAiB,GAAG,MAAM,KAAK,WAAW,GAAG,IAAI,OAAO;QACjE,OAAO;IACT;IAEQ,6BAA6B,QAAgB,EAAU;QAC7D,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACxE,MAAM,mBAA6C,CAAC;QAEpD,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,QAAQ,KAAK,QAAQ,CAAC,MAAM;YAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,GAAG,EAAE;YAC1D,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK;QACpD;QAEA,IAAI,YAAY,IAAI,UAAU;QAC9B,IAAI,UAAU;QAEd,OAAO,OAAO,CAAC,kBAAkB,OAAO,CAAC,CAAC,CAAC,OAAO,YAAY;YAC5D,IAAI,YAAY,MAAM,IAAI,GAAG;gBAC3B,MAAM,MAAM,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,YAAY,MAAM;gBAC/E,IAAI,MAAM,SAAS;oBACjB,UAAU;oBACV,YAAY,SAAS;gBACvB;YACF;QACF;QAEA,OAAO;IACT;IAEQ,wBAAwB,YAAoB,EAAY;QAC9D,MAAM,WAAqB,EAAE;QAC7B,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAC5C,KAAK,YAAY,KAAK,gBAAgB,KAAK,OAAO;QAGpD,sDAAsD;QACtD,MAAM,mBAA2C,CAAC;QAClD,cAAc,OAAO,CAAC,CAAA;YACpB,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACpB,gBAAgB,CAAC,QAAQ,GAAG,CAAC,gBAAgB,CAAC,QAAQ,IAAI,CAAC,IAAI;YACjE;QACF;QAEA,MAAM,iBAAiB,OAAO,OAAO,CAAC,kBACnC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,SAAS,GAC/B,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,GAC3B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;QAEtB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS,IAAI,CAAC,CAAC,4BAA4B,EAAE,eAAe,IAAI,CAAC,OAAO;QAC1E;QAEA,OAAO;IACT;IAEQ,oBAAoB,MAAgB,EAAY;QACtD,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAC5C,OAAO,QAAQ,CAAC,KAAK,SAAS,CAAC,QAAQ;QAGzC,MAAM,mBAA2C,CAAC;QAClD,cAAc,OAAO,CAAC,CAAA;YACpB,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACpB,gBAAgB,CAAC,QAAQ,GAAG,CAAC,gBAAgB,CAAC,QAAQ,IAAI,CAAC,IAAI;YACjE;QACF;QAEA,OAAO,OAAO,OAAO,CAAC,kBACnB,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,GAC3B,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;IACxB;IAEQ,6BAAkH;QACxH,MAAM,WAAgG,CAAC;QAEvG,oBAAoB;QACpB,MAAM,iBAAwD,CAAC;QAC/D,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YACvB,IAAI,CAAC,cAAc,CAAC,KAAK,QAAQ,CAAC,EAAE,cAAc,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;YACtE,cAAc,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACrC;QAEA,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;YACvD,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,KAAK,EAAE,KAAK,MAAM,MAAM;YAChG,MAAM,eAAe,IAAI,CAAC,4BAA4B,CAAC;YAEvD,qCAAqC;YACrC,MAAM,cAAwC,CAAC;YAC/C,MAAM,OAAO,CAAC,CAAA;gBACZ,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACpB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,GAAG,EAAE;oBACpD,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK;gBACjD;YACF;YAEA,MAAM,aAAa,OAAO,OAAO,CAAC,aAC/B,MAAM,CAAC,CAAC,GAAG,YAAY,GAAK,YAAY,MAAM,IAAI,GAClD,GAAG,CAAC,CAAC,CAAC,SAAS,YAAY,GAAK,CAAC;oBAChC;oBACA,KAAK,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,YAAY,MAAM;gBAC1E,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,WAAW;YAEhD,QAAQ,CAAC,SAAS,GAAG;gBAAE;gBAAe;gBAAc;YAAW;QACjE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI;YACF,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;YAC5D,IAAI,iBAAiB;gBACnB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,OAAO,OAAO,CAAC;YAChD;YAEA,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI,CAAC,qBAAqB;YACvE,IAAI,iBAAiB;gBACnB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,OAAO,OAAO,CAAC;YAChD;YAEA,MAAM,cAAc,aAAa,OAAO,CAAC,IAAI,CAAC,cAAc;YAC5D,IAAI,aAAa;gBACf,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,OAAc,CAAC;wBAC7D,GAAG,IAAI;wBACP,WAAW,IAAI,KAAK,KAAK,SAAS;oBACpC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;QACd,8CAA8C;QAChD;IACF;IAEQ,sBAA4B;QAClC,IAAI;YACF,wBAAwB;YACxB,MAAM,iBAAiB,OAAO,WAAW,CAAC,IAAI,CAAC,eAAe;YAC9D,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;YAErD,wBAAwB;YACxB,MAAM,iBAAiB,OAAO,WAAW,CAAC,IAAI,CAAC,eAAe;YAC9D,aAAa,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,SAAS,CAAC;YAEhE,gDAAgD;YAChD,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC9C,aAAa,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,SAAS,CAAC;QAC3D,EAAE,OAAO,OAAO;QACd,mCAAmC;QACrC;IACF;IAEA;;GAEC,GACD,AAAO,uBAA6B;QAClC,IAAI,CAAC,eAAe,CAAC,KAAK;QAC1B,IAAI,CAAC,eAAe,CAAC,KAAK;QAC1B,IAAI,CAAC,WAAW,GAAG,EAAE;QAErB,aAAa,UAAU,CAAC,IAAI,CAAC,UAAU;QACvC,aAAa,UAAU,CAAC,IAAI,CAAC,qBAAqB;QAClD,aAAa,UAAU,CAAC,IAAI,CAAC,cAAc;IAC7C;AACF;AAGO,MAAM,4BAA4B,IAAI", "debugId": null}}, {"offset": {"line": 3207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/viral-hashtag-engine.ts"], "sourcesContent": ["/**\r\n * Viral Hashtag Engine - Real-time trending hashtag generation\r\n * Integrates with RSS feeds and trending data to generate viral hashtags\r\n * Enhanced with Advanced Trending Hashtag Analyzer for superior relevance\r\n */\r\n\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\nimport { advancedHashtagAnalyzer, AnalysisContext } from './advanced-trending-hashtag-analyzer';\r\nimport { intelligentHashtagMixer, MixingContext } from './intelligent-hashtag-mixer';\r\nimport { hashtagPerformanceTracker } from './hashtag-performance-tracker';\r\n\r\nexport interface ViralHashtagStrategy {\r\n  trending: string[];      // Currently trending hashtags from RSS feeds\r\n  viral: string[];         // High-engagement viral hashtags\r\n  niche: string[];         // Business-specific niche hashtags\r\n  location: string[];      // Location-based hashtags\r\n  community: string[];     // Community engagement hashtags\r\n  seasonal: string[];      // Seasonal/timely hashtags\r\n  platform: string[];     // Platform-specific hashtags\r\n  total: string[];         // Final combined strategy (15 hashtags)\r\n\r\n  // Enhanced analytics from Advanced Hashtag Analyzer\r\n  analytics?: {\r\n    topPerformers: string[];\r\n    emergingTrends: string[];\r\n    businessOptimized: string[];\r\n    rssSourced: string[];\r\n    confidenceScore: number;\r\n\r\n    // Intelligent mixing analytics\r\n    mixingStrategy?: {\r\n      rssInfluence: number;\r\n      businessRelevance: number;\r\n      trendingScore: number;\r\n      diversityScore: number;\r\n      confidenceLevel: number;\r\n      algorithm: string;\r\n    };\r\n\r\n    // Performance learning insights\r\n    learningInsights?: {\r\n      learnedRecommendations: Array<{ hashtag: string; confidence: number; reason: string }>;\r\n      historicalPerformance: number;\r\n      improvementSuggestions: string[];\r\n    };\r\n  };\r\n}\r\n\r\nexport class ViralHashtagEngine {\r\n\r\n  /**\r\n   * Generate viral hashtag strategy using advanced RSS analysis and real-time trending data\r\n   */\r\n  async generateViralHashtags(\r\n    businessType: string,\r\n    businessName: string,\r\n    location: string,\r\n    platform: string,\r\n    services?: string,\r\n    targetAudience?: string\r\n  ): Promise<ViralHashtagStrategy> {\r\n\r\n    try {\r\n      // 🚀 ENHANCED: Use Advanced Hashtag Analyzer for superior RSS integration\r\n      const analysisContext: AnalysisContext = {\r\n        businessType,\r\n        businessName,\r\n        location,\r\n        platform,\r\n        services,\r\n        targetAudience\r\n      };\r\n\r\n      // Get advanced hashtag analysis with RSS integration\r\n      const advancedAnalysis = await advancedHashtagAnalyzer.analyzeHashtagTrends(analysisContext);\r\n\r\n      // Get traditional trending data as backup\r\n      const trendingData = await trendingEnhancer.getTrendingEnhancement({\r\n        businessType,\r\n        location,\r\n        platform,\r\n        targetAudience\r\n      });\r\n\r\n      // 🔥 ENHANCED: Generate hashtag categories using advanced analysis\r\n      const trending = this.extractTrendingFromAnalysis(advancedAnalysis, trendingData);\r\n      const viral = this.getEnhancedViralHashtags(businessType, platform, advancedAnalysis);\r\n      const niche = this.getEnhancedNicheHashtags(businessType, services, advancedAnalysis);\r\n      const location_tags = this.getEnhancedLocationHashtags(location, advancedAnalysis);\r\n      const community = this.getCommunityHashtags(businessType, targetAudience);\r\n      const seasonal = this.getSeasonalHashtags();\r\n      const platform_tags = this.getEnhancedPlatformHashtags(platform, advancedAnalysis);\r\n\r\n      // 🧠 ENHANCED: Use Intelligent Hashtag Mixer for optimal combination\r\n      const mixingContext: MixingContext = {\r\n        businessType,\r\n        businessName,\r\n        location,\r\n        platform,\r\n        postContent: undefined, // Could be passed from content generation\r\n        targetAudience,\r\n        services,\r\n        priority: 'balanced', // Could be configurable\r\n        ...intelligentHashtagMixer.getOptimalWeights(businessType, platform)\r\n      };\r\n\r\n      // Create the current strategy for mixing\r\n      const currentStrategy = {\r\n        trending,\r\n        viral,\r\n        niche,\r\n        location: location_tags,\r\n        community,\r\n        seasonal,\r\n        platform: platform_tags,\r\n        total: [] // Will be filled by mixer\r\n      };\r\n\r\n      // Apply intelligent mixing\r\n      const intelligentMix = await intelligentHashtagMixer.createIntelligentMix(\r\n        advancedAnalysis,\r\n        currentStrategy,\r\n        mixingContext\r\n      );\r\n\r\n      // 🧠 ENHANCED: Get learned recommendations from performance tracking\r\n      const learnedRecommendations = hashtagPerformanceTracker.getLearnedRecommendations(\r\n        businessType,\r\n        platform,\r\n        location,\r\n        5\r\n      );\r\n\r\n      // 📊 Get performance insights for improvement suggestions\r\n      const performanceInsights = hashtagPerformanceTracker.getPerformanceInsights(\r\n        businessType,\r\n        platform,\r\n        location\r\n      );\r\n\r\n      // 🎯 Integrate learned recommendations with intelligent mix\r\n      const enhancedTotal = this.integrateLearnedRecommendations(\r\n        intelligentMix.final,\r\n        learnedRecommendations,\r\n        performanceInsights\r\n      );\r\n\r\n      // Use the enhanced hashtags as the final total\r\n      const total = enhancedTotal;\r\n\r\n      // Calculate confidence score based on RSS data quality\r\n      const confidenceScore = this.calculateConfidenceScore(advancedAnalysis);\r\n\r\n      return {\r\n        trending,\r\n        viral,\r\n        niche,\r\n        location: location_tags,\r\n        community,\r\n        seasonal,\r\n        platform: platform_tags,\r\n        total,\r\n        analytics: {\r\n          topPerformers: advancedAnalysis.finalRecommendations.slice(0, 5),\r\n          emergingTrends: advancedAnalysis.emergingTrends.map(t => t.hashtag).slice(0, 3),\r\n          businessOptimized: advancedAnalysis.businessOptimized.map(b => b.hashtag).slice(0, 3),\r\n          rssSourced: this.extractRSSSourcedHashtags(advancedAnalysis),\r\n          confidenceScore,\r\n\r\n          // Include intelligent mixing analytics\r\n          mixingStrategy: {\r\n            rssInfluence: intelligentMix.analytics.rssInfluence,\r\n            businessRelevance: intelligentMix.analytics.businessRelevance,\r\n            trendingScore: intelligentMix.analytics.trendingScore,\r\n            diversityScore: intelligentMix.analytics.diversityScore,\r\n            confidenceLevel: intelligentMix.analytics.confidenceLevel,\r\n            algorithm: intelligentMix.analytics.mixingStrategy\r\n          },\r\n\r\n          // Include performance learning insights\r\n          learningInsights: {\r\n            learnedRecommendations,\r\n            historicalPerformance: this.calculateHistoricalPerformance(performanceInsights),\r\n            improvementSuggestions: performanceInsights.learningRecommendations\r\n          }\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      return this.getFallbackHashtags(businessType, location, platform);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract trending hashtags from advanced analysis\r\n   */\r\n  private extractTrendingFromAnalysis(advancedAnalysis: any, fallbackData: any): string[] {\r\n    // Prioritize RSS-sourced trending hashtags\r\n    const rssHashtags = advancedAnalysis.topTrending\r\n      .filter((analysis: any) => analysis.sources.some((source: string) =>\r\n        source !== 'business_generator' && source !== 'fallback'\r\n      ))\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Add high-scoring emerging trends\r\n    const emergingHashtags = advancedAnalysis.emergingTrends\r\n      .filter((analysis: any) => analysis.trendingScore >= 3)\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Combine with fallback data if needed\r\n    const combined = [...rssHashtags, ...emergingHashtags, ...fallbackData.hashtags];\r\n\r\n    // Remove duplicates and return top trending\r\n    return Array.from(new Set(combined)).slice(0, 8);\r\n  }\r\n\r\n  /**\r\n   * Get enhanced viral hashtags using RSS analysis\r\n   */\r\n  private getEnhancedViralHashtags(businessType: string, platform: string, advancedAnalysis: any): string[] {\r\n    // Get traditional viral hashtags\r\n    const traditionalViral = this.getViralHashtags(businessType, platform);\r\n\r\n    // Add high-engagement hashtags from RSS analysis\r\n    const rssViral = advancedAnalysis.topTrending\r\n      .filter((analysis: any) => analysis.engagementPotential >= 7)\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Combine and prioritize\r\n    const combined = [...rssViral.slice(0, 4), ...traditionalViral.slice(0, 3)];\r\n    return Array.from(new Set(combined)).slice(0, 7);\r\n  }\r\n\r\n  /**\r\n   * Get enhanced niche hashtags using business analysis\r\n   */\r\n  private getEnhancedNicheHashtags(businessType: string, services: string | undefined, advancedAnalysis: any): string[] {\r\n    // Get traditional niche hashtags\r\n    const traditionalNiche = this.getNicheHashtags(businessType, services);\r\n\r\n    // Add business-optimized hashtags from RSS analysis\r\n    const rssNiche = advancedAnalysis.businessOptimized\r\n      .filter((analysis: any) => analysis.businessRelevance >= 6)\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Combine and prioritize\r\n    const combined = [...rssNiche.slice(0, 3), ...traditionalNiche.slice(0, 3)];\r\n    return Array.from(new Set(combined)).slice(0, 6);\r\n  }\r\n\r\n  /**\r\n   * Get enhanced location hashtags using location analysis\r\n   */\r\n  private getEnhancedLocationHashtags(location: string, advancedAnalysis: any): string[] {\r\n    // Get traditional location hashtags\r\n    const traditionalLocation = this.getLocationHashtags(location);\r\n\r\n    // Add location-specific hashtags from RSS analysis\r\n    const rssLocation = advancedAnalysis.locationSpecific\r\n      .filter((analysis: any) => analysis.locationRelevance >= 6)\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Combine and prioritize\r\n    const combined = [...rssLocation.slice(0, 2), ...traditionalLocation.slice(0, 2)];\r\n    return Array.from(new Set(combined)).slice(0, 4);\r\n  }\r\n\r\n  /**\r\n   * Get enhanced platform hashtags using platform analysis\r\n   */\r\n  private getEnhancedPlatformHashtags(platform: string, advancedAnalysis: any): string[] {\r\n    // Get traditional platform hashtags\r\n    const traditionalPlatform = this.getPlatformHashtags(platform);\r\n\r\n    // Add platform-optimized hashtags from RSS analysis\r\n    const rssPlatform = advancedAnalysis.platformNative\r\n      .filter((analysis: any) => analysis.platformOptimization >= 6)\r\n      .map((analysis: any) => analysis.hashtag);\r\n\r\n    // Combine and prioritize\r\n    const combined = [...rssPlatform.slice(0, 2), ...traditionalPlatform.slice(0, 2)];\r\n    return Array.from(new Set(combined)).slice(0, 4);\r\n  }\r\n\r\n  /**\r\n   * Intelligent hashtag mixing algorithm\r\n   */\r\n  private intelligentHashtagMixing(hashtags: string[], advancedAnalysis: any): string[] {\r\n    // Create a scoring system for hashtag selection\r\n    const hashtagScores = new Map<string, number>();\r\n\r\n    // Score each hashtag based on multiple factors\r\n    hashtags.forEach(hashtag => {\r\n      let score = 0;\r\n\r\n      // Find analysis for this hashtag\r\n      const analysis = this.findHashtagAnalysis(hashtag, advancedAnalysis);\r\n\r\n      if (analysis) {\r\n        score += analysis.relevanceScore * 0.3;\r\n        score += analysis.trendingScore * 0.25;\r\n        score += analysis.businessRelevance * 0.2;\r\n        score += analysis.engagementPotential * 0.15;\r\n        score += analysis.platformOptimization * 0.1;\r\n      } else {\r\n        // Default score for hashtags not in analysis\r\n        score = 5;\r\n      }\r\n\r\n      hashtagScores.set(hashtag, score);\r\n    });\r\n\r\n    // Sort by score and return top hashtags\r\n    const sortedHashtags = Array.from(hashtagScores.entries())\r\n      .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)\r\n      .map(([hashtag]) => hashtag);\r\n\r\n    return sortedHashtags.slice(0, 15);\r\n  }\r\n\r\n  /**\r\n   * Find hashtag analysis in advanced analysis results\r\n   */\r\n  private findHashtagAnalysis(hashtag: string, advancedAnalysis: any): any {\r\n    const allAnalyses = [\r\n      ...advancedAnalysis.topTrending,\r\n      ...advancedAnalysis.businessOptimized,\r\n      ...advancedAnalysis.locationSpecific,\r\n      ...advancedAnalysis.platformNative,\r\n      ...advancedAnalysis.emergingTrends\r\n    ];\r\n\r\n    return allAnalyses.find((analysis: any) => analysis.hashtag === hashtag);\r\n  }\r\n\r\n  /**\r\n   * Calculate confidence score based on RSS data quality\r\n   */\r\n  private calculateConfidenceScore(advancedAnalysis: any): number {\r\n    let score = 0;\r\n    let factors = 0;\r\n\r\n    // Factor 1: Number of RSS sources\r\n    const rssSourceCount = this.countRSSSources(advancedAnalysis);\r\n    if (rssSourceCount > 0) {\r\n      score += Math.min(rssSourceCount * 2, 10);\r\n      factors++;\r\n    }\r\n\r\n    // Factor 2: Quality of trending data\r\n    const trendingQuality = advancedAnalysis.topTrending.length > 0 ? 8 : 3;\r\n    score += trendingQuality;\r\n    factors++;\r\n\r\n    // Factor 3: Business relevance coverage\r\n    const businessCoverage = advancedAnalysis.businessOptimized.length >= 3 ? 9 : 5;\r\n    score += businessCoverage;\r\n    factors++;\r\n\r\n    // Factor 4: Emerging trends availability\r\n    const emergingTrends = advancedAnalysis.emergingTrends.length > 0 ? 7 : 4;\r\n    score += emergingTrends;\r\n    factors++;\r\n\r\n    return factors > 0 ? Math.round(score / factors) : 5;\r\n  }\r\n\r\n  /**\r\n   * Count RSS sources in analysis\r\n   */\r\n  private countRSSSources(advancedAnalysis: any): number {\r\n    const sources = new Set<string>();\r\n\r\n    const allAnalyses = [\r\n      ...advancedAnalysis.topTrending,\r\n      ...advancedAnalysis.businessOptimized,\r\n      ...advancedAnalysis.locationSpecific,\r\n      ...advancedAnalysis.platformNative,\r\n      ...advancedAnalysis.emergingTrends\r\n    ];\r\n\r\n    allAnalyses.forEach((analysis: any) => {\r\n      analysis.sources.forEach((source: string) => {\r\n        if (source !== 'business_generator' && source !== 'fallback') {\r\n          sources.add(source);\r\n        }\r\n      });\r\n    });\r\n\r\n    return sources.size;\r\n  }\r\n\r\n  /**\r\n   * Extract RSS-sourced hashtags\r\n   */\r\n  private extractRSSSourcedHashtags(advancedAnalysis: any): string[] {\r\n    const allAnalyses = [\r\n      ...advancedAnalysis.topTrending,\r\n      ...advancedAnalysis.businessOptimized,\r\n      ...advancedAnalysis.locationSpecific,\r\n      ...advancedAnalysis.platformNative,\r\n      ...advancedAnalysis.emergingTrends\r\n    ];\r\n\r\n    return allAnalyses\r\n      .filter((analysis: any) =>\r\n        analysis.sources.some((source: string) =>\r\n          source !== 'business_generator' && source !== 'fallback'\r\n        )\r\n      )\r\n      .map((analysis: any) => analysis.hashtag)\r\n      .slice(0, 8);\r\n  }\r\n\r\n  /**\r\n   * Get high-engagement viral hashtags\r\n   */\r\n  private getViralHashtags(businessType: string, platform: string): string[] {\r\n    const viralHashtags = {\r\n      general: ['#viral', '#trending', '#fyp', '#explore', '#discover', '#amazing', '#incredible', '#mustsee'],\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#reels', '#explorepage'],\r\n      tiktok: ['#fyp', '#foryou', '#viral', '#trending', '#foryoupage'],\r\n      facebook: ['#viral', '#share', '#community', '#local', '#trending'],\r\n      twitter: ['#trending', '#viral', '#breaking', '#news', '#update'],\r\n      linkedin: ['#professional', '#business', '#networking', '#career', '#industry']\r\n    };\r\n\r\n    const general = viralHashtags.general.sort(() => 0.5 - Math.random()).slice(0, 4);\r\n    const platformSpecific = viralHashtags[platform.toLowerCase() as keyof typeof viralHashtags] || [];\r\n\r\n    return [...general, ...platformSpecific.slice(0, 3)];\r\n  }\r\n\r\n  /**\r\n   * Get business-specific niche hashtags\r\n   */\r\n  private getNicheHashtags(businessType: string, services?: string): string[] {\r\n    const nicheMap: Record<string, string[]> = {\r\n      restaurant: ['#foodie', '#delicious', '#freshfood', '#localeats', '#foodlover', '#tasty', '#chef', '#dining'],\r\n      bakery: ['#freshbaked', '#artisan', '#homemade', '#bakery', '#pastry', '#bread', '#dessert', '#sweet'],\r\n      fitness: ['#fitness', '#workout', '#health', '#gym', '#strong', '#motivation', '#fitlife', '#training'],\r\n      beauty: ['#beauty', '#skincare', '#makeup', '#glam', '#selfcare', '#beautiful', '#style', '#cosmetics'],\r\n      tech: ['#tech', '#innovation', '#digital', '#software', '#technology', '#startup', '#coding', '#ai'],\r\n      retail: ['#shopping', '#fashion', '#style', '#sale', '#newcollection', '#boutique', '#trendy', '#deals']\r\n    };\r\n\r\n    const baseNiche = nicheMap[businessType.toLowerCase()] || ['#business', '#service', '#quality', '#professional'];\r\n\r\n    // Add service-specific hashtags if provided\r\n    if (services) {\r\n      const serviceWords = services.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const serviceHashtags = serviceWords.slice(0, 3).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      baseNiche.push(...serviceHashtags);\r\n    }\r\n\r\n    return baseNiche.slice(0, 6);\r\n  }\r\n\r\n  /**\r\n   * Get location-based hashtags\r\n   */\r\n  private getLocationHashtags(location: string): string[] {\r\n    const locationParts = location.split(',').map(part => part.trim());\r\n    const hashtags: string[] = [];\r\n\r\n    locationParts.forEach(part => {\r\n      const cleanLocation = part.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '');\r\n      if (cleanLocation.length > 2) {\r\n        hashtags.push(`#${cleanLocation.toLowerCase()}`);\r\n      }\r\n    });\r\n\r\n    // Add generic location hashtags\r\n    hashtags.push('#local', '#community', '#neighborhood');\r\n\r\n    return hashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get community engagement hashtags\r\n   */\r\n  private getCommunityHashtags(businessType: string, targetAudience?: string): string[] {\r\n    const communityHashtags = ['#community', '#local', '#support', '#family', '#friends', '#together', '#love'];\r\n\r\n    if (targetAudience) {\r\n      const audienceWords = targetAudience.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const audienceHashtags = audienceWords.slice(0, 2).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      communityHashtags.push(...audienceHashtags);\r\n    }\r\n\r\n    return communityHashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get seasonal/timely hashtags\r\n   */\r\n  private getSeasonalHashtags(): string[] {\r\n    const now = new Date();\r\n    const month = now.getMonth();\r\n    const day = now.getDate();\r\n\r\n    // Seasonal hashtags based on current time\r\n    const seasonal: Record<number, string[]> = {\r\n      0: ['#newyear', '#january', '#fresh', '#newbeginnings'], // January\r\n      1: ['#february', '#love', '#valentine', '#winter'], // February  \r\n      2: ['#march', '#spring', '#fresh', '#bloom'], // March\r\n      3: ['#april', '#spring', '#easter', '#renewal'], // April\r\n      4: ['#may', '#spring', '#mothers', '#bloom'], // May\r\n      5: ['#june', '#summer', '#fathers', '#sunshine'], // June\r\n      6: ['#july', '#summer', '#vacation', '#hot'], // July\r\n      7: ['#august', '#summer', '#vacation', '#sunny'], // August\r\n      8: ['#september', '#fall', '#autumn', '#backtoschool'], // September\r\n      9: ['#october', '#fall', '#halloween', '#autumn'], // October\r\n      10: ['#november', '#thanksgiving', '#grateful', '#fall'], // November\r\n      11: ['#december', '#christmas', '#holiday', '#winter'] // December\r\n    };\r\n\r\n    return seasonal[month] || ['#today', '#now', '#current'];\r\n  }\r\n\r\n  /**\r\n   * Get platform-specific hashtags\r\n   */\r\n  private getPlatformHashtags(platform: string): string[] {\r\n    const platformHashtags: Record<string, string[]> = {\r\n      instagram: ['#instagram', '#insta', '#ig'],\r\n      facebook: ['#facebook', '#fb', '#social'],\r\n      twitter: ['#twitter', '#tweet', '#x'],\r\n      linkedin: ['#linkedin', '#professional', '#business'],\r\n      tiktok: ['#tiktok', '#tt', '#video']\r\n    };\r\n\r\n    return platformHashtags[platform.toLowerCase()] || ['#social', '#media'];\r\n  }\r\n\r\n  /**\r\n   * Get business-relevant trending hashtags\r\n   */\r\n  private getBusinessTrendingHashtags(businessType: string, platform: string): string[] {\r\n    // This would integrate with real trending APIs in production\r\n    const trendingByBusiness: Record<string, string[]> = {\r\n      restaurant: ['#foodtrends', '#eats2024', '#localfood', '#foodie'],\r\n      fitness: ['#fitness2024', '#healthtrends', '#workout', '#wellness'],\r\n      beauty: ['#beautytrends', '#skincare2024', '#makeup', '#selfcare'],\r\n      tech: ['#tech2024', '#innovation', '#ai', '#digital'],\r\n      retail: ['#fashion2024', '#shopping', '#style', '#trends']\r\n    };\r\n\r\n    return trendingByBusiness[businessType.toLowerCase()] || ['#trending', '#popular', '#new'];\r\n  }\r\n\r\n  /**\r\n   * Optimize hashtag selection for maximum virality\r\n   */\r\n  private optimizeForVirality(hashtags: string[]): string[] {\r\n    // Remove duplicates\r\n    const unique = Array.from(new Set(hashtags));\r\n\r\n    // Sort by estimated engagement potential (simplified scoring)\r\n    const scored = unique.map(tag => ({\r\n      tag,\r\n      score: this.calculateViralScore(tag)\r\n    }));\r\n\r\n    scored.sort((a, b) => b.score - a.score);\r\n\r\n    return scored.slice(0, 15).map(item => item.tag);\r\n  }\r\n\r\n  /**\r\n   * Calculate viral potential score for a hashtag\r\n   */\r\n  private calculateViralScore(hashtag: string): number {\r\n    let score = 0;\r\n\r\n    // High-engagement keywords get bonus points\r\n    const viralKeywords = ['viral', 'trending', 'fyp', 'explore', 'amazing', 'incredible'];\r\n    if (viralKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 10;\r\n    }\r\n\r\n    // Platform-specific hashtags get bonus\r\n    const platformKeywords = ['instagram', 'tiktok', 'reels', 'story'];\r\n    if (platformKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 5;\r\n    }\r\n\r\n    // Local hashtags get moderate bonus\r\n    const localKeywords = ['local', 'community', 'neighborhood'];\r\n    if (localKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 3;\r\n    }\r\n\r\n    // Length penalty (very long hashtags perform worse)\r\n    if (hashtag.length > 20) score -= 2;\r\n    if (hashtag.length > 30) score -= 5;\r\n\r\n    return score + Math.random(); // Add randomness for variety\r\n  }\r\n\r\n  /**\r\n   * Enhanced fallback hashtags when trending data fails\r\n   */\r\n  private getFallbackHashtags(businessType: string, location: string, platform: string): ViralHashtagStrategy {\r\n    const fallbackTotal = [\r\n      '#trending', '#viral', `#${businessType.replace(/\\s+/g, '')}`, '#local', '#community',\r\n      '#amazing', '#quality', '#professional', '#popular', '#new',\r\n      '#support', '#service', `#${platform.toLowerCase()}`, '#today', '#love'\r\n    ];\r\n\r\n    return {\r\n      trending: ['#trending', '#viral', '#popular', '#new'],\r\n      viral: ['#amazing', '#incredible', '#mustsee', '#wow'],\r\n      niche: [`#${businessType.replace(/\\s+/g, '')}`, '#quality', '#professional', '#service'],\r\n      location: ['#local', '#community', `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`],\r\n      community: ['#community', '#support', '#family', '#love'],\r\n      seasonal: ['#today', '#now'],\r\n      platform: [`#${platform.toLowerCase()}`],\r\n      total: fallbackTotal,\r\n      analytics: {\r\n        topPerformers: fallbackTotal.slice(0, 5),\r\n        emergingTrends: ['#trending', '#viral', '#new'],\r\n        businessOptimized: [`#${businessType.replace(/\\s+/g, '')}`, '#quality', '#professional'],\r\n        rssSourced: [], // No RSS data in fallback\r\n        confidenceScore: 3 // Low confidence for fallback\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 🧠 ENHANCED: Integrate learned recommendations with intelligent mix\r\n   */\r\n  private integrateLearnedRecommendations(\r\n    intelligentMix: string[],\r\n    learnedRecommendations: Array<{ hashtag: string; confidence: number; reason: string }>,\r\n    performanceInsights: any\r\n  ): string[] {\r\n    const enhancedHashtags = [...intelligentMix];\r\n\r\n    // Replace low-confidence hashtags with high-confidence learned recommendations\r\n    const highConfidenceRecommendations = learnedRecommendations.filter(rec => rec.confidence >= 0.7);\r\n\r\n    if (highConfidenceRecommendations.length > 0) {\r\n      // Find hashtags in the mix that might be replaced\r\n      const replaceableIndices: number[] = [];\r\n\r\n      // Look for hashtags that aren't in the top performers\r\n      const topPerformers = performanceInsights.topPerformingHashtags.map((h: any) => h.hashtag);\r\n\r\n      enhancedHashtags.forEach((hashtag, index) => {\r\n        if (!topPerformers.includes(hashtag) && index >= 10) { // Only replace from tertiary hashtags\r\n          replaceableIndices.push(index);\r\n        }\r\n      });\r\n\r\n      // Replace up to 3 hashtags with learned recommendations\r\n      const replacementCount = Math.min(\r\n        highConfidenceRecommendations.length,\r\n        replaceableIndices.length,\r\n        3\r\n      );\r\n\r\n      for (let i = 0; i < replacementCount; i++) {\r\n        const indexToReplace = replaceableIndices[i];\r\n        const recommendation = highConfidenceRecommendations[i];\r\n\r\n        // Only replace if the recommendation isn't already in the mix\r\n        if (!enhancedHashtags.includes(recommendation.hashtag)) {\r\n          enhancedHashtags[indexToReplace] = recommendation.hashtag;\r\n        }\r\n      }\r\n    }\r\n\r\n    return enhancedHashtags;\r\n  }\r\n\r\n  /**\r\n   * Calculate historical performance score\r\n   */\r\n  private calculateHistoricalPerformance(performanceInsights: any): number {\r\n    if (!performanceInsights.topPerformingHashtags.length) return 0;\r\n\r\n    const avgEngagement = performanceInsights.topPerformingHashtags\r\n      .reduce((sum: number, hashtag: any) => sum + hashtag.avgEngagement, 0) /\r\n      performanceInsights.topPerformingHashtags.length;\r\n\r\n    const avgSuccessRate = performanceInsights.topPerformingHashtags\r\n      .reduce((sum: number, hashtag: any) => sum + hashtag.successRate, 0) /\r\n      performanceInsights.topPerformingHashtags.length;\r\n\r\n    // Weighted score: 70% engagement, 30% success rate\r\n    return Math.round((avgEngagement * 0.7 + avgSuccessRate * 0.3) * 10) / 10;\r\n  }\r\n\r\n  /**\r\n   * 📊 ENHANCED: Method to track hashtag performance after post creation\r\n   */\r\n  public trackHashtagPerformance(\r\n    hashtags: string[],\r\n    platform: string,\r\n    businessType: string,\r\n    location: string,\r\n    engagement: {\r\n      likes: number;\r\n      comments: number;\r\n      shares: number;\r\n      views?: number;\r\n      clicks?: number;\r\n      total: number;\r\n    },\r\n    success: boolean = false\r\n  ): void {\r\n    const postData = {\r\n      postId: `post_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n      hashtags,\r\n      platform,\r\n      businessType,\r\n      location,\r\n      timestamp: new Date(),\r\n      engagement,\r\n      success\r\n    };\r\n\r\n    hashtagPerformanceTracker.trackPostPerformance(postData);\r\n  }\r\n\r\n  /**\r\n   * 📈 Get performance insights for hashtag optimization\r\n   */\r\n  public getHashtagPerformanceInsights(\r\n    businessType?: string,\r\n    platform?: string,\r\n    location?: string\r\n  ) {\r\n    return hashtagPerformanceTracker.getPerformanceInsights(businessType, platform, location);\r\n  }\r\n\r\n  /**\r\n   * 🎯 Get learned hashtag recommendations\r\n   */\r\n  public getLearnedHashtagRecommendations(\r\n    businessType: string,\r\n    platform: string,\r\n    location: string,\r\n    count: number = 10\r\n  ) {\r\n    return hashtagPerformanceTracker.getLearnedRecommendations(businessType, platform, location, count);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const viralHashtagEngine = new ViralHashtagEngine();\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;AACA;AACA;;;;;AAuCO,MAAM;IAEX;;GAEC,GACD,MAAM,sBACJ,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACjB,cAAuB,EACQ;QAE/B,IAAI;YACF,0EAA0E;YAC1E,MAAM,kBAAmC;gBACvC;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,qDAAqD;YACrD,MAAM,mBAAmB,MAAM,0JAAA,CAAA,0BAAuB,CAAC,oBAAoB,CAAC;YAE5E,0CAA0C;YAC1C,MAAM,eAAe,MAAM,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;gBACjE;gBACA;gBACA;gBACA;YACF;YAEA,mEAAmE;YACnE,MAAM,WAAW,IAAI,CAAC,2BAA2B,CAAC,kBAAkB;YACpE,MAAM,QAAQ,IAAI,CAAC,wBAAwB,CAAC,cAAc,UAAU;YACpE,MAAM,QAAQ,IAAI,CAAC,wBAAwB,CAAC,cAAc,UAAU;YACpE,MAAM,gBAAgB,IAAI,CAAC,2BAA2B,CAAC,UAAU;YACjE,MAAM,YAAY,IAAI,CAAC,oBAAoB,CAAC,cAAc;YAC1D,MAAM,WAAW,IAAI,CAAC,mBAAmB;YACzC,MAAM,gBAAgB,IAAI,CAAC,2BAA2B,CAAC,UAAU;YAEjE,qEAAqE;YACrE,MAAM,gBAA+B;gBACnC;gBACA;gBACA;gBACA;gBACA,aAAa;gBACb;gBACA;gBACA,UAAU;gBACV,GAAG,8IAAA,CAAA,0BAAuB,CAAC,iBAAiB,CAAC,cAAc,SAAS;YACtE;YAEA,yCAAyC;YACzC,MAAM,kBAAkB;gBACtB;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA,UAAU;gBACV,OAAO,EAAE,CAAC,0BAA0B;YACtC;YAEA,2BAA2B;YAC3B,MAAM,iBAAiB,MAAM,8IAAA,CAAA,0BAAuB,CAAC,oBAAoB,CACvE,kBACA,iBACA;YAGF,qEAAqE;YACrE,MAAM,yBAAyB,gJAAA,CAAA,4BAAyB,CAAC,yBAAyB,CAChF,cACA,UACA,UACA;YAGF,0DAA0D;YAC1D,MAAM,sBAAsB,gJAAA,CAAA,4BAAyB,CAAC,sBAAsB,CAC1E,cACA,UACA;YAGF,4DAA4D;YAC5D,MAAM,gBAAgB,IAAI,CAAC,+BAA+B,CACxD,eAAe,KAAK,EACpB,wBACA;YAGF,+CAA+C;YAC/C,MAAM,QAAQ;YAEd,uDAAuD;YACvD,MAAM,kBAAkB,IAAI,CAAC,wBAAwB,CAAC;YAEtD,OAAO;gBACL;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA,UAAU;gBACV;gBACA,WAAW;oBACT,eAAe,iBAAiB,oBAAoB,CAAC,KAAK,CAAC,GAAG;oBAC9D,gBAAgB,iBAAiB,cAAc,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG;oBAC7E,mBAAmB,iBAAiB,iBAAiB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG;oBACnF,YAAY,IAAI,CAAC,yBAAyB,CAAC;oBAC3C;oBAEA,uCAAuC;oBACvC,gBAAgB;wBACd,cAAc,eAAe,SAAS,CAAC,YAAY;wBACnD,mBAAmB,eAAe,SAAS,CAAC,iBAAiB;wBAC7D,eAAe,eAAe,SAAS,CAAC,aAAa;wBACrD,gBAAgB,eAAe,SAAS,CAAC,cAAc;wBACvD,iBAAiB,eAAe,SAAS,CAAC,eAAe;wBACzD,WAAW,eAAe,SAAS,CAAC,cAAc;oBACpD;oBAEA,wCAAwC;oBACxC,kBAAkB;wBAChB;wBACA,uBAAuB,IAAI,CAAC,8BAA8B,CAAC;wBAC3D,wBAAwB,oBAAoB,uBAAuB;oBACrE;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,UAAU;QAC1D;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B,gBAAqB,EAAE,YAAiB,EAAY;QACtF,2CAA2C;QAC3C,MAAM,cAAc,iBAAiB,WAAW,CAC7C,MAAM,CAAC,CAAC,WAAkB,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC,SAChD,WAAW,wBAAwB,WAAW,aAE/C,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,mCAAmC;QACnC,MAAM,mBAAmB,iBAAiB,cAAc,CACrD,MAAM,CAAC,CAAC,WAAkB,SAAS,aAAa,IAAI,GACpD,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,uCAAuC;QACvC,MAAM,WAAW;eAAI;eAAgB;eAAqB,aAAa,QAAQ;SAAC;QAEhF,4CAA4C;QAC5C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,YAAoB,EAAE,QAAgB,EAAE,gBAAqB,EAAY;QACxG,iCAAiC;QACjC,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,cAAc;QAE7D,iDAAiD;QACjD,MAAM,WAAW,iBAAiB,WAAW,CAC1C,MAAM,CAAC,CAAC,WAAkB,SAAS,mBAAmB,IAAI,GAC1D,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,yBAAyB;QACzB,MAAM,WAAW;eAAI,SAAS,KAAK,CAAC,GAAG;eAAO,iBAAiB,KAAK,CAAC,GAAG;SAAG;QAC3E,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,YAAoB,EAAE,QAA4B,EAAE,gBAAqB,EAAY;QACpH,iCAAiC;QACjC,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,cAAc;QAE7D,oDAAoD;QACpD,MAAM,WAAW,iBAAiB,iBAAiB,CAChD,MAAM,CAAC,CAAC,WAAkB,SAAS,iBAAiB,IAAI,GACxD,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,yBAAyB;QACzB,MAAM,WAAW;eAAI,SAAS,KAAK,CAAC,GAAG;eAAO,iBAAiB,KAAK,CAAC,GAAG;SAAG;QAC3E,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,4BAA4B,QAAgB,EAAE,gBAAqB,EAAY;QACrF,oCAAoC;QACpC,MAAM,sBAAsB,IAAI,CAAC,mBAAmB,CAAC;QAErD,mDAAmD;QACnD,MAAM,cAAc,iBAAiB,gBAAgB,CAClD,MAAM,CAAC,CAAC,WAAkB,SAAS,iBAAiB,IAAI,GACxD,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,yBAAyB;QACzB,MAAM,WAAW;eAAI,YAAY,KAAK,CAAC,GAAG;eAAO,oBAAoB,KAAK,CAAC,GAAG;SAAG;QACjF,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,4BAA4B,QAAgB,EAAE,gBAAqB,EAAY;QACrF,oCAAoC;QACpC,MAAM,sBAAsB,IAAI,CAAC,mBAAmB,CAAC;QAErD,oDAAoD;QACpD,MAAM,cAAc,iBAAiB,cAAc,CAChD,MAAM,CAAC,CAAC,WAAkB,SAAS,oBAAoB,IAAI,GAC3D,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO;QAE1C,yBAAyB;QACzB,MAAM,WAAW;eAAI,YAAY,KAAK,CAAC,GAAG;eAAO,oBAAoB,KAAK,CAAC,GAAG;SAAG;QACjF,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,QAAkB,EAAE,gBAAqB,EAAY;QACpF,gDAAgD;QAChD,MAAM,gBAAgB,IAAI;QAE1B,+CAA+C;QAC/C,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,QAAQ;YAEZ,iCAAiC;YACjC,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,SAAS;YAEnD,IAAI,UAAU;gBACZ,SAAS,SAAS,cAAc,GAAG;gBACnC,SAAS,SAAS,aAAa,GAAG;gBAClC,SAAS,SAAS,iBAAiB,GAAG;gBACtC,SAAS,SAAS,mBAAmB,GAAG;gBACxC,SAAS,SAAS,oBAAoB,GAAG;YAC3C,OAAO;gBACL,6CAA6C;gBAC7C,QAAQ;YACV;YAEA,cAAc,GAAG,CAAC,SAAS;QAC7B;QAEA,wCAAwC;QACxC,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,OAAO,IACpD,IAAI,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,GAAK,SAAS,QAC1C,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;QAEtB,OAAO,eAAe,KAAK,CAAC,GAAG;IACjC;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAe,EAAE,gBAAqB,EAAO;QACvE,MAAM,cAAc;eACf,iBAAiB,WAAW;eAC5B,iBAAiB,iBAAiB;eAClC,iBAAiB,gBAAgB;eACjC,iBAAiB,cAAc;eAC/B,iBAAiB,cAAc;SACnC;QAED,OAAO,YAAY,IAAI,CAAC,CAAC,WAAkB,SAAS,OAAO,KAAK;IAClE;IAEA;;GAEC,GACD,AAAQ,yBAAyB,gBAAqB,EAAU;QAC9D,IAAI,QAAQ;QACZ,IAAI,UAAU;QAEd,kCAAkC;QAClC,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC;QAC5C,IAAI,iBAAiB,GAAG;YACtB,SAAS,KAAK,GAAG,CAAC,iBAAiB,GAAG;YACtC;QACF;QAEA,qCAAqC;QACrC,MAAM,kBAAkB,iBAAiB,WAAW,CAAC,MAAM,GAAG,IAAI,IAAI;QACtE,SAAS;QACT;QAEA,wCAAwC;QACxC,MAAM,mBAAmB,iBAAiB,iBAAiB,CAAC,MAAM,IAAI,IAAI,IAAI;QAC9E,SAAS;QACT;QAEA,yCAAyC;QACzC,MAAM,iBAAiB,iBAAiB,cAAc,CAAC,MAAM,GAAG,IAAI,IAAI;QACxE,SAAS;QACT;QAEA,OAAO,UAAU,IAAI,KAAK,KAAK,CAAC,QAAQ,WAAW;IACrD;IAEA;;GAEC,GACD,AAAQ,gBAAgB,gBAAqB,EAAU;QACrD,MAAM,UAAU,IAAI;QAEpB,MAAM,cAAc;eACf,iBAAiB,WAAW;eAC5B,iBAAiB,iBAAiB;eAClC,iBAAiB,gBAAgB;eACjC,iBAAiB,cAAc;eAC/B,iBAAiB,cAAc;SACnC;QAED,YAAY,OAAO,CAAC,CAAC;YACnB,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,IAAI,WAAW,wBAAwB,WAAW,YAAY;oBAC5D,QAAQ,GAAG,CAAC;gBACd;YACF;QACF;QAEA,OAAO,QAAQ,IAAI;IACrB;IAEA;;GAEC,GACD,AAAQ,0BAA0B,gBAAqB,EAAY;QACjE,MAAM,cAAc;eACf,iBAAiB,WAAW;eAC5B,iBAAiB,iBAAiB;eAClC,iBAAiB,gBAAgB;eACjC,iBAAiB,cAAc;eAC/B,iBAAiB,cAAc;SACnC;QAED,OAAO,YACJ,MAAM,CAAC,CAAC,WACP,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC,SACrB,WAAW,wBAAwB,WAAW,aAGjD,GAAG,CAAC,CAAC,WAAkB,SAAS,OAAO,EACvC,KAAK,CAAC,GAAG;IACd;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAgB,EAAY;QACzE,MAAM,gBAAgB;YACpB,SAAS;gBAAC;gBAAU;gBAAa;gBAAQ;gBAAY;gBAAa;gBAAY;gBAAe;aAAW;YACxG,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;gBAAU;aAAe;YACpF,QAAQ;gBAAC;gBAAQ;gBAAW;gBAAU;gBAAa;aAAc;YACjE,UAAU;gBAAC;gBAAU;gBAAU;gBAAc;gBAAU;aAAY;YACnE,SAAS;gBAAC;gBAAa;gBAAU;gBAAa;gBAAS;aAAU;YACjE,UAAU;gBAAC;gBAAiB;gBAAa;gBAAe;gBAAW;aAAY;QACjF;QAEA,MAAM,UAAU,cAAc,OAAO,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG;QAC/E,MAAM,mBAAmB,aAAa,CAAC,SAAS,WAAW,GAAiC,IAAI,EAAE;QAElG,OAAO;eAAI;eAAY,iBAAiB,KAAK,CAAC,GAAG;SAAG;IACtD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAiB,EAAY;QAC1E,MAAM,WAAqC;YACzC,YAAY;gBAAC;gBAAW;gBAAc;gBAAc;gBAAc;gBAAc;gBAAU;gBAAS;aAAU;YAC7G,QAAQ;gBAAC;gBAAe;gBAAY;gBAAa;gBAAW;gBAAW;gBAAU;gBAAY;aAAS;YACtG,SAAS;gBAAC;gBAAY;gBAAY;gBAAW;gBAAQ;gBAAW;gBAAe;gBAAY;aAAY;YACvG,QAAQ;gBAAC;gBAAW;gBAAa;gBAAW;gBAAS;gBAAa;gBAAc;gBAAU;aAAa;YACvG,MAAM;gBAAC;gBAAS;gBAAe;gBAAY;gBAAa;gBAAe;gBAAY;gBAAW;aAAM;YACpG,QAAQ;gBAAC;gBAAa;gBAAY;gBAAU;gBAAS;gBAAkB;gBAAa;gBAAW;aAAS;QAC1G;QAEA,MAAM,YAAY,QAAQ,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;YAAY;SAAgB;QAEhH,4CAA4C;QAC5C,IAAI,UAAU;YACZ,MAAM,eAAe,SAAS,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YACzF,MAAM,kBAAkB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACjG,UAAU,IAAI,IAAI;QACpB;QAEA,OAAO,UAAU,KAAK,CAAC,GAAG;IAC5B;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC/D,MAAM,WAAqB,EAAE;QAE7B,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,gBAAgB,KAAK,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ;YAC1E,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,WAAW,IAAI;YACjD;QACF;QAEA,gCAAgC;QAChC,SAAS,IAAI,CAAC,UAAU,cAAc;QAEtC,OAAO,SAAS,KAAK,CAAC,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAQ,qBAAqB,YAAoB,EAAE,cAAuB,EAAY;QACpF,MAAM,oBAAoB;YAAC;YAAc;YAAU;YAAY;YAAW;YAAY;YAAa;SAAQ;QAE3G,IAAI,gBAAgB;YAClB,MAAM,gBAAgB,eAAe,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAChG,MAAM,mBAAmB,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACnG,kBAAkB,IAAI,IAAI;QAC5B;QAEA,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IAEA;;GAEC,GACD,AAAQ,sBAAgC;QACtC,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,QAAQ;QAC1B,MAAM,MAAM,IAAI,OAAO;QAEvB,0CAA0C;QAC1C,MAAM,WAAqC;YACzC,GAAG;gBAAC;gBAAY;gBAAY;gBAAU;aAAiB;YACvD,GAAG;gBAAC;gBAAa;gBAAS;gBAAc;aAAU;YAClD,GAAG;gBAAC;gBAAU;gBAAW;gBAAU;aAAS;YAC5C,GAAG;gBAAC;gBAAU;gBAAW;gBAAW;aAAW;YAC/C,GAAG;gBAAC;gBAAQ;gBAAW;gBAAY;aAAS;YAC5C,GAAG;gBAAC;gBAAS;gBAAW;gBAAY;aAAY;YAChD,GAAG;gBAAC;gBAAS;gBAAW;gBAAa;aAAO;YAC5C,GAAG;gBAAC;gBAAW;gBAAW;gBAAa;aAAS;YAChD,GAAG;gBAAC;gBAAc;gBAAS;gBAAW;aAAgB;YACtD,GAAG;gBAAC;gBAAY;gBAAS;gBAAc;aAAU;YACjD,IAAI;gBAAC;gBAAa;gBAAiB;gBAAa;aAAQ;YACxD,IAAI;gBAAC;gBAAa;gBAAc;gBAAY;aAAU,CAAC,WAAW;QACpE;QAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;YAAC;YAAU;YAAQ;SAAW;IAC1D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,mBAA6C;YACjD,WAAW;gBAAC;gBAAc;gBAAU;aAAM;YAC1C,UAAU;gBAAC;gBAAa;gBAAO;aAAU;YACzC,SAAS;gBAAC;gBAAY;gBAAU;aAAK;YACrC,UAAU;gBAAC;gBAAa;gBAAiB;aAAY;YACrD,QAAQ;gBAAC;gBAAW;gBAAO;aAAS;QACtC;QAEA,OAAO,gBAAgB,CAAC,SAAS,WAAW,GAAG,IAAI;YAAC;YAAW;SAAS;IAC1E;IAEA;;GAEC,GACD,AAAQ,4BAA4B,YAAoB,EAAE,QAAgB,EAAY;QACpF,6DAA6D;QAC7D,MAAM,qBAA+C;YACnD,YAAY;gBAAC;gBAAe;gBAAa;gBAAc;aAAU;YACjE,SAAS;gBAAC;gBAAgB;gBAAiB;gBAAY;aAAY;YACnE,QAAQ;gBAAC;gBAAiB;gBAAiB;gBAAW;aAAY;YAClE,MAAM;gBAAC;gBAAa;gBAAe;gBAAO;aAAW;YACrD,QAAQ;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;QAC5D;QAEA,OAAO,kBAAkB,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;SAAO;IAC5F;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAkB,EAAY;QACxD,oBAAoB;QACpB,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,IAAI;QAElC,8DAA8D;QAC9D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;gBAChC;gBACA,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAEvC,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAe,EAAU;QACnD,IAAI,QAAQ;QAEZ,4CAA4C;QAC5C,MAAM,gBAAgB;YAAC;YAAS;YAAY;YAAO;YAAW;YAAW;SAAa;QACtF,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,uCAAuC;QACvC,MAAM,mBAAmB;YAAC;YAAa;YAAU;YAAS;SAAQ;QAClE,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC7E,SAAS;QACX;QAEA,oCAAoC;QACpC,MAAM,gBAAgB;YAAC;YAAS;YAAa;SAAe;QAC5D,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,oDAAoD;QACpD,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAClC,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAElC,OAAO,QAAQ,KAAK,MAAM,IAAI,6BAA6B;IAC7D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAAoB,EAAE,QAAgB,EAAE,QAAgB,EAAwB;QAC1G,MAAM,gBAAgB;YACpB;YAAa;YAAU,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;YAAE;YAAU;YACzE;YAAY;YAAY;YAAiB;YAAY;YACrD;YAAY;YAAY,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;YAAE;YAAU;SACjE;QAED,OAAO;YACL,UAAU;gBAAC;gBAAa;gBAAU;gBAAY;aAAO;YACrD,OAAO;gBAAC;gBAAY;gBAAe;gBAAY;aAAO;YACtD,OAAO;gBAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;gBAAE;gBAAY;gBAAiB;aAAW;YACxF,UAAU;gBAAC;gBAAU;gBAAc,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,iBAAiB,IAAI,WAAW,IAAI;aAAC;YAC7F,WAAW;gBAAC;gBAAc;gBAAY;gBAAW;aAAQ;YACzD,UAAU;gBAAC;gBAAU;aAAO;YAC5B,UAAU;gBAAC,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;aAAC;YACxC,OAAO;YACP,WAAW;gBACT,eAAe,cAAc,KAAK,CAAC,GAAG;gBACtC,gBAAgB;oBAAC;oBAAa;oBAAU;iBAAO;gBAC/C,mBAAmB;oBAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;oBAAE;oBAAY;iBAAgB;gBACxF,YAAY,EAAE;gBACd,iBAAiB,EAAE,8BAA8B;YACnD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gCACN,cAAwB,EACxB,sBAAsF,EACtF,mBAAwB,EACd;QACV,MAAM,mBAAmB;eAAI;SAAe;QAE5C,+EAA+E;QAC/E,MAAM,gCAAgC,uBAAuB,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,IAAI;QAE7F,IAAI,8BAA8B,MAAM,GAAG,GAAG;YAC5C,kDAAkD;YAClD,MAAM,qBAA+B,EAAE;YAEvC,sDAAsD;YACtD,MAAM,gBAAgB,oBAAoB,qBAAqB,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,OAAO;YAEzF,iBAAiB,OAAO,CAAC,CAAC,SAAS;gBACjC,IAAI,CAAC,cAAc,QAAQ,CAAC,YAAY,SAAS,IAAI;oBACnD,mBAAmB,IAAI,CAAC;gBAC1B;YACF;YAEA,wDAAwD;YACxD,MAAM,mBAAmB,KAAK,GAAG,CAC/B,8BAA8B,MAAM,EACpC,mBAAmB,MAAM,EACzB;YAGF,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACzC,MAAM,iBAAiB,kBAAkB,CAAC,EAAE;gBAC5C,MAAM,iBAAiB,6BAA6B,CAAC,EAAE;gBAEvD,8DAA8D;gBAC9D,IAAI,CAAC,iBAAiB,QAAQ,CAAC,eAAe,OAAO,GAAG;oBACtD,gBAAgB,CAAC,eAAe,GAAG,eAAe,OAAO;gBAC3D;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,+BAA+B,mBAAwB,EAAU;QACvE,IAAI,CAAC,oBAAoB,qBAAqB,CAAC,MAAM,EAAE,OAAO;QAE9D,MAAM,gBAAgB,oBAAoB,qBAAqB,CAC5D,MAAM,CAAC,CAAC,KAAa,UAAiB,MAAM,QAAQ,aAAa,EAAE,KACpE,oBAAoB,qBAAqB,CAAC,MAAM;QAElD,MAAM,iBAAiB,oBAAoB,qBAAqB,CAC7D,MAAM,CAAC,CAAC,KAAa,UAAiB,MAAM,QAAQ,WAAW,EAAE,KAClE,oBAAoB,qBAAqB,CAAC,MAAM;QAElD,mDAAmD;QACnD,OAAO,KAAK,KAAK,CAAC,CAAC,gBAAgB,MAAM,iBAAiB,GAAG,IAAI,MAAM;IACzE;IAEA;;GAEC,GACD,AAAO,wBACL,QAAkB,EAClB,QAAgB,EAChB,YAAoB,EACpB,QAAgB,EAChB,UAOC,EACD,UAAmB,KAAK,EAClB;QACN,MAAM,WAAW;YACf,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACvE;YACA;YACA;YACA;YACA,WAAW,IAAI;YACf;YACA;QACF;QAEA,gJAAA,CAAA,4BAAyB,CAAC,oBAAoB,CAAC;IACjD;IAEA;;GAEC,GACD,AAAO,8BACL,YAAqB,EACrB,QAAiB,EACjB,QAAiB,EACjB;QACA,OAAO,gJAAA,CAAA,4BAAyB,CAAC,sBAAsB,CAAC,cAAc,UAAU;IAClF;IAEA;;GAEC,GACD,AAAO,iCACL,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAAE,EAClB;QACA,OAAO,gJAAA,CAAA,4BAAyB,CAAC,yBAAyB,CAAC,cAAc,UAAU,UAAU;IAC/F;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}]}