{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/revo-2.0-service.ts"], "sourcesContent": ["/**\r\n * Revo 2.0 Service - Next-Generation AI Content Creation\r\n * Uses Gemini 2.5 Flash Image Preview for enhanced content generation\r\n */\r\n\r\nimport { GoogleGenerativeAI } from '@google/generative-ai';\r\nimport OpenAI from 'openai';\r\nimport type { BrandProfile, Platform } from '@/lib/types';\r\n\r\n// Initialize AI clients\r\nconst ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);\r\nconst openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });\r\n\r\n// Revo 2.0 uses Gemini 2.5 Flash Image Preview (same as Revo 1.0 but with enhanced prompting)\r\nconst REVO_2_0_MODEL = 'gemini-2.5-flash-image-preview';\r\n\r\nexport interface Revo20GenerationOptions {\r\n  businessType: string;\r\n  platform: Platform;\r\n  visualStyle?: 'modern' | 'minimalist' | 'bold' | 'elegant' | 'playful' | 'professional';\r\n  imageText?: string;\r\n  brandProfile: BrandProfile;\r\n  aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n  includePeopleInDesigns?: boolean;\r\n  useLocalLanguage?: boolean;\r\n}\r\n\r\nexport interface Revo20GenerationResult {\r\n  imageUrl: string;\r\n  model: string;\r\n  qualityScore: number;\r\n  processingTime: number;\r\n  enhancementsApplied: string[];\r\n  caption: string;\r\n  hashtags: string[];\r\n}\r\n\r\n/**\r\n * Generate enhanced creative concept using GPT-4\r\n */\r\nasync function generateCreativeConcept(options: Revo20GenerationOptions): Promise<any> {\r\n  const { businessType, platform, brandProfile, visualStyle = 'modern' } = options;\r\n\r\n  const prompt = `You are a world-class creative director specializing in ${businessType} businesses. \r\nCreate an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.\r\n\r\nBusiness Context:\r\n- Type: ${businessType}\r\n- Platform: ${platform}\r\n- Style: ${visualStyle}\r\n- Location: ${brandProfile.location || 'Global'}\r\n- Brand: ${brandProfile.businessName || businessType}\r\n\r\nCreate a concept that:\r\n1. Feels authentic and locally relevant\r\n2. Uses relatable human experiences\r\n3. Connects emotionally with the target audience\r\n4. Incorporates cultural nuances naturally\r\n5. Avoids generic corporate messaging\r\n\r\nReturn your response in this exact JSON format:\r\n{\r\n  \"concept\": \"Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)\",\r\n  \"catchwords\": [\"word1\", \"word2\", \"word3\", \"word4\", \"word5\"],\r\n  \"visualDirection\": \"Authentic visual direction that feels real and community-focused (2-3 sentences)\",\r\n  \"designElements\": [\"element1\", \"element2\", \"element3\", \"element4\"],\r\n  \"colorSuggestions\": [\"#color1\", \"#color2\", \"#color3\"],\r\n  \"moodKeywords\": [\"mood1\", \"mood2\", \"mood3\", \"mood4\"],\r\n  \"targetEmotions\": [\"emotion1\", \"emotion2\", \"emotion3\"]\r\n}`;\r\n\r\n  const response = await openai.chat.completions.create({\r\n    model: 'gpt-4o',\r\n    messages: [{ role: 'user', content: prompt }],\r\n    temperature: 0.8,\r\n    max_tokens: 1000\r\n  });\r\n\r\n  try {\r\n    const content = response.choices[0].message.content || '{}';\r\n    // Remove markdown code blocks if present\r\n    const cleanContent = content.replace(/```json\\n?/g, '').replace(/```\\n?/g, '').trim();\r\n    return JSON.parse(cleanContent);\r\n  } catch (error) {\r\n    return {\r\n      concept: `Professional ${businessType} content for ${platform}`,\r\n      catchwords: ['quality', 'professional', 'trusted', 'local', 'expert'],\r\n      visualDirection: 'Clean, professional design with modern aesthetics',\r\n      designElements: ['clean typography', 'professional imagery', 'brand colors', 'modern layout'],\r\n      colorSuggestions: ['#2563eb', '#1f2937', '#f8fafc'],\r\n      moodKeywords: ['professional', 'trustworthy', 'modern', 'clean'],\r\n      targetEmotions: ['trust', 'confidence', 'reliability']\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with Revo 2.0 (Gemini 2.5 Flash Image Preview)\r\n */\r\nexport async function generateWithRevo20(options: Revo20GenerationOptions): Promise<Revo20GenerationResult> {\r\n  const startTime = Date.now();\r\n\r\n  try {\r\n    // Step 1: Generate creative concept\r\n    const concept = await generateCreativeConcept(options);\r\n\r\n    // Step 2: Build enhanced prompt\r\n    const enhancedPrompt = buildEnhancedPrompt(options, concept);\r\n\r\n    // Step 3: Generate image with Gemini 2.5 Flash Image Preview\r\n    const imageResult = await generateImageWithGemini(enhancedPrompt, options);\r\n\r\n    // Step 4: Generate caption and hashtags\r\n    const contentResult = await generateCaptionAndHashtags(options, concept);\r\n\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    return {\r\n      imageUrl: imageResult.imageUrl,\r\n      model: 'Revo 2.0 (Gemini 2.5 Flash Image Preview)',\r\n      qualityScore: 9.2,\r\n      processingTime,\r\n      enhancementsApplied: [\r\n        'Creative concept generation',\r\n        'Enhanced prompt engineering',\r\n        'Brand consistency optimization',\r\n        'Platform-specific formatting',\r\n        'Cultural relevance integration'\r\n      ],\r\n      caption: contentResult.caption,\r\n      hashtags: contentResult.hashtags\r\n    };\r\n\r\n  } catch (error) {\r\n    throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build enhanced prompt for Revo 2.0\r\n */\r\nfunction buildEnhancedPrompt(options: Revo20GenerationOptions, concept: any): string {\r\n  const { businessType, platform, brandProfile, aspectRatio = '1:1', visualStyle = 'modern' } = options;\r\n\r\n  return `Create a high-quality, professional ${businessType} design for ${platform}.\r\n\r\nCREATIVE CONCEPT: ${concept.concept}\r\n\r\nVISUAL DIRECTION: ${concept.visualDirection}\r\n\r\nDESIGN REQUIREMENTS:\r\n- Style: ${visualStyle}, premium quality\r\n- Aspect Ratio: ${aspectRatio}\r\n- Platform: ${platform} optimized\r\n- Business: ${brandProfile.businessName || businessType}\r\n- Location: ${brandProfile.location || 'Professional setting'}\r\n\r\nDESIGN ELEMENTS:\r\n${concept.designElements.map((element: string) => `- ${element}`).join('\\n')}\r\n\r\nMOOD & EMOTIONS:\r\n- Target emotions: ${concept.targetEmotions.join(', ')}\r\n- Mood keywords: ${concept.moodKeywords.join(', ')}\r\n\r\nBRAND INTEGRATION:\r\n- Colors: ${brandProfile.primaryColor ? `Primary: ${brandProfile.primaryColor}, Accent: ${brandProfile.accentColor}, Background: ${brandProfile.backgroundColor}` : concept.colorSuggestions.join(', ')}\r\n- Business name: ${brandProfile.businessName || businessType}\r\n- Logo: ${brandProfile.logoDataUrl ? 'Include provided brand logo prominently' : 'No logo provided'}\r\n- Professional, trustworthy appearance\r\n\r\nQUALITY STANDARDS:\r\n- Ultra-high resolution and clarity\r\n- Professional composition\r\n- Perfect typography and text rendering\r\n- MAXIMUM 3 COLORS ONLY (use brand colors if provided)\r\n- NO LINES: no decorative lines, borders, dividers, or linear elements\r\n- Platform-optimized dimensions\r\n- Brand consistency throughout\r\n- Clean, minimalist design with 50%+ white space\r\n\r\nCreate a stunning, professional design that captures the essence of this ${businessType} business.`;\r\n}\r\n\r\n/**\r\n * Generate image using Gemini 2.5 Flash Image Preview with logo support\r\n */\r\nasync function generateImageWithGemini(prompt: string, options: Revo20GenerationOptions): Promise<{ imageUrl: string }> {\r\n  const maxRetries = 3;\r\n  let lastError: any;\r\n\r\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n    try {\r\n\r\n      const model = ai.getGenerativeModel({\r\n        model: REVO_2_0_MODEL,\r\n        generationConfig: {\r\n          temperature: 0.7,\r\n          topP: 0.9,\r\n          topK: 40,\r\n          maxOutputTokens: 2048,\r\n        },\r\n      });\r\n\r\n      // Prepare the generation request with logo if available\r\n      const generationParts = [\r\n        'You are an expert graphic designer using Gemini 2.5 Flash Image Preview. Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.',\r\n        prompt\r\n      ];\r\n\r\n      // If logo is provided, include it in the generation\r\n      if (options.brandProfile.logoDataUrl) {\r\n\r\n        // Extract the base64 data and mime type from the data URL\r\n        const logoMatch = options.brandProfile.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);\r\n        if (logoMatch) {\r\n          const [, mimeType, base64Data] = logoMatch;\r\n\r\n          generationParts.push({\r\n            inlineData: {\r\n              data: base64Data,\r\n              mimeType: mimeType\r\n            }\r\n          });\r\n\r\n          // Update the prompt to reference the provided logo\r\n          const logoPrompt = `\\n\\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;\r\n          generationParts[1] = prompt + logoPrompt;\r\n        } else {\r\n        }\r\n      }\r\n\r\n      const result = await model.generateContent(generationParts);\r\n      const response = await result.response;\r\n\r\n\r\n      // Extract image data from Gemini response (same as Revo 1.0)\r\n      const parts = response.candidates?.[0]?.content?.parts || [];\r\n      let imageUrl = '';\r\n\r\n      for (const part of parts) {\r\n        if (part.inlineData) {\r\n          const imageData = part.inlineData.data;\r\n          const mimeType = part.inlineData.mimeType;\r\n          imageUrl = `data:${mimeType};base64,${imageData}`;\r\n          break;\r\n        }\r\n      }\r\n\r\n      if (!imageUrl) {\r\n        throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');\r\n      }\r\n\r\n      return { imageUrl };\r\n\r\n    } catch (error: any) {\r\n      lastError = error;\r\n\r\n      if (attempt === maxRetries) {\r\n        break;\r\n      }\r\n\r\n      const waitTime = Math.pow(2, attempt) * 1000;\r\n      await new Promise(resolve => setTimeout(resolve, waitTime));\r\n    }\r\n  }\r\n\r\n  throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);\r\n}\r\n\r\n/**\r\n * Generate caption and hashtags with AI-powered contextual generation\r\n */\r\nasync function generateCaptionAndHashtags(options: Revo20GenerationOptions, concept: any): Promise<{ caption: string; hashtags: string[] }> {\r\n  const { businessType, platform, brandProfile } = options;\r\n\r\n  const prompt = `Create engaging ${platform} content for a ${businessType} business.\r\n\r\nBusiness Details:\r\n- Name: ${brandProfile.businessName || businessType}\r\n- Type: ${businessType}\r\n- Location: ${brandProfile.location || 'Local area'}\r\n- Concept: ${concept.concept}\r\n- Catchwords: ${concept.catchwords.join(', ')}\r\n\r\nCreate:\r\n1. A catchy, engaging caption (2-3 sentences max) that incorporates the concept and catchwords naturally\r\n2. 10 highly relevant, specific hashtags that are:\r\n   - Specific to this business and location\r\n   - Mix of business-specific, location-based, industry-relevant, and platform-optimized\r\n   - Avoid generic hashtags like #business, #professional, #quality, #local\r\n   - Discoverable and relevant to the target audience\r\n   - Appropriate for ${platform}\r\n\r\nMake the content authentic, locally relevant, and engaging for ${platform}.\r\n\r\nFormat as JSON:\r\n{\r\n  \"caption\": \"Your engaging caption here\",\r\n  \"hashtags\": [\"#SpecificHashtag1\", \"#LocationBasedHashtag\", \"#IndustryRelevant\", ...]\r\n}`;\r\n\r\n  const response = await openai.chat.completions.create({\r\n    model: 'gpt-4o',\r\n    messages: [{ role: 'user', content: prompt }],\r\n    temperature: 0.7,\r\n    max_tokens: 600\r\n  });\r\n\r\n  try {\r\n    let responseContent = response.choices[0].message.content || '{}';\r\n\r\n    // Remove markdown code blocks if present\r\n    responseContent = responseContent.replace(/```json\\s*|\\s*```/g, '').trim();\r\n\r\n    const result = JSON.parse(responseContent);\r\n\r\n    // Validate the response\r\n    if (result.caption && Array.isArray(result.hashtags) && result.hashtags.length > 0) {\r\n      return {\r\n        caption: result.caption,\r\n        hashtags: result.hashtags.slice(0, 10) // Ensure max 10 hashtags\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to parse AI content response:', error);\r\n  }\r\n\r\n  // Fallback with contextual generation (no hardcoded placeholders)\r\n  return generateContextualFallback(businessType, brandProfile, platform, concept);\r\n}\r\n\r\n/**\r\n * Generate contextual fallback content without hardcoded placeholders\r\n */\r\nfunction generateContextualFallback(\r\n  businessType: string,\r\n  brandProfile: BrandProfile,\r\n  platform: string,\r\n  concept: any\r\n): { caption: string; hashtags: string[] } {\r\n  const businessName = brandProfile.businessName || businessType;\r\n  const location = brandProfile.location || 'your area';\r\n\r\n  // Generate contextual caption\r\n  const caption = `${concept.catchwords[0] || 'Discover'} what makes ${businessName} special in ${location}! ${concept.concept || 'Experience the difference with our exceptional service.'}`;\r\n\r\n  // Generate contextual hashtags\r\n  const hashtags: string[] = [];\r\n\r\n  // Business-specific\r\n  hashtags.push(`#${businessName.replace(/\\s+/g, '')}`);\r\n  hashtags.push(`#${businessType.replace(/\\s+/g, '')}Business`);\r\n\r\n  // Location-based\r\n  const locationParts = location.split(',').map(part => part.trim());\r\n  locationParts.forEach(part => {\r\n    if (part.length > 2) {\r\n      hashtags.push(`#${part.replace(/\\s+/g, '')}`);\r\n    }\r\n  });\r\n\r\n  // Platform-specific contextual\r\n  if (platform === 'instagram') {\r\n    hashtags.push('#InstagramContent', '#VisualStory');\r\n  } else if (platform === 'facebook') {\r\n    hashtags.push('#FacebookPost', '#CommunityBusiness');\r\n  } else if (platform === 'linkedin') {\r\n    hashtags.push('#LinkedInBusiness', '#ProfessionalServices');\r\n  } else if (platform === 'tiktok') {\r\n    hashtags.push('#TikTokBusiness', '#CreativeContent');\r\n  }\r\n\r\n  // Add current date context\r\n  const today = new Date();\r\n  const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n  hashtags.push(`#${dayName}Vibes`);\r\n\r\n  return {\r\n    caption,\r\n    hashtags: [...new Set(hashtags)].slice(0, 10) // Remove duplicates and limit to 10\r\n  };\r\n}\r\n\r\n/**\r\n * Test Revo 2.0 availability\r\n */\r\nexport async function testRevo20Availability(): Promise<boolean> {\r\n  try {\r\n\r\n    const model = ai.getGenerativeModel({ model: REVO_2_0_MODEL });\r\n    const response = await model.generateContent('Create a simple test image with the text \"Revo 2.0 Test\" on a modern gradient background');\r\n\r\n    const parts = response.candidates?.[0]?.content?.parts || [];\r\n    let hasImage = false;\r\n\r\n    for (const part of parts) {\r\n      if (part.inlineData) {\r\n        hasImage = true;\r\n      }\r\n    }\r\n\r\n    if (hasImage) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;;;AAGA,wBAAwB;AACxB,MAAM,KAAK,IAAI,gKAAA,CAAA,qBAAkB,CAAC,QAAQ,GAAG,CAAC,cAAc;AAC5D,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAAE,QAAQ,QAAQ,GAAG,CAAC,cAAc;AAAE;AAEhE,8FAA8F;AAC9F,MAAM,iBAAiB;AAuBvB;;CAEC,GACD,eAAe,wBAAwB,OAAgC;IACrE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,QAAQ,EAAE,GAAG;IAEzE,MAAM,SAAS,CAAC,wDAAwD,EAAE,aAAa;2DAC9B,EAAE,SAAS;;;QAG9D,EAAE,aAAa;YACX,EAAE,SAAS;SACd,EAAE,YAAY;YACX,EAAE,aAAa,QAAQ,IAAI,SAAS;SACvC,EAAE,aAAa,YAAY,IAAI,aAAa;;;;;;;;;;;;;;;;;;CAkBpD,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;QACvD,yCAAyC;QACzC,MAAM,eAAe,QAAQ,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;QACnF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,UAAU;YAC/D,YAAY;gBAAC;gBAAW;gBAAgB;gBAAW;gBAAS;aAAS;YACrE,iBAAiB;YACjB,gBAAgB;gBAAC;gBAAoB;gBAAwB;gBAAgB;aAAgB;YAC7F,kBAAkB;gBAAC;gBAAW;gBAAW;aAAU;YACnD,cAAc;gBAAC;gBAAgB;gBAAe;gBAAU;aAAQ;YAChE,gBAAgB;gBAAC;gBAAS;gBAAc;aAAc;QACxD;IACF;AACF;AAKO,eAAe,mBAAmB,OAAgC;IACvE,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,oCAAoC;QACpC,MAAM,UAAU,MAAM,wBAAwB;QAE9C,gCAAgC;QAChC,MAAM,iBAAiB,oBAAoB,SAAS;QAEpD,6DAA6D;QAC7D,MAAM,cAAc,MAAM,wBAAwB,gBAAgB;QAElE,wCAAwC;QACxC,MAAM,gBAAgB,MAAM,2BAA2B,SAAS;QAEhE,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,OAAO;YACP,cAAc;YACd;YACA,qBAAqB;gBACnB;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS,cAAc,OAAO;YAC9B,UAAU,cAAc,QAAQ;QAClC;IAEF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3G;AACF;AAEA;;CAEC,GACD,SAAS,oBAAoB,OAAgC,EAAE,OAAY;IACzE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE,cAAc,QAAQ,EAAE,GAAG;IAE9F,OAAO,CAAC,oCAAoC,EAAE,aAAa,YAAY,EAAE,SAAS;;kBAElE,EAAE,QAAQ,OAAO,CAAC;;kBAElB,EAAE,QAAQ,eAAe,CAAC;;;SAGnC,EAAE,YAAY;gBACP,EAAE,YAAY;YAClB,EAAE,SAAS;YACX,EAAE,aAAa,YAAY,IAAI,aAAa;YAC5C,EAAE,aAAa,QAAQ,IAAI,uBAAuB;;;AAG9D,EAAE,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,UAAoB,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM;;;mBAG1D,EAAE,QAAQ,cAAc,CAAC,IAAI,CAAC,MAAM;iBACtC,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;;;UAGzC,EAAE,aAAa,YAAY,GAAG,CAAC,SAAS,EAAE,aAAa,YAAY,CAAC,UAAU,EAAE,aAAa,WAAW,CAAC,cAAc,EAAE,aAAa,eAAe,EAAE,GAAG,QAAQ,gBAAgB,CAAC,IAAI,CAAC,MAAM;iBACvL,EAAE,aAAa,YAAY,IAAI,aAAa;QACrD,EAAE,aAAa,WAAW,GAAG,4CAA4C,mBAAmB;;;;;;;;;;;;;yEAa3B,EAAE,aAAa,UAAU,CAAC;AACnG;AAEA;;CAEC,GACD,eAAe,wBAAwB,MAAc,EAAE,OAAgC;IACrF,MAAM,aAAa;IACnB,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;gBAClC,OAAO;gBACP,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;YACF;YAEA,wDAAwD;YACxD,MAAM,kBAAkB;gBACtB;gBACA;aACD;YAED,oDAAoD;YACpD,IAAI,QAAQ,YAAY,CAAC,WAAW,EAAE;gBAEpC,0DAA0D;gBAC1D,MAAM,YAAY,QAAQ,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;gBACzD,IAAI,WAAW;oBACb,MAAM,GAAG,UAAU,WAAW,GAAG;oBAEjC,gBAAgB,IAAI,CAAC;wBACnB,YAAY;4BACV,MAAM;4BACN,UAAU;wBACZ;oBACF;oBAEA,mDAAmD;oBACnD,MAAM,aAAa,CAAC,6MAA6M,CAAC;oBAClO,eAAe,CAAC,EAAE,GAAG,SAAS;gBAChC,OAAO,CACP;YACF;YAEA,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;YAGtC,6DAA6D;YAC7D,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;YAC5D,IAAI,WAAW;YAEf,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,UAAU,EAAE;oBACnB,MAAM,YAAY,KAAK,UAAU,CAAC,IAAI;oBACtC,MAAM,WAAW,KAAK,UAAU,CAAC,QAAQ;oBACzC,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,EAAE,WAAW;oBACjD;gBACF;YACF;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;gBAAE;YAAS;QAEpB,EAAE,OAAO,OAAY;YACnB,YAAY;YAEZ,IAAI,YAAY,YAAY;gBAC1B;YACF;YAEA,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,WAAW;YACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,WAAW,WAAW,EAAE,WAAW,WAAW,iBAAiB;AACrH;AAEA;;CAEC,GACD,eAAe,2BAA2B,OAAgC,EAAE,OAAY;IACtF,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG;IAEjD,MAAM,SAAS,CAAC,gBAAgB,EAAE,SAAS,eAAe,EAAE,aAAa;;;QAGnE,EAAE,aAAa,YAAY,IAAI,aAAa;QAC5C,EAAE,aAAa;YACX,EAAE,aAAa,QAAQ,IAAI,aAAa;WACzC,EAAE,QAAQ,OAAO,CAAC;cACf,EAAE,QAAQ,UAAU,CAAC,IAAI,CAAC,MAAM;;;;;;;;;qBASzB,EAAE,SAAS;;+DAE+B,EAAE,SAAS;;;;;;CAMzE,CAAC;IAEA,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,OAAO;QACP,UAAU;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAO;SAAE;QAC7C,aAAa;QACb,YAAY;IACd;IAEA,IAAI;QACF,IAAI,kBAAkB,SAAS,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI;QAE7D,yCAAyC;QACzC,kBAAkB,gBAAgB,OAAO,CAAC,sBAAsB,IAAI,IAAI;QAExE,MAAM,SAAS,KAAK,KAAK,CAAC;QAE1B,wBAAwB;QACxB,IAAI,OAAO,OAAO,IAAI,MAAM,OAAO,CAAC,OAAO,QAAQ,KAAK,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;YAClF,OAAO;gBACL,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,yBAAyB;YAClE;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,wCAAwC;IACvD;IAEA,kEAAkE;IAClE,OAAO,2BAA2B,cAAc,cAAc,UAAU;AAC1E;AAEA;;CAEC,GACD,SAAS,2BACP,YAAoB,EACpB,YAA0B,EAC1B,QAAgB,EAChB,OAAY;IAEZ,MAAM,eAAe,aAAa,YAAY,IAAI;IAClD,MAAM,WAAW,aAAa,QAAQ,IAAI;IAE1C,8BAA8B;IAC9B,MAAM,UAAU,GAAG,QAAQ,UAAU,CAAC,EAAE,IAAI,WAAW,YAAY,EAAE,aAAa,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,OAAO,IAAI,2DAA2D;IAE3L,+BAA+B;IAC/B,MAAM,WAAqB,EAAE;IAE7B,oBAAoB;IACpB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,KAAK;IACpD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;IAE5D,iBAAiB;IACjB,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC/D,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,KAAK;QAC9C;IACF;IAEA,+BAA+B;IAC/B,IAAI,aAAa,aAAa;QAC5B,SAAS,IAAI,CAAC,qBAAqB;IACrC,OAAO,IAAI,aAAa,YAAY;QAClC,SAAS,IAAI,CAAC,iBAAiB;IACjC,OAAO,IAAI,aAAa,YAAY;QAClC,SAAS,IAAI,CAAC,qBAAqB;IACrC,OAAO,IAAI,aAAa,UAAU;QAChC,SAAS,IAAI,CAAC,mBAAmB;IACnC;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,UAAU,MAAM,kBAAkB,CAAC,SAAS;QAAE,SAAS;IAAO;IACpE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC;IAEhC,OAAO;QACL;QACA,UAAU;eAAI,IAAI,IAAI;SAAU,CAAC,KAAK,CAAC,GAAG,IAAI,oCAAoC;IACpF;AACF;AAKO,eAAe;IACpB,IAAI;QAEF,MAAM,QAAQ,GAAG,kBAAkB,CAAC;YAAE,OAAO;QAAe;QAC5D,MAAM,WAAW,MAAM,MAAM,eAAe,CAAC;QAE7C,MAAM,QAAQ,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,SAAS,EAAE;QAC5D,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,EAAE;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,UAAU;YACZ,OAAO;QACT,OAAO;YACL,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/api/generate-revo-2.0/route.ts"], "sourcesContent": ["/**\r\n * Revo 2.0 Generation API Route\r\n * Uses Gemini 2.5 Flash Image Preview for next-generation content creation\r\n */\r\n\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { generateWithRevo20 } from '@/ai/revo-2.0-service';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n\r\n    const body = await request.json();\r\n    const {\r\n      businessType,\r\n      platform,\r\n      brandProfile,\r\n      visualStyle,\r\n      imageText,\r\n      aspectRatio,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!businessType || !platform || !brandProfile) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Missing required fields: businessType, platform, brandProfile'\r\n      }, { status: 400 });\r\n    }\r\n\r\n    console.log('Revo 2.0 generation request:', {\r\n      businessType,\r\n      platform,\r\n      visualStyle: visualStyle || 'modern',\r\n      aspectRatio: aspectRatio || '1:1'\r\n    });\r\n\r\n    // Generate content with Revo 2.0\r\n    const result = await generateWithRevo20({\r\n      businessType,\r\n      platform,\r\n      visualStyle: visualStyle || 'modern',\r\n      imageText: imageText || `${brandProfile.businessName || businessType} - Premium Content`,\r\n      brandProfile,\r\n      aspectRatio,\r\n      includePeopleInDesigns,\r\n      useLocalLanguage\r\n    });\r\n\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      imageUrl: result.imageUrl,\r\n      model: result.model,\r\n      qualityScore: result.qualityScore,\r\n      processingTime: result.processingTime,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      caption: result.caption,\r\n      hashtags: result.hashtags,\r\n      message: 'Revo 2.0 content generated successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Unknown error',\r\n      message: 'Revo 2.0 generation failed'\r\n    }, { status: 500 });\r\n  }\r\n}\r\n\r\nexport async function GET() {\r\n  return NextResponse.json({\r\n    message: 'Revo 2.0 Generation API',\r\n    description: 'Use POST method to generate content with Revo 2.0',\r\n    requiredFields: ['businessType', 'platform', 'brandProfile'],\r\n    optionalFields: ['visualStyle', 'imageText', 'aspectRatio'],\r\n    model: 'Gemini 2.5 Flash Image Preview',\r\n    version: '2.0.0'\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QAEF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,SAAS,EACT,WAAW,EACX,sBAAsB,EACtB,gBAAgB,EACjB,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,cAAc;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,gCAAgC;YAC1C;YACA;YACA,aAAa,eAAe;YAC5B,aAAa,eAAe;QAC9B;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;YACtC;YACA;YACA,aAAa,eAAe;YAC5B,WAAW,aAAa,GAAG,aAAa,YAAY,IAAI,aAAa,kBAAkB,CAAC;YACxF;YACA;YACA;YACA;QACF;QAGA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,cAAc,OAAO,YAAY;YACjC,gBAAgB,OAAO,cAAc;YACrC,qBAAqB,OAAO,mBAAmB;YAC/C,SAAS,OAAO,OAAO;YACvB,UAAU,OAAO,QAAQ;YACzB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,aAAa;QACb,gBAAgB;YAAC;YAAgB;YAAY;SAAe;QAC5D,gBAAgB;YAAC;YAAe;YAAa;SAAc;QAC3D,OAAO;QACP,SAAS;IACX;AACF", "debugId": null}}]}