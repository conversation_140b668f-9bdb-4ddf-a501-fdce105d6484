{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/capabilities.ts"], "sourcesContent": ["/**\n * Model Capabilities Configuration\n * Defines what each model version can do\n */\n\nimport type { ModelCapabilities, RevoModelId } from '../types/model-types';\nimport type { Platform } from '@/lib/types';\n\n// Define capabilities for each model version\nexport const modelCapabilities: Record<RevoModelId, ModelCapabilities> = {\n  'revo-1.0': {\n    // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Not supported in 1.0\n    enhancedFeatures: true, // Upgraded from false\n    artifactSupport: false, // Basic model doesn't support artifacts\n    aspectRatios: ['1:1'], // Only square images\n    maxQuality: 9, // Upgraded from 7 for Gemini 2.5 Flash Image Preview\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Enhanced brand consistency\n    realTimeContext: true, // Now enabled for better context\n    perfectTextRendering: true, // NEW: Gemini 2.5 Flash Image Preview feature\n    highResolution: true // NEW: 2048x2048 support\n  },\n\n  'revo-1.5': {\n    // Enhanced model with advanced features\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Video coming in 2.0\n    enhancedFeatures: true,\n    artifactSupport: true, // Full artifact support\n    aspectRatios: ['1:1', '16:9', '9:16'], // Multiple aspect ratios\n    maxQuality: 8, // Superior quality\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Advanced brand consistency\n    realTimeContext: true // Real-time context and trends\n  },\n\n\n\n  'revo-2.0': {\n    // Premium Next-Gen AI model\n    contentGeneration: true,\n    designGeneration: true,\n    videoGeneration: false, // Focus on premium image generation\n    enhancedFeatures: true,\n    artifactSupport: true, // Premium artifact support\n    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'], // All aspect ratios\n    maxQuality: 10, // Maximum quality with native image generation\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'] as Platform[],\n    advancedPrompting: true,\n    brandConsistency: true, // Perfect brand consistency with character consistency\n    realTimeContext: true, // Premium real-time features\n    characterConsistency: true, // NEW: Maintain character consistency across images\n    intelligentEditing: true, // NEW: Inpainting, outpainting, targeted edits\n    multimodalReasoning: true // NEW: Advanced visual context understanding\n  }\n};\n\n// Capability comparison matrix\nexport const capabilityMatrix = {\n  contentGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from standard\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  designGeneration: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'enhanced',\n    'revo-2.0': 'premium'\n  },\n  videoGeneration: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'none'\n  },\n  artifactSupport: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'full',\n    'revo-2.0': 'premium'\n  },\n  brandConsistency: {\n    'revo-1.0': 'enhanced', // Upgraded from basic\n    'revo-1.5': 'advanced',\n    'revo-2.0': 'perfect'\n  },\n  characterConsistency: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  },\n  intelligentEditing: {\n    'revo-1.0': 'none',\n    'revo-1.5': 'none',\n    'revo-2.0': 'advanced'\n  }\n} as const;\n\n// Feature availability by model\nexport const featureAvailability = {\n  // Content features\n  hashtagGeneration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  catchyWords: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  subheadlines: ['revo-1.5', 'revo-2.0'],\n  callToAction: ['revo-1.5', 'revo-2.0'],\n  contentVariants: ['revo-1.5', 'revo-2.0'],\n\n  // Design features\n  logoIntegration: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  brandColors: ['revo-1.0', 'revo-1.5', 'revo-2.0'],\n  designExamples: ['revo-1.5', 'revo-2.0'],\n  textOverlay: ['revo-1.5', 'revo-2.0'],\n  multipleAspectRatios: ['revo-1.5', 'revo-2.0'],\n\n  // Advanced features\n  realTimeContext: ['revo-1.5', 'revo-2.0'],\n  trendingTopics: ['revo-1.5', 'revo-2.0'],\n  marketIntelligence: ['revo-1.5', 'revo-2.0'],\n  competitorAnalysis: ['revo-2.0'],\n\n  // Revo 2.0 exclusive features\n  characterConsistency: ['revo-2.0'],\n  intelligentEditing: ['revo-2.0'],\n  inpainting: ['revo-2.0'],\n  outpainting: ['revo-2.0'],\n  multimodalReasoning: ['revo-2.0'],\n\n  // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)\n  perfectTextRendering: ['revo-1.0', 'revo-2.0'],\n  highResolution: ['revo-1.0', 'revo-2.0'],\n\n  // Artifact features\n  artifactReference: ['revo-1.5'],\n  exactUseArtifacts: ['revo-1.5'],\n  textOverlayArtifacts: ['revo-1.5']\n} as const;\n\n// Platform-specific capabilities\nexport const platformCapabilities = {\n  Instagram: {\n    'revo-1.0': {\n      aspectRatios: ['1:1'],\n      maxQuality: 7,\n      features: ['basic-design', 'hashtags']\n    },\n    'revo-1.5': {\n      aspectRatios: ['1:1', '9:16'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'hashtags', 'stories', 'reels-ready']\n    }\n  },\n  Facebook: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'page-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'page-posts', 'stories']\n    }\n  },\n  Twitter: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'tweets']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'tweets', 'threads']\n    }\n  },\n  LinkedIn: {\n    'revo-1.0': {\n      aspectRatios: ['16:9'],\n      maxQuality: 7,\n      features: ['basic-design', 'professional-posts']\n    },\n    'revo-1.5': {\n      aspectRatios: ['16:9', '1:1'],\n      maxQuality: 8,\n      features: ['enhanced-design', 'professional-posts', 'articles']\n    }\n  }\n} as const;\n\n// Utility functions\nexport function hasCapability(modelId: RevoModelId, capability: keyof ModelCapabilities): boolean {\n  return modelCapabilities[modelId][capability] as boolean;\n}\n\nexport function getCapabilityLevel(modelId: RevoModelId, capability: keyof typeof capabilityMatrix): string {\n  return capabilityMatrix[capability][modelId];\n}\n\nexport function hasFeature(modelId: RevoModelId, feature: keyof typeof featureAvailability): boolean {\n  return featureAvailability[feature].includes(modelId);\n}\n\nexport function getModelsByFeature(feature: keyof typeof featureAvailability): RevoModelId[] {\n  return [...featureAvailability[feature]] as RevoModelId[];\n}\n\nexport function getPlatformCapabilities(modelId: RevoModelId, platform: Platform) {\n  return platformCapabilities[platform]?.[modelId] || null;\n}\n\nexport function getMaxQualityForPlatform(modelId: RevoModelId, platform: Platform): number {\n  const platformCaps = getPlatformCapabilities(modelId, platform);\n  return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;\n}\n\nexport function getSupportedAspectRatios(modelId: RevoModelId, platform?: Platform): string[] {\n  if (platform) {\n    const platformCaps = getPlatformCapabilities(modelId, platform);\n    return platformCaps?.aspectRatios ? [...platformCaps.aspectRatios] : [...modelCapabilities[modelId].aspectRatios];\n  }\n  return [...modelCapabilities[modelId].aspectRatios];\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAMM,MAAM,oBAA4D;IACvE,YAAY;QACV,yEAAyE;QACzE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;SAAM;QACrB,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,gBAAgB,KAAK,yBAAyB;IAChD;IAEA,YAAY;QACV,wCAAwC;QACxC,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;SAAO;QACrC,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB,KAAK,+BAA+B;IACvD;IAIA,YAAY;QACV,4BAA4B;QAC5B,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAQ;YAAQ;YAAO;SAAM;QACnD,YAAY;QACZ,oBAAoB;YAAC;YAAa;YAAY;YAAW;SAAW;QACpE,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;QACtB,oBAAoB;QACpB,qBAAqB,KAAK,6CAA6C;IACzE;AACF;AAGO,MAAM,mBAAmB;IAC9B,mBAAmB;QACjB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,iBAAiB;QACf,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,kBAAkB;QAChB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,sBAAsB;QACpB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IACA,oBAAoB;QAClB,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,mBAAmB;IACnB,mBAAmB;QAAC;QAAY;QAAY;KAAW;IACvD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,cAAc;QAAC;QAAY;KAAW;IACtC,cAAc;QAAC;QAAY;KAAW;IACtC,iBAAiB;QAAC;QAAY;KAAW;IAEzC,kBAAkB;IAClB,iBAAiB;QAAC;QAAY;QAAY;KAAW;IACrD,aAAa;QAAC;QAAY;QAAY;KAAW;IACjD,gBAAgB;QAAC;QAAY;KAAW;IACxC,aAAa;QAAC;QAAY;KAAW;IACrC,sBAAsB;QAAC;QAAY;KAAW;IAE9C,oBAAoB;IACpB,iBAAiB;QAAC;QAAY;KAAW;IACzC,gBAAgB;QAAC;QAAY;KAAW;IACxC,oBAAoB;QAAC;QAAY;KAAW;IAC5C,oBAAoB;QAAC;KAAW;IAEhC,8BAA8B;IAC9B,sBAAsB;QAAC;KAAW;IAClC,oBAAoB;QAAC;KAAW;IAChC,YAAY;QAAC;KAAW;IACxB,aAAa;QAAC;KAAW;IACzB,qBAAqB;QAAC;KAAW;IAEjC,uEAAuE;IACvE,sBAAsB;QAAC;QAAY;KAAW;IAC9C,gBAAgB;QAAC;QAAY;KAAW;IAExC,oBAAoB;IACpB,mBAAmB;QAAC;KAAW;IAC/B,mBAAmB;QAAC;KAAW;IAC/B,sBAAsB;QAAC;KAAW;AACpC;AAGO,MAAM,uBAAuB;IAClC,WAAW;QACT,YAAY;YACV,cAAc;gBAAC;aAAM;YACrB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAW;QACxC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAO;aAAO;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAY;gBAAW;aAAc;QACrE;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAa;QAC1C;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAc;aAAU;QACxD;IACF;IACA,SAAS;QACP,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAS;QACtC;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAU;aAAU;QACpD;IACF;IACA,UAAU;QACR,YAAY;YACV,cAAc;gBAAC;aAAO;YACtB,YAAY;YACZ,UAAU;gBAAC;gBAAgB;aAAqB;QAClD;QACA,YAAY;YACV,cAAc;gBAAC;gBAAQ;aAAM;YAC7B,YAAY;YACZ,UAAU;gBAAC;gBAAmB;gBAAsB;aAAW;QACjE;IACF;AACF;AAGO,SAAS,cAAc,OAAoB,EAAE,UAAmC;IACrF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,WAAW;AAC/C;AAEO,SAAS,mBAAmB,OAAoB,EAAE,UAAyC;IAChG,OAAO,gBAAgB,CAAC,WAAW,CAAC,QAAQ;AAC9C;AAEO,SAAS,WAAW,OAAoB,EAAE,OAAyC;IACxF,OAAO,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AAEO,SAAS,mBAAmB,OAAyC;IAC1E,OAAO;WAAI,mBAAmB,CAAC,QAAQ;KAAC;AAC1C;AAEO,SAAS,wBAAwB,OAAoB,EAAE,QAAkB;IAC9E,OAAO,oBAAoB,CAAC,SAAS,EAAE,CAAC,QAAQ,IAAI;AACtD;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAkB;IAC/E,MAAM,eAAe,wBAAwB,SAAS;IACtD,OAAO,cAAc,cAAc,iBAAiB,CAAC,QAAQ,CAAC,UAAU;AAC1E;AAEO,SAAS,yBAAyB,OAAoB,EAAE,QAAmB;IAChF,IAAI,UAAU;QACZ,MAAM,eAAe,wBAAwB,SAAS;QACtD,OAAO,cAAc,eAAe;eAAI,aAAa,YAAY;SAAC,GAAG;eAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;SAAC;IACnH;IACA,OAAO;WAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY;KAAC;AACrD", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/pricing.ts"], "sourcesContent": ["/**\n * Model Pricing Configuration\n * Defines credit costs and pricing tiers for each model\n */\n\nimport type { ModelPricing, RevoModelId } from '../types/model-types';\n\n// Pricing configuration for each model\nexport const modelPricing: Record<RevoModelId, ModelPricing> = {\n  'revo-1.0': {\n    creditsPerGeneration: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerDesign: 1.5, // Slightly increased from 1 for enhanced capabilities\n    creditsPerVideo: 0, // Video not supported\n    tier: 'enhanced' // Upgraded from basic\n  },\n\n  'revo-1.5': {\n    creditsPerGeneration: 2,\n    creditsPerDesign: 2,\n    creditsPerVideo: 0, // Video not supported yet\n    tier: 'premium'\n  },\n\n\n\n  'revo-2.0': {\n    creditsPerGeneration: 5,\n    creditsPerDesign: 5,\n    creditsPerVideo: 0, // Focus on premium image generation\n    tier: 'premium'\n  }\n};\n\n// Pricing tiers and their characteristics\nexport const pricingTiers = {\n  basic: {\n    name: 'Basic',\n    description: 'Reliable and cost-effective',\n    maxCreditsPerGeneration: 2,\n    features: [\n      'Standard quality generation',\n      'Basic brand consistency',\n      'Core platform support',\n      'Standard processing speed'\n    ],\n    recommendedFor: [\n      'Small businesses',\n      'Personal brands',\n      'Budget-conscious users',\n      'Basic content needs'\n    ]\n  },\n  premium: {\n    name: 'Premium',\n    description: 'Enhanced features and quality',\n    maxCreditsPerGeneration: 10,\n    features: [\n      'Enhanced quality generation',\n      'Advanced brand consistency',\n      'Full platform support',\n      'Artifact integration',\n      'Real-time context',\n      'Trending topics',\n      'Multiple aspect ratios'\n    ],\n    recommendedFor: [\n      'Growing businesses',\n      'Marketing agencies',\n      'Content creators',\n      'Professional brands'\n    ]\n  },\n  enterprise: {\n    name: 'Enterprise',\n    description: 'Maximum quality and features',\n    maxCreditsPerGeneration: 20,\n    features: [\n      'Premium quality generation',\n      '4K resolution support',\n      'Perfect text rendering',\n      'Advanced style controls',\n      'Priority processing',\n      'Dedicated support',\n      'Custom integrations'\n    ],\n    recommendedFor: [\n      'Large enterprises',\n      'Premium brands',\n      'High-volume users',\n      'Quality-focused campaigns'\n    ]\n  }\n} as const;\n\n// Credit packages and their values\nexport const creditPackages = {\n  starter: {\n    name: 'Starter Pack',\n    credits: 50,\n    price: 9.99,\n    pricePerCredit: 0.20,\n    bestFor: 'revo-1.0',\n    estimatedGenerations: {\n      'revo-1.0': 50,\n      'revo-1.5': 25,\n      'imagen-4': 5\n    }\n  },\n  professional: {\n    name: 'Professional Pack',\n    credits: 200,\n    price: 29.99,\n    pricePerCredit: 0.15,\n    bestFor: 'revo-1.5',\n    estimatedGenerations: {\n      'revo-1.0': 200,\n      'revo-1.5': 100,\n      'imagen-4': 20\n    }\n  },\n  business: {\n    name: 'Business Pack',\n    credits: 500,\n    price: 59.99,\n    pricePerCredit: 0.12,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 500,\n      'revo-1.5': 250,\n      'imagen-4': 50\n    }\n  },\n  enterprise: {\n    name: 'Enterprise Pack',\n    credits: 1000,\n    price: 99.99,\n    pricePerCredit: 0.10,\n    bestFor: 'imagen-4',\n    estimatedGenerations: {\n      'revo-1.0': 1000,\n      'revo-1.5': 500,\n      'revo-2.0': 200,\n      'imagen-4': 100\n    }\n  }\n} as const;\n\n// Usage-based pricing calculations\nexport const usageCalculations = {\n  // Calculate cost for a specific generation request\n  calculateGenerationCost(modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): number {\n    const pricing = modelPricing[modelId];\n\n    switch (type) {\n      case 'content':\n        return pricing.creditsPerGeneration;\n      case 'design':\n        return pricing.creditsPerDesign;\n      case 'video':\n        return pricing.creditsPerVideo || 0;\n      default:\n        return pricing.creditsPerGeneration;\n    }\n  },\n\n  // Calculate total cost for multiple generations\n  calculateBatchCost(requests: { modelId: RevoModelId; type: 'content' | 'design' | 'video' }[]): number {\n    return requests.reduce((total, request) => {\n      return total + this.calculateGenerationCost(request.modelId, request.type);\n    }, 0);\n  },\n\n  // Estimate monthly cost based on usage patterns\n  estimateMonthlyCost(usage: {\n    modelId: RevoModelId;\n    generationsPerDay: number;\n    designsPerDay: number;\n    videosPerDay?: number;\n  }): {\n    dailyCost: number;\n    monthlyCost: number;\n    recommendedPackage: keyof typeof creditPackages;\n  } {\n    const pricing = modelPricing[usage.modelId];\n\n    const dailyCost =\n      (usage.generationsPerDay * pricing.creditsPerGeneration) +\n      (usage.designsPerDay * pricing.creditsPerDesign) +\n      ((usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0));\n\n    const monthlyCost = dailyCost * 30;\n\n    // Recommend package based on monthly cost\n    let recommendedPackage: keyof typeof creditPackages = 'starter';\n    if (monthlyCost > 400) recommendedPackage = 'enterprise';\n    else if (monthlyCost > 150) recommendedPackage = 'business';\n    else if (monthlyCost > 50) recommendedPackage = 'professional';\n\n    return {\n      dailyCost,\n      monthlyCost,\n      recommendedPackage\n    };\n  },\n\n  // Check if user has enough credits for a request\n  canAfford(userCredits: number, modelId: RevoModelId, type: 'content' | 'design' | 'video' = 'content'): boolean {\n    const cost = this.calculateGenerationCost(modelId, type);\n    return userCredits >= cost;\n  },\n\n  // Get the best model within budget\n  getBestModelForBudget(availableCredits: number, type: 'content' | 'design' | 'video' = 'content'): RevoModelId[] {\n    const affordableModels: RevoModelId[] = [];\n\n    for (const [modelId, pricing] of Object.entries(modelPricing)) {\n      const cost = type === 'content' ? pricing.creditsPerGeneration :\n        type === 'design' ? pricing.creditsPerDesign :\n          pricing.creditsPerVideo || 0;\n\n      if (cost <= availableCredits && cost > 0) {\n        affordableModels.push(modelId as RevoModelId);\n      }\n    }\n\n    // Sort by quality (higher credit cost usually means higher quality)\n    return affordableModels.sort((a, b) => {\n      const costA = this.calculateGenerationCost(a, type);\n      const costB = this.calculateGenerationCost(b, type);\n      return costB - costA; // Descending order (highest quality first)\n    });\n  }\n};\n\n// Pricing display utilities\nexport const pricingDisplay = {\n  // Format credits for display\n  formatCredits(credits: number): string {\n    if (credits >= 1000) {\n      return `${(credits / 1000).toFixed(1)}K`;\n    }\n    return credits.toString();\n  },\n\n  // Format price for display\n  formatPrice(price: number): string {\n    return `$${price.toFixed(2)}`;\n  },\n\n  // Get pricing tier info\n  getTierInfo(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    return pricingTiers[pricing.tier];\n  },\n\n  // Get cost comparison between models\n  compareCosts(modelA: RevoModelId, modelB: RevoModelId) {\n    const costA = modelPricing[modelA].creditsPerGeneration;\n    const costB = modelPricing[modelB].creditsPerGeneration;\n\n    const difference = Math.abs(costA - costB);\n    const percentDifference = ((difference / Math.min(costA, costB)) * 100).toFixed(0);\n\n    return {\n      cheaper: costA < costB ? modelA : modelB,\n      moreExpensive: costA > costB ? modelA : modelB,\n      difference,\n      percentDifference: `${percentDifference}%`,\n      ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`\n    };\n  },\n\n  // Get value proposition for each model\n  getValueProposition(modelId: RevoModelId) {\n    const pricing = modelPricing[modelId];\n    const tierInfo = pricingTiers[pricing.tier];\n\n    return {\n      model: modelId,\n      tier: pricing.tier,\n      creditsPerGeneration: pricing.creditsPerGeneration,\n      valueScore: tierInfo.features.length / pricing.creditsPerGeneration, // Features per credit\n      description: tierInfo.description,\n      bestFor: tierInfo.recommendedFor\n    };\n  }\n};\n\n// Export utility functions\nexport function getModelPricing(modelId: RevoModelId): ModelPricing {\n  return modelPricing[modelId];\n}\n\nexport function getAllPricing(): Record<RevoModelId, ModelPricing> {\n  return modelPricing;\n}\n\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModelId[] {\n  return Object.entries(modelPricing)\n    .filter(([_, pricing]) => pricing.tier === tier)\n    .map(([modelId]) => modelId as RevoModelId);\n}\n\nexport function getCheapestModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((cheapest, [modelId, pricing]) => {\n      const currentCheapest = modelPricing[cheapest as RevoModelId];\n      return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ?\n        modelId as RevoModelId : cheapest as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n\nexport function getMostExpensiveModel(): RevoModelId {\n  return Object.entries(modelPricing)\n    .reduce((mostExpensive, [modelId, pricing]) => {\n      const currentMostExpensive = modelPricing[mostExpensive as RevoModelId];\n      return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ?\n        modelId as RevoModelId : mostExpensive as RevoModelId;\n    }, 'revo-1.0' as RevoModelId);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAKM,MAAM,eAAkD;IAC7D,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM,WAAW,sBAAsB;IACzC;IAEA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;IAIA,YAAY;QACV,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO;QACL,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,yBAAyB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B,SAAS;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,SAAS;QACT,sBAAsB;YACpB,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,mDAAmD;IACnD,yBAAwB,OAAoB,EAAE,OAAuC,SAAS;QAC5F,MAAM,UAAU,YAAY,CAAC,QAAQ;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,oBAAoB;YACrC,KAAK;gBACH,OAAO,QAAQ,gBAAgB;YACjC,KAAK;gBACH,OAAO,QAAQ,eAAe,IAAI;YACpC;gBACE,OAAO,QAAQ,oBAAoB;QACvC;IACF;IAEA,gDAAgD;IAChD,oBAAmB,QAA0E;QAC3F,OAAO,SAAS,MAAM,CAAC,CAAC,OAAO;YAC7B,OAAO,QAAQ,IAAI,CAAC,uBAAuB,CAAC,QAAQ,OAAO,EAAE,QAAQ,IAAI;QAC3E,GAAG;IACL;IAEA,gDAAgD;IAChD,qBAAoB,KAKnB;QAKC,MAAM,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC;QAE3C,MAAM,YACJ,AAAC,MAAM,iBAAiB,GAAG,QAAQ,oBAAoB,GACtD,MAAM,aAAa,GAAG,QAAQ,gBAAgB,GAC9C,CAAC,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC;QAE5D,MAAM,cAAc,YAAY;QAEhC,0CAA0C;QAC1C,IAAI,qBAAkD;QACtD,IAAI,cAAc,KAAK,qBAAqB;aACvC,IAAI,cAAc,KAAK,qBAAqB;aAC5C,IAAI,cAAc,IAAI,qBAAqB;QAEhD,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,iDAAiD;IACjD,WAAU,WAAmB,EAAE,OAAoB,EAAE,OAAuC,SAAS;QACnG,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS;QACnD,OAAO,eAAe;IACxB;IAEA,mCAAmC;IACnC,uBAAsB,gBAAwB,EAAE,OAAuC,SAAS;QAC9F,MAAM,mBAAkC,EAAE;QAE1C,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAe;YAC7D,MAAM,OAAO,SAAS,YAAY,QAAQ,oBAAoB,GAC5D,SAAS,WAAW,QAAQ,gBAAgB,GAC1C,QAAQ,eAAe,IAAI;YAE/B,IAAI,QAAQ,oBAAoB,OAAO,GAAG;gBACxC,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,oEAAoE;QACpE,OAAO,iBAAiB,IAAI,CAAC,CAAC,GAAG;YAC/B,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,GAAG;YAC9C,OAAO,QAAQ,OAAO,2CAA2C;QACnE;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,6BAA6B;IAC7B,eAAc,OAAe;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO,GAAG,CAAC,UAAU,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C;QACA,OAAO,QAAQ,QAAQ;IACzB;IAEA,2BAA2B;IAC3B,aAAY,KAAa;QACvB,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAC/B;IAEA,wBAAwB;IACxB,aAAY,OAAoB;QAC9B,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,OAAO,YAAY,CAAC,QAAQ,IAAI,CAAC;IACnC;IAEA,qCAAqC;IACrC,cAAa,MAAmB,EAAE,MAAmB;QACnD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QACvD,MAAM,QAAQ,YAAY,CAAC,OAAO,CAAC,oBAAoB;QAEvD,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ;QACpC,MAAM,oBAAoB,CAAC,AAAC,aAAa,KAAK,GAAG,CAAC,OAAO,SAAU,GAAG,EAAE,OAAO,CAAC;QAEhF,OAAO;YACL,SAAS,QAAQ,QAAQ,SAAS;YAClC,eAAe,QAAQ,QAAQ,SAAS;YACxC;YACA,mBAAmB,GAAG,kBAAkB,CAAC,CAAC;YAC1C,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,QAAQ;QAC9D;IACF;IAEA,uCAAuC;IACvC,qBAAoB,OAAoB;QACtC,MAAM,UAAU,YAAY,CAAC,QAAQ;QACrC,MAAM,WAAW,YAAY,CAAC,QAAQ,IAAI,CAAC;QAE3C,OAAO;YACL,OAAO;YACP,MAAM,QAAQ,IAAI;YAClB,sBAAsB,QAAQ,oBAAoB;YAClD,YAAY,SAAS,QAAQ,CAAC,MAAM,GAAG,QAAQ,oBAAoB;YACnE,aAAa,SAAS,WAAW;YACjC,SAAS,SAAS,cAAc;QAClC;IACF;AACF;AAGO,SAAS,gBAAgB,OAAoB;IAClD,OAAO,YAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAK,QAAQ,IAAI,KAAK,MAC1C,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;AACxB;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,UAAU,CAAC,SAAS,QAAQ;QACnC,MAAM,kBAAkB,YAAY,CAAC,SAAwB;QAC7D,OAAO,QAAQ,oBAAoB,GAAG,gBAAgB,oBAAoB,GACxE,UAAyB;IAC7B,GAAG;AACP;AAEO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,cACnB,MAAM,CAAC,CAAC,eAAe,CAAC,SAAS,QAAQ;QACxC,MAAM,uBAAuB,YAAY,CAAC,cAA6B;QACvE,OAAO,QAAQ,oBAAoB,GAAG,qBAAqB,oBAAoB,GAC7E,UAAyB;IAC7B,GAAG;AACP", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/config/model-configs.ts"], "sourcesContent": ["/**\r\n * Model Configurations\r\n * Centralized configuration for all Revo model versions\r\n */\r\n\r\nimport type { RevoModel, RevoModelId } from '../types/model-types';\r\nimport { modelCapabilities } from './capabilities';\r\nimport { modelPricing } from './pricing';\r\n\r\n// Base configurations for different AI services\r\nconst baseConfigs = {\r\n  'gemini-2.0': {\r\n    aiService: 'gemini-2.0' as const,\r\n    fallbackServices: ['gemini-2.5', 'openai'],\r\n    maxRetries: 3,\r\n    timeout: 30000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 85,\r\n      enhancementLevel: 5\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.7,\r\n      maxTokens: 2048,\r\n      topP: 0.9,\r\n      topK: 40\r\n    }\r\n  },\r\n  'gemini-2.5': {\r\n    aiService: 'gemini-2.5' as const,\r\n    fallbackServices: ['gemini-2.0', 'openai'],\r\n    maxRetries: 2,\r\n    timeout: 45000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 90,\r\n      enhancementLevel: 7\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.8,\r\n      maxTokens: 4096,\r\n      topP: 0.95,\r\n      topK: 50\r\n    }\r\n  },\r\n  'openai': {\r\n    aiService: 'openai' as const,\r\n    fallbackServices: ['gemini-2.5', 'gemini-2.0'],\r\n    maxRetries: 3,\r\n    timeout: 35000,\r\n    qualitySettings: {\r\n      imageResolution: '1024x1024',\r\n      compressionLevel: 88,\r\n      enhancementLevel: 6\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.7,\r\n      maxTokens: 3000,\r\n      topP: 0.9\r\n    }\r\n  },\r\n  'gemini-2.5-flash-image': {\r\n    aiService: 'gemini-2.5-flash-image' as const,\r\n    fallbackServices: ['imagen-4', 'gemini-2.5'],\r\n    maxRetries: 3,\r\n    timeout: 45000,\r\n    qualitySettings: {\r\n      imageResolution: '2048x2048',     // Ultra HD resolution\r\n      compressionLevel: 95,             // Maximum quality\r\n      enhancementLevel: 8               // Reduced for cleaner designs (was 10)\r\n    },\r\n    promptSettings: {\r\n      temperature: 0.4,                 // Reduced creativity for consistency (was 0.9)\r\n      maxTokens: 4096,                  // Detailed prompts for clean instructions\r\n      topP: 0.7,                        // Reduced variety for cleaner results (was 0.95)\r\n      topK: 30                          // Fewer creative choices for consistency (was 60)\r\n    }\r\n  }\r\n};\r\n\r\n// Model definitions\r\nexport const modelConfigs: Record<RevoModelId, RevoModel> = {\r\n  'revo-1.0': {\r\n    id: 'revo-1.0',\r\n    name: 'Revo 1.0',\r\n    version: '1.0.0',\r\n    description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',\r\n    longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',\r\n    icon: 'Zap',\r\n    badge: 'Enhanced',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-1.0'],\r\n    config: baseConfigs['gemini-2.5-flash-image'],\r\n    pricing: modelPricing['revo-1.0'],\r\n    features: [\r\n      'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',\r\n      '1:1 Images with High Resolution',\r\n      'Core Features',\r\n      'Proven Performance',\r\n      'Multi-platform Support',\r\n      'Enhanced Brand Consistency',\r\n      'Perfect Text Rendering',\r\n      'High-Resolution Output (2048x2048)'\r\n    ],\r\n    releaseDate: '2024-01-15',\r\n    lastUpdated: '2025-01-27'\r\n  },\r\n\r\n  'revo-1.5': {\r\n    id: 'revo-1.5',\r\n    name: 'Revo 1.5',\r\n    version: '1.5.0',\r\n    description: 'Enhanced Model - Advanced Features',\r\n    longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',\r\n    icon: 'Sparkles',\r\n    badge: 'Enhanced',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-1.5'],\r\n    config: {\r\n      ...baseConfigs['gemini-2.5'],\r\n      qualitySettings: {\r\n        ...baseConfigs['gemini-2.5'].qualitySettings,\r\n        enhancementLevel: 8\r\n      }\r\n    },\r\n    pricing: modelPricing['revo-1.5'],\r\n    features: [\r\n      'Advanced AI Engine',\r\n      'Superior Quality',\r\n      'Enhanced Design',\r\n      'Smart Optimizations',\r\n      'Professional Templates',\r\n      'Advanced Brand Integration',\r\n      'Real-time Context',\r\n      'Trending Topics Integration'\r\n    ],\r\n    releaseDate: '2024-06-20',\r\n    lastUpdated: '2024-12-15'\r\n  },\r\n\r\n\r\n\r\n  'revo-2.0': {\r\n    id: 'revo-2.0',\r\n    name: 'Revo 2.0',\r\n    version: '2.0.0',\r\n    description: 'Next-Gen Model - Advanced AI with native image generation',\r\n    longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',\r\n    icon: 'Rocket',\r\n    badge: 'Next-Gen',\r\n    badgeVariant: 'default',\r\n    status: 'enhanced',\r\n    capabilities: modelCapabilities['revo-2.0'],\r\n    config: baseConfigs['gemini-2.5-flash-image'],\r\n    pricing: modelPricing['revo-2.0'],\r\n    features: [\r\n      'Next-Gen AI Engine',\r\n      'Native Image Generation',\r\n      'Character Consistency',\r\n      'Intelligent Editing',\r\n      'Inpainting & Outpainting',\r\n      'Multimodal Reasoning',\r\n      'All Aspect Ratios',\r\n      'Perfect Brand Consistency'\r\n    ],\r\n    releaseDate: '2025-01-27',\r\n    lastUpdated: '2025-01-27'\r\n  }\r\n};\r\n\r\n// Helper functions\r\nexport function getModelConfig(modelId: RevoModelId): RevoModel {\r\n  const config = modelConfigs[modelId];\r\n  if (!config) {\r\n    throw new Error(`Model configuration not found for: ${modelId}`);\r\n  }\r\n  return config;\r\n}\r\n\r\nexport function getAllModelConfigs(): RevoModel[] {\r\n  return Object.values(modelConfigs);\r\n}\r\n\r\nexport function getModelsByStatus(status: RevoModel['status']): RevoModel[] {\r\n  return getAllModelConfigs().filter(model => model.status === status);\r\n}\r\n\r\nexport function getModelsByTier(tier: 'basic' | 'premium' | 'enterprise'): RevoModel[] {\r\n  return getAllModelConfigs().filter(model => model.pricing.tier === tier);\r\n}\r\n\r\nexport function getLatestModels(): RevoModel[] {\r\n  return getAllModelConfigs()\r\n    .sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())\r\n    .slice(0, 3);\r\n}\r\n\r\nexport function getRecommendedModel(): RevoModel {\r\n  // Return Revo 1.5 as the recommended balanced option\r\n  return modelConfigs['revo-1.5'];\r\n}\r\n\r\nexport function getModelForBudget(maxCredits: number): RevoModel[] {\r\n  return getAllModelConfigs()\r\n    .filter(model => model.pricing.creditsPerGeneration <= maxCredits)\r\n    .sort((a, b) => a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);\r\n}\r\n\r\n// Model comparison utilities\r\nexport function compareModels(modelA: RevoModelId, modelB: RevoModelId) {\r\n  const configA = getModelConfig(modelA);\r\n  const configB = getModelConfig(modelB);\r\n\r\n  return {\r\n    quality: {\r\n      a: configA.capabilities.maxQuality,\r\n      b: configB.capabilities.maxQuality,\r\n      winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB\r\n    },\r\n    cost: {\r\n      a: configA.pricing.creditsPerGeneration,\r\n      b: configB.pricing.creditsPerGeneration,\r\n      winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB\r\n    },\r\n    features: {\r\n      a: configA.features.length,\r\n      b: configB.features.length,\r\n      winner: configA.features.length > configB.features.length ? modelA : modelB\r\n    },\r\n    status: {\r\n      a: configA.status,\r\n      b: configB.status,\r\n      recommendation: configA.status === 'stable' || configB.status === 'stable' ?\r\n        (configA.status === 'stable' ? modelA : modelB) : modelA\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAGD;AACA;;;AAEA,gDAAgD;AAChD,MAAM,cAAc;IAClB,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,cAAc;QACZ,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAS;QAC1C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,UAAU;QACR,WAAW;QACX,kBAAkB;YAAC;YAAc;SAAa;QAC9C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;QACR;IACF;IACA,0BAA0B;QACxB,WAAW;QACX,kBAAkB;YAAC;YAAY;SAAa;QAC5C,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB,EAAgB,uCAAuC;QAC3E;QACA,gBAAgB;YACd,aAAa;YACb,WAAW;YACX,MAAM;YACN,MAAM,GAA4B,kDAAkD;QACtF;IACF;AACF;AAGO,MAAM,eAA+C;IAC1D,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ;YACN,GAAG,WAAW,CAAC,aAAa;YAC5B,iBAAiB;gBACf,GAAG,WAAW,CAAC,aAAa,CAAC,eAAe;gBAC5C,kBAAkB;YACpB;QACF;QACA,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;IAIA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,cAAc,6IAAA,CAAA,oBAAiB,CAAC,WAAW;QAC3C,QAAQ,WAAW,CAAC,yBAAyB;QAC7C,SAAS,wIAAA,CAAA,eAAY,CAAC,WAAW;QACjC,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,aAAa;IACf;AACF;AAGO,SAAS,eAAe,OAAoB;IACjD,MAAM,SAAS,YAAY,CAAC,QAAQ;IACpC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS;IACjE;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAEO,SAAS,kBAAkB,MAA2B;IAC3D,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AAC/D;AAEO,SAAS,gBAAgB,IAAwC;IACtE,OAAO,qBAAqB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,IAAI,KAAK;AACrE;AAEO,SAAS;IACd,OAAO,qBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,IAClF,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,qDAAqD;IACrD,OAAO,YAAY,CAAC,WAAW;AACjC;AAEO,SAAS,kBAAkB,UAAkB;IAClD,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,oBAAoB,IAAI,YACtD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,oBAAoB,GAAG,EAAE,OAAO,CAAC,oBAAoB;AACnF;AAGO,SAAS,cAAc,MAAmB,EAAE,MAAmB;IACpE,MAAM,UAAU,eAAe;IAC/B,MAAM,UAAU,eAAe;IAE/B,OAAO;QACL,SAAS;YACP,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,GAAG,QAAQ,YAAY,CAAC,UAAU;YAClC,QAAQ,QAAQ,YAAY,CAAC,UAAU,GAAG,QAAQ,YAAY,CAAC,UAAU,GAAG,SAAS;QACvF;QACA,MAAM;YACJ,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,GAAG,QAAQ,OAAO,CAAC,oBAAoB;YACvC,QAAQ,QAAQ,OAAO,CAAC,oBAAoB,GAAG,QAAQ,OAAO,CAAC,oBAAoB,GAAG,SAAS;QACjG;QACA,UAAU;YACR,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,GAAG,QAAQ,QAAQ,CAAC,MAAM;YAC1B,QAAQ,QAAQ,QAAQ,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,MAAM,GAAG,SAAS;QACvE;QACA,QAAQ;YACN,GAAG,QAAQ,MAAM;YACjB,GAAG,QAAQ,MAAM;YACjB,gBAAgB,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,KAAK,WAC/D,QAAQ,MAAM,KAAK,WAAW,SAAS,SAAU;QACtD;IACF;AACF", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/weather.ts"], "sourcesContent": ["// src/services/weather.ts\r\nimport fetch from 'node-fetch';\r\n\r\nconst API_KEY = process.env.OPENWEATHERMAP_API_KEY;\r\nconst BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';\r\n\r\n/**\r\n * Fetches the current weather for a given location.\r\n * @param location A string in the format \"City, ST\" or \"City, Country\".\r\n * @returns A human-readable weather description string, or null if an error occurs.\r\n */\r\nexport async function getWeather(location: string): Promise<string | null> {\r\n  if (!API_KEY || API_KEY === 'YOUR_OPENWEATHERMAP_API_KEY' || API_KEY.length < 20) {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${BASE_URL}?q=${location}&appid=${API_KEY}&units=imperial`);\r\n    if (!response.ok) {\r\n      // Return null to allow the flow to continue without weather data\r\n      return `Could not retrieve weather information due to an API error (Status: ${response.status})`;\r\n    }\r\n    \r\n    const data: any = await response.json();\r\n\r\n    if (data && data.weather && data.main) {\r\n      const description = data.weather[0].description;\r\n      const temp = Math.round(data.main.temp);\r\n      return `${description} with a temperature of ${temp}°F`;\r\n    }\r\n    return null;\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;AAC1B;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,sBAAsB;AAClD,MAAM,WAAW;AAOV,eAAe,WAAW,QAAgB;IAC/C,IAAI,CAAC,WAAW,YAAY,iCAAiC,QAAQ,MAAM,GAAG,IAAI;QAChF,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,UAAK,AAAD,EAAE,GAAG,SAAS,GAAG,EAAE,SAAS,OAAO,EAAE,QAAQ,eAAe,CAAC;QACxF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,iEAAiE;YACjE,OAAO,CAAC,oEAAoE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAClG;QAEA,MAAM,OAAY,MAAM,SAAS,IAAI;QAErC,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;YACrC,MAAM,cAAc,KAAK,OAAO,CAAC,EAAE,CAAC,WAAW;YAC/C,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI;YACtC,OAAO,GAAG,YAAY,uBAAuB,EAAE,KAAK,EAAE,CAAC;QACzD;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/events.ts"], "sourcesContent": ["// src/services/events.ts\r\nimport fetch from 'node-fetch';\r\nimport { format, add } from 'date-fns';\r\n\r\nconst API_KEY = process.env.EVENTBRITE_PRIVATE_TOKEN;\r\nconst BASE_URL = 'https://www.eventbriteapi.com/v3/events/search/';\r\n\r\n/**\r\n * Fetches local events for a given location and date using Eventbrite.\r\n * @param location A string in the format \"City, ST\" or \"City\".\r\n * @param date The date for which to find events.\r\n * @returns A string summarizing local events, or null if an error occurs.\r\n */\r\nexport async function getEvents(location: string, date: Date): Promise<string | null> {\r\n  if (!API_KEY || API_KEY === 'YOUR_EVENTBRITE_PRIVATE_TOKEN' || API_KEY.length < 10) {\r\n    return null;\r\n  }\r\n\r\n  // Eventbrite is more flexible with location strings.\r\n  const city = location.split(',')[0].trim();\r\n  \r\n  // Search for events starting from today up to one week from now to get more results\r\n  const startDate = format(date, \"yyyy-MM-dd'T'HH:mm:ss'Z'\");\r\n  const endDate = format(add(date, { days: 7 }), \"yyyy-MM-dd'T'HH:mm:ss'Z'\");\r\n\r\n  try {\r\n    const url = `${BASE_URL}?location.address=${city}&start_date.range_start=${startDate}&start_date.range_end=${endDate}&sort_by=date`;\r\n    \r\n    const response = await fetch(url, {\r\n        headers: {\r\n            'Authorization': `Bearer ${API_KEY}`,\r\n            'Accept': 'application/json',\r\n        }\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      const errorBody = await response.text();\r\n      // Return null to allow the flow to continue without event data\r\n      return `Could not retrieve local event information due to an API error (Status: ${response.status}).`;\r\n    }\r\n\r\n    const data: any = await response.json();\r\n\r\n    if (data.events && data.events.length > 0) {\r\n      const eventNames = data.events.slice(0, 5).map((event: any) => event.name.text);\r\n      return `local events happening soon include: ${eventNames.join(', ')}`;\r\n    } else {\r\n      return 'no major local events found on Eventbrite for the upcoming week';\r\n    }\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;AACzB;AACA;AAAA;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,wBAAwB;AACpD,MAAM,WAAW;AAQV,eAAe,UAAU,QAAgB,EAAE,IAAU;IAC1D,IAAI,CAAC,WAAW,YAAY,mCAAmC,QAAQ,MAAM,GAAG,IAAI;QAClF,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAExC,oFAAoF;IACpF,MAAM,YAAY,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAC/B,MAAM,UAAU,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,mIAAA,CAAA,MAAG,AAAD,EAAE,MAAM;QAAE,MAAM;IAAE,IAAI;IAE/C,IAAI;QACF,MAAM,MAAM,GAAG,SAAS,kBAAkB,EAAE,KAAK,wBAAwB,EAAE,UAAU,sBAAsB,EAAE,QAAQ,aAAa,CAAC;QAEnI,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAC9B,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,SAAS;gBACpC,UAAU;YACd;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,+DAA+D;YAC/D,OAAO,CAAC,wEAAwE,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC;QACvG;QAEA,MAAM,OAAY,MAAM,SAAS,IAAI;QAErC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;YACzC,MAAM,aAAa,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,MAAM,IAAI,CAAC,IAAI;YAC9E,OAAO,CAAC,qCAAqC,EAAE,WAAW,IAAI,CAAC,OAAO;QACxE,OAAO;YACL,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/tools/local-data.ts"], "sourcesContent": ["'use server';\r\n\r\n/**\r\n * @fileOverview Defines Genkit tools for fetching local weather and event data.\r\n * This allows the AI to dynamically decide when to pull in local information\r\n * to make social media posts more relevant and timely.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\nimport { getWeather } from '@/services/weather';\r\nimport { getEvents } from '@/services/events';\r\n\r\nexport const getWeatherTool = ai.defineTool(\r\n  {\r\n    name: 'getWeather',\r\n    description: 'Gets the current weather for a specific location. Use this to make posts more relevant to the current conditions.',\r\n    inputSchema: z.object({\r\n      location: z.string().describe('The city and state, e.g., \"San Francisco, CA\"'),\r\n    }),\r\n    outputSchema: z.string(),\r\n  },\r\n  async (input) => {\r\n    const weather = await getWeather(input.location);\r\n    return weather || 'Could not retrieve weather information.';\r\n  }\r\n);\r\n\r\nexport const getEventsTool = ai.defineTool(\r\n  {\r\n    name: 'getEvents',\r\n    description: 'Finds local events happening on or after the current date for a specific location. Use this to create timely posts about local happenings.',\r\n    inputSchema: z.object({\r\n        location: z.string().describe('The city and state, e.g., \"San Francisco, CA\"'),\r\n    }),\r\n    outputSchema: z.string(),\r\n  },\r\n  async (input) => {\r\n    // Tools will always be called with the current date\r\n    const events = await getEvents(input.location, new Date());\r\n    return events || 'Could not retrieve local event information.';\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;CAIC,GAED;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,iBAAiB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,aAAa;IACb,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC;IACA,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;AACxB,GACA,OAAO;IACL,MAAM,UAAU,MAAM,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ;IAC/C,OAAO,WAAW;AACpB;AAGK,MAAM,gBAAgB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAClB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC;IACA,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;AACxB,GACA,OAAO;IACL,oDAAoD;IACpD,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ,EAAE,IAAI;IACnD,OAAO,UAAU;AACnB;;;IA5BW;IAeA;;AAfA,+OAAA;AAeA,+OAAA", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/tools/enhanced-local-data.ts"], "sourcesContent": ["/**\r\n * Enhanced Local Data Tools - Events and Weather Integration\r\n * \r\n * This module provides real-time local events and weather data\r\n * for contextually aware content generation.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\nexport interface LocalEvent {\r\n  name: string;\r\n  description?: string;\r\n  start_date: string;\r\n  end_date?: string;\r\n  venue?: string;\r\n  category: string;\r\n  url?: string;\r\n  is_free: boolean;\r\n  relevance_score: number; // 1-10 based on business type\r\n}\r\n\r\nexport interface WeatherContext {\r\n  temperature: number;\r\n  condition: string;\r\n  description: string;\r\n  humidity: number;\r\n  feels_like: number;\r\n  location: string;\r\n  content_opportunities: string[];\r\n  business_impact: string;\r\n}\r\n\r\n/**\r\n * Enhanced Eventbrite Events Tool\r\n */\r\nexport const getEnhancedEventsTool = ai.defineTool({\r\n  name: 'getEnhancedEvents',\r\n  description: 'Fetch local events from Eventbrite API that are relevant to the business type and location',\r\n  input: z.object({\r\n    location: z.string().describe('Location for events (city, country or coordinates)'),\r\n    businessType: z.string().describe('Business type to filter relevant events'),\r\n    radius: z.string().optional().default('25km').describe('Search radius for events'),\r\n    timeframe: z.string().optional().default('this_week').describe('Time period: today, this_week, this_month')\r\n  }),\r\n  output: z.array(z.object({\r\n    name: z.string(),\r\n    description: z.string().optional(),\r\n    start_date: z.string(),\r\n    venue: z.string().optional(),\r\n    category: z.string(),\r\n    url: z.string().optional(),\r\n    is_free: z.boolean(),\r\n    relevance_score: z.number()\r\n  })),\r\n}, async (input) => {\r\n  try {\r\n    if (!process.env.EVENTBRITE_API_KEY) {\r\n      return getEventsFallback(input.location, input.businessType);\r\n    }\r\n\r\n\r\n    // Convert location to coordinates if needed\r\n    const locationQuery = await geocodeLocation(input.location);\r\n    \r\n    // Build Eventbrite API request\r\n    const params = new URLSearchParams({\r\n      'location.address': input.location,\r\n      'location.within': input.radius,\r\n      'start_date.range_start': getDateRange(input.timeframe).start,\r\n      'start_date.range_end': getDateRange(input.timeframe).end,\r\n      'sort_by': 'relevance',\r\n      'page_size': '20',\r\n      'expand': 'venue,category'\r\n    });\r\n\r\n    const response = await fetch(\r\n      `https://www.eventbriteapi.com/v3/events/search/?${params}`,\r\n      {\r\n        headers: {\r\n          'Authorization': `Bearer ${process.env.EVENTBRITE_API_KEY}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Eventbrite API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Process and filter events by business relevance\r\n    const relevantEvents = processEventbriteEvents(data.events || [], input.businessType);\r\n    \r\n    return relevantEvents.slice(0, 10);\r\n\r\n  } catch (error) {\r\n    return getEventsFallback(input.location, input.businessType);\r\n  }\r\n});\r\n\r\n/**\r\n * Enhanced OpenWeather Tool\r\n */\r\nexport const getEnhancedWeatherTool = ai.defineTool({\r\n  name: 'getEnhancedWeather',\r\n  description: 'Fetch current weather and forecast with business context and content opportunities',\r\n  input: z.object({\r\n    location: z.string().describe('Location for weather (city, country)'),\r\n    businessType: z.string().describe('Business type to provide relevant weather context'),\r\n    includeForecast: z.boolean().optional().default(false).describe('Include 5-day forecast')\r\n  }),\r\n  output: z.object({\r\n    temperature: z.number(),\r\n    condition: z.string(),\r\n    description: z.string(),\r\n    humidity: z.number(),\r\n    feels_like: z.number(),\r\n    location: z.string(),\r\n    content_opportunities: z.array(z.string()),\r\n    business_impact: z.string(),\r\n    forecast: z.array(z.object({\r\n      date: z.string(),\r\n      temperature: z.number(),\r\n      condition: z.string(),\r\n      business_opportunity: z.string()\r\n    })).optional()\r\n  }),\r\n}, async (input) => {\r\n  try {\r\n    if (!process.env.OPENWEATHER_API_KEY) {\r\n      return getWeatherFallback(input.location, input.businessType);\r\n    }\r\n\r\n\r\n    // Current weather\r\n    const currentParams = new URLSearchParams({\r\n      q: input.location,\r\n      appid: process.env.OPENWEATHER_API_KEY!,\r\n      units: 'metric'\r\n    });\r\n\r\n    const currentResponse = await fetch(\r\n      `https://api.openweathermap.org/data/2.5/weather?${currentParams}`\r\n    );\r\n\r\n    if (!currentResponse.ok) {\r\n      throw new Error(`OpenWeather API error: ${currentResponse.status}`);\r\n    }\r\n\r\n    const currentData = await currentResponse.json();\r\n\r\n    // Process weather data with business context\r\n    const weatherContext = processWeatherData(currentData, input.businessType);\r\n\r\n    // Get forecast if requested\r\n    if (input.includeForecast) {\r\n      const forecastParams = new URLSearchParams({\r\n        q: input.location,\r\n        appid: process.env.OPENWEATHER_API_KEY!,\r\n        units: 'metric'\r\n      });\r\n\r\n      const forecastResponse = await fetch(\r\n        `https://api.openweathermap.org/data/2.5/forecast?${forecastParams}`\r\n      );\r\n\r\n      if (forecastResponse.ok) {\r\n        const forecastData = await forecastResponse.json();\r\n        weatherContext.forecast = processForecastData(forecastData, input.businessType);\r\n      }\r\n    }\r\n\r\n    return weatherContext;\r\n\r\n  } catch (error) {\r\n    return getWeatherFallback(input.location, input.businessType);\r\n  }\r\n});\r\n\r\n/**\r\n * Helper functions\r\n */\r\nasync function geocodeLocation(location: string): Promise<{lat: number, lon: number} | null> {\r\n  try {\r\n    if (!process.env.OPENWEATHER_API_KEY) return null;\r\n\r\n    const params = new URLSearchParams({\r\n      q: location,\r\n      limit: '1',\r\n      appid: process.env.OPENWEATHER_API_KEY!\r\n    });\r\n\r\n    const response = await fetch(\r\n      `https://api.openweathermap.org/geo/1.0/direct?${params}`\r\n    );\r\n\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      if (data.length > 0) {\r\n        return { lat: data[0].lat, lon: data[0].lon };\r\n      }\r\n    }\r\n  } catch (error) {\r\n  }\r\n  return null;\r\n}\r\n\r\nfunction getDateRange(timeframe: string): {start: string, end: string} {\r\n  const now = new Date();\r\n  const start = new Date(now);\r\n  let end = new Date(now);\r\n\r\n  switch (timeframe) {\r\n    case 'today':\r\n      end.setDate(end.getDate() + 1);\r\n      break;\r\n    case 'this_week':\r\n      end.setDate(end.getDate() + 7);\r\n      break;\r\n    case 'this_month':\r\n      end.setMonth(end.getMonth() + 1);\r\n      break;\r\n    default:\r\n      end.setDate(end.getDate() + 7);\r\n  }\r\n\r\n  return {\r\n    start: start.toISOString(),\r\n    end: end.toISOString()\r\n  };\r\n}\r\n\r\nfunction processEventbriteEvents(events: any[], businessType: string): LocalEvent[] {\r\n  return events.map(event => {\r\n    const relevanceScore = calculateEventRelevance(event, businessType);\r\n    \r\n    return {\r\n      name: event.name?.text || 'Unnamed Event',\r\n      description: event.description?.text?.substring(0, 200) || '',\r\n      start_date: event.start?.local || event.start?.utc || '',\r\n      end_date: event.end?.local || event.end?.utc,\r\n      venue: event.venue?.name || 'Online Event',\r\n      category: event.category?.name || 'General',\r\n      url: event.url,\r\n      is_free: event.is_free || false,\r\n      relevance_score: relevanceScore\r\n    };\r\n  }).filter(event => event.relevance_score >= 5)\r\n    .sort((a, b) => b.relevance_score - a.relevance_score);\r\n}\r\n\r\nfunction calculateEventRelevance(event: any, businessType: string): number {\r\n  let score = 5; // Base score\r\n\r\n  const eventName = (event.name?.text || '').toLowerCase();\r\n  const eventDescription = (event.description?.text || '').toLowerCase();\r\n  const eventCategory = (event.category?.name || '').toLowerCase();\r\n\r\n  // Business type relevance\r\n  const businessKeywords = getBusinessKeywords(businessType);\r\n  for (const keyword of businessKeywords) {\r\n    if (eventName.includes(keyword) || eventDescription.includes(keyword) || eventCategory.includes(keyword)) {\r\n      score += 2;\r\n    }\r\n  }\r\n\r\n  // Event category bonus\r\n  if (eventCategory.includes('business') || eventCategory.includes('networking')) {\r\n    score += 1;\r\n  }\r\n\r\n  // Free events get slight bonus for broader appeal\r\n  if (event.is_free) {\r\n    score += 1;\r\n  }\r\n\r\n  return Math.min(10, score);\r\n}\r\n\r\nfunction getBusinessKeywords(businessType: string): string[] {\r\n  const keywordMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'finance', 'banking', 'payment', 'blockchain', 'cryptocurrency', 'startup', 'tech'],\r\n    'restaurant': ['food', 'culinary', 'cooking', 'dining', 'chef', 'restaurant', 'hospitality'],\r\n    'fitness': ['fitness', 'health', 'wellness', 'gym', 'workout', 'nutrition', 'sports'],\r\n    'technology': ['tech', 'software', 'programming', 'ai', 'digital', 'innovation', 'startup'],\r\n    'beauty': ['beauty', 'cosmetics', 'skincare', 'wellness', 'spa', 'fashion'],\r\n    'retail': ['retail', 'shopping', 'ecommerce', 'business', 'sales', 'marketing']\r\n  };\r\n\r\n  return keywordMap[businessType.toLowerCase()] || ['business', 'networking', 'professional'];\r\n}\r\n\r\nfunction processWeatherData(weatherData: any, businessType: string): WeatherContext {\r\n  const temperature = Math.round(weatherData.main.temp);\r\n  const condition = weatherData.weather[0].main;\r\n  const description = weatherData.weather[0].description;\r\n  \r\n  return {\r\n    temperature,\r\n    condition,\r\n    description,\r\n    humidity: weatherData.main.humidity,\r\n    feels_like: Math.round(weatherData.main.feels_like),\r\n    location: weatherData.name,\r\n    content_opportunities: generateWeatherContentOpportunities(condition, temperature, businessType),\r\n    business_impact: generateBusinessWeatherImpact(condition, temperature, businessType)\r\n  };\r\n}\r\n\r\nfunction processForecastData(forecastData: any, businessType: string): any[] {\r\n  const dailyForecasts = forecastData.list.filter((_: any, index: number) => index % 8 === 0).slice(0, 5);\r\n  \r\n  return dailyForecasts.map((forecast: any) => ({\r\n    date: new Date(forecast.dt * 1000).toLocaleDateString(),\r\n    temperature: Math.round(forecast.main.temp),\r\n    condition: forecast.weather[0].main,\r\n    business_opportunity: generateBusinessWeatherImpact(forecast.weather[0].main, forecast.main.temp, businessType)\r\n  }));\r\n}\r\n\r\nfunction generateWeatherContentOpportunities(condition: string, temperature: number, businessType: string): string[] {\r\n  const opportunities: string[] = [];\r\n\r\n  // Temperature-based opportunities\r\n  if (temperature > 25) {\r\n    opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');\r\n  } else if (temperature < 10) {\r\n    opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');\r\n  }\r\n\r\n  // Condition-based opportunities\r\n  switch (condition.toLowerCase()) {\r\n    case 'rain':\r\n      opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');\r\n      break;\r\n    case 'sunny':\r\n    case 'clear':\r\n      opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');\r\n      break;\r\n    case 'clouds':\r\n      opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');\r\n      break;\r\n  }\r\n\r\n  // Business-specific weather opportunities\r\n  const businessOpportunities = getBusinessWeatherOpportunities(businessType, condition, temperature);\r\n  opportunities.push(...businessOpportunities);\r\n\r\n  return opportunities;\r\n}\r\n\r\nfunction generateBusinessWeatherImpact(condition: string, temperature: number, businessType: string): string {\r\n  const businessImpacts: Record<string, Record<string, string>> = {\r\n    'restaurant': {\r\n      'sunny': 'Perfect weather for outdoor dining and patio service',\r\n      'rain': 'Great opportunity to promote cozy indoor dining experience',\r\n      'hot': 'Ideal time to highlight refreshing drinks and cool dishes',\r\n      'cold': 'Perfect weather for warm comfort food and hot beverages'\r\n    },\r\n    'fitness': {\r\n      'sunny': 'Excellent conditions for outdoor workouts and activities',\r\n      'rain': 'Great time to promote indoor fitness programs',\r\n      'hot': 'Important to emphasize hydration and cooling strategies',\r\n      'cold': 'Perfect for promoting warm-up routines and indoor training'\r\n    },\r\n    'retail': {\r\n      'sunny': 'Great shopping weather, people are out and about',\r\n      'rain': 'Perfect time for online shopping promotions',\r\n      'hot': 'Opportunity to promote summer collections and cooling products',\r\n      'cold': 'Ideal for promoting warm clothing and comfort items'\r\n    }\r\n  };\r\n\r\n  const businessKey = businessType.toLowerCase();\r\n  const impacts = businessImpacts[businessKey] || businessImpacts['retail'];\r\n\r\n  if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';\r\n  if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';\r\n  \r\n  return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';\r\n}\r\n\r\nfunction getBusinessWeatherOpportunities(businessType: string, condition: string, temperature: number): string[] {\r\n  // Business-specific weather content opportunities\r\n  const opportunities: string[] = [];\r\n\r\n  if (businessType.toLowerCase().includes('restaurant')) {\r\n    if (condition === 'sunny') opportunities.push('Outdoor dining promotion', 'Fresh seasonal menu highlight');\r\n    if (condition === 'rain') opportunities.push('Cozy indoor atmosphere', 'Comfort food specials');\r\n  }\r\n\r\n  if (businessType.toLowerCase().includes('fitness')) {\r\n    if (condition === 'sunny') opportunities.push('Outdoor workout motivation', 'Vitamin D benefits');\r\n    if (temperature > 25) opportunities.push('Hydration importance', 'Summer fitness tips');\r\n  }\r\n\r\n  return opportunities;\r\n}\r\n\r\n// Fallback functions\r\nfunction getEventsFallback(location: string, businessType: string): LocalEvent[] {\r\n  return [\r\n    {\r\n      name: `${businessType} networking event in ${location}`,\r\n      description: `Local networking opportunity for ${businessType} professionals`,\r\n      start_date: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 days from now\r\n      venue: `${location} Business Center`,\r\n      category: 'Business & Professional',\r\n      is_free: true,\r\n      relevance_score: 8\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getWeatherFallback(location: string, businessType: string): WeatherContext {\r\n  return {\r\n    temperature: 22,\r\n    condition: 'Clear',\r\n    description: 'clear sky',\r\n    humidity: 60,\r\n    feels_like: 24,\r\n    location: location,\r\n    content_opportunities: ['Pleasant weather content opportunity', 'Comfortable conditions messaging'],\r\n    business_impact: 'Current weather conditions are favorable for business activities'\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;AA4BO,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,UAAU,CAAC;IACjD,MAAM;IACN,aAAa;IACb,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAClC,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAAC;QACvD,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,aAAa,QAAQ,CAAC;IACjE;IACA,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;QACd,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxB,SAAS,oIAAA,CAAA,IAAC,CAAC,OAAO;QAClB,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;IAC3B;AACF,GAAG,OAAO;IACR,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,OAAO,kBAAkB,MAAM,QAAQ,EAAE,MAAM,YAAY;QAC7D;QAGA,4CAA4C;QAC5C,MAAM,gBAAgB,MAAM,gBAAgB,MAAM,QAAQ;QAE1D,+BAA+B;QAC/B,MAAM,SAAS,IAAI,gBAAgB;YACjC,oBAAoB,MAAM,QAAQ;YAClC,mBAAmB,MAAM,MAAM;YAC/B,0BAA0B,aAAa,MAAM,SAAS,EAAE,KAAK;YAC7D,wBAAwB,aAAa,MAAM,SAAS,EAAE,GAAG;YACzD,WAAW;YACX,aAAa;YACb,UAAU;QACZ;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,gDAAgD,EAAE,QAAQ,EAC3D;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE;gBAC3D,gBAAgB;YAClB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,kDAAkD;QAClD,MAAM,iBAAiB,wBAAwB,KAAK,MAAM,IAAI,EAAE,EAAE,MAAM,YAAY;QAEpF,OAAO,eAAe,KAAK,CAAC,GAAG;IAEjC,EAAE,OAAO,OAAO;QACd,OAAO,kBAAkB,MAAM,QAAQ,EAAE,MAAM,YAAY;IAC7D;AACF;AAKO,MAAM,yBAAyB,mHAAA,CAAA,KAAE,CAAC,UAAU,CAAC;IAClD,MAAM;IACN,aAAa;IACb,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAClC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,QAAQ,CAAC;IAClE;IACA,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACf,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM;QACnB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,uBAAuB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QACvC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;QACzB,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACzB,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;YACd,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM;QAChC,IAAI,QAAQ;IACd;AACF,GAAG,OAAO;IACR,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,mBAAmB,EAAE;YACpC,OAAO,mBAAmB,MAAM,QAAQ,EAAE,MAAM,YAAY;QAC9D;QAGA,kBAAkB;QAClB,MAAM,gBAAgB,IAAI,gBAAgB;YACxC,GAAG,MAAM,QAAQ;YACjB,OAAO,QAAQ,GAAG,CAAC,mBAAmB;YACtC,OAAO;QACT;QAEA,MAAM,kBAAkB,MAAM,MAC5B,CAAC,gDAAgD,EAAE,eAAe;QAGpE,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACvB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,EAAE;QACpE;QAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;QAE9C,6CAA6C;QAC7C,MAAM,iBAAiB,mBAAmB,aAAa,MAAM,YAAY;QAEzE,4BAA4B;QAC5B,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,iBAAiB,IAAI,gBAAgB;gBACzC,GAAG,MAAM,QAAQ;gBACjB,OAAO,QAAQ,GAAG,CAAC,mBAAmB;gBACtC,OAAO;YACT;YAEA,MAAM,mBAAmB,MAAM,MAC7B,CAAC,iDAAiD,EAAE,gBAAgB;YAGtE,IAAI,iBAAiB,EAAE,EAAE;gBACvB,MAAM,eAAe,MAAM,iBAAiB,IAAI;gBAChD,eAAe,QAAQ,GAAG,oBAAoB,cAAc,MAAM,YAAY;YAChF;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,OAAO,mBAAmB,MAAM,QAAQ,EAAE,MAAM,YAAY;IAC9D;AACF;AAEA;;CAEC,GACD,eAAe,gBAAgB,QAAgB;IAC7C,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,mBAAmB,EAAE,OAAO;QAE7C,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO;YACP,OAAO,QAAQ,GAAG,CAAC,mBAAmB;QACxC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,8CAA8C,EAAE,QAAQ;QAG3D,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,OAAO;oBAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;oBAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;gBAAC;YAC9C;QACF;IACF,EAAE,OAAO,OAAO,CAChB;IACA,OAAO;AACT;AAEA,SAAS,aAAa,SAAiB;IACrC,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAI,MAAM,IAAI,KAAK;IAEnB,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;YAC5B;QACF,KAAK;YACH,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;YAC5B;QACF,KAAK;YACH,IAAI,QAAQ,CAAC,IAAI,QAAQ,KAAK;YAC9B;QACF;YACE,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK;IAChC;IAEA,OAAO;QACL,OAAO,MAAM,WAAW;QACxB,KAAK,IAAI,WAAW;IACtB;AACF;AAEA,SAAS,wBAAwB,MAAa,EAAE,YAAoB;IAClE,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,MAAM,iBAAiB,wBAAwB,OAAO;QAEtD,OAAO;YACL,MAAM,MAAM,IAAI,EAAE,QAAQ;YAC1B,aAAa,MAAM,WAAW,EAAE,MAAM,UAAU,GAAG,QAAQ;YAC3D,YAAY,MAAM,KAAK,EAAE,SAAS,MAAM,KAAK,EAAE,OAAO;YACtD,UAAU,MAAM,GAAG,EAAE,SAAS,MAAM,GAAG,EAAE;YACzC,OAAO,MAAM,KAAK,EAAE,QAAQ;YAC5B,UAAU,MAAM,QAAQ,EAAE,QAAQ;YAClC,KAAK,MAAM,GAAG;YACd,SAAS,MAAM,OAAO,IAAI;YAC1B,iBAAiB;QACnB;IACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,eAAe,IAAI,GACzC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,eAAe,GAAG,EAAE,eAAe;AACzD;AAEA,SAAS,wBAAwB,KAAU,EAAE,YAAoB;IAC/D,IAAI,QAAQ,GAAG,aAAa;IAE5B,MAAM,YAAY,CAAC,MAAM,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW;IACtD,MAAM,mBAAmB,CAAC,MAAM,WAAW,EAAE,QAAQ,EAAE,EAAE,WAAW;IACpE,MAAM,gBAAgB,CAAC,MAAM,QAAQ,EAAE,QAAQ,EAAE,EAAE,WAAW;IAE9D,0BAA0B;IAC1B,MAAM,mBAAmB,oBAAoB;IAC7C,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI,UAAU,QAAQ,CAAC,YAAY,iBAAiB,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;YACxG,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,eAAe;QAC9E,SAAS;IACX;IAEA,kDAAkD;IAClD,IAAI,MAAM,OAAO,EAAE;QACjB,SAAS;IACX;IAEA,OAAO,KAAK,GAAG,CAAC,IAAI;AACtB;AAEA,SAAS,oBAAoB,YAAoB;IAC/C,MAAM,aAAuC;QAC3C,iCAAiC;YAAC;YAAW;YAAW;YAAW;YAAW;YAAc;YAAkB;YAAW;SAAO;QAChI,cAAc;YAAC;YAAQ;YAAY;YAAW;YAAU;YAAQ;YAAc;SAAc;QAC5F,WAAW;YAAC;YAAW;YAAU;YAAY;YAAO;YAAW;YAAa;SAAS;QACrF,cAAc;YAAC;YAAQ;YAAY;YAAe;YAAM;YAAW;YAAc;SAAU;QAC3F,UAAU;YAAC;YAAU;YAAa;YAAY;YAAY;YAAO;SAAU;QAC3E,UAAU;YAAC;YAAU;YAAY;YAAa;YAAY;YAAS;SAAY;IACjF;IAEA,OAAO,UAAU,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;QAAc;KAAe;AAC7F;AAEA,SAAS,mBAAmB,WAAgB,EAAE,YAAoB;IAChE,MAAM,cAAc,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,IAAI;IACpD,MAAM,YAAY,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI;IAC7C,MAAM,cAAc,YAAY,OAAO,CAAC,EAAE,CAAC,WAAW;IAEtD,OAAO;QACL;QACA;QACA;QACA,UAAU,YAAY,IAAI,CAAC,QAAQ;QACnC,YAAY,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,UAAU;QAClD,UAAU,YAAY,IAAI;QAC1B,uBAAuB,oCAAoC,WAAW,aAAa;QACnF,iBAAiB,8BAA8B,WAAW,aAAa;IACzE;AACF;AAEA,SAAS,oBAAoB,YAAiB,EAAE,YAAoB;IAClE,MAAM,iBAAiB,aAAa,IAAI,CAAC,MAAM,CAAC,CAAC,GAAQ,QAAkB,QAAQ,MAAM,GAAG,KAAK,CAAC,GAAG;IAErG,OAAO,eAAe,GAAG,CAAC,CAAC,WAAkB,CAAC;YAC5C,MAAM,IAAI,KAAK,SAAS,EAAE,GAAG,MAAM,kBAAkB;YACrD,aAAa,KAAK,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI;YAC1C,WAAW,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI;YACnC,sBAAsB,8BAA8B,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;QACpG,CAAC;AACH;AAEA,SAAS,oCAAoC,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACvG,MAAM,gBAA0B,EAAE;IAElC,kCAAkC;IAClC,IAAI,cAAc,IAAI;QACpB,cAAc,IAAI,CAAC,6BAA6B,gCAAgC;IAClF,OAAO,IAAI,cAAc,IAAI;QAC3B,cAAc,IAAI,CAAC,8BAA8B,4BAA4B;IAC/E;IAEA,gCAAgC;IAChC,OAAQ,UAAU,WAAW;QAC3B,KAAK;YACH,cAAc,IAAI,CAAC,+BAA+B,gCAAgC;YAClF;QACF,KAAK;QACL,KAAK;YACH,cAAc,IAAI,CAAC,iCAAiC,8BAA8B;YAClF;QACF,KAAK;YACH,cAAc,IAAI,CAAC,kCAAkC;YACrD;IACJ;IAEA,0CAA0C;IAC1C,MAAM,wBAAwB,gCAAgC,cAAc,WAAW;IACvF,cAAc,IAAI,IAAI;IAEtB,OAAO;AACT;AAEA,SAAS,8BAA8B,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACjG,MAAM,kBAA0D;QAC9D,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,UAAU;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,UAAU,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,SAAS;IAEzE,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI;IAC/C,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;IAEhD,OAAO,OAAO,CAAC,UAAU,WAAW,GAAG,IAAI,OAAO,CAAC,QAAQ,IAAI;AACjE;AAEA,SAAS,gCAAgC,YAAoB,EAAE,SAAiB,EAAE,WAAmB;IACnG,kDAAkD;IAClD,MAAM,gBAA0B,EAAE;IAElC,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,eAAe;QACrD,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,4BAA4B;QAC1E,IAAI,cAAc,QAAQ,cAAc,IAAI,CAAC,0BAA0B;IACzE;IAEA,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,YAAY;QAClD,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,8BAA8B;QAC5E,IAAI,cAAc,IAAI,cAAc,IAAI,CAAC,wBAAwB;IACnE;IAEA,OAAO;AACT;AAEA,qBAAqB;AACrB,SAAS,kBAAkB,QAAgB,EAAE,YAAoB;IAC/D,OAAO;QACL;YACE,MAAM,GAAG,aAAa,qBAAqB,EAAE,UAAU;YACvD,aAAa,CAAC,iCAAiC,EAAE,aAAa,cAAc,CAAC;YAC7E,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,GAAG,WAAW;YAC3D,OAAO,GAAG,SAAS,gBAAgB,CAAC;YACpC,UAAU;YACV,SAAS;YACT,iBAAiB;QACnB;KACD;AACH;AAEA,SAAS,mBAAmB,QAAgB,EAAE,YAAoB;IAChE,OAAO;QACL,aAAa;QACb,WAAW;QACX,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,uBAAuB;YAAC;YAAwC;SAAmC;QACnG,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 1538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/advanced-ai-prompt.ts"], "sourcesContent": ["/**\r\n * Advanced AI Content Generation Prompt\r\n * \r\n * This prompt integrates trending topics, competitor analysis, cultural optimization,\r\n * human-like content generation, and traffic-driving strategies.\r\n */\r\n\r\nexport const ADVANCED_AI_PROMPT = `You are an elite social media strategist, cultural anthropologist, and viral content creator with deep expertise in the {{{businessType}}} industry.\r\n\r\nYour mission is to create content that:\r\n🎯 Captures trending conversations and cultural moments\r\n🚀 Drives maximum traffic and business results\r\n🤝 Feels authentically human and culturally sensitive\r\n💡 Differentiates from competitors strategically\r\n📈 Optimizes for platform-specific viral potential\r\n🌤️ Integrates current weather and local events naturally\r\n🎪 Leverages local happenings for timely relevance\r\n🌍 Uses ENGLISH ONLY for all content generation\r\n\r\nBUSINESS INTELLIGENCE:\r\n- Industry: {{{businessType}}}\r\n- Location: {{{location}}}\r\n- Brand Voice: {{{writingTone}}}\r\n- Content Themes: {{{contentThemes}}}\r\n- Day: {{{dayOfWeek}}}\r\n- Date: {{{currentDate}}}\r\n{{#if platform}}- Primary Platform: {{{platform}}}{{/if}}\r\n{{#if services}}- Services/Products: {{{services}}}{{/if}}\r\n{{#if targetAudience}}- Target Audience: {{{targetAudience}}}{{/if}}\r\n{{#if keyFeatures}}- Key Features: {{{keyFeatures}}}{{/if}}\r\n{{#if competitiveAdvantages}}- Competitive Edge: {{{competitiveAdvantages}}}{{/if}}\r\n{{#if contentVariation}}- Content Approach: {{{contentVariation}}} (MANDATORY: Use this specific approach for content generation){{/if}}\r\n\r\nTRENDING TOPICS INTEGRATION:\r\nResearch and incorporate current trending topics relevant to:\r\n- {{{businessType}}} industry developments\r\n- {{{location}}} local events and cultural moments\r\n- Platform-specific trending hashtags and conversations\r\n- Seasonal relevance and timely opportunities\r\n- News events that connect to your business value\r\n\r\nCOMPETITOR DIFFERENTIATION STRATEGY:\r\nAnalyze and differentiate from typical competitor content by:\r\n- Avoiding generic industry messaging\r\n- Finding unique angles on common topics\r\n- Highlighting authentic personal/business stories\r\n- Focusing on underserved audience needs\r\n- Creating content gaps competitors miss\r\n- Using authentic local cultural connections\r\n\r\nCONTENT DIVERSITY ENFORCEMENT:\r\nCRITICAL: Each post must be completely different from previous generations:\r\n- Use different opening hooks (question, statement, story, statistic, quote)\r\n- Vary content structure (problem-solution, story-lesson, tip-benefit, behind-scenes)\r\n- Alternate between different emotional tones (inspiring, educational, entertaining, personal)\r\n- Change content length and paragraph structure significantly\r\n- Use different call-to-action styles (direct, subtle, question-based, action-oriented)\r\n- Vary hashtag themes and combinations\r\n- Never repeat the same content pattern or messaging approach\r\n\r\n{{#if contentVariation}}\r\nMANDATORY CONTENT VARIATION APPROACH - {{{contentVariation}}}:\r\n\r\nUse the following approach based on the content variation specified:\r\n- For \"trending_hook\": Start with a trending topic or viral conversation, connect the trend to your business naturally, use current social media language and references, include trending hashtags and phrases\r\n- For \"story_driven\": Begin with a compelling personal or customer story, use narrative structure with beginning, middle, end, include emotional elements and relatable characters, end with a meaningful lesson or takeaway\r\n- For \"educational_tip\": Lead with valuable, actionable advice, use numbered lists or step-by-step format, position your business as the expert solution, include \"did you know\" or \"pro tip\" elements\r\n- For \"behind_scenes\": Show the human side of your business, include process, preparation, or team moments, use authentic, unpolished language, create connection through transparency\r\n- For \"question_engagement\": Start with a thought-provoking question, encourage audience participation and responses, use polls, \"this or that,\" or opinion requests, build community through conversation\r\n- For \"statistic_driven\": Lead with surprising or compelling statistics, use data to support your business value, include industry insights and research, position your business as data-informed\r\n- For \"personal_insight\": Share personal experiences or observations, use first-person perspective and authentic voice, include lessons learned or mistakes made, connect personal growth to business value\r\n- For \"industry_contrarian\": Challenge common industry assumptions, present alternative viewpoints respectfully, use \"unpopular opinion\" or \"hot take\" framing, support contrarian views with evidence\r\n- For \"local_cultural\": Reference local events, landmarks, or culture, use location-specific language and references, connect to community values and traditions, show deep local understanding\r\n- For \"seasonal_relevance\": Connect to current season, weather, or holidays, use timely references and seasonal language, align business offerings with seasonal needs\r\n- For \"problem_solution\": Identify a specific customer pain point, agitate the problem to create urgency, present your business as the clear solution, use before/after or transformation language\r\n- For \"inspiration_motivation\": Use uplifting, motivational language, include inspirational quotes or mantras, focus on transformation and possibility, connect inspiration to business outcomes\r\n\r\nApply the specific approach for the {{{contentVariation}}} variation throughout your content generation.\r\n{{/if}}\r\n\r\nCULTURAL & LOCATION OPTIMIZATION:\r\nFor {{{location}}}, incorporate:\r\n- Local cultural nuances and values\r\n- Regional language preferences and expressions\r\n- Community customs and social norms\r\n- Seasonal and cultural calendar awareness\r\n- Local landmarks, events, and references\r\n- Respectful acknowledgment of cultural diversity\r\n\r\nINTELLIGENT CONTEXT USAGE:\r\n{{#if contextInstructions}}\r\nCONTEXT INSTRUCTIONS FOR THIS SPECIFIC POST:\r\n{{{contextInstructions}}}\r\n\r\nFollow these instructions precisely - they are based on expert analysis of what information is relevant for this specific business type and location.\r\n{{/if}}\r\n\r\nWEATHER & EVENTS INTEGRATION:\r\n{{#if selectedWeather}}\r\n- Current weather: {{{selectedWeather.temperature}}}°C, {{{selectedWeather.condition}}}\r\n- Business impact: {{{selectedWeather.business_impact}}}\r\n- Content opportunities: {{{selectedWeather.content_opportunities}}}\r\n{{/if}}\r\n\r\n{{#if selectedEvents}}\r\n- Relevant local events:\r\n{{#each selectedEvents}}\r\n  * {{{this.name}}} ({{{this.category}}}) - {{{this.start_date}}}\r\n{{/each}}\r\n{{/if}}\r\n\r\nUse this information ONLY if the context instructions indicate it's relevant for this business type.\r\n\r\nHUMAN-LIKE AUTHENTICITY MARKERS:\r\nMake content feel genuinely human by:\r\n- Using conversational, imperfect language\r\n- Including personal experiences and observations\r\n- Showing vulnerability and learning moments\r\n- Using specific details over generic statements\r\n- Adding natural speech patterns and contractions\r\n- Including time-specific references (today, this morning)\r\n- Expressing genuine emotions and reactions\r\n\r\nTRAFFIC-DRIVING OPTIMIZATION:\r\nMaximize engagement and traffic through:\r\n- Curiosity gaps that demand attention\r\n- Shareability factors that encourage spreading\r\n- Conversion triggers that drive action\r\n- Social proof elements that build trust\r\n- Interactive elements that boost engagement\r\n- Viral hooks that capture trending conversations\r\n\r\nADVANCED COPYWRITING FRAMEWORKS:\r\n1. **AIDA Framework**: Attention → Interest → Desire → Action\r\n2. **PAS Framework**: Problem → Agitation → Solution  \r\n3. **Storytelling Arc**: Setup → Conflict → Resolution → Lesson\r\n4. **Social Proof Stack**: Testimonial → Statistics → Authority → Community\r\n5. **Curiosity Loop**: Hook → Tension → Payoff → Next Hook\r\n\r\nPSYCHOLOGICAL TRIGGERS FOR MAXIMUM ENGAGEMENT:\r\n✅ **Urgency & Scarcity**: Time-sensitive opportunities\r\n✅ **Social Proof**: Community validation and testimonials\r\n✅ **FOMO**: Exclusive access and insider information\r\n✅ **Curiosity Gaps**: Intriguing questions and reveals\r\n✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy\r\n✅ **Authority**: Expert insights and industry knowledge\r\n✅ **Reciprocity**: Valuable tips and free insights\r\n✅ **Tribal Identity**: Community belonging and shared values\r\n\r\nCONTENT GENERATION REQUIREMENTS:\r\n\r\nGenerate a comprehensive social media post with these components:\r\n\r\n1. **CAPTION (content)**:\r\n   - Start with a trending topic hook or cultural moment\r\n   - Use authentic, conversational human language\r\n   - Include competitor differentiation naturally\r\n   - Apply psychological triggers strategically\r\n   - Incorporate local cultural references appropriately\r\n   - End with traffic-driving call-to-action\r\n   - Length optimized for platform and engagement\r\n   - Feel like it was written by a real person, not AI\r\n\r\n2. **CATCHY WORDS (catchyWords)**:\r\n   - Create relevant, business-focused catchy words (max 5 words)\r\n   - MUST be directly related to the specific business services/products\r\n   - Use clear, professional language that matches the business type\r\n   - Focus on the business value proposition or key service\r\n   - Avoid generic phrases like \"Banking Made Easy\" or random financial terms\r\n   - Examples: For a restaurant: \"Fresh Daily Specials\", For a gym: \"Transform Your Body\", For a salon: \"Expert Hair Care\"\r\n   - Required for ALL posts - this is the main visual text\r\n   - Optimize for visual impact and business relevance\r\n\r\n3. **SUBHEADLINE (subheadline)** - OPTIONAL:\r\n   - Add only when it would make the post more effective\r\n   - Maximum 14 words\r\n   - Use your marketing expertise to decide when needed\r\n   - Should complement the catchy words and enhance the message\r\n   - Examples: When explaining a complex service, highlighting a special offer, or providing context\r\n   - Skip if the catchy words and caption are sufficient\r\n\r\n4. **CALL TO ACTION (callToAction)** - OPTIONAL:\r\n   - Add only when it would drive better engagement or conversions\r\n   - Use your marketing expertise to decide when needed\r\n   - Should be specific and actionable\r\n   - Examples: \"Book Now\", \"Call Today\", \"Visit Us\", \"Learn More\", \"Get Started\"\r\n   - Skip if the post is more about awareness or engagement rather than direct action\r\n\r\n5. **HASHTAGS**:\r\n   - Mix trending hashtags with niche industry tags\r\n   - Include location-specific and cultural hashtags\r\n   - Balance high-competition and low-competition tags\r\n   - Ensure cultural sensitivity and appropriateness\r\n   - Optimize quantity for platform (Instagram: 20-30, LinkedIn: 3-5, etc.)\r\n\r\n6. **CONTENT VARIANTS (contentVariants)**:\r\n   Generate 2-3 alternative approaches:\r\n\r\n   **Variant 1 - Trending Topic Angle**:\r\n   - Hook into current trending conversation\r\n   - Connect trend to business value naturally\r\n   - Use viral content patterns\r\n   - Include shareability factors\r\n\r\n   **Variant 2 - Cultural Connection Angle**:\r\n   - Start with local cultural reference\r\n   - Show deep community understanding\r\n   - Use location-specific language naturally\r\n   - Build authentic local connections\r\n\r\n   **Variant 3 - Competitor Differentiation Angle**:\r\n   - Address common industry pain points differently\r\n   - Highlight unique business approach\r\n   - Use contrarian but respectful positioning\r\n   - Show authentic expertise and experience\r\n\r\n   For each variant, provide:\r\n   - The alternative caption content\r\n   - The strategic approach used\r\n   - Why this variant will drive traffic and engagement\r\n   - Cultural sensitivity considerations\r\n\r\nQUALITY STANDARDS:\r\n- Every word serves engagement or conversion purpose\r\n- Content feels authentically human, never robotic\r\n- Cultural references are respectful and accurate\r\n- Trending topics are naturally integrated, not forced\r\n- Competitor differentiation is subtle but clear\r\n- Traffic-driving elements are seamlessly woven in\r\n- Platform optimization is invisible but effective\r\n- Local cultural nuances are appropriately honored\r\n\r\nTRAFFIC & CONVERSION OPTIMIZATION:\r\n- Include clear value proposition for audience\r\n- Create multiple engagement touchpoints\r\n- Use psychological triggers ethically\r\n- Provide shareable insights or entertainment\r\n- Include conversion pathway (comment, DM, visit, etc.)\r\n- Optimize for algorithm preferences\r\n- Encourage community building and return visits\r\n\r\nWEBSITE REFERENCE GUIDELINES:\r\n{{#if websiteUrl}}\r\n- Website available for CTAs: {{{websiteUrl}}} (use clean format without https:// or www.)\r\n- Only include website when CTA specifically calls for it (e.g., \"check us out online\", \"visit our site\")\r\n- Don't force website into every post - use contextually when it makes sense\r\n- Examples: \"Visit us online\", \"Check our website\", \"Learn more at [clean-url]\"\r\n{{else}}\r\n- No website URL provided - focus on other CTAs (DM, call, visit location)\r\n{{/if}}\r\n\r\nLANGUAGE REQUIREMENTS:\r\n🌍 TEXT CLARITY: Generate clear, readable text\r\n{{#if useLocalLanguage}}\r\n- You may use local language text when 100% certain of spelling, meaning, and cultural appropriateness\r\n- Mix local language with English naturally (1-2 local words maximum per text element)\r\n- Only use commonly known local words that add cultural connection to {{{location}}}\r\n- When uncertain about local language accuracy, use English instead\r\n- Better to use clear English than incorrect or garbled local language\r\n{{else}}\r\n- USE ONLY ENGLISH for all text content (captions, hashtags, call-to-actions)\r\n- Do not use any local language words or phrases\r\n- Keep all text elements in clear, professional English\r\n- Focus on universal messaging that works across all markets\r\n{{/if}}\r\n- Do NOT use corrupted, gibberish, or unreadable character sequences\r\n- Do NOT use random symbols or malformed text\r\n- Ensure all text is properly formatted and legible\r\n- Avoid character encoding issues or text corruption\r\n- All text must be clear and professional\r\n- Prevent any garbled or nonsensical character combinations\r\n\r\nYour response MUST be a valid JSON object that conforms to the output schema.\r\nFocus on creating content that real humans will love, share, and act upon.`;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAEM,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EA0QuC,CAAC", "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/real-time-trends-integration.ts"], "sourcesContent": ["/**\r\n * Real-Time Trends Integration System\r\n * \r\n * This module integrates multiple real-time trending topic sources\r\n * and provides a unified interface for getting current trends.\r\n */\r\n\r\nexport interface TrendingTopicSource {\r\n  name: string;\r\n  enabled: boolean;\r\n  apiKey?: string;\r\n  baseUrl?: string;\r\n  rateLimitPerHour: number;\r\n}\r\n\r\nexport interface LocalContext {\r\n  weather?: {\r\n    temperature: number;\r\n    condition: string;\r\n    business_impact: string;\r\n    content_opportunities: string[];\r\n  };\r\n  events?: Array<{\r\n    name: string;\r\n    category: string;\r\n    relevance_score: number;\r\n    start_date: string;\r\n  }>;\r\n}\r\n\r\nexport interface RealTimeTrendingConfig {\r\n  sources: {\r\n    googleTrends: TrendingTopicSource;\r\n    twitterApi: TrendingTopicSource;\r\n    newsApi: TrendingTopicSource;\r\n    redditApi: TrendingTopicSource;\r\n    youtubeApi: TrendingTopicSource;\r\n    eventbriteApi: TrendingTopicSource;\r\n    openWeatherApi: TrendingTopicSource;\r\n  };\r\n  fallbackToStatic: boolean;\r\n  cacheTimeMinutes: number;\r\n}\r\n\r\n/**\r\n * Configuration for real-time trending topics\r\n * Add your API keys to environment variables\r\n */\r\nexport const TRENDING_CONFIG: RealTimeTrendingConfig = {\r\n  sources: {\r\n    googleTrends: {\r\n      name: 'Google Trends RSS',\r\n      enabled: process.env.GOOGLE_TRENDS_RSS_ENABLED === 'true',\r\n      apiKey: undefined, // RSS doesn't need API key\r\n      baseUrl: 'https://trends.google.com/trends/trendingsearches/daily/rss',\r\n      rateLimitPerHour: 1000 // RSS has higher limits\r\n    },\r\n    twitterApi: {\r\n      name: 'Twitter API v1.1',\r\n      enabled: false, // Temporarily disabled due to endpoint issues\r\n      apiKey: process.env.TWITTER_BEARER_TOKEN,\r\n      baseUrl: 'https://api.twitter.com/1.1',\r\n      rateLimitPerHour: 300\r\n    },\r\n    newsApi: {\r\n      name: 'News API',\r\n      enabled: false, // Temporarily disabled due to API key issues\r\n      apiKey: process.env.NEWS_API_KEY,\r\n      baseUrl: 'https://newsapi.org/v2',\r\n      rateLimitPerHour: 1000\r\n    },\r\n    redditApi: {\r\n      name: 'Reddit RSS',\r\n      enabled: process.env.REDDIT_RSS_ENABLED === 'true',\r\n      apiKey: undefined, // RSS doesn't need API key\r\n      baseUrl: 'https://www.reddit.com',\r\n      rateLimitPerHour: 1000 // RSS has higher limits\r\n    },\r\n    youtubeApi: {\r\n      name: 'YouTube Data API',\r\n      enabled: !!process.env.YOUTUBE_API_KEY,\r\n      apiKey: process.env.YOUTUBE_API_KEY,\r\n      baseUrl: 'https://www.googleapis.com/youtube/v3',\r\n      rateLimitPerHour: 10000\r\n    },\r\n    eventbriteApi: {\r\n      name: 'Eventbrite API',\r\n      enabled: !!process.env.EVENTBRITE_API_KEY,\r\n      apiKey: process.env.EVENTBRITE_API_KEY,\r\n      baseUrl: 'https://www.eventbriteapi.com/v3',\r\n      rateLimitPerHour: 1000\r\n    },\r\n    openWeatherApi: {\r\n      name: 'OpenWeather API',\r\n      enabled: !!process.env.OPENWEATHER_API_KEY,\r\n      apiKey: process.env.OPENWEATHER_API_KEY,\r\n      baseUrl: 'https://api.openweathermap.org/data/2.5',\r\n      rateLimitPerHour: 1000\r\n    }\r\n  },\r\n  fallbackToStatic: true,\r\n  cacheTimeMinutes: 30\r\n};\r\n\r\n/**\r\n * Google Trends Integration via RSS\r\n */\r\nexport async function fetchGoogleTrends(\r\n  location: string,\r\n  category?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.googleTrends.enabled) {\r\n    return getGoogleTrendsFallback(location, category);\r\n  }\r\n\r\n  try {\r\n    // Import RSS integration\r\n    const { fetchGoogleTrendsRSS } = await import('./rss-feeds-integration');\r\n\r\n    // Use RSS feeds for Google Trends\r\n    const geoCode = getGoogleTrendsGeoCode(location);\r\n    const trends = await fetchGoogleTrendsRSS(geoCode, category);\r\n\r\n    // Convert to expected format\r\n    return trends.map(trend => ({\r\n      topic: trend.topic,\r\n      relevanceScore: trend.relevanceScore,\r\n      category: trend.category,\r\n      timeframe: trend.timeframe,\r\n      engagement_potential: trend.engagement_potential,\r\n      source: 'google_trends_rss'\r\n    }));\r\n\r\n  } catch (error) {\r\n    return getGoogleTrendsFallback(location, category);\r\n  }\r\n}\r\n\r\n/**\r\n * Twitter/X Trends Integration\r\n */\r\nexport async function fetchTwitterTrends(\r\n  location: string,\r\n  businessType?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.twitterApi.enabled) {\r\n    return getTwitterTrendsFallback(location, businessType);\r\n  }\r\n\r\n  try {\r\n    const woeid = getTwitterWOEID(location);\r\n\r\n    // Use Twitter API v2 trending topics endpoint\r\n    const response = await fetch(\r\n      `${TRENDING_CONFIG.sources.twitterApi.baseUrl}/trends/place.json?id=${woeid}`,\r\n      {\r\n        headers: {\r\n          'Authorization': `Bearer ${TRENDING_CONFIG.sources.twitterApi.apiKey}`,\r\n          'Content-Type': 'application/json',\r\n          'User-Agent': 'TrendingTopicsBot/2.0'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Twitter API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Process Twitter trends data\r\n    return processTwitterTrendsData(data, businessType);\r\n\r\n  } catch (error) {\r\n    return getTwitterTrendsFallback(location, businessType);\r\n  }\r\n}\r\n\r\n/**\r\n * News API Integration\r\n */\r\nexport async function fetchCurrentNews(\r\n  location: string,\r\n  businessType: string,\r\n  category?: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.newsApi.enabled) {\r\n    return getNewsFallback(location, businessType, category);\r\n  }\r\n\r\n  try {\r\n    const params = new URLSearchParams({\r\n      country: getNewsApiCountryCode(location),\r\n      category: category || 'business',\r\n      pageSize: '10',\r\n      apiKey: TRENDING_CONFIG.sources.newsApi.apiKey!\r\n    });\r\n\r\n    const response = await fetch(`${TRENDING_CONFIG.sources.newsApi.baseUrl}/top-headlines?${params}`);\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`News API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Process news data\r\n    return processNewsData(data, businessType);\r\n\r\n  } catch (error) {\r\n    return getNewsFallback(location, businessType, category);\r\n  }\r\n}\r\n\r\n/**\r\n * Reddit Trends Integration via RSS\r\n */\r\nexport async function fetchRedditTrends(\r\n  businessType: string,\r\n  platform: string\r\n): Promise<any[]> {\r\n  if (!TRENDING_CONFIG.sources.redditApi.enabled) {\r\n    return getRedditTrendsFallback(businessType, platform);\r\n  }\r\n\r\n  try {\r\n    // Import RSS integration\r\n    const { fetchRedditRSS } = await import('./rss-feeds-integration');\r\n\r\n    // Use RSS feeds for Reddit trends\r\n    const trends = await fetchRedditRSS(businessType);\r\n\r\n    // Convert to expected format\r\n    return trends.map(trend => ({\r\n      topic: trend.topic,\r\n      relevanceScore: trend.relevanceScore,\r\n      category: trend.category,\r\n      timeframe: trend.timeframe,\r\n      engagement_potential: trend.engagement_potential,\r\n      source: 'reddit_rss'\r\n    }));\r\n\r\n  } catch (error) {\r\n    return getRedditTrendsFallback(businessType, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Helper functions for processing API data\r\n */\r\nfunction processGoogleTrendsData(data: any, location: string, category?: string) {\r\n  // Process Google Trends API response\r\n  return [\r\n    {\r\n      topic: `Trending in ${location}`,\r\n      source: 'google_trends',\r\n      relevanceScore: 9,\r\n      category: category || 'general',\r\n      timeframe: 'now',\r\n      engagement_potential: 'high'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction processTwitterTrendsData(data: any, businessType?: string) {\r\n  // Process Twitter API response\r\n  if (data && data[0] && data[0].trends) {\r\n    return data[0].trends.slice(0, 10).map((trend: any) => ({\r\n      topic: trend.name,\r\n      source: 'twitter',\r\n      relevanceScore: trend.tweet_volume ? Math.min(10, Math.log10(trend.tweet_volume)) : 5,\r\n      category: 'social',\r\n      timeframe: 'now',\r\n      engagement_potential: trend.tweet_volume > 10000 ? 'high' : 'medium'\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\nfunction processNewsData(data: any, businessType: string) {\r\n  // Process News API response\r\n  if (data && data.articles) {\r\n    return data.articles.slice(0, 8).map((article: any) => ({\r\n      topic: article.title,\r\n      source: 'news',\r\n      relevanceScore: 8,\r\n      category: 'news',\r\n      timeframe: 'today',\r\n      engagement_potential: 'high',\r\n      business_angle: `How this relates to ${businessType} industry`\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\nfunction processRedditData(data: any, subreddit: string) {\r\n  // Process Reddit API response\r\n  if (data && data.data && data.data.children) {\r\n    return data.data.children.slice(0, 5).map((post: any) => ({\r\n      topic: post.data.title,\r\n      source: 'reddit',\r\n      relevanceScore: Math.min(10, post.data.score / 100),\r\n      category: 'community',\r\n      timeframe: 'today',\r\n      engagement_potential: post.data.score > 1000 ? 'high' : 'medium',\r\n      subreddit: subreddit\r\n    }));\r\n  }\r\n  return [];\r\n}\r\n\r\n/**\r\n * Helper functions for API parameters\r\n */\r\nfunction getGoogleTrendsGeoCode(location: string): string {\r\n  const geoMap: Record<string, string> = {\r\n    'kenya': 'KE',\r\n    'united states': 'US',\r\n    'nairobi': 'KE',\r\n    'new york': 'US-NY',\r\n    'london': 'GB-ENG'\r\n  };\r\n  return geoMap[location.toLowerCase()] || 'US';\r\n}\r\n\r\nfunction getTwitterWOEID(location: string): string {\r\n  const woeidMap: Record<string, string> = {\r\n    'kenya': '23424863',\r\n    'united states': '23424977',\r\n    'nairobi': '1528488',\r\n    'new york': '2459115',\r\n    'london': '44418'\r\n  };\r\n  return woeidMap[location.toLowerCase()] || '1'; // Worldwide\r\n}\r\n\r\nfunction getNewsApiCountryCode(location: string): string {\r\n  const countryMap: Record<string, string> = {\r\n    'kenya': 'ke',\r\n    'united states': 'us',\r\n    'nairobi': 'ke',\r\n    'new york': 'us',\r\n    'london': 'gb'\r\n  };\r\n  return countryMap[location.toLowerCase()] || 'us';\r\n}\r\n\r\nfunction getRelevantSubreddits(businessType: string): string[] {\r\n  const subredditMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'personalfinance', 'investing', 'entrepreneur'],\r\n    'restaurant': ['food', 'recipes', 'restaurantowners', 'smallbusiness'],\r\n    'fitness': ['fitness', 'bodybuilding', 'nutrition', 'personaltrainer'],\r\n    'technology': ['technology', 'programming', 'startups', 'artificial']\r\n  };\r\n  return subredditMap[businessType.toLowerCase()] || ['business', 'entrepreneur'];\r\n}\r\n\r\n/**\r\n * Fallback functions when APIs are not available\r\n */\r\nfunction getGoogleTrendsFallback(location: string, category?: string) {\r\n  return [\r\n    {\r\n      topic: `Local business trends in ${location}`,\r\n      source: 'fallback',\r\n      relevanceScore: 7,\r\n      category: category || 'business',\r\n      timeframe: 'week',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getTwitterTrendsFallback(location: string, businessType?: string) {\r\n  return [\r\n    {\r\n      topic: '#MondayMotivation',\r\n      source: 'fallback',\r\n      relevanceScore: 6,\r\n      category: 'social',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getNewsFallback(location: string, businessType: string, category?: string) {\r\n  return [\r\n    {\r\n      topic: `${businessType} industry updates`,\r\n      source: 'fallback',\r\n      relevanceScore: 6,\r\n      category: 'news',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\nfunction getRedditTrendsFallback(businessType: string, platform: string) {\r\n  return [\r\n    {\r\n      topic: `${businessType} community discussions`,\r\n      source: 'fallback',\r\n      relevanceScore: 5,\r\n      category: 'community',\r\n      timeframe: 'today',\r\n      engagement_potential: 'medium'\r\n    }\r\n  ];\r\n}\r\n\r\n/**\r\n * Fetch comprehensive local context (weather + events)\r\n */\r\nexport async function fetchLocalContext(\r\n  location: string,\r\n  businessType: string\r\n): Promise<LocalContext> {\r\n  const context: LocalContext = {};\r\n\r\n  try {\r\n    // Fetch weather context\r\n    if (TRENDING_CONFIG.sources.openWeatherApi.enabled) {\r\n\r\n      const params = new URLSearchParams({\r\n        q: location,\r\n        appid: TRENDING_CONFIG.sources.openWeatherApi.apiKey!,\r\n        units: 'metric'\r\n      });\r\n\r\n      const response = await fetch(\r\n        `${TRENDING_CONFIG.sources.openWeatherApi.baseUrl}/weather?${params}`\r\n      );\r\n\r\n      if (response.ok) {\r\n        const weatherData = await response.json();\r\n        context.weather = {\r\n          temperature: Math.round(weatherData.main.temp),\r\n          condition: weatherData.weather[0].main,\r\n          business_impact: generateBusinessWeatherImpact(weatherData.weather[0].main, weatherData.main.temp, businessType),\r\n          content_opportunities: generateWeatherContentOpportunities(weatherData.weather[0].main, weatherData.main.temp, businessType)\r\n        };\r\n      }\r\n    }\r\n\r\n    // Fetch events context\r\n    if (TRENDING_CONFIG.sources.eventbriteApi.enabled) {\r\n\r\n      const params = new URLSearchParams({\r\n        'location.address': location,\r\n        'location.within': '25km',\r\n        'start_date.range_start': new Date().toISOString(),\r\n        'start_date.range_end': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        'sort_by': 'relevance',\r\n        'page_size': '10'\r\n      });\r\n\r\n      const response = await fetch(\r\n        `${TRENDING_CONFIG.sources.eventbriteApi.baseUrl}/events/search/?${params}`,\r\n        {\r\n          headers: {\r\n            'Authorization': `Bearer ${TRENDING_CONFIG.sources.eventbriteApi.apiKey}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        const eventsData = await response.json();\r\n        context.events = (eventsData.events || []).slice(0, 5).map((event: any) => ({\r\n          name: event.name?.text || 'Event',\r\n          category: event.category?.name || 'General',\r\n          relevance_score: calculateEventRelevance(event, businessType),\r\n          start_date: event.start?.local || event.start?.utc\r\n        }));\r\n      }\r\n    }\r\n\r\n  } catch (error) {\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n// Helper functions for weather and events\r\nfunction generateBusinessWeatherImpact(condition: string, temperature: number, businessType: string): string {\r\n  const businessImpacts: Record<string, Record<string, string>> = {\r\n    'restaurant': {\r\n      'sunny': 'Perfect weather for outdoor dining and patio service',\r\n      'rain': 'Great opportunity to promote cozy indoor dining experience',\r\n      'hot': 'Ideal time to highlight refreshing drinks and cool dishes',\r\n      'cold': 'Perfect weather for warm comfort food and hot beverages'\r\n    },\r\n    'fitness': {\r\n      'sunny': 'Excellent conditions for outdoor workouts and activities',\r\n      'rain': 'Great time to promote indoor fitness programs',\r\n      'hot': 'Important to emphasize hydration and cooling strategies',\r\n      'cold': 'Perfect for promoting warm-up routines and indoor training'\r\n    },\r\n    'financial technology software': {\r\n      'sunny': 'Great weather for outdoor meetings and client visits',\r\n      'rain': 'Perfect time for indoor productivity and digital solutions',\r\n      'hot': 'Ideal for promoting mobile solutions and remote services',\r\n      'cold': 'Good time for cozy indoor planning and financial reviews'\r\n    }\r\n  };\r\n\r\n  const businessKey = businessType.toLowerCase();\r\n  const impacts = businessImpacts[businessKey] || businessImpacts['restaurant'];\r\n\r\n  if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';\r\n  if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';\r\n\r\n  return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';\r\n}\r\n\r\nfunction generateWeatherContentOpportunities(condition: string, temperature: number, businessType: string): string[] {\r\n  const opportunities: string[] = [];\r\n\r\n  // Temperature-based opportunities\r\n  if (temperature > 25) {\r\n    opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');\r\n  } else if (temperature < 10) {\r\n    opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');\r\n  }\r\n\r\n  // Condition-based opportunities\r\n  switch (condition.toLowerCase()) {\r\n    case 'rain':\r\n      opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');\r\n      break;\r\n    case 'sunny':\r\n    case 'clear':\r\n      opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');\r\n      break;\r\n    case 'clouds':\r\n      opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');\r\n      break;\r\n  }\r\n\r\n  return opportunities;\r\n}\r\n\r\nfunction calculateEventRelevance(event: any, businessType: string): number {\r\n  let score = 5; // Base score\r\n\r\n  const eventName = (event.name?.text || '').toLowerCase();\r\n  const eventCategory = (event.category?.name || '').toLowerCase();\r\n\r\n  // Business type relevance\r\n  const businessKeywords = getBusinessKeywords(businessType);\r\n  for (const keyword of businessKeywords) {\r\n    if (eventName.includes(keyword) || eventCategory.includes(keyword)) {\r\n      score += 2;\r\n    }\r\n  }\r\n\r\n  // Event category bonus\r\n  if (eventCategory.includes('business') || eventCategory.includes('networking')) {\r\n    score += 1;\r\n  }\r\n\r\n  return Math.min(10, score);\r\n}\r\n\r\nfunction getBusinessKeywords(businessType: string): string[] {\r\n  const keywordMap: Record<string, string[]> = {\r\n    'financial technology software': ['fintech', 'finance', 'banking', 'payment', 'blockchain', 'startup', 'tech'],\r\n    'restaurant': ['food', 'culinary', 'cooking', 'dining', 'chef', 'restaurant'],\r\n    'fitness': ['fitness', 'health', 'wellness', 'gym', 'workout', 'nutrition'],\r\n    'technology': ['tech', 'software', 'programming', 'ai', 'digital', 'innovation']\r\n  };\r\n\r\n  return keywordMap[businessType.toLowerCase()] || ['business', 'networking', 'professional'];\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;AA2CM,MAAM,kBAA0C;IACrD,SAAS;QACP,cAAc;YACZ,MAAM;YACN,SAAS,QAAQ,GAAG,CAAC,yBAAyB,KAAK;YACnD,QAAQ;YACR,SAAS;YACT,kBAAkB,KAAK,wBAAwB;QACjD;QACA,YAAY;YACV,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,oBAAoB;YACxC,SAAS;YACT,kBAAkB;QACpB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,YAAY;YAChC,SAAS;YACT,kBAAkB;QACpB;QACA,WAAW;YACT,MAAM;YACN,SAAS,QAAQ,GAAG,CAAC,kBAAkB,KAAK;YAC5C,QAAQ;YACR,SAAS;YACT,kBAAkB,KAAK,wBAAwB;QACjD;QACA,YAAY;YACV,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,eAAe;YACtC,QAAQ,QAAQ,GAAG,CAAC,eAAe;YACnC,SAAS;YACT,kBAAkB;QACpB;QACA,eAAe;YACb,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,kBAAkB;YACzC,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACtC,SAAS;YACT,kBAAkB;QACpB;QACA,gBAAgB;YACd,MAAM;YACN,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;YAC1C,QAAQ,QAAQ,GAAG,CAAC,mBAAmB;YACvC,SAAS;YACT,kBAAkB;QACpB;IACF;IACA,kBAAkB;IAClB,kBAAkB;AACpB;AAKO,eAAe,kBACpB,QAAgB,EAChB,QAAiB;IAEjB,IAAI,CAAC,gBAAgB,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE;QACjD,OAAO,wBAAwB,UAAU;IAC3C;IAEA,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,oBAAoB,EAAE,GAAG;QAEjC,kCAAkC;QAClC,MAAM,UAAU,uBAAuB;QACvC,MAAM,SAAS,MAAM,qBAAqB,SAAS;QAEnD,6BAA6B;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC1B,OAAO,MAAM,KAAK;gBAClB,gBAAgB,MAAM,cAAc;gBACpC,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,sBAAsB,MAAM,oBAAoB;gBAChD,QAAQ;YACV,CAAC;IAEH,EAAE,OAAO,OAAO;QACd,OAAO,wBAAwB,UAAU;IAC3C;AACF;AAKO,eAAe,mBACpB,QAAgB,EAChB,YAAqB;IAErB,IAAI,CAAC,gBAAgB,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE;QAC/C,OAAO,yBAAyB,UAAU;IAC5C;IAEA,IAAI;QACF,MAAM,QAAQ,gBAAgB;QAE9B,8CAA8C;QAC9C,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,OAAO,EAC7E;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtE,gBAAgB;gBAChB,cAAc;YAChB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,MAAM,EAAE;QACzD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,8BAA8B;QAC9B,OAAO,yBAAyB,MAAM;IAExC,EAAE,OAAO,OAAO;QACd,OAAO,yBAAyB,UAAU;IAC5C;AACF;AAKO,eAAe,iBACpB,QAAgB,EAChB,YAAoB,EACpB,QAAiB;IAEjB,IAAI,CAAC,gBAAgB,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;QAC5C,OAAO,gBAAgB,UAAU,cAAc;IACjD;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC,SAAS,sBAAsB;YAC/B,UAAU,YAAY;YACtB,UAAU;YACV,QAAQ,gBAAgB,OAAO,CAAC,OAAO,CAAC,MAAM;QAChD;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,gBAAgB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,QAAQ;QAEjG,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,SAAS,MAAM,EAAE;QACtD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,oBAAoB;QACpB,OAAO,gBAAgB,MAAM;IAE/B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,UAAU,cAAc;IACjD;AACF;AAKO,eAAe,kBACpB,YAAoB,EACpB,QAAgB;IAEhB,IAAI,CAAC,gBAAgB,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE;QAC9C,OAAO,wBAAwB,cAAc;IAC/C;IAEA,IAAI;QACF,yBAAyB;QACzB,MAAM,EAAE,cAAc,EAAE,GAAG;QAE3B,kCAAkC;QAClC,MAAM,SAAS,MAAM,eAAe;QAEpC,6BAA6B;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC1B,OAAO,MAAM,KAAK;gBAClB,gBAAgB,MAAM,cAAc;gBACpC,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,sBAAsB,MAAM,oBAAoB;gBAChD,QAAQ;YACV,CAAC;IAEH,EAAE,OAAO,OAAO;QACd,OAAO,wBAAwB,cAAc;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,wBAAwB,IAAS,EAAE,QAAgB,EAAE,QAAiB;IAC7E,qCAAqC;IACrC,OAAO;QACL;YACE,OAAO,CAAC,YAAY,EAAE,UAAU;YAChC,QAAQ;YACR,gBAAgB;YAChB,UAAU,YAAY;YACtB,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,yBAAyB,IAAS,EAAE,YAAqB;IAChE,+BAA+B;IAC/B,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACrC,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,QAAe,CAAC;gBACtD,OAAO,MAAM,IAAI;gBACjB,QAAQ;gBACR,gBAAgB,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,YAAY,KAAK;gBACpF,UAAU;gBACV,WAAW;gBACX,sBAAsB,MAAM,YAAY,GAAG,QAAQ,SAAS;YAC9D,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA,SAAS,gBAAgB,IAAS,EAAE,YAAoB;IACtD,4BAA4B;IAC5B,IAAI,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAiB,CAAC;gBACtD,OAAO,QAAQ,KAAK;gBACpB,QAAQ;gBACR,gBAAgB;gBAChB,UAAU;gBACV,WAAW;gBACX,sBAAsB;gBACtB,gBAAgB,CAAC,oBAAoB,EAAE,aAAa,SAAS,CAAC;YAChE,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA,SAAS,kBAAkB,IAAS,EAAE,SAAiB;IACrD,8BAA8B;IAC9B,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE;QAC3C,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAc,CAAC;gBACxD,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,QAAQ;gBACR,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG;gBAC/C,UAAU;gBACV,WAAW;gBACX,sBAAsB,KAAK,IAAI,CAAC,KAAK,GAAG,OAAO,SAAS;gBACxD,WAAW;YACb,CAAC;IACH;IACA,OAAO,EAAE;AACX;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,MAAM,SAAiC;QACrC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,SAAS,WAAW,GAAG,IAAI;AAC3C;AAEA,SAAS,gBAAgB,QAAgB;IACvC,MAAM,WAAmC;QACvC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,QAAQ,CAAC,SAAS,WAAW,GAAG,IAAI,KAAK,YAAY;AAC9D;AAEA,SAAS,sBAAsB,QAAgB;IAC7C,MAAM,aAAqC;QACzC,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,UAAU,CAAC,SAAS,WAAW,GAAG,IAAI;AAC/C;AAEA,SAAS,sBAAsB,YAAoB;IACjD,MAAM,eAAyC;QAC7C,iCAAiC;YAAC;YAAW;YAAmB;YAAa;SAAe;QAC5F,cAAc;YAAC;YAAQ;YAAW;YAAoB;SAAgB;QACtE,WAAW;YAAC;YAAW;YAAgB;YAAa;SAAkB;QACtE,cAAc;YAAC;YAAc;YAAe;YAAY;SAAa;IACvE;IACA,OAAO,YAAY,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;KAAe;AACjF;AAEA;;CAEC,GACD,SAAS,wBAAwB,QAAgB,EAAE,QAAiB;IAClE,OAAO;QACL;YACE,OAAO,CAAC,yBAAyB,EAAE,UAAU;YAC7C,QAAQ;YACR,gBAAgB;YAChB,UAAU,YAAY;YACtB,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,yBAAyB,QAAgB,EAAE,YAAqB;IACvE,OAAO;QACL;YACE,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,gBAAgB,QAAgB,EAAE,YAAoB,EAAE,QAAiB;IAChF,OAAO;QACL;YACE,OAAO,GAAG,aAAa,iBAAiB,CAAC;YACzC,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAEA,SAAS,wBAAwB,YAAoB,EAAE,QAAgB;IACrE,OAAO;QACL;YACE,OAAO,GAAG,aAAa,sBAAsB,CAAC;YAC9C,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,sBAAsB;QACxB;KACD;AACH;AAKO,eAAe,kBACpB,QAAgB,EAChB,YAAoB;IAEpB,MAAM,UAAwB,CAAC;IAE/B,IAAI;QACF,wBAAwB;QACxB,IAAI,gBAAgB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE;YAElD,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG;gBACH,OAAO,gBAAgB,OAAO,CAAC,cAAc,CAAC,MAAM;gBACpD,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ;YAGvE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBACvC,QAAQ,OAAO,GAAG;oBAChB,aAAa,KAAK,KAAK,CAAC,YAAY,IAAI,CAAC,IAAI;oBAC7C,WAAW,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI;oBACtC,iBAAiB,8BAA8B,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,EAAE;oBACnG,uBAAuB,oCAAoC,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,EAAE;gBACjH;YACF;QACF;QAEA,uBAAuB;QACvB,IAAI,gBAAgB,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE;YAEjD,MAAM,SAAS,IAAI,gBAAgB;gBACjC,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B,IAAI,OAAO,WAAW;gBAChD,wBAAwB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBAClF,WAAW;gBACX,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,MACrB,GAAG,gBAAgB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAC3E;gBACE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE;oBACzE,gBAAgB;gBAClB;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,aAAa,MAAM,SAAS,IAAI;gBACtC,QAAQ,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAe,CAAC;wBAC1E,MAAM,MAAM,IAAI,EAAE,QAAQ;wBAC1B,UAAU,MAAM,QAAQ,EAAE,QAAQ;wBAClC,iBAAiB,wBAAwB,OAAO;wBAChD,YAAY,MAAM,KAAK,EAAE,SAAS,MAAM,KAAK,EAAE;oBACjD,CAAC;YACH;QACF;IAEF,EAAE,OAAO,OAAO,CAChB;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1C,SAAS,8BAA8B,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACjG,MAAM,kBAA0D;QAC9D,cAAc;YACZ,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,iCAAiC;YAC/B,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,UAAU,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,aAAa;IAE7E,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI;IAC/C,IAAI,cAAc,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;IAEhD,OAAO,OAAO,CAAC,UAAU,WAAW,GAAG,IAAI,OAAO,CAAC,QAAQ,IAAI;AACjE;AAEA,SAAS,oCAAoC,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACvG,MAAM,gBAA0B,EAAE;IAElC,kCAAkC;IAClC,IAAI,cAAc,IAAI;QACpB,cAAc,IAAI,CAAC,6BAA6B,gCAAgC;IAClF,OAAO,IAAI,cAAc,IAAI;QAC3B,cAAc,IAAI,CAAC,8BAA8B,4BAA4B;IAC/E;IAEA,gCAAgC;IAChC,OAAQ,UAAU,WAAW;QAC3B,KAAK;YACH,cAAc,IAAI,CAAC,+BAA+B,gCAAgC;YAClF;QACF,KAAK;QACL,KAAK;YACH,cAAc,IAAI,CAAC,iCAAiC,8BAA8B;YAClF;QACF,KAAK;YACH,cAAc,IAAI,CAAC,kCAAkC;YACrD;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,KAAU,EAAE,YAAoB;IAC/D,IAAI,QAAQ,GAAG,aAAa;IAE5B,MAAM,YAAY,CAAC,MAAM,IAAI,EAAE,QAAQ,EAAE,EAAE,WAAW;IACtD,MAAM,gBAAgB,CAAC,MAAM,QAAQ,EAAE,QAAQ,EAAE,EAAE,WAAW;IAE9D,0BAA0B;IAC1B,MAAM,mBAAmB,oBAAoB;IAC7C,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI,UAAU,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,UAAU;YAClE,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,IAAI,cAAc,QAAQ,CAAC,eAAe,cAAc,QAAQ,CAAC,eAAe;QAC9E,SAAS;IACX;IAEA,OAAO,KAAK,GAAG,CAAC,IAAI;AACtB;AAEA,SAAS,oBAAoB,YAAoB;IAC/C,MAAM,aAAuC;QAC3C,iCAAiC;YAAC;YAAW;YAAW;YAAW;YAAW;YAAc;YAAW;SAAO;QAC9G,cAAc;YAAC;YAAQ;YAAY;YAAW;YAAU;YAAQ;SAAa;QAC7E,WAAW;YAAC;YAAW;YAAU;YAAY;YAAO;YAAW;SAAY;QAC3E,cAAc;YAAC;YAAQ;YAAY;YAAe;YAAM;YAAW;SAAa;IAClF;IAEA,OAAO,UAAU,CAAC,aAAa,WAAW,GAAG,IAAI;QAAC;QAAY;QAAc;KAAe;AAC7F", "debugId": null}}, {"offset": {"line": 2318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/trending-topics.ts"], "sourcesContent": ["/**\r\n * Trending Topics and Market Intelligence System\r\n *\r\n * This module provides real-time trending topics, competitor analysis,\r\n * and market intelligence for content optimization.\r\n */\r\n\r\nimport {\r\n  fetchGoogleTrends,\r\n  fetchTwitterTrends,\r\n  fetchCurrentNews,\r\n  fetchRedditTrends\r\n} from './real-time-trends-integration';\r\n\r\nexport interface TrendingTopic {\r\n  topic: string;\r\n  relevanceScore: number; // 1-10\r\n  platform: string;\r\n  category: 'news' | 'entertainment' | 'business' | 'technology' | 'lifestyle' | 'local';\r\n  timeframe: 'now' | 'today' | 'week' | 'month';\r\n  engagement_potential: 'high' | 'medium' | 'low';\r\n}\r\n\r\nexport interface CompetitorInsight {\r\n  competitor_name: string;\r\n  content_gap: string;\r\n  differentiation_opportunity: string;\r\n  successful_strategy: string;\r\n  avoid_strategy: string;\r\n}\r\n\r\nexport interface CulturalContext {\r\n  location: string;\r\n  cultural_nuances: string[];\r\n  local_customs: string[];\r\n  language_preferences: string[];\r\n  seasonal_relevance: string[];\r\n  local_events: string[];\r\n}\r\n\r\nexport interface MarketIntelligence {\r\n  trending_topics: TrendingTopic[];\r\n  competitor_insights: CompetitorInsight[];\r\n  cultural_context: CulturalContext;\r\n  viral_content_patterns: string[];\r\n  engagement_triggers: string[];\r\n}\r\n\r\n/**\r\n * Generates real-time trending topics with fallback to static data\r\n */\r\nexport async function generateRealTimeTrendingTopics(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string = 'general'\r\n): Promise<TrendingTopic[]> {\r\n  try {\r\n\r\n    // Fetch from working real-time sources (temporarily disable failing APIs)\r\n    const [googleTrends, redditTrends] = await Promise.allSettled([\r\n      fetchGoogleTrends(location, businessType),\r\n      fetchRedditTrends(businessType, platform)\r\n    ]);\r\n\r\n\r\n    // Temporarily disable failing APIs until we fix them\r\n    const twitterTrends = { status: 'rejected' as const, reason: 'Temporarily disabled' };\r\n    const currentNews = { status: 'rejected' as const, reason: 'Temporarily disabled' };\r\n\r\n    const allTrends: TrendingTopic[] = [];\r\n\r\n    // Process Google Trends\r\n    if (googleTrends.status === 'fulfilled') {\r\n      allTrends.push(...googleTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    }\r\n\r\n    // Process Twitter Trends\r\n    if (twitterTrends.status === 'fulfilled') {\r\n      allTrends.push(...twitterTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    }\r\n\r\n    // Process News\r\n    if (currentNews.status === 'fulfilled') {\r\n      allTrends.push(...currentNews.value.map(news => ({\r\n        topic: news.topic,\r\n        relevanceScore: news.relevanceScore,\r\n        platform: platform,\r\n        category: news.category as any,\r\n        timeframe: news.timeframe as any,\r\n        engagement_potential: news.engagement_potential as any\r\n      })));\r\n    } else {\r\n    }\r\n\r\n    // Process Reddit Trends\r\n    if (redditTrends.status === 'fulfilled') {\r\n      allTrends.push(...redditTrends.value.map(trend => ({\r\n        topic: trend.topic,\r\n        relevanceScore: trend.relevanceScore,\r\n        platform: platform,\r\n        category: trend.category as any,\r\n        timeframe: trend.timeframe as any,\r\n        engagement_potential: trend.engagement_potential as any\r\n      })));\r\n    } else {\r\n    }\r\n\r\n    // If we have real-time trends, use them\r\n    if (allTrends.length > 0) {\r\n      return allTrends\r\n        .sort((a, b) => b.relevanceScore - a.relevanceScore)\r\n        .slice(0, 10);\r\n    }\r\n\r\n    // Fallback to static trends\r\n    return generateStaticTrendingTopics(businessType, location, platform);\r\n\r\n  } catch (error) {\r\n    return generateStaticTrendingTopics(businessType, location, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Generates static trending topics (original function, now renamed)\r\n */\r\nexport function generateStaticTrendingTopics(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string = 'general'\r\n): TrendingTopic[] {\r\n\r\n  const businessTrends: Record<string, TrendingTopic[]> = {\r\n    'restaurant': [\r\n      {\r\n        topic: 'Sustainable dining trends',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Local food festivals',\r\n        relevanceScore: 8,\r\n        platform: 'facebook',\r\n        category: 'local',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Plant-based menu innovations',\r\n        relevanceScore: 7,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'month',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'fitness': [\r\n      {\r\n        topic: 'New Year fitness resolutions',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Mental health and exercise',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'lifestyle',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Home workout equipment trends',\r\n        relevanceScore: 7,\r\n        platform: 'facebook',\r\n        category: 'lifestyle',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'technology': [\r\n      {\r\n        topic: 'AI in business automation',\r\n        relevanceScore: 10,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Cybersecurity awareness',\r\n        relevanceScore: 9,\r\n        platform: 'twitter',\r\n        category: 'technology',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Remote work productivity tools',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'beauty': [\r\n      {\r\n        topic: 'Clean beauty movement',\r\n        relevanceScore: 9,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Skincare for different seasons',\r\n        relevanceScore: 8,\r\n        platform: 'instagram',\r\n        category: 'lifestyle',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Sustainable beauty packaging',\r\n        relevanceScore: 7,\r\n        platform: 'facebook',\r\n        category: 'lifestyle',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'financial technology software': [\r\n      {\r\n        topic: 'Digital banking adoption in Africa',\r\n        relevanceScore: 10,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'now',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Financial inclusion initiatives',\r\n        relevanceScore: 9,\r\n        platform: 'twitter',\r\n        category: 'business',\r\n        timeframe: 'today',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Mobile payment security',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'technology',\r\n        timeframe: 'week',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Get base trends for business type\r\n  const baseTrends = businessTrends[businessType.toLowerCase()] || businessTrends['technology'];\r\n\r\n  // Add location-specific trends\r\n  const locationTrends = generateLocationTrends(location);\r\n\r\n  // Combine and filter by platform\r\n  const allTrends = [...baseTrends, ...locationTrends];\r\n\r\n  return allTrends\r\n    .filter(trend => trend.platform === platform || trend.platform === 'general')\r\n    .sort((a, b) => b.relevanceScore - a.relevanceScore)\r\n    .slice(0, 5);\r\n}\r\n\r\n/**\r\n * Generates location-specific trending topics\r\n */\r\nfunction generateLocationTrends(location: string): TrendingTopic[] {\r\n  const locationMap: Record<string, TrendingTopic[]> = {\r\n    'nairobi': [\r\n      {\r\n        topic: 'Nairobi tech hub growth',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      },\r\n      {\r\n        topic: 'Kenyan startup ecosystem',\r\n        relevanceScore: 7,\r\n        platform: 'twitter',\r\n        category: 'business',\r\n        timeframe: 'today',\r\n        engagement_potential: 'medium'\r\n      }\r\n    ],\r\n    'new york': [\r\n      {\r\n        topic: 'NYC small business support',\r\n        relevanceScore: 8,\r\n        platform: 'facebook',\r\n        category: 'local',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      }\r\n    ],\r\n    'london': [\r\n      {\r\n        topic: 'London fintech innovation',\r\n        relevanceScore: 8,\r\n        platform: 'linkedin',\r\n        category: 'business',\r\n        timeframe: 'week',\r\n        engagement_potential: 'high'\r\n      }\r\n    ]\r\n  };\r\n\r\n  const locationKey = location.toLowerCase().split(',')[0].trim();\r\n  return locationMap[locationKey] || [];\r\n}\r\n\r\n/**\r\n * Generates competitor analysis insights\r\n */\r\nexport function generateCompetitorInsights(\r\n  businessType: string,\r\n  location: string,\r\n  services?: string\r\n): CompetitorInsight[] {\r\n\r\n  const competitorStrategies: Record<string, CompetitorInsight[]> = {\r\n    'financial technology software': [\r\n      {\r\n        competitor_name: 'Traditional Banks',\r\n        content_gap: 'Lack of educational content about digital banking benefits',\r\n        differentiation_opportunity: 'Focus on simplicity and accessibility for everyday users',\r\n        successful_strategy: 'Trust-building through security messaging',\r\n        avoid_strategy: 'Overly technical jargon that confuses users'\r\n      },\r\n      {\r\n        competitor_name: 'Other Fintech Apps',\r\n        content_gap: 'Limited focus on local market needs and culture',\r\n        differentiation_opportunity: 'Emphasize local partnerships and community impact',\r\n        successful_strategy: 'User testimonials and success stories',\r\n        avoid_strategy: 'Generic global messaging without local relevance'\r\n      }\r\n    ],\r\n    'restaurant': [\r\n      {\r\n        competitor_name: 'Chain Restaurants',\r\n        content_gap: 'Lack of personal connection and local community focus',\r\n        differentiation_opportunity: 'Highlight local sourcing, chef personality, and community involvement',\r\n        successful_strategy: 'Behind-the-scenes content and food preparation videos',\r\n        avoid_strategy: 'Generic food photos without story or context'\r\n      }\r\n    ],\r\n    'fitness': [\r\n      {\r\n        competitor_name: 'Large Gym Chains',\r\n        content_gap: 'Impersonal approach and lack of individual attention',\r\n        differentiation_opportunity: 'Focus on personal transformation stories and community support',\r\n        successful_strategy: 'Client success stories and progress tracking',\r\n        avoid_strategy: 'Intimidating fitness content that discourages beginners'\r\n      }\r\n    ]\r\n  };\r\n\r\n  return competitorStrategies[businessType.toLowerCase()] || [\r\n    {\r\n      competitor_name: 'Industry Leaders',\r\n      content_gap: 'Generic messaging without personal touch',\r\n      differentiation_opportunity: 'Focus on authentic storytelling and customer relationships',\r\n      successful_strategy: 'Educational content that provides real value',\r\n      avoid_strategy: 'Overly promotional content without substance'\r\n    }\r\n  ];\r\n}\r\n\r\n/**\r\n * Generates cultural context for location-specific content\r\n */\r\nexport function generateCulturalContext(location: string): CulturalContext {\r\n  const culturalMap: Record<string, CulturalContext> = {\r\n    'nairobi, kenya': {\r\n      location: 'Nairobi, Kenya',\r\n      cultural_nuances: [\r\n        'Ubuntu philosophy - community and interconnectedness',\r\n        'Respect for elders and traditional values',\r\n        'Entrepreneurial spirit and innovation mindset',\r\n        'Multilingual communication (English, Swahili, local languages)'\r\n      ],\r\n      local_customs: [\r\n        'Harambee - community cooperation and fundraising',\r\n        'Greeting customs and respect protocols',\r\n        'Religious diversity and tolerance',\r\n        'Family-centered decision making'\r\n      ],\r\n      language_preferences: [\r\n        'Mix of English and Swahili phrases',\r\n        'Respectful and formal tone in business contexts',\r\n        'Storytelling and proverb usage',\r\n        'Community-focused language'\r\n      ],\r\n      seasonal_relevance: [\r\n        'Rainy seasons (March-May, October-December)',\r\n        'School calendar considerations',\r\n        'Agricultural seasons and harvest times',\r\n        'Holiday seasons and celebrations'\r\n      ],\r\n      local_events: [\r\n        'Nairobi Innovation Week',\r\n        'Kenya Music Festival',\r\n        'Nairobi Restaurant Week',\r\n        'Local cultural festivals'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const locationKey = location.toLowerCase();\r\n  return culturalMap[locationKey] || {\r\n    location: location,\r\n    cultural_nuances: ['Local community values', 'Regional business customs'],\r\n    local_customs: ['Local traditions', 'Community practices'],\r\n    language_preferences: ['Local language nuances', 'Regional communication styles'],\r\n    seasonal_relevance: ['Local seasons', 'Regional events'],\r\n    local_events: ['Local festivals', 'Community gatherings']\r\n  };\r\n}\r\n\r\n/**\r\n * Generates complete market intelligence\r\n */\r\nexport function generateMarketIntelligence(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  services?: string\r\n): MarketIntelligence {\r\n  return {\r\n    trending_topics: generateStaticTrendingTopics(businessType, location, platform),\r\n    competitor_insights: generateCompetitorInsights(businessType, location, services),\r\n    cultural_context: generateCulturalContext(location),\r\n    viral_content_patterns: [\r\n      'Behind-the-scenes authentic moments',\r\n      'User-generated content and testimonials',\r\n      'Educational content that solves problems',\r\n      'Emotional storytelling with clear outcomes',\r\n      'Interactive content that encourages participation'\r\n    ],\r\n    engagement_triggers: [\r\n      'Ask questions that require personal responses',\r\n      'Share relatable struggles and solutions',\r\n      'Use local references and cultural touchpoints',\r\n      'Create content that people want to share with friends',\r\n      'Provide exclusive insights or early access'\r\n    ]\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAED;;AA4CO,eAAe,+BACpB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,SAAS;IAE5B,IAAI;QAEF,0EAA0E;QAC1E,MAAM,CAAC,cAAc,aAAa,GAAG,MAAM,QAAQ,UAAU,CAAC;YAC5D,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;YAC5B,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc;SACjC;QAGD,qDAAqD;QACrD,MAAM,gBAAgB;YAAE,QAAQ;YAAqB,QAAQ;QAAuB;QACpF,MAAM,cAAc;YAAE,QAAQ;YAAqB,QAAQ;QAAuB;QAElF,MAAM,YAA6B,EAAE;QAErC,wBAAwB;QACxB,IAAI,aAAa,MAAM,KAAK,aAAa;YACvC,UAAU,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACjD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH;QAEA,yBAAyB;QACzB,IAAI,cAAc,MAAM,KAAK,aAAa;YACxC,UAAU,IAAI,IAAI,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAClD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH;QAEA,eAAe;QACf,IAAI,YAAY,MAAM,KAAK,aAAa;YACtC,UAAU,IAAI,IAAI,YAAY,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC/C,OAAO,KAAK,KAAK;oBACjB,gBAAgB,KAAK,cAAc;oBACnC,UAAU;oBACV,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,sBAAsB,KAAK,oBAAoB;gBACjD,CAAC;QACH,OAAO,CACP;QAEA,wBAAwB;QACxB,IAAI,aAAa,MAAM,KAAK,aAAa;YACvC,UAAU,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACjD,OAAO,MAAM,KAAK;oBAClB,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,sBAAsB,MAAM,oBAAoB;gBAClD,CAAC;QACH,OAAO,CACP;QAEA,wCAAwC;QACxC,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,OAAO,UACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,KAAK,CAAC,GAAG;QACd;QAEA,4BAA4B;QAC5B,OAAO,6BAA6B,cAAc,UAAU;IAE9D,EAAE,OAAO,OAAO;QACd,OAAO,6BAA6B,cAAc,UAAU;IAC9D;AACF;AAKO,SAAS,6BACd,YAAoB,EACpB,QAAgB,EAChB,WAAmB,SAAS;IAG5B,MAAM,iBAAkD;QACtD,cAAc;YACZ;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,WAAW;YACT;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,cAAc;YACZ;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,UAAU;YACR;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,iCAAiC;YAC/B;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;IACH;IAEA,oCAAoC;IACpC,MAAM,aAAa,cAAc,CAAC,aAAa,WAAW,GAAG,IAAI,cAAc,CAAC,aAAa;IAE7F,+BAA+B;IAC/B,MAAM,iBAAiB,uBAAuB;IAE9C,iCAAiC;IACjC,MAAM,YAAY;WAAI;WAAe;KAAe;IAEpD,OAAO,UACJ,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK,YAAY,MAAM,QAAQ,KAAK,WAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,KAAK,CAAC,GAAG;AACd;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,MAAM,cAA+C;QACnD,WAAW;YACT;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;YACA;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,YAAY;YACV;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;QACD,UAAU;YACR;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,sBAAsB;YACxB;SACD;IACH;IAEA,MAAM,cAAc,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAC7D,OAAO,WAAW,CAAC,YAAY,IAAI,EAAE;AACvC;AAKO,SAAS,2BACd,YAAoB,EACpB,QAAgB,EAChB,QAAiB;IAGjB,MAAM,uBAA4D;QAChE,iCAAiC;YAC/B;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;YACA;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;QACD,cAAc;YACZ;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;QACD,WAAW;YACT;gBACE,iBAAiB;gBACjB,aAAa;gBACb,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB;YAClB;SACD;IACH;IAEA,OAAO,oBAAoB,CAAC,aAAa,WAAW,GAAG,IAAI;QACzD;YACE,iBAAiB;YACjB,aAAa;YACb,6BAA6B;YAC7B,qBAAqB;YACrB,gBAAgB;QAClB;KACD;AACH;AAKO,SAAS,wBAAwB,QAAgB;IACtD,MAAM,cAA+C;QACnD,kBAAkB;YAChB,UAAU;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,eAAe;gBACb;gBACA;gBACA;gBACA;aACD;YACD,sBAAsB;gBACpB;gBACA;gBACA;gBACA;aACD;YACD,oBAAoB;gBAClB;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,cAAc,SAAS,WAAW;IACxC,OAAO,WAAW,CAAC,YAAY,IAAI;QACjC,UAAU;QACV,kBAAkB;YAAC;YAA0B;SAA4B;QACzE,eAAe;YAAC;YAAoB;SAAsB;QAC1D,sBAAsB;YAAC;YAA0B;SAAgC;QACjF,oBAAoB;YAAC;YAAiB;SAAkB;QACxD,cAAc;YAAC;YAAmB;SAAuB;IAC3D;AACF;AAKO,SAAS,2BACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB;IAEjB,OAAO;QACL,iBAAiB,6BAA6B,cAAc,UAAU;QACtE,qBAAqB,2BAA2B,cAAc,UAAU;QACxE,kBAAkB,wBAAwB;QAC1C,wBAAwB;YACtB;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/intelligent-context-selector.ts"], "sourcesContent": ["/**\r\n * Intelligent Context Selector\r\n * \r\n * This module acts like a local expert who knows what information\r\n * is relevant for each business type, location, and content context.\r\n * It intelligently selects which data to use and which to ignore.\r\n */\r\n\r\nexport interface ContextRelevance {\r\n  weather: {\r\n    useWeather: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n  };\r\n  events: {\r\n    useEvents: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    eventTypes: string[];\r\n  };\r\n  trends: {\r\n    useTrends: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    trendTypes: string[];\r\n  };\r\n  cultural: {\r\n    useCultural: boolean;\r\n    relevanceReason: string;\r\n    priority: 'high' | 'medium' | 'low' | 'ignore';\r\n    culturalElements: string[];\r\n  };\r\n}\r\n\r\n/**\r\n * Intelligently determines what context information to use\r\n * based on business type, location, and content purpose\r\n */\r\nexport function selectRelevantContext(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  contentThemes?: string,\r\n  dayOfWeek?: string\r\n): ContextRelevance {\r\n  \r\n  const businessKey = businessType.toLowerCase();\r\n  const locationKey = location.toLowerCase();\r\n  const isWeekend = dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday';\r\n  \r\n  return {\r\n    weather: analyzeWeatherRelevance(businessKey, locationKey, platform, isWeekend),\r\n    events: analyzeEventsRelevance(businessKey, locationKey, platform, isWeekend),\r\n    trends: analyzeTrendsRelevance(businessKey, locationKey, platform),\r\n    cultural: analyzeCulturalRelevance(businessKey, locationKey, platform)\r\n  };\r\n}\r\n\r\n/**\r\n * Determines if weather information is relevant for this business/location\r\n */\r\nfunction analyzeWeatherRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  isWeekend: boolean\r\n): ContextRelevance['weather'] {\r\n  \r\n  // High weather relevance businesses\r\n  const weatherSensitiveBusinesses = [\r\n    'restaurant', 'cafe', 'food', 'dining',\r\n    'fitness', 'gym', 'sports', 'outdoor',\r\n    'retail', 'shopping', 'fashion',\r\n    'tourism', 'travel', 'hotel',\r\n    'construction', 'agriculture',\r\n    'delivery', 'transportation'\r\n  ];\r\n  \r\n  // Medium weather relevance\r\n  const moderateWeatherBusinesses = [\r\n    'beauty', 'spa', 'wellness',\r\n    'entertainment', 'events',\r\n    'real estate', 'automotive'\r\n  ];\r\n  \r\n  // Low/No weather relevance\r\n  const weatherIndependentBusinesses = [\r\n    'financial technology software', 'fintech', 'banking',\r\n    'software', 'technology', 'saas',\r\n    'consulting', 'legal', 'accounting',\r\n    'insurance', 'healthcare', 'education',\r\n    'digital marketing', 'design'\r\n  ];\r\n  \r\n  // Check business type relevance\r\n  const isHighRelevance = weatherSensitiveBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  const isMediumRelevance = moderateWeatherBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  const isLowRelevance = weatherIndependentBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  // Location-based adjustments\r\n  const isExtremeWeatherLocation = location.includes('nairobi') || \r\n                                   location.includes('kenya') ||\r\n                                   location.includes('tropical');\r\n  \r\n  if (isHighRelevance) {\r\n    return {\r\n      useWeather: true,\r\n      relevanceReason: `${businessType} customers are highly influenced by weather conditions`,\r\n      priority: 'high'\r\n    };\r\n  }\r\n  \r\n  if (isMediumRelevance) {\r\n    return {\r\n      useWeather: true,\r\n      relevanceReason: `Weather can impact ${businessType} customer behavior`,\r\n      priority: 'medium'\r\n    };\r\n  }\r\n  \r\n  if (isLowRelevance) {\r\n    return {\r\n      useWeather: false,\r\n      relevanceReason: `${businessType} operates independently of weather conditions`,\r\n      priority: 'ignore'\r\n    };\r\n  }\r\n  \r\n  // Default case\r\n  return {\r\n    useWeather: isExtremeWeatherLocation,\r\n    relevanceReason: isExtremeWeatherLocation ? \r\n      'Local weather is culturally significant' : \r\n      'Weather has minimal business impact',\r\n    priority: isExtremeWeatherLocation ? 'low' : 'ignore'\r\n  };\r\n}\r\n\r\n/**\r\n * Determines if local events are relevant for this business/location\r\n */\r\nfunction analyzeEventsRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string,\r\n  isWeekend: boolean\r\n): ContextRelevance['events'] {\r\n  \r\n  // Always relevant for networking/community businesses\r\n  const networkingBusinesses = [\r\n    'consulting', 'marketing', 'business services',\r\n    'financial technology software', 'fintech',\r\n    'real estate', 'insurance', 'legal'\r\n  ];\r\n  \r\n  // Event-dependent businesses\r\n  const eventDependentBusinesses = [\r\n    'restaurant', 'entertainment', 'retail',\r\n    'fitness', 'beauty', 'tourism'\r\n  ];\r\n  \r\n  // B2B vs B2C consideration\r\n  const isB2B = networkingBusinesses.some(type => businessType.includes(type)) ||\r\n                businessType.includes('software') ||\r\n                businessType.includes('technology');\r\n  \r\n  const isB2C = eventDependentBusinesses.some(type => businessType.includes(type));\r\n  \r\n  // Relevant event types based on business\r\n  let eventTypes: string[] = [];\r\n  \r\n  if (isB2B) {\r\n    eventTypes = ['business', 'networking', 'conference', 'workshop', 'professional'];\r\n  }\r\n  \r\n  if (isB2C) {\r\n    eventTypes = ['community', 'festival', 'entertainment', 'cultural', 'local'];\r\n  }\r\n  \r\n  // Location-based event culture\r\n  const isEventCentricLocation = location.includes('nairobi') ||\r\n                                 location.includes('new york') ||\r\n                                 location.includes('london');\r\n  \r\n  if (isB2B && isEventCentricLocation) {\r\n    return {\r\n      useEvents: true,\r\n      relevanceReason: `${businessType} benefits from professional networking events`,\r\n      priority: 'high',\r\n      eventTypes\r\n    };\r\n  }\r\n  \r\n  if (isB2C) {\r\n    return {\r\n      useEvents: true,\r\n      relevanceReason: `Local events drive foot traffic for ${businessType}`,\r\n      priority: 'medium',\r\n      eventTypes\r\n    };\r\n  }\r\n  \r\n  return {\r\n    useEvents: isEventCentricLocation,\r\n    relevanceReason: isEventCentricLocation ? \r\n      'Local events show community engagement' : \r\n      'Events have minimal business relevance',\r\n    priority: isEventCentricLocation ? 'low' : 'ignore',\r\n    eventTypes: ['community']\r\n  };\r\n}\r\n\r\n/**\r\n * Determines trending topics relevance\r\n */\r\nfunction analyzeTrendsRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string\r\n): ContextRelevance['trends'] {\r\n  \r\n  // Always use trends for social media businesses\r\n  const trendDependentBusinesses = [\r\n    'marketing', 'social media', 'content',\r\n    'entertainment', 'fashion', 'beauty',\r\n    'technology', 'startup'\r\n  ];\r\n  \r\n  // Industry-specific trend types\r\n  let trendTypes: string[] = [];\r\n  \r\n  if (businessType.includes('technology') || businessType.includes('fintech')) {\r\n    trendTypes = ['technology', 'business', 'innovation', 'startup'];\r\n  } else if (businessType.includes('restaurant') || businessType.includes('food')) {\r\n    trendTypes = ['food', 'lifestyle', 'local', 'cultural'];\r\n  } else if (businessType.includes('fitness')) {\r\n    trendTypes = ['health', 'wellness', 'lifestyle', 'sports'];\r\n  } else {\r\n    trendTypes = ['business', 'local', 'community'];\r\n  }\r\n  \r\n  const isTrendSensitive = trendDependentBusinesses.some(type => \r\n    businessType.includes(type)\r\n  );\r\n  \r\n  // Platform consideration\r\n  const isSocialPlatform = platform === 'instagram' || platform === 'twitter';\r\n  \r\n  return {\r\n    useTrends: true, // Most businesses benefit from some trending topics\r\n    relevanceReason: isTrendSensitive ? \r\n      `${businessType} thrives on current trends and conversations` :\r\n      'Trending topics increase content relevance and engagement',\r\n    priority: isTrendSensitive ? 'high' : 'medium',\r\n    trendTypes\r\n  };\r\n}\r\n\r\n/**\r\n * Determines cultural context relevance\r\n */\r\nfunction analyzeCulturalRelevance(\r\n  businessType: string,\r\n  location: string,\r\n  platform: string\r\n): ContextRelevance['cultural'] {\r\n  \r\n  // Always high relevance for local businesses\r\n  const localBusinesses = [\r\n    'restaurant', 'retail', 'fitness', 'beauty',\r\n    'real estate', 'healthcare', 'education'\r\n  ];\r\n  \r\n  // Cultural elements to emphasize\r\n  let culturalElements: string[] = [];\r\n  \r\n  if (location.includes('nairobi') || location.includes('kenya')) {\r\n    culturalElements = ['ubuntu philosophy', 'harambee spirit', 'swahili expressions', 'community values'];\r\n  } else if (location.includes('new york')) {\r\n    culturalElements = ['diversity', 'hustle culture', 'innovation', 'fast-paced lifestyle'];\r\n  } else if (location.includes('london')) {\r\n    culturalElements = ['tradition', 'multiculturalism', 'business etiquette', 'dry humor'];\r\n  } else {\r\n    culturalElements = ['local customs', 'community values', 'regional preferences'];\r\n  }\r\n  \r\n  const isLocalBusiness = localBusinesses.some(type => businessType.includes(type));\r\n  const isInternationalLocation = !location.includes('united states');\r\n  \r\n  return {\r\n    useCultural: true, // Cultural context is almost always relevant\r\n    relevanceReason: isLocalBusiness ? \r\n      `Local ${businessType} must connect with community culture` :\r\n      'Cultural awareness builds authentic connections',\r\n    priority: isLocalBusiness || isInternationalLocation ? 'high' : 'medium',\r\n    culturalElements\r\n  };\r\n}\r\n\r\n/**\r\n * Filters and prioritizes available context data based on relevance analysis\r\n */\r\nexport function filterContextData(\r\n  relevance: ContextRelevance,\r\n  availableData: {\r\n    weather?: any;\r\n    events?: any[];\r\n    trends?: any[];\r\n    cultural?: any;\r\n  }\r\n): {\r\n  selectedWeather?: any;\r\n  selectedEvents?: any[];\r\n  selectedTrends?: any[];\r\n  selectedCultural?: any;\r\n  contextInstructions: string;\r\n} {\r\n  \r\n  const result: any = {\r\n    contextInstructions: generateContextInstructions(relevance)\r\n  };\r\n  \r\n  // Filter weather data\r\n  if (relevance.weather.useWeather && availableData.weather) {\r\n    result.selectedWeather = availableData.weather;\r\n  }\r\n  \r\n  // Filter events data\r\n  if (relevance.events.useEvents && availableData.events) {\r\n    result.selectedEvents = availableData.events\r\n      .filter(event => \r\n        relevance.events.eventTypes.some(type => \r\n          event.category?.toLowerCase().includes(type) ||\r\n          event.name?.toLowerCase().includes(type)\r\n        )\r\n      )\r\n      .slice(0, relevance.events.priority === 'high' ? 3 : 1);\r\n  }\r\n  \r\n  // Filter trends data\r\n  if (relevance.trends.useTrends && availableData.trends) {\r\n    result.selectedTrends = availableData.trends\r\n      .filter(trend => \r\n        relevance.trends.trendTypes.some(type => \r\n          trend.category?.toLowerCase().includes(type) ||\r\n          trend.topic?.toLowerCase().includes(type)\r\n        )\r\n      )\r\n      .slice(0, relevance.trends.priority === 'high' ? 5 : 3);\r\n  }\r\n  \r\n  // Filter cultural data\r\n  if (relevance.cultural.useCultural && availableData.cultural) {\r\n    result.selectedCultural = {\r\n      ...availableData.cultural,\r\n      cultural_nuances: availableData.cultural.cultural_nuances?.filter(nuance =>\r\n        relevance.cultural.culturalElements.some(element =>\r\n          nuance.toLowerCase().includes(element.toLowerCase())\r\n        )\r\n      ).slice(0, 3)\r\n    };\r\n  }\r\n  \r\n  return result;\r\n}\r\n\r\n/**\r\n * Generates context-specific instructions for the AI\r\n */\r\nfunction generateContextInstructions(relevance: ContextRelevance): string {\r\n  const instructions: string[] = [];\r\n  \r\n  if (relevance.weather.useWeather) {\r\n    if (relevance.weather.priority === 'high') {\r\n      instructions.push('WEATHER: Integrate weather naturally as it significantly impacts customer behavior');\r\n    } else if (relevance.weather.priority === 'medium') {\r\n      instructions.push('WEATHER: Mention weather subtly if it adds value to the message');\r\n    }\r\n  } else {\r\n    instructions.push('WEATHER: Ignore weather data - not relevant for this business type');\r\n  }\r\n  \r\n  if (relevance.events.useEvents) {\r\n    if (relevance.events.priority === 'high') {\r\n      instructions.push('EVENTS: Highlight relevant local events as key business opportunities');\r\n    } else {\r\n      instructions.push('EVENTS: Reference events only if they add community connection value');\r\n    }\r\n  } else {\r\n    instructions.push('EVENTS: Skip event references - focus on core business value');\r\n  }\r\n  \r\n  if (relevance.trends.priority === 'high') {\r\n    instructions.push('TRENDS: Lead with trending topics to maximize engagement and relevance');\r\n  } else {\r\n    instructions.push('TRENDS: Use trends subtly to add contemporary relevance');\r\n  }\r\n  \r\n  if (relevance.cultural.priority === 'high') {\r\n    instructions.push('CULTURE: Deeply integrate local cultural elements for authentic connection');\r\n  } else {\r\n    instructions.push('CULTURE: Include respectful cultural awareness without overdoing it');\r\n  }\r\n  \r\n  return instructions.join('\\n');\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAgCM,SAAS,sBACd,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,aAAsB,EACtB,SAAkB;IAGlB,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,cAAc,SAAS,WAAW;IACxC,MAAM,YAAY,cAAc,cAAc,cAAc;IAE5D,OAAO;QACL,SAAS,wBAAwB,aAAa,aAAa,UAAU;QACrE,QAAQ,uBAAuB,aAAa,aAAa,UAAU;QACnE,QAAQ,uBAAuB,aAAa,aAAa;QACzD,UAAU,yBAAyB,aAAa,aAAa;IAC/D;AACF;AAEA;;CAEC,GACD,SAAS,wBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,SAAkB;IAGlB,oCAAoC;IACpC,MAAM,6BAA6B;QACjC;QAAc;QAAQ;QAAQ;QAC9B;QAAW;QAAO;QAAU;QAC5B;QAAU;QAAY;QACtB;QAAW;QAAU;QACrB;QAAgB;QAChB;QAAY;KACb;IAED,2BAA2B;IAC3B,MAAM,4BAA4B;QAChC;QAAU;QAAO;QACjB;QAAiB;QACjB;QAAe;KAChB;IAED,2BAA2B;IAC3B,MAAM,+BAA+B;QACnC;QAAiC;QAAW;QAC5C;QAAY;QAAc;QAC1B;QAAc;QAAS;QACvB;QAAa;QAAc;QAC3B;QAAqB;KACtB;IAED,gCAAgC;IAChC,MAAM,kBAAkB,2BAA2B,IAAI,CAAC,CAAA,OACtD,aAAa,QAAQ,CAAC;IAGxB,MAAM,oBAAoB,0BAA0B,IAAI,CAAC,CAAA,OACvD,aAAa,QAAQ,CAAC;IAGxB,MAAM,iBAAiB,6BAA6B,IAAI,CAAC,CAAA,OACvD,aAAa,QAAQ,CAAC;IAGxB,6BAA6B;IAC7B,MAAM,2BAA2B,SAAS,QAAQ,CAAC,cAClB,SAAS,QAAQ,CAAC,YAClB,SAAS,QAAQ,CAAC;IAEnD,IAAI,iBAAiB;QACnB,OAAO;YACL,YAAY;YACZ,iBAAiB,GAAG,aAAa,sDAAsD,CAAC;YACxF,UAAU;QACZ;IACF;IAEA,IAAI,mBAAmB;QACrB,OAAO;YACL,YAAY;YACZ,iBAAiB,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,CAAC;YACvE,UAAU;QACZ;IACF;IAEA,IAAI,gBAAgB;QAClB,OAAO;YACL,YAAY;YACZ,iBAAiB,GAAG,aAAa,6CAA6C,CAAC;YAC/E,UAAU;QACZ;IACF;IAEA,eAAe;IACf,OAAO;QACL,YAAY;QACZ,iBAAiB,2BACf,4CACA;QACF,UAAU,2BAA2B,QAAQ;IAC/C;AACF;AAEA;;CAEC,GACD,SAAS,uBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,SAAkB;IAGlB,sDAAsD;IACtD,MAAM,uBAAuB;QAC3B;QAAc;QAAa;QAC3B;QAAiC;QACjC;QAAe;QAAa;KAC7B;IAED,6BAA6B;IAC7B,MAAM,2BAA2B;QAC/B;QAAc;QAAiB;QAC/B;QAAW;QAAU;KACtB;IAED,2BAA2B;IAC3B,MAAM,QAAQ,qBAAqB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,UACxD,aAAa,QAAQ,CAAC,eACtB,aAAa,QAAQ,CAAC;IAEpC,MAAM,QAAQ,yBAAyB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC;IAE1E,yCAAyC;IACzC,IAAI,aAAuB,EAAE;IAE7B,IAAI,OAAO;QACT,aAAa;YAAC;YAAY;YAAc;YAAc;YAAY;SAAe;IACnF;IAEA,IAAI,OAAO;QACT,aAAa;YAAC;YAAa;YAAY;YAAiB;YAAY;SAAQ;IAC9E;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB,SAAS,QAAQ,CAAC,cAClB,SAAS,QAAQ,CAAC,eAClB,SAAS,QAAQ,CAAC;IAEjD,IAAI,SAAS,wBAAwB;QACnC,OAAO;YACL,WAAW;YACX,iBAAiB,GAAG,aAAa,6CAA6C,CAAC;YAC/E,UAAU;YACV;QACF;IACF;IAEA,IAAI,OAAO;QACT,OAAO;YACL,WAAW;YACX,iBAAiB,CAAC,oCAAoC,EAAE,cAAc;YACtE,UAAU;YACV;QACF;IACF;IAEA,OAAO;QACL,WAAW;QACX,iBAAiB,yBACf,2CACA;QACF,UAAU,yBAAyB,QAAQ;QAC3C,YAAY;YAAC;SAAY;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,uBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB;IAGhB,gDAAgD;IAChD,MAAM,2BAA2B;QAC/B;QAAa;QAAgB;QAC7B;QAAiB;QAAW;QAC5B;QAAc;KACf;IAED,gCAAgC;IAChC,IAAI,aAAuB,EAAE;IAE7B,IAAI,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,YAAY;QAC3E,aAAa;YAAC;YAAc;YAAY;YAAc;SAAU;IAClE,OAAO,IAAI,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,SAAS;QAC/E,aAAa;YAAC;YAAQ;YAAa;YAAS;SAAW;IACzD,OAAO,IAAI,aAAa,QAAQ,CAAC,YAAY;QAC3C,aAAa;YAAC;YAAU;YAAY;YAAa;SAAS;IAC5D,OAAO;QACL,aAAa;YAAC;YAAY;YAAS;SAAY;IACjD;IAEA,MAAM,mBAAmB,yBAAyB,IAAI,CAAC,CAAA,OACrD,aAAa,QAAQ,CAAC;IAGxB,yBAAyB;IACzB,MAAM,mBAAmB,aAAa,eAAe,aAAa;IAElE,OAAO;QACL,WAAW;QACX,iBAAiB,mBACf,GAAG,aAAa,4CAA4C,CAAC,GAC7D;QACF,UAAU,mBAAmB,SAAS;QACtC;IACF;AACF;AAEA;;CAEC,GACD,SAAS,yBACP,YAAoB,EACpB,QAAgB,EAChB,QAAgB;IAGhB,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB;QAAc;QAAU;QAAW;QACnC;QAAe;QAAc;KAC9B;IAED,iCAAiC;IACjC,IAAI,mBAA6B,EAAE;IAEnC,IAAI,SAAS,QAAQ,CAAC,cAAc,SAAS,QAAQ,CAAC,UAAU;QAC9D,mBAAmB;YAAC;YAAqB;YAAmB;YAAuB;SAAmB;IACxG,OAAO,IAAI,SAAS,QAAQ,CAAC,aAAa;QACxC,mBAAmB;YAAC;YAAa;YAAkB;YAAc;SAAuB;IAC1F,OAAO,IAAI,SAAS,QAAQ,CAAC,WAAW;QACtC,mBAAmB;YAAC;YAAa;YAAoB;YAAsB;SAAY;IACzF,OAAO;QACL,mBAAmB;YAAC;YAAiB;YAAoB;SAAuB;IAClF;IAEA,MAAM,kBAAkB,gBAAgB,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC;IAC3E,MAAM,0BAA0B,CAAC,SAAS,QAAQ,CAAC;IAEnD,OAAO;QACL,aAAa;QACb,iBAAiB,kBACf,CAAC,MAAM,EAAE,aAAa,oCAAoC,CAAC,GAC3D;QACF,UAAU,mBAAmB,0BAA0B,SAAS;QAChE;IACF;AACF;AAKO,SAAS,kBACd,SAA2B,EAC3B,aAKC;IASD,MAAM,SAAc;QAClB,qBAAqB,4BAA4B;IACnD;IAEA,sBAAsB;IACtB,IAAI,UAAU,OAAO,CAAC,UAAU,IAAI,cAAc,OAAO,EAAE;QACzD,OAAO,eAAe,GAAG,cAAc,OAAO;IAChD;IAEA,qBAAqB;IACrB,IAAI,UAAU,MAAM,CAAC,SAAS,IAAI,cAAc,MAAM,EAAE;QACtD,OAAO,cAAc,GAAG,cAAc,MAAM,CACzC,MAAM,CAAC,CAAA,QACN,UAAU,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,OAC/B,MAAM,QAAQ,EAAE,cAAc,SAAS,SACvC,MAAM,IAAI,EAAE,cAAc,SAAS,QAGtC,KAAK,CAAC,GAAG,UAAU,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI;IACzD;IAEA,qBAAqB;IACrB,IAAI,UAAU,MAAM,CAAC,SAAS,IAAI,cAAc,MAAM,EAAE;QACtD,OAAO,cAAc,GAAG,cAAc,MAAM,CACzC,MAAM,CAAC,CAAA,QACN,UAAU,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,OAC/B,MAAM,QAAQ,EAAE,cAAc,SAAS,SACvC,MAAM,KAAK,EAAE,cAAc,SAAS,QAGvC,KAAK,CAAC,GAAG,UAAU,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI;IACzD;IAEA,uBAAuB;IACvB,IAAI,UAAU,QAAQ,CAAC,WAAW,IAAI,cAAc,QAAQ,EAAE;QAC5D,OAAO,gBAAgB,GAAG;YACxB,GAAG,cAAc,QAAQ;YACzB,kBAAkB,cAAc,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAA,SAChE,UAAU,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,UACvC,OAAO,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,MAEnD,MAAM,GAAG;QACb;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,4BAA4B,SAA2B;IAC9D,MAAM,eAAyB,EAAE;IAEjC,IAAI,UAAU,OAAO,CAAC,UAAU,EAAE;QAChC,IAAI,UAAU,OAAO,CAAC,QAAQ,KAAK,QAAQ;YACzC,aAAa,IAAI,CAAC;QACpB,OAAO,IAAI,UAAU,OAAO,CAAC,QAAQ,KAAK,UAAU;YAClD,aAAa,IAAI,CAAC;QACpB;IACF,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,MAAM,CAAC,SAAS,EAAE;QAC9B,IAAI,UAAU,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACxC,aAAa,IAAI,CAAC;QACpB,OAAO;YACL,aAAa,IAAI,CAAC;QACpB;IACF,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,MAAM,CAAC,QAAQ,KAAK,QAAQ;QACxC,aAAa,IAAI,CAAC;IACpB,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,IAAI,UAAU,QAAQ,CAAC,QAAQ,KAAK,QAAQ;QAC1C,aAAa,IAAI,CAAC;IACpB,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 3078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/human-content-generator.ts"], "sourcesContent": ["/**\r\n * Human-Like Content Generation System\r\n * \r\n * This module provides techniques to make AI-generated content\r\n * feel authentic, human, and engaging while avoiding AI detection.\r\n */\r\n\r\nexport interface HumanizationTechniques {\r\n  personality_markers: string[];\r\n  authenticity_elements: string[];\r\n  conversational_patterns: string[];\r\n  storytelling_devices: string[];\r\n  emotional_connectors: string[];\r\n  imperfection_markers: string[];\r\n}\r\n\r\nexport interface TrafficDrivingElements {\r\n  viral_hooks: string[];\r\n  engagement_magnets: string[];\r\n  conversion_triggers: string[];\r\n  shareability_factors: string[];\r\n  curiosity_gaps: string[];\r\n  social_proof_elements: string[];\r\n}\r\n\r\n/**\r\n * Generates human-like content techniques based on business type and brand voice\r\n */\r\nexport function generateHumanizationTechniques(\r\n  businessType: string,\r\n  brandVoice: string,\r\n  location: string\r\n): HumanizationTechniques {\r\n  \r\n  const basePersonality = getPersonalityMarkers(brandVoice);\r\n  const industryAuthenticity = getIndustryAuthenticity(businessType);\r\n  const locationConversation = getLocationConversation(location);\r\n  \r\n  return {\r\n    personality_markers: [\r\n      ...basePersonality,\r\n      'Use first-person perspective occasionally',\r\n      'Include personal opinions and preferences',\r\n      'Show vulnerability and learning moments',\r\n      'Express genuine excitement about successes'\r\n    ],\r\n    authenticity_elements: [\r\n      ...industryAuthenticity,\r\n      'Share behind-the-scenes moments',\r\n      'Admit mistakes and lessons learned',\r\n      'Use specific details instead of generalities',\r\n      'Reference real experiences and observations',\r\n      'Include time-specific references (today, this morning, etc.)'\r\n    ],\r\n    conversational_patterns: [\r\n      ...locationConversation,\r\n      'Start sentences with \"You know what?\"',\r\n      'Use rhetorical questions naturally',\r\n      'Include conversational fillers like \"honestly\" or \"actually\"',\r\n      'Break up long thoughts with shorter sentences',\r\n      'Use contractions (we\\'re, don\\'t, can\\'t) naturally'\r\n    ],\r\n    storytelling_devices: [\r\n      'Start with \"I remember when...\" or \"Last week...\"',\r\n      'Use the \"But here\\'s the thing...\" transition',\r\n      'Include dialogue: \"My customer said...\"',\r\n      'Paint vivid scenes with sensory details',\r\n      'End with unexpected insights or realizations'\r\n    ],\r\n    emotional_connectors: [\r\n      'Share moments of doubt and breakthrough',\r\n      'Express genuine gratitude to customers',\r\n      'Show empathy for customer struggles',\r\n      'Celebrate small wins with enthusiasm',\r\n      'Use emotional language that resonates'\r\n    ],\r\n    imperfection_markers: [\r\n      'Occasional typos that feel natural (but not distracting)',\r\n      'Slightly informal grammar in casual contexts',\r\n      'Stream-of-consciousness moments',\r\n      'Self-corrections: \"Actually, let me rephrase that...\"',\r\n      'Honest admissions: \"I\\'m still figuring this out...\"'\r\n    ]\r\n  };\r\n}\r\n\r\n/**\r\n * Generates traffic-driving content elements\r\n */\r\nexport function generateTrafficDrivingElements(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string\r\n): TrafficDrivingElements {\r\n  \r\n  return {\r\n    viral_hooks: [\r\n      'Controversial but respectful opinions',\r\n      'Surprising industry statistics',\r\n      'Before/after transformations',\r\n      'Myth-busting content',\r\n      'Exclusive behind-the-scenes reveals',\r\n      'Timely reactions to trending topics',\r\n      'Unexpected collaborations or partnerships'\r\n    ],\r\n    engagement_magnets: [\r\n      'Fill-in-the-blank questions',\r\n      'This or that choices',\r\n      'Caption this photo challenges',\r\n      'Share your experience prompts',\r\n      'Prediction requests',\r\n      'Opinion polls and surveys',\r\n      'Challenge participation invites'\r\n    ],\r\n    conversion_triggers: [\r\n      'Limited-time offers with urgency',\r\n      'Exclusive access for followers',\r\n      'Free valuable resources',\r\n      'Personal consultation offers',\r\n      'Early bird opportunities',\r\n      'Member-only benefits',\r\n      'Referral incentives'\r\n    ],\r\n    shareability_factors: [\r\n      'Relatable everyday struggles',\r\n      'Inspirational success stories',\r\n      'Useful tips people want to save',\r\n      'Funny observations about the industry',\r\n      'Heartwarming customer stories',\r\n      'Educational content that teaches',\r\n      'Content that makes people look smart for sharing'\r\n    ],\r\n    curiosity_gaps: [\r\n      'The one thing nobody tells you about...',\r\n      'What happened next will surprise you...',\r\n      'The secret that changed everything...',\r\n      'Why everyone is wrong about...',\r\n      'The mistake I made that taught me...',\r\n      'What I wish I knew before...',\r\n      'The truth about... that nobody talks about'\r\n    ],\r\n    social_proof_elements: [\r\n      'Customer testimonials and reviews',\r\n      'User-generated content features',\r\n      'Industry recognition and awards',\r\n      'Media mentions and press coverage',\r\n      'Collaboration with respected figures',\r\n      'Community size and engagement stats',\r\n      'Success metrics and achievements'\r\n    ]\r\n  };\r\n}\r\n\r\n/**\r\n * Gets personality markers based on brand voice\r\n */\r\nfunction getPersonalityMarkers(brandVoice: string): string[] {\r\n  const voiceMap: Record<string, string[]> = {\r\n    'friendly': [\r\n      'Use warm, welcoming language',\r\n      'Include friendly greetings and sign-offs',\r\n      'Show genuine interest in followers',\r\n      'Use inclusive language that brings people together'\r\n    ],\r\n    'professional': [\r\n      'Maintain expertise while being approachable',\r\n      'Use industry knowledge to build authority',\r\n      'Balance formal tone with personal touches',\r\n      'Show competence through specific examples'\r\n    ],\r\n    'casual': [\r\n      'Use everyday language and slang appropriately',\r\n      'Be relaxed and conversational',\r\n      'Include humor and light-hearted moments',\r\n      'Feel like talking to a friend'\r\n    ],\r\n    'innovative': [\r\n      'Show forward-thinking perspectives',\r\n      'Challenge conventional wisdom respectfully',\r\n      'Share cutting-edge insights',\r\n      'Express excitement about new possibilities'\r\n    ]\r\n  };\r\n\r\n  // Extract key words from brand voice description\r\n  const lowerVoice = brandVoice.toLowerCase();\r\n  for (const [key, markers] of Object.entries(voiceMap)) {\r\n    if (lowerVoice.includes(key)) {\r\n      return markers;\r\n    }\r\n  }\r\n\r\n  return voiceMap['friendly']; // Default fallback\r\n}\r\n\r\n/**\r\n * Gets industry-specific authenticity elements\r\n */\r\nfunction getIndustryAuthenticity(businessType: string): string[] {\r\n  const industryMap: Record<string, string[]> = {\r\n    'restaurant': [\r\n      'Share cooking failures and successes',\r\n      'Talk about ingredient sourcing stories',\r\n      'Mention customer reactions and feedback',\r\n      'Describe the sensory experience of food'\r\n    ],\r\n    'fitness': [\r\n      'Share personal workout struggles',\r\n      'Admit to having off days',\r\n      'Celebrate client progress genuinely',\r\n      'Talk about the mental health benefits'\r\n    ],\r\n    'technology': [\r\n      'Explain complex concepts simply',\r\n      'Share debugging stories and solutions',\r\n      'Admit when technology isn\\'t perfect',\r\n      'Focus on human impact of technology'\r\n    ],\r\n    'financial technology software': [\r\n      'Share stories about financial inclusion impact',\r\n      'Explain complex financial concepts simply',\r\n      'Highlight real customer success stories',\r\n      'Address common financial fears and concerns',\r\n      'Show the human side of financial technology'\r\n    ],\r\n    'beauty': [\r\n      'Share makeup fails and learning moments',\r\n      'Talk about skin struggles and solutions',\r\n      'Celebrate diverse beauty standards',\r\n      'Share product testing experiences'\r\n    ]\r\n  };\r\n\r\n  return industryMap[businessType.toLowerCase()] || [\r\n    'Share real customer interactions',\r\n    'Talk about daily business challenges',\r\n    'Celebrate small business wins',\r\n    'Show the human side of your industry'\r\n  ];\r\n}\r\n\r\n/**\r\n * Gets location-specific conversational patterns\r\n */\r\nfunction getLocationConversation(location: string): string[] {\r\n  const locationMap: Record<string, string[]> = {\r\n    'nairobi': [\r\n      'Use occasional Swahili phrases naturally',\r\n      'Reference local landmarks and experiences',\r\n      'Include community-focused language',\r\n      'Show respect for local customs and values'\r\n    ],\r\n    'new york': [\r\n      'Use direct, fast-paced communication',\r\n      'Reference city experiences and culture',\r\n      'Include diverse perspectives',\r\n      'Show hustle and ambition'\r\n    ],\r\n    'london': [\r\n      'Use British expressions naturally',\r\n      'Include dry humor appropriately',\r\n      'Reference local culture and experiences',\r\n      'Maintain polite but direct communication'\r\n    ]\r\n  };\r\n\r\n  const locationKey = location.toLowerCase().split(',')[0].trim();\r\n  return locationMap[locationKey] || [\r\n    'Use local expressions and references',\r\n    'Include regional cultural touchpoints',\r\n    'Show understanding of local context',\r\n    'Connect with community values'\r\n  ];\r\n}\r\n\r\n/**\r\n * Generates content optimization strategies for maximum engagement\r\n */\r\nexport function generateContentOptimization(\r\n  platform: string,\r\n  businessType: string,\r\n  timeOfDay: string = 'morning'\r\n): {\r\n  posting_strategy: string[];\r\n  engagement_timing: string[];\r\n  content_mix: string[];\r\n  performance_indicators: string[];\r\n} {\r\n  \r\n  const platformStrategies: Record<string, any> = {\r\n    'instagram': {\r\n      posting_strategy: [\r\n        'Use high-quality visuals as primary hook',\r\n        'Write captions that encourage saves and shares',\r\n        'Include clear call-to-actions in stories',\r\n        'Use relevant hashtags strategically'\r\n      ],\r\n      engagement_timing: [\r\n        'Post when your audience is most active',\r\n        'Respond to comments within first hour',\r\n        'Use stories for real-time engagement',\r\n        'Go live during peak audience times'\r\n      ]\r\n    },\r\n    'linkedin': {\r\n      posting_strategy: [\r\n        'Lead with valuable insights or questions',\r\n        'Use professional but personal tone',\r\n        'Include industry-relevant hashtags',\r\n        'Share thought leadership content'\r\n      ],\r\n      engagement_timing: [\r\n        'Post during business hours for B2B',\r\n        'Engage with comments professionally',\r\n        'Share in relevant LinkedIn groups',\r\n        'Connect with commenters personally'\r\n      ]\r\n    },\r\n    'twitter': {\r\n      posting_strategy: [\r\n        'Use trending hashtags when relevant',\r\n        'Create tweetable quotes and insights',\r\n        'Engage in real-time conversations',\r\n        'Share quick tips and observations'\r\n      ],\r\n      engagement_timing: [\r\n        'Tweet during peak conversation times',\r\n        'Respond quickly to mentions',\r\n        'Join trending conversations',\r\n        'Retweet with thoughtful comments'\r\n      ]\r\n    },\r\n    'facebook': {\r\n      posting_strategy: [\r\n        'Create community-focused content',\r\n        'Use longer-form storytelling',\r\n        'Encourage group discussions',\r\n        'Share local community content'\r\n      ],\r\n      engagement_timing: [\r\n        'Post when your community is online',\r\n        'Respond to all comments personally',\r\n        'Share in relevant Facebook groups',\r\n        'Use Facebook events for promotion'\r\n      ]\r\n    }\r\n  };\r\n\r\n  const strategy = platformStrategies[platform.toLowerCase()] || platformStrategies['instagram'];\r\n  \r\n  return {\r\n    ...strategy,\r\n    content_mix: [\r\n      '60% educational/valuable content',\r\n      '20% behind-the-scenes/personal',\r\n      '15% promotional/business',\r\n      '5% trending/entertainment'\r\n    ],\r\n    performance_indicators: [\r\n      'Comments and meaningful engagement',\r\n      'Saves and shares over likes',\r\n      'Profile visits and follows',\r\n      'Website clicks and conversions',\r\n      'Direct messages and inquiries'\r\n    ]\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAuBM,SAAS,+BACd,YAAoB,EACpB,UAAkB,EAClB,QAAgB;IAGhB,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,uBAAuB,wBAAwB;IACrD,MAAM,uBAAuB,wBAAwB;IAErD,OAAO;QACL,qBAAqB;eAChB;YACH;YACA;YACA;YACA;SACD;QACD,uBAAuB;eAClB;YACH;YACA;YACA;YACA;YACA;SACD;QACD,yBAAyB;eACpB;YACH;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAKO,SAAS,+BACd,YAAoB,EACpB,QAAgB,EAChB,cAAuB;IAGvB,OAAO;QACL,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,sBAAsB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,uBAAuB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA;;CAEC,GACD,SAAS,sBAAsB,UAAkB;IAC/C,MAAM,WAAqC;QACzC,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;IACH;IAEA,iDAAiD;IACjD,MAAM,aAAa,WAAW,WAAW;IACzC,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAW;QACrD,IAAI,WAAW,QAAQ,CAAC,MAAM;YAC5B,OAAO;QACT;IACF;IAEA,OAAO,QAAQ,CAAC,WAAW,EAAE,mBAAmB;AAClD;AAEA;;CAEC,GACD,SAAS,wBAAwB,YAAoB;IACnD,MAAM,cAAwC;QAC5C,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iCAAiC;YAC/B;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,OAAO,WAAW,CAAC,aAAa,WAAW,GAAG,IAAI;QAChD;QACA;QACA;QACA;KACD;AACH;AAEA;;CAEC,GACD,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,cAAwC;QAC5C,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IAC7D,OAAO,WAAW,CAAC,YAAY,IAAI;QACjC;QACA;QACA;QACA;KACD;AACH;AAKO,SAAS,4BACd,QAAgB,EAChB,YAAoB,EACpB,YAAoB,SAAS;IAQ7B,MAAM,qBAA0C;QAC9C,aAAa;YACX,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,YAAY;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,WAAW;YACT,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;QACA,YAAY;YACV,kBAAkB;gBAChB;gBACA;gBACA;gBACA;aACD;YACD,mBAAmB;gBACjB;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,WAAW,kBAAkB,CAAC,SAAS,WAAW,GAAG,IAAI,kBAAkB,CAAC,YAAY;IAE9F,OAAO;QACL,GAAG,QAAQ;QACX,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,wBAAwB;YACtB;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 3392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-trends.ts"], "sourcesContent": ["/**\r\n * Design Trends Integration System\r\n * \r\n * Keeps design generation current with latest visual trends and best practices\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { z } from 'zod';\r\n\r\n// Schema for design trends analysis\r\nexport const DesignTrendsSchema = z.object({\r\n  currentTrends: z.array(z.object({\r\n    name: z.string().describe('Name of the design trend'),\r\n    description: z.string().describe('Description of the trend'),\r\n    applicability: z.enum(['high', 'medium', 'low']).describe('How applicable this trend is to the business type'),\r\n    implementation: z.string().describe('How to implement this trend in the design'),\r\n    examples: z.array(z.string()).describe('Visual examples or descriptions of the trend')\r\n  })).describe('Current relevant design trends'),\r\n  colorTrends: z.object({\r\n    palette: z.array(z.string()).describe('Trending color palette in hex format'),\r\n    mood: z.string().describe('Overall mood of trending colors'),\r\n    application: z.string().describe('How to apply these colors effectively')\r\n  }),\r\n  typographyTrends: z.object({\r\n    styles: z.array(z.string()).describe('Trending typography styles'),\r\n    pairings: z.array(z.string()).describe('Popular font pairings'),\r\n    treatments: z.array(z.string()).describe('Special text treatments and effects')\r\n  }),\r\n  layoutTrends: z.object({\r\n    compositions: z.array(z.string()).describe('Trending layout compositions'),\r\n    spacing: z.string().describe('Current spacing and whitespace trends'),\r\n    hierarchy: z.string().describe('Visual hierarchy trends')\r\n  }),\r\n  platformSpecific: z.object({\r\n    instagram: z.array(z.string()).describe('Instagram-specific design trends'),\r\n    facebook: z.array(z.string()).describe('Facebook-specific design trends'),\r\n    twitter: z.array(z.string()).describe('Twitter/X-specific design trends'),\r\n    linkedin: z.array(z.string()).describe('LinkedIn-specific design trends')\r\n  })\r\n});\r\n\r\nexport type DesignTrends = z.infer<typeof DesignTrendsSchema>;\r\n\r\n// Design trends analysis prompt\r\nconst designTrendsPrompt = ai.definePrompt({\r\n  name: 'analyzeDesignTrends',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      platform: z.string(),\r\n      targetAudience: z.string().optional(),\r\n      industry: z.string().optional()\r\n    })\r\n  },\r\n  output: {\r\n    schema: DesignTrendsSchema\r\n  },\r\n  prompt: `You are a leading design trend analyst with deep knowledge of current visual design trends, social media best practices, and industry-specific design patterns.\r\n\r\nAnalyze and provide current design trends relevant to:\r\n- Business Type: {{businessType}}\r\n- Platform: {{platform}}\r\n- Target Audience: {{targetAudience}}\r\n- Industry: {{industry}}\r\n\r\nFocus on trends that are:\r\n1. Currently popular and effective (2024-2025)\r\n2. Relevant to the specific business type and platform\r\n3. Proven to drive engagement and conversions\r\n4. Accessible and implementable in AI-generated designs\r\n\r\nProvide specific, actionable trend insights that can be directly applied to design generation.`\r\n});\r\n\r\n/**\r\n * Gets current design trends relevant to the business and platform\r\n */\r\nexport async function getCurrentDesignTrends(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string,\r\n  industry?: string\r\n): Promise<DesignTrends> {\r\n  try {\r\n    // For now, return fallback trends to avoid API issues\r\n    // This provides current, relevant trends while the system is being tested\r\n    return getFallbackTrends(businessType, platform);\r\n  } catch (error) {\r\n    // Return fallback trends\r\n    return getFallbackTrends(businessType, platform);\r\n  }\r\n}\r\n\r\n/**\r\n * Generates trend-aware design instructions\r\n */\r\nexport function generateTrendInstructions(trends: DesignTrends, platform: string): string {\r\n  const platformTrends = trends.platformSpecific[platform as keyof typeof trends.platformSpecific] || [];\r\n  const highApplicabilityTrends = trends.currentTrends.filter(t => t.applicability === 'high');\r\n\r\n  return `\r\n**CURRENT DESIGN TRENDS INTEGRATION:**\r\n\r\n**High-Priority Trends to Incorporate:**\r\n${highApplicabilityTrends.map(trend => `\r\n- **${trend.name}**: ${trend.description}\r\n  Implementation: ${trend.implementation}`).join('\\n')}\r\n\r\n**Color Trends:**\r\n- Trending Palette: ${trends.colorTrends.palette.join(', ')}\r\n- Mood: ${trends.colorTrends.mood}\r\n- Application: ${trends.colorTrends.application}\r\n\r\n**Typography Trends:**\r\n- Styles: ${trends.typographyTrends.styles.join(', ')}\r\n- Popular Pairings: ${trends.typographyTrends.pairings.join(', ')}\r\n- Special Treatments: ${trends.typographyTrends.treatments.join(', ')}\r\n\r\n**Layout Trends:**\r\n- Compositions: ${trends.layoutTrends.compositions.join(', ')}\r\n- Spacing: ${trends.layoutTrends.spacing}\r\n- Hierarchy: ${trends.layoutTrends.hierarchy}\r\n\r\n**Platform-Specific Trends (${platform}):**\r\n${platformTrends.map(trend => `- ${trend}`).join('\\n')}\r\n\r\n**TREND APPLICATION GUIDELINES:**\r\n- Incorporate 2-3 relevant trends maximum to avoid overwhelming the design\r\n- Ensure trends align with brand personality and business goals\r\n- Prioritize trends that enhance readability and user experience\r\n- Balance trendy elements with timeless design principles\r\n`;\r\n}\r\n\r\n/**\r\n * Fallback trends when API fails\r\n */\r\nfunction getFallbackTrends(businessType: string, platform: string): DesignTrends {\r\n  return {\r\n    currentTrends: [\r\n      {\r\n        name: \"Bold Typography\",\r\n        description: \"Large, impactful typography that commands attention\",\r\n        applicability: \"high\",\r\n        implementation: \"Use oversized headlines with strong contrast\",\r\n        examples: [\"Large sans-serif headers\", \"Bold statement text\", \"Typography as hero element\"]\r\n      },\r\n      {\r\n        name: \"Minimalist Design\",\r\n        description: \"Clean, uncluttered designs with plenty of white space\",\r\n        applicability: \"high\",\r\n        implementation: \"Focus on essential elements, generous spacing, simple color palette\",\r\n        examples: [\"Clean layouts\", \"Minimal color schemes\", \"Focused messaging\"]\r\n      },\r\n      {\r\n        name: \"Authentic Photography\",\r\n        description: \"Real, unposed photography over stock imagery\",\r\n        applicability: \"medium\",\r\n        implementation: \"Use candid, lifestyle photography that feels genuine\",\r\n        examples: [\"Behind-the-scenes shots\", \"Real customer photos\", \"Lifestyle imagery\"]\r\n      }\r\n    ],\r\n    colorTrends: {\r\n      palette: [\"#FF6B6B\", \"#4ECDC4\", \"#45B7D1\", \"#96CEB4\", \"#FFEAA7\"],\r\n      mood: \"Vibrant yet calming, optimistic and approachable\",\r\n      application: \"Use as accent colors against neutral backgrounds for maximum impact\"\r\n    },\r\n    typographyTrends: {\r\n      styles: [\"Bold sans-serif\", \"Modern serif\", \"Custom lettering\"],\r\n      pairings: [\"Bold header + clean body\", \"Serif headline + sans-serif body\"],\r\n      treatments: [\"Gradient text\", \"Outlined text\", \"Text with shadows\"]\r\n    },\r\n    layoutTrends: {\r\n      compositions: [\"Asymmetrical balance\", \"Grid-based layouts\", \"Centered focal points\"],\r\n      spacing: \"Generous white space with intentional breathing room\",\r\n      hierarchy: \"Clear size differentiation with strong contrast\"\r\n    },\r\n    platformSpecific: {\r\n      instagram: [\"Square and vertical formats\", \"Story-friendly designs\", \"Carousel-optimized layouts\"],\r\n      facebook: [\"Horizontal emphasis\", \"Video-first approach\", \"Community-focused messaging\"],\r\n      twitter: [\"High contrast for timeline\", \"Text-heavy designs\", \"Trending hashtag integration\"],\r\n      linkedin: [\"Professional aesthetics\", \"Data visualization\", \"Thought leadership focus\"]\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Caches trends to avoid excessive API calls\r\n * Reduced cache duration and added randomization to prevent repetitive designs\r\n */\r\nconst trendsCache = new Map<string, { trends: DesignTrends; timestamp: number; usageCount: number }>();\r\nconst CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours (reduced from 24 hours)\r\nconst MAX_USAGE_COUNT = 5; // Force refresh after 5 uses to add variety\r\n\r\nexport async function getCachedDesignTrends(\r\n  businessType: string,\r\n  platform: string,\r\n  targetAudience?: string,\r\n  industry?: string\r\n): Promise<DesignTrends> {\r\n  // Add randomization to cache key to create more variety\r\n  const hourOfDay = new Date().getHours();\r\n  const randomSeed = Math.floor(hourOfDay / 2); // Changes every 2 hours\r\n  const cacheKey = `${businessType}-${platform}-${targetAudience}-${industry}-${randomSeed}`;\r\n  const cached = trendsCache.get(cacheKey);\r\n\r\n  // Check if cache is valid and not overused\r\n  if (cached &&\r\n    Date.now() - cached.timestamp < CACHE_DURATION &&\r\n    cached.usageCount < MAX_USAGE_COUNT) {\r\n    cached.usageCount++;\r\n    return cached.trends;\r\n  }\r\n\r\n  const trends = await getCurrentDesignTrends(businessType, platform, targetAudience, industry);\r\n  trendsCache.set(cacheKey, { trends, timestamp: Date.now(), usageCount: 1 });\r\n\r\n  return trends;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AACA;;;AAGO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,eAAe,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC9B,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACjC,eAAe,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAM,EAAE,QAAQ,CAAC;QAC1D,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QACpC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACzC,IAAI,QAAQ,CAAC;IACb,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,SAAS,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACtC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC1B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC;IACA,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACrC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,YAAY,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC3C;IACA,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QAC3C,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;IACA,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACxC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,SAAS,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACtC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACzC;AACF;AAIA,gCAAgC;AAChC,MAAM,qBAAqB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACzC,MAAM;IACN,OAAO;QACL,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B;IACF;IACA,QAAQ;QACN,QAAQ;IACV;IACA,QAAQ,CAAC;;;;;;;;;;;;;;8FAcmF,CAAC;AAC/F;AAKO,eAAe,uBACpB,YAAoB,EACpB,QAAgB,EAChB,cAAuB,EACvB,QAAiB;IAEjB,IAAI;QACF,sDAAsD;QACtD,0EAA0E;QAC1E,OAAO,kBAAkB,cAAc;IACzC,EAAE,OAAO,OAAO;QACd,yBAAyB;QACzB,OAAO,kBAAkB,cAAc;IACzC;AACF;AAKO,SAAS,0BAA0B,MAAoB,EAAE,QAAgB;IAC9E,MAAM,iBAAiB,OAAO,gBAAgB,CAAC,SAAiD,IAAI,EAAE;IACtG,MAAM,0BAA0B,OAAO,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;IAErF,OAAO,CAAC;;;;AAIV,EAAE,wBAAwB,GAAG,CAAC,CAAA,QAAS,CAAC;IACpC,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,MAAM,WAAW,CAAC;kBACvB,EAAE,MAAM,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM;;;oBAGnC,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;QACpD,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC;eACnB,EAAE,OAAO,WAAW,CAAC,WAAW,CAAC;;;UAGtC,EAAE,OAAO,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;oBAClC,EAAE,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;sBAC5C,EAAE,OAAO,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;;;gBAGtD,EAAE,OAAO,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM;WACnD,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC;aAC5B,EAAE,OAAO,YAAY,CAAC,SAAS,CAAC;;4BAEjB,EAAE,SAAS;AACvC,EAAE,eAAe,GAAG,CAAC,CAAA,QAAS,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM;;;;;;;AAOvD,CAAC;AACD;AAEA;;CAEC,GACD,SAAS,kBAAkB,YAAoB,EAAE,QAAgB;IAC/D,OAAO;QACL,eAAe;YACb;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAA4B;oBAAuB;iBAA6B;YAC7F;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAAiB;oBAAyB;iBAAoB;YAC3E;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,UAAU;oBAAC;oBAA2B;oBAAwB;iBAAoB;YACpF;SACD;QACD,aAAa;YACX,SAAS;gBAAC;gBAAW;gBAAW;gBAAW;gBAAW;aAAU;YAChE,MAAM;YACN,aAAa;QACf;QACA,kBAAkB;YAChB,QAAQ;gBAAC;gBAAmB;gBAAgB;aAAmB;YAC/D,UAAU;gBAAC;gBAA4B;aAAmC;YAC1E,YAAY;gBAAC;gBAAiB;gBAAiB;aAAoB;QACrE;QACA,cAAc;YACZ,cAAc;gBAAC;gBAAwB;gBAAsB;aAAwB;YACrF,SAAS;YACT,WAAW;QACb;QACA,kBAAkB;YAChB,WAAW;gBAAC;gBAA+B;gBAA0B;aAA6B;YAClG,UAAU;gBAAC;gBAAuB;gBAAwB;aAA8B;YACxF,SAAS;gBAAC;gBAA8B;gBAAsB;aAA+B;YAC7F,UAAU;gBAAC;gBAA2B;gBAAsB;aAA2B;QACzF;IACF;AACF;AAEA;;;CAGC,GACD,MAAM,cAAc,IAAI;AACxB,MAAM,iBAAiB,IAAI,KAAK,KAAK,MAAM,kCAAkC;AAC7E,MAAM,kBAAkB,GAAG,4CAA4C;AAEhE,eAAe,sBACpB,YAAoB,EACpB,QAAgB,EAChB,cAAuB,EACvB,QAAiB;IAEjB,wDAAwD;IACxD,MAAM,YAAY,IAAI,OAAO,QAAQ;IACrC,MAAM,aAAa,KAAK,KAAK,CAAC,YAAY,IAAI,wBAAwB;IACtE,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,CAAC,EAAE,SAAS,CAAC,EAAE,YAAY;IAC1F,MAAM,SAAS,YAAY,GAAG,CAAC;IAE/B,2CAA2C;IAC3C,IAAI,UACF,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,kBAChC,OAAO,UAAU,GAAG,iBAAiB;QACrC,OAAO,UAAU;QACjB,OAAO,OAAO,MAAM;IACtB;IAEA,MAAM,SAAS,MAAM,uBAAuB,cAAc,UAAU,gBAAgB;IACpF,YAAY,GAAG,CAAC,UAAU;QAAE;QAAQ,WAAW,KAAK,GAAG;QAAI,YAAY;IAAE;IAEzE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/utils/design-analytics.ts"], "sourcesContent": ["/**\r\n * Design Performance Analytics System\r\n * \r\n * Tracks design performance, learns from successful patterns, and optimizes future generations\r\n */\r\n\r\nimport { z } from 'zod';\r\n\r\n// Schema for design performance metrics\r\nexport const DesignPerformanceSchema = z.object({\r\n  designId: z.string(),\r\n  businessType: z.string(),\r\n  platform: z.string(),\r\n  visualStyle: z.string(),\r\n  generatedAt: z.date(),\r\n  metrics: z.object({\r\n    qualityScore: z.number().min(1).max(10),\r\n    engagementPrediction: z.number().min(1).max(10),\r\n    brandAlignmentScore: z.number().min(1).max(10),\r\n    technicalQuality: z.number().min(1).max(10),\r\n    trendRelevance: z.number().min(1).max(10)\r\n  }),\r\n  designElements: z.object({\r\n    colorPalette: z.array(z.string()),\r\n    typography: z.string(),\r\n    composition: z.string(),\r\n    trends: z.array(z.string()),\r\n    businessDNA: z.string()\r\n  }),\r\n  performance: z.object({\r\n    actualEngagement: z.number().optional(),\r\n    clickThroughRate: z.number().optional(),\r\n    conversionRate: z.number().optional(),\r\n    brandRecall: z.number().optional(),\r\n    userFeedback: z.number().min(1).max(5).optional()\r\n  }).optional(),\r\n  improvements: z.array(z.string()).optional(),\r\n  tags: z.array(z.string()).optional()\r\n});\r\n\r\nexport type DesignPerformance = z.infer<typeof DesignPerformanceSchema>;\r\n\r\n// In-memory storage for design analytics (in production, use a database)\r\nconst designAnalytics: Map<string, DesignPerformance> = new Map();\r\nconst performancePatterns: Map<string, any> = new Map();\r\n\r\n/**\r\n * Records design generation and initial metrics\r\n */\r\nexport function recordDesignGeneration(\r\n  designId: string,\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  qualityScore: number,\r\n  designElements: {\r\n    colorPalette: string[];\r\n    typography: string;\r\n    composition: string;\r\n    trends: string[];\r\n    businessDNA: string;\r\n  },\r\n  predictions: {\r\n    engagement: number;\r\n    brandAlignment: number;\r\n    technicalQuality: number;\r\n    trendRelevance: number;\r\n  }\r\n): void {\r\n  const record: DesignPerformance = {\r\n    designId,\r\n    businessType,\r\n    platform,\r\n    visualStyle,\r\n    generatedAt: new Date(),\r\n    metrics: {\r\n      qualityScore,\r\n      engagementPrediction: predictions.engagement,\r\n      brandAlignmentScore: predictions.brandAlignment,\r\n      technicalQuality: predictions.technicalQuality,\r\n      trendRelevance: predictions.trendRelevance\r\n    },\r\n    designElements,\r\n    tags: [businessType, platform, visualStyle]\r\n  };\r\n\r\n  designAnalytics.set(designId, record);\r\n  updatePerformancePatterns(record);\r\n}\r\n\r\n/**\r\n * Updates design performance with actual metrics\r\n */\r\nexport function updateDesignPerformance(\r\n  designId: string,\r\n  actualMetrics: {\r\n    engagement?: number;\r\n    clickThroughRate?: number;\r\n    conversionRate?: number;\r\n    brandRecall?: number;\r\n    userFeedback?: number;\r\n  }\r\n): void {\r\n  const record = designAnalytics.get(designId);\r\n  if (!record) return;\r\n\r\n  record.performance = {\r\n    ...record.performance,\r\n    ...actualMetrics\r\n  };\r\n\r\n  designAnalytics.set(designId, record);\r\n  updatePerformancePatterns(record);\r\n}\r\n\r\n/**\r\n * Analyzes performance patterns to improve future designs\r\n */\r\nfunction updatePerformancePatterns(record: DesignPerformance): void {\r\n  const patternKey = `${record.businessType}-${record.platform}-${record.visualStyle}`;\r\n  \r\n  if (!performancePatterns.has(patternKey)) {\r\n    performancePatterns.set(patternKey, {\r\n      count: 0,\r\n      avgQuality: 0,\r\n      avgEngagement: 0,\r\n      successfulElements: new Map(),\r\n      commonIssues: new Map(),\r\n      bestPractices: []\r\n    });\r\n  }\r\n\r\n  const pattern = performancePatterns.get(patternKey);\r\n  pattern.count += 1;\r\n  \r\n  // Update averages\r\n  pattern.avgQuality = (pattern.avgQuality * (pattern.count - 1) + record.metrics.qualityScore) / pattern.count;\r\n  pattern.avgEngagement = (pattern.avgEngagement * (pattern.count - 1) + record.metrics.engagementPrediction) / pattern.count;\r\n\r\n  // Track successful elements\r\n  if (record.metrics.qualityScore >= 8) {\r\n    record.designElements.trends.forEach(trend => {\r\n      const count = pattern.successfulElements.get(trend) || 0;\r\n      pattern.successfulElements.set(trend, count + 1);\r\n    });\r\n  }\r\n\r\n  // Track common issues\r\n  if (record.improvements) {\r\n    record.improvements.forEach(issue => {\r\n      const count = pattern.commonIssues.get(issue) || 0;\r\n      pattern.commonIssues.set(issue, count + 1);\r\n    });\r\n  }\r\n\r\n  performancePatterns.set(patternKey, pattern);\r\n}\r\n\r\n/**\r\n * Gets performance insights for a specific business/platform combination\r\n */\r\nexport function getPerformanceInsights(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle?: string\r\n): {\r\n  averageQuality: number;\r\n  averageEngagement: number;\r\n  topSuccessfulElements: string[];\r\n  commonIssues: string[];\r\n  recommendations: string[];\r\n  sampleSize: number;\r\n} {\r\n  const patternKey = visualStyle \r\n    ? `${businessType}-${platform}-${visualStyle}`\r\n    : `${businessType}-${platform}`;\r\n  \r\n  const pattern = performancePatterns.get(patternKey);\r\n  \r\n  if (!pattern) {\r\n    return {\r\n      averageQuality: 0,\r\n      averageEngagement: 0,\r\n      topSuccessfulElements: [],\r\n      commonIssues: [],\r\n      recommendations: ['Insufficient data for insights'],\r\n      sampleSize: 0\r\n    };\r\n  }\r\n\r\n  // Get top successful elements\r\n  const topElements = Array.from(pattern.successfulElements.entries())\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([element]) => element);\r\n\r\n  // Get common issues\r\n  const topIssues = Array.from(pattern.commonIssues.entries())\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 3)\r\n    .map(([issue]) => issue);\r\n\r\n  // Generate recommendations\r\n  const recommendations = generateRecommendations(pattern, topElements, topIssues);\r\n\r\n  return {\r\n    averageQuality: Math.round(pattern.avgQuality * 10) / 10,\r\n    averageEngagement: Math.round(pattern.avgEngagement * 10) / 10,\r\n    topSuccessfulElements: topElements,\r\n    commonIssues: topIssues,\r\n    recommendations,\r\n    sampleSize: pattern.count\r\n  };\r\n}\r\n\r\n/**\r\n * Generates actionable recommendations based on performance data\r\n */\r\nfunction generateRecommendations(\r\n  pattern: any,\r\n  successfulElements: string[],\r\n  commonIssues: string[]\r\n): string[] {\r\n  const recommendations: string[] = [];\r\n\r\n  // Quality-based recommendations\r\n  if (pattern.avgQuality < 7) {\r\n    recommendations.push('Focus on improving overall design quality through better composition and typography');\r\n  }\r\n\r\n  // Engagement-based recommendations\r\n  if (pattern.avgEngagement < 7) {\r\n    recommendations.push('Incorporate more attention-grabbing elements and bold visual choices');\r\n  }\r\n\r\n  // Element-based recommendations\r\n  if (successfulElements.length > 0) {\r\n    recommendations.push(`Continue using successful elements: ${successfulElements.slice(0, 3).join(', ')}`);\r\n  }\r\n\r\n  // Issue-based recommendations\r\n  if (commonIssues.length > 0) {\r\n    recommendations.push(`Address common issues: ${commonIssues.slice(0, 2).join(', ')}`);\r\n  }\r\n\r\n  // Sample size recommendations\r\n  if (pattern.count < 10) {\r\n    recommendations.push('Generate more designs to improve insights accuracy');\r\n  }\r\n\r\n  return recommendations;\r\n}\r\n\r\n/**\r\n * Gets top performing designs for learning\r\n */\r\nexport function getTopPerformingDesigns(\r\n  businessType?: string,\r\n  platform?: string,\r\n  limit: number = 10\r\n): DesignPerformance[] {\r\n  let designs = Array.from(designAnalytics.values());\r\n\r\n  // Filter by business type and platform if specified\r\n  if (businessType) {\r\n    designs = designs.filter(d => d.businessType === businessType);\r\n  }\r\n  if (platform) {\r\n    designs = designs.filter(d => d.platform === platform);\r\n  }\r\n\r\n  // Sort by quality score and engagement prediction\r\n  designs.sort((a, b) => {\r\n    const scoreA = (a.metrics.qualityScore + a.metrics.engagementPrediction) / 2;\r\n    const scoreB = (b.metrics.qualityScore + b.metrics.engagementPrediction) / 2;\r\n    return scoreB - scoreA;\r\n  });\r\n\r\n  return designs.slice(0, limit);\r\n}\r\n\r\n/**\r\n * Generates performance-optimized design instructions\r\n */\r\nexport function generatePerformanceOptimizedInstructions(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string\r\n): string {\r\n  const insights = getPerformanceInsights(businessType, platform, visualStyle);\r\n  \r\n  if (insights.sampleSize === 0) {\r\n    return ''; // No data available\r\n  }\r\n\r\n  let instructions = `\\n**PERFORMANCE-OPTIMIZED DESIGN INSTRUCTIONS:**\\n`;\r\n  \r\n  if (insights.topSuccessfulElements.length > 0) {\r\n    instructions += `**Proven Successful Elements (${insights.sampleSize} designs analyzed):**\\n`;\r\n    insights.topSuccessfulElements.forEach(element => {\r\n      instructions += `- Incorporate: ${element}\\n`;\r\n    });\r\n  }\r\n\r\n  if (insights.commonIssues.length > 0) {\r\n    instructions += `\\n**Avoid Common Issues:**\\n`;\r\n    insights.commonIssues.forEach(issue => {\r\n      instructions += `- Prevent: ${issue}\\n`;\r\n    });\r\n  }\r\n\r\n  if (insights.recommendations.length > 0) {\r\n    instructions += `\\n**Performance Recommendations:**\\n`;\r\n    insights.recommendations.forEach(rec => {\r\n      instructions += `- ${rec}\\n`;\r\n    });\r\n  }\r\n\r\n  instructions += `\\n**Performance Benchmarks:**\\n`;\r\n  instructions += `- Target Quality Score: ${Math.max(insights.averageQuality + 0.5, 8)}/10\\n`;\r\n  instructions += `- Target Engagement: ${Math.max(insights.averageEngagement + 0.5, 8)}/10\\n`;\r\n\r\n  return instructions;\r\n}\r\n\r\n/**\r\n * Exports analytics data for external analysis\r\n */\r\nexport function exportAnalyticsData(): {\r\n  designs: DesignPerformance[];\r\n  patterns: Array<{ key: string; data: any }>;\r\n  summary: {\r\n    totalDesigns: number;\r\n    averageQuality: number;\r\n    topBusinessTypes: string[];\r\n    topPlatforms: string[];\r\n  };\r\n} {\r\n  const designs = Array.from(designAnalytics.values());\r\n  const patterns = Array.from(performancePatterns.entries()).map(([key, data]) => ({ key, data }));\r\n\r\n  // Calculate summary statistics\r\n  const totalDesigns = designs.length;\r\n  const averageQuality = designs.reduce((sum, d) => sum + d.metrics.qualityScore, 0) / totalDesigns;\r\n  \r\n  const businessTypeCounts = designs.reduce((acc, d) => {\r\n    acc[d.businessType] = (acc[d.businessType] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n  \r\n  const platformCounts = designs.reduce((acc, d) => {\r\n    acc[d.platform] = (acc[d.platform] || 0) + 1;\r\n    return acc;\r\n  }, {} as Record<string, number>);\r\n\r\n  const topBusinessTypes = Object.entries(businessTypeCounts)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([type]) => type);\r\n\r\n  const topPlatforms = Object.entries(platformCounts)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 5)\r\n    .map(([platform]) => platform);\r\n\r\n  return {\r\n    designs,\r\n    patterns,\r\n    summary: {\r\n      totalDesigns,\r\n      averageQuality: Math.round(averageQuality * 10) / 10,\r\n      topBusinessTypes,\r\n      topPlatforms\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;AAED;;AAGO,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;IACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;IAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACrB,aAAa,oIAAA,CAAA,IAAC,CAAC,IAAI;IACnB,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACpC,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC5C,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC3C,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACxC;IACA,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QAC9B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM;QACpB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;QACrB,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QACxB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACvB;IACA,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IACjD,GAAG,QAAQ;IACX,cAAc,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAC1C,MAAM,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACpC;AAIA,yEAAyE;AACzE,MAAM,kBAAkD,IAAI;AAC5D,MAAM,sBAAwC,IAAI;AAK3C,SAAS,uBACd,QAAgB,EAChB,YAAoB,EACpB,QAAgB,EAChB,WAAmB,EACnB,YAAoB,EACpB,cAMC,EACD,WAKC;IAED,MAAM,SAA4B;QAChC;QACA;QACA;QACA;QACA,aAAa,IAAI;QACjB,SAAS;YACP;YACA,sBAAsB,YAAY,UAAU;YAC5C,qBAAqB,YAAY,cAAc;YAC/C,kBAAkB,YAAY,gBAAgB;YAC9C,gBAAgB,YAAY,cAAc;QAC5C;QACA;QACA,MAAM;YAAC;YAAc;YAAU;SAAY;IAC7C;IAEA,gBAAgB,GAAG,CAAC,UAAU;IAC9B,0BAA0B;AAC5B;AAKO,SAAS,wBACd,QAAgB,EAChB,aAMC;IAED,MAAM,SAAS,gBAAgB,GAAG,CAAC;IACnC,IAAI,CAAC,QAAQ;IAEb,OAAO,WAAW,GAAG;QACnB,GAAG,OAAO,WAAW;QACrB,GAAG,aAAa;IAClB;IAEA,gBAAgB,GAAG,CAAC,UAAU;IAC9B,0BAA0B;AAC5B;AAEA;;CAEC,GACD,SAAS,0BAA0B,MAAyB;IAC1D,MAAM,aAAa,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,OAAO,WAAW,EAAE;IAEpF,IAAI,CAAC,oBAAoB,GAAG,CAAC,aAAa;QACxC,oBAAoB,GAAG,CAAC,YAAY;YAClC,OAAO;YACP,YAAY;YACZ,eAAe;YACf,oBAAoB,IAAI;YACxB,cAAc,IAAI;YAClB,eAAe,EAAE;QACnB;IACF;IAEA,MAAM,UAAU,oBAAoB,GAAG,CAAC;IACxC,QAAQ,KAAK,IAAI;IAEjB,kBAAkB;IAClB,QAAQ,UAAU,GAAG,CAAC,QAAQ,UAAU,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,YAAY,IAAI,QAAQ,KAAK;IAC7G,QAAQ,aAAa,GAAG,CAAC,QAAQ,aAAa,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,oBAAoB,IAAI,QAAQ,KAAK;IAE3H,4BAA4B;IAC5B,IAAI,OAAO,OAAO,CAAC,YAAY,IAAI,GAAG;QACpC,OAAO,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YACnC,MAAM,QAAQ,QAAQ,kBAAkB,CAAC,GAAG,CAAC,UAAU;YACvD,QAAQ,kBAAkB,CAAC,GAAG,CAAC,OAAO,QAAQ;QAChD;IACF;IAEA,sBAAsB;IACtB,IAAI,OAAO,YAAY,EAAE;QACvB,OAAO,YAAY,CAAC,OAAO,CAAC,CAAA;YAC1B,MAAM,QAAQ,QAAQ,YAAY,CAAC,GAAG,CAAC,UAAU;YACjD,QAAQ,YAAY,CAAC,GAAG,CAAC,OAAO,QAAQ;QAC1C;IACF;IAEA,oBAAoB,GAAG,CAAC,YAAY;AACtC;AAKO,SAAS,uBACd,YAAoB,EACpB,QAAgB,EAChB,WAAoB;IASpB,MAAM,aAAa,cACf,GAAG,aAAa,CAAC,EAAE,SAAS,CAAC,EAAE,aAAa,GAC5C,GAAG,aAAa,CAAC,EAAE,UAAU;IAEjC,MAAM,UAAU,oBAAoB,GAAG,CAAC;IAExC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,gBAAgB;YAChB,mBAAmB;YACnB,uBAAuB,EAAE;YACzB,cAAc,EAAE;YAChB,iBAAiB;gBAAC;aAAiC;YACnD,YAAY;QACd;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,MAAM,IAAI,CAAC,QAAQ,kBAAkB,CAAC,OAAO,IAC9D,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAK;IAEtB,oBAAoB;IACpB,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,YAAY,CAAC,OAAO,IACtD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK;IAEpB,2BAA2B;IAC3B,MAAM,kBAAkB,wBAAwB,SAAS,aAAa;IAEtE,OAAO;QACL,gBAAgB,KAAK,KAAK,CAAC,QAAQ,UAAU,GAAG,MAAM;QACtD,mBAAmB,KAAK,KAAK,CAAC,QAAQ,aAAa,GAAG,MAAM;QAC5D,uBAAuB;QACvB,cAAc;QACd;QACA,YAAY,QAAQ,KAAK;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,wBACP,OAAY,EACZ,kBAA4B,EAC5B,YAAsB;IAEtB,MAAM,kBAA4B,EAAE;IAEpC,gCAAgC;IAChC,IAAI,QAAQ,UAAU,GAAG,GAAG;QAC1B,gBAAgB,IAAI,CAAC;IACvB;IAEA,mCAAmC;IACnC,IAAI,QAAQ,aAAa,GAAG,GAAG;QAC7B,gBAAgB,IAAI,CAAC;IACvB;IAEA,gCAAgC;IAChC,IAAI,mBAAmB,MAAM,GAAG,GAAG;QACjC,gBAAgB,IAAI,CAAC,CAAC,oCAAoC,EAAE,mBAAmB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACzG;IAEA,8BAA8B;IAC9B,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,gBAAgB,IAAI,CAAC,CAAC,uBAAuB,EAAE,aAAa,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACtF;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,KAAK,GAAG,IAAI;QACtB,gBAAgB,IAAI,CAAC;IACvB;IAEA,OAAO;AACT;AAKO,SAAS,wBACd,YAAqB,EACrB,QAAiB,EACjB,QAAgB,EAAE;IAElB,IAAI,UAAU,MAAM,IAAI,CAAC,gBAAgB,MAAM;IAE/C,oDAAoD;IACpD,IAAI,cAAc;QAChB,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;IACnD;IACA,IAAI,UAAU;QACZ,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAC/C;IAEA,kDAAkD;IAClD,QAAQ,IAAI,CAAC,CAAC,GAAG;QACf,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,YAAY,GAAG,EAAE,OAAO,CAAC,oBAAoB,IAAI;QAC3E,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,YAAY,GAAG,EAAE,OAAO,CAAC,oBAAoB,IAAI;QAC3E,OAAO,SAAS;IAClB;IAEA,OAAO,QAAQ,KAAK,CAAC,GAAG;AAC1B;AAKO,SAAS,yCACd,YAAoB,EACpB,QAAgB,EAChB,WAAmB;IAEnB,MAAM,WAAW,uBAAuB,cAAc,UAAU;IAEhE,IAAI,SAAS,UAAU,KAAK,GAAG;QAC7B,OAAO,IAAI,oBAAoB;IACjC;IAEA,IAAI,eAAe,CAAC,kDAAkD,CAAC;IAEvE,IAAI,SAAS,qBAAqB,CAAC,MAAM,GAAG,GAAG;QAC7C,gBAAgB,CAAC,8BAA8B,EAAE,SAAS,UAAU,CAAC,uBAAuB,CAAC;QAC7F,SAAS,qBAAqB,CAAC,OAAO,CAAC,CAAA;YACrC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC;QAC/C;IACF;IAEA,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,GAAG;QACpC,gBAAgB,CAAC,4BAA4B,CAAC;QAC9C,SAAS,YAAY,CAAC,OAAO,CAAC,CAAA;YAC5B,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;QACzC;IACF;IAEA,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,GAAG;QACvC,gBAAgB,CAAC,oCAAoC,CAAC;QACtD,SAAS,eAAe,CAAC,OAAO,CAAC,CAAA;YAC/B,gBAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;QAC9B;IACF;IAEA,gBAAgB,CAAC,+BAA+B,CAAC;IACjD,gBAAgB,CAAC,wBAAwB,EAAE,KAAK,GAAG,CAAC,SAAS,cAAc,GAAG,KAAK,GAAG,KAAK,CAAC;IAC5F,gBAAgB,CAAC,qBAAqB,EAAE,KAAK,GAAG,CAAC,SAAS,iBAAiB,GAAG,KAAK,GAAG,KAAK,CAAC;IAE5F,OAAO;AACT;AAKO,SAAS;IAUd,MAAM,UAAU,MAAM,IAAI,CAAC,gBAAgB,MAAM;IACjD,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAK,CAAC;YAAE;YAAK;QAAK,CAAC;IAE9F,+BAA+B;IAC/B,MAAM,eAAe,QAAQ,MAAM;IACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,KAAK;IAErF,MAAM,qBAAqB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC9C,GAAG,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC1C,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;QAC3C,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,mBAAmB,OAAO,OAAO,CAAC,oBACrC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IAEnB,MAAM,eAAe,OAAO,OAAO,CAAC,gBACjC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,SAAS,GAAK;IAEvB,OAAO;QACL;QACA;QACA,SAAS;YACP;YACA,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,MAAM;YAClD;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/flows/generate-post-from-profile.ts"], "sourcesContent": ["\r\n'use server';\r\n\r\n/**\r\n * @fileOverview This file defines a Genkit flow for generating a daily social media post.\r\n *\r\n * It takes into account business type, location, brand voice, current weather, and local events to create engaging content.\r\n * @exports generatePostFromProfile - The main function to generate a post.\r\n * @exports GeneratePostFromProfileInput - The input type for the generation flow.\r\n * @exports GeneratePostFromProfileOutput - The output type for the generation flow.\r\n */\r\n\r\nimport { ai } from '@/ai/genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport { z } from 'zod';\r\nimport { getWeatherTool, getEventsTool } from '@/ai/tools/local-data';\r\nimport { getEnhancedEventsTool, getEnhancedWeatherTool } from '@/ai/tools/enhanced-local-data';\r\nimport { ENHANCED_CAPTION_PROMPT, PLATFORM_SPECIFIC_OPTIMIZATIONS } from '@/ai/prompts/enhanced-caption-prompt';\r\nimport { ADVANCED_AI_PROMPT } from '@/ai/prompts/advanced-ai-prompt';\r\nimport { viralHashtagEngine } from '@/ai/viral-hashtag-engine';\r\nimport { generateMarketIntelligence, generateRealTimeTrendingTopics } from '@/ai/utils/trending-topics';\r\nimport { fetchLocalContext } from '@/ai/utils/real-time-trends-integration';\r\nimport { selectRelevantContext, filterContextData } from '@/ai/utils/intelligent-context-selector';\r\nimport { generateHumanizationTechniques, generateTrafficDrivingElements } from '@/ai/utils/human-content-generator';\r\nimport {\r\n  ADVANCED_DESIGN_PRINCIPLES,\r\n  PLATFORM_SPECIFIC_GUIDELINES,\r\n  BUSINESS_TYPE_DESIGN_DNA,\r\n  QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from '@/ai/prompts/advanced-design-prompts';\r\n// Clean design system implemented inline for immediate use\r\n\r\n// 7 Different Design Template Styles for Variety - STRONG VISUAL DIFFERENTIATION\r\nconst DESIGN_TEMPLATES = [\r\n  {\r\n    name: \"Motivational Quote\",\r\n    style: \"WATERCOLOR BACKGROUND - NO ILLUSTRATIONS\",\r\n    description: \"MANDATORY: Soft watercolor wash background in pastels (pink/blue/purple). NO illustrations, NO graphics, NO icons. Only elegant typography on watercolor texture.\",\r\n    elements: [\"watercolor texture background\", \"script font headlines\", \"minimal text-only design\"],\r\n    forbidden: [\"illustrations\", \"graphics\", \"icons\", \"people\", \"objects\", \"geometric shapes\"]\r\n  },\r\n  {\r\n    name: \"Behind the Brand\",\r\n    style: \"CUSTOM ILLUSTRATIONS ONLY\",\r\n    description: \"MANDATORY: Hand-drawn style illustrations of business elements. Illustrated style with warm storytelling visuals.\",\r\n    elements: [\"custom illustrations\", \"hand-drawn style\", \"storytelling visuals\"],\r\n    forbidden: [\"photos\", \"watercolor\", \"geometric shapes\", \"minimal design\"]\r\n  },\r\n  {\r\n    name: \"Engagement Post\",\r\n    style: \"SPLIT PHOTO COLLAGE - NO ILLUSTRATIONS\",\r\n    description: \"MANDATORY: Split screen layout with real photos on each side. 'This or That' style with bold text overlay. NO illustrations allowed.\",\r\n    elements: [\"split screen layout\", \"real photographs\", \"comparison design\", \"bold overlay text\"],\r\n    forbidden: [\"illustrations\", \"watercolor\", \"single image\", \"minimal design\"]\r\n  },\r\n  {\r\n    name: \"Promotional Highlight\",\r\n    style: \"BOLD TYPOGRAPHY FOCUS - MINIMAL GRAPHICS\",\r\n    description: \"MANDATORY: Typography-driven design with clean background. Focus on text hierarchy and product showcase. Minimal geometric accents only.\",\r\n    elements: [\"large typography\", \"text hierarchy\", \"clean background\", \"minimal geometric accents\"],\r\n    forbidden: [\"illustrations\", \"watercolor\", \"complex graphics\", \"busy backgrounds\"]\r\n  },\r\n  {\r\n    name: \"Fun/Trending\",\r\n    style: \"MEME TEMPLATE - WHITE BACKGROUND\",\r\n    description: \"MANDATORY: Clean white background with meme-style text placement. Simple, humorous layout with minimal visual elements.\",\r\n    elements: [\"white background\", \"meme-style text\", \"simple layout\", \"humorous approach\"],\r\n    forbidden: [\"illustrations\", \"watercolor\", \"complex graphics\", \"busy designs\", \"multiple colors\"]\r\n  },\r\n  {\r\n    name: \"Customer Love\",\r\n    style: \"POLAROID FRAME - RETRO PHOTO STYLE\",\r\n    description: \"MANDATORY: Polaroid photo frame design with retro styling. Testimonial presentation with vintage photo aesthetic.\",\r\n    elements: [\"polaroid frame\", \"retro photo style\", \"vintage aesthetic\", \"testimonial layout\"],\r\n    forbidden: [\"illustrations\", \"watercolor\", \"modern design\", \"minimal layout\"]\r\n  },\r\n  {\r\n    name: \"Creativity + Brand Values\",\r\n    style: \"MIXED MEDIA ARTISTIC - WATERCOLOR + ELEMENTS\",\r\n    description: \"MANDATORY: Watercolor splash background combined with artistic mixed media elements. Creative inspiration with artistic flair.\",\r\n    elements: [\"watercolor splash\", \"mixed media\", \"artistic elements\", \"creative inspiration\"],\r\n    forbidden: [\"pure illustrations\", \"clean minimal\", \"geometric only\", \"photo-based\"]\r\n  }\r\n];\r\n// Enhanced design system temporarily disabled - will be re-enabled after module resolution\r\n// import { generateEnhancedDesignPrompt, generateDesignEnhancements, validateDesignQuality } from '@/ai/utils/enhanced-design-generator';\r\nimport {\r\n  analyzeDesignExample,\r\n  selectOptimalDesignExamples,\r\n  extractDesignDNA,\r\n  type DesignAnalysis\r\n} from '@/ai/utils/design-analysis';\r\nimport {\r\n  assessDesignQuality,\r\n  generateImprovementPrompt,\r\n  meetsQualityStandards,\r\n  type DesignQuality\r\n} from '@/ai/utils/design-quality';\r\nimport {\r\n  getCachedDesignTrends,\r\n  generateTrendInstructions,\r\n  type DesignTrends\r\n} from '@/ai/utils/design-trends';\r\nimport {\r\n  recordDesignGeneration,\r\n  generatePerformanceOptimizedInstructions\r\n} from '@/ai/utils/design-analytics';\r\n\r\nconst GeneratePostFromProfileInputSchema = z.object({\r\n  businessType: z.string().describe('The type of business (e.g., restaurant, salon).'),\r\n  location: z.string().describe('The location of the business (city, state).'),\r\n  visualStyle: z.string().describe('The visual style of the brand (e.g., modern, vintage).'),\r\n  writingTone: z.string().describe('The brand voice of the business.'),\r\n  contentThemes: z.string().describe('The content themes of the business.'),\r\n  logoDataUrl: z.string().describe(\"The business logo as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'.\"),\r\n  designExamples: z.array(z.string()).optional().describe(\"Array of design example data URIs to use as style reference for generating similar designs.\"),\r\n  dayOfWeek: z.string().describe('The day of the week for the post.'),\r\n  currentDate: z.string().describe('The current date for the post.'),\r\n  variants: z.array(z.object({\r\n    platform: z.string(),\r\n    aspectRatio: z.string(),\r\n  })).describe('An array of platform and aspect ratio variants to generate.'),\r\n  primaryColor: z.string().optional().describe('The primary brand color in HSL format.'),\r\n  accentColor: z.string().optional().describe('The accent brand color in HSL format.'),\r\n  backgroundColor: z.string().optional().describe('The background brand color in HSL format.'),\r\n\r\n  // New detailed fields for richer content\r\n  services: z.string().optional().describe('A newline-separated list of key services or products.'),\r\n  targetAudience: z.string().optional().describe('A description of the target audience.'),\r\n  keyFeatures: z.string().optional().describe('A newline-separated list of key features or selling points.'),\r\n  competitiveAdvantages: z.string().optional().describe('A newline-separated list of competitive advantages.'),\r\n\r\n  // Brand consistency preferences\r\n  brandConsistency: z.object({\r\n    strictConsistency: z.boolean().optional(),\r\n    followBrandColors: z.boolean().optional(),\r\n  }).optional().describe('Brand consistency preferences for content generation.'),\r\n\r\n  // Enhanced brand context\r\n  websiteUrl: z.string().optional().describe('The business website URL for additional context.'),\r\n  description: z.string().optional().describe('Detailed business description for better content context.'),\r\n  contactInfo: z.object({\r\n    phone: z.string().optional(),\r\n    email: z.string().optional(),\r\n    address: z.string().optional(),\r\n  }).optional().describe('Contact information for business context.'),\r\n  socialMedia: z.object({\r\n    facebook: z.string().optional(),\r\n    instagram: z.string().optional(),\r\n    twitter: z.string().optional(),\r\n    linkedin: z.string().optional(),\r\n  }).optional().describe('Social media handles for cross-platform consistency.'),\r\n\r\n  // Language preferences\r\n  useLocalLanguage: z.boolean().optional().describe('Whether to use local language in content generation (default: false).'),\r\n});\r\n\r\nexport type GeneratePostFromProfileInput = z.infer<typeof GeneratePostFromProfileInputSchema>;\r\n\r\nconst GeneratePostFromProfileOutputSchema = z.object({\r\n  content: z.string().describe('The primary generated social media post content (the caption).'),\r\n  catchyWords: z.string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),\r\n  subheadline: z.string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),\r\n  callToAction: z.string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),\r\n  hashtags: z.string().describe('Strategically selected hashtags for the post.'),\r\n  contentVariants: z.array(z.object({\r\n    content: z.string().describe('Alternative caption variant.'),\r\n    approach: z.string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),\r\n    rationale: z.string().describe('Why this variant might perform well.')\r\n  })).optional().describe('Alternative caption variants for A/B testing.'),\r\n  hashtagAnalysis: z.object({\r\n    trending: z.array(z.string()).describe('Trending hashtags for reach.'),\r\n    niche: z.array(z.string()).describe('Industry-specific hashtags.'),\r\n    location: z.array(z.string()).describe('Location-based hashtags.'),\r\n    community: z.array(z.string()).describe('Community engagement hashtags.')\r\n  }).optional().describe('Categorized hashtag strategy.'),\r\n  marketIntelligence: z.object({\r\n    trending_topics: z.array(z.object({\r\n      topic: z.string(),\r\n      relevanceScore: z.number(),\r\n      category: z.string(),\r\n      engagement_potential: z.string()\r\n    })).describe('Current trending topics relevant to the business.'),\r\n    competitor_insights: z.array(z.object({\r\n      competitor_name: z.string(),\r\n      content_gap: z.string(),\r\n      differentiation_opportunity: z.string()\r\n    })).describe('Competitor analysis and differentiation opportunities.'),\r\n    cultural_context: z.object({\r\n      location: z.string(),\r\n      cultural_nuances: z.array(z.string()),\r\n      local_customs: z.array(z.string())\r\n    }).describe('Cultural and location-specific context.'),\r\n    viral_patterns: z.array(z.string()).describe('Content patterns that drive viral engagement.'),\r\n    engagement_triggers: z.array(z.string()).describe('Psychological triggers for maximum engagement.')\r\n  }).optional().describe('Advanced market intelligence and optimization data.'),\r\n  localContext: z.object({\r\n    weather: z.object({\r\n      temperature: z.number(),\r\n      condition: z.string(),\r\n      business_impact: z.string(),\r\n      content_opportunities: z.array(z.string())\r\n    }).optional().describe('Current weather context and business opportunities.'),\r\n    events: z.array(z.object({\r\n      name: z.string(),\r\n      category: z.string(),\r\n      relevance_score: z.number(),\r\n      start_date: z.string()\r\n    })).optional().describe('Relevant local events for content integration.')\r\n  }).optional().describe('Local context including weather and events.'),\r\n  variants: z.array(z.object({\r\n    platform: z.string(),\r\n    imageUrl: z.string(),\r\n  })),\r\n});\r\n\r\nexport type GeneratePostFromProfileOutput = z.infer<typeof GeneratePostFromProfileOutputSchema>;\r\n\r\n// Export function moved to end of file after flow definition\r\n\r\n\r\n/**\r\n * Combines catchy words, subheadline, and call to action into a single text for image overlay\r\n */\r\nfunction combineTextComponents(catchyWords: string, subheadline?: string, callToAction?: string): string {\r\n  const components = [catchyWords];\r\n\r\n  if (subheadline && subheadline.trim()) {\r\n    components.push(subheadline.trim());\r\n  }\r\n\r\n  if (callToAction && callToAction.trim()) {\r\n    components.push(callToAction.trim());\r\n  }\r\n\r\n  return components.join('\\n');\r\n}\r\n\r\n// Define the enhanced text generation prompt\r\nconst enhancedTextGenPrompt = ai.definePrompt({\r\n  name: 'enhancedGeneratePostTextPrompt',\r\n  input: {\r\n    schema: z.object({\r\n      businessType: z.string(),\r\n      location: z.string(),\r\n      writingTone: z.string(),\r\n      contentThemes: z.string(),\r\n      dayOfWeek: z.string(),\r\n      currentDate: z.string(),\r\n      platform: z.string().optional(),\r\n      services: z.string().optional(),\r\n      targetAudience: z.string().optional(),\r\n      keyFeatures: z.string().optional(),\r\n      competitiveAdvantages: z.string().optional(),\r\n      contentVariation: z.string().optional(),\r\n      contextInstructions: z.string().optional(),\r\n      selectedWeather: z.any().optional(),\r\n      selectedEvents: z.any().optional(),\r\n      selectedTrends: z.any().optional(),\r\n      selectedCultural: z.any().optional(),\r\n      useLocalLanguage: z.boolean().optional(),\r\n    })\r\n  },\r\n  output: {\r\n    schema: z.object({\r\n      content: z.string().describe('The primary generated social media post content (the caption).'),\r\n      catchyWords: z.string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),\r\n      subheadline: z.string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),\r\n      callToAction: z.string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),\r\n      hashtags: z.string().describe('Strategically selected hashtags for the post.'),\r\n      contentVariants: z.array(z.object({\r\n        content: z.string().describe('Alternative caption variant.'),\r\n        approach: z.string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),\r\n        rationale: z.string().describe('Why this variant might perform well.')\r\n      })).describe('2-3 alternative caption variants for A/B testing.'),\r\n    })\r\n  },\r\n  tools: [getWeatherTool, getEventsTool, getEnhancedWeatherTool, getEnhancedEventsTool],\r\n  prompt: ADVANCED_AI_PROMPT,\r\n});\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n  for (let i = 0; i < retries; i++) {\r\n    try {\r\n      const result = await ai.generate(request);\r\n      return result;\r\n    } catch (e: any) {\r\n      if (e.message && e.message.includes('503') && i < retries - 1) {\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n      } else {\r\n        if (e.message && e.message.includes('503')) {\r\n          throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n        }\r\n        if (e.message && e.message.includes('429')) {\r\n          throw new Error(\"You've exceeded your request limit for the AI model. Please check your plan or try again later.\");\r\n        }\r\n        throw e; // Rethrow other errors immediately\r\n      }\r\n    }\r\n  }\r\n  // This line should not be reachable if retries are configured, but as a fallback:\r\n  throw new Error(\"The AI model is currently overloaded after multiple retries. Please try again later.\");\r\n}\r\n\r\nconst getMimeTypeFromDataURI = (dataURI: string): string => {\r\n  const match = dataURI.match(/^data:(.*?);/);\r\n  return match ? match[1] : 'application/octet-stream'; // Default if no match\r\n};\r\n\r\n// Helper function to generate an image for a single variant with ENHANCED design principles\r\nasync function generateImageForVariant(\r\n  variant: { platform: string, aspectRatio: string },\r\n  input: GeneratePostFromProfileInput,\r\n  textOutput: { imageText: string }\r\n) {\r\n\r\n  // TEMPORARILY DISABLED - Enhanced design system will be re-enabled after module resolution\r\n  /*\r\n  const enhancedDesignInput = {\r\n    businessType: input.businessType,\r\n    platform: variant.platform,\r\n    visualStyle: input.visualStyle,\r\n    primaryColor: input.primaryColor,\r\n    accentColor: input.accentColor,\r\n    backgroundColor: input.backgroundColor,\r\n    imageText: textOutput.imageText,\r\n    businessName: input.businessName,\r\n    logoDataUrl: input.logoDataUrl,\r\n    designExamples: input.designExamples,\r\n    qualityLevel: 'premium' as const\r\n  };\r\n\r\n  const designEnhancements = generateDesignEnhancements(enhancedDesignInput);\r\n\r\n  const enhancedPrompt = generateEnhancedDesignPrompt(enhancedDesignInput);\r\n  */\r\n  // Determine consistency level based on preferences first\r\n  const isStrictConsistency = input.brandConsistency?.strictConsistency ?? false;\r\n  const followBrandColors = input.brandConsistency?.followBrandColors ?? true;\r\n\r\n  // STRICT 3-color maximum with brand color enforcement\r\n  const colorInstructions = followBrandColors ? `\r\n  **MAXIMUM 3 COLORS ONLY - BRAND COLORS MANDATORY:**\r\n  - Primary Color: ${input.primaryColor} - DOMINANT color (60-70% of design)\r\n  - Accent Color: ${input.accentColor} - HIGHLIGHT color (20-30% of design)\r\n  - Background Color: ${input.backgroundColor} - BASE color (10-20% of design)\r\n\r\n  **ABSOLUTE COLOR LIMITS - NO EXCEPTIONS:**\r\n  - MAXIMUM 3 colors total in entire design\r\n  - ONLY use these exact 3 brand colors - NO additional colors\r\n  - NO 4th, 5th, or more colors allowed\r\n  - NO random colors, NO complementary colors, NO decorative colors\r\n  - NO gradients using non-brand colors\r\n  - Text: Use high contrast white or black only when needed\r\n  - FORBIDDEN: Any design with more than 3 colors total\r\n  ` : `\r\n  **MAXIMUM 3 COLORS TOTAL:**\r\n  - Primary: ${input.primaryColor} - DOMINANT (60-70%)\r\n  - Accent: ${input.accentColor} - HIGHLIGHT (20-30%)\r\n  - Background: ${input.backgroundColor} - BASE (10-20%)\r\n  - ABSOLUTE LIMIT: 3 colors maximum in entire design\r\n  `;\r\n\r\n  // Get platform-specific guidelines\r\n  const platformGuidelines = PLATFORM_SPECIFIC_GUIDELINES[variant.platform as keyof typeof PLATFORM_SPECIFIC_GUIDELINES] || PLATFORM_SPECIFIC_GUIDELINES.instagram;\r\n\r\n  // Get business-specific design DNA\r\n  const businessDNA = BUSINESS_TYPE_DESIGN_DNA[input.businessType as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n  // Get current design trends\r\n  let trendInstructions = '';\r\n  try {\r\n    const trends = await getCachedDesignTrends(\r\n      input.businessType,\r\n      variant.platform,\r\n      input.targetAudience,\r\n      input.businessType\r\n    );\r\n    trendInstructions = generateTrendInstructions(trends, variant.platform);\r\n  } catch (error) {\r\n  }\r\n\r\n  // Get performance-optimized instructions\r\n  const performanceInstructions = generatePerformanceOptimizedInstructions(\r\n    input.businessType,\r\n    variant.platform,\r\n    input.visualStyle\r\n  );\r\n\r\n  // Enhanced brand context for better design generation\r\n  const businessContext = `\r\n  **BUSINESS PROFILE:**\r\n  - Name: ${input.businessName || 'Business'}\r\n  - Type: ${input.businessType}\r\n  - Location: ${input.location}\r\n  - Description: ${input.description || 'Professional business'}\r\n  ${input.services ? `- Services: ${input.services.split('\\n').slice(0, 3).join(', ')}` : ''}\r\n  ${input.targetAudience ? `- Target Audience: ${input.targetAudience}` : ''}\r\n  ${input.websiteUrl ? `- Website: ${input.websiteUrl}` : ''}\r\n  `;\r\n\r\n  // Select random design template for variety\r\n  const selectedTemplate = DESIGN_TEMPLATES[Math.floor(Math.random() * DESIGN_TEMPLATES.length)];\r\n\r\n  // Generate visual variation approach for diversity\r\n  const visualVariations = [\r\n    'minimalist_clean', 'bold_dynamic', 'elegant_sophisticated', 'playful_creative',\r\n    'modern_geometric', 'organic_natural', 'industrial_urban', 'artistic_abstract',\r\n    'photographic_realistic', 'illustrated_stylized', 'gradient_colorful', 'monochrome_accent'\r\n  ];\r\n  const selectedVisualVariation = visualVariations[Math.floor(Math.random() * visualVariations.length)];\r\n\r\n  // TEMPLATE-BASED DESIGN APPROACH - Using selected template style\r\n\r\n  let imagePrompt = `Create a ${selectedTemplate.style.toUpperCase()} social media design following the \"${selectedTemplate.name}\" template approach.\r\n\r\n**BUSINESS:** ${input.businessType}\r\n**PLATFORM:** ${variant.platform}\r\n**ASPECT RATIO:** ${variant.aspectRatio}\r\n**MESSAGE:** \"${combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction)}\"\r\n\r\n**MANDATORY TEMPLATE STYLE:** ${selectedTemplate.name}\r\n**MANDATORY TEMPLATE DESCRIPTION:** ${selectedTemplate.description}\r\n**REQUIRED ELEMENTS:** ${selectedTemplate.elements.join(', ')}\r\n**ABSOLUTELY FORBIDDEN:** ${selectedTemplate.forbidden.join(', ')}\r\n\r\n**CRITICAL: You MUST follow the template style exactly. Do NOT default to illustrations if the template specifies otherwise.**\r\n\r\n**STRICT REQUIREMENTS - FOLLOW EXACTLY:**\r\n- Use ONLY 3 visual elements maximum: logo, main text, one simple accent\r\n- 50%+ of the design must be white/empty space\r\n- Single, clean sans-serif font family only\r\n- MAXIMUM 3 COLORS TOTAL in entire design\r\n- NO LINES: no decorative lines, borders, dividers, or linear elements\r\n- No decorative elements, shapes, or complex backgrounds\r\n- High contrast for perfect readability\r\n- Generous margins and spacing throughout\r\n- One clear focal point only\r\n\r\n**MAXIMUM 3 COLORS ONLY - BRAND COLORS:**\r\n- Primary Color: ${input.primaryColor} (DOMINANT - 60-70% of design)\r\n- Accent Color: ${input.accentColor} (HIGHLIGHTS - 20-30% of design)\r\n- Background Color: ${input.backgroundColor} (BASE - 10-20% of design)\r\n- ABSOLUTE LIMIT: These 3 colors only - NO 4th color allowed\r\n- FORBIDDEN: Any design using more than 3 colors total\r\n- FORBIDDEN: Additional colors, random colors, decorative colors\r\n\r\n**MANDATORY DESIGN APPROACH - ${selectedTemplate.name.toUpperCase()}:**\r\n- MUST follow ${selectedTemplate.style} aesthetic - NO EXCEPTIONS\r\n- MUST incorporate: ${selectedTemplate.elements.join(', ')}\r\n- ABSOLUTELY FORBIDDEN: ${selectedTemplate.forbidden.join(', ')}\r\n- ${selectedTemplate.description}\r\n- DO NOT default to illustration style unless template specifically requires it\r\n- Template requirements override all other design preferences\r\n\r\n**FORBIDDEN ELEMENTS:**\r\n❌ Multiple competing focal points\r\n❌ Decorative shapes or ornaments\r\n❌ Complex backgrounds or textures\r\n❌ Multiple font families\r\n❌ MORE THAN 3 COLORS TOTAL in the design\r\n❌ Any 4th, 5th, or additional colors beyond the 3 brand colors\r\n❌ Random colors, rainbow colors, complementary colors\r\n❌ Gradients using non-brand colors\r\n❌ ALL LINES: decorative lines, border lines, divider lines, underlines\r\n❌ Frame lines, geometric lines, separator lines, outline borders\r\n❌ Any linear elements or line-based decorations\r\n❌ Cramped spacing\r\n❌ Overlapping elements\r\n❌ Busy compositions\r\n❌ Multiple graphics or icons\r\n❌ Patterns or textures\r\n\r\n**MANDATORY SIMPLICITY:**\r\n- Single clear message\r\n- Generous white space (50%+ empty)\r\n- Maximum 3 visual elements\r\n- High contrast readability\r\n- Professional, uncluttered appearance\r\n\r\n**TEMPLATE ENFORCEMENT:**\r\n- If template says \"WATERCOLOR\" → Use watercolor texture, NOT illustrations\r\n- If template says \"SPLIT PHOTO\" → Use real photos split screen, NOT illustrations\r\n- If template says \"MEME TEMPLATE\" → Use white background with simple text, NOT illustrations\r\n- If template says \"POLAROID\" → Use retro photo frame style, NOT illustrations\r\n- If template says \"TYPOGRAPHY FOCUS\" → Focus on text design, NOT illustrations\r\n\r\n**FINAL INSTRUCTION:** Create a ${selectedTemplate.style} design following the ${selectedTemplate.name} template using MAXIMUM 3 COLORS ONLY and NO LINES. STRICTLY follow template requirements - do NOT default to illustration style. ABSOLUTE LIMITS: 3 colors maximum, NO lines, FOLLOW TEMPLATE EXACTLY - NO EXCEPTIONS.`;\r\n\r\n  // Add brand colors section if colors are available\r\n  if (input.brandProfile?.colors?.primary || input.brandProfile?.colors?.accent || input.brandProfile?.colors?.background) {\r\n    const primaryColor = input.brandProfile.colors.primary || 'default';\r\n    const accentColor = input.brandProfile.colors.accent || 'default';\r\n    const backgroundColor = input.brandProfile.colors.background || 'default';\r\n\r\n    imagePrompt += '\\n - Brand Colors: The brand\\'s color palette is: Primary HSL(' + primaryColor + '), Accent HSL(' + accentColor + '), Background HSL(' + backgroundColor + '). Please use these colors in the design.';\r\n  }\r\n\r\n  // Intelligent design examples processing\r\n  let designDNA = '';\r\n  let selectedExamples: string[] = [];\r\n\r\n  if (input.designExamples && input.designExamples.length > 0) {\r\n    try {\r\n      // Analyze design examples for intelligent processing\r\n      const analyses: DesignAnalysis[] = [];\r\n      for (const example of input.designExamples.slice(0, 5)) { // Limit to 5 for performance\r\n        try {\r\n          const analysis = await analyzeDesignExample(\r\n            example,\r\n            input.businessType,\r\n            variant.platform,\r\n            input.visualStyle + \" design for \" + textOutput.imageText\r\n          );\r\n          analyses.push(analysis);\r\n        } catch (error) {\r\n        }\r\n      }\r\n\r\n      if (analyses.length > 0) {\r\n        // Extract design DNA from analyzed examples\r\n        designDNA = extractDesignDNA(analyses);\r\n\r\n        // Select optimal examples based on analysis\r\n        selectedExamples = selectOptimalDesignExamples(\r\n          input.designExamples,\r\n          analyses,\r\n          textOutput.imageText,\r\n          variant.platform,\r\n          isStrictConsistency ? 3 : 1\r\n        );\r\n      } else {\r\n        // Fallback to original logic if analysis fails\r\n        selectedExamples = isStrictConsistency\r\n          ? input.designExamples\r\n          : [input.designExamples[Math.floor(Math.random() * input.designExamples.length)]];\r\n      }\r\n    } catch (error) {\r\n      selectedExamples = isStrictConsistency\r\n        ? input.designExamples\r\n        : [input.designExamples[Math.floor(Math.random() * input.designExamples.length)]];\r\n    }\r\n  }\r\n\r\n  // Add design consistency instructions based on analysis\r\n  if (isStrictConsistency) {\r\n    imagePrompt += \"\\n ** STRICT STYLE REFERENCE:**\\n\" +\r\n      \"Use the provided design examples as strict style reference. Closely match the visual aesthetic, color scheme, typography, layout patterns, and overall design approach of the reference designs. Create content that looks very similar to the uploaded examples while incorporating the new text and subject matter.\\n\\n\" +\r\n      designDNA;\r\n  } else {\r\n    imagePrompt += \"\\n ** STYLE INSPIRATION:**\\n\" +\r\n      \"Use the provided design examples as loose inspiration for the overall aesthetic and mood, but feel free to create more varied and creative designs while maintaining the brand essence.\\n\\n\" +\r\n      designDNA;\r\n\r\n  }\r\n\r\n  // Add clean design consistency instruction\r\n  imagePrompt += \"\\n\\n** CLEAN DESIGN CONSISTENCY:** Maintain the same clean, minimal approach for all designs. Focus on clarity and simplicity rather than variation. Each design should feel part of a cohesive, professional brand family.\\n\\n\" +\r\n    \"** SIMPLICITY REQUIREMENT:** Every design must prioritize:\\n\" +\r\n    \"- Single clear message\\n\" +\r\n    \"- Generous white space (50%+ empty)\\n\" +\r\n    \"- Maximum 3 visual elements\\n\" +\r\n    \"- High contrast readability\\n\" +\r\n    \"- Professional, uncluttered appearance\\n\\n\" +\r\n    \"** GENERATION ID:** \" + Date.now() + \"_clean_minimal - Clean design approach\";\r\n\r\n  // Build prompt parts array - Enhanced system temporarily disabled\r\n  const promptParts: any[] = [{ text: imagePrompt }];\r\n\r\n\r\n  // Enhanced logo integration with analysis\r\n  if (input.logoDataUrl) {\r\n    // Add logo analysis instructions to the prompt\r\n    const logoInstructions =\r\n      \"\\n\\n** CRITICAL LOGO USAGE REQUIREMENTS:**\\n\" +\r\n      \"🚨 MANDATORY: You MUST use the uploaded brand logo image provided below. DO NOT create, generate, or design a new logo.\\n\\n\" +\r\n      \"** LOGO INTEGRATION REQUIREMENTS:**\\n\" +\r\n      \"- Use ONLY the provided logo image - never create or generate a new logo\\n\" +\r\n      \"- The uploaded logo is the official brand logo and must be used exactly as provided\\n\" +\r\n      \"- Incorporate the provided logo naturally and prominently into the design\\n\" +\r\n      \"- Ensure logo is clearly visible and properly sized for the platform (minimum 10% of design area)\\n\" +\r\n      \"- Maintain logo's original proportions and readability - do not distort or modify the logo\\n\" +\r\n      \"- Position logo strategically: \" + (platformGuidelines.logoPlacement || 'Place logo prominently in corner or integrated into layout') + \"\\n\" +\r\n      \"- Ensure sufficient contrast between logo and background (minimum 4.5:1 ratio)\\n\" +\r\n      \"- For \" + variant.platform + \": Logo should be clearly visible and recognizable\\n\\n\" +\r\n      \"** BRAND CONSISTENCY WITH UPLOADED LOGO:**\\n\" +\r\n      \"- Extract and use colors from the provided logo for the overall color scheme\\n\" +\r\n      \"- Match the design style to complement the logo's aesthetic and personality\\n\" +\r\n      \"- Ensure visual harmony between the uploaded logo and all design elements\\n\" +\r\n      \"- The logo is the primary brand identifier - treat it as the most important visual element\";\r\n\r\n\r\n    // Add additional logo placement instructions\r\n    const logoPlacementInstructions =\r\n      \"\\n\\n** LOGO PLACEMENT PRIORITY:**\\n\" +\r\n      \"- Logo visibility is more important than other design elements\\n\" +\r\n      \"- If space is limited, reduce other elements to ensure logo prominence\\n\" +\r\n      \"- Logo should be one of the first things viewers notice in the design\";\r\n\r\n    // Update the main prompt with logo instructions\r\n    promptParts[0].text += logoInstructions + logoPlacementInstructions;\r\n\r\n    // Add logo as media with high priority\r\n    promptParts.push({\r\n      media: {\r\n        url: input.logoDataUrl,\r\n        contentType: getMimeTypeFromDataURI(input.logoDataUrl)\r\n      }\r\n    });\r\n\r\n  } else {\r\n  }\r\n\r\n  // Add selected design examples\r\n  selectedExamples.forEach(example => {\r\n    promptParts.push({ media: { url: example, contentType: getMimeTypeFromDataURI(example) } });\r\n  });\r\n\r\n  // Generate initial design\r\n  let finalImageUrl = '';\r\n  let attempts = 0;\r\n  const maxAttempts = 2; // Limit attempts to avoid excessive API calls\r\n\r\n  while (attempts < maxAttempts) {\r\n    attempts++;\r\n\r\n    try {\r\n      const { media } = await generateWithRetry({\r\n        model: 'googleai/gemini-2.0-flash-preview-image-generation',\r\n        prompt: promptParts,\r\n        config: {\r\n          responseModalities: ['TEXT', 'IMAGE'],\r\n        },\r\n      });\r\n\r\n      let imageUrl = media?.url ?? '';\r\n      if (!imageUrl) {\r\n        throw new Error('No image generated');\r\n      }\r\n\r\n      // Apply aspect ratio correction for non-square platforms\r\n      const { cropImageFromUrl, needsAspectRatioCorrection } = await import('@/lib/image-processing');\r\n      if (needsAspectRatioCorrection(variant.platform)) {\r\n        try {\r\n          imageUrl = await cropImageFromUrl(imageUrl, variant.platform);\r\n        } catch (cropError) {\r\n          // Continue with original image if cropping fails\r\n        }\r\n      }\r\n\r\n      // ENHANCED Quality validation for first attempt\r\n      if (attempts === 1) {\r\n        try {\r\n\r\n          // Standard quality assessment (Enhanced validation temporarily disabled)\r\n          const quality = await assessDesignQuality(\r\n            imageUrl,\r\n            input.businessType,\r\n            variant.platform,\r\n            input.visualStyle,\r\n            followBrandColors && input.primaryColor ? colorInstructions : undefined,\r\n            \"Create engaging design for: \" + textOutput.catchyWords\r\n          );\r\n\r\n\r\n          // If quality is acceptable, use this design\r\n          if (meetsQualityStandards(quality, 7)) {\r\n            finalImageUrl = imageUrl;\r\n            break;\r\n          }\r\n\r\n          // If quality is poor and we have attempts left, try to improve\r\n          if (attempts < maxAttempts) {\r\n\r\n            // Add improvement instructions to prompt\r\n            const improvementInstructions = generateImprovementPrompt(quality);\r\n            const improvedPrompt = imagePrompt + \"\\n\\n\" + improvementInstructions;\r\n            promptParts[0] = { text: improvedPrompt };\r\n            continue;\r\n          } else {\r\n            // Use the design even if quality is subpar (better than nothing)\r\n            finalImageUrl = imageUrl;\r\n            break;\r\n          }\r\n        } catch (qualityError) {\r\n          finalImageUrl = imageUrl;\r\n          break;\r\n        }\r\n      } else {\r\n        // For subsequent attempts, use the result\r\n        finalImageUrl = imageUrl;\r\n        break;\r\n      }\r\n    } catch (error) {\r\n      if (attempts === maxAttempts) {\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Record design generation for analytics\r\n  if (finalImageUrl) {\r\n    try {\r\n      const designId = \"design_\" + Date.now() + \"_\" + Math.random().toString(36).substr(2, 9);\r\n      recordDesignGeneration(\r\n        designId,\r\n        input.businessType,\r\n        variant.platform,\r\n        input.visualStyle,\r\n        9.2, // HD quality score with enhanced settings and prompting\r\n        {\r\n          colorPalette: input.primaryColor ? [input.primaryColor, input.accentColor, input.backgroundColor].filter(Boolean) : [],\r\n          typography: 'Modern social media optimized',\r\n          composition: variant.aspectRatio,\r\n          trends: selectedExamples.length > 0 ? ['design-examples-based'] : ['ai-generated'],\r\n          businessDNA: businessDNA.substring(0, 100) // Truncate for storage\r\n        },\r\n        {\r\n          engagement: 8,\r\n          brandAlignment: followBrandColors ? 9 : 7,\r\n          technicalQuality: 8,\r\n          trendRelevance: trendInstructions ? 8 : 6\r\n        }\r\n      );\r\n    } catch (analyticsError) {\r\n    }\r\n  }\r\n\r\n  return {\r\n    platform: variant.platform,\r\n    imageUrl: finalImageUrl,\r\n  };\r\n}\r\n\r\n\r\nconst generatePostFromProfileFlow = ai.defineFlow(\r\n  {\r\n    name: 'generatePostFromProfileFlow',\r\n    inputSchema: GeneratePostFromProfileInputSchema,\r\n    outputSchema: GeneratePostFromProfileOutputSchema,\r\n  },\r\n  async (input) => {\r\n    // Determine the primary platform for optimization\r\n    const primaryPlatform = input.variants[0]?.platform || 'instagram';\r\n\r\n    // Generate unique content variation approach to ensure diversity\r\n    const contentVariations = [\r\n      'trending_hook', 'story_driven', 'educational_tip', 'behind_scenes',\r\n      'question_engagement', 'statistic_driven', 'personal_insight', 'industry_contrarian',\r\n      'local_cultural', 'seasonal_relevance', 'problem_solution', 'inspiration_motivation'\r\n    ];\r\n    const selectedVariation = contentVariations[Math.floor(Math.random() * contentVariations.length)];\r\n\r\n    // Step 1: Intelligent Context Analysis - Determine what information is relevant\r\n    const contextRelevance = selectRelevantContext(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform,\r\n      input.contentThemes,\r\n      input.dayOfWeek\r\n    );\r\n\r\n\r\n    // Step 2: Fetch Real-Time Trending Topics (always useful)\r\n    const realTimeTrends = await generateRealTimeTrendingTopics(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform\r\n    );\r\n\r\n    // Step 3: Fetch Local Context (Weather + Events) - but filter intelligently\r\n    const rawLocalContext = await fetchLocalContext(\r\n      input.location,\r\n      input.businessType\r\n    );\r\n\r\n    // Step 4: Generate Market Intelligence for Advanced Content\r\n    const marketIntelligence = generateMarketIntelligence(\r\n      input.businessType,\r\n      input.location,\r\n      primaryPlatform,\r\n      input.services\r\n    );\r\n\r\n    // Step 5: Intelligently Filter Context Data\r\n    const filteredContext = filterContextData(contextRelevance, {\r\n      weather: rawLocalContext.weather,\r\n      events: rawLocalContext.events,\r\n      trends: realTimeTrends,\r\n      cultural: marketIntelligence.cultural_context\r\n    });\r\n\r\n    // Enhance market intelligence with filtered real-time trends\r\n    marketIntelligence.trending_topics = [\r\n      ...(filteredContext.selectedTrends || []).slice(0, 3),\r\n      ...marketIntelligence.trending_topics.slice(0, 2)\r\n    ];\r\n\r\n    // Step 6: Generate Human-like Content Techniques\r\n    const humanizationTechniques = generateHumanizationTechniques(\r\n      input.businessType,\r\n      input.writingTone,\r\n      input.location\r\n    );\r\n\r\n    // Step 7: Generate Traffic-Driving Elements\r\n    const trafficElements = generateTrafficDrivingElements(\r\n      input.businessType,\r\n      primaryPlatform,\r\n      input.targetAudience\r\n    );\r\n\r\n    // Step 8: Generate Enhanced Text Content with Intelligent Context\r\n    const { output: textOutput } = await enhancedTextGenPrompt({\r\n      businessType: input.businessType,\r\n      location: input.location,\r\n      writingTone: input.writingTone,\r\n      contentThemes: input.contentThemes,\r\n      dayOfWeek: input.dayOfWeek,\r\n      currentDate: input.currentDate,\r\n      platform: primaryPlatform,\r\n      services: input.services,\r\n      targetAudience: input.targetAudience,\r\n      keyFeatures: input.keyFeatures,\r\n      competitiveAdvantages: input.competitiveAdvantages,\r\n      // Add intelligent context instructions\r\n      contextInstructions: filteredContext.contextInstructions,\r\n      selectedWeather: filteredContext.selectedWeather,\r\n      selectedEvents: filteredContext.selectedEvents,\r\n      selectedTrends: filteredContext.selectedTrends,\r\n      selectedCultural: filteredContext.selectedCultural,\r\n      // Add content variation for diversity\r\n      contentVariation: selectedVariation,\r\n      // Template-specific content guidance\r\n      designTemplate: selectedTemplate.name,\r\n      templateStyle: selectedTemplate.style,\r\n      templateDescription: selectedTemplate.description,\r\n      // Language preferences\r\n      useLocalLanguage: input.useLocalLanguage || false,\r\n    });\r\n\r\n    if (!textOutput) {\r\n      throw new Error('Failed to generate advanced AI post content.');\r\n    }\r\n\r\n    // 🚀 ENHANCED: Generate Strategic Hashtag Analysis using Advanced RSS-Integrated System\r\n    const viralHashtagStrategy = await viralHashtagEngine.generateViralHashtags(\r\n      input.businessType,\r\n      input.businessName || input.businessType, // Use business name if available\r\n      input.location,\r\n      primaryPlatform,\r\n      input.services,\r\n      input.targetAudience\r\n    );\r\n\r\n    // Step 10: Generate Image for each variant in parallel\r\n    const imagePromises = input.variants.map(variant =>\r\n      generateImageForVariant(variant, input, textOutput)\r\n    );\r\n\r\n    const variants = await Promise.all(imagePromises);\r\n\r\n    // Step 11: Combine text components for image overlay\r\n    const combinedImageText = combineTextComponents(\r\n      textOutput.catchyWords,\r\n      textOutput.subheadline,\r\n      textOutput.callToAction\r\n    );\r\n\r\n    // 🎯 ENHANCED: Use Advanced RSS-Integrated Hashtags (exactly 10 hashtags)\r\n    const finalHashtags = viralHashtagStrategy.total.slice(0, 10); // Use the intelligently mixed hashtags\r\n\r\n    // Step 13: Combine results with intelligently selected context\r\n    return {\r\n      content: textOutput.content,\r\n      catchyWords: textOutput.catchyWords,\r\n      subheadline: textOutput.subheadline,\r\n      callToAction: textOutput.callToAction,\r\n      hashtags: finalHashtags.join(' '), // Convert to string format\r\n      contentVariants: textOutput.contentVariants,\r\n      hashtagAnalysis: {\r\n        trending: viralHashtagStrategy.trending,\r\n        viral: viralHashtagStrategy.viral,\r\n        niche: viralHashtagStrategy.niche,\r\n        location: viralHashtagStrategy.location,\r\n        community: viralHashtagStrategy.community,\r\n        platform: viralHashtagStrategy.platform,\r\n        seasonal: viralHashtagStrategy.seasonal,\r\n        analytics: viralHashtagStrategy.analytics // Include advanced analytics\r\n      },\r\n      // Advanced AI features metadata (for future UI display)\r\n      marketIntelligence: {\r\n        trending_topics: marketIntelligence.trending_topics.slice(0, 3),\r\n        competitor_insights: marketIntelligence.competitor_insights.slice(0, 2),\r\n        cultural_context: marketIntelligence.cultural_context,\r\n        viral_patterns: marketIntelligence.viral_content_patterns.slice(0, 3),\r\n        engagement_triggers: marketIntelligence.engagement_triggers.slice(0, 3)\r\n      },\r\n      // Intelligently selected local context\r\n      localContext: {\r\n        weather: filteredContext.selectedWeather,\r\n        events: filteredContext.selectedEvents,\r\n        contextRelevance: {\r\n          weather: contextRelevance.weather.priority,\r\n          events: contextRelevance.events.priority,\r\n          weatherReason: contextRelevance.weather.relevanceReason,\r\n          eventsReason: contextRelevance.events.relevanceReason\r\n        }\r\n      },\r\n      variants,\r\n    };\r\n  }\r\n);\r\n\r\n// Export function for use in other modules\r\nexport async function generatePostFromProfile(input: GeneratePostFromProfileInput): Promise<GeneratePostFromProfileOutput> {\r\n  return generatePostFromProfileFlow(input);\r\n}\r\n\r\n// End of file\r\nexport { generatePostFromProfileFlow };\r\n"], "names": [], "mappings": ";;;;;;AAGA;;;;;;;CAOC,GAED;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AA4DA,2FAA2F;AAC3F,0IAA0I;AAC1I;AAMA;AAMA;AAKA;;;;;;;;;;;;;;;AAzEA,2DAA2D;AAE3D,iFAAiF;AACjF,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAiC;YAAyB;SAA2B;QAChG,WAAW;YAAC;YAAiB;YAAY;YAAS;YAAU;YAAW;SAAmB;IAC5F;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAwB;YAAoB;SAAuB;QAC9E,WAAW;YAAC;YAAU;YAAc;YAAoB;SAAiB;IAC3E;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAuB;YAAoB;YAAqB;SAAoB;QAC/F,WAAW;YAAC;YAAiB;YAAc;YAAgB;SAAiB;IAC9E;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAkB;YAAoB;SAA4B;QACjG,WAAW;YAAC;YAAiB;YAAc;YAAoB;SAAmB;IACpF;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAmB;YAAiB;SAAoB;QACvF,WAAW;YAAC;YAAiB;YAAc;YAAoB;YAAgB;SAAkB;IACnG;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAkB;YAAqB;YAAqB;SAAqB;QAC5F,WAAW;YAAC;YAAiB;YAAc;YAAiB;SAAiB;IAC/E;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAqB;YAAe;YAAqB;SAAuB;QAC3F,WAAW;YAAC;YAAsB;YAAiB;YAAkB;SAAc;IACrF;CACD;;;;;AAyBD,MAAM,qCAAqC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACxD,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAAI,QAAQ,CAAC;IACb,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC7C,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEhD,yCAAyC;IACzC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC/C,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEtD,gCAAgC;IAChC,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,mBAAmB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QACvC,mBAAmB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACzC,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,yBAAyB;IACzB,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC3C,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC5C,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAEvB,uBAAuB;IACvB,kBAAkB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACpD;AAIA,MAAM,sCAAsC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnD,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvD,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACxD,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACxB,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,OAAO,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACpC,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QACvC,WAAW,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IAC1C,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,oBAAoB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC3B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAChC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM;YACf,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM;YACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM;QAChC,IAAI,QAAQ,CAAC;QACb,qBAAqB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACpC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,6BAA6B,oIAAA,CAAA,IAAC,CAAC,MAAM;QACvC,IAAI,QAAQ,CAAC;QACb,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACzB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,kBAAkB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClC,eAAe,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QACjC,GAAG,QAAQ,CAAC;QACZ,gBAAgB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;QAC7C,qBAAqB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ,CAAC;IACpD,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAChB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,uBAAuB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM;QACzC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACvB,QAAQ,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACvB,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;YACd,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;YACzB,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM;QACtB,IAAI,QAAQ,GAAG,QAAQ,CAAC;IAC1B,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACvB,UAAU,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;IACpB;AACF;AAIA,6DAA6D;AAG7D;;CAEC,GACD,SAAS,sBAAsB,WAAmB,EAAE,WAAoB,EAAE,YAAqB;IAC7F,MAAM,aAAa;QAAC;KAAY;IAEhC,IAAI,eAAe,YAAY,IAAI,IAAI;QACrC,WAAW,IAAI,CAAC,YAAY,IAAI;IAClC;IAEA,IAAI,gBAAgB,aAAa,IAAI,IAAI;QACvC,WAAW,IAAI,CAAC,aAAa,IAAI;IACnC;IAEA,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,6CAA6C;AAC7C,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC5C,MAAM;IACN,OAAO;QACL,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM;YACtB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;YAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM;YACvB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM;YACnB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;YACrB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC1C,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACrC,qBAAqB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YACjC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAChC,gBAAgB,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAChC,kBAAkB,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;YAClC,kBAAkB,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QACxC;IACF;IACA,QAAQ;QACN,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC7B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YACjC,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACvD,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;YACxD,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC9B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;gBAChC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC9B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YACjC,IAAI,QAAQ,CAAC;QACf;IACF;IACA,OAAO;QAAC,mIAAA,CAAA,iBAAc;QAAE,mIAAA,CAAA,gBAAa;QAAE,+IAAA,CAAA,yBAAsB;QAAE,+IAAA,CAAA,wBAAqB;KAAC;IACrF,QAAQ,gJAAA,CAAA,qBAAkB;AAC5B;AAEA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAClF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAChC,IAAI;YACF,MAAM,SAAS,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACT,EAAE,OAAO,GAAQ;YACf,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC7D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD,OAAO;gBACL,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC1C,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC1C,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,GAAG,mCAAmC;YAC9C;QACF;IACF;IACA,kFAAkF;IAClF,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,yBAAyB,CAAC;IAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG,4BAA4B,sBAAsB;AAC9E;AAEA,4FAA4F;AAC5F,eAAe,wBACb,OAAkD,EAClD,KAAmC,EACnC,UAAiC;IAGjC,2FAA2F;IAC3F;;;;;;;;;;;;;;;;;;EAkBA,GACA,yDAAyD;IACzD,MAAM,sBAAsB,MAAM,gBAAgB,EAAE,qBAAqB;IACzE,MAAM,oBAAoB,MAAM,gBAAgB,EAAE,qBAAqB;IAEvE,sDAAsD;IACtD,MAAM,oBAAoB,oBAAoB,CAAC;;mBAE9B,EAAE,MAAM,YAAY,CAAC;kBACtB,EAAE,MAAM,WAAW,CAAC;sBAChB,EAAE,MAAM,eAAe,CAAC;;;;;;;;;;EAU5C,CAAC,GAAG,CAAC;;aAEM,EAAE,MAAM,YAAY,CAAC;YACtB,EAAE,MAAM,WAAW,CAAC;gBAChB,EAAE,MAAM,eAAe,CAAC;;EAEtC,CAAC;IAED,mCAAmC;IACnC,MAAM,qBAAqB,qJAAA,CAAA,+BAA4B,CAAC,QAAQ,QAAQ,CAA8C,IAAI,qJAAA,CAAA,+BAA4B,CAAC,SAAS;IAEhK,mCAAmC;IACnC,MAAM,cAAc,qJAAA,CAAA,2BAAwB,CAAC,MAAM,YAAY,CAA0C,IAAI,qJAAA,CAAA,2BAAwB,CAAC,OAAO;IAE7I,4BAA4B;IAC5B,IAAI,oBAAoB;IACxB,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EACvC,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,cAAc,EACpB,MAAM,YAAY;QAEpB,oBAAoB,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,QAAQ,QAAQ;IACxE,EAAE,OAAO,OAAO,CAChB;IAEA,yCAAyC;IACzC,MAAM,0BAA0B,CAAA,GAAA,yIAAA,CAAA,2CAAwC,AAAD,EACrE,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW;IAGnB,sDAAsD;IACtD,MAAM,kBAAkB,CAAC;;UAEjB,EAAE,MAAM,YAAY,IAAI,WAAW;UACnC,EAAE,MAAM,YAAY,CAAC;cACjB,EAAE,MAAM,QAAQ,CAAC;iBACd,EAAE,MAAM,WAAW,IAAI,wBAAwB;EAC9D,EAAE,MAAM,QAAQ,GAAG,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG;EAC3F,EAAE,MAAM,cAAc,GAAG,CAAC,mBAAmB,EAAE,MAAM,cAAc,EAAE,GAAG,GAAG;EAC3E,EAAE,MAAM,UAAU,GAAG,CAAC,WAAW,EAAE,MAAM,UAAU,EAAE,GAAG,GAAG;EAC3D,CAAC;IAED,4CAA4C;IAC5C,MAAM,oBAAmB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IAE9F,mDAAmD;IACnD,MAAM,mBAAmB;QACvB;QAAoB;QAAgB;QAAyB;QAC7D;QAAoB;QAAmB;QAAoB;QAC3D;QAA0B;QAAwB;QAAqB;KACxE;IACD,MAAM,0BAA0B,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IAErG,iEAAiE;IAEjE,IAAI,cAAc,CAAC,SAAS,EAAE,kBAAiB,KAAK,CAAC,WAAW,GAAG,oCAAoC,EAAE,kBAAiB,IAAI,CAAC;;cAEnH,EAAE,MAAM,YAAY,CAAC;cACrB,EAAE,QAAQ,QAAQ,CAAC;kBACf,EAAE,QAAQ,WAAW,CAAC;cAC1B,EAAE,sBAAsB,WAAW,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,YAAY,EAAE;;8BAEjF,EAAE,kBAAiB,IAAI,CAAC;oCAClB,EAAE,kBAAiB,WAAW,CAAC;uBAC5C,EAAE,kBAAiB,QAAQ,CAAC,IAAI,CAAC,MAAM;0BACpC,EAAE,kBAAiB,SAAS,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;iBAgBjD,EAAE,MAAM,YAAY,CAAC;gBACtB,EAAE,MAAM,WAAW,CAAC;oBAChB,EAAE,MAAM,eAAe,CAAC;;;;;8BAKd,EAAE,kBAAiB,IAAI,CAAC,WAAW,GAAG;cACtD,EAAE,kBAAiB,KAAK,CAAC;oBACnB,EAAE,kBAAiB,QAAQ,CAAC,IAAI,CAAC,MAAM;wBACnC,EAAE,kBAAiB,SAAS,CAAC,IAAI,CAAC,MAAM;EAC9D,EAAE,kBAAiB,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAoCD,EAAE,kBAAiB,KAAK,CAAC,sBAAsB,EAAE,kBAAiB,IAAI,CAAC,uNAAuN,CAAC;IAE7T,mDAAmD;IACnD,IAAI,MAAM,YAAY,EAAE,QAAQ,WAAW,MAAM,YAAY,EAAE,QAAQ,UAAU,MAAM,YAAY,EAAE,QAAQ,YAAY;QACvH,MAAM,eAAe,MAAM,YAAY,CAAC,MAAM,CAAC,OAAO,IAAI;QAC1D,MAAM,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI;QACxD,MAAM,kBAAkB,MAAM,YAAY,CAAC,MAAM,CAAC,UAAU,IAAI;QAEhE,eAAe,mEAAmE,eAAe,mBAAmB,cAAc,uBAAuB,kBAAkB;IAC7K;IAEA,yCAAyC;IACzC,IAAI,YAAY;IAChB,IAAI,mBAA6B,EAAE;IAEnC,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,MAAM,GAAG,GAAG;QAC3D,IAAI;YACF,qDAAqD;YACrD,MAAM,WAA6B,EAAE;YACrC,KAAK,MAAM,WAAW,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,GAAI;gBACtD,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EACxC,SACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW,GAAG,iBAAiB,WAAW,SAAS;oBAE3D,SAAS,IAAI,CAAC;gBAChB,EAAE,OAAO,OAAO,CAChB;YACF;YAEA,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,4CAA4C;gBAC5C,YAAY,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;gBAE7B,4CAA4C;gBAC5C,mBAAmB,CAAA,GAAA,wIAAA,CAAA,8BAA2B,AAAD,EAC3C,MAAM,cAAc,EACpB,UACA,WAAW,SAAS,EACpB,QAAQ,QAAQ,EAChB,sBAAsB,IAAI;YAE9B,OAAO;gBACL,+CAA+C;gBAC/C,mBAAmB,sBACf,MAAM,cAAc,GACpB;oBAAC,MAAM,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,cAAc,CAAC,MAAM,EAAE;iBAAC;YACrF;QACF,EAAE,OAAO,OAAO;YACd,mBAAmB,sBACf,MAAM,cAAc,GACpB;gBAAC,MAAM,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,cAAc,CAAC,MAAM,EAAE;aAAC;QACrF;IACF;IAEA,wDAAwD;IACxD,IAAI,qBAAqB;QACvB,eAAe,sCACb,8TACA;IACJ,OAAO;QACL,eAAe,iCACb,gMACA;IAEJ;IAEA,2CAA2C;IAC3C,eAAe,oOACb,iEACA,6BACA,0CACA,kCACA,kCACA,+CACA,yBAAyB,KAAK,GAAG,KAAK;IAExC,kEAAkE;IAClE,MAAM,cAAqB;QAAC;YAAE,MAAM;QAAY;KAAE;IAGlD,0CAA0C;IAC1C,IAAI,MAAM,WAAW,EAAE;QACrB,+CAA+C;QAC/C,MAAM,mBACJ,iDACA,gIACA,0CACA,+EACA,0FACA,gFACA,wGACA,iGACA,oCAAoC,CAAC,mBAAmB,aAAa,IAAI,4DAA4D,IAAI,OACzI,qFACA,WAAW,QAAQ,QAAQ,GAAG,0DAC9B,iDACA,mFACA,kFACA,gFACA;QAGF,6CAA6C;QAC7C,MAAM,4BACJ,wCACA,qEACA,6EACA;QAEF,gDAAgD;QAChD,WAAW,CAAC,EAAE,CAAC,IAAI,IAAI,mBAAmB;QAE1C,uCAAuC;QACvC,YAAY,IAAI,CAAC;YACf,OAAO;gBACL,KAAK,MAAM,WAAW;gBACtB,aAAa,uBAAuB,MAAM,WAAW;YACvD;QACF;IAEF,OAAO,CACP;IAEA,+BAA+B;IAC/B,iBAAiB,OAAO,CAAC,CAAA;QACvB,YAAY,IAAI,CAAC;YAAE,OAAO;gBAAE,KAAK;gBAAS,aAAa,uBAAuB;YAAS;QAAE;IAC3F;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB;IACpB,IAAI,WAAW;IACf,MAAM,cAAc,GAAG,8CAA8C;IAErE,MAAO,WAAW,YAAa;QAC7B;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;gBACxC,OAAO;gBACP,QAAQ;gBACR,QAAQ;oBACN,oBAAoB;wBAAC;wBAAQ;qBAAQ;gBACvC;YACF;YAEA,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,yDAAyD;YACzD,MAAM,EAAE,gBAAgB,EAAE,0BAA0B,EAAE,GAAG;YACzD,IAAI,2BAA2B,QAAQ,QAAQ,GAAG;gBAChD,IAAI;oBACF,WAAW,MAAM,iBAAiB,UAAU,QAAQ,QAAQ;gBAC9D,EAAE,OAAO,WAAW;gBAClB,iDAAiD;gBACnD;YACF;YAEA,gDAAgD;YAChD,IAAI,aAAa,GAAG;gBAClB,IAAI;oBAEF,yEAAyE;oBACzE,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EACtC,UACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW,EACjB,qBAAqB,MAAM,YAAY,GAAG,oBAAoB,WAC9D,iCAAiC,WAAW,WAAW;oBAIzD,4CAA4C;oBAC5C,IAAI,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,IAAI;wBACrC,gBAAgB;wBAChB;oBACF;oBAEA,+DAA+D;oBAC/D,IAAI,WAAW,aAAa;wBAE1B,yCAAyC;wBACzC,MAAM,0BAA0B,CAAA,GAAA,uIAAA,CAAA,4BAAyB,AAAD,EAAE;wBAC1D,MAAM,iBAAiB,cAAc,SAAS;wBAC9C,WAAW,CAAC,EAAE,GAAG;4BAAE,MAAM;wBAAe;wBACxC;oBACF,OAAO;wBACL,iEAAiE;wBACjE,gBAAgB;wBAChB;oBACF;gBACF,EAAE,OAAO,cAAc;oBACrB,gBAAgB;oBAChB;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,gBAAgB;gBAChB;YACF;QACF,EAAE,OAAO,OAAO;YACd,IAAI,aAAa,aAAa;gBAC5B,MAAM;YACR;QACF;IACF;IAEA,yCAAyC;IACzC,IAAI,eAAe;QACjB,IAAI;YACF,MAAM,WAAW,YAAY,KAAK,GAAG,KAAK,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACrF,CAAA,GAAA,yIAAA,CAAA,yBAAsB,AAAD,EACnB,UACA,MAAM,YAAY,EAClB,QAAQ,QAAQ,EAChB,MAAM,WAAW,EACjB,KACA;gBACE,cAAc,MAAM,YAAY,GAAG;oBAAC,MAAM,YAAY;oBAAE,MAAM,WAAW;oBAAE,MAAM,eAAe;iBAAC,CAAC,MAAM,CAAC,WAAW,EAAE;gBACtH,YAAY;gBACZ,aAAa,QAAQ,WAAW;gBAChC,QAAQ,iBAAiB,MAAM,GAAG,IAAI;oBAAC;iBAAwB,GAAG;oBAAC;iBAAe;gBAClF,aAAa,YAAY,SAAS,CAAC,GAAG,KAAK,uBAAuB;YACpE,GACA;gBACE,YAAY;gBACZ,gBAAgB,oBAAoB,IAAI;gBACxC,kBAAkB;gBAClB,gBAAgB,oBAAoB,IAAI;YAC1C;QAEJ,EAAE,OAAO,gBAAgB,CACzB;IACF;IAEA,OAAO;QACL,UAAU,QAAQ,QAAQ;QAC1B,UAAU;IACZ;AACF;AAGA,MAAM,8BAA8B,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC/C;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,kDAAkD;IAClD,MAAM,kBAAkB,MAAM,QAAQ,CAAC,EAAE,EAAE,YAAY;IAEvD,iEAAiE;IACjE,MAAM,oBAAoB;QACxB;QAAiB;QAAgB;QAAmB;QACpD;QAAuB;QAAoB;QAAoB;QAC/D;QAAkB;QAAsB;QAAoB;KAC7D;IACD,MAAM,oBAAoB,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;IAEjG,gFAAgF;IAChF,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD,EAC3C,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,aAAa,EACnB,MAAM,SAAS;IAIjB,0DAA0D;IAC1D,MAAM,iBAAiB,MAAM,CAAA,GAAA,wIAAA,CAAA,iCAA8B,AAAD,EACxD,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd;IAGF,4EAA4E;IAC5E,MAAM,kBAAkB,MAAM,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAC5C,MAAM,QAAQ,EACd,MAAM,YAAY;IAGpB,4DAA4D;IAC5D,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,6BAA0B,AAAD,EAClD,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ;IAGhB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;QAC1D,SAAS,gBAAgB,OAAO;QAChC,QAAQ,gBAAgB,MAAM;QAC9B,QAAQ;QACR,UAAU,mBAAmB,gBAAgB;IAC/C;IAEA,6DAA6D;IAC7D,mBAAmB,eAAe,GAAG;WAChC,CAAC,gBAAgB,cAAc,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG;WAChD,mBAAmB,eAAe,CAAC,KAAK,CAAC,GAAG;KAChD;IAED,iDAAiD;IACjD,MAAM,yBAAyB,CAAA,GAAA,mJAAA,CAAA,iCAA8B,AAAD,EAC1D,MAAM,YAAY,EAClB,MAAM,WAAW,EACjB,MAAM,QAAQ;IAGhB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAA,GAAA,mJAAA,CAAA,iCAA8B,AAAD,EACnD,MAAM,YAAY,EAClB,iBACA,MAAM,cAAc;IAGtB,kEAAkE;IAClE,MAAM,EAAE,QAAQ,UAAU,EAAE,GAAG,MAAM,sBAAsB;QACzD,cAAc,MAAM,YAAY;QAChC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,eAAe,MAAM,aAAa;QAClC,WAAW,MAAM,SAAS;QAC1B,aAAa,MAAM,WAAW;QAC9B,UAAU;QACV,UAAU,MAAM,QAAQ;QACxB,gBAAgB,MAAM,cAAc;QACpC,aAAa,MAAM,WAAW;QAC9B,uBAAuB,MAAM,qBAAqB;QAClD,uCAAuC;QACvC,qBAAqB,gBAAgB,mBAAmB;QACxD,iBAAiB,gBAAgB,eAAe;QAChD,gBAAgB,gBAAgB,cAAc;QAC9C,gBAAgB,gBAAgB,cAAc;QAC9C,kBAAkB,gBAAgB,gBAAgB;QAClD,sCAAsC;QACtC,kBAAkB;QAClB,qCAAqC;QACrC,gBAAgB,iBAAiB,IAAI;QACrC,eAAe,iBAAiB,KAAK;QACrC,qBAAqB,iBAAiB,WAAW;QACjD,uBAAuB;QACvB,kBAAkB,MAAM,gBAAgB,IAAI;IAC9C;IAEA,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,wFAAwF;IACxF,MAAM,uBAAuB,MAAM,uIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CACzE,MAAM,YAAY,EAClB,MAAM,YAAY,IAAI,MAAM,YAAY,EACxC,MAAM,QAAQ,EACd,iBACA,MAAM,QAAQ,EACd,MAAM,cAAc;IAGtB,uDAAuD;IACvD,MAAM,gBAAgB,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,UACvC,wBAAwB,SAAS,OAAO;IAG1C,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAC;IAEnC,qDAAqD;IACrD,MAAM,oBAAoB,sBACxB,WAAW,WAAW,EACtB,WAAW,WAAW,EACtB,WAAW,YAAY;IAGzB,0EAA0E;IAC1E,MAAM,gBAAgB,qBAAqB,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,uCAAuC;IAEtG,+DAA+D;IAC/D,OAAO;QACL,SAAS,WAAW,OAAO;QAC3B,aAAa,WAAW,WAAW;QACnC,aAAa,WAAW,WAAW;QACnC,cAAc,WAAW,YAAY;QACrC,UAAU,cAAc,IAAI,CAAC;QAC7B,iBAAiB,WAAW,eAAe;QAC3C,iBAAiB;YACf,UAAU,qBAAqB,QAAQ;YACvC,OAAO,qBAAqB,KAAK;YACjC,OAAO,qBAAqB,KAAK;YACjC,UAAU,qBAAqB,QAAQ;YACvC,WAAW,qBAAqB,SAAS;YACzC,UAAU,qBAAqB,QAAQ;YACvC,UAAU,qBAAqB,QAAQ;YACvC,WAAW,qBAAqB,SAAS,CAAC,6BAA6B;QACzE;QACA,wDAAwD;QACxD,oBAAoB;YAClB,iBAAiB,mBAAmB,eAAe,CAAC,KAAK,CAAC,GAAG;YAC7D,qBAAqB,mBAAmB,mBAAmB,CAAC,KAAK,CAAC,GAAG;YACrE,kBAAkB,mBAAmB,gBAAgB;YACrD,gBAAgB,mBAAmB,sBAAsB,CAAC,KAAK,CAAC,GAAG;YACnE,qBAAqB,mBAAmB,mBAAmB,CAAC,KAAK,CAAC,GAAG;QACvE;QACA,uCAAuC;QACvC,cAAc;YACZ,SAAS,gBAAgB,eAAe;YACxC,QAAQ,gBAAgB,cAAc;YACtC,kBAAkB;gBAChB,SAAS,iBAAiB,OAAO,CAAC,QAAQ;gBAC1C,QAAQ,iBAAiB,MAAM,CAAC,QAAQ;gBACxC,eAAe,iBAAiB,OAAO,CAAC,eAAe;gBACvD,cAAc,iBAAiB,MAAM,CAAC,eAAe;YACvD;QACF;QACA;IACF;AACF;AAIK,eAAe,wBAAwB,KAAmC;IAC/E,OAAO,4BAA4B;AACrC;;;;IAFsB;IAKb;;AALa,+OAAA;AAKb,+OAAA", "debugId": null}}, {"offset": {"line": 4745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.5/content-generator.ts"], "sourcesContent": ["/**\r\n * Revo 1.5 Content Generator\r\n * Enhanced content generation with advanced features\r\n */\r\n\r\nimport type { \r\n  IContentGenerator, \r\n  ContentGenerationRequest, \r\n  GenerationResponse \r\n} from '../../types/model-types';\r\nimport type { GeneratedPost } from '@/lib/types';\r\nimport { generatePostFromProfile } from '@/ai/flows/generate-post-from-profile';\r\n\r\nexport class Revo15ContentGenerator implements IContentGenerator {\r\n  private readonly modelId = 'revo-1.5';\r\n\r\n  /**\r\n   * Generate enhanced content using Revo 1.5 specifications\r\n   */\r\n  async generateContent(request: ContentGenerationRequest): Promise<GenerationResponse<GeneratedPost>> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n\r\n      // Validate request\r\n      if (!this.validateRequest(request)) {\r\n        throw new Error('Invalid content generation request for Revo 1.5');\r\n      }\r\n\r\n      // Prepare enhanced generation parameters\r\n      const generationParams = this.prepareEnhancedGenerationParams(request);\r\n\r\n      // Generate content with enhanced features\r\n      const postDetails = await generatePostFromProfile(generationParams);\r\n\r\n      // Create the enhanced generated post\r\n      const generatedPost: GeneratedPost = {\r\n        id: new Date().toISOString(),\r\n        date: new Date().toISOString(),\r\n        content: postDetails.content,\r\n        hashtags: postDetails.hashtags,\r\n        status: 'generated',\r\n        variants: postDetails.variants,\r\n        catchyWords: postDetails.catchyWords,\r\n        subheadline: postDetails.subheadline,\r\n        callToAction: postDetails.callToAction,\r\n        // Enhanced features for Revo 1.5\r\n        contentVariants: postDetails.contentVariants,\r\n        hashtagAnalysis: postDetails.hashtagAnalysis,\r\n        marketIntelligence: postDetails.marketIntelligence,\r\n        localContext: postDetails.localContext,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          modelVersion: '1.5.0',\r\n          generationType: 'enhanced',\r\n          processingTime: Date.now() - startTime,\r\n          qualityLevel: 'enhanced',\r\n          enhancedFeatures: this.getAppliedEnhancements(request),\r\n          artifactsUsed: request.artifactIds?.length || 0\r\n        }\r\n      };\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      const qualityScore = this.calculateEnhancedQualityScore(generatedPost);\r\n\r\n\r\n      return {\r\n        success: true,\r\n        data: generatedPost,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore,\r\n          creditsUsed: 2, // Revo 1.5 uses 2 credits\r\n          enhancementsApplied: [\r\n            'enhanced-ai-engine',\r\n            'real-time-context',\r\n            'trending-topics',\r\n            'advanced-prompting',\r\n            'quality-optimization',\r\n            ...(request.artifactIds?.length ? ['artifact-integration'] : [])\r\n          ]\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore: 0,\r\n          creditsUsed: 0,\r\n          enhancementsApplied: []\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate content generation request for Revo 1.5\r\n   */\r\n  private validateRequest(request: ContentGenerationRequest): boolean {\r\n    // Check required fields\r\n    if (!request.profile || !request.platform) {\r\n      return false;\r\n    }\r\n\r\n    // Check if profile has minimum required information\r\n    if (!request.profile.businessType || !request.profile.businessName) {\r\n      return false;\r\n    }\r\n\r\n    // Revo 1.5 supports artifacts - validate if provided\r\n    if (request.artifactIds && request.artifactIds.length > 5) {\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Prepare enhanced generation parameters for Revo 1.5\r\n   */\r\n  private prepareEnhancedGenerationParams(request: ContentGenerationRequest) {\r\n    const { profile, platform, brandConsistency } = request;\r\n    const today = new Date();\r\n\r\n    // Enhanced parameter preparation with more sophisticated processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n          typeof service === 'object' && service.name\r\n            ? `${service.name}: ${service.description || ''}`\r\n            : service\r\n        ).join('\\n')\r\n      : profile.services || '';\r\n\r\n    return {\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: today.toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: this.getOptimalAspectRatio(platform),\r\n      }],\r\n      services: servicesString,\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Revo 1.5 enhanced features\r\n      modelConstraints: {\r\n        maxComplexity: 'enhanced',\r\n        enhancedFeatures: true,\r\n        realTimeContext: true,\r\n        trendingTopics: true,\r\n        artifactSupport: true,\r\n        advancedPrompting: true,\r\n        qualityLevel: 'enhanced'\r\n      },\r\n      // Artifact integration\r\n      artifactIds: request.artifactIds?.slice(0, 5) || [], // Limit to 5 artifacts\r\n      customInstructions: request.customInstructions\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)\r\n   */\r\n  private getOptimalAspectRatio(platform: string): string {\r\n    switch (platform) {\r\n      case 'Instagram':\r\n        return '1:1'; // Square for feed, can also do 9:16 for stories\r\n      case 'Facebook':\r\n        return '16:9'; // Landscape for better engagement\r\n      case 'Twitter':\r\n        return '16:9'; // Landscape works well\r\n      case 'LinkedIn':\r\n        return '16:9'; // Professional landscape format\r\n      default:\r\n        return '1:1';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced quality score for generated content\r\n   */\r\n  private calculateEnhancedQualityScore(post: GeneratedPost): number {\r\n    let score = 6; // Higher base score for Revo 1.5\r\n\r\n    // Content quality checks\r\n    if (post.content && post.content.length > 50) score += 0.5;\r\n    if (post.content && post.content.length > 150) score += 0.5;\r\n    \r\n    // Enhanced content features\r\n    if (post.subheadline && post.subheadline.trim().length > 0) score += 0.5;\r\n    if (post.callToAction && post.callToAction.trim().length > 0) score += 0.5;\r\n    \r\n    // Hashtag quality and analysis\r\n    if (post.hashtags && post.hashtags.length >= 8) score += 0.5;\r\n    if (post.hashtagAnalysis) score += 0.5;\r\n    \r\n    // Advanced features\r\n    if (post.contentVariants && post.contentVariants.length > 0) score += 0.5;\r\n    if (post.marketIntelligence) score += 0.5;\r\n    if (post.localContext) score += 0.5;\r\n    \r\n    // Image generation success\r\n    if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Cap at 10\r\n    return Math.min(score, 10);\r\n  }\r\n\r\n  /**\r\n   * Get applied enhancements for this generation\r\n   */\r\n  private getAppliedEnhancements(request: ContentGenerationRequest): string[] {\r\n    const enhancements = ['enhanced-ai-engine', 'advanced-prompting'];\r\n    \r\n    if (request.artifactIds && request.artifactIds.length > 0) {\r\n      enhancements.push('artifact-integration');\r\n    }\r\n    \r\n    if (request.profile.location) {\r\n      enhancements.push('local-context', 'real-time-context');\r\n    }\r\n    \r\n    enhancements.push('trending-topics', 'quality-optimization', 'brand-consistency-advanced');\r\n    \r\n    return enhancements;\r\n  }\r\n\r\n  /**\r\n   * Health check for enhanced content generator\r\n   */\r\n  async healthCheck(): Promise<boolean> {\r\n    try {\r\n      // Check if we can access enhanced AI services\r\n      const hasGeminiKey = !!(\r\n        process.env.GEMINI_API_KEY || \r\n        process.env.GOOGLE_API_KEY || \r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n      \r\n      const hasOpenAIKey = !!process.env.OPENAI_API_KEY;\r\n      \r\n      return hasGeminiKey || hasOpenAIKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get generator-specific information\r\n   */\r\n  getGeneratorInfo() {\r\n    return {\r\n      modelId: this.modelId,\r\n      type: 'content',\r\n      capabilities: [\r\n        'Enhanced content generation',\r\n        'Real-time context integration',\r\n        'Trending topics analysis',\r\n        'Advanced brand consistency',\r\n        'Artifact support (up to 5)',\r\n        'Content variants generation',\r\n        'Hashtag analysis',\r\n        'Market intelligence',\r\n        'Local context optimization'\r\n      ],\r\n      limitations: [\r\n        'Higher credit cost (2x)',\r\n        'Longer processing times',\r\n        'Requires more system resources'\r\n      ],\r\n      averageProcessingTime: '20-30 seconds',\r\n      qualityRange: '7-9/10',\r\n      costPerGeneration: 2,\r\n      enhancedFeatures: this.getEnhancedFeaturesList()\r\n    };\r\n  }\r\n\r\n  private getEnhancedFeaturesList() {\r\n    return {\r\n      realTimeContext: true,\r\n      trendingTopics: true,\r\n      artifactSupport: true,\r\n      contentVariants: true,\r\n      hashtagAnalysis: true,\r\n      marketIntelligence: true,\r\n      localOptimization: true,\r\n      advancedPrompting: true,\r\n      qualityOptimization: true\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAQD;;AAEO,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,gBAAgB,OAAiC,EAA8C;QACnG,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YAEF,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,yCAAyC;YACzC,MAAM,mBAAmB,IAAI,CAAC,+BAA+B,CAAC;YAE9D,0CAA0C;YAC1C,MAAM,cAAc,MAAM,CAAA,GAAA,yJAAA,CAAA,0BAAuB,AAAD,EAAE;YAElD,qCAAqC;YACrC,MAAM,gBAA+B;gBACnC,IAAI,IAAI,OAAO,WAAW;gBAC1B,MAAM,IAAI,OAAO,WAAW;gBAC5B,SAAS,YAAY,OAAO;gBAC5B,UAAU,YAAY,QAAQ;gBAC9B,QAAQ;gBACR,UAAU,YAAY,QAAQ;gBAC9B,aAAa,YAAY,WAAW;gBACpC,aAAa,YAAY,WAAW;gBACpC,cAAc,YAAY,YAAY;gBACtC,iCAAiC;gBACjC,iBAAiB,YAAY,eAAe;gBAC5C,iBAAiB,YAAY,eAAe;gBAC5C,oBAAoB,YAAY,kBAAkB;gBAClD,cAAc,YAAY,YAAY;gBACtC,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB,KAAK,GAAG,KAAK;oBAC7B,cAAc;oBACd,kBAAkB,IAAI,CAAC,sBAAsB,CAAC;oBAC9C,eAAe,QAAQ,WAAW,EAAE,UAAU;gBAChD;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,6BAA6B,CAAC;YAGxD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBACnB;wBACA;wBACA;wBACA;wBACA;2BACI,QAAQ,WAAW,EAAE,SAAS;4BAAC;yBAAuB,GAAG,EAAE;qBAChE;gBACH;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAiC,EAAW;QAClE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACzC,OAAO;QACT;QAEA,oDAAoD;QACpD,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,IAAI,CAAC,QAAQ,OAAO,CAAC,YAAY,EAAE;YAClE,OAAO;QACT;QAEA,qDAAqD;QACrD,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG,CAC3D;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gCAAgC,OAAiC,EAAE;QACzE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG;QAChD,MAAM,QAAQ,IAAI;QAElB,oEAAoE;QACpE,MAAM,oBAAoB,MAAM,OAAO,CAAC,QAAQ,WAAW,IACvD,QAAQ,WAAW,CAAC,IAAI,CAAC,QACzB,QAAQ,WAAW,IAAI;QAE3B,MAAM,8BAA8B,MAAM,OAAO,CAAC,QAAQ,qBAAqB,IAC3E,QAAQ,qBAAqB,CAAC,IAAI,CAAC,QACnC,QAAQ,qBAAqB,IAAI;QAErC,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,QAAQ,IACjD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,UACnB,OAAO,YAAY,YAAY,QAAQ,IAAI,GACvC,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,IAAI,IAAI,GAC/C,SACJ,IAAI,CAAC,QACP,QAAQ,QAAQ,IAAI;QAExB,OAAO;YACL,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa;YACpC,aAAa,QAAQ,WAAW;YAChC,aAAa,QAAQ,WAAW;YAChC,gBAAgB,kBAAkB,oBAAqB,QAAQ,cAAc,IAAI,EAAE,GAAI,EAAE;YACzF,cAAc,QAAQ,YAAY;YAClC,aAAa,QAAQ,WAAW;YAChC,iBAAiB,QAAQ,eAAe;YACxC,WAAW,MAAM,kBAAkB,CAAC,SAAS;gBAAE,SAAS;YAAO;YAC/D,aAAa,MAAM,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,OAAO;gBAAQ,KAAK;YAAU;YAChG,UAAU;gBAAC;oBACT,UAAU;oBACV,aAAa,IAAI,CAAC,qBAAqB,CAAC;gBAC1C;aAAE;YACF,UAAU;YACV,gBAAgB,QAAQ,cAAc;YACtC,aAAa;YACb,uBAAuB;YACvB,kBAAkB,oBAAoB;gBAAE,mBAAmB;gBAAO,mBAAmB;YAAK;YAC1F,6BAA6B;YAC7B,kBAAkB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB;gBACjB,mBAAmB;gBACnB,cAAc;YAChB;YACA,uBAAuB;YACvB,aAAa,QAAQ,WAAW,EAAE,MAAM,GAAG,MAAM,EAAE;YACnD,oBAAoB,QAAQ,kBAAkB;QAChD;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,QAAgB,EAAU;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,gDAAgD;YAChE,KAAK;gBACH,OAAO,QAAQ,kCAAkC;YACnD,KAAK;gBACH,OAAO,QAAQ,uBAAuB;YACxC,KAAK;gBACH,OAAO,QAAQ,gCAAgC;YACjD;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,8BAA8B,IAAmB,EAAU;QACjE,IAAI,QAAQ,GAAG,iCAAiC;QAEhD,yBAAyB;QACzB,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,SAAS;QACvD,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,SAAS;QAExD,4BAA4B;QAC5B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,SAAS;QACrE,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,SAAS;QAEvE,+BAA+B;QAC/B,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,GAAG,SAAS;QACzD,IAAI,KAAK,eAAe,EAAE,SAAS;QAEnC,oBAAoB;QACpB,IAAI,KAAK,eAAe,IAAI,KAAK,eAAe,CAAC,MAAM,GAAG,GAAG,SAAS;QACtE,IAAI,KAAK,kBAAkB,EAAE,SAAS;QACtC,IAAI,KAAK,YAAY,EAAE,SAAS;QAEhC,2BAA2B;QAC3B,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1E,SAAS;QACX;QAEA,YAAY;QACZ,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,AAAQ,uBAAuB,OAAiC,EAAY;QAC1E,MAAM,eAAe;YAAC;YAAsB;SAAqB;QAEjE,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;YACzD,aAAa,IAAI,CAAC;QACpB;QAEA,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;YAC5B,aAAa,IAAI,CAAC,iBAAiB;QACrC;QAEA,aAAa,IAAI,CAAC,mBAAmB,wBAAwB;QAE7D,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,8CAA8C;YAC9C,MAAM,eAAe,CAAC,CAAC,CACrB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,MAAM,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAEjD,OAAO,gBAAgB;QACzB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;aACD;YACD,uBAAuB;YACvB,cAAc;YACd,mBAAmB;YACnB,kBAAkB,IAAI,CAAC,uBAAuB;QAChD;IACF;IAEQ,0BAA0B;QAChC,OAAO;YACL,iBAAiB;YACjB,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,oBAAoB;YACpB,mBAAmB;YACnB,mBAAmB;YACnB,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 5019, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.5/design-generator.ts"], "sourcesContent": ["/**\r\n * Revo 1.5 Design Generator\r\n * Enhanced design generation with advanced features\r\n */\r\n\r\nimport type {\r\n  IDesignGenerator,\r\n  DesignGenerationRequest,\r\n  GenerationResponse\r\n} from '../../types/model-types';\r\nimport type { PostVariant } from '@/lib/types';\r\n\r\nexport class Revo15DesignGenerator implements IDesignGenerator {\r\n  private readonly modelId = 'revo-1.5';\r\n\r\n  /**\r\n   * Generate enhanced design using Revo 1.5 specifications\r\n   */\r\n  async generateDesign(request: DesignGenerationRequest): Promise<GenerationResponse<PostVariant>> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n\r\n      // Validate request\r\n      if (!this.validateRequest(request)) {\r\n        throw new Error('Invalid design generation request for Revo 1.5');\r\n      }\r\n\r\n      // Generate enhanced design using Gemini 2.5 or fallback\r\n      const designResult = await this.generateEnhancedDesign(request);\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      const qualityScore = this.calculateEnhancedQualityScore(designResult);\r\n\r\n\r\n      return {\r\n        success: true,\r\n        data: designResult,\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore,\r\n          creditsUsed: 2, // Revo 1.5 uses 2 credits for design\r\n          enhancementsApplied: [\r\n            'enhanced-ai-engine',\r\n            'advanced-styling',\r\n            'brand-consistency-advanced',\r\n            'multi-aspect-ratio',\r\n            'quality-optimization',\r\n            ...(request.artifactInstructions ? ['artifact-integration'] : [])\r\n          ]\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        success: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        metadata: {\r\n          modelId: this.modelId,\r\n          processingTime,\r\n          qualityScore: 0,\r\n          creditsUsed: 0,\r\n          enhancementsApplied: []\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate enhanced design using Revo 1.5 two-step process\r\n   */\r\n  private async generateEnhancedDesign(request: DesignGenerationRequest): Promise<PostVariant> {\r\n    try {\r\n      // Try Revo 1.5 enhanced two-step design generation first\r\n      const { generateRevo15EnhancedDesign } = await import('@/ai/revo-1.5-enhanced-design');\r\n\r\n      // Prepare image text\r\n      let imageText: string;\r\n      if (typeof request.imageText === 'string') {\r\n        imageText = request.imageText;\r\n      } else {\r\n        // Enhanced text combination for Revo 1.5\r\n        const components = [request.imageText.catchyWords];\r\n        if (request.imageText.subheadline) {\r\n          components.push(request.imageText.subheadline);\r\n        }\r\n        if (request.imageText.callToAction) {\r\n          components.push(request.imageText.callToAction);\r\n        }\r\n        imageText = components.join('\\n');\r\n      }\r\n\r\n\r\n      // Generate enhanced design using two-step process\r\n      const result = await generateRevo15EnhancedDesign({\r\n        businessType: request.businessType,\r\n        platform: request.platform,\r\n        visualStyle: request.visualStyle,\r\n        imageText,\r\n        brandProfile: request.brandProfile,\r\n        brandConsistency: request.brandConsistency,\r\n        artifactInstructions: request.artifactInstructions,\r\n        includePeopleInDesigns: true,\r\n        useLocalLanguage: false\r\n      });\r\n\r\n\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: result.imageUrl,\r\n        caption: imageText,\r\n        hashtags: []\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Fallback to original enhanced design\r\n      return this.generateOriginalEnhancedDesign(request);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Original enhanced design generation (fallback for two-step process)\r\n   */\r\n  private async generateOriginalEnhancedDesign(request: DesignGenerationRequest): Promise<PostVariant> {\r\n    try {\r\n      // Try original enhanced design generation\r\n      const { generateEnhancedDesign } = await import('@/ai/gemini-2.5-design');\r\n\r\n      // Prepare image text\r\n      let imageText: string;\r\n      if (typeof request.imageText === 'string') {\r\n        imageText = request.imageText;\r\n      } else {\r\n        // Enhanced text combination for Revo 1.5\r\n        const components = [request.imageText.catchyWords];\r\n        if (request.imageText.subheadline) {\r\n          components.push(request.imageText.subheadline);\r\n        }\r\n        if (request.imageText.callToAction) {\r\n          components.push(request.imageText.callToAction);\r\n        }\r\n        imageText = components.join('\\n');\r\n      }\r\n\r\n\r\n      // Generate enhanced design\r\n      const result = await generateEnhancedDesign({\r\n        businessType: request.businessType,\r\n        platform: request.platform,\r\n        visualStyle: request.visualStyle,\r\n        imageText,\r\n        brandProfile: request.brandProfile,\r\n        brandConsistency: request.brandConsistency,\r\n        artifactInstructions: request.artifactInstructions,\r\n      });\r\n\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: result.imageUrl,\r\n        caption: imageText,\r\n        hashtags: []\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Fallback to basic generation\r\n      return this.generateFallbackDesign(request);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Basic fallback design generation\r\n   */\r\n  private async generateFallbackDesign(request: DesignGenerationRequest): Promise<PostVariant> {\r\n    try {\r\n      const { generatePostFromProfile } = await import('@/ai/flows/generate-post-from-profile');\r\n\r\n      // Prepare image text\r\n      let imageText: string;\r\n      if (typeof request.imageText === 'string') {\r\n        imageText = request.imageText;\r\n      } else {\r\n        imageText = request.imageText.catchyWords;\r\n        if (request.imageText.subheadline) {\r\n          imageText += '\\n' + request.imageText.subheadline;\r\n        }\r\n      }\r\n\r\n      // Create generation parameters\r\n      const generationParams = {\r\n        businessType: request.businessType,\r\n        location: request.brandProfile.location || '',\r\n        writingTone: request.brandProfile.writingTone || 'professional',\r\n        contentThemes: request.brandProfile.contentThemes || '',\r\n        visualStyle: request.visualStyle,\r\n        logoDataUrl: request.brandProfile.logoDataUrl,\r\n        designExamples: request.brandConsistency?.strictConsistency ?\r\n          (request.brandProfile.designExamples || []) : [],\r\n        primaryColor: request.brandProfile.primaryColor,\r\n        accentColor: request.brandProfile.accentColor,\r\n        backgroundColor: request.brandProfile.backgroundColor,\r\n        dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n        currentDate: new Date().toLocaleDateString('en-US', {\r\n          year: 'numeric', month: 'long', day: 'numeric'\r\n        }),\r\n        variants: [{\r\n          platform: request.platform,\r\n          aspectRatio: this.getOptimalAspectRatio(request.platform),\r\n        }],\r\n        services: '',\r\n        targetAudience: request.brandProfile.targetAudience || '',\r\n        keyFeatures: '',\r\n        competitiveAdvantages: '',\r\n        brandConsistency: request.brandConsistency || {\r\n          strictConsistency: false,\r\n          followBrandColors: true\r\n        }\r\n      };\r\n\r\n      const result = await generatePostFromProfile(generationParams);\r\n\r\n      if (result.variants && result.variants.length > 0) {\r\n        return {\r\n          ...result.variants[0],\r\n          caption: imageText,\r\n          hashtags: result.hashtags || []\r\n        };\r\n      }\r\n\r\n      // Final fallback\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: '',\r\n        caption: imageText,\r\n        hashtags: []\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      return {\r\n        platform: request.platform,\r\n        imageUrl: '',\r\n        caption: typeof request.imageText === 'string' ?\r\n          request.imageText : request.imageText.catchyWords,\r\n        hashtags: []\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get optimal aspect ratio for platform (Revo 1.5 supports multiple)\r\n   */\r\n  private getOptimalAspectRatio(platform: string): string {\r\n    switch (platform) {\r\n      case 'Instagram':\r\n        return '1:1'; // Can also support 9:16 for stories\r\n      case 'Facebook':\r\n        return '16:9';\r\n      case 'Twitter':\r\n        return '16:9';\r\n      case 'LinkedIn':\r\n        return '16:9';\r\n      default:\r\n        return '1:1';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate design generation request for Revo 1.5\r\n   */\r\n  private validateRequest(request: DesignGenerationRequest): boolean {\r\n    // Check required fields\r\n    if (!request.businessType || !request.platform || !request.brandProfile) {\r\n      return false;\r\n    }\r\n\r\n    // Check image text\r\n    if (!request.imageText) {\r\n      return false;\r\n    }\r\n\r\n    // Revo 1.5 supports multiple aspect ratios and artifacts\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced quality score for generated design\r\n   */\r\n  private calculateEnhancedQualityScore(variant: PostVariant): number {\r\n    let score = 6; // Higher base score for Revo 1.5\r\n\r\n    // Image generation success\r\n    if (variant.imageUrl && variant.imageUrl.length > 0) {\r\n      score += 2;\r\n    }\r\n\r\n    // Caption quality\r\n    if (variant.caption && variant.caption.length > 10) {\r\n      score += 0.5;\r\n    }\r\n    if (variant.caption && variant.caption.length > 50) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Hashtags presence and quality\r\n    if (variant.hashtags && variant.hashtags.length > 0) {\r\n      score += 0.5;\r\n    }\r\n    if (variant.hashtags && variant.hashtags.length >= 5) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Platform optimization\r\n    if (variant.platform) {\r\n      score += 0.5;\r\n    }\r\n\r\n    // Revo 1.5 can achieve higher quality scores\r\n    return Math.min(score, 9);\r\n  }\r\n\r\n  /**\r\n   * Health check for enhanced design generator\r\n   */\r\n  async healthCheck(): Promise<boolean> {\r\n    try {\r\n      // Check if we can access enhanced AI services\r\n      const hasGeminiKey = !!(\r\n        process.env.GEMINI_API_KEY ||\r\n        process.env.GOOGLE_API_KEY ||\r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n\r\n      const hasOpenAIKey = !!process.env.OPENAI_API_KEY;\r\n\r\n      return hasGeminiKey || hasOpenAIKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get generator-specific information\r\n   */\r\n  getGeneratorInfo() {\r\n    return {\r\n      modelId: this.modelId,\r\n      type: 'design',\r\n      capabilities: [\r\n        'Two-step enhanced design process',\r\n        'Gemini 2.5 Flash strategic planning',\r\n        'Gemini 2.5 Flash Image Preview generation',\r\n        'Premium image generation quality',\r\n        'Multiple aspect ratios (1:1, 16:9, 9:16)',\r\n        'Advanced brand integration',\r\n        'Artifact support',\r\n        'Superior text overlay',\r\n        'Advanced color harmony',\r\n        'Layout optimization',\r\n        'Platform-specific optimization',\r\n        'Strategic design planning',\r\n        'Professional visual depth'\r\n      ],\r\n      limitations: [\r\n        'Higher credit cost (2x)',\r\n        'Longer processing times (two-step process)',\r\n        'Requires more system resources',\r\n        'Requires both Gemini models available'\r\n      ],\r\n      supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\r\n      supportedAspectRatios: ['1:1', '16:9', '9:16'],\r\n      averageProcessingTime: '25-45 seconds',\r\n      qualityRange: '8-9.8/10',\r\n      costPerGeneration: 2,\r\n      resolution: '1024x1024 to 2048x2048',\r\n      enhancedFeatures: {\r\n        twoStepProcess: true,\r\n        strategicPlanning: true,\r\n        premiumGeneration: true,\r\n        multipleAspectRatios: true,\r\n        artifactSupport: true,\r\n        advancedStyling: true,\r\n        brandConsistencyAdvanced: true,\r\n        qualityOptimization: true,\r\n        textOverlayAdvanced: true,\r\n        gemini25FlashPlanning: true,\r\n        gemini25FlashImagePreview: true\r\n      },\r\n      models: {\r\n        planning: 'gemini-2.5-flash',\r\n        generation: 'gemini-2.5-flash-image-preview'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AASM,MAAM;IACM,UAAU,WAAW;IAEtC;;GAEC,GACD,MAAM,eAAe,OAAgC,EAA4C;QAC/F,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YAEF,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,wDAAwD;YACxD,MAAM,eAAe,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAEvD,MAAM,iBAAiB,KAAK,GAAG,KAAK;YACpC,MAAM,eAAe,IAAI,CAAC,6BAA6B,CAAC;YAGxD,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA;oBACA,aAAa;oBACb,qBAAqB;wBACnB;wBACA;wBACA;wBACA;wBACA;2BACI,QAAQ,oBAAoB,GAAG;4BAAC;yBAAuB,GAAG,EAAE;qBACjE;gBACH;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,UAAU;oBACR,SAAS,IAAI,CAAC,OAAO;oBACrB;oBACA,cAAc;oBACd,aAAa;oBACb,qBAAqB,EAAE;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAc,uBAAuB,OAAgC,EAAwB;QAC3F,IAAI;YACF,yDAAyD;YACzD,MAAM,EAAE,4BAA4B,EAAE,GAAG;YAEzC,qBAAqB;YACrB,IAAI;YACJ,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,YAAY,QAAQ,SAAS;YAC/B,OAAO;gBACL,yCAAyC;gBACzC,MAAM,aAAa;oBAAC,QAAQ,SAAS,CAAC,WAAW;iBAAC;gBAClD,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE;oBACjC,WAAW,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW;gBAC/C;gBACA,IAAI,QAAQ,SAAS,CAAC,YAAY,EAAE;oBAClC,WAAW,IAAI,CAAC,QAAQ,SAAS,CAAC,YAAY;gBAChD;gBACA,YAAY,WAAW,IAAI,CAAC;YAC9B;YAGA,kDAAkD;YAClD,MAAM,SAAS,MAAM,6BAA6B;gBAChD,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW;gBAChC;gBACA,cAAc,QAAQ,YAAY;gBAClC,kBAAkB,QAAQ,gBAAgB;gBAC1C,sBAAsB,QAAQ,oBAAoB;gBAClD,wBAAwB;gBACxB,kBAAkB;YACpB;YAGA,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,OAAO,QAAQ;gBACzB,SAAS;gBACT,UAAU,EAAE;YACd;QAEF,EAAE,OAAO,OAAO;YAEd,uCAAuC;YACvC,OAAO,IAAI,CAAC,8BAA8B,CAAC;QAC7C;IACF;IAEA;;GAEC,GACD,MAAc,+BAA+B,OAAgC,EAAwB;QACnG,IAAI;YACF,0CAA0C;YAC1C,MAAM,EAAE,sBAAsB,EAAE,GAAG;YAEnC,qBAAqB;YACrB,IAAI;YACJ,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,YAAY,QAAQ,SAAS;YAC/B,OAAO;gBACL,yCAAyC;gBACzC,MAAM,aAAa;oBAAC,QAAQ,SAAS,CAAC,WAAW;iBAAC;gBAClD,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE;oBACjC,WAAW,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW;gBAC/C;gBACA,IAAI,QAAQ,SAAS,CAAC,YAAY,EAAE;oBAClC,WAAW,IAAI,CAAC,QAAQ,SAAS,CAAC,YAAY;gBAChD;gBACA,YAAY,WAAW,IAAI,CAAC;YAC9B;YAGA,2BAA2B;YAC3B,MAAM,SAAS,MAAM,uBAAuB;gBAC1C,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW;gBAChC;gBACA,cAAc,QAAQ,YAAY;gBAClC,kBAAkB,QAAQ,gBAAgB;gBAC1C,sBAAsB,QAAQ,oBAAoB;YACpD;YAEA,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,OAAO,QAAQ;gBACzB,SAAS;gBACT,UAAU,EAAE;YACd;QAEF,EAAE,OAAO,OAAO;YAEd,+BAA+B;YAC/B,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;IACF;IAEA;;GAEC,GACD,MAAc,uBAAuB,OAAgC,EAAwB;QAC3F,IAAI;YACF,MAAM,EAAE,uBAAuB,EAAE,GAAG;YAEpC,qBAAqB;YACrB,IAAI;YACJ,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;gBACzC,YAAY,QAAQ,SAAS;YAC/B,OAAO;gBACL,YAAY,QAAQ,SAAS,CAAC,WAAW;gBACzC,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE;oBACjC,aAAa,OAAO,QAAQ,SAAS,CAAC,WAAW;gBACnD;YACF;YAEA,+BAA+B;YAC/B,MAAM,mBAAmB;gBACvB,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,YAAY,CAAC,QAAQ,IAAI;gBAC3C,aAAa,QAAQ,YAAY,CAAC,WAAW,IAAI;gBACjD,eAAe,QAAQ,YAAY,CAAC,aAAa,IAAI;gBACrD,aAAa,QAAQ,WAAW;gBAChC,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,gBAAgB,QAAQ,gBAAgB,EAAE,oBACvC,QAAQ,YAAY,CAAC,cAAc,IAAI,EAAE,GAAI,EAAE;gBAClD,cAAc,QAAQ,YAAY,CAAC,YAAY;gBAC/C,aAAa,QAAQ,YAAY,CAAC,WAAW;gBAC7C,iBAAiB,QAAQ,YAAY,CAAC,eAAe;gBACrD,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAAE,SAAS;gBAAO;gBACpE,aAAa,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAClD,MAAM;oBAAW,OAAO;oBAAQ,KAAK;gBACvC;gBACA,UAAU;oBAAC;wBACT,UAAU,QAAQ,QAAQ;wBAC1B,aAAa,IAAI,CAAC,qBAAqB,CAAC,QAAQ,QAAQ;oBAC1D;iBAAE;gBACF,UAAU;gBACV,gBAAgB,QAAQ,YAAY,CAAC,cAAc,IAAI;gBACvD,aAAa;gBACb,uBAAuB;gBACvB,kBAAkB,QAAQ,gBAAgB,IAAI;oBAC5C,mBAAmB;oBACnB,mBAAmB;gBACrB;YACF;YAEA,MAAM,SAAS,MAAM,wBAAwB;YAE7C,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACjD,OAAO;oBACL,GAAG,OAAO,QAAQ,CAAC,EAAE;oBACrB,SAAS;oBACT,UAAU,OAAO,QAAQ,IAAI,EAAE;gBACjC;YACF;YAEA,iBAAiB;YACjB,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU;gBACV,SAAS;gBACT,UAAU,EAAE;YACd;QAEF,EAAE,OAAO,OAAO;YAEd,OAAO;gBACL,UAAU,QAAQ,QAAQ;gBAC1B,UAAU;gBACV,SAAS,OAAO,QAAQ,SAAS,KAAK,WACpC,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,WAAW;gBACnD,UAAU,EAAE;YACd;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,QAAgB,EAAU;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,oCAAoC;YACpD,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAgC,EAAW;QACjE,wBAAwB;QACxB,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,YAAY,EAAE;YACvE,OAAO;QACT;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO;QACT;QAEA,yDAAyD;QACzD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,8BAA8B,OAAoB,EAAU;QAClE,IAAI,QAAQ,GAAG,iCAAiC;QAEhD,2BAA2B;QAC3B,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS;QACX;QAEA,kBAAkB;QAClB,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,IAAI;YAClD,SAAS;QACX;QACA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,IAAI;YAClD,SAAS;QACX;QAEA,gCAAgC;QAChC,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnD,SAAS;QACX;QACA,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,IAAI,GAAG;YACpD,SAAS;QACX;QAEA,wBAAwB;QACxB,IAAI,QAAQ,QAAQ,EAAE;YACpB,SAAS;QACX;QAEA,6CAA6C;QAC7C,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,8CAA8C;YAC9C,MAAM,eAAe,CAAC,CAAC,CACrB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,MAAM,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAEjD,OAAO,gBAAgB;QACzB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,OAAO;YACL,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;YACD,oBAAoB;gBAAC;gBAAa;gBAAY;gBAAW;aAAW;YACpE,uBAAuB;gBAAC;gBAAO;gBAAQ;aAAO;YAC9C,uBAAuB;YACvB,cAAc;YACd,mBAAmB;YACnB,YAAY;YACZ,kBAAkB;gBAChB,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;gBACnB,sBAAsB;gBACtB,iBAAiB;gBACjB,iBAAiB;gBACjB,0BAA0B;gBAC1B,qBAAqB;gBACrB,qBAAqB;gBACrB,uBAAuB;gBACvB,2BAA2B;YAC7B;YACA,QAAQ;gBACN,UAAU;gBACV,YAAY;YACd;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 5378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/models/versions/revo-1.5/index.ts"], "sourcesContent": ["/**\r\n * Revo 1.5 Model Implementation\r\n * Enhanced Model - Advanced Features\r\n */\r\n\r\nimport type {\r\n  IModelImplementation,\r\n  IContentGenerator,\r\n  IDesignGenerator,\r\n  ContentGenerationRequest,\r\n  DesignGenerationRequest,\r\n  GenerationResponse\r\n} from '../../types/model-types';\r\nimport { getModelConfig } from '../../config/model-configs';\r\nimport { Revo15ContentGenerator } from './content-generator';\r\nimport { Revo15DesignGenerator } from './design-generator';\r\n\r\nexport class Revo15Implementation implements IModelImplementation {\r\n  public readonly model = getModelConfig('revo-1.5');\r\n  public readonly contentGenerator: IContentGenerator;\r\n  public readonly designGenerator: IDesignGenerator;\r\n\r\n  constructor() {\r\n    this.contentGenerator = new Revo15ContentGenerator();\r\n    this.designGenerator = new Revo15DesignGenerator();\r\n  }\r\n\r\n  /**\r\n   * Check if the model is available and ready to use\r\n   */\r\n  async isAvailable(): Promise<boolean> {\r\n    try {\r\n      // Check if Gemini 2.5 (preferred) or fallback services are available\r\n      const hasGeminiKey = !!(\r\n        process.env.GEMINI_API_KEY ||\r\n        process.env.GOOGLE_API_KEY ||\r\n        process.env.GOOGLE_GENAI_API_KEY\r\n      );\r\n\r\n      const hasOpenAIKey = !!process.env.OPENAI_API_KEY;\r\n\r\n      // Revo 1.5 needs at least one advanced AI service\r\n      return hasGeminiKey || hasOpenAIKey;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate a generation request for this model\r\n   */\r\n  validateRequest(request: ContentGenerationRequest | DesignGenerationRequest): boolean {\r\n    try {\r\n      // Basic validation\r\n      if (!request || !request.modelId) {\r\n        return false;\r\n      }\r\n\r\n      // Check if this is the correct model\r\n      if (request.modelId !== 'revo-1.5') {\r\n        return false;\r\n      }\r\n\r\n      // Content generation validation\r\n      if ('profile' in request) {\r\n        const contentRequest = request as ContentGenerationRequest;\r\n        return !!(\r\n          contentRequest.profile &&\r\n          contentRequest.platform &&\r\n          contentRequest.profile.businessType\r\n        );\r\n      }\r\n\r\n      // Design generation validation\r\n      if ('businessType' in request) {\r\n        const designRequest = request as DesignGenerationRequest;\r\n        return !!(\r\n          designRequest.businessType &&\r\n          designRequest.platform &&\r\n          designRequest.visualStyle &&\r\n          designRequest.brandProfile\r\n        );\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get model-specific information\r\n   */\r\n  getModelInfo() {\r\n    return {\r\n      id: this.model.id,\r\n      name: this.model.name,\r\n      version: this.model.version,\r\n      description: this.model.description,\r\n      status: this.model.status,\r\n      capabilities: this.model.capabilities,\r\n      pricing: this.model.pricing,\r\n      features: this.model.features,\r\n      strengths: [\r\n        'Advanced AI engine with superior capabilities',\r\n        'Enhanced content generation algorithms',\r\n        'Superior quality control and consistency',\r\n        'Professional design generation',\r\n        'Improved brand integration',\r\n        'Real-time context and trending topics',\r\n        'Full artifact support',\r\n        'Multiple aspect ratios'\r\n      ],\r\n      limitations: [\r\n        'Higher credit cost than Revo 1.0',\r\n        'Longer processing times',\r\n        'No video generation (coming in 2.0)',\r\n        'Requires more system resources'\r\n      ],\r\n      bestUseCases: [\r\n        'Growing businesses',\r\n        'Marketing agencies',\r\n        'Content creators',\r\n        'Professional brands',\r\n        'Users wanting enhanced quality',\r\n        'Artifact-based workflows',\r\n        'Multi-platform campaigns'\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get performance metrics for this model\r\n   */\r\n  async getPerformanceMetrics() {\r\n    return {\r\n      modelId: this.model.id,\r\n      averageProcessingTime: 25000, // 25 seconds\r\n      successRate: 0.92, // 92% success rate\r\n      averageQualityScore: 8.1,\r\n      costEfficiency: 'medium',\r\n      reliability: 'very good',\r\n      userSatisfaction: 4.4, // out of 5\r\n      lastUpdated: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Health check for this specific model\r\n   */\r\n  async healthCheck(): Promise<{ healthy: boolean; details: any }> {\r\n    try {\r\n      const isAvailable = await this.isAvailable();\r\n      const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;\r\n      const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;\r\n\r\n      const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;\r\n\r\n      return {\r\n        healthy,\r\n        details: {\r\n          modelAvailable: isAvailable,\r\n          contentGenerator: contentGeneratorHealthy,\r\n          designGenerator: designGeneratorHealthy,\r\n          enhancedFeaturesEnabled: true,\r\n          artifactSupportEnabled: true,\r\n          realTimeContextEnabled: true,\r\n          timestamp: new Date().toISOString()\r\n        }\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        healthy: false,\r\n        details: {\r\n          error: error instanceof Error ? error.message : 'Unknown error',\r\n          timestamp: new Date().toISOString()\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get enhanced features specific to Revo 1.5\r\n   */\r\n  getEnhancedFeatures() {\r\n    return {\r\n      artifactSupport: {\r\n        enabled: true,\r\n        supportedTypes: ['image', 'text', 'reference'],\r\n        maxArtifacts: 5,\r\n        features: ['exact-use', 'reference', 'text-overlay']\r\n      },\r\n      realTimeContext: {\r\n        enabled: true,\r\n        features: ['weather', 'events', 'trending-topics', 'local-optimization']\r\n      },\r\n      advancedDesign: {\r\n        enabled: true,\r\n        aspectRatios: ['1:1', '16:9', '9:16'],\r\n        qualityEnhancements: ['color-harmony', 'layout-optimization', 'brand-consistency'],\r\n        textOverlay: 'advanced'\r\n      },\r\n      contentEnhancements: {\r\n        enabled: true,\r\n        features: ['content-variants', 'hashtag-analysis', 'market-intelligence'],\r\n        qualityLevel: 'enhanced'\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n// Export generators for direct use if needed\r\nexport { Revo15ContentGenerator } from './content-generator';\r\nexport { Revo15DesignGenerator } from './design-generator';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAUD;AACA;AACA;;;;AAEO,MAAM;IACK,QAAQ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;IACnC,iBAAoC;IACpC,gBAAkC;IAElD,aAAc;QACZ,IAAI,CAAC,gBAAgB,GAAG,IAAI,yKAAA,CAAA,yBAAsB;QAClD,IAAI,CAAC,eAAe,GAAG,IAAI,wKAAA,CAAA,wBAAqB;IAClD;IAEA;;GAEC,GACD,MAAM,cAAgC;QACpC,IAAI;YACF,qEAAqE;YACrE,MAAM,eAAe,CAAC,CAAC,CACrB,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,oBAAoB,AAClC;YAEA,MAAM,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;YAEjD,kDAAkD;YAClD,OAAO,gBAAgB;QACzB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,gBAAgB,OAA2D,EAAW;QACpF,IAAI;YACF,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAAE;gBAChC,OAAO;YACT;YAEA,qCAAqC;YACrC,IAAI,QAAQ,OAAO,KAAK,YAAY;gBAClC,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAI,aAAa,SAAS;gBACxB,MAAM,iBAAiB;gBACvB,OAAO,CAAC,CAAC,CACP,eAAe,OAAO,IACtB,eAAe,QAAQ,IACvB,eAAe,OAAO,CAAC,YAAY,AACrC;YACF;YAEA,+BAA+B;YAC/B,IAAI,kBAAkB,SAAS;gBAC7B,MAAM,gBAAgB;gBACtB,OAAO,CAAC,CAAC,CACP,cAAc,YAAY,IAC1B,cAAc,QAAQ,IACtB,cAAc,WAAW,IACzB,cAAc,YAAY,AAC5B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,eAAe;QACb,OAAO;YACL,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACjB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,cAAc,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB;QAC5B,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YACtB,uBAAuB;YACvB,aAAa;YACb,qBAAqB;YACrB,gBAAgB;YAChB,aAAa;YACb,kBAAkB;YAClB,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,cAA2D;QAC/D,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW;YAC1C,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,QAAQ;YAC/E,MAAM,yBAAyB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,QAAQ;YAE7E,MAAM,UAAU,eAAe,2BAA2B;YAE1D,OAAO;gBACL;gBACA,SAAS;oBACP,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,yBAAyB;oBACzB,wBAAwB;oBACxB,wBAAwB;oBACxB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;oBACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF;IACF;IAEA;;GAEC,GACD,sBAAsB;QACpB,OAAO;YACL,iBAAiB;gBACf,SAAS;gBACT,gBAAgB;oBAAC;oBAAS;oBAAQ;iBAAY;gBAC9C,cAAc;gBACd,UAAU;oBAAC;oBAAa;oBAAa;iBAAe;YACtD;YACA,iBAAiB;gBACf,SAAS;gBACT,UAAU;oBAAC;oBAAW;oBAAU;oBAAmB;iBAAqB;YAC1E;YACA,gBAAgB;gBACd,SAAS;gBACT,cAAc;oBAAC;oBAAO;oBAAQ;iBAAO;gBACrC,qBAAqB;oBAAC;oBAAiB;oBAAuB;iBAAoB;gBAClF,aAAa;YACf;YACA,qBAAqB;gBACnB,SAAS;gBACT,UAAU;oBAAC;oBAAoB;oBAAoB;iBAAsB;gBACzE,cAAc;YAChB;QACF;IACF;AACF", "debugId": null}}]}