module.exports = {

"[project]/src/app/actions.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_app_b5111a23._.js",
  "server/chunks/ssr/0f475_dist_build_webpack_loaders_next-flight-loader_action-client-wrapper_e7348fed.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/actions.ts [app-ssr] (ecmascript)");
    });
});
}}),

};