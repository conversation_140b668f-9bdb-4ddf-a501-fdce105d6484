module.exports = {

"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * RSS Feed Service for Trending Content & Social Media Insights
 * Fetches and parses RSS feeds to extract trending topics, keywords, and themes
 */ __turbopack_context__.s({
    "RSSFeedService": (()=>RSSFeedService),
    "rssService": (()=>rssService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/xml2js/lib/xml2js.js [app-route] (ecmascript)");
;
class RSSFeedService {
    cache = new Map();
    cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000;
    feedUrls = {
        // Social Media & Marketing Trends
        socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,
        socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,
        bufferBlog: process.env.RSS_BUFFER_BLOG,
        hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,
        sproutSocial: process.env.RSS_SPROUT_SOCIAL,
        laterBlog: process.env.RSS_LATER_BLOG,
        // Trending Topics & News
        googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,
        redditPopular: process.env.RSS_REDDIT_POPULAR,
        buzzfeed: process.env.RSS_BUZZFEED,
        twitterTrending: process.env.RSS_TWITTER_TRENDING,
        // Business & Marketing
        hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,
        contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,
        marketingProfs: process.env.RSS_MARKETING_PROFS,
        marketingLand: process.env.RSS_MARKETING_LAND,
        neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,
        // Industry News
        techCrunch: process.env.RSS_TECHCRUNCH,
        mashable: process.env.RSS_MASHABLE,
        theVerge: process.env.RSS_THE_VERGE,
        wired: process.env.RSS_WIRED,
        // Platform-Specific
        instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,
        facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,
        linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,
        youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,
        tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,
        // Analytics & Data
        googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,
        hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,
        // Design & Creative
        canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,
        adobeBlog: process.env.RSS_ADOBE_BLOG,
        creativeBloq: process.env.RSS_CREATIVE_BLOQ,
        // Seasonal & Events
        eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG
    };
    /**
   * Fetch and parse a single RSS feed
   */ async fetchRSSFeed(url, sourceName) {
        try {
            // Check cache first
            const cached = this.cache.get(url);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Nevis-AI-Content-Generator/1.0'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const xmlData = await response.text();
            const parsed = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseStringPromise"])(xmlData);
            const articles = [];
            const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];
            const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');
            for (const item of items.slice(0, maxArticles)){
                const article = {
                    title: this.extractText(item.title),
                    description: this.extractText(item.description || item.summary),
                    link: this.extractText(item.link || item.id),
                    pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),
                    category: this.extractText(item.category),
                    keywords: this.extractKeywords(this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)),
                    source: sourceName
                };
                articles.push(article);
            }
            // Cache the results
            this.cache.set(url, {
                data: articles,
                timestamp: Date.now()
            });
            return articles;
        } catch (error) {
            return [];
        }
    }
    /**
   * Extract text content from RSS item fields
   */ extractText(field) {
        if (!field) return '';
        if (typeof field === 'string') return field;
        if (Array.isArray(field) && field.length > 0) {
            return typeof field[0] === 'string' ? field[0] : field[0]._ || '';
        }
        if (typeof field === 'object' && field._) return field._;
        return '';
    }
    /**
   * Extract keywords from text content
   */ extractKeywords(text) {
        if (!text) return [];
        // Remove HTML tags and normalize text
        const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
        // Extract meaningful words (3+ characters, not common stop words)
        const stopWords = new Set([
            'the',
            'and',
            'for',
            'are',
            'but',
            'not',
            'you',
            'all',
            'can',
            'had',
            'her',
            'was',
            'one',
            'our',
            'out',
            'day',
            'get',
            'has',
            'him',
            'his',
            'how',
            'its',
            'may',
            'new',
            'now',
            'old',
            'see',
            'two',
            'who',
            'boy',
            'did',
            'she',
            'use',
            'way',
            'will',
            'with'
        ]);
        const words = cleanText.split(' ').filter((word)=>word.length >= 3 && !stopWords.has(word)).slice(0, 10); // Limit to top 10 keywords per article
        return Array.from(new Set(words)); // Remove duplicates
    }
    /**
   * Fetch all RSS feeds and return trending data
   */ async getTrendingData() {
        const allArticles = [];
        const fetchPromises = [];
        // Fetch all feeds concurrently
        for (const [sourceName, url] of Object.entries(this.feedUrls)){
            if (url) {
                fetchPromises.push(this.fetchRSSFeed(url, sourceName));
            }
        }
        const results = await Promise.allSettled(fetchPromises);
        // Collect all successful results
        results.forEach((result)=>{
            if (result.status === 'fulfilled') {
                allArticles.push(...result.value);
            }
        });
        // Sort articles by publication date (newest first)
        allArticles.sort((a, b)=>b.pubDate.getTime() - a.pubDate.getTime());
        // Extract trending keywords and topics
        const allKeywords = [];
        const allTopics = [];
        const allThemes = [];
        allArticles.forEach((article)=>{
            allKeywords.push(...article.keywords);
            if (article.title) allTopics.push(article.title);
            if (article.category) allThemes.push(article.category);
        });
        // Count frequency and get top items
        const keywordCounts = this.getTopItems(allKeywords, 50);
        const topicCounts = this.getTopItems(allTopics, 30);
        const themeCounts = this.getTopItems(allThemes, 20);
        return {
            keywords: keywordCounts,
            topics: topicCounts,
            themes: themeCounts,
            articles: allArticles.slice(0, 100),
            lastUpdated: new Date()
        };
    }
    /**
   * Get top items by frequency
   */ getTopItems(items, limit) {
        const counts = new Map();
        items.forEach((item)=>{
            const normalized = item.toLowerCase().trim();
            if (normalized.length >= 3) {
                counts.set(normalized, (counts.get(normalized) || 0) + 1);
            }
        });
        return Array.from(counts.entries()).sort((a, b)=>b[1] - a[1]).slice(0, limit).map(([item])=>item);
    }
    /**
   * Get trending keywords for a specific category
   */ async getTrendingKeywordsByCategory(category) {
        const trendingData = await this.getTrendingData();
        const categoryFeeds = {
            social: [
                'socialMediaToday',
                'socialMediaExaminer',
                'bufferBlog',
                'hootsuiteBlogs'
            ],
            business: [
                'hubspotMarketing',
                'contentMarketingInstitute',
                'marketingProfs'
            ],
            tech: [
                'techCrunch',
                'theVerge',
                'wired'
            ],
            design: [
                'canvaDesignSchool',
                'adobeBlog',
                'creativeBloq'
            ]
        };
        const categoryArticles = trendingData.articles.filter((article)=>categoryFeeds[category].includes(article.source));
        const keywords = [];
        categoryArticles.forEach((article)=>keywords.push(...article.keywords));
        return this.getTopItems(keywords, 20);
    }
}
const rssService = new RSSFeedService();
}}),
"[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Content Enhancer
 * Integrates RSS feed data to enhance content generation with trending topics
 */ __turbopack_context__.s({
    "TrendingContentEnhancer": (()=>TrendingContentEnhancer),
    "trendingEnhancer": (()=>trendingEnhancer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)");
;
class TrendingContentEnhancer {
    trendingCache = null;
    lastCacheUpdate = 0;
    cacheTimeout = 30 * 60 * 1000;
    /**
   * Get fresh trending data with caching
   */ async getTrendingData() {
        const now = Date.now();
        if (this.trendingCache && now - this.lastCacheUpdate < this.cacheTimeout) {
            return this.trendingCache;
        }
        this.trendingCache = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
        this.lastCacheUpdate = now;
        return this.trendingCache;
    }
    /**
   * Get trending enhancement data for content generation
   */ async getTrendingEnhancement(context = {}) {
        try {
            const trendingData = await this.getTrendingData();
            // Filter and prioritize based on context
            const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);
            const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);
            // Generate hashtags from trending keywords
            const hashtags = this.generateHashtags(relevantKeywords, context);
            // Extract seasonal themes
            const seasonalThemes = this.extractSeasonalThemes(trendingData);
            // Extract industry-specific buzz
            const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);
            return {
                keywords: relevantKeywords.slice(0, 15),
                topics: relevantTopics.slice(0, 10),
                hashtags: hashtags.slice(0, 10),
                seasonalThemes: seasonalThemes.slice(0, 5),
                industryBuzz: industryBuzz.slice(0, 8)
            };
        } catch (error) {
            // Return fallback data
            return {
                keywords: [
                    'trending',
                    'viral',
                    'popular',
                    'latest',
                    'new'
                ],
                topics: [
                    'social media trends',
                    'digital marketing',
                    'content creation'
                ],
                hashtags: [
                    '#trending',
                    '#viral',
                    '#socialmedia',
                    '#marketing'
                ],
                seasonalThemes: [],
                industryBuzz: []
            };
        }
    }
    /**
   * Filter keywords based on context relevance
   */ filterKeywordsByContext(keywords, context) {
        const platformKeywords = {
            instagram: [
                'visual',
                'photo',
                'story',
                'reel',
                'aesthetic',
                'lifestyle'
            ],
            facebook: [
                'community',
                'share',
                'connect',
                'family',
                'local',
                'event'
            ],
            twitter: [
                'news',
                'update',
                'breaking',
                'discussion',
                'opinion',
                'thread'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'industry',
                'networking',
                'leadership'
            ],
            tiktok: [
                'viral',
                'trend',
                'challenge',
                'creative',
                'fun',
                'entertainment'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'design',
                'home',
                'style'
            ]
        };
        const businessKeywords = {
            restaurant: [
                'food',
                'dining',
                'menu',
                'chef',
                'cuisine',
                'taste',
                'fresh'
            ],
            retail: [
                'shopping',
                'sale',
                'fashion',
                'style',
                'product',
                'deal',
                'new'
            ],
            fitness: [
                'health',
                'workout',
                'training',
                'wellness',
                'strength',
                'motivation'
            ],
            beauty: [
                'skincare',
                'makeup',
                'beauty',
                'glow',
                'treatment',
                'style'
            ],
            tech: [
                'innovation',
                'digital',
                'technology',
                'software',
                'app',
                'solution'
            ],
            healthcare: [
                'health',
                'wellness',
                'care',
                'treatment',
                'medical',
                'patient'
            ]
        };
        let filtered = [
            ...keywords
        ];
        // Boost platform-relevant keywords
        if (context.platform && platformKeywords[context.platform]) {
            const platformBoost = platformKeywords[context.platform];
            filtered = filtered.sort((a, b)=>{
                const aBoost = platformBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = platformBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        // Boost business-relevant keywords
        if (context.businessType && businessKeywords[context.businessType]) {
            const businessBoost = businessKeywords[context.businessType];
            filtered = filtered.sort((a, b)=>{
                const aBoost = businessBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = businessBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        return filtered;
    }
    /**
   * Filter topics based on context relevance
   */ filterTopicsByContext(topics, context) {
        // Remove topics that are too generic or not suitable for social media
        const filtered = topics.filter((topic)=>{
            const lower = topic.toLowerCase();
            return !lower.includes('error') && !lower.includes('404') && !lower.includes('page not found') && lower.length > 10 && lower.length < 100;
        });
        return filtered;
    }
    /**
   * Generate relevant hashtags from keywords
   */ generateHashtags(keywords, context) {
        const hashtags = [];
        // Convert keywords to hashtags
        keywords.forEach((keyword)=>{
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        });
        // Add platform-specific hashtags
        const platformHashtags = {
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#picoftheday'
            ],
            facebook: [
                '#community',
                '#local',
                '#share',
                '#connect'
            ],
            twitter: [
                '#news',
                '#update',
                '#discussion',
                '#trending'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#career',
                '#networking'
            ],
            tiktok: [
                '#fyp',
                '#viral',
                '#trending',
                '#foryou'
            ],
            pinterest: [
                '#inspiration',
                '#ideas',
                '#diy',
                '#style'
            ]
        };
        if (context.platform && platformHashtags[context.platform]) {
            hashtags.push(...platformHashtags[context.platform]);
        }
        // Remove duplicates and return
        return Array.from(new Set(hashtags));
    }
    /**
   * Extract seasonal themes from trending data
   */ extractSeasonalThemes(trendingData) {
        const currentMonth = new Date().getMonth();
        const seasonalKeywords = {
            0: [
                'new year',
                'resolution',
                'fresh start',
                'winter'
            ],
            1: [
                'valentine',
                'love',
                'romance',
                'winter'
            ],
            2: [
                'spring',
                'march madness',
                'renewal',
                'growth'
            ],
            3: [
                'easter',
                'spring',
                'bloom',
                'fresh'
            ],
            4: [
                'mother\'s day',
                'spring',
                'flowers',
                'celebration'
            ],
            5: [
                'summer',
                'graduation',
                'father\'s day',
                'vacation'
            ],
            6: [
                'summer',
                'july 4th',
                'independence',
                'freedom'
            ],
            7: [
                'summer',
                'vacation',
                'back to school',
                'preparation'
            ],
            8: [
                'back to school',
                'fall',
                'autumn',
                'harvest'
            ],
            9: [
                'halloween',
                'october',
                'spooky',
                'fall'
            ],
            10: [
                'thanksgiving',
                'gratitude',
                'family',
                'harvest'
            ],
            11: [
                'christmas',
                'holiday',
                'winter',
                'celebration'
            ]
        };
        const currentSeasonalKeywords = seasonalKeywords[currentMonth] || [];
        const seasonalThemes = trendingData.keywords.filter((keyword)=>currentSeasonalKeywords.some((seasonal)=>keyword.toLowerCase().includes(seasonal.toLowerCase())));
        return seasonalThemes;
    }
    /**
   * Extract industry-specific buzz from trending data
   */ extractIndustryBuzz(trendingData, businessType) {
        if (!businessType) return [];
        const industryKeywords = {
            restaurant: [
                'food',
                'dining',
                'chef',
                'cuisine',
                'recipe',
                'restaurant',
                'menu'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'product',
                'brand',
                'sale',
                'deal'
            ],
            fitness: [
                'fitness',
                'workout',
                'health',
                'gym',
                'training',
                'wellness',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'cosmetics',
                'treatment',
                'spa'
            ],
            tech: [
                'technology',
                'tech',
                'digital',
                'software',
                'app',
                'innovation',
                'ai'
            ],
            healthcare: [
                'health',
                'medical',
                'healthcare',
                'wellness',
                'treatment',
                'care'
            ]
        };
        const relevantKeywords = industryKeywords[businessType] || [];
        const industryBuzz = trendingData.keywords.filter((keyword)=>relevantKeywords.some((industry)=>keyword.toLowerCase().includes(industry.toLowerCase())));
        return industryBuzz;
    }
    /**
   * Get trending prompt enhancement for AI content generation
   */ async getTrendingPromptEnhancement(context = {}) {
        const enhancement = await this.getTrendingEnhancement(context);
        const promptParts = [];
        if (enhancement.keywords.length > 0) {
            promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);
        }
        if (enhancement.seasonalThemes.length > 0) {
            promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);
        }
        if (enhancement.industryBuzz.length > 0) {
            promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);
        }
        if (enhancement.hashtags.length > 0) {
            promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);
        }
        return promptParts.join('\n');
    }
}
const trendingEnhancer = new TrendingContentEnhancer();
}}),
"[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Viral Hashtag Engine - Real-time trending hashtag generation
 * Integrates with RSS feeds and trending data to generate viral hashtags
 */ __turbopack_context__.s({
    "ViralHashtagEngine": (()=>ViralHashtagEngine),
    "viralHashtagEngine": (()=>viralHashtagEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
;
class ViralHashtagEngine {
    /**
   * Generate viral hashtag strategy using real-time trending data
   */ async generateViralHashtags(businessType, businessName, location, platform, services, targetAudience) {
        try {
            // Get trending data from RSS feeds and trending enhancer
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                businessType,
                location,
                platform,
                targetAudience
            });
            // Generate different hashtag categories
            const trending = await this.getTrendingHashtags(trendingData, businessType, platform);
            const viral = this.getViralHashtags(businessType, platform);
            const niche = this.getNicheHashtags(businessType, services);
            const location_tags = this.getLocationHashtags(location);
            const community = this.getCommunityHashtags(businessType, targetAudience);
            const seasonal = this.getSeasonalHashtags();
            const platform_tags = this.getPlatformHashtags(platform);
            // Combine and optimize for virality
            const total = this.optimizeForVirality([
                ...trending.slice(0, 4),
                ...viral.slice(0, 3),
                ...niche.slice(0, 2),
                ...location_tags.slice(0, 2),
                ...community.slice(0, 2),
                ...seasonal.slice(0, 1),
                ...platform_tags.slice(0, 1)
            ]);
            return {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total
            };
        } catch (error) {
            return this.getFallbackHashtags(businessType, location, platform);
        }
    }
    /**
   * Get trending hashtags from RSS data
   */ async getTrendingHashtags(trendingData, businessType, platform) {
        const hashtags = [
            ...trendingData.hashtags
        ];
        // Add business-relevant trending hashtags
        const businessTrending = this.getBusinessTrendingHashtags(businessType, platform);
        hashtags.push(...businessTrending);
        // Remove duplicates and return top trending
        return Array.from(new Set(hashtags)).slice(0, 8);
    }
    /**
   * Get high-engagement viral hashtags
   */ getViralHashtags(businessType, platform) {
        const viralHashtags = {
            general: [
                '#viral',
                '#trending',
                '#fyp',
                '#explore',
                '#discover',
                '#amazing',
                '#incredible',
                '#mustsee'
            ],
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#reels',
                '#explorepage'
            ],
            tiktok: [
                '#fyp',
                '#foryou',
                '#viral',
                '#trending',
                '#foryoupage'
            ],
            facebook: [
                '#viral',
                '#share',
                '#community',
                '#local',
                '#trending'
            ],
            twitter: [
                '#trending',
                '#viral',
                '#breaking',
                '#news',
                '#update'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#networking',
                '#career',
                '#industry'
            ]
        };
        const general = viralHashtags.general.sort(()=>0.5 - Math.random()).slice(0, 4);
        const platformSpecific = viralHashtags[platform.toLowerCase()] || [];
        return [
            ...general,
            ...platformSpecific.slice(0, 3)
        ];
    }
    /**
   * Get business-specific niche hashtags
   */ getNicheHashtags(businessType, services) {
        const nicheMap = {
            restaurant: [
                '#foodie',
                '#delicious',
                '#freshfood',
                '#localeats',
                '#foodlover',
                '#tasty',
                '#chef',
                '#dining'
            ],
            bakery: [
                '#freshbaked',
                '#artisan',
                '#homemade',
                '#bakery',
                '#pastry',
                '#bread',
                '#dessert',
                '#sweet'
            ],
            fitness: [
                '#fitness',
                '#workout',
                '#health',
                '#gym',
                '#strong',
                '#motivation',
                '#fitlife',
                '#training'
            ],
            beauty: [
                '#beauty',
                '#skincare',
                '#makeup',
                '#glam',
                '#selfcare',
                '#beautiful',
                '#style',
                '#cosmetics'
            ],
            tech: [
                '#tech',
                '#innovation',
                '#digital',
                '#software',
                '#technology',
                '#startup',
                '#coding',
                '#ai'
            ],
            retail: [
                '#shopping',
                '#fashion',
                '#style',
                '#sale',
                '#newcollection',
                '#boutique',
                '#trendy',
                '#deals'
            ]
        };
        const baseNiche = nicheMap[businessType.toLowerCase()] || [
            '#business',
            '#service',
            '#quality',
            '#professional'
        ];
        // Add service-specific hashtags if provided
        if (services) {
            const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            baseNiche.push(...serviceHashtags);
        }
        return baseNiche.slice(0, 6);
    }
    /**
   * Get location-based hashtags
   */ getLocationHashtags(location) {
        const locationParts = location.split(',').map((part)=>part.trim());
        const hashtags = [];
        locationParts.forEach((part)=>{
            const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '');
            if (cleanLocation.length > 2) {
                hashtags.push(`#${cleanLocation.toLowerCase()}`);
            }
        });
        // Add generic location hashtags
        hashtags.push('#local', '#community', '#neighborhood');
        return hashtags.slice(0, 5);
    }
    /**
   * Get community engagement hashtags
   */ getCommunityHashtags(businessType, targetAudience) {
        const communityHashtags = [
            '#community',
            '#local',
            '#support',
            '#family',
            '#friends',
            '#together',
            '#love'
        ];
        if (targetAudience) {
            const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            communityHashtags.push(...audienceHashtags);
        }
        return communityHashtags.slice(0, 5);
    }
    /**
   * Get seasonal/timely hashtags
   */ getSeasonalHashtags() {
        const now = new Date();
        const month = now.getMonth();
        const day = now.getDate();
        // Seasonal hashtags based on current time
        const seasonal = {
            0: [
                '#newyear',
                '#january',
                '#fresh',
                '#newbeginnings'
            ],
            1: [
                '#february',
                '#love',
                '#valentine',
                '#winter'
            ],
            2: [
                '#march',
                '#spring',
                '#fresh',
                '#bloom'
            ],
            3: [
                '#april',
                '#spring',
                '#easter',
                '#renewal'
            ],
            4: [
                '#may',
                '#spring',
                '#mothers',
                '#bloom'
            ],
            5: [
                '#june',
                '#summer',
                '#fathers',
                '#sunshine'
            ],
            6: [
                '#july',
                '#summer',
                '#vacation',
                '#hot'
            ],
            7: [
                '#august',
                '#summer',
                '#vacation',
                '#sunny'
            ],
            8: [
                '#september',
                '#fall',
                '#autumn',
                '#backtoschool'
            ],
            9: [
                '#october',
                '#fall',
                '#halloween',
                '#autumn'
            ],
            10: [
                '#november',
                '#thanksgiving',
                '#grateful',
                '#fall'
            ],
            11: [
                '#december',
                '#christmas',
                '#holiday',
                '#winter'
            ] // December
        };
        return seasonal[month] || [
            '#today',
            '#now',
            '#current'
        ];
    }
    /**
   * Get platform-specific hashtags
   */ getPlatformHashtags(platform) {
        const platformHashtags = {
            instagram: [
                '#instagram',
                '#insta',
                '#ig'
            ],
            facebook: [
                '#facebook',
                '#fb',
                '#social'
            ],
            twitter: [
                '#twitter',
                '#tweet',
                '#x'
            ],
            linkedin: [
                '#linkedin',
                '#professional',
                '#business'
            ],
            tiktok: [
                '#tiktok',
                '#tt',
                '#video'
            ]
        };
        return platformHashtags[platform.toLowerCase()] || [
            '#social',
            '#media'
        ];
    }
    /**
   * Get business-relevant trending hashtags
   */ getBusinessTrendingHashtags(businessType, platform) {
        // This would integrate with real trending APIs in production
        const trendingByBusiness = {
            restaurant: [
                '#foodtrends',
                '#eats2024',
                '#localfood',
                '#foodie'
            ],
            fitness: [
                '#fitness2024',
                '#healthtrends',
                '#workout',
                '#wellness'
            ],
            beauty: [
                '#beautytrends',
                '#skincare2024',
                '#makeup',
                '#selfcare'
            ],
            tech: [
                '#tech2024',
                '#innovation',
                '#ai',
                '#digital'
            ],
            retail: [
                '#fashion2024',
                '#shopping',
                '#style',
                '#trends'
            ]
        };
        return trendingByBusiness[businessType.toLowerCase()] || [
            '#trending',
            '#popular',
            '#new'
        ];
    }
    /**
   * Optimize hashtag selection for maximum virality
   */ optimizeForVirality(hashtags) {
        // Remove duplicates
        const unique = Array.from(new Set(hashtags));
        // Sort by estimated engagement potential (simplified scoring)
        const scored = unique.map((tag)=>({
                tag,
                score: this.calculateViralScore(tag)
            }));
        scored.sort((a, b)=>b.score - a.score);
        return scored.slice(0, 15).map((item)=>item.tag);
    }
    /**
   * Calculate viral potential score for a hashtag
   */ calculateViralScore(hashtag) {
        let score = 0;
        // High-engagement keywords get bonus points
        const viralKeywords = [
            'viral',
            'trending',
            'fyp',
            'explore',
            'amazing',
            'incredible'
        ];
        if (viralKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 10;
        }
        // Platform-specific hashtags get bonus
        const platformKeywords = [
            'instagram',
            'tiktok',
            'reels',
            'story'
        ];
        if (platformKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 5;
        }
        // Local hashtags get moderate bonus
        const localKeywords = [
            'local',
            'community',
            'neighborhood'
        ];
        if (localKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 3;
        }
        // Length penalty (very long hashtags perform worse)
        if (hashtag.length > 20) score -= 2;
        if (hashtag.length > 30) score -= 5;
        return score + Math.random(); // Add randomness for variety
    }
    /**
   * Fallback hashtags when trending data fails
   */ getFallbackHashtags(businessType, location, platform) {
        return {
            trending: [
                '#trending',
                '#viral',
                '#popular',
                '#new'
            ],
            viral: [
                '#amazing',
                '#incredible',
                '#mustsee',
                '#wow'
            ],
            niche: [
                `#${businessType}`,
                '#quality',
                '#professional',
                '#service'
            ],
            location: [
                '#local',
                '#community',
                `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`
            ],
            community: [
                '#community',
                '#support',
                '#family',
                '#love'
            ],
            seasonal: [
                '#today',
                '#now'
            ],
            platform: [
                `#${platform.toLowerCase()}`
            ],
            total: [
                '#trending',
                '#viral',
                `#${businessType}`,
                '#local',
                '#community',
                '#amazing',
                '#quality',
                '#professional',
                '#popular',
                '#new',
                '#support',
                '#service',
                `#${platform.toLowerCase()}`,
                '#today',
                '#love'
            ]
        };
    }
}
const viralHashtagEngine = new ViralHashtagEngine();
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0cbf39f2._.js.map