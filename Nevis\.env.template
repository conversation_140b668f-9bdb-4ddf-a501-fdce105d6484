# Firebase Configuration Template
# Copy this file to .env.local and fill in your Firebase project details

# Firebase Web App Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id_here
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id_here
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# Firebase Admin SDK Configuration (Server-side)
FIREBASE_PROJECT_ID=your_project_id_here
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project_id.iam.gserviceaccount.com
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"

# Alternative: Firebase Admin SDK Service Account Key File Path
# FIREBASE_SERVICE_ACCOUNT_KEY_PATH=./path/to/your-service-account-key.json

# Firebase Database URLs (if using Realtime Database)
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your_project_id-default-rtdb.firebaseio.com/

# Environment Configuration
NODE_ENV=development

# Optional: Firebase Emulator Configuration (for local development)
# FIRESTORE_EMULATOR_HOST=localhost:8080
# FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
# FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# OpenAI Configuration (Latest DALL-E 3 Image Generation)
OPENAI_API_KEY=your_openai_api_key_here

# Google AI Configuration (Gemini Models)
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Security Note:
# Never commit the actual .env.local file to version control
# Keep your Firebase service account key secure and never share it publicly
