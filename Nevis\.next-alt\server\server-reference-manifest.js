self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f5d637b281692c3f27909f9e0d661b29f958881f8\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f7ba9144001954daaaa5cea5934ab233d76c6e00d\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"789496ee83d803e1e9644583e4a6f21523a907741c\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"78a2cc90f0c309202520d68173137d83d08ea32806\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f184a7753a1c29638132401afe2bdafb4cd96f602\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7002a50d713c2964d62133b2af4480664176bd8291\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"60e429b11b311c0b871a717d8e7e036936b64edea0\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"7f1495e5c9392c71a0638d8ee09c874aef3fff06f4\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    },\n    \"409d8d4d6ee48a33e913d651c38aa0005eeae0dac6\": {\n      \"workers\": {\n        \"app/quick-content/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/quick-content/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/quick-content/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"IX4L4kHGX5wroRBvr4IfyffSFrv275hnIe9TQmW/+R8=\"\n}"