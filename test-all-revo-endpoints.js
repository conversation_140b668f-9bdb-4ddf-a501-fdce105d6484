/**
 * Test all Revo API endpoints to verify they're working correctly
 */

const testBrandProfile = {
  businessName: 'Test Restaurant',
  businessType: 'Restaurant',
  location: 'New York, NY',
  targetAudience: 'Food lovers',
  brandVoice: 'friendly',
  contentThemes: ['delicious food', 'great service'],
  services: ['Fine dining', 'Catering'],
  keyFeatures: ['Fresh ingredients', 'Expert chefs'],
  competitiveAdvantages: ['Local sourcing', '20 years experience'],
  visualStyle: 'modern',
  primaryColor: '#FF6B35',
  accentColor: '#F7931E',
  backgroundColor: '#FFFFFF'
};

async function testEndpoint(version, endpoint) {
  console.log(`\n🧪 Testing ${version}...`);
  
  try {
    // Test GET endpoint first
    console.log(`📡 GET ${endpoint}`);
    const getResponse = await fetch(`http://localhost:3001${endpoint}`, {
      method: 'GET'
    });
    
    if (getResponse.ok) {
      const getResult = await getResponse.json();
      console.log(`✅ GET ${version}: ${getResult.message}`);
      console.log(`   Model: ${getResult.model || 'N/A'}`);
      console.log(`   Version: ${getResult.version || 'N/A'}`);
    } else {
      console.log(`❌ GET ${version}: ${getResponse.status} ${getResponse.statusText}`);
      return false;
    }

    // Test POST endpoint (actual generation)
    console.log(`📡 POST ${endpoint}`);
    const postResponse = await fetch(`http://localhost:3001${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        businessType: testBrandProfile.businessType,
        platform: 'instagram',
        brandProfile: testBrandProfile,
        visualStyle: 'modern',
        aspectRatio: '1:1',
        includePeopleInDesigns: false,
        useLocalLanguage: false,
        ...(version === 'Revo 1.5' ? { artifactIds: [] } : {})
      })
    });

    if (postResponse.ok) {
      const postResult = await postResponse.json();
      if (postResult.success) {
        console.log(`✅ POST ${version}: Generation successful!`);
        console.log(`   Model: ${postResult.model || 'N/A'}`);
        console.log(`   Quality Score: ${postResult.qualityScore || 'N/A'}`);
        console.log(`   Processing Time: ${postResult.processingTime || 'N/A'}`);
        console.log(`   Has Image: ${postResult.imageUrl ? 'Yes' : 'No'}`);
        console.log(`   Has Caption: ${postResult.caption ? 'Yes' : 'No'}`);
        console.log(`   Hashtags: ${postResult.hashtags?.length || 0}`);
        return true;
      } else {
        console.log(`❌ POST ${version}: Generation failed - ${postResult.error || 'Unknown error'}`);
        return false;
      }
    } else {
      const errorText = await postResponse.text();
      console.log(`❌ POST ${version}: ${postResponse.status} ${postResponse.statusText}`);
      console.log(`   Error: ${errorText.substring(0, 200)}...`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${version}: Network error - ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Testing All Revo API Endpoints');
  console.log('=====================================');
  
  const tests = [
    { version: 'Revo 1.0', endpoint: '/api/generate-revo-1.0' },
    { version: 'Revo 1.5', endpoint: '/api/generate-revo-1.5' },
    { version: 'Revo 2.0', endpoint: '/api/generate-revo-2.0' }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const success = await testEndpoint(test.version, test.endpoint);
    results.push({ version: test.version, success });
  }
  
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.version}`);
  });
  
  const passCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalCount} endpoints working`);
  
  if (passCount === totalCount) {
    console.log('🎉 All Revo endpoints are working correctly!');
    console.log('✨ The 500 error issue should now be resolved.');
  } else {
    console.log('⚠️  Some endpoints are still having issues.');
    console.log('🔧 Check the server logs for more details.');
  }
}

// Run the tests
runAllTests().catch(console.error);
