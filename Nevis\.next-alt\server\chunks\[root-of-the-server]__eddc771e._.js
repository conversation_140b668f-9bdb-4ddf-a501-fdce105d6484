module.exports = {

"[project]/.next-internal/server/app/api/generate-revo-2.0/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * RSS Feed Service for Trending Content & Social Media Insights
 * Fetches and parses RSS feeds to extract trending topics, keywords, and themes
 */ __turbopack_context__.s({
    "RSSFeedService": (()=>RSSFeedService),
    "rssService": (()=>rssService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/xml2js/lib/xml2js.js [app-route] (ecmascript)");
;
class RSSFeedService {
    cache = new Map();
    cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000;
    feedUrls = {
        // Social Media & Marketing Trends
        socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,
        socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,
        bufferBlog: process.env.RSS_BUFFER_BLOG,
        hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,
        sproutSocial: process.env.RSS_SPROUT_SOCIAL,
        laterBlog: process.env.RSS_LATER_BLOG,
        // Trending Topics & News
        googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,
        redditPopular: process.env.RSS_REDDIT_POPULAR,
        buzzfeed: process.env.RSS_BUZZFEED,
        twitterTrending: process.env.RSS_TWITTER_TRENDING,
        // Business & Marketing
        hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,
        contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,
        marketingProfs: process.env.RSS_MARKETING_PROFS,
        marketingLand: process.env.RSS_MARKETING_LAND,
        neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,
        // Industry News
        techCrunch: process.env.RSS_TECHCRUNCH,
        mashable: process.env.RSS_MASHABLE,
        theVerge: process.env.RSS_THE_VERGE,
        wired: process.env.RSS_WIRED,
        // Platform-Specific
        instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,
        facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,
        linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,
        youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,
        tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,
        // Analytics & Data
        googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,
        hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,
        // Design & Creative
        canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,
        adobeBlog: process.env.RSS_ADOBE_BLOG,
        creativeBloq: process.env.RSS_CREATIVE_BLOQ,
        // Seasonal & Events
        eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG
    };
    /**
   * Fetch and parse a single RSS feed
   */ async fetchRSSFeed(url, sourceName) {
        try {
            // Check cache first
            const cached = this.cache.get(url);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Nevis-AI-Content-Generator/1.0'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const xmlData = await response.text();
            const parsed = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xml2js$2f$lib$2f$xml2js$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseStringPromise"])(xmlData);
            const articles = [];
            const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];
            const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');
            for (const item of items.slice(0, maxArticles)){
                const article = {
                    title: this.extractText(item.title),
                    description: this.extractText(item.description || item.summary),
                    link: this.extractText(item.link || item.id),
                    pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),
                    category: this.extractText(item.category),
                    keywords: this.extractKeywords(this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)),
                    source: sourceName
                };
                articles.push(article);
            }
            // Cache the results
            this.cache.set(url, {
                data: articles,
                timestamp: Date.now()
            });
            return articles;
        } catch (error) {
            return [];
        }
    }
    /**
   * Extract text content from RSS item fields
   */ extractText(field) {
        if (!field) return '';
        if (typeof field === 'string') return field;
        if (Array.isArray(field) && field.length > 0) {
            return typeof field[0] === 'string' ? field[0] : field[0]._ || '';
        }
        if (typeof field === 'object' && field._) return field._;
        return '';
    }
    /**
   * Extract keywords from text content
   */ extractKeywords(text) {
        if (!text) return [];
        // Remove HTML tags and normalize text
        const cleanText = text.replace(/<[^>]*>/g, '').toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
        // Extract meaningful words (3+ characters, not common stop words)
        const stopWords = new Set([
            'the',
            'and',
            'for',
            'are',
            'but',
            'not',
            'you',
            'all',
            'can',
            'had',
            'her',
            'was',
            'one',
            'our',
            'out',
            'day',
            'get',
            'has',
            'him',
            'his',
            'how',
            'its',
            'may',
            'new',
            'now',
            'old',
            'see',
            'two',
            'who',
            'boy',
            'did',
            'she',
            'use',
            'way',
            'will',
            'with'
        ]);
        const words = cleanText.split(' ').filter((word)=>word.length >= 3 && !stopWords.has(word)).slice(0, 10); // Limit to top 10 keywords per article
        return Array.from(new Set(words)); // Remove duplicates
    }
    /**
   * Fetch all RSS feeds and return trending data
   */ async getTrendingData() {
        const allArticles = [];
        const fetchPromises = [];
        // Fetch all feeds concurrently
        for (const [sourceName, url] of Object.entries(this.feedUrls)){
            if (url) {
                fetchPromises.push(this.fetchRSSFeed(url, sourceName));
            }
        }
        const results = await Promise.allSettled(fetchPromises);
        // Collect all successful results
        results.forEach((result)=>{
            if (result.status === 'fulfilled') {
                allArticles.push(...result.value);
            }
        });
        // Sort articles by publication date (newest first)
        allArticles.sort((a, b)=>b.pubDate.getTime() - a.pubDate.getTime());
        // Extract trending keywords and topics
        const allKeywords = [];
        const allTopics = [];
        const allThemes = [];
        allArticles.forEach((article)=>{
            allKeywords.push(...article.keywords);
            if (article.title) allTopics.push(article.title);
            if (article.category) allThemes.push(article.category);
        });
        // Count frequency and get top items
        const keywordCounts = this.getTopItems(allKeywords, 50);
        const topicCounts = this.getTopItems(allTopics, 30);
        const themeCounts = this.getTopItems(allThemes, 20);
        // 🚀 ENHANCED: Generate hashtag analytics
        const hashtagAnalytics = this.generateHashtagAnalytics(allArticles, keywordCounts);
        return {
            keywords: keywordCounts,
            hashtags: keywordCounts.map((keyword)=>`#${keyword.replace(/\s+/g, '')}`),
            topics: topicCounts,
            themes: themeCounts,
            articles: allArticles.slice(0, 100),
            lastUpdated: new Date(),
            hashtagAnalytics
        };
    }
    /**
   * Get top items by frequency
   */ getTopItems(items, limit) {
        const counts = new Map();
        items.forEach((item)=>{
            const normalized = item.toLowerCase().trim();
            if (normalized.length >= 3) {
                counts.set(normalized, (counts.get(normalized) || 0) + 1);
            }
        });
        return Array.from(counts.entries()).sort((a, b)=>b[1] - a[1]).slice(0, limit).map(([item])=>item);
    }
    /**
   * Get trending keywords for a specific category
   */ async getTrendingKeywordsByCategory(category) {
        const trendingData = await this.getTrendingData();
        const categoryFeeds = {
            social: [
                'socialMediaToday',
                'socialMediaExaminer',
                'bufferBlog',
                'hootsuiteBlogs'
            ],
            business: [
                'hubspotMarketing',
                'contentMarketingInstitute',
                'marketingProfs'
            ],
            tech: [
                'techCrunch',
                'theVerge',
                'wired'
            ],
            design: [
                'canvaDesignSchool',
                'adobeBlog',
                'creativeBloq'
            ]
        };
        const categoryArticles = trendingData.articles.filter((article)=>categoryFeeds[category].includes(article.source));
        const keywords = [];
        categoryArticles.forEach((article)=>keywords.push(...article.keywords));
        return this.getTopItems(keywords, 20);
    }
    /**
   * 🚀 ENHANCED: Generate comprehensive hashtag analytics from RSS data
   */ generateHashtagAnalytics(articles, keywords) {
        const hashtagFrequency = new Map();
        const hashtagsByCategory = new Map();
        const hashtagsByLocation = new Map();
        const hashtagsByIndustry = new Map();
        const hashtagSentiment = new Map();
        // Process articles for hashtag analytics
        articles.forEach((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            // Extract hashtags from content
            const hashtags = content.match(/#[a-zA-Z0-9_]+/g) || [];
            // Generate hashtags from keywords
            const keywordHashtags = keywords.filter((keyword)=>content.includes(keyword.toLowerCase())).map((keyword)=>`#${keyword.replace(/\s+/g, '')}`);
            const allHashtags = [
                ...hashtags,
                ...keywordHashtags
            ];
            allHashtags.forEach((hashtag)=>{
                // Count frequency
                hashtagFrequency.set(hashtag, (hashtagFrequency.get(hashtag) || 0) + 1);
                // Categorize by article category
                if (article.category) {
                    if (!hashtagsByCategory.has(article.category)) {
                        hashtagsByCategory.set(article.category, new Set());
                    }
                    hashtagsByCategory.get(article.category).add(hashtag);
                }
                // Categorize by source (as industry proxy)
                if (article.source) {
                    const industry = this.mapSourceToIndustry(article.source);
                    if (!hashtagsByIndustry.has(industry)) {
                        hashtagsByIndustry.set(industry, new Set());
                    }
                    hashtagsByIndustry.get(industry).add(hashtag);
                }
                // Basic sentiment analysis
                if (!hashtagSentiment.has(hashtag)) {
                    hashtagSentiment.set(hashtag, this.analyzeSentiment(content));
                }
            });
        });
        // Calculate trending hashtags with momentum
        const trending = Array.from(hashtagFrequency.entries()).sort(([, a], [, b])=>b - a).slice(0, 20).map(([hashtag, frequency])=>({
                hashtag,
                frequency,
                momentum: this.calculateMomentum(hashtag, articles)
            }));
        // Convert sets to arrays for the final result
        const byCategory = {};
        hashtagsByCategory.forEach((hashtags, category)=>{
            byCategory[category] = Array.from(hashtags).slice(0, 10);
        });
        const byLocation = {};
        // Location analysis would require more sophisticated processing
        // For now, we'll use a simple approach
        byLocation['global'] = trending.slice(0, 10).map((t)=>t.hashtag);
        const byIndustry = {};
        hashtagsByIndustry.forEach((hashtags, industry)=>{
            byIndustry[industry] = Array.from(hashtags).slice(0, 8);
        });
        const sentiment = {};
        hashtagSentiment.forEach((sent, hashtag)=>{
            sentiment[hashtag] = sent;
        });
        return {
            trending,
            byCategory,
            byLocation,
            byIndustry,
            sentiment
        };
    }
    /**
   * Map RSS source to industry category
   */ mapSourceToIndustry(source) {
        const industryMap = {
            'socialMediaToday': 'social_media',
            'socialMediaExaminer': 'social_media',
            'bufferBlog': 'social_media',
            'hootsuiteBlogs': 'social_media',
            'hubspotMarketing': 'marketing',
            'contentMarketingInstitute': 'marketing',
            'marketingProfs': 'marketing',
            'techCrunch': 'technology',
            'theVerge': 'technology',
            'wired': 'technology',
            'canvaDesignSchool': 'design',
            'adobeBlog': 'design',
            'creativeBloq': 'design'
        };
        return industryMap[source] || 'general';
    }
    /**
   * Calculate hashtag momentum based on recent usage
   */ calculateMomentum(hashtag, articles) {
        const now = Date.now();
        const recentArticles = articles.filter((article)=>{
            const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished <= 24;
        });
        const oldArticles = articles.filter((article)=>{
            const hoursSincePublished = (now - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished > 24 && hoursSincePublished <= 72;
        });
        const recentMentions = recentArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        const oldMentions = oldArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        if (recentMentions > oldMentions * 1.5) return 'rising';
        if (recentMentions < oldMentions * 0.5) return 'declining';
        return 'stable';
    }
    /**
   * Basic sentiment analysis for hashtags
   */ analyzeSentiment(content) {
        const positiveWords = [
            'amazing',
            'awesome',
            'great',
            'excellent',
            'fantastic',
            'wonderful',
            'love',
            'best',
            'perfect',
            'incredible',
            'outstanding',
            'brilliant',
            'success',
            'win',
            'achieve',
            'growth',
            'improve',
            'boost'
        ];
        const negativeWords = [
            'bad',
            'terrible',
            'awful',
            'horrible',
            'worst',
            'hate',
            'fail',
            'problem',
            'issue',
            'crisis',
            'decline',
            'drop',
            'loss',
            'damage',
            'risk',
            'threat',
            'concern',
            'worry'
        ];
        const words = content.toLowerCase().split(/\s+/);
        const positiveCount = words.filter((word)=>positiveWords.includes(word)).length;
        const negativeCount = words.filter((word)=>negativeWords.includes(word)).length;
        if (positiveCount > negativeCount) return 'positive';
        if (negativeCount > positiveCount) return 'negative';
        return 'neutral';
    }
}
const rssService = new RSSFeedService();
}}),
"[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Content Enhancer
 * Integrates RSS feed data to enhance content generation with trending topics
 */ __turbopack_context__.s({
    "TrendingContentEnhancer": (()=>TrendingContentEnhancer),
    "trendingEnhancer": (()=>trendingEnhancer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)");
;
class TrendingContentEnhancer {
    trendingCache = null;
    lastCacheUpdate = 0;
    cacheTimeout = 30 * 60 * 1000;
    /**
   * Get fresh trending data with caching
   */ async getTrendingData() {
        const now = Date.now();
        if (this.trendingCache && now - this.lastCacheUpdate < this.cacheTimeout) {
            return this.trendingCache;
        }
        this.trendingCache = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
        this.lastCacheUpdate = now;
        return this.trendingCache;
    }
    /**
   * Get trending enhancement data for content generation
   */ async getTrendingEnhancement(context = {}) {
        try {
            const trendingData = await this.getTrendingData();
            // Filter and prioritize based on context
            const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);
            const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);
            // Generate hashtags from trending keywords
            const hashtags = this.generateHashtags(relevantKeywords, context);
            // Extract seasonal themes
            const seasonalThemes = this.extractSeasonalThemes(trendingData);
            // Extract industry-specific buzz
            const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);
            return {
                keywords: relevantKeywords.slice(0, 15),
                topics: relevantTopics.slice(0, 10),
                hashtags: hashtags.slice(0, 10),
                seasonalThemes: seasonalThemes.slice(0, 5),
                industryBuzz: industryBuzz.slice(0, 8)
            };
        } catch (error) {
            // Return contextual fallback data based on current context
            return this.generateContextualFallback(context);
        }
    }
    /**
   * Filter keywords based on context relevance
   */ filterKeywordsByContext(keywords, context) {
        const platformKeywords = {
            instagram: [
                'visual',
                'photo',
                'story',
                'reel',
                'aesthetic',
                'lifestyle'
            ],
            facebook: [
                'community',
                'share',
                'connect',
                'family',
                'local',
                'event'
            ],
            twitter: [
                'news',
                'update',
                'breaking',
                'discussion',
                'opinion',
                'thread'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'industry',
                'networking',
                'leadership'
            ],
            tiktok: [
                'viral',
                'trend',
                'challenge',
                'creative',
                'fun',
                'entertainment'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'design',
                'home',
                'style'
            ]
        };
        const businessKeywords = {
            restaurant: [
                'food',
                'dining',
                'menu',
                'chef',
                'cuisine',
                'taste',
                'fresh'
            ],
            retail: [
                'shopping',
                'sale',
                'fashion',
                'style',
                'product',
                'deal',
                'new'
            ],
            fitness: [
                'health',
                'workout',
                'training',
                'wellness',
                'strength',
                'motivation'
            ],
            beauty: [
                'skincare',
                'makeup',
                'beauty',
                'glow',
                'treatment',
                'style'
            ],
            tech: [
                'innovation',
                'digital',
                'technology',
                'software',
                'app',
                'solution'
            ],
            healthcare: [
                'health',
                'wellness',
                'care',
                'treatment',
                'medical',
                'patient'
            ]
        };
        let filtered = [
            ...keywords
        ];
        // Boost platform-relevant keywords
        if (context.platform && platformKeywords[context.platform]) {
            const platformBoost = platformKeywords[context.platform];
            filtered = filtered.sort((a, b)=>{
                const aBoost = platformBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = platformBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        // Boost business-relevant keywords
        if (context.businessType && businessKeywords[context.businessType]) {
            const businessBoost = businessKeywords[context.businessType];
            filtered = filtered.sort((a, b)=>{
                const aBoost = businessBoost.some((boost)=>a.includes(boost)) ? -1 : 0;
                const bBoost = businessBoost.some((boost)=>b.includes(boost)) ? -1 : 0;
                return aBoost - bBoost;
            });
        }
        return filtered;
    }
    /**
   * Filter topics based on context relevance
   */ filterTopicsByContext(topics, context) {
        // Remove topics that are too generic or not suitable for social media
        const filtered = topics.filter((topic)=>{
            const lower = topic.toLowerCase();
            return !lower.includes('error') && !lower.includes('404') && !lower.includes('page not found') && lower.length > 10 && lower.length < 100;
        });
        return filtered;
    }
    /**
   * Generate relevant hashtags from keywords
   */ generateHashtags(keywords, context) {
        const hashtags = [];
        // Convert keywords to hashtags
        keywords.forEach((keyword)=>{
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        });
        // Add platform-specific hashtags
        const platformHashtags = {
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#picoftheday'
            ],
            facebook: [
                '#community',
                '#local',
                '#share',
                '#connect'
            ],
            twitter: [
                '#news',
                '#update',
                '#discussion',
                '#trending'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#career',
                '#networking'
            ],
            tiktok: [
                '#fyp',
                '#viral',
                '#trending',
                '#foryou'
            ],
            pinterest: [
                '#inspiration',
                '#ideas',
                '#diy',
                '#style'
            ]
        };
        if (context.platform && platformHashtags[context.platform]) {
            hashtags.push(...platformHashtags[context.platform]);
        }
        // Remove duplicates and return
        return Array.from(new Set(hashtags));
    }
    /**
   * Extract seasonal themes from trending data
   */ extractSeasonalThemes(trendingData) {
        const currentMonth = new Date().getMonth();
        const seasonalKeywords = {
            0: [
                'new year',
                'resolution',
                'fresh start',
                'winter'
            ],
            1: [
                'valentine',
                'love',
                'romance',
                'winter'
            ],
            2: [
                'spring',
                'march madness',
                'renewal',
                'growth'
            ],
            3: [
                'easter',
                'spring',
                'bloom',
                'fresh'
            ],
            4: [
                'mother\'s day',
                'spring',
                'flowers',
                'celebration'
            ],
            5: [
                'summer',
                'graduation',
                'father\'s day',
                'vacation'
            ],
            6: [
                'summer',
                'july 4th',
                'independence',
                'freedom'
            ],
            7: [
                'summer',
                'vacation',
                'back to school',
                'preparation'
            ],
            8: [
                'back to school',
                'fall',
                'autumn',
                'harvest'
            ],
            9: [
                'halloween',
                'october',
                'spooky',
                'fall'
            ],
            10: [
                'thanksgiving',
                'gratitude',
                'family',
                'harvest'
            ],
            11: [
                'christmas',
                'holiday',
                'winter',
                'celebration'
            ]
        };
        const currentSeasonalKeywords = seasonalKeywords[currentMonth] || [];
        const seasonalThemes = trendingData.keywords.filter((keyword)=>currentSeasonalKeywords.some((seasonal)=>keyword.toLowerCase().includes(seasonal.toLowerCase())));
        return seasonalThemes;
    }
    /**
   * Extract industry-specific buzz from trending data
   */ extractIndustryBuzz(trendingData, businessType) {
        if (!businessType) return [];
        const industryKeywords = {
            restaurant: [
                'food',
                'dining',
                'chef',
                'cuisine',
                'recipe',
                'restaurant',
                'menu'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'product',
                'brand',
                'sale',
                'deal'
            ],
            fitness: [
                'fitness',
                'workout',
                'health',
                'gym',
                'training',
                'wellness',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'cosmetics',
                'treatment',
                'spa'
            ],
            tech: [
                'technology',
                'tech',
                'digital',
                'software',
                'app',
                'innovation',
                'ai'
            ],
            healthcare: [
                'health',
                'medical',
                'healthcare',
                'wellness',
                'treatment',
                'care'
            ]
        };
        const relevantKeywords = industryKeywords[businessType] || [];
        const industryBuzz = trendingData.keywords.filter((keyword)=>relevantKeywords.some((industry)=>keyword.toLowerCase().includes(industry.toLowerCase())));
        return industryBuzz;
    }
    /**
   * Generate contextual fallback data without hardcoded placeholders
   */ generateContextualFallback(context) {
        const today = new Date();
        const currentMonth = today.toLocaleDateString('en-US', {
            month: 'long'
        });
        const currentDay = today.toLocaleDateString('en-US', {
            weekday: 'long'
        });
        // Generate contextual keywords based on business type and current date
        const keywords = [];
        if (context.businessType) {
            keywords.push(`${context.businessType} services`, `${context.businessType} solutions`);
        }
        keywords.push(`${currentDay} motivation`, `${currentMonth} opportunities`);
        // Generate contextual topics
        const topics = [];
        if (context.businessType) {
            topics.push(`${context.businessType} industry insights`);
        }
        if (context.location) {
            topics.push(`${context.location} business community`);
        }
        topics.push(`${currentMonth} business trends`);
        // Generate contextual hashtags
        const hashtags = [];
        if (context.businessType) {
            hashtags.push(`#${context.businessType.replace(/\s+/g, '')}Business`);
        }
        if (context.location) {
            const locationParts = context.location.split(',').map((part)=>part.trim());
            locationParts.forEach((part)=>{
                if (part.length > 2) {
                    hashtags.push(`#${part.replace(/\s+/g, '')}`);
                }
            });
        }
        hashtags.push(`#${currentDay}Motivation`, `#${currentMonth}${today.getFullYear()}`);
        // Generate seasonal themes
        const seasonalThemes = [];
        const month = today.getMonth();
        if (month >= 2 && month <= 4) seasonalThemes.push('Spring renewal', 'Fresh starts');
        else if (month >= 5 && month <= 7) seasonalThemes.push('Summer energy', 'Outdoor activities');
        else if (month >= 8 && month <= 10) seasonalThemes.push('Autumn preparation', 'Harvest season');
        else seasonalThemes.push('Winter planning', 'Year-end reflection');
        return {
            keywords: keywords.slice(0, 15),
            topics: topics.slice(0, 10),
            hashtags: hashtags.slice(0, 10),
            seasonalThemes: seasonalThemes.slice(0, 5),
            industryBuzz: context.businessType ? [
                `${context.businessType} innovation`
            ] : []
        };
    }
    /**
   * Get trending prompt enhancement for AI content generation
   */ async getTrendingPromptEnhancement(context = {}) {
        const enhancement = await this.getTrendingEnhancement(context);
        const promptParts = [];
        if (enhancement.keywords.length > 0) {
            promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);
        }
        if (enhancement.seasonalThemes.length > 0) {
            promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);
        }
        if (enhancement.industryBuzz.length > 0) {
            promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);
        }
        if (enhancement.hashtags.length > 0) {
            promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);
        }
        return promptParts.join('\n');
    }
}
const trendingEnhancer = new TrendingContentEnhancer();
}}),
"[project]/src/ai/advanced-trending-hashtag-analyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Trending Hashtag Analyzer
 * Analyzes RSS feeds and trending data to extract the most relevant hashtags
 * with sophisticated contextual understanding and business relevance scoring
 */ __turbopack_context__.s({
    "AdvancedTrendingHashtagAnalyzer": (()=>AdvancedTrendingHashtagAnalyzer),
    "advancedHashtagAnalyzer": (()=>advancedHashtagAnalyzer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
;
;
class AdvancedTrendingHashtagAnalyzer {
    cache = new Map();
    cacheTimeout = 15 * 60 * 1000;
    /**
   * Analyze trending data and generate advanced hashtag strategy
   */ async analyzeHashtagTrends(context) {
        const cacheKey = this.generateCacheKey(context);
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        try {
            // Get comprehensive trending data
            const [trendingData, enhancementData] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rssService"].getTrendingData(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                    businessType: context.businessType,
                    location: context.location,
                    platform: context.platform,
                    targetAudience: context.targetAudience
                })
            ]);
            // Extract and analyze hashtags from multiple sources
            const hashtagAnalyses = await this.extractAndAnalyzeHashtags(trendingData, enhancementData, context);
            // Categorize hashtags by type and relevance
            const strategy = this.categorizeHashtags(hashtagAnalyses, context);
            // Cache the results
            this.cache.set(cacheKey, {
                data: strategy,
                timestamp: Date.now()
            });
            return strategy;
        } catch (error) {
            return this.getFallbackStrategy(context);
        }
    }
    /**
   * Extract hashtags from RSS articles and trending data
   */ async extractAndAnalyzeHashtags(trendingData, enhancementData, context) {
        const hashtagMap = new Map();
        // Process RSS articles for hashtag extraction
        for (const article of trendingData.articles){
            const extractedHashtags = this.extractHashtagsFromArticle(article, context);
            for (const hashtag of extractedHashtags){
                if (hashtagMap.has(hashtag)) {
                    const existing = hashtagMap.get(hashtag);
                    existing.sources.push(article.source);
                    existing.trendingScore += 1;
                } else {
                    hashtagMap.set(hashtag, {
                        hashtag,
                        relevanceScore: this.calculateRelevanceScore(hashtag, context),
                        trendingScore: 1,
                        businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                        platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                        locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                        engagementPotential: this.calculateEngagementPotential(hashtag),
                        sources: [
                            article.source
                        ],
                        momentum: this.calculateMomentum(hashtag, trendingData),
                        category: this.categorizeHashtag(hashtag, context)
                    });
                }
            }
        }
        // Add hashtags from trending enhancement data
        for (const hashtag of enhancementData.hashtags){
            if (hashtagMap.has(hashtag)) {
                const existing = hashtagMap.get(hashtag);
                existing.trendingScore += 2; // Enhancement data gets higher weight
                existing.sources.push('trending_enhancer');
            } else {
                hashtagMap.set(hashtag, {
                    hashtag,
                    relevanceScore: this.calculateRelevanceScore(hashtag, context),
                    trendingScore: 2,
                    businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                    platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                    locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                    engagementPotential: this.calculateEngagementPotential(hashtag),
                    sources: [
                        'trending_enhancer'
                    ],
                    momentum: 'rising',
                    category: this.categorizeHashtag(hashtag, context)
                });
            }
        }
        // Add business-specific trending hashtags
        const businessHashtags = this.generateBusinessTrendingHashtags(context);
        for (const hashtag of businessHashtags){
            if (!hashtagMap.has(hashtag)) {
                hashtagMap.set(hashtag, {
                    hashtag,
                    relevanceScore: this.calculateRelevanceScore(hashtag, context),
                    trendingScore: 1,
                    businessRelevance: 10,
                    platformOptimization: this.calculatePlatformOptimization(hashtag, context.platform),
                    locationRelevance: this.calculateLocationRelevance(hashtag, context.location),
                    engagementPotential: this.calculateEngagementPotential(hashtag),
                    sources: [
                        'business_generator'
                    ],
                    momentum: 'stable',
                    category: 'business'
                });
            }
        }
        return Array.from(hashtagMap.values());
    }
    /**
   * Extract hashtags from article content
   */ extractHashtagsFromArticle(article, context) {
        const hashtags = [];
        const content = `${article.title} ${article.description}`.toLowerCase();
        // Extract existing hashtags
        const hashtagMatches = content.match(/#[a-zA-Z0-9_]+/g) || [];
        hashtags.push(...hashtagMatches);
        // Generate hashtags from keywords
        const keywords = article.keywords || [];
        for (const keyword of keywords){
            const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {
                hashtags.push(`#${cleanKeyword}`);
            }
        }
        // Generate contextual hashtags based on content relevance
        const contextualHashtags = this.generateContextualHashtags(content, context);
        hashtags.push(...contextualHashtags);
        return Array.from(new Set(hashtags));
    }
    /**
   * Generate contextual hashtags based on content analysis
   */ generateContextualHashtags(content, context) {
        const hashtags = [];
        // Business type relevance
        if (content.includes(context.businessType.toLowerCase())) {
            hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
        }
        // Location relevance
        if (content.includes(context.location.toLowerCase())) {
            hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
        }
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        for (const keyword of industryKeywords){
            if (content.includes(keyword.toLowerCase())) {
                hashtags.push(`#${keyword.replace(/\s+/g, '')}`);
            }
        }
        return hashtags;
    }
    /**
   * Calculate relevance score for a hashtag
   */ calculateRelevanceScore(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase();
        // Business type relevance
        if (hashtagLower.includes(context.businessType.toLowerCase())) score += 5;
        // Location relevance
        if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) score += 4;
        // Service relevance
        if (context.services) {
            const services = context.services.toLowerCase().split(/[,\s]+/);
            for (const service of services){
                if (hashtagLower.includes(service)) score += 3;
            }
        }
        // Platform optimization
        score += this.calculatePlatformOptimization(hashtag, context.platform);
        return Math.min(score, 10); // Cap at 10
    }
    /**
   * Calculate business relevance score
   */ calculateBusinessRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase();
        // Direct business name match
        if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\s+/g, ''))) score += 10;
        // Business type match
        if (hashtagLower.includes(context.businessType.toLowerCase())) score += 8;
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        for (const keyword of industryKeywords){
            if (hashtagLower.includes(keyword.toLowerCase())) score += 6;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate platform optimization score
   */ calculatePlatformOptimization(hashtag, platform) {
        const platformHashtags = {
            instagram: [
                'instagood',
                'photooftheday',
                'instadaily',
                'reels',
                'igers'
            ],
            facebook: [
                'community',
                'local',
                'share',
                'connect',
                'family'
            ],
            twitter: [
                'news',
                'update',
                'discussion',
                'trending',
                'breaking'
            ],
            linkedin: [
                'professional',
                'business',
                'career',
                'networking',
                'industry'
            ],
            tiktok: [
                'fyp',
                'viral',
                'trending',
                'foryou',
                'dance'
            ],
            pinterest: [
                'inspiration',
                'ideas',
                'diy',
                'style',
                'design'
            ]
        };
        const platformSpecific = platformHashtags[platform.toLowerCase()] || [];
        const hashtagLower = hashtag.toLowerCase();
        for (const specific of platformSpecific){
            if (hashtagLower.includes(specific)) return 8;
        }
        return 2; // Base score for any hashtag
    }
    /**
   * Calculate location relevance score
   */ calculateLocationRelevance(hashtag, location) {
        const hashtagLower = hashtag.toLowerCase();
        const locationLower = location.toLowerCase();
        if (hashtagLower.includes(locationLower.replace(/\s+/g, ''))) return 10;
        if (hashtagLower.includes('local') || hashtagLower.includes('community')) return 6;
        // Check for city/state/country keywords
        const locationParts = location.split(/[,\s]+/);
        for (const part of locationParts){
            if (part.length > 2 && hashtagLower.includes(part.toLowerCase())) return 8;
        }
        return 1;
    }
    /**
   * Calculate engagement potential score
   */ calculateEngagementPotential(hashtag) {
        const highEngagementKeywords = [
            'viral',
            'trending',
            'amazing',
            'incredible',
            'awesome',
            'beautiful',
            'love',
            'best',
            'new',
            'hot',
            'popular',
            'top',
            'must',
            'perfect'
        ];
        const hashtagLower = hashtag.toLowerCase();
        for (const keyword of highEngagementKeywords){
            if (hashtagLower.includes(keyword)) return 9;
        }
        // Length-based scoring (shorter hashtags often perform better)
        if (hashtag.length <= 10) return 7;
        if (hashtag.length <= 15) return 5;
        return 3;
    }
    /**
   * Calculate momentum for hashtag trends
   */ calculateMomentum(hashtag, trendingData) {
        // Simple momentum calculation based on recency and frequency
        const recentArticles = trendingData.articles.filter((article)=>{
            const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);
            return hoursSincePublished <= 24;
        });
        const hashtagMentions = recentArticles.filter((article)=>`${article.title} ${article.description}`.toLowerCase().includes(hashtag.toLowerCase())).length;
        if (hashtagMentions >= 3) return 'rising';
        if (hashtagMentions >= 1) return 'stable';
        return 'declining';
    }
    /**
   * Categorize hashtag by type
   */ categorizeHashtag(hashtag, context) {
        const hashtagLower = hashtag.toLowerCase();
        if (hashtagLower.includes('viral') || hashtagLower.includes('trending')) return 'viral';
        if (hashtagLower.includes(context.businessType.toLowerCase())) return 'business';
        if (hashtagLower.includes(context.location.toLowerCase().replace(/\s+/g, ''))) return 'location';
        if (hashtagLower.includes('season') || hashtagLower.includes('holiday')) return 'seasonal';
        if (this.isNicheHashtag(hashtag, context)) return 'niche';
        return 'trending';
    }
    /**
   * Check if hashtag is niche-specific
   */ isNicheHashtag(hashtag, context) {
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const hashtagLower = hashtag.toLowerCase();
        return industryKeywords.some((keyword)=>hashtagLower.includes(keyword.toLowerCase()));
    }
    /**
   * Get industry-specific keywords
   */ getIndustryKeywords(businessType) {
        const industryMap = {
            restaurant: [
                'food',
                'dining',
                'cuisine',
                'chef',
                'menu',
                'delicious',
                'taste'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'sale',
                'deals',
                'boutique'
            ],
            healthcare: [
                'health',
                'wellness',
                'medical',
                'care',
                'treatment',
                'doctor'
            ],
            fitness: [
                'workout',
                'gym',
                'fitness',
                'health',
                'training',
                'exercise'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'salon',
                'spa',
                'treatment'
            ],
            technology: [
                'tech',
                'digital',
                'innovation',
                'software',
                'app',
                'online'
            ],
            education: [
                'learning',
                'education',
                'training',
                'course',
                'skill',
                'knowledge'
            ],
            automotive: [
                'car',
                'auto',
                'vehicle',
                'repair',
                'service',
                'maintenance'
            ],
            realestate: [
                'property',
                'home',
                'house',
                'real estate',
                'investment'
            ],
            legal: [
                'law',
                'legal',
                'attorney',
                'lawyer',
                'justice',
                'rights'
            ]
        };
        return industryMap[businessType.toLowerCase()] || [
            'business',
            'service',
            'professional'
        ];
    }
    /**
   * Generate business-specific trending hashtags
   */ generateBusinessTrendingHashtags(context) {
        const hashtags = [];
        // Business name hashtag
        hashtags.push(`#${context.businessName.replace(/[^a-zA-Z0-9]/g, '')}`);
        // Business type hashtag
        hashtags.push(`#${context.businessType.replace(/\s+/g, '')}`);
        // Location hashtag
        hashtags.push(`#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`);
        // Industry-specific hashtags
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        hashtags.push(...industryKeywords.slice(0, 3).map((keyword)=>`#${keyword.replace(/\s+/g, '')}`));
        return hashtags;
    }
    /**
   * Categorize hashtags into strategy groups
   */ categorizeHashtags(analyses, context) {
        // Sort by overall relevance score
        const sortedAnalyses = analyses.sort((a, b)=>{
            const scoreA = (a.relevanceScore + a.trendingScore + a.businessRelevance + a.engagementPotential) / 4;
            const scoreB = (b.relevanceScore + b.trendingScore + b.businessRelevance + b.engagementPotential) / 4;
            return scoreB - scoreA;
        });
        const topTrending = sortedAnalyses.filter((a)=>a.category === 'trending' || a.category === 'viral').slice(0, 8);
        const businessOptimized = sortedAnalyses.filter((a)=>a.businessRelevance >= 6).slice(0, 6);
        const locationSpecific = sortedAnalyses.filter((a)=>a.locationRelevance >= 6).slice(0, 4);
        const platformNative = sortedAnalyses.filter((a)=>a.platformOptimization >= 6).slice(0, 5);
        const emergingTrends = sortedAnalyses.filter((a)=>a.momentum === 'rising').slice(0, 6);
        // Create final recommendations (top 15 hashtags)
        const finalRecommendations = this.createFinalRecommendations(topTrending, businessOptimized, locationSpecific, platformNative, emergingTrends);
        return {
            topTrending,
            businessOptimized,
            locationSpecific,
            platformNative,
            emergingTrends,
            finalRecommendations
        };
    }
    /**
   * Create final hashtag recommendations
   */ createFinalRecommendations(topTrending, businessOptimized, locationSpecific, platformNative, emergingTrends) {
        const recommendations = new Set();
        // Add top performers from each category
        topTrending.slice(0, 4).forEach((h)=>recommendations.add(h.hashtag));
        businessOptimized.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        locationSpecific.slice(0, 2).forEach((h)=>recommendations.add(h.hashtag));
        platformNative.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        emergingTrends.slice(0, 3).forEach((h)=>recommendations.add(h.hashtag));
        return Array.from(recommendations).slice(0, 15);
    }
    /**
   * Generate cache key for analysis context
   */ generateCacheKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}-${context.businessName}`.toLowerCase();
    }
    /**
   * Get fallback strategy when analysis fails
   */ getFallbackStrategy(context) {
        const fallbackHashtags = [
            '#trending',
            '#viral',
            '#business',
            '#local',
            '#community',
            `#${context.businessType.replace(/\s+/g, '')}`,
            `#${context.location.replace(/[^a-zA-Z0-9]/g, '')}`,
            '#quality',
            '#professional',
            '#service'
        ];
        const fallbackAnalyses = fallbackHashtags.map((hashtag)=>({
                hashtag,
                relevanceScore: 5,
                trendingScore: 3,
                businessRelevance: 5,
                platformOptimization: 4,
                locationRelevance: 3,
                engagementPotential: 5,
                sources: [
                    'fallback'
                ],
                momentum: 'stable',
                category: 'trending'
            }));
        return {
            topTrending: fallbackAnalyses.slice(0, 4),
            businessOptimized: fallbackAnalyses.slice(0, 3),
            locationSpecific: fallbackAnalyses.slice(0, 2),
            platformNative: fallbackAnalyses.slice(0, 3),
            emergingTrends: fallbackAnalyses.slice(0, 3),
            finalRecommendations: fallbackHashtags
        };
    }
}
const advancedHashtagAnalyzer = new AdvancedTrendingHashtagAnalyzer();
}}),
"[project]/src/ai/realtime-hashtag-scorer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Real-time Hashtag Relevance Scoring System
 * Advanced scoring algorithm that evaluates hashtag relevance based on
 * RSS trends, business context, location, platform, and engagement potential
 */ __turbopack_context__.s({
    "RealtimeHashtagScorer": (()=>RealtimeHashtagScorer),
    "realtimeHashtagScorer": (()=>realtimeHashtagScorer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/rss-feed-service.ts [app-route] (ecmascript)");
;
class RealtimeHashtagScorer {
    scoreCache = new Map();
    cacheTimeout = 10 * 60 * 1000;
    /**
   * Score a single hashtag with comprehensive analysis
   */ async scoreHashtag(hashtag, context) {
        const cacheKey = `${hashtag}-${this.generateContextKey(context)}`;
        const cached = this.scoreCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.score;
        }
        try {
            // Get trending data for analysis
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$rss$2d$feed$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rssService"].getTrendingData();
            // Calculate individual score components
            const breakdown = {
                trendingScore: await this.calculateTrendingScore(hashtag, trendingData),
                businessRelevance: this.calculateBusinessRelevance(hashtag, context),
                locationRelevance: this.calculateLocationRelevance(hashtag, context),
                platformOptimization: this.calculatePlatformOptimization(hashtag, context),
                engagementPotential: this.calculateEngagementPotential(hashtag, context),
                temporalRelevance: this.calculateTemporalRelevance(hashtag, context),
                competitorAnalysis: await this.calculateCompetitorAnalysis(hashtag, context, trendingData),
                semanticRelevance: this.calculateSemanticRelevance(hashtag, context)
            };
            // Calculate weighted total score
            const totalScore = this.calculateWeightedScore(breakdown, context);
            // Determine confidence level
            const confidence = this.calculateConfidence(breakdown, trendingData);
            // Generate recommendation
            const recommendation = this.generateRecommendation(totalScore, confidence);
            // Generate reasoning
            const reasoning = this.generateReasoning(breakdown, context);
            const score = {
                hashtag,
                totalScore,
                breakdown,
                confidence,
                recommendation,
                reasoning
            };
            // Cache the result
            this.scoreCache.set(cacheKey, {
                score,
                timestamp: Date.now()
            });
            return score;
        } catch (error) {
            return this.getFallbackScore(hashtag, context);
        }
    }
    /**
   * Score multiple hashtags and return sorted by relevance
   */ async scoreHashtags(hashtags, context) {
        const scores = await Promise.all(hashtags.map((hashtag)=>this.scoreHashtag(hashtag, context)));
        return scores.sort((a, b)=>b.totalScore - a.totalScore);
    }
    /**
   * Calculate trending score based on RSS data
   */ async calculateTrendingScore(hashtag, trendingData) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Check direct mentions in RSS articles
        const mentionCount = trendingData.articles.filter((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            return content.includes(hashtagLower);
        }).length;
        // Score based on mention frequency
        if (mentionCount >= 5) score += 10;
        else if (mentionCount >= 3) score += 8;
        else if (mentionCount >= 1) score += 6;
        else score += 2;
        // Check keyword relevance in trending topics
        const keywordRelevance = trendingData.keywords.filter((keyword)=>keyword.toLowerCase().includes(hashtagLower) || hashtagLower.includes(keyword.toLowerCase())).length;
        score += Math.min(keywordRelevance * 2, 4);
        // Recency bonus (newer articles get higher weight)
        const recentMentions = trendingData.articles.filter((article)=>{
            const hoursSincePublished = (Date.now() - article.pubDate.getTime()) / (1000 * 60 * 60);
            const content = `${article.title} ${article.description}`.toLowerCase();
            return hoursSincePublished <= 6 && content.includes(hashtagLower);
        }).length;
        if (recentMentions > 0) score += 2;
        return Math.min(score, 10);
    }
    /**
   * Calculate business relevance score
   */ calculateBusinessRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Direct business name match
        if (hashtagLower.includes(context.businessName.toLowerCase().replace(/\s+/g, ''))) {
            score += 10;
        }
        // Business type relevance
        if (hashtagLower.includes(context.businessType.toLowerCase())) {
            score += 8;
        }
        // Services/expertise relevance
        if (context.services) {
            const services = context.services.toLowerCase().split(/[,\s]+/);
            const serviceMatches = services.filter((service)=>hashtagLower.includes(service) || service.includes(hashtagLower)).length;
            score += Math.min(serviceMatches * 3, 6);
        }
        // Industry keywords
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const industryMatches = industryKeywords.filter((keyword)=>hashtagLower.includes(keyword.toLowerCase())).length;
        score += Math.min(industryMatches * 2, 4);
        return Math.min(score, 10);
    }
    /**
   * Calculate location relevance score
   */ calculateLocationRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const locationLower = context.location.toLowerCase();
        // Direct location match
        if (hashtagLower.includes(locationLower.replace(/\s+/g, ''))) {
            score += 10;
        }
        // Location parts (city, state, country)
        const locationParts = context.location.split(/[,\s]+/).filter((part)=>part.length > 2);
        const locationMatches = locationParts.filter((part)=>hashtagLower.includes(part.toLowerCase())).length;
        score += Math.min(locationMatches * 4, 8);
        // Local/community keywords
        const localKeywords = [
            'local',
            'community',
            'neighborhood',
            'area',
            'town',
            'city'
        ];
        if (localKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 6;
        }
        // Regional keywords
        const regionalKeywords = [
            'regional',
            'metro',
            'downtown',
            'uptown',
            'district'
        ];
        if (regionalKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 4;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate platform optimization score
   */ calculatePlatformOptimization(hashtag, context) {
        const platformHashtags = {
            instagram: {
                high: [
                    'instagood',
                    'photooftheday',
                    'instadaily',
                    'reels',
                    'igers',
                    'instamood'
                ],
                medium: [
                    'picoftheday',
                    'instapic',
                    'instalike',
                    'followme',
                    'instagramhub'
                ]
            },
            facebook: {
                high: [
                    'community',
                    'local',
                    'share',
                    'connect',
                    'family',
                    'friends'
                ],
                medium: [
                    'like',
                    'follow',
                    'page',
                    'group',
                    'event'
                ]
            },
            twitter: {
                high: [
                    'news',
                    'update',
                    'discussion',
                    'trending',
                    'breaking',
                    'thread'
                ],
                medium: [
                    'tweet',
                    'retweet',
                    'follow',
                    'hashtag',
                    'viral'
                ]
            },
            linkedin: {
                high: [
                    'professional',
                    'business',
                    'career',
                    'networking',
                    'industry',
                    'leadership'
                ],
                medium: [
                    'job',
                    'work',
                    'corporate',
                    'company',
                    'team'
                ]
            },
            tiktok: {
                high: [
                    'fyp',
                    'viral',
                    'trending',
                    'foryou',
                    'dance',
                    'challenge'
                ],
                medium: [
                    'tiktok',
                    'video',
                    'funny',
                    'entertainment',
                    'music'
                ]
            },
            pinterest: {
                high: [
                    'inspiration',
                    'ideas',
                    'diy',
                    'style',
                    'design',
                    'home'
                ],
                medium: [
                    'pinterest',
                    'pin',
                    'board',
                    'creative',
                    'art'
                ]
            }
        };
        const platform = context.platform.toLowerCase();
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const platformData = platformHashtags[platform];
        if (!platformData) return 5; // Default score for unknown platforms
        // Check high-value platform hashtags
        if (platformData.high.some((tag)=>hashtagLower.includes(tag))) {
            return 10;
        }
        // Check medium-value platform hashtags
        if (platformData.medium.some((tag)=>hashtagLower.includes(tag))) {
            return 7;
        }
        // Platform-specific length optimization
        const optimalLengths = {
            instagram: {
                min: 5,
                max: 20
            },
            twitter: {
                min: 3,
                max: 15
            },
            tiktok: {
                min: 3,
                max: 12
            },
            linkedin: {
                min: 8,
                max: 25
            },
            facebook: {
                min: 5,
                max: 18
            },
            pinterest: {
                min: 6,
                max: 22
            }
        };
        const lengthData = optimalLengths[platform];
        if (lengthData && hashtag.length >= lengthData.min && hashtag.length <= lengthData.max) {
            return 6;
        }
        return 3; // Base score
    }
    /**
   * Calculate engagement potential score
   */ calculateEngagementPotential(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // High-engagement keywords
        const highEngagementKeywords = [
            'viral',
            'trending',
            'amazing',
            'incredible',
            'awesome',
            'beautiful',
            'love',
            'best',
            'new',
            'hot',
            'popular',
            'top',
            'must',
            'perfect',
            'exclusive',
            'limited',
            'special',
            'unique',
            'rare'
        ];
        if (highEngagementKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 9;
        }
        // Emotional keywords
        const emotionalKeywords = [
            'happy',
            'excited',
            'proud',
            'grateful',
            'blessed',
            'inspired',
            'motivated',
            'passionate',
            'thrilled',
            'delighted'
        ];
        if (emotionalKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 7;
        }
        // Action keywords
        const actionKeywords = [
            'discover',
            'explore',
            'experience',
            'try',
            'learn',
            'create',
            'build',
            'grow',
            'achieve',
            'succeed'
        ];
        if (actionKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
            score += 6;
        }
        // Length-based scoring (optimal hashtag lengths)
        if (hashtag.length >= 6 && hashtag.length <= 15) {
            score += 5;
        } else if (hashtag.length >= 4 && hashtag.length <= 20) {
            score += 3;
        } else {
            score += 1;
        }
        // Avoid overly generic hashtags
        const genericHashtags = [
            'good',
            'nice',
            'cool',
            'great',
            'ok',
            'fine'
        ];
        if (genericHashtags.some((generic)=>hashtagLower === generic)) {
            score -= 3;
        }
        return Math.min(Math.max(score, 0), 10);
    }
    /**
   * Calculate temporal relevance score
   */ calculateTemporalRelevance(hashtag, context) {
        let score = 5; // Base score
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        const now = new Date();
        const currentHour = context.timeOfDay || now.getHours();
        const currentDay = context.dayOfWeek || now.getDay();
        // Time-of-day relevance
        const timeKeywords = {
            morning: [
                'morning',
                'breakfast',
                'coffee',
                'start',
                'fresh'
            ],
            afternoon: [
                'lunch',
                'afternoon',
                'work',
                'business',
                'professional'
            ],
            evening: [
                'dinner',
                'evening',
                'relax',
                'unwind',
                'family'
            ],
            night: [
                'night',
                'late',
                'weekend',
                'party',
                'fun'
            ]
        };
        let timeCategory = 'morning';
        if (currentHour >= 12 && currentHour < 17) timeCategory = 'afternoon';
        else if (currentHour >= 17 && currentHour < 21) timeCategory = 'evening';
        else if (currentHour >= 21 || currentHour < 6) timeCategory = 'night';
        if (timeKeywords[timeCategory].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 3;
        }
        // Day-of-week relevance
        const dayKeywords = {
            weekday: [
                'work',
                'business',
                'professional',
                'office',
                'meeting'
            ],
            weekend: [
                'weekend',
                'fun',
                'relax',
                'family',
                'leisure',
                'party'
            ]
        };
        const isWeekend = currentDay === 0 || currentDay === 6;
        const dayCategory = isWeekend ? 'weekend' : 'weekday';
        if (dayKeywords[dayCategory].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 2;
        }
        // Seasonal relevance (basic implementation)
        const month = now.getMonth();
        const seasonalKeywords = {
            spring: [
                'spring',
                'fresh',
                'new',
                'bloom',
                'growth'
            ],
            summer: [
                'summer',
                'hot',
                'vacation',
                'beach',
                'outdoor'
            ],
            fall: [
                'fall',
                'autumn',
                'harvest',
                'cozy',
                'warm'
            ],
            winter: [
                'winter',
                'cold',
                'holiday',
                'celebration',
                'indoor'
            ]
        };
        let season = 'spring';
        if (month >= 5 && month <= 7) season = 'summer';
        else if (month >= 8 && month <= 10) season = 'fall';
        else if (month >= 11 || month <= 1) season = 'winter';
        if (seasonalKeywords[season].some((keyword)=>hashtagLower.includes(keyword))) {
            score += 2;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate competitor analysis score
   */ async calculateCompetitorAnalysis(hashtag, context, trendingData) {
        let score = 5; // Base score
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Analyze if competitors in the same industry are using this hashtag
        const industryKeywords = this.getIndustryKeywords(context.businessType);
        const competitorMentions = trendingData.articles.filter((article)=>{
            const content = `${article.title} ${article.description}`.toLowerCase();
            return industryKeywords.some((keyword)=>content.includes(keyword.toLowerCase())) && content.includes(hashtagLower);
        }).length;
        // Score based on competitor usage
        if (competitorMentions >= 3) {
            score += 4; // High competitor usage indicates relevance
        } else if (competitorMentions >= 1) {
            score += 2; // Some competitor usage
        }
        // Check for oversaturation (too many competitors using the same hashtag)
        if (competitorMentions >= 10) {
            score -= 2; // Penalty for oversaturated hashtags
        }
        return Math.min(Math.max(score, 0), 10);
    }
    /**
   * Calculate semantic relevance score
   */ calculateSemanticRelevance(hashtag, context) {
        let score = 0;
        const hashtagLower = hashtag.toLowerCase().replace('#', '');
        // Content relevance (if post content is provided)
        if (context.postContent) {
            const contentLower = context.postContent.toLowerCase();
            const contentWords = contentLower.split(/\s+/);
            // Direct word match
            if (contentWords.some((word)=>word.includes(hashtagLower) || hashtagLower.includes(word))) {
                score += 8;
            }
            // Semantic similarity (basic implementation)
            const semanticKeywords = this.extractSemanticKeywords(context.postContent);
            if (semanticKeywords.some((keyword)=>hashtagLower.includes(keyword) || keyword.includes(hashtagLower))) {
                score += 6;
            }
        }
        // Target audience relevance
        if (context.targetAudience) {
            const audienceKeywords = context.targetAudience.toLowerCase().split(/[,\s]+/);
            if (audienceKeywords.some((keyword)=>hashtagLower.includes(keyword))) {
                score += 5;
            }
        }
        // Industry semantic relevance
        const industrySemantics = this.getIndustrySemantics(context.businessType);
        if (industrySemantics.some((semantic)=>hashtagLower.includes(semantic.toLowerCase()))) {
            score += 4;
        }
        return Math.min(score, 10);
    }
    /**
   * Calculate weighted total score
   */ calculateWeightedScore(breakdown, context) {
        // Weights can be adjusted based on business priorities
        const weights = {
            trendingScore: 0.25,
            businessRelevance: 0.20,
            engagementPotential: 0.15,
            platformOptimization: 0.12,
            locationRelevance: 0.10,
            semanticRelevance: 0.08,
            temporalRelevance: 0.06,
            competitorAnalysis: 0.04 // Competitive intelligence
        };
        let totalScore = 0;
        totalScore += breakdown.trendingScore * weights.trendingScore;
        totalScore += breakdown.businessRelevance * weights.businessRelevance;
        totalScore += breakdown.engagementPotential * weights.engagementPotential;
        totalScore += breakdown.platformOptimization * weights.platformOptimization;
        totalScore += breakdown.locationRelevance * weights.locationRelevance;
        totalScore += breakdown.semanticRelevance * weights.semanticRelevance;
        totalScore += breakdown.temporalRelevance * weights.temporalRelevance;
        totalScore += breakdown.competitorAnalysis * weights.competitorAnalysis;
        return Math.round(totalScore * 10) / 10; // Round to 1 decimal place
    }
    /**
   * Calculate confidence in the score
   */ calculateConfidence(breakdown, trendingData) {
        let confidence = 0;
        let factors = 0;
        // RSS data quality factor
        if (trendingData.articles.length >= 10) {
            confidence += 0.3;
            factors++;
        } else if (trendingData.articles.length >= 5) {
            confidence += 0.2;
            factors++;
        }
        // Score consistency factor
        const scores = Object.values(breakdown);
        const avgScore = scores.reduce((sum, score)=>sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score)=>sum + Math.pow(score - avgScore, 2), 0) / scores.length;
        if (variance < 4) {
            confidence += 0.3;
            factors++;
        } else if (variance < 9) {
            confidence += 0.2;
            factors++;
        }
        // High-scoring factors
        const highScores = scores.filter((score)=>score >= 7).length;
        if (highScores >= 4) {
            confidence += 0.4;
            factors++;
        } else if (highScores >= 2) {
            confidence += 0.3;
            factors++;
        }
        return factors > 0 ? Math.min(confidence, 1) : 0.5;
    }
    /**
   * Generate recommendation based on score and confidence
   */ generateRecommendation(totalScore, confidence) {
        if (totalScore >= 8 && confidence >= 0.7) return 'high';
        if (totalScore >= 6 && confidence >= 0.5) return 'medium';
        if (totalScore >= 4) return 'low';
        return 'avoid';
    }
    /**
   * Generate reasoning for the score
   */ generateReasoning(breakdown, context) {
        const reasoning = [];
        if (breakdown.trendingScore >= 8) {
            reasoning.push('Highly trending in RSS feeds and news sources');
        } else if (breakdown.trendingScore >= 6) {
            reasoning.push('Moderately trending in current news cycle');
        }
        if (breakdown.businessRelevance >= 8) {
            reasoning.push('Highly relevant to your business type and services');
        } else if (breakdown.businessRelevance >= 6) {
            reasoning.push('Good business relevance for your industry');
        }
        if (breakdown.engagementPotential >= 8) {
            reasoning.push('High potential for user engagement and interaction');
        }
        if (breakdown.platformOptimization >= 8) {
            reasoning.push(`Optimized for ${context.platform} platform algorithms`);
        }
        if (breakdown.locationRelevance >= 8) {
            reasoning.push('Strong local/geographic relevance');
        }
        if (breakdown.competitorAnalysis >= 7) {
            reasoning.push('Successfully used by industry competitors');
        }
        if (reasoning.length === 0) {
            reasoning.push('Basic hashtag with standard performance potential');
        }
        return reasoning;
    }
    /**
   * Get industry-specific keywords
   */ getIndustryKeywords(businessType) {
        const industryMap = {
            restaurant: [
                'food',
                'dining',
                'cuisine',
                'chef',
                'menu',
                'delicious',
                'taste',
                'recipe'
            ],
            retail: [
                'shopping',
                'fashion',
                'style',
                'sale',
                'deals',
                'boutique',
                'store',
                'brand'
            ],
            healthcare: [
                'health',
                'wellness',
                'medical',
                'care',
                'treatment',
                'doctor',
                'patient'
            ],
            fitness: [
                'workout',
                'gym',
                'fitness',
                'health',
                'training',
                'exercise',
                'strength'
            ],
            beauty: [
                'beauty',
                'skincare',
                'makeup',
                'salon',
                'spa',
                'treatment',
                'cosmetics'
            ],
            technology: [
                'tech',
                'digital',
                'innovation',
                'software',
                'app',
                'online',
                'data'
            ],
            education: [
                'learning',
                'education',
                'training',
                'course',
                'skill',
                'knowledge',
                'teach'
            ],
            automotive: [
                'car',
                'auto',
                'vehicle',
                'repair',
                'service',
                'maintenance',
                'drive'
            ],
            realestate: [
                'property',
                'home',
                'house',
                'real estate',
                'investment',
                'buy',
                'sell'
            ],
            legal: [
                'law',
                'legal',
                'attorney',
                'lawyer',
                'justice',
                'rights',
                'court'
            ]
        };
        return industryMap[businessType.toLowerCase()] || [
            'business',
            'service',
            'professional',
            'quality'
        ];
    }
    /**
   * Get industry semantic keywords
   */ getIndustrySemantics(businessType) {
        const semanticMap = {
            restaurant: [
                'culinary',
                'gastronomy',
                'hospitality',
                'ambiance',
                'flavor'
            ],
            retail: [
                'merchandise',
                'consumer',
                'lifestyle',
                'trend',
                'collection'
            ],
            healthcare: [
                'therapeutic',
                'diagnosis',
                'prevention',
                'recovery',
                'healing'
            ],
            fitness: [
                'performance',
                'endurance',
                'transformation',
                'motivation',
                'results'
            ],
            beauty: [
                'aesthetic',
                'enhancement',
                'rejuvenation',
                'glamour',
                'confidence'
            ],
            technology: [
                'automation',
                'efficiency',
                'connectivity',
                'intelligence',
                'solution'
            ],
            education: [
                'development',
                'growth',
                'achievement',
                'mastery',
                'expertise'
            ],
            automotive: [
                'performance',
                'reliability',
                'maintenance',
                'transportation',
                'mobility'
            ],
            realestate: [
                'investment',
                'location',
                'value',
                'opportunity',
                'lifestyle'
            ],
            legal: [
                'advocacy',
                'representation',
                'protection',
                'resolution',
                'compliance'
            ]
        };
        return semanticMap[businessType.toLowerCase()] || [
            'excellence',
            'quality',
            'service',
            'professional'
        ];
    }
    /**
   * Extract semantic keywords from content
   */ extractSemanticKeywords(content) {
        // Simple keyword extraction (can be enhanced with NLP)
        const words = content.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((word)=>word.length > 3);
        // Remove common stop words
        const stopWords = [
            'this',
            'that',
            'with',
            'have',
            'will',
            'from',
            'they',
            'been',
            'were',
            'said'
        ];
        return words.filter((word)=>!stopWords.includes(word));
    }
    /**
   * Generate context key for caching
   */ generateContextKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}`.toLowerCase();
    }
    /**
   * Get fallback score when analysis fails
   */ getFallbackScore(hashtag, context) {
        return {
            hashtag,
            totalScore: 5.0,
            breakdown: {
                trendingScore: 5,
                businessRelevance: 5,
                locationRelevance: 5,
                platformOptimization: 5,
                engagementPotential: 5,
                temporalRelevance: 5,
                competitorAnalysis: 5,
                semanticRelevance: 5
            },
            confidence: 0.3,
            recommendation: 'medium',
            reasoning: [
                'Fallback scoring due to analysis error'
            ]
        };
    }
}
const realtimeHashtagScorer = new RealtimeHashtagScorer();
}}),
"[project]/src/ai/intelligent-hashtag-mixer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Intelligent Hashtag Mixing Algorithm
 * Advanced algorithm that combines trending RSS hashtags with business-specific tags
 * for optimal reach and relevance using machine learning-inspired scoring
 */ __turbopack_context__.s({
    "IntelligentHashtagMixer": (()=>IntelligentHashtagMixer),
    "intelligentHashtagMixer": (()=>intelligentHashtagMixer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$realtime$2d$hashtag$2d$scorer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/realtime-hashtag-scorer.ts [app-route] (ecmascript)");
;
class IntelligentHashtagMixer {
    mixingCache = new Map();
    cacheTimeout = 20 * 60 * 1000;
    /**
   * Create intelligent hashtag mix using advanced algorithms
   */ async createIntelligentMix(advancedStrategy, viralStrategy, context) {
        const cacheKey = this.generateCacheKey(context);
        const cached = this.mixingCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.strategy;
        }
        try {
            // 🧠 STEP 1: Score all available hashtags
            const allHashtags = this.collectAllHashtags(advancedStrategy, viralStrategy);
            const scoredHashtags = await this.scoreAllHashtags(allHashtags, context);
            // 🎯 STEP 2: Apply intelligent mixing algorithm
            const mixedHashtags = this.applyMixingAlgorithm(scoredHashtags, context);
            // 📊 STEP 3: Analyze the final mix
            const analytics = this.analyzeMix(mixedHashtags, advancedStrategy, context);
            // 🏗️ STEP 4: Structure the final strategy
            const strategy = this.structureFinalStrategy(mixedHashtags, analytics);
            // Cache the result
            this.mixingCache.set(cacheKey, {
                strategy,
                timestamp: Date.now()
            });
            return strategy;
        } catch (error) {
            return this.getFallbackMix(context);
        }
    }
    /**
   * Collect all hashtags from different sources
   */ collectAllHashtags(advancedStrategy, viralStrategy) {
        const hashtags = [];
        // Advanced strategy hashtags (highest priority)
        advancedStrategy.finalRecommendations.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'advanced_rss',
                priority: 10
            });
        });
        advancedStrategy.topTrending.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_trending',
                priority: 9
            });
        });
        advancedStrategy.emergingTrends.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_emerging',
                priority: 8
            });
        });
        advancedStrategy.businessOptimized.forEach((analysis)=>{
            hashtags.push({
                hashtag: analysis.hashtag,
                source: 'rss_business',
                priority: 8
            });
        });
        // Viral strategy hashtags (medium priority)
        viralStrategy.trending.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_trending',
                priority: 7
            });
        });
        viralStrategy.viral.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_engagement',
                priority: 7
            });
        });
        viralStrategy.niche.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_niche',
                priority: 6
            });
        });
        viralStrategy.location.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_location',
                priority: 6
            });
        });
        viralStrategy.platform.forEach((hashtag)=>{
            hashtags.push({
                hashtag,
                source: 'viral_platform',
                priority: 5
            });
        });
        // Remove duplicates while preserving highest priority
        const uniqueHashtags = new Map();
        hashtags.forEach((item)=>{
            const existing = uniqueHashtags.get(item.hashtag);
            if (!existing || item.priority > existing.priority) {
                uniqueHashtags.set(item.hashtag, item);
            }
        });
        return Array.from(uniqueHashtags.values());
    }
    /**
   * Score all hashtags using the realtime scorer
   */ async scoreAllHashtags(hashtags, context) {
        const scoringContext = {
            businessType: context.businessType,
            businessName: context.businessName,
            location: context.location,
            platform: context.platform,
            postContent: context.postContent,
            targetAudience: context.targetAudience,
            services: context.services
        };
        const scoredHashtags = await Promise.all(hashtags.map(async (item)=>({
                ...item,
                score: await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$realtime$2d$hashtag$2d$scorer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["realtimeHashtagScorer"].scoreHashtag(item.hashtag, scoringContext)
            })));
        return scoredHashtags;
    }
    /**
   * Apply intelligent mixing algorithm based on context priority
   */ applyMixingAlgorithm(scoredHashtags, context) {
        return scoredHashtags.map((item)=>{
            let finalScore = 0;
            // Base score from realtime scorer
            finalScore += item.score.totalScore * 0.4;
            // Priority bonus from source
            finalScore += item.priority * 0.2;
            // Context-specific adjustments
            switch(context.priority){
                case 'reach':
                    finalScore += item.score.breakdown.trendingScore * 0.3;
                    finalScore += item.score.breakdown.engagementPotential * 0.1;
                    break;
                case 'relevance':
                    finalScore += item.score.breakdown.businessRelevance * 0.3;
                    finalScore += item.score.breakdown.semanticRelevance * 0.1;
                    break;
                case 'engagement':
                    finalScore += item.score.breakdown.engagementPotential * 0.3;
                    finalScore += item.score.breakdown.platformOptimization * 0.1;
                    break;
                case 'balanced':
                default:
                    finalScore += (item.score.breakdown.trendingScore + item.score.breakdown.businessRelevance + item.score.breakdown.engagementPotential) * 0.1;
                    break;
            }
            // RSS weight adjustment
            if (item.source.includes('rss')) {
                finalScore += finalScore * context.rssWeight * 0.2;
            }
            // Business weight adjustment
            if (item.source.includes('business') || item.source.includes('niche')) {
                finalScore += finalScore * context.businessWeight * 0.2;
            }
            // Confidence bonus
            finalScore += item.score.confidence * 2;
            return {
                ...item,
                finalScore: Math.round(finalScore * 10) / 10
            };
        }).sort((a, b)=>b.finalScore - a.finalScore);
    }
    /**
   * Analyze the quality of the final mix
   */ analyzeMix(mixedHashtags, advancedStrategy, context) {
        const top15 = mixedHashtags.slice(0, 15);
        // Calculate RSS influence
        const rssHashtags = top15.filter((item)=>item.source.includes('rss')).length;
        const rssInfluence = Math.round(rssHashtags / 15 * 100);
        // Calculate average business relevance
        const avgBusinessRelevance = top15.reduce((sum, item)=>sum + item.score.breakdown.businessRelevance, 0) / 15;
        // Calculate average trending score
        const avgTrendingScore = top15.reduce((sum, item)=>sum + item.score.breakdown.trendingScore, 0) / 15;
        // Calculate diversity score
        const sources = new Set(top15.map((item)=>item.source));
        const diversityScore = Math.min(sources.size / 6 * 10, 10); // Max 6 different sources
        // Calculate overall confidence
        const avgConfidence = top15.reduce((sum, item)=>sum + item.score.confidence, 0) / 15;
        const confidenceLevel = Math.round(avgConfidence * 10);
        // Determine mixing strategy description
        const mixingStrategy = this.describeMixingStrategy(context, rssInfluence, avgBusinessRelevance);
        return {
            rssInfluence,
            businessRelevance: Math.round(avgBusinessRelevance * 10) / 10,
            trendingScore: Math.round(avgTrendingScore * 10) / 10,
            diversityScore: Math.round(diversityScore * 10) / 10,
            confidenceLevel,
            mixingStrategy
        };
    }
    /**
   * Structure the final hashtag strategy
   */ structureFinalStrategy(mixedHashtags, analytics) {
        const top15 = mixedHashtags.slice(0, 15);
        return {
            primary: top15.slice(0, 5).map((item)=>item.hashtag),
            secondary: top15.slice(5, 10).map((item)=>item.hashtag),
            tertiary: top15.slice(10, 15).map((item)=>item.hashtag),
            final: top15.map((item)=>item.hashtag),
            analytics
        };
    }
    /**
   * Describe the mixing strategy used
   */ describeMixingStrategy(context, rssInfluence, businessRelevance) {
        let strategy = `${context.priority.charAt(0).toUpperCase() + context.priority.slice(1)}-focused mixing`;
        if (rssInfluence >= 70) {
            strategy += ' with heavy RSS trending emphasis';
        } else if (rssInfluence >= 40) {
            strategy += ' with balanced RSS integration';
        } else {
            strategy += ' with minimal RSS influence';
        }
        if (businessRelevance >= 8) {
            strategy += ' and high business relevance';
        } else if (businessRelevance >= 6) {
            strategy += ' and moderate business relevance';
        } else {
            strategy += ' and broad market appeal';
        }
        return strategy;
    }
    /**
   * Generate cache key for mixing context
   */ generateCacheKey(context) {
        return `${context.businessType}-${context.location}-${context.platform}-${context.priority}-${context.rssWeight}-${context.businessWeight}`.toLowerCase();
    }
    /**
   * Get fallback mix when algorithm fails
   */ getFallbackMix(context) {
        const fallbackHashtags = [
            '#trending',
            '#viral',
            `#${context.businessType.replace(/\s+/g, '')}`,
            '#local',
            '#community',
            '#business',
            '#quality',
            '#professional',
            '#service',
            '#new',
            '#amazing',
            '#best',
            '#popular',
            '#love',
            '#today'
        ];
        return {
            primary: fallbackHashtags.slice(0, 5),
            secondary: fallbackHashtags.slice(5, 10),
            tertiary: fallbackHashtags.slice(10, 15),
            final: fallbackHashtags,
            analytics: {
                rssInfluence: 0,
                businessRelevance: 5.0,
                trendingScore: 3.0,
                diversityScore: 4.0,
                confidenceLevel: 3,
                mixingStrategy: 'Fallback strategy due to algorithm failure'
            }
        };
    }
    /**
   * Get optimal mixing weights based on business type and platform
   */ getOptimalWeights(businessType, platform) {
        // Platform-specific weights
        const platformWeights = {
            instagram: {
                rssWeight: 0.7,
                businessWeight: 0.6
            },
            tiktok: {
                rssWeight: 0.8,
                businessWeight: 0.4
            },
            twitter: {
                rssWeight: 0.9,
                businessWeight: 0.5
            },
            linkedin: {
                rssWeight: 0.6,
                businessWeight: 0.8
            },
            facebook: {
                rssWeight: 0.5,
                businessWeight: 0.7
            },
            pinterest: {
                rssWeight: 0.6,
                businessWeight: 0.6
            }
        };
        // Business type adjustments
        const businessAdjustments = {
            restaurant: {
                rssBoost: 0.1,
                businessBoost: 0.2
            },
            retail: {
                rssBoost: 0.2,
                businessBoost: 0.1
            },
            healthcare: {
                rssBoost: 0.0,
                businessBoost: 0.3
            },
            technology: {
                rssBoost: 0.3,
                businessBoost: 0.1
            },
            fitness: {
                rssBoost: 0.2,
                businessBoost: 0.2
            },
            beauty: {
                rssBoost: 0.2,
                businessBoost: 0.1
            }
        };
        const platformWeight = platformWeights[platform.toLowerCase()] || {
            rssWeight: 0.6,
            businessWeight: 0.6
        };
        const businessAdj = businessAdjustments[businessType.toLowerCase()] || {
            rssBoost: 0.1,
            businessBoost: 0.1
        };
        return {
            rssWeight: Math.min(platformWeight.rssWeight + businessAdj.rssBoost, 1.0),
            businessWeight: Math.min(platformWeight.businessWeight + businessAdj.businessBoost, 1.0)
        };
    }
}
const intelligentHashtagMixer = new IntelligentHashtagMixer();
}}),
"[project]/src/ai/hashtag-performance-tracker.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Hashtag Performance Tracking and Learning System
 * Tracks hashtag performance and learns from successful combinations
 * to improve future hashtag generation with machine learning insights
 */ __turbopack_context__.s({
    "HashtagPerformanceTracker": (()=>HashtagPerformanceTracker),
    "hashtagPerformanceTracker": (()=>hashtagPerformanceTracker)
});
class HashtagPerformanceTracker {
    performanceData = new Map();
    combinationData = new Map();
    postHistory = [];
    storageKey = 'hashtag_performance_data';
    combinationStorageKey = 'hashtag_combination_data';
    postHistoryKey = 'post_performance_history';
    constructor(){
        this.loadPerformanceData();
    }
    /**
   * Track performance of a post with its hashtags
   */ trackPostPerformance(postData) {
        // Store post data
        this.postHistory.push(postData);
        // Update individual hashtag performance
        postData.hashtags.forEach((hashtag)=>{
            this.updateHashtagPerformance(hashtag, postData);
        });
        // Update combination performance
        this.updateCombinationPerformance(postData);
        // Save to storage
        this.savePerformanceData();
    }
    /**
   * Get performance insights for hashtag optimization
   */ getPerformanceInsights(businessType, platform, location) {
        const filteredData = this.filterPerformanceData(businessType, platform, location);
        return {
            topPerformingHashtags: this.getTopPerformingHashtags(filteredData),
            bestCombinations: this.getBestCombinations(businessType, platform, location),
            platformInsights: this.getPlatformInsights(),
            businessTypeInsights: this.getBusinessTypeInsights(),
            temporalInsights: this.getTemporalInsights(),
            learningRecommendations: this.generateLearningRecommendations(filteredData)
        };
    }
    /**
   * Get hashtag recommendations based on learning
   */ getLearnedRecommendations(businessType, platform, location, count = 10) {
        const recommendations = [];
        // Get hashtags that performed well for similar contexts
        const contextualData = Array.from(this.performanceData.values()).filter((data)=>{
            const businessMatch = data.businessTypes[businessType]?.avgEngagement > 0;
            const platformMatch = data.platforms[platform]?.avgEngagement > 0;
            const locationMatch = data.locations[location]?.avgEngagement > 0;
            return businessMatch || platformMatch || locationMatch;
        }).sort((a, b)=>b.averageEngagement - a.averageEngagement);
        contextualData.slice(0, count).forEach((data)=>{
            let confidence = 0;
            let reason = '';
            // Calculate confidence based on performance and context match
            if (data.businessTypes[businessType]) {
                confidence += 0.4 * (data.businessTypes[businessType].avgEngagement / 100);
                reason += `Strong performance in ${businessType} (${data.businessTypes[businessType].avgEngagement.toFixed(1)} avg engagement). `;
            }
            if (data.platforms[platform]) {
                confidence += 0.3 * (data.platforms[platform].avgEngagement / 100);
                reason += `Good ${platform} performance (${data.platforms[platform].avgEngagement.toFixed(1)} avg). `;
            }
            if (data.locations[location]) {
                confidence += 0.2 * (data.locations[location].avgEngagement / 100);
                reason += `Local relevance in ${location}. `;
            }
            confidence += 0.1 * data.successRate;
            reason += `${data.successRate.toFixed(1)}% success rate over ${data.usageCount} uses.`;
            recommendations.push({
                hashtag: data.hashtag,
                confidence: Math.min(confidence, 1),
                reason: reason.trim()
            });
        });
        return recommendations.sort((a, b)=>b.confidence - a.confidence);
    }
    /**
   * Update individual hashtag performance
   */ updateHashtagPerformance(hashtag, postData) {
        let data = this.performanceData.get(hashtag);
        if (!data) {
            data = {
                hashtag,
                usageCount: 0,
                totalEngagement: 0,
                averageEngagement: 0,
                platforms: {},
                businessTypes: {},
                locations: {},
                timePatterns: {
                    hourly: {},
                    daily: {},
                    monthly: {}
                },
                lastUsed: postData.timestamp,
                firstUsed: postData.timestamp,
                trendingScore: 0,
                successRate: 0
            };
        }
        // Update basic metrics
        data.usageCount++;
        data.totalEngagement += postData.engagement.total;
        data.averageEngagement = data.totalEngagement / data.usageCount;
        data.lastUsed = postData.timestamp;
        // Update platform performance
        if (!data.platforms[postData.platform]) {
            data.platforms[postData.platform] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.platforms[postData.platform].usage++;
        data.platforms[postData.platform].engagement += postData.engagement.total;
        data.platforms[postData.platform].avgEngagement = data.platforms[postData.platform].engagement / data.platforms[postData.platform].usage;
        // Update business type performance
        if (!data.businessTypes[postData.businessType]) {
            data.businessTypes[postData.businessType] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.businessTypes[postData.businessType].usage++;
        data.businessTypes[postData.businessType].engagement += postData.engagement.total;
        data.businessTypes[postData.businessType].avgEngagement = data.businessTypes[postData.businessType].engagement / data.businessTypes[postData.businessType].usage;
        // Update location performance
        if (!data.locations[postData.location]) {
            data.locations[postData.location] = {
                usage: 0,
                engagement: 0,
                avgEngagement: 0
            };
        }
        data.locations[postData.location].usage++;
        data.locations[postData.location].engagement += postData.engagement.total;
        data.locations[postData.location].avgEngagement = data.locations[postData.location].engagement / data.locations[postData.location].usage;
        // Update time patterns
        const hour = postData.timestamp.getHours();
        const day = postData.timestamp.getDay();
        const month = postData.timestamp.getMonth();
        if (!data.timePatterns.hourly[hour]) {
            data.timePatterns.hourly[hour] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.hourly[hour].usage++;
        data.timePatterns.hourly[hour].engagement += postData.engagement.total;
        if (!data.timePatterns.daily[day]) {
            data.timePatterns.daily[day] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.daily[day].usage++;
        data.timePatterns.daily[day].engagement += postData.engagement.total;
        if (!data.timePatterns.monthly[month]) {
            data.timePatterns.monthly[month] = {
                usage: 0,
                engagement: 0
            };
        }
        data.timePatterns.monthly[month].usage++;
        data.timePatterns.monthly[month].engagement += postData.engagement.total;
        // Update success rate
        const successfulPosts = this.postHistory.filter((post)=>post.hashtags.includes(hashtag) && post.success).length;
        data.successRate = successfulPosts / data.usageCount * 100;
        this.performanceData.set(hashtag, data);
    }
    /**
   * Update combination performance
   */ updateCombinationPerformance(postData) {
        const combinationKey = postData.hashtags.sort().join('|');
        let data = this.combinationData.get(combinationKey);
        if (!data) {
            data = {
                combination: postData.hashtags.sort(),
                usageCount: 0,
                totalEngagement: 0,
                averageEngagement: 0,
                successRate: 0,
                businessType: postData.businessType,
                platform: postData.platform,
                location: postData.location,
                lastUsed: postData.timestamp,
                performanceScore: 0
            };
        }
        data.usageCount++;
        data.totalEngagement += postData.engagement.total;
        data.averageEngagement = data.totalEngagement / data.usageCount;
        data.lastUsed = postData.timestamp;
        // Calculate success rate for this combination
        const combinationPosts = this.postHistory.filter((post)=>post.hashtags.sort().join('|') === combinationKey);
        const successfulCombinationPosts = combinationPosts.filter((post)=>post.success);
        data.successRate = successfulCombinationPosts.length / combinationPosts.length * 100;
        // Calculate performance score (weighted average of engagement and success rate)
        data.performanceScore = data.averageEngagement * 0.7 + data.successRate * 0.3;
        this.combinationData.set(combinationKey, data);
    }
    /**
   * Get top performing hashtags
   */ getTopPerformingHashtags(data) {
        return data.filter((d)=>d.usageCount >= 3) // Minimum usage for reliability
        .sort((a, b)=>b.averageEngagement - a.averageEngagement).slice(0, 20).map((d)=>({
                hashtag: d.hashtag,
                avgEngagement: d.averageEngagement,
                successRate: d.successRate,
                recommendationStrength: this.getRecommendationStrength(d)
            }));
    }
    /**
   * Get best hashtag combinations
   */ getBestCombinations(businessType, platform, location) {
        return Array.from(this.combinationData.values()).filter((data)=>{
            if (businessType && data.businessType !== businessType) return false;
            if (platform && data.platform !== platform) return false;
            if (location && data.location !== location) return false;
            return data.usageCount >= 2;
        }).sort((a, b)=>b.performanceScore - a.performanceScore).slice(0, 10).map((data)=>({
                hashtags: data.combination,
                avgEngagement: data.averageEngagement,
                successRate: data.successRate,
                context: `${data.businessType} on ${data.platform} in ${data.location}`
            }));
    }
    /**
   * Get platform-specific insights
   */ getPlatformInsights() {
        const insights = {};
        // Group data by platform
        const platformData = {};
        this.performanceData.forEach((data)=>{
            Object.keys(data.platforms).forEach((platform)=>{
                if (!platformData[platform]) platformData[platform] = [];
                platformData[platform].push(data);
            });
        });
        // Generate insights for each platform
        Object.entries(platformData).forEach(([platform, data])=>{
            const sortedData = data.filter((d)=>d.platforms[platform].usage >= 2).sort((a, b)=>b.platforms[platform].avgEngagement - a.platforms[platform].avgEngagement);
            insights[platform] = {
                bestHashtags: sortedData.slice(0, 10).map((d)=>d.hashtag),
                avgEngagement: sortedData.reduce((sum, d)=>sum + d.platforms[platform].avgEngagement, 0) / sortedData.length,
                optimalCount: this.calculateOptimalHashtagCount(platform)
            };
        });
        return insights;
    }
    /**
   * Get business type insights
   */ getBusinessTypeInsights() {
        const insights = {};
        // Group data by business type
        const businessData = {};
        this.performanceData.forEach((data)=>{
            Object.keys(data.businessTypes).forEach((businessType)=>{
                if (!businessData[businessType]) businessData[businessType] = [];
                businessData[businessType].push(data);
            });
        });
        // Generate insights for each business type
        Object.entries(businessData).forEach(([businessType, data])=>{
            const sortedData = data.filter((d)=>d.businessTypes[businessType].usage >= 2).sort((a, b)=>b.businessTypes[businessType].avgEngagement - a.businessTypes[businessType].avgEngagement);
            insights[businessType] = {
                bestHashtags: sortedData.slice(0, 8).map((d)=>d.hashtag),
                avgEngagement: sortedData.reduce((sum, d)=>sum + d.businessTypes[businessType].avgEngagement, 0) / sortedData.length,
                successPatterns: this.identifySuccessPatterns(businessType)
            };
        });
        return insights;
    }
    /**
   * Get temporal insights
   */ getTemporalInsights() {
        // Analyze best posting times
        const timePerformance = [];
        for(let day = 0; day < 7; day++){
            for(let hour = 0; hour < 24; hour++){
                const posts = this.postHistory.filter((post)=>post.timestamp.getDay() === day && post.timestamp.getHours() === hour);
                if (posts.length >= 3) {
                    const avgEngagement = posts.reduce((sum, post)=>sum + post.engagement.total, 0) / posts.length;
                    timePerformance.push({
                        hour,
                        day,
                        performance: avgEngagement
                    });
                }
            }
        }
        const bestTimes = timePerformance.sort((a, b)=>b.performance - a.performance).slice(0, 10);
        // Seasonal trends (simplified)
        const seasonalTrends = {
            spring: this.getSeasonalHashtags([
                2,
                3,
                4
            ]),
            summer: this.getSeasonalHashtags([
                5,
                6,
                7
            ]),
            fall: this.getSeasonalHashtags([
                8,
                9,
                10
            ]),
            winter: this.getSeasonalHashtags([
                11,
                0,
                1
            ])
        };
        return {
            bestTimes,
            seasonalTrends
        };
    }
    /**
   * Generate learning recommendations
   */ generateLearningRecommendations(data) {
        const recommendations = [];
        // Analyze performance patterns
        const highPerformers = data.filter((d)=>d.averageEngagement > 50 && d.successRate > 70);
        const lowPerformers = data.filter((d)=>d.averageEngagement < 10 || d.successRate < 30);
        if (highPerformers.length > 0) {
            recommendations.push(`Focus on high-performing hashtags like ${highPerformers.slice(0, 3).map((d)=>d.hashtag).join(', ')}`);
        }
        if (lowPerformers.length > 5) {
            recommendations.push(`Consider replacing underperforming hashtags: ${lowPerformers.slice(0, 3).map((d)=>d.hashtag).join(', ')}`);
        }
        // Platform-specific recommendations
        const platformPerformance = this.analyzePlatformPerformance();
        Object.entries(platformPerformance).forEach(([platform, perf])=>{
            if (perf.avgEngagement > 0) {
                recommendations.push(`${platform} performs best with ${perf.optimalCount} hashtags, focus on ${perf.topHashtag}`);
            }
        });
        return recommendations;
    }
    /**
   * Helper methods
   */ filterPerformanceData(businessType, platform, location) {
        return Array.from(this.performanceData.values()).filter((data)=>{
            if (businessType && !data.businessTypes[businessType]) return false;
            if (platform && !data.platforms[platform]) return false;
            if (location && !data.locations[location]) return false;
            return true;
        });
    }
    getRecommendationStrength(data) {
        if (data.averageEngagement > 50 && data.successRate > 70) return 'high';
        if (data.averageEngagement > 20 && data.successRate > 50) return 'medium';
        return 'low';
    }
    calculateOptimalHashtagCount(platform) {
        const platformPosts = this.postHistory.filter((post)=>post.platform === platform);
        const countPerformance = {};
        platformPosts.forEach((post)=>{
            const count = post.hashtags.length;
            if (!countPerformance[count]) countPerformance[count] = [];
            countPerformance[count].push(post.engagement.total);
        });
        let bestCount = 10; // Default
        let bestAvg = 0;
        Object.entries(countPerformance).forEach(([count, engagements])=>{
            if (engagements.length >= 3) {
                const avg = engagements.reduce((sum, eng)=>sum + eng, 0) / engagements.length;
                if (avg > bestAvg) {
                    bestAvg = avg;
                    bestCount = parseInt(count);
                }
            }
        });
        return bestCount;
    }
    identifySuccessPatterns(businessType) {
        const patterns = [];
        const businessPosts = this.postHistory.filter((post)=>post.businessType === businessType && post.success);
        // Analyze common hashtag patterns in successful posts
        const hashtagFrequency = {};
        businessPosts.forEach((post)=>{
            post.hashtags.forEach((hashtag)=>{
                hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;
            });
        });
        const commonHashtags = Object.entries(hashtagFrequency).filter(([, count])=>count >= 3).sort(([, a], [, b])=>b - a).slice(0, 5).map(([hashtag])=>hashtag);
        if (commonHashtags.length > 0) {
            patterns.push(`Common successful hashtags: ${commonHashtags.join(', ')}`);
        }
        return patterns;
    }
    getSeasonalHashtags(months) {
        const seasonalPosts = this.postHistory.filter((post)=>months.includes(post.timestamp.getMonth()));
        const hashtagFrequency = {};
        seasonalPosts.forEach((post)=>{
            post.hashtags.forEach((hashtag)=>{
                hashtagFrequency[hashtag] = (hashtagFrequency[hashtag] || 0) + 1;
            });
        });
        return Object.entries(hashtagFrequency).sort(([, a], [, b])=>b - a).slice(0, 10).map(([hashtag])=>hashtag);
    }
    analyzePlatformPerformance() {
        const analysis = {};
        // Group by platform
        const platformGroups = {};
        this.postHistory.forEach((post)=>{
            if (!platformGroups[post.platform]) platformGroups[post.platform] = [];
            platformGroups[post.platform].push(post);
        });
        Object.entries(platformGroups).forEach(([platform, posts])=>{
            const avgEngagement = posts.reduce((sum, post)=>sum + post.engagement.total, 0) / posts.length;
            const optimalCount = this.calculateOptimalHashtagCount(platform);
            // Find top hashtag for this platform
            const hashtagPerf = {};
            posts.forEach((post)=>{
                post.hashtags.forEach((hashtag)=>{
                    if (!hashtagPerf[hashtag]) hashtagPerf[hashtag] = [];
                    hashtagPerf[hashtag].push(post.engagement.total);
                });
            });
            const topHashtag = Object.entries(hashtagPerf).filter(([, engagements])=>engagements.length >= 2).map(([hashtag, engagements])=>({
                    hashtag,
                    avg: engagements.reduce((sum, eng)=>sum + eng, 0) / engagements.length
                })).sort((a, b)=>b.avg - a.avg)[0]?.hashtag || '';
            analysis[platform] = {
                avgEngagement,
                optimalCount,
                topHashtag
            };
        });
        return analysis;
    }
    /**
   * Storage methods
   */ loadPerformanceData() {
        try {
            const performanceData = localStorage.getItem(this.storageKey);
            if (performanceData) {
                const parsed = JSON.parse(performanceData);
                this.performanceData = new Map(Object.entries(parsed));
            }
            const combinationData = localStorage.getItem(this.combinationStorageKey);
            if (combinationData) {
                const parsed = JSON.parse(combinationData);
                this.combinationData = new Map(Object.entries(parsed));
            }
            const postHistory = localStorage.getItem(this.postHistoryKey);
            if (postHistory) {
                this.postHistory = JSON.parse(postHistory).map((post)=>({
                        ...post,
                        timestamp: new Date(post.timestamp)
                    }));
            }
        } catch (error) {
        // Initialize with empty data if loading fails
        }
    }
    savePerformanceData() {
        try {
            // Save performance data
            const performanceObj = Object.fromEntries(this.performanceData);
            localStorage.setItem(this.storageKey, JSON.stringify(performanceObj));
            // Save combination data
            const combinationObj = Object.fromEntries(this.combinationData);
            localStorage.setItem(this.combinationStorageKey, JSON.stringify(combinationObj));
            // Save post history (keep only last 1000 posts)
            const recentHistory = this.postHistory.slice(-1000);
            localStorage.setItem(this.postHistoryKey, JSON.stringify(recentHistory));
        } catch (error) {
        // Handle storage errors gracefully
        }
    }
    /**
   * Clear all performance data (for testing or reset)
   */ clearPerformanceData() {
        this.performanceData.clear();
        this.combinationData.clear();
        this.postHistory = [];
        localStorage.removeItem(this.storageKey);
        localStorage.removeItem(this.combinationStorageKey);
        localStorage.removeItem(this.postHistoryKey);
    }
}
const hashtagPerformanceTracker = new HashtagPerformanceTracker();
}}),
"[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Viral Hashtag Engine - Real-time trending hashtag generation
 * Integrates with RSS feeds and trending data to generate viral hashtags
 * Enhanced with Advanced Trending Hashtag Analyzer for superior relevance
 */ __turbopack_context__.s({
    "ViralHashtagEngine": (()=>ViralHashtagEngine),
    "viralHashtagEngine": (()=>viralHashtagEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$trending$2d$hashtag$2d$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/advanced-trending-hashtag-analyzer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/intelligent-hashtag-mixer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/hashtag-performance-tracker.ts [app-route] (ecmascript)");
;
;
;
;
class ViralHashtagEngine {
    /**
   * Generate viral hashtag strategy using advanced RSS analysis and real-time trending data
   */ async generateViralHashtags(businessType, businessName, location, platform, services, targetAudience) {
        try {
            // 🚀 ENHANCED: Use Advanced Hashtag Analyzer for superior RSS integration
            const analysisContext = {
                businessType,
                businessName,
                location,
                platform,
                services,
                targetAudience
            };
            // Get advanced hashtag analysis with RSS integration
            const advancedAnalysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$trending$2d$hashtag$2d$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["advancedHashtagAnalyzer"].analyzeHashtagTrends(analysisContext);
            // Get traditional trending data as backup
            const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
                businessType,
                location,
                platform,
                targetAudience
            });
            // 🔥 ENHANCED: Generate hashtag categories using advanced analysis
            const trending = this.extractTrendingFromAnalysis(advancedAnalysis, trendingData);
            const viral = this.getEnhancedViralHashtags(businessType, platform, advancedAnalysis);
            const niche = this.getEnhancedNicheHashtags(businessType, services, advancedAnalysis);
            const location_tags = this.getEnhancedLocationHashtags(location, advancedAnalysis);
            const community = this.getCommunityHashtags(businessType, targetAudience);
            const seasonal = this.getSeasonalHashtags();
            const platform_tags = this.getEnhancedPlatformHashtags(platform, advancedAnalysis);
            // 🧠 ENHANCED: Use Intelligent Hashtag Mixer for optimal combination
            const mixingContext = {
                businessType,
                businessName,
                location,
                platform,
                postContent: undefined,
                targetAudience,
                services,
                priority: 'balanced',
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["intelligentHashtagMixer"].getOptimalWeights(businessType, platform)
            };
            // Create the current strategy for mixing
            const currentStrategy = {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total: [] // Will be filled by mixer
            };
            // Apply intelligent mixing
            const intelligentMix = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$intelligent$2d$hashtag$2d$mixer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["intelligentHashtagMixer"].createIntelligentMix(advancedAnalysis, currentStrategy, mixingContext);
            // 🧠 ENHANCED: Get learned recommendations from performance tracking
            const learnedRecommendations = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getLearnedRecommendations(businessType, platform, location, 5);
            // 📊 Get performance insights for improvement suggestions
            const performanceInsights = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getPerformanceInsights(businessType, platform, location);
            // 🎯 Integrate learned recommendations with intelligent mix
            const enhancedTotal = this.integrateLearnedRecommendations(intelligentMix.final, learnedRecommendations, performanceInsights);
            // Use the enhanced hashtags as the final total
            const total = enhancedTotal;
            // Calculate confidence score based on RSS data quality
            const confidenceScore = this.calculateConfidenceScore(advancedAnalysis);
            return {
                trending,
                viral,
                niche,
                location: location_tags,
                community,
                seasonal,
                platform: platform_tags,
                total,
                analytics: {
                    topPerformers: advancedAnalysis.finalRecommendations.slice(0, 5),
                    emergingTrends: advancedAnalysis.emergingTrends.map((t)=>t.hashtag).slice(0, 3),
                    businessOptimized: advancedAnalysis.businessOptimized.map((b)=>b.hashtag).slice(0, 3),
                    rssSourced: this.extractRSSSourcedHashtags(advancedAnalysis),
                    confidenceScore,
                    // Include intelligent mixing analytics
                    mixingStrategy: {
                        rssInfluence: intelligentMix.analytics.rssInfluence,
                        businessRelevance: intelligentMix.analytics.businessRelevance,
                        trendingScore: intelligentMix.analytics.trendingScore,
                        diversityScore: intelligentMix.analytics.diversityScore,
                        confidenceLevel: intelligentMix.analytics.confidenceLevel,
                        algorithm: intelligentMix.analytics.mixingStrategy
                    },
                    // Include performance learning insights
                    learningInsights: {
                        learnedRecommendations,
                        historicalPerformance: this.calculateHistoricalPerformance(performanceInsights),
                        improvementSuggestions: performanceInsights.learningRecommendations
                    }
                }
            };
        } catch (error) {
            return this.getFallbackHashtags(businessType, location, platform);
        }
    }
    /**
   * Extract trending hashtags from advanced analysis
   */ extractTrendingFromAnalysis(advancedAnalysis, fallbackData) {
        // Prioritize RSS-sourced trending hashtags
        const rssHashtags = advancedAnalysis.topTrending.filter((analysis)=>analysis.sources.some((source)=>source !== 'business_generator' && source !== 'fallback')).map((analysis)=>analysis.hashtag);
        // Add high-scoring emerging trends
        const emergingHashtags = advancedAnalysis.emergingTrends.filter((analysis)=>analysis.trendingScore >= 3).map((analysis)=>analysis.hashtag);
        // Combine with fallback data if needed
        const combined = [
            ...rssHashtags,
            ...emergingHashtags,
            ...fallbackData.hashtags
        ];
        // Remove duplicates and return top trending
        return Array.from(new Set(combined)).slice(0, 8);
    }
    /**
   * Get enhanced viral hashtags using RSS analysis
   */ getEnhancedViralHashtags(businessType, platform, advancedAnalysis) {
        // Get traditional viral hashtags
        const traditionalViral = this.getViralHashtags(businessType, platform);
        // Add high-engagement hashtags from RSS analysis
        const rssViral = advancedAnalysis.topTrending.filter((analysis)=>analysis.engagementPotential >= 7).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssViral.slice(0, 4),
            ...traditionalViral.slice(0, 3)
        ];
        return Array.from(new Set(combined)).slice(0, 7);
    }
    /**
   * Get enhanced niche hashtags using business analysis
   */ getEnhancedNicheHashtags(businessType, services, advancedAnalysis) {
        // Get traditional niche hashtags
        const traditionalNiche = this.getNicheHashtags(businessType, services);
        // Add business-optimized hashtags from RSS analysis
        const rssNiche = advancedAnalysis.businessOptimized.filter((analysis)=>analysis.businessRelevance >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssNiche.slice(0, 3),
            ...traditionalNiche.slice(0, 3)
        ];
        return Array.from(new Set(combined)).slice(0, 6);
    }
    /**
   * Get enhanced location hashtags using location analysis
   */ getEnhancedLocationHashtags(location, advancedAnalysis) {
        // Get traditional location hashtags
        const traditionalLocation = this.getLocationHashtags(location);
        // Add location-specific hashtags from RSS analysis
        const rssLocation = advancedAnalysis.locationSpecific.filter((analysis)=>analysis.locationRelevance >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssLocation.slice(0, 2),
            ...traditionalLocation.slice(0, 2)
        ];
        return Array.from(new Set(combined)).slice(0, 4);
    }
    /**
   * Get enhanced platform hashtags using platform analysis
   */ getEnhancedPlatformHashtags(platform, advancedAnalysis) {
        // Get traditional platform hashtags
        const traditionalPlatform = this.getPlatformHashtags(platform);
        // Add platform-optimized hashtags from RSS analysis
        const rssPlatform = advancedAnalysis.platformNative.filter((analysis)=>analysis.platformOptimization >= 6).map((analysis)=>analysis.hashtag);
        // Combine and prioritize
        const combined = [
            ...rssPlatform.slice(0, 2),
            ...traditionalPlatform.slice(0, 2)
        ];
        return Array.from(new Set(combined)).slice(0, 4);
    }
    /**
   * Intelligent hashtag mixing algorithm
   */ intelligentHashtagMixing(hashtags, advancedAnalysis) {
        // Create a scoring system for hashtag selection
        const hashtagScores = new Map();
        // Score each hashtag based on multiple factors
        hashtags.forEach((hashtag)=>{
            let score = 0;
            // Find analysis for this hashtag
            const analysis = this.findHashtagAnalysis(hashtag, advancedAnalysis);
            if (analysis) {
                score += analysis.relevanceScore * 0.3;
                score += analysis.trendingScore * 0.25;
                score += analysis.businessRelevance * 0.2;
                score += analysis.engagementPotential * 0.15;
                score += analysis.platformOptimization * 0.1;
            } else {
                // Default score for hashtags not in analysis
                score = 5;
            }
            hashtagScores.set(hashtag, score);
        });
        // Sort by score and return top hashtags
        const sortedHashtags = Array.from(hashtagScores.entries()).sort(([, scoreA], [, scoreB])=>scoreB - scoreA).map(([hashtag])=>hashtag);
        return sortedHashtags.slice(0, 15);
    }
    /**
   * Find hashtag analysis in advanced analysis results
   */ findHashtagAnalysis(hashtag, advancedAnalysis) {
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        return allAnalyses.find((analysis)=>analysis.hashtag === hashtag);
    }
    /**
   * Calculate confidence score based on RSS data quality
   */ calculateConfidenceScore(advancedAnalysis) {
        let score = 0;
        let factors = 0;
        // Factor 1: Number of RSS sources
        const rssSourceCount = this.countRSSSources(advancedAnalysis);
        if (rssSourceCount > 0) {
            score += Math.min(rssSourceCount * 2, 10);
            factors++;
        }
        // Factor 2: Quality of trending data
        const trendingQuality = advancedAnalysis.topTrending.length > 0 ? 8 : 3;
        score += trendingQuality;
        factors++;
        // Factor 3: Business relevance coverage
        const businessCoverage = advancedAnalysis.businessOptimized.length >= 3 ? 9 : 5;
        score += businessCoverage;
        factors++;
        // Factor 4: Emerging trends availability
        const emergingTrends = advancedAnalysis.emergingTrends.length > 0 ? 7 : 4;
        score += emergingTrends;
        factors++;
        return factors > 0 ? Math.round(score / factors) : 5;
    }
    /**
   * Count RSS sources in analysis
   */ countRSSSources(advancedAnalysis) {
        const sources = new Set();
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        allAnalyses.forEach((analysis)=>{
            analysis.sources.forEach((source)=>{
                if (source !== 'business_generator' && source !== 'fallback') {
                    sources.add(source);
                }
            });
        });
        return sources.size;
    }
    /**
   * Extract RSS-sourced hashtags
   */ extractRSSSourcedHashtags(advancedAnalysis) {
        const allAnalyses = [
            ...advancedAnalysis.topTrending,
            ...advancedAnalysis.businessOptimized,
            ...advancedAnalysis.locationSpecific,
            ...advancedAnalysis.platformNative,
            ...advancedAnalysis.emergingTrends
        ];
        return allAnalyses.filter((analysis)=>analysis.sources.some((source)=>source !== 'business_generator' && source !== 'fallback')).map((analysis)=>analysis.hashtag).slice(0, 8);
    }
    /**
   * Get high-engagement viral hashtags
   */ getViralHashtags(businessType, platform) {
        const viralHashtags = {
            general: [
                '#viral',
                '#trending',
                '#fyp',
                '#explore',
                '#discover',
                '#amazing',
                '#incredible',
                '#mustsee'
            ],
            instagram: [
                '#instagood',
                '#photooftheday',
                '#instadaily',
                '#reels',
                '#explorepage'
            ],
            tiktok: [
                '#fyp',
                '#foryou',
                '#viral',
                '#trending',
                '#foryoupage'
            ],
            facebook: [
                '#viral',
                '#share',
                '#community',
                '#local',
                '#trending'
            ],
            twitter: [
                '#trending',
                '#viral',
                '#breaking',
                '#news',
                '#update'
            ],
            linkedin: [
                '#professional',
                '#business',
                '#networking',
                '#career',
                '#industry'
            ]
        };
        const general = viralHashtags.general.sort(()=>0.5 - Math.random()).slice(0, 4);
        const platformSpecific = viralHashtags[platform.toLowerCase()] || [];
        return [
            ...general,
            ...platformSpecific.slice(0, 3)
        ];
    }
    /**
   * Get business-specific niche hashtags
   */ getNicheHashtags(businessType, services) {
        const nicheMap = {
            restaurant: [
                '#foodie',
                '#delicious',
                '#freshfood',
                '#localeats',
                '#foodlover',
                '#tasty',
                '#chef',
                '#dining'
            ],
            bakery: [
                '#freshbaked',
                '#artisan',
                '#homemade',
                '#bakery',
                '#pastry',
                '#bread',
                '#dessert',
                '#sweet'
            ],
            fitness: [
                '#fitness',
                '#workout',
                '#health',
                '#gym',
                '#strong',
                '#motivation',
                '#fitlife',
                '#training'
            ],
            beauty: [
                '#beauty',
                '#skincare',
                '#makeup',
                '#glam',
                '#selfcare',
                '#beautiful',
                '#style',
                '#cosmetics'
            ],
            tech: [
                '#tech',
                '#innovation',
                '#digital',
                '#software',
                '#technology',
                '#startup',
                '#coding',
                '#ai'
            ],
            retail: [
                '#shopping',
                '#fashion',
                '#style',
                '#sale',
                '#newcollection',
                '#boutique',
                '#trendy',
                '#deals'
            ]
        };
        const baseNiche = nicheMap[businessType.toLowerCase()] || [
            '#business',
            '#service',
            '#quality',
            '#professional'
        ];
        // Add service-specific hashtags if provided
        if (services) {
            const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            baseNiche.push(...serviceHashtags);
        }
        return baseNiche.slice(0, 6);
    }
    /**
   * Get location-based hashtags
   */ getLocationHashtags(location) {
        const locationParts = location.split(',').map((part)=>part.trim());
        const hashtags = [];
        locationParts.forEach((part)=>{
            const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '');
            if (cleanLocation.length > 2) {
                hashtags.push(`#${cleanLocation.toLowerCase()}`);
            }
        });
        // Add generic location hashtags
        hashtags.push('#local', '#community', '#neighborhood');
        return hashtags.slice(0, 5);
    }
    /**
   * Get community engagement hashtags
   */ getCommunityHashtags(businessType, targetAudience) {
        const communityHashtags = [
            '#community',
            '#local',
            '#support',
            '#family',
            '#friends',
            '#together',
            '#love'
        ];
        if (targetAudience) {
            const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
            const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
            communityHashtags.push(...audienceHashtags);
        }
        return communityHashtags.slice(0, 5);
    }
    /**
   * Get seasonal/timely hashtags
   */ getSeasonalHashtags() {
        const now = new Date();
        const month = now.getMonth();
        const day = now.getDate();
        // Seasonal hashtags based on current time
        const seasonal = {
            0: [
                '#newyear',
                '#january',
                '#fresh',
                '#newbeginnings'
            ],
            1: [
                '#february',
                '#love',
                '#valentine',
                '#winter'
            ],
            2: [
                '#march',
                '#spring',
                '#fresh',
                '#bloom'
            ],
            3: [
                '#april',
                '#spring',
                '#easter',
                '#renewal'
            ],
            4: [
                '#may',
                '#spring',
                '#mothers',
                '#bloom'
            ],
            5: [
                '#june',
                '#summer',
                '#fathers',
                '#sunshine'
            ],
            6: [
                '#july',
                '#summer',
                '#vacation',
                '#hot'
            ],
            7: [
                '#august',
                '#summer',
                '#vacation',
                '#sunny'
            ],
            8: [
                '#september',
                '#fall',
                '#autumn',
                '#backtoschool'
            ],
            9: [
                '#october',
                '#fall',
                '#halloween',
                '#autumn'
            ],
            10: [
                '#november',
                '#thanksgiving',
                '#grateful',
                '#fall'
            ],
            11: [
                '#december',
                '#christmas',
                '#holiday',
                '#winter'
            ] // December
        };
        return seasonal[month] || [
            '#today',
            '#now',
            '#current'
        ];
    }
    /**
   * Get platform-specific hashtags
   */ getPlatformHashtags(platform) {
        const platformHashtags = {
            instagram: [
                '#instagram',
                '#insta',
                '#ig'
            ],
            facebook: [
                '#facebook',
                '#fb',
                '#social'
            ],
            twitter: [
                '#twitter',
                '#tweet',
                '#x'
            ],
            linkedin: [
                '#linkedin',
                '#professional',
                '#business'
            ],
            tiktok: [
                '#tiktok',
                '#tt',
                '#video'
            ]
        };
        return platformHashtags[platform.toLowerCase()] || [
            '#social',
            '#media'
        ];
    }
    /**
   * Get business-relevant trending hashtags
   */ getBusinessTrendingHashtags(businessType, platform) {
        // This would integrate with real trending APIs in production
        const trendingByBusiness = {
            restaurant: [
                '#foodtrends',
                '#eats2024',
                '#localfood',
                '#foodie'
            ],
            fitness: [
                '#fitness2024',
                '#healthtrends',
                '#workout',
                '#wellness'
            ],
            beauty: [
                '#beautytrends',
                '#skincare2024',
                '#makeup',
                '#selfcare'
            ],
            tech: [
                '#tech2024',
                '#innovation',
                '#ai',
                '#digital'
            ],
            retail: [
                '#fashion2024',
                '#shopping',
                '#style',
                '#trends'
            ]
        };
        return trendingByBusiness[businessType.toLowerCase()] || [
            '#trending',
            '#popular',
            '#new'
        ];
    }
    /**
   * Optimize hashtag selection for maximum virality
   */ optimizeForVirality(hashtags) {
        // Remove duplicates
        const unique = Array.from(new Set(hashtags));
        // Sort by estimated engagement potential (simplified scoring)
        const scored = unique.map((tag)=>({
                tag,
                score: this.calculateViralScore(tag)
            }));
        scored.sort((a, b)=>b.score - a.score);
        return scored.slice(0, 15).map((item)=>item.tag);
    }
    /**
   * Calculate viral potential score for a hashtag
   */ calculateViralScore(hashtag) {
        let score = 0;
        // High-engagement keywords get bonus points
        const viralKeywords = [
            'viral',
            'trending',
            'fyp',
            'explore',
            'amazing',
            'incredible'
        ];
        if (viralKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 10;
        }
        // Platform-specific hashtags get bonus
        const platformKeywords = [
            'instagram',
            'tiktok',
            'reels',
            'story'
        ];
        if (platformKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 5;
        }
        // Local hashtags get moderate bonus
        const localKeywords = [
            'local',
            'community',
            'neighborhood'
        ];
        if (localKeywords.some((keyword)=>hashtag.toLowerCase().includes(keyword))) {
            score += 3;
        }
        // Length penalty (very long hashtags perform worse)
        if (hashtag.length > 20) score -= 2;
        if (hashtag.length > 30) score -= 5;
        return score + Math.random(); // Add randomness for variety
    }
    /**
   * Enhanced fallback hashtags when trending data fails
   */ getFallbackHashtags(businessType, location, platform) {
        const fallbackTotal = [
            '#trending',
            '#viral',
            `#${businessType.replace(/\s+/g, '')}`,
            '#local',
            '#community',
            '#amazing',
            '#quality',
            '#professional',
            '#popular',
            '#new',
            '#support',
            '#service',
            `#${platform.toLowerCase()}`,
            '#today',
            '#love'
        ];
        return {
            trending: [
                '#trending',
                '#viral',
                '#popular',
                '#new'
            ],
            viral: [
                '#amazing',
                '#incredible',
                '#mustsee',
                '#wow'
            ],
            niche: [
                `#${businessType.replace(/\s+/g, '')}`,
                '#quality',
                '#professional',
                '#service'
            ],
            location: [
                '#local',
                '#community',
                `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`
            ],
            community: [
                '#community',
                '#support',
                '#family',
                '#love'
            ],
            seasonal: [
                '#today',
                '#now'
            ],
            platform: [
                `#${platform.toLowerCase()}`
            ],
            total: fallbackTotal,
            analytics: {
                topPerformers: fallbackTotal.slice(0, 5),
                emergingTrends: [
                    '#trending',
                    '#viral',
                    '#new'
                ],
                businessOptimized: [
                    `#${businessType.replace(/\s+/g, '')}`,
                    '#quality',
                    '#professional'
                ],
                rssSourced: [],
                confidenceScore: 3 // Low confidence for fallback
            }
        };
    }
    /**
   * 🧠 ENHANCED: Integrate learned recommendations with intelligent mix
   */ integrateLearnedRecommendations(intelligentMix, learnedRecommendations, performanceInsights) {
        const enhancedHashtags = [
            ...intelligentMix
        ];
        // Replace low-confidence hashtags with high-confidence learned recommendations
        const highConfidenceRecommendations = learnedRecommendations.filter((rec)=>rec.confidence >= 0.7);
        if (highConfidenceRecommendations.length > 0) {
            // Find hashtags in the mix that might be replaced
            const replaceableIndices = [];
            // Look for hashtags that aren't in the top performers
            const topPerformers = performanceInsights.topPerformingHashtags.map((h)=>h.hashtag);
            enhancedHashtags.forEach((hashtag, index)=>{
                if (!topPerformers.includes(hashtag) && index >= 10) {
                    replaceableIndices.push(index);
                }
            });
            // Replace up to 3 hashtags with learned recommendations
            const replacementCount = Math.min(highConfidenceRecommendations.length, replaceableIndices.length, 3);
            for(let i = 0; i < replacementCount; i++){
                const indexToReplace = replaceableIndices[i];
                const recommendation = highConfidenceRecommendations[i];
                // Only replace if the recommendation isn't already in the mix
                if (!enhancedHashtags.includes(recommendation.hashtag)) {
                    enhancedHashtags[indexToReplace] = recommendation.hashtag;
                }
            }
        }
        return enhancedHashtags;
    }
    /**
   * Calculate historical performance score
   */ calculateHistoricalPerformance(performanceInsights) {
        if (!performanceInsights.topPerformingHashtags.length) return 0;
        const avgEngagement = performanceInsights.topPerformingHashtags.reduce((sum, hashtag)=>sum + hashtag.avgEngagement, 0) / performanceInsights.topPerformingHashtags.length;
        const avgSuccessRate = performanceInsights.topPerformingHashtags.reduce((sum, hashtag)=>sum + hashtag.successRate, 0) / performanceInsights.topPerformingHashtags.length;
        // Weighted score: 70% engagement, 30% success rate
        return Math.round((avgEngagement * 0.7 + avgSuccessRate * 0.3) * 10) / 10;
    }
    /**
   * 📊 ENHANCED: Method to track hashtag performance after post creation
   */ trackHashtagPerformance(hashtags, platform, businessType, location, engagement, success = false) {
        const postData = {
            postId: `post_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            hashtags,
            platform,
            businessType,
            location,
            timestamp: new Date(),
            engagement,
            success
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].trackPostPerformance(postData);
    }
    /**
   * 📈 Get performance insights for hashtag optimization
   */ getHashtagPerformanceInsights(businessType, platform, location) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getPerformanceInsights(businessType, platform, location);
    }
    /**
   * 🎯 Get learned hashtag recommendations
   */ getLearnedHashtagRecommendations(businessType, platform, location, count = 10) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$hashtag$2d$performance$2d$tracker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashtagPerformanceTracker"].getLearnedRecommendations(businessType, platform, location, count);
    }
}
const viralHashtagEngine = new ViralHashtagEngine();
}}),
"[project]/src/ai/dynamic-cta-generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Dynamic CTA Generator - Creates compelling, conversion-focused call-to-actions
 * Uses AI and business intelligence to generate CTAs that drive action
 */ __turbopack_context__.s({
    "DynamicCTAGenerator": (()=>DynamicCTAGenerator),
    "dynamicCTAGenerator": (()=>dynamicCTAGenerator)
});
class DynamicCTAGenerator {
    /**
   * Generate dynamic, conversion-focused CTA
   */ async generateDynamicCTA(businessName, businessType, location, platform, contentGoal = 'engagement', services, targetAudience) {
        // Select optimal CTA style based on business context
        const ctaStyle = this.selectOptimalCTAStyle(businessType, platform, contentGoal);
        // Generate primary CTA
        const primary = this.generateCTAByStyle(ctaStyle, businessName, businessType, location, platform, services);
        // Generate alternatives for A/B testing
        const alternatives = this.generateAlternativeCTAs(businessName, businessType, location, platform, services);
        // Get reasoning for CTA choice
        const reasoning = this.getCTAReasoning(ctaStyle, businessType, platform);
        return {
            primary,
            alternatives,
            style: ctaStyle,
            reasoning,
            platform
        };
    }
    /**
   * Select optimal CTA style based on business context
   */ selectOptimalCTAStyle(businessType, platform, contentGoal) {
        const businessCTAMap = {
            restaurant: [
                'URGENCY',
                'INVITATION',
                'SENSORY',
                'LOCAL_REFERENCE'
            ],
            bakery: [
                'SENSORY',
                'URGENCY',
                'INVITATION',
                'COMMUNITY'
            ],
            fitness: [
                'CHALLENGE',
                'BENEFIT_FOCUSED',
                'MOTIVATION',
                'PERSONAL'
            ],
            beauty: [
                'TRANSFORMATION',
                'CONFIDENCE',
                'EXCLUSIVE',
                'PERSONAL'
            ],
            retail: [
                'URGENCY',
                'EXCLUSIVE',
                'BENEFIT_FOCUSED',
                'DISCOVERY'
            ],
            tech: [
                'INNOVATION',
                'BENEFIT_FOCUSED',
                'CURIOSITY',
                'PROFESSIONAL'
            ],
            service: [
                'DIRECT_ACTION',
                'BENEFIT_FOCUSED',
                'TRUST',
                'LOCAL_REFERENCE'
            ]
        };
        const platformCTAMap = {
            instagram: [
                'VISUAL',
                'DISCOVERY',
                'COMMUNITY',
                'INVITATION'
            ],
            facebook: [
                'COMMUNITY',
                'LOCAL_REFERENCE',
                'INVITATION',
                'SHARE'
            ],
            twitter: [
                'URGENCY',
                'CURIOSITY',
                'DIRECT_ACTION',
                'TRENDING'
            ],
            linkedin: [
                'PROFESSIONAL',
                'BENEFIT_FOCUSED',
                'NETWORKING',
                'EXPERTISE'
            ],
            tiktok: [
                'CHALLENGE',
                'TRENDING',
                'VIRAL',
                'FUN'
            ]
        };
        const goalCTAMap = {
            engagement: [
                'CURIOSITY',
                'COMMUNITY',
                'INVITATION',
                'QUESTION'
            ],
            conversion: [
                'URGENCY',
                'BENEFIT_FOCUSED',
                'EXCLUSIVE',
                'DIRECT_ACTION'
            ],
            awareness: [
                'DISCOVERY',
                'CURIOSITY',
                'SHARE',
                'VIRAL'
            ],
            retention: [
                'COMMUNITY',
                'LOYALTY',
                'PERSONAL',
                'APPRECIATION'
            ]
        };
        // Get possible styles from each category
        const businessStyles = businessCTAMap[businessType.toLowerCase()] || [
            'DIRECT_ACTION',
            'BENEFIT_FOCUSED'
        ];
        const platformStyles = platformCTAMap[platform.toLowerCase()] || [
            'DIRECT_ACTION'
        ];
        const goalStyles = goalCTAMap[contentGoal.toLowerCase()] || [
            'ENGAGEMENT'
        ];
        // Find intersection or pick best match
        const allStyles = [
            ...businessStyles,
            ...platformStyles,
            ...goalStyles
        ];
        const styleCounts = allStyles.reduce((acc, style)=>{
            acc[style] = (acc[style] || 0) + 1;
            return acc;
        }, {});
        // Return style with highest count (most relevant)
        const bestStyle = Object.entries(styleCounts).sort(([, a], [, b])=>b - a)[0][0];
        return bestStyle;
    }
    /**
   * Generate CTA based on selected style
   */ generateCTAByStyle(style, businessName, businessType, location, platform, services) {
        const timestamp = Date.now();
        const variation = timestamp % 4; // 4 variations per style
        const ctaTemplates = {
            URGENCY: [
                `Book now - limited spots!`,
                `Don't wait - call today!`,
                `Limited time offer - act fast!`,
                `Only a few left - grab yours!`
            ],
            INVITATION: [
                `Come experience the difference!`,
                `Visit us this weekend!`,
                `Join our community today!`,
                `See what everyone's talking about!`
            ],
            CHALLENGE: [
                `Try to find better - we dare you!`,
                `Challenge yourself today!`,
                `Beat this quality anywhere!`,
                `Prove us wrong - we're confident!`
            ],
            BENEFIT_FOCUSED: [
                `Get more for your money!`,
                `Save time and hassle!`,
                `Double your results!`,
                `Feel the difference immediately!`
            ],
            COMMUNITY: [
                `Join the ${location} family!`,
                `Be part of something special!`,
                `Connect with your neighbors!`,
                `Share the love with friends!`
            ],
            CURIOSITY: [
                `Discover what makes us different!`,
                `Find out why locals choose us!`,
                `See what the buzz is about!`,
                `Uncover ${location}'s best kept secret!`
            ],
            LOCAL_REFERENCE: [
                `${location}'s favorite spot awaits!`,
                `Proudly serving ${location} families!`,
                `Your neighborhood ${businessType}!`,
                `Where ${location} locals go!`
            ],
            SENSORY: [
                `Taste the difference today!`,
                `Experience pure quality!`,
                `Feel the freshness!`,
                `Savor every moment!`
            ],
            EXCLUSIVE: [
                `VIP treatment awaits you!`,
                `Exclusive access - members only!`,
                `Premium experience guaranteed!`,
                `Elite service, just for you!`
            ],
            DIRECT_ACTION: [
                `Call us now!`,
                `Book your appointment!`,
                `Order online today!`,
                `Get started immediately!`
            ]
        };
        const templates = ctaTemplates[style] || ctaTemplates.DIRECT_ACTION;
        let cta = templates[variation];
        // Personalize with business name or location when appropriate
        if (Math.random() > 0.5 && !cta.includes(businessName) && !cta.includes(location)) {
            const personalizations = [
                `at ${businessName}`,
                `with ${businessName}`,
                `- ${businessName}`,
                `@ ${businessName}`
            ];
            const personalization = personalizations[variation % personalizations.length];
            cta = `${cta.replace('!', '')} ${personalization}!`;
        }
        return cta;
    }
    /**
   * Generate alternative CTAs for A/B testing
   */ generateAlternativeCTAs(businessName, businessType, location, platform, services) {
        const alternativeStyles = [
            'URGENCY',
            'INVITATION',
            'BENEFIT_FOCUSED',
            'COMMUNITY',
            'CURIOSITY'
        ];
        const alternatives = [];
        alternativeStyles.forEach((style)=>{
            const cta = this.generateCTAByStyle(style, businessName, businessType, location, platform, services);
            alternatives.push(cta);
        });
        // Remove duplicates and return top 3
        return Array.from(new Set(alternatives)).slice(0, 3);
    }
    /**
   * Get reasoning for CTA choice
   */ getCTAReasoning(style, businessType, platform) {
        const reasoningMap = {
            URGENCY: `Creates immediate action through scarcity and time pressure, effective for ${businessType} conversions`,
            INVITATION: `Builds welcoming community feeling, perfect for local ${businessType} businesses`,
            CHALLENGE: `Engages competitive spirit and confidence, great for ${businessType} differentiation`,
            BENEFIT_FOCUSED: `Highlights clear value proposition, drives ${businessType} decision-making`,
            COMMUNITY: `Leverages local connection and belonging, ideal for neighborhood ${businessType}`,
            CURIOSITY: `Sparks interest and discovery, effective for ${platform} engagement`,
            LOCAL_REFERENCE: `Emphasizes local pride and familiarity, builds ${businessType} trust`,
            SENSORY: `Appeals to emotional and physical experience, perfect for ${businessType}`,
            EXCLUSIVE: `Creates premium positioning and special treatment feeling`,
            DIRECT_ACTION: `Clear, straightforward instruction that drives immediate response`
        };
        return reasoningMap[style] || `Optimized for ${businessType} on ${platform} to drive engagement and conversions`;
    }
    /**
   * Generate platform-optimized CTA
   */ generatePlatformOptimizedCTA(baseCTA, platform) {
        const platformOptimizations = {
            instagram: (cta)=>`${cta} 📸✨`,
            facebook: (cta)=>`${cta} Share with friends! 👥`,
            twitter: (cta)=>`${cta} #${new Date().getFullYear()}`,
            linkedin: (cta)=>`${cta} Connect with us professionally.`,
            tiktok: (cta)=>`${cta} 🔥💯`
        };
        const optimizer = platformOptimizations[platform.toLowerCase()];
        return optimizer ? optimizer(baseCTA) : baseCTA;
    }
    /**
   * Generate time-sensitive CTA
   */ generateTimeSensitiveCTA(businessType, location) {
        const now = new Date();
        const hour = now.getHours();
        const day = now.getDay();
        const isWeekend = day === 0 || day === 6;
        if (hour < 11) {
            return `Start your day right - visit us now!`;
        } else if (hour < 14) {
            return `Perfect lunch break spot - come by!`;
        } else if (hour < 17) {
            return `Afternoon pick-me-up awaits!`;
        } else if (hour < 20) {
            return `End your day on a high note!`;
        } else {
            return `Evening treat - you deserve it!`;
        }
    }
    /**
   * Generate seasonal CTA
   */ generateSeasonalCTA(businessType, location) {
        const now = new Date();
        const month = now.getMonth();
        const seasonalCTAs = {
            0: [
                `New Year, new experiences - try us!`,
                `Start 2024 right with us!`
            ],
            1: [
                `Warm up with us this February!`,
                `Love is in the air - visit us!`
            ],
            2: [
                `Spring into action - book now!`,
                `Fresh start, fresh experience!`
            ],
            3: [
                `April showers bring May flowers - and great service!`,
                `Spring special awaits!`
            ],
            4: [
                `May we serve you today?`,
                `Mother's Day special - treat her!`
            ],
            5: [
                `Summer starts here - join us!`,
                `Father's Day celebration awaits!`
            ],
            6: [
                `Beat the heat with us!`,
                `Summer vibes, great service!`
            ],
            7: [
                `August special - don't miss out!`,
                `Late summer treat awaits!`
            ],
            8: [
                `Back to school, back to us!`,
                `Fall into great service!`
            ],
            9: [
                `October surprise awaits you!`,
                `Halloween special - spooktacular!`
            ],
            10: [
                `Thanksgiving gratitude - visit us!`,
                `Give thanks for great service!`
            ],
            11: [
                `Holiday magic awaits you!`,
                `Christmas special - ho ho ho!`
            ] // December
        };
        const monthCTAs = seasonalCTAs[month] || [
            `Visit us today!`,
            `Experience the difference!`
        ];
        return monthCTAs[Math.floor(Math.random() * monthCTAs.length)];
    }
}
const dynamicCTAGenerator = new DynamicCTAGenerator();
}}),
"[project]/src/ai/creative-enhancement.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced Business Intelligence & Strategic Content Generation System
 * Replaces generic templates with business-specific insights and strategic planning
 */ __turbopack_context__.s({
    "AntiRepetitionSystem": (()=>AntiRepetitionSystem),
    "BUSINESS_INTELLIGENCE_SYSTEM": (()=>BUSINESS_INTELLIGENCE_SYSTEM),
    "CONTENT_VARIATION_ENGINE": (()=>CONTENT_VARIATION_ENGINE),
    "CREATIVE_PROMPT_SYSTEM": (()=>CREATIVE_PROMPT_SYSTEM),
    "StrategicContentPlanner": (()=>StrategicContentPlanner),
    "analyzeBusinessContext": (()=>analyzeBusinessContext),
    "enhanceDesignCreativity": (()=>enhanceDesignCreativity),
    "generateBusinessSpecificCaption": (()=>generateBusinessSpecificCaption),
    "generateBusinessSpecificHeadline": (()=>generateBusinessSpecificHeadline),
    "generateBusinessSpecificSubheadline": (()=>generateBusinessSpecificSubheadline),
    "generateCreativeCTA": (()=>generateCreativeCTA),
    "generateCreativeHeadline": (()=>generateCreativeHeadline),
    "generateCreativeSeed": (()=>generateCreativeSeed),
    "generateCreativeSubheadline": (()=>generateCreativeSubheadline),
    "generateUnifiedContent": (()=>generateUnifiedContent),
    "getRandomElement": (()=>getRandomElement),
    "getRandomElements": (()=>getRandomElements)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/viral-hashtag-engine.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$dynamic$2d$cta$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/dynamic-cta-generator.ts [app-route] (ecmascript)");
;
;
;
// Word Repetition Removal Function - Fixes issues like "buy now now pay later"
function removeWordRepetitions(text) {
    // Simple approach: split by spaces and check for consecutive duplicate words
    const words = text.split(/\s+/);
    const cleanedWords = [];
    for(let i = 0; i < words.length; i++){
        const currentWord = words[i];
        const previousWord = cleanedWords[cleanedWords.length - 1];
        // Skip if current word is the same as previous word (case-insensitive)
        // Only for actual words (not empty strings)
        if (currentWord && previousWord && currentWord.toLowerCase() === previousWord.toLowerCase() && currentWord.trim().length > 0) {
            continue; // Skip this duplicate word
        }
        cleanedWords.push(currentWord);
    }
    return cleanedWords.join(' ');
}
// Shared AI initialization to avoid duplicate variable names
function initializeAI() {
    const geminiApiKey = process.env.GOOGLE_AI_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.GEMINI_API_KEY;
    const genAI = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](geminiApiKey);
    // Use the same model as Revo 1.0 service for consistency
    return genAI.getGenerativeModel({
        model: 'gemini-2.5-flash-image-preview',
        generationConfig: {
            temperature: 0.9,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 4096
        }
    });
}
// Dynamic approach instructions to force variety
function getApproachInstructions(approach, businessName, location, creativityBoost) {
    switch(approach){
        case 'DIRECT_BENEFIT':
            return `HEADLINES: Lead with specific benefit. Example: "8g Protein Per Cookie" SUBHEADLINES: Expand with business details. Example: "Finally snacks that fuel kids properly - made fresh daily in ${location}" CAPTIONS: Full benefit story with RSS/business data.`;
        case 'SOCIAL_PROOF':
            return `HEADLINES: Reference community adoption. Example: "200+ ${location} Families Agree" SUBHEADLINES: Add business specifics. Example: "Our protein cookies beat sugar crashes every time" CAPTIONS: Full social proof story with testimonials and business intelligence.`;
        case 'PROBLEM_SOLUTION':
            return `HEADLINES: State the problem. Example: "Sugar Crashes Ruining Snacktime" SUBHEADLINES: Present solution. Example: "${businessName}'s protein cookies keep energy steady for hours" CAPTIONS: Full problem-solution narrative with business details.`;
        case 'LOCAL_INSIDER':
            return `HEADLINES: Use local insider knowledge. Example: "${location} Parents Secret Weapon" SUBHEADLINES: Add business insider details. Example: "What 500+ local families know about our cookies" CAPTIONS: Full insider story with local references and business intelligence.`;
        case 'URGENCY_SCARCITY':
            return `HEADLINES: Create real urgency. Example: "Only 50 Packs Left" SUBHEADLINES: Add business context. Example: "This week's batch selling faster than expected" CAPTIONS: Full urgency story with business details and RSS trends.`;
        case 'QUESTION_HOOK':
            return `HEADLINES: Ask specific question. Example: "Tired of Sugar Crashes?" SUBHEADLINES: Hint at solution. Example: "${businessName} has the protein-packed answer parents love" CAPTIONS: Full question-answer story with business intelligence.`;
        case 'STATISTIC_LEAD':
            return `HEADLINES: Lead with business statistic. Example: "95% Same-Day Fix Rate" SUBHEADLINES: Add context. Example: "Our certified technicians solve most issues within hours" CAPTIONS: Full statistic story with business details and proof.`;
        case 'STORY_ANGLE':
            return `HEADLINES: Start story hook. Example: "Local Baker's Secret Recipe" SUBHEADLINES: Continue story. Example: "Three generations of ${location} families can't be wrong" CAPTIONS: Full story with business history and customer experiences.`;
        case 'COMPARISON':
            return `HEADLINES: Set up comparison. Example: "Better Than Downtown Options" SUBHEADLINES: Specify difference. Example: "Same quality, half the price, right in ${location}" CAPTIONS: Full comparison with business advantages and local benefits.`;
        case 'NEWS_TREND':
            return `HEADLINES: Connect to current news/trends. Example: "Holiday Rush Solution Found" SUBHEADLINES: Add business connection. Example: "${businessName} handles your busiest season stress-free" CAPTIONS: Full trend connection with RSS data and business solutions.`;
        default:
            return `Create unique content that could only apply to ${businessName} in ${location}. Be specific and authentic.`;
    }
}
// Dynamic CTA style instructions to force variety
function getCtaStyleInstructions(style, businessName, location) {
    switch(style){
        case 'DIRECT_ACTION':
            return `Use action verbs specific to the business. Example: "Grab your protein cookies today" NOT generic "Shop now".`;
        case 'INVITATION':
            return `Sound like a personal invitation from ${businessName}. Example: "Come taste what ${location} is talking about" NOT generic invites.`;
        case 'CHALLENGE':
            return `Challenge the customer to try something. Example: "Find better cookies - we dare you" NOT generic challenges.`;
        case 'BENEFIT_FOCUSED':
            return `Lead with the specific benefit. Example: "Get 8g protein per bite" NOT generic benefits.`;
        case 'COMMUNITY':
            return `Reference the ${location} community. Example: "Join 200+ ${location} families" NOT generic community language.`;
        case 'URGENCY':
            return `Create real urgency with specifics. Example: "Only 50 left this week" NOT generic "limited time".`;
        case 'CURIOSITY':
            return `Make them curious about something specific. Example: "See why kids ask for seconds" NOT generic curiosity.`;
        case 'LOCAL_REFERENCE':
            return `Use actual ${location} references. Example: "Better than Main Street bakery" NOT generic local references.`;
        case 'PERSONAL':
            return `Sound personal and direct. Example: "Your kids will thank you" NOT generic personal language.`;
        case 'EXCLUSIVE':
            return `Make it feel exclusive to ${businessName}. Example: "Only at ${businessName}" NOT generic exclusivity.`;
        default:
            return `Create a unique CTA that could only apply to ${businessName} in ${location}.`;
    }
}
const BUSINESS_INTELLIGENCE_SYSTEM = {
    industryInsights: {
        'restaurant': {
            trends: [
                'farm-to-table',
                'fusion cuisine',
                'sustainable dining',
                'local ingredients',
                'chef-driven menus',
                'wine pairing',
                'seasonal specialties'
            ],
            challenges: [
                'food costs',
                'staff retention',
                'customer loyalty',
                'online reviews',
                'seasonal fluctuations',
                'competition from chains'
            ],
            opportunities: [
                'private dining',
                'catering services',
                'cooking classes',
                'wine tastings',
                'local partnerships'
            ],
            uniqueValue: [
                'chef expertise',
                'local sourcing',
                'authentic recipes',
                'atmosphere',
                'service quality'
            ],
            customerPainPoints: [
                'long wait times',
                'expensive prices',
                'limited options',
                'poor service',
                'generic food'
            ],
            successMetrics: [
                'repeat customers',
                'online reviews',
                'word-of-mouth',
                'reservations',
                'social media engagement'
            ],
            localCompetition: [
                'chain restaurants',
                'fast food',
                'other local restaurants',
                'food trucks',
                'delivery services'
            ],
            seasonalOpportunities: [
                'summer outdoor dining',
                'winter comfort food',
                'holiday specials',
                'local events',
                'weather-based menus'
            ]
        },
        'technology': {
            trends: [
                'AI integration',
                'automation',
                'cloud solutions',
                'cybersecurity',
                'digital transformation',
                'remote work tools'
            ],
            challenges: [
                'rapid change',
                'skill gaps',
                'security',
                'scalability',
                'competition',
                'client retention'
            ],
            opportunities: [
                'consulting services',
                'custom development',
                'training programs',
                'maintenance contracts',
                'upgrades'
            ],
            uniqueValue: [
                'technical expertise',
                'local support',
                'custom solutions',
                'ongoing partnership',
                'industry knowledge'
            ],
            customerPainPoints: [
                'complex technology',
                'high costs',
                'poor support',
                'outdated systems',
                'security concerns'
            ],
            successMetrics: [
                'client satisfaction',
                'project completion',
                'referrals',
                'long-term contracts',
                'technical outcomes'
            ],
            localCompetition: [
                'large tech companies',
                'freelancers',
                'other local tech firms',
                'national chains',
                'online services'
            ],
            seasonalOpportunities: [
                'year-end planning',
                'tax season',
                'new fiscal year',
                'back-to-school',
                'holiday e-commerce'
            ]
        },
        'healthcare': {
            trends: [
                'telemedicine',
                'preventive care',
                'patient experience',
                'digital health',
                'personalized medicine',
                'wellness focus'
            ],
            challenges: [
                'regulations',
                'patient trust',
                'technology adoption',
                'insurance complexity',
                'staff shortages'
            ],
            opportunities: [
                'preventive programs',
                'specialized services',
                'wellness coaching',
                'community outreach',
                'partnerships'
            ],
            uniqueValue: [
                'medical expertise',
                'personalized care',
                'local accessibility',
                'trusted relationships',
                'comprehensive services'
            ],
            customerPainPoints: [
                'long wait times',
                'high costs',
                'complex insurance',
                'poor communication',
                'impersonal care'
            ],
            successMetrics: [
                'patient outcomes',
                'satisfaction scores',
                'referrals',
                'community trust',
                'health improvements'
            ],
            localCompetition: [
                'hospitals',
                'other clinics',
                'urgent care centers',
                'specialists',
                'online health services'
            ],
            seasonalOpportunities: [
                'flu season',
                'summer wellness',
                'back-to-school checkups',
                'holiday stress',
                'new year resolutions'
            ]
        },
        'fitness': {
            trends: [
                'home workouts',
                'personal training',
                'group classes',
                'mind-body connection',
                'nutrition integration',
                'wearable tech'
            ],
            challenges: [
                'member retention',
                'seasonal fluctuations',
                'competition',
                'staff turnover',
                'facility costs'
            ],
            opportunities: [
                'online programs',
                'corporate wellness',
                'specialized training',
                'nutrition coaching',
                'community events'
            ],
            uniqueValue: [
                'expert trainers',
                'community atmosphere',
                'personalized programs',
                'convenient location',
                'proven results'
            ],
            customerPainPoints: [
                'lack of motivation',
                'time constraints',
                'intimidation',
                'poor results',
                'expensive memberships'
            ],
            successMetrics: [
                'member retention',
                'goal achievement',
                'referrals',
                'class attendance',
                'community engagement'
            ],
            localCompetition: [
                'other gyms',
                'home equipment',
                'online programs',
                'personal trainers',
                'sports clubs'
            ],
            seasonalOpportunities: [
                'new year resolutions',
                'summer body prep',
                'holiday fitness',
                'back-to-school',
                'weather-based activities'
            ]
        },
        'retail': {
            trends: [
                'omnichannel',
                'personalization',
                'sustainability',
                'local sourcing',
                'experiential shopping',
                'community focus'
            ],
            challenges: [
                'online competition',
                'inventory management',
                'customer experience',
                'seasonal sales',
                'staff training'
            ],
            opportunities: [
                'online presence',
                'local partnerships',
                'loyalty programs',
                'events',
                'personal shopping'
            ],
            uniqueValue: [
                'curated selection',
                'personal service',
                'local knowledge',
                'quality products',
                'community connection'
            ],
            customerPainPoints: [
                'limited selection',
                'high prices',
                'poor service',
                'inconvenient hours',
                'lack of expertise'
            ],
            successMetrics: [
                'sales growth',
                'customer loyalty',
                'repeat purchases',
                'word-of-mouth',
                'community engagement'
            ],
            localCompetition: [
                'online retailers',
                'big box stores',
                'other local shops',
                'malls',
                'direct sales'
            ],
            seasonalOpportunities: [
                'holiday shopping',
                'back-to-school',
                'summer sales',
                'seasonal products',
                'local events'
            ]
        },
        'real-estate': {
            trends: [
                'virtual tours',
                'digital marketing',
                'local expertise',
                'investment focus',
                'sustainability',
                'smart homes'
            ],
            challenges: [
                'market fluctuations',
                'competition',
                'client acquisition',
                'market knowledge',
                'technology adoption'
            ],
            opportunities: [
                'investment properties',
                'property management',
                'consulting services',
                'local partnerships',
                'specialized markets'
            ],
            uniqueValue: [
                'local expertise',
                'market knowledge',
                'personal service',
                'proven track record',
                'community connections'
            ],
            customerPainPoints: [
                'high fees',
                'poor communication',
                'lack of expertise',
                'market uncertainty',
                'slow process'
            ],
            successMetrics: [
                'sales volume',
                'client satisfaction',
                'referrals',
                'market share',
                'repeat clients'
            ],
            localCompetition: [
                'other agents',
                'online platforms',
                'national companies',
                'for-sale-by-owner',
                'investors'
            ],
            seasonalOpportunities: [
                'spring market',
                'summer families',
                'fall investors',
                'winter deals',
                'holiday moves'
            ]
        },
        'automotive': {
            trends: [
                'electric vehicles',
                'digital services',
                'sustainability',
                'convenience',
                'technology integration',
                'online sales'
            ],
            challenges: [
                'parts shortages',
                'technician shortage',
                'technology changes',
                'competition',
                'customer expectations'
            ],
            opportunities: [
                'EV services',
                'mobile repair',
                'fleet services',
                'specialized repairs',
                'maintenance programs'
            ],
            uniqueValue: [
                'expert technicians',
                'honest service',
                'convenient location',
                'quality parts',
                'warranty support'
            ],
            customerPainPoints: [
                'expensive repairs',
                'poor service',
                'unreliable work',
                'long wait times',
                'hidden costs'
            ],
            successMetrics: [
                'customer satisfaction',
                'repeat business',
                'referrals',
                'online reviews',
                'service quality'
            ],
            localCompetition: [
                'dealerships',
                'other repair shops',
                'chain stores',
                'mobile services',
                'online parts'
            ],
            seasonalOpportunities: [
                'winter preparation',
                'summer road trips',
                'back-to-school',
                'holiday travel',
                'seasonal maintenance'
            ]
        },
        'beauty': {
            trends: [
                'clean beauty',
                'personalization',
                'sustainability',
                'wellness integration',
                'technology',
                'inclusivity'
            ],
            challenges: [
                'product costs',
                'staff retention',
                'trend changes',
                'competition',
                'client retention'
            ],
            opportunities: [
                'online services',
                'product sales',
                'membership programs',
                'events',
                'corporate services'
            ],
            uniqueValue: [
                'expert stylists',
                'quality products',
                'personalized service',
                'convenient location',
                'trend knowledge'
            ],
            customerPainPoints: [
                'high costs',
                'poor results',
                'long appointments',
                'limited availability',
                'product damage'
            ],
            successMetrics: [
                'client retention',
                'referrals',
                'online reviews',
                'service quality',
                'product sales'
            ],
            localCompetition: [
                'salons',
                'chain stores',
                'mobile services',
                'online products',
                'other beauty professionals'
            ],
            seasonalOpportunities: [
                'wedding season',
                'holiday parties',
                'summer styles',
                'back-to-school',
                'special events'
            ]
        }
    },
    audiencePsychology: {
        motivations: [
            'success',
            'security',
            'convenience',
            'status',
            'belonging',
            'growth',
            'health',
            'savings',
            'recognition'
        ],
        painPoints: [
            'time constraints',
            'budget concerns',
            'trust issues',
            'complexity',
            'uncertainty',
            'frustration',
            'stress'
        ],
        aspirations: [
            'better life',
            'success',
            'recognition',
            'peace of mind',
            'efficiency',
            'independence',
            'security'
        ],
        communication: [
            'clear benefits',
            'social proof',
            'emotional connection',
            'practical value',
            'expertise',
            'trust'
        ]
    },
    // New: Strategic Content Planning
    contentStrategy: {
        'awareness': {
            goal: 'Introduce business and build recognition',
            approach: 'Educational, informative, community-focused',
            contentTypes: [
                'industry insights',
                'local news',
                'educational tips',
                'community stories'
            ],
            emotionalTone: 'helpful, informative, community-minded'
        },
        'consideration': {
            goal: 'Build trust and demonstrate expertise',
            approach: 'Problem-solving, expertise demonstration, social proof',
            contentTypes: [
                'case studies',
                'expert tips',
                'customer stories',
                'industry knowledge'
            ],
            emotionalTone: 'expert, trustworthy, helpful'
        },
        'conversion': {
            goal: 'Drive action and sales',
            approach: 'Urgency, offers, clear benefits, strong CTAs',
            contentTypes: [
                'special offers',
                'limited time deals',
                'clear benefits',
                'action-oriented content'
            ],
            emotionalTone: 'urgent, compelling, confident'
        },
        'retention': {
            goal: 'Keep existing customers engaged',
            approach: 'Value-added content, community building, ongoing support',
            contentTypes: [
                'loyalty programs',
                'exclusive content',
                'community events',
                'ongoing value'
            ],
            emotionalTone: 'appreciative, supportive, community-focused'
        }
    }
};
class StrategicContentPlanner {
    static generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness') {
        const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
        const strategy = BUSINESS_INTELLIGENCE_SYSTEM.contentStrategy[contentGoal];
        // Analyze business strengths and opportunities
        const businessStrengths = this.analyzeBusinessStrengths(businessDetails, industry);
        const marketOpportunities = this.identifyMarketOpportunities(industry, location);
        const customerPainPoints = this.mapCustomerPainPoints(industry, businessStrengths);
        return {
            strategy: strategy,
            businessType: businessType,
            businessStrengths,
            marketOpportunities,
            customerPainPoints,
            contentAngle: this.determineContentAngle(contentGoal, businessStrengths, marketOpportunities),
            emotionalHook: this.selectEmotionalHook(contentGoal, customerPainPoints),
            valueProposition: this.craftValueProposition(businessStrengths, customerPainPoints),
            localRelevance: this.createLocalRelevance(location, industry, businessDetails)
        };
    }
    static analyzeBusinessStrengths(businessDetails, industry) {
        const strengths = [];
        if (businessDetails.experience) strengths.push(`${businessDetails.experience} years of experience`);
        if (businessDetails.expertise) strengths.push(`specialized in ${businessDetails.expertise}`);
        if (businessDetails.awards) strengths.push(`award-winning ${businessDetails.awards}`);
        if (businessDetails.certifications) strengths.push(`certified in ${businessDetails.certifications}`);
        if (businessDetails.uniqueServices) strengths.push(`unique ${businessDetails.uniqueServices} services`);
        // Add industry-specific strengths
        strengths.push(...industry.uniqueValue.slice(0, 3));
        return strengths;
    }
    static identifyMarketOpportunities(industry, location) {
        return industry.seasonalOpportunities.map((opportunity)=>`${opportunity} in ${location}`).slice(0, 3);
    }
    static mapCustomerPainPoints(industry, businessStrengths) {
        return industry.customerPainPoints.filter((painPoint)=>businessStrengths.some((strength)=>strength.toLowerCase().includes(painPoint.toLowerCase().replace(/\s+/g, '')))).slice(0, 3);
    }
    static determineContentAngle(contentGoal, businessStrengths, marketOpportunities) {
        const angles = {
            'awareness': [
                'educational',
                'community',
                'industry insights'
            ],
            'consideration': [
                'problem-solving',
                'expertise',
                'social proof'
            ],
            'conversion': [
                'benefits',
                'offers',
                'urgency'
            ],
            'retention': [
                'value-added',
                'community',
                'exclusive'
            ]
        };
        return angles[contentGoal] || angles['awareness'];
    }
    static selectEmotionalHook(contentGoal, customerPainPoints) {
        const hooks = {
            'awareness': [
                'curiosity',
                'community pride',
                'local knowledge'
            ],
            'consideration': [
                'frustration relief',
                'trust building',
                'expertise recognition'
            ],
            'conversion': [
                'urgency',
                'excitement',
                'confidence'
            ],
            'retention': [
                'appreciation',
                'belonging',
                'exclusive access'
            ]
        };
        return hooks[contentGoal] || hooks['awareness'];
    }
    static craftValueProposition(businessStrengths, customerPainPoints) {
        if (businessStrengths.length === 0 || customerPainPoints.length === 0) {
            return 'Quality service and expertise';
        }
        const strength = businessStrengths[0];
        const painPoint = customerPainPoints[0];
        return `We solve ${painPoint} with ${strength}`;
    }
    static createLocalRelevance(location, industry, businessDetails) {
        return {
            localMarket: `${location} market insights`,
            communityConnection: `${location} community focus`,
            localCompetition: `Understanding ${location} competition`,
            localOpportunities: industry.seasonalOpportunities.map((opp)=>`${opp} in ${location}`)
        };
    }
}
async function generateBusinessSpecificHeadline(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal);
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create dynamic AI prompt for headline generation with RSS trends and regional marketing intelligence
    const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];
    const trendingHashtags = trendingData?.hashtags?.slice(0, 3) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    const prompt = `You are a brilliant local marketing expert who deeply understands ${location} culture, language, and market dynamics. You stay updated with current trends and know exactly how businesses in ${location} market themselves successfully.

BUSINESS INTELLIGENCE:
- Business: ${businessName} (${businessType})
- Location: ${location}
- Experience: ${businessDetails.experience || 'established business'}
- Specialties: ${businessDetails.expertise || businessDetails.services || 'professional services'}
- Target Market: ${businessDetails.targetAudience || 'local community'}
- Marketing Goal: ${contentGoal}

CURRENT TRENDING CONTEXT (Use these insights to make content relevant):
- Trending Keywords: ${trendingKeywords.join(', ') || 'quality, authentic, local, fresh, community'}
- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Tone: ${regionalLanguage}

LOCAL MARKET INTELLIGENCE:
- Industry Trends: ${industry.trends.slice(0, 3).join(', ')}
- Competitive Advantages: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market Opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- How locals in ${location} talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

REGIONAL MARKETING STRATEGY:
You understand that in ${location}, people respond to ${marketingStyle} marketing. Use the trending keywords naturally and speak like locals do. Create content that feels authentic to ${location} culture and current market trends.

CONVERSION PSYCHOLOGY REQUIREMENTS:
- STRICT WORD LIMIT: Maximum 6 words only - no exceptions
- Use psychological triggers: scarcity, exclusivity, curiosity, FOMO
- Create urgency and desire - make people think "I NEED this NOW"
- Sound like a successful local marketer who knows conversion psychology
- Incorporate trending elements naturally (don't force them)
- Use language patterns that drive action in ${location}
- Focus on what makes people instantly want to experience ${businessName}
- Create curiosity gaps that make people want to know more

CONVERSION-FOCUSED EXAMPLES (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):
- "Secret Recipe Finally Revealed" (curiosity + exclusivity)
- "Last Batch This Week" (scarcity + urgency)
- "Addictive Flavors Warning Inside" (intrigue + benefit)
- "Hidden Gem Locals Obsess" (social proof + exclusivity)
- "Revolutionary Taste Experience Awaits" (innovation + anticipation)

CRITICAL ANTI-REPETITION INSTRUCTIONS:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

IMPORTANT: Generate ONLY ONE headline, not multiple options or lists.
Do NOT write "Here are headlines" or provide multiple choices.
Generate ONE unique headline that makes people instantly want to try ${businessName}. Focus on conversion, not just awareness.
Make it so specific to ${businessName} in ${location} that it could never be used for another business.`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original content that has never been generated before.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(prompt + uniqueContext);
        let headline = result.response.text().trim();
        // Post-process to remove word repetitions
        headline = removeWordRepetitions(headline);
        // Add randomization to approach and emotional impact to ensure variety
        const approaches = [
            'strategic',
            'creative',
            'authentic',
            'bold',
            'community-focused',
            'innovative'
        ];
        const emotions = [
            'engaging',
            'inspiring',
            'trustworthy',
            'exciting',
            'confident',
            'welcoming'
        ];
        return {
            headline: headline,
            approach: approaches[Math.floor(Math.random() * approaches.length)],
            emotionalImpact: emotions[Math.floor(Math.random() * emotions.length)]
        };
    } catch (error) {
        // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback
        try {
            const simplifiedHeadlinePrompt = `Create a unique 5-word headline for ${businessName}, a ${businessType} in ${location}.

Make it:
- Conversion-focused (makes people want to try it NOW)
- Different from typical marketing words
- Uses psychological triggers like scarcity, urgency, or exclusivity
- Locally relevant to ${location}

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before

Just return the headline, nothing else.`;
            // Add unique generation context to headline retry as well
            const headlineRetryContext = `\n\nUNIQUE HEADLINE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
      This headline retry must be completely different and avoid repetitive patterns.`;
            const retryResult = await model.generateContent(simplifiedHeadlinePrompt + headlineRetryContext);
            const retryHeadline = retryResult.response.text().trim();
            return {
                headline: retryHeadline,
                approach: 'ai-retry-generated',
                emotionalImpact: 'conversion-focused'
            };
        } catch (retryError) {
            // EMERGENCY AI GENERATION - Ultra Simple Prompt
            try {
                const emergencyPrompt = `Write a catchy 5-word headline for ${businessName} in ${location}. Make it unique and compelling.

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before`;
                // Add unique generation context to emergency headline generation as well
                const headlineEmergencyContext = `\n\nUNIQUE HEADLINE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
        This emergency headline must be completely different and avoid repetitive patterns.`;
                const emergencyResult = await model.generateContent(emergencyPrompt + headlineEmergencyContext);
                const emergencyHeadline = emergencyResult.response.text().trim();
                return {
                    headline: emergencyHeadline,
                    approach: 'emergency-ai-generated',
                    emotionalImpact: 'unique-ai-created'
                };
            } catch (emergencyError) {
                // LAST RESORT: Generate with current timestamp for uniqueness
                const timestamp = Date.now();
                const uniqueId = timestamp % 1000;
                const emergencyHeadlines = [
                    `${businessName} ${location} Experience`,
                    `Exclusive ${businessType} ${location}`,
                    `${location}'s Premium ${businessType}`,
                    `Limited ${businessName} Access`,
                    `Secret ${businessType} Discovery`
                ];
                return {
                    headline: emergencyHeadlines[uniqueId % emergencyHeadlines.length] + ` #${uniqueId}`,
                    approach: 'timestamp-unique',
                    emotionalImpact: 'emergency-fallback'
                };
            }
        }
    }
}
async function generateBusinessSpecificSubheadline(businessType, businessName, location, businessDetails, headline, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create marketing-focused AI prompt for subheadline generation with trending intelligence
    const trendingKeywords = trendingData?.keywords?.slice(0, 5) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    const prompt = `You are a skilled local marketer creating a subheadline for ${businessName} that will make people in ${location} want to visit immediately. You understand current trends and local marketing patterns.

MARKETING CONTEXT:
- Main Headline: "${headline}"
- Business: ${businessName} (${businessType})
- Location: ${location}
- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}
- Target Market: Local ${location} residents and visitors
- Marketing Goal: ${contentGoal}

CURRENT TRENDING INTELLIGENCE:
- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Patterns: ${regionalLanguage}
- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

LOCAL MARKET INTELLIGENCE:
- What locals value: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- Industry trends: ${industry.trends.slice(0, 2).join(', ')}

REGIONAL MARKETING STRATEGY:
Create a subheadline that makes locals think "I need to try this place!" Use trending keywords naturally and speak like successful marketers in ${location} do. Focus on what makes ${businessName} irresistible to people in ${location}.

CONVERSION-FOCUSED SUBHEADLINE REQUIREMENTS:
- STRICT WORD LIMIT: Maximum 25 words that trigger immediate action and desire
- Use psychological triggers: social proof, scarcity, exclusivity, urgency
- Create FOMO (Fear of Missing Out) - make people think they'll regret not trying
- Include specific benefits that answer "What's in it for me?"
- Use action-oriented language that drives immediate response
- Build on the headline's promise with compelling reasons to act NOW
- Sound like a successful conversion-focused marketer in ${location}
- Should make the offer irresistible and create urgency to visit/buy

Examples of effective ${location} subheadlines (DO NOT COPY THESE - CREATE SOMETHING COMPLETELY DIFFERENT):
${getLocalMarketingExamples(location, businessType).split('\n').map((line)=>line.replace('- "', '- "').replace('"', '" (subheadline style)')).slice(0, 3).join('\n')}

CRITICAL ANTI-REPETITION INSTRUCTIONS FOR SUBHEADLINES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula
❌ Make it specific to ${businessName}'s actual services and features

Generate ONLY the subheadline text, nothing else.
Make it so specific to ${businessName} in ${location} that it could never be used for another business.`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This subheadline generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original subheadlines that have never been generated before.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(prompt + uniqueContext);
        let subheadline = result.response.text().trim();
        // Post-process to remove word repetitions
        subheadline = removeWordRepetitions(subheadline);
        // Add randomization to framework and benefit
        const frameworks = [
            'benefit-focused',
            'problem-solving',
            'community-centered',
            'expertise-driven',
            'results-oriented'
        ];
        const benefits = industry.uniqueValue.concat([
            'exceptional service',
            'local expertise',
            'proven results'
        ]);
        return {
            subheadline: subheadline,
            framework: frameworks[Math.floor(Math.random() * frameworks.length)],
            benefit: benefits[Math.floor(Math.random() * benefits.length)]
        };
    } catch (error) {
        // Marketing-focused fallback with enhanced randomization
        const timestamp = Date.now();
        const randomSeed = Math.floor(Math.random() * 1000) + timestamp;
        const variation = randomSeed % 16;
        const experienceWords = [
            'authentic',
            'fresh',
            'handcrafted',
            'traditional',
            'artisan',
            'premium',
            'local',
            'quality'
        ];
        const actionWords = [
            'discover',
            'experience',
            'taste',
            'enjoy',
            'savor',
            'explore',
            'try',
            'visit'
        ];
        const benefitPhrases = [
            'where quality meets tradition',
            'crafted with care daily',
            'your local favorite since day one',
            'bringing authentic flavors to life',
            'where every detail matters',
            'made fresh, served with pride',
            'your neighborhood gem awaits',
            'experience the difference'
        ];
        const marketingSubheadlines = [
            `${experienceWords[variation % experienceWords.length]} ${businessType} ${actionWords[(variation + 1) % actionWords.length]} in ${location}`,
            `${benefitPhrases[variation % benefitPhrases.length]}`,
            `${actionWords[variation % actionWords.length]} ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType} in ${location}`,
            `where ${location} locals ${actionWords[(variation + 3) % actionWords.length]} ${experienceWords[variation % experienceWords.length]} ${businessType}`,
            `${experienceWords[variation % experienceWords.length]} ingredients, ${experienceWords[(variation + 1) % experienceWords.length]} results`,
            `serving ${location} with ${experienceWords[(variation + 2) % experienceWords.length]} ${businessType}`,
            `your go-to spot for ${experienceWords[variation % experienceWords.length]} ${businessType}`,
            `${benefitPhrases[(variation + 4) % benefitPhrases.length]}`,
            `bringing ${experienceWords[(variation + 3) % experienceWords.length]} ${businessType} to ${location}`,
            `${location}'s most ${experienceWords[(variation + 4) % experienceWords.length]} ${businessType} experience`,
            `${experienceWords[(variation + 5) % experienceWords.length]} ${businessType} crafted for ${location}`,
            `where ${experienceWords[variation % experienceWords.length]} meets ${experienceWords[(variation + 2) % experienceWords.length]}`,
            `${location} deserves ${experienceWords[(variation + 1) % experienceWords.length]} ${businessType}`,
            `creating ${experienceWords[(variation + 3) % experienceWords.length]} moments in ${location}`,
            `${experienceWords[(variation + 4) % experienceWords.length]} ${businessType}, ${experienceWords[(variation + 5) % experienceWords.length]} service`,
            `your ${location} destination for ${experienceWords[variation % experienceWords.length]} ${businessType}`
        ];
        return {
            subheadline: marketingSubheadlines[variation],
            framework: 'benefit-focused',
            benefit: experienceWords[variation % experienceWords.length]
        };
    }
}
async function generateUnifiedContent(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    const contentPlan = StrategicContentPlanner.generateBusinessSpecificContent(businessType, businessName, location, businessDetails, platform, contentGoal);
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    // Initialize AI generation capability
    const model = initializeAI();
    // Create marketing-focused AI prompt for unified content generation
    const trendingKeywords = trendingData?.keywords?.slice(0, 8) || [];
    const trendingHashtags = trendingData?.hashtags?.slice(0, 5) || [];
    const regionalLanguage = getRegionalLanguageStyle(location);
    const marketingStyle = getRegionalMarketingStyle(location);
    // INTELLIGENT APPROACH SELECTION - Let AI decide based on context
    const uniqueGenerationId = Date.now() + Math.floor(Math.random() * 1000);
    // DEBUG: Log what data we actually received
    // DYNAMIC ANTI-REPETITION SYSTEM - No hardcoded phrases
    const dynamicVariationSeed = uniqueGenerationId % 1000;
    const creativityBoost = Math.floor(Math.random() * 100) + dynamicVariationSeed;
    // FORCE DIFFERENT APPROACHES BASED ON GENERATION ID
    const approachStyles = [
        'DIRECT_BENEFIT',
        'SOCIAL_PROOF',
        'PROBLEM_SOLUTION',
        'LOCAL_INSIDER',
        'URGENCY_SCARCITY',
        'QUESTION_HOOK',
        'STATISTIC_LEAD',
        'STORY_ANGLE',
        'COMPARISON',
        'NEWS_TREND'
    ];
    const selectedApproach = approachStyles[creativityBoost % approachStyles.length];
    const uniquenessPrompt = `
MANDATORY APPROACH: ${selectedApproach}
You MUST use this specific approach - no other approach is allowed for this generation.

STRICT ANTI-REPETITION RULES:
❌ NEVER use "2025" or any year references like "2025's Best-Kept Secret"
❌ NEVER use "best-kept secret", "secret", "hidden gem", or mystery language
❌ NEVER use "chakula kizuri" - if using Swahili, use different phrases like "chakula bora", "vyakula vizuri", "lishe nzuri"
❌ NEVER use "Shop now via the link in our bio! Karibu!" - create completely unique CTAs
❌ NEVER use "Discover", "Experience", "Taste the", "Try our", "Indulge in"
❌ NEVER use formulaic patterns that sound like templates
❌ NEVER repeat the same opening words or sentence structures
❌ NEVER use "for your familia's delight" or similar repetitive family references
❌ AVOID any phrase that sounds like it could be copy-pasted to another business

APPROACH-SPECIFIC REQUIREMENTS (Apply to ALL components - headlines, subheadlines, captions):
${getApproachInstructions(selectedApproach, businessName, location, creativityBoost)}

CREATIVITY BOOST ${creativityBoost} CHALLENGE:
Create ALL COMPONENTS (headline, subheadline, caption) that are so unique and specific to ${businessName} in ${location} that they could NEVER be used for any other business. Use the actual business data, trending information, RSS feeds, local events, and business intelligence to create something genuinely original.

MANDATORY UNIQUENESS REQUIREMENTS:
- Each component must reference specific details about ${businessName}
- Headlines must connect to current events, trends, or local happenings
- Subheadlines must mention actual services, products, or business features
- Captions must tell a story specific to this business and location
- NO generic phrases that could apply to any ${businessType}
- NO template-like language patterns
- Every sentence must add unique value specific to ${businessName}

UNIFIED DATA INTEGRATION REQUIREMENTS:
- HEADLINES: Must incorporate RSS trends, current events, or seasonal opportunities
- SUBHEADLINES: Must reference specific business services, features, or intelligence data
- CAPTIONS: Must weave together all data sources into compelling marketing copy
- ALL COMPONENTS: Must tell the same story using the same data sources consistently

GENERATION UNIQUENESS ID: ${uniqueGenerationId}
Use this ID to ensure this content is completely different from any previous generation.
`;
    // FORCE DIFFERENT CTA STYLES
    const ctaStyles = [
        'DIRECT_ACTION',
        'INVITATION',
        'CHALLENGE',
        'BENEFIT_FOCUSED',
        'COMMUNITY',
        'URGENCY',
        'CURIOSITY',
        'LOCAL_REFERENCE',
        'PERSONAL',
        'EXCLUSIVE'
    ];
    const selectedCtaStyle = ctaStyles[creativityBoost % ctaStyles.length];
    const unifiedPrompt = `You are a conversion-focused social media marketer creating a COMPLETE UNIFIED CAMPAIGN for ${businessName} that will make people in ${location} take immediate action. You must create ALL components (headline, subheadline, caption, CTA, design direction) that work together as ONE cohesive message.

${uniquenessPrompt}

UNIFIED CAMPAIGN REQUIREMENTS:
- ALL components must tell the SAME STORY with consistent information
- Headline, subheadline, caption, and design must reinforce the SAME key message
- No contradictory information between components
- One unified theme that runs through everything
- Design direction must match the content tone and message
- All components should feel like they came from the same marketing campaign

MARKETING BRIEF:
- Business: ${businessName} (${businessType})
- Location: ${location}
- Services/Products: ${businessDetails.services || businessDetails.expertise || 'quality offerings'}
- Target Market: Local ${location} residents and visitors
- Platform: ${platform}
- Marketing Goal: ${contentGoal}

CURRENT TRENDING INTELLIGENCE (From RSS feeds and market data):
- Trending Keywords: ${trendingKeywords.join(', ') || 'authentic, quality, local, fresh, community, experience, tradition, innovation'}
- Popular Hashtags: ${trendingHashtags.join(', ') || '#local #authentic #quality #community #fresh'}
- Regional Marketing Style: ${marketingStyle}
- Local Language Patterns: ${regionalLanguage}
- How locals talk about ${businessType}: ${getLocalBusinessLanguage(location, businessType)}

REAL BUSINESS INTELLIGENCE DATA:
${businessIntelligence ? `
- Business Strengths: ${businessIntelligence.businessStrengths?.join(', ') || 'quality service, customer satisfaction'}
- Value Propositions: ${businessIntelligence.valuePropositions?.join(', ') || 'exceptional quality, local expertise'}
- Target Emotions: ${businessIntelligence.targetEmotions?.join(', ') || 'trust, satisfaction, excitement'}
- Industry Keywords: ${businessIntelligence.industryKeywords?.join(', ') || 'professional, reliable, innovative'}
- Local Relevance: ${typeof businessIntelligence.localRelevance === 'object' ? Object.values(businessIntelligence.localRelevance || {}).join(', ') : businessIntelligence.localRelevance || 'community-focused, locally-owned'}
- Seasonal Opportunities: ${businessIntelligence.seasonalOpportunities?.join(', ') || 'year-round service'}
` : 'Use general business intelligence for this business type'}

LIVE RSS TRENDING DATA (Use this for ALL components - headlines, subheadlines, captions):
${trendingData ? `
- Current News Topics: ${trendingData.news?.slice(0, 3).map((n)=>n.title).join(', ') || 'No current news data'}
- Social Media Trends: ${trendingData.socialTrends?.slice(0, 3).join(', ') || 'No social trends data'}
- Local Events: ${trendingData.events?.slice(0, 2).map((e)=>e.name).join(', ') || 'No local events data'}
- Market Insights: ${trendingData.insights?.slice(0, 2).join(', ') || 'No market insights'}
` : 'No live RSS data available - use general market knowledge'}

HEADLINE GENERATION REQUIREMENTS (Use RSS data and business intelligence):
- HEADLINES must reference current trends, events, or news when relevant
- Connect ${businessName} to trending topics or local events naturally
- Use specific business services/features from business details
- Reference current market conditions or seasonal opportunities
- Make headlines feel current and timely, not generic
- Examples of RSS-integrated headlines:
  * "Local Food Festival Winner" (if there's a food event)
  * "Beat Holiday Rush Stress" (if trending topic is holiday stress)
  * "New Year Fitness Goals" (if trending topic is resolutions)
  * "Supply Chain Solution Found" (if news mentions supply issues)

SUBHEADLINE GENERATION REQUIREMENTS (Build on headline with business intelligence):
- SUBHEADLINES must expand on headline using specific business details
- Reference actual services, products, or unique features offered
- Use business intelligence data (industry trends, local opportunities)
- Connect to target audience pain points and solutions
- Support headline's promise with concrete business benefits
- Examples of business-integrated subheadlines:
  * "Our 15-year catering experience serves 200+ events monthly"
  * "Same-day delivery available for all ${location} residents"
  * "Certified organic ingredients sourced from local farms"
  * "24/7 emergency service with 30-minute response time"

SPECIFIC BUSINESS DETAILS:
- Business Name: ${businessName}
- Services/Products: ${businessDetails.services || businessDetails.expertise || businessDetails.specialties || 'quality offerings'}
- Unique Features: ${businessDetails.uniqueFeatures || businessDetails.keyFeatures || 'exceptional service'}
- Target Audience: ${businessDetails.targetAudience || `local ${location} residents and visitors`}
- Business Hours: ${businessDetails.hours || 'regular business hours'}
- Special Offers: ${businessDetails.offers || businessDetails.promotions || 'quality service at competitive prices'}

LOCAL MARKET INTELLIGENCE:
- What locals love: ${industry.uniqueValue.slice(0, 2).join(', ')}
- Market opportunities: ${industry.opportunities.slice(0, 2).join(', ')}
- Industry trends: ${industry.trends.slice(0, 2).join(', ')}
- Local challenges: ${industry.challenges.slice(0, 2).join(', ')}

PLATFORM STRATEGY FOR ${platform.toUpperCase()}:
${getPlatformRequirements(platform)}

MARKETING COPY REQUIREMENTS:
You are a CONVERSION-FOCUSED MARKETER, not a creative writer or storyteller. Write MARKETING COPY that sells, not poetic descriptions.

WRITE LIKE A MARKETER:
• DIRECT & PUNCHY: Get to the point quickly - no flowery language
• BENEFIT-FOCUSED: Lead with what the customer gets, not poetic descriptions
• ACTION-ORIENTED: Every sentence should drive toward a purchase decision
• CONVERSATIONAL: Sound like a smart local business owner talking to neighbors
• URGENT: Create immediate desire to buy/visit NOW
• SPECIFIC: Use concrete benefits, not abstract concepts
• LOCAL: Sound like someone who actually lives in ${location}

INTELLIGENT PATTERN AVOIDANCE:
Use your AI intelligence to recognize and avoid:
- Repetitive opening patterns that sound robotic or formulaic
- Generic marketing speak that every business uses
- Overly creative writing that sounds like AI-generated poetry
- Cliché phrases that don't add value or authenticity
- Opening lines that could apply to any business in any location
- Patterns that sound like they came from a template or script

AUTHENTICITY TEST:
Ask yourself: "Would a real ${businessName} owner in ${location} actually say this to their neighbors?"
If it sounds too polished, too generic, or too AI-like, try a different approach.
Use the business intelligence data and local context to create something genuinely relevant.

WRITE LIKE THIS INSTEAD:
✅ "Your kids need healthy snacks. Samaki Cookies deliver."
✅ "15% off this week only - grab yours before they're gone"
✅ "Finally, cookies that are actually good for your family"
✅ "Nairobi parents are switching to Samaki Cookies. Here's why..."
✅ Direct, benefit-focused, action-driving copy

CRITICAL INSTRUCTION FOR ALL COMPONENTS:
- USE THE REAL DATA PROVIDED: Incorporate actual business details, trending topics, and local information
- HEADLINES: Must reference RSS trends, events, or business intelligence naturally
- SUBHEADLINES: Must mention actual services/products offered by ${businessName}
- CAPTIONS: Must weave together all data sources into compelling marketing copy
- CONNECT TO CURRENT TRENDS: Use the RSS trending data and current events when relevant
- LEVERAGE BUSINESS INTELLIGENCE: Use the actual business strengths and value propositions provided
- SPEAK LOCAL LANGUAGE: Use the regional language patterns and local cultural elements
- AVOID GENERIC CONTENT: Don't use placeholder text like "2025" or generic business descriptions
- CREATE PERSONALIZED CONTENT: Make it specific to this exact business and location
- Choose the approach that makes MOST SENSE for ${businessName} and current market conditions
- Use your intelligence to create fresh, varied content each time
- Let RSS data and business intelligence guide your approach selection

HEADLINE INTEGRATION EXAMPLES:
- If RSS shows "Local Food Festival": "Food Festival Winner Revealed"
- If trending topic is "Holiday Stress": "Beat Holiday Rush Stress"
- If business intelligence shows "24/7 Service": "Always Open Always Ready"
- If local event is "Back to School": "School Rush Solution Found"
- If seasonal opportunity is "Summer": "Summer Special Starts Now"

SUBHEADLINE INTEGRATION EXAMPLES:
- Business service: "Our certified technicians fix 95% of issues same-day"
- Unique feature: "Only ${location} bakery using organic local flour"
- Business intelligence: "Serving ${location} families for 15+ years with proven results"
- Target audience: "Designed specifically for busy ${location} professionals"

MARKETING COPY REQUIREMENTS:
- WRITE MARKETING COPY, NOT CREATIVE WRITING: Sound like a business owner, not a poet
- LEAD WITH BENEFITS: Start with what the customer gets, not scenic descriptions
- BE DIRECT & PUNCHY: Short, clear sentences that drive action
- AVOID FLOWERY LANGUAGE: No "crisp afternoons", "sun dipping", "painting the sky"
- NO STORYTELLING OPENINGS: Don't start with "Imagine this..." or scene-setting
- SOUND LOCAL: Write like someone who actually lives and works in ${location}
- CREATE URGENCY: Make people want to buy/visit RIGHT NOW
- USE SOCIAL PROOF: Reference other locals, community, real benefits
- BE CONVERSATIONAL: Sound like talking to a neighbor, not writing poetry
- FOCUS ON PROBLEMS/SOLUTIONS: What problem does this solve for ${location} residents?
- INCLUDE SPECIFIC OFFERS: Mention actual deals, prices, limited time offers
- END WITH CLEAR ACTION: Tell people exactly what to do next
- AVOID ABSTRACT CONCEPTS: No "heritage", "traditions", "journeys" - focus on concrete benefits
- USE REAL LOCAL LANGUAGE: Include actual ${location} slang/phrases naturally
- MAKE IT SCANNABLE: Use short paragraphs, bullet points, clear structure
- GENERATION ID ${uniqueGenerationId}: Use this number to ensure this content is completely unique
- CRITICAL: Sound like a smart local marketer, not an AI creative writer

EXAMPLES OF GOOD MARKETING COPY:
✅ "Your kids need protein. Samaki Cookies deliver 8g per serving. 15% off this week."
✅ "Tired of unhealthy snacks? 200+ Nairobi families switched to Samaki Cookies."
✅ "Finally - cookies that don't spike blood sugar. Made with real fish protein."
✅ "Limited batch this week: Fish protein cookies that kids actually love."

EXAMPLES OF BAD AI WRITING (NEVER DO THIS):
❌ "Imagine this: a crisp, sunny afternoon in Nairobi..."
❌ "These aren't your grandma's cookies; they're bursting with..."
❌ "the sun dips below the horizon, painting the Kenyan sky..."
❌ "This isn't just a snack; it's a piece of Kenyan heritage..."

UNIFIED CONTENT GENERATION FORMAT:
Generate ALL components as ONE cohesive campaign:

UNIFIED_THEME: [the main theme/angle that connects everything - one sentence]
KEY_MESSAGE: [the core message all components will reinforce - one sentence]

HEADLINE: [5-word catchy headline using RSS trends/events/business intelligence - must feel current and specific to ${businessName}]
SUBHEADLINE: [supporting headline using specific business services/features from business details - max 14 words that build on headline]
CAPTION: [full social media caption that weaves together RSS data, business intelligence, and trending information - marketing copy, not creative writing]
CTA: [MANDATORY CTA STYLE: ${selectedCtaStyle} - ${getCtaStyleInstructions(selectedCtaStyle, businessName, location)} - Max 8 words, completely unique]
DESIGN_DIRECTION: [specific visual direction that matches the content tone and message]

IMPORTANT:
- ALL components must reinforce the SAME key message
- NO contradictory information between headline, subheadline, and caption
- Design direction must visually support the content message
- Generate as ONE unified campaign, not separate pieces`;
    try {
        // Add unique generation context to prevent repetitive responses
        const uniqueContext = `\n\nUNIQUE GENERATION CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
    This unified content generation must be completely different from any previous generation.
    Use this unique context to ensure fresh, original content that has never been generated before.
    CRITICAL: Avoid any patterns like "2025's Best-Kept Secret", "Chakula Kizuri", or repetitive phrases.

    CRITICAL WORD REPETITION RULES:
    - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
    - Check each sentence for duplicate adjacent words before finalizing
    - If you write "now now" or "the the" or any repeated word, remove the duplicate
    - Read your output carefully to ensure no word appears twice in a row`;
        const result = await model.generateContent(unifiedPrompt + uniqueContext);
        let response = result.response.text().trim();
        // Post-process to remove word repetitions from the entire response
        response = removeWordRepetitions(response);
        // Parse all unified components
        const unifiedThemeMatch = response.match(/UNIFIED_THEME:\s*(.*?)(?=KEY_MESSAGE:|$)/);
        const keyMessageMatch = response.match(/KEY_MESSAGE:\s*(.*?)(?=HEADLINE:|$)/);
        const headlineMatch = response.match(/HEADLINE:\s*(.*?)(?=SUBHEADLINE:|$)/);
        const subheadlineMatch = response.match(/SUBHEADLINE:\s*(.*?)(?=CAPTION:|$)/);
        const captionMatch = response.match(/CAPTION:\s*(.*?)(?=CTA:|$)/);
        const ctaMatch = response.match(/CTA:\s*(.*?)(?=DESIGN_DIRECTION:|$)/);
        const designMatch = response.match(/DESIGN_DIRECTION:\s*(.*?)$/);
        // Extract all components and apply word repetition removal to each
        const unifiedTheme = removeWordRepetitions(unifiedThemeMatch?.[1]?.trim() || 'Quality local business');
        const keyMessage = removeWordRepetitions(keyMessageMatch?.[1]?.trim() || 'Exceptional service for local community');
        const headline = removeWordRepetitions(headlineMatch?.[1]?.trim() || `${businessName} ${location}`);
        const subheadline = removeWordRepetitions(subheadlineMatch?.[1]?.trim() || `Quality ${businessType} in ${location}`);
        const caption = removeWordRepetitions(captionMatch?.[1]?.trim() || response);
        // 🎯 GENERATE DYNAMIC CTA using AI and business intelligence
        const ctaStrategy = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$dynamic$2d$cta$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dynamicCTAGenerator"].generateDynamicCTA(businessName, businessType, location, platform, contentGoal, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        const callToAction = removeWordRepetitions(ctaMatch?.[1]?.trim() || ctaStrategy.primary);
        const designDirection = removeWordRepetitions(designMatch?.[1]?.trim() || 'Clean, professional design with local elements');
        // Generate dynamic engagement hooks
        const engagementHooks = generateDynamicEngagementHooks(businessType, location, industry);
        // 🚀 ENHANCED: Generate hashtags using advanced RSS-integrated viral hashtag engine
        const viralHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        // 📊 Log hashtag analytics for debugging (RSS integration quality)
        if (viralHashtags.analytics) {
            const analytics = viralHashtags.analytics;
        // Enhanced hashtag generation with RSS confidence: ${analytics.confidenceScore}/10
        // RSS-sourced hashtags: ${analytics.rssSourced.length}
        // Top performers: ${analytics.topPerformers.join(', ')}
        // Emerging trends: ${analytics.emergingTrends.join(', ')}
        }
        return {
            headline,
            subheadline,
            caption,
            callToAction,
            engagementHooks,
            designDirection: removeWordRepetitions(designMatch?.[1]?.trim() || `Clean, professional design with local elements. IMPORTANT: Include the CTA "${callToAction}" as prominent text overlay on the design - make it bold, readable, and visually striking like "PAYA: YOUR FUTURE, NOW!" style.`),
            unifiedTheme,
            keyMessage,
            hashtags: viralHashtags.total,
            hashtagStrategy: viralHashtags,
            ctaStrategy: ctaStrategy,
            imageText: callToAction // Pass CTA as imageText for design integration
        };
    } catch (error) {
        return {
            headline: `${businessName} - ${businessType}`,
            subheadline: `Quality ${businessType} services in ${location}`,
            caption: `Experience the best ${businessType} services at ${businessName}. Located in ${location}, we're committed to excellence.`,
            callToAction: `Visit ${businessName} today!`,
            engagementHooks: [
                'Quality service',
                'Local expertise',
                'Customer satisfaction'
            ],
            designDirection: 'Professional, clean design with local elements',
            unifiedTheme: 'Professional excellence',
            keyMessage: 'Quality service provider',
            hashtags: [
                '#business',
                '#local',
                '#quality',
                '#service',
                '#professional'
            ],
            hashtagStrategy: {
                total: [
                    '#business',
                    '#local',
                    '#quality',
                    '#service',
                    '#professional'
                ]
            },
            ctaStrategy: {
                primary: `Visit ${businessName} today!`
            },
            imageText: `Visit ${businessName} today!`
        };
    }
    // RETRY WITH SIMPLIFIED AI PROMPT - No Static Fallback
    try {
        const simplifiedPrompt = `Create ONE unique ${platform} caption for ${businessName}, a ${businessType} in ${location}.

INTELLIGENT APPROACH SELECTION:
Use your marketing intelligence to choose the BEST approach based on:
- What would work best for ${businessType} in ${location}
- Current market trends and local culture
- What would make ${location} residents most interested

REQUIREMENTS:
- Write ONE compelling caption using your chosen marketing approach
- AVOID overused words: "taste", "flavor", "delicious", "amazing"
- Use different opening words than typical marketing (avoid "Discover", "Experience", "Try")
- Include local ${location} cultural elements that create connection
- End with an effective call-to-action
- Make it conversion-focused and unique to this business

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

IMPORTANT: Generate ONLY ONE caption, not multiple options.

Format:
CAPTION: [write one single caption here]
CTA: [write one call to action here]

Do NOT write "Here are captions" or provide lists.`;
        // Add unique generation context to retry as well
        const retryUniqueContext = `\n\nUNIQUE RETRY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
      This retry generation must be completely different and avoid repetitive patterns.

      CRITICAL WORD REPETITION RULES:
      - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
      - Check each sentence for duplicate adjacent words before finalizing
      - If you write "now now" or "the the" or any repeated word, remove the duplicate
      - Read your output carefully to ensure no word appears twice in a row`;
        const retryResult = await model.generateContent(simplifiedPrompt + retryUniqueContext);
        let retryResponse = retryResult.response.text().trim();
        // Post-process to remove word repetitions from retry response
        retryResponse = removeWordRepetitions(retryResponse);
        // Parse the retry response
        const retryCaptionMatch = retryResponse.match(/CAPTION:\s*(.*?)(?=CTA:|$)/);
        const retryCtaMatch = retryResponse.match(/CTA:\s*(.*?)$/);
        const retryCaption = removeWordRepetitions(retryCaptionMatch ? retryCaptionMatch[1].trim() : retryResponse);
        const retryCallToAction = removeWordRepetitions(retryCtaMatch ? retryCtaMatch[1].trim() : generateFallbackCTA(platform));
        // Generate viral hashtags for retry
        const retryHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
        return {
            headline: `${businessName} - ${businessType}`,
            subheadline: `Quality ${businessType} services in ${location}`,
            caption: retryCaption,
            engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
            callToAction: retryCallToAction,
            designDirection: 'Professional, clean design with local elements',
            unifiedTheme: 'Professional excellence',
            keyMessage: 'Quality service provider',
            hashtags: retryHashtags.total,
            hashtagStrategy: retryHashtags,
            ctaStrategy: {
                primary: retryCallToAction
            },
            imageText: retryCallToAction
        };
    } catch (retryError) {
        // EMERGENCY AI GENERATION - Ultra Simple Prompt
        try {
            const emergencyPrompt = `Write ONE unique social media post for ${businessName} in ${location}. Make it compelling and different from typical posts. Include a call-to-action.

CRITICAL ANTI-REPETITION RULES:
❌ DO NOT use "2025's Best-Kept Secret" or any variation
❌ DO NOT use "Chakula Kizuri" or repetitive Swahili phrases
❌ DO NOT use "for your familia's delight" or similar family references
❌ CREATE something completely original that has never been generated before
❌ AVOID any pattern that sounds like a template or formula

Do NOT write "Here are posts" or provide multiple options. Write ONE post only.`;
            // Add unique generation context to emergency generation as well
            const emergencyUniqueContext = `\n\nUNIQUE EMERGENCY CONTEXT: ${Date.now()}-${Math.random().toString(36).substr(2, 9)}
        This emergency generation must be completely different and avoid any repetitive patterns.

        CRITICAL WORD REPETITION RULES:
        - NEVER repeat the same word consecutively (e.g., "buy now now pay" should be "buy now pay")
        - Check each sentence for duplicate adjacent words before finalizing
        - If you write "now now" or "the the" or any repeated word, remove the duplicate
        - Read your output carefully to ensure no word appears twice in a row`;
            const emergencyResult = await model.generateContent(emergencyPrompt + emergencyUniqueContext);
            let emergencyResponse = emergencyResult.response.text().trim();
            // Post-process to remove word repetitions from emergency response
            emergencyResponse = removeWordRepetitions(emergencyResponse);
            // Generate viral hashtags for emergency
            const emergencyHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
            return {
                headline: `${businessName} - ${businessType}`,
                subheadline: `Quality ${businessType} services in ${location}`,
                caption: emergencyResponse,
                engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
                callToAction: removeWordRepetitions(generateFallbackCTA(platform)),
                designDirection: 'Professional, clean design with local elements',
                unifiedTheme: 'Professional excellence',
                keyMessage: 'Quality service provider',
                hashtags: emergencyHashtags.total,
                hashtagStrategy: emergencyHashtags,
                ctaStrategy: {
                    primary: removeWordRepetitions(generateFallbackCTA(platform))
                },
                imageText: removeWordRepetitions(generateFallbackCTA(platform))
            };
        } catch (emergencyError) {
            // LAST RESORT: Generate with current timestamp for uniqueness
            const timestamp = Date.now();
            const uniqueId = Math.floor(Math.random() * 10000);
            // Generate viral hashtags for final fallback
            const fallbackHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$viral$2d$hashtag$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["viralHashtagEngine"].generateViralHashtags(businessType, businessName, location, platform, businessDetails.services || businessDetails.expertise, businessDetails.targetAudience);
            return {
                headline: `${businessName} - ${businessType}`,
                subheadline: `Quality ${businessType} services in ${location}`,
                caption: removeWordRepetitions(`${businessName} in ${location} - where quality meets innovation. Every visit is a new experience that locals can't stop talking about. Join the community that knows great ${businessType}! #${timestamp}`),
                engagementHooks: generateDynamicEngagementHooks(businessType, location, industry),
                callToAction: removeWordRepetitions(generateFallbackCTA(platform)),
                designDirection: 'Professional, clean design with local elements',
                unifiedTheme: 'Professional excellence',
                keyMessage: 'Quality service provider',
                hashtags: fallbackHashtags.total,
                hashtagStrategy: fallbackHashtags,
                ctaStrategy: {
                    primary: removeWordRepetitions(generateFallbackCTA(platform))
                },
                imageText: removeWordRepetitions(generateFallbackCTA(platform))
            };
        }
    }
}
// Helper functions for AI-powered caption generation
function getPlatformRequirements(platform) {
    const requirements = {
        'Instagram': '- Use 1-3 relevant emojis\n- Keep it visually engaging\n- Include hashtag-friendly language\n- Encourage visual interaction',
        'Facebook': '- More conversational tone\n- Can be longer and more detailed\n- Focus on community engagement\n- Include questions to spark discussion',
        'LinkedIn': '- Professional but approachable tone\n- Focus on business value and expertise\n- Include industry insights\n- Encourage professional networking',
        'Twitter': '- Concise and punchy\n- Use relevant hashtags\n- Encourage retweets and replies\n- Keep under 280 characters'
    };
    return requirements[platform] || requirements['Instagram'];
}
function generateFallbackCTA(platform) {
    const timestamp = Date.now();
    const creativityBoost = Math.floor(Math.random() * 1000) + timestamp;
    // Use the same dynamic CTA styles as the main system
    const ctaStyles = [
        'DIRECT_ACTION',
        'INVITATION',
        'CHALLENGE',
        'BENEFIT_FOCUSED',
        'COMMUNITY',
        'URGENCY',
        'CURIOSITY',
        'LOCAL_REFERENCE',
        'PERSONAL',
        'EXCLUSIVE'
    ];
    const selectedStyle = ctaStyles[creativityBoost % ctaStyles.length];
    // Dynamic CTAs based on style - avoid repetitive patterns
    const dynamicCTAs = {
        'DIRECT_ACTION': [
            'Grab yours today! 🔥',
            'Book your spot now! ⚡',
            'Try it this week! 💪',
            'Get started today! 🚀'
        ],
        'INVITATION': [
            'Come see for yourself! 👀',
            'Join us this weekend! 🎉',
            'Experience it firsthand! ✨',
            'Visit us soon! 🏃‍♂️'
        ],
        'CHALLENGE': [
            'Find better - we dare you! 💪',
            'Beat this quality anywhere! 🏆',
            'Try to resist this! 😏',
            'Prove us wrong! 🤔'
        ],
        'BENEFIT_FOCUSED': [
            'Get more for less! 💰',
            'Save time and money! ⏰',
            'Double your results! 📈',
            'Feel the difference! ✨'
        ],
        'COMMUNITY': [
            'Join 500+ happy customers! 👥',
            'Be part of something special! 🌟',
            'Connect with like-minded people! 🤝',
            'Become a local favorite! ❤️'
        ],
        'URGENCY': [
            'Only 3 spots left! ⚡',
            'Ends this Friday! ⏰',
            'While supplies last! 🏃‍♂️',
            'Don\'t wait too long! ⚠️'
        ],
        'CURIOSITY': [
            'See what everyone\'s talking about! 👀',
            'Discover the secret! 🔍',
            'Find out why! 🤔',
            'Uncover the truth! 💡'
        ],
        'LOCAL_REFERENCE': [
            'Better than downtown! 🏙️',
            'Your neighborhood choice! 🏠',
            'Local favorite since day one! ⭐',
            'Right in your backyard! 📍'
        ],
        'PERSONAL': [
            'You deserve this! 💎',
            'Made just for you! 🎯',
            'Your perfect match! 💕',
            'Exactly what you need! ✅'
        ],
        'EXCLUSIVE': [
            'Members only access! 🔐',
            'VIP treatment awaits! 👑',
            'Exclusive to our community! 🌟',
            'Limited to select few! 💎'
        ]
    };
    const styleCTAs = dynamicCTAs[selectedStyle] || dynamicCTAs['DIRECT_ACTION'];
    const variation = creativityBoost % styleCTAs.length;
    return styleCTAs[variation];
}
function generateDynamicEngagementHooks(businessType, location, industry) {
    const timestamp = Date.now();
    const randomSeed = Math.floor(Math.random() * 1000) + timestamp;
    const variation = randomSeed % 8;
    const localQuestions = [
        `What's your favorite ${businessType} spot in ${location}?`,
        `Where do ${location} locals go for the best ${businessType}?`,
        `What makes ${location}'s ${businessType} scene special?`,
        `Have you discovered ${location}'s hidden ${businessType} gems?`,
        `What do you love most about ${businessType} in ${location}?`,
        `Which ${location} ${businessType} place holds your best memories?`,
        `What's missing from ${location}'s ${businessType} options?`,
        `How has ${businessType} in ${location} changed over the years?`
    ];
    const experienceQuestions = [
        `What's your go-to order when trying new ${businessType}?`,
        `What makes you choose one ${businessType} place over another?`,
        `What's the most important thing in great ${businessType}?`,
        `How do you know when you've found quality ${businessType}?`,
        `What's your best ${businessType} experience been like?`,
        `What would make your perfect ${businessType} experience?`,
        `What draws you to authentic ${businessType}?`,
        `How do you discover new ${businessType} places?`
    ];
    const trendQuestions = [
        `Have you tried ${industry.trends[variation % industry.trends.length]} yet?`,
        `What do you think about the latest ${businessType} trends?`,
        `Are you excited about ${industry.opportunities[variation % industry.opportunities.length]}?`,
        `How important is ${industry.uniqueValue[variation % industry.uniqueValue.length]} to you?`,
        `What's your take on modern ${businessType} approaches?`,
        `Do you prefer traditional or innovative ${businessType}?`,
        `What ${businessType} trend should everyone try?`,
        `How do you stay updated on ${businessType} innovations?`
    ];
    // Mix different types of hooks for variety
    const allHooks = [
        ...localQuestions,
        ...experienceQuestions,
        ...trendQuestions
    ];
    const selectedHooks = [];
    // Ensure we get one from each category for variety
    selectedHooks.push(localQuestions[variation % localQuestions.length]);
    selectedHooks.push(experienceQuestions[(variation + 1) % experienceQuestions.length]);
    selectedHooks.push(trendQuestions[(variation + 2) % trendQuestions.length]);
    return selectedHooks;
}
// Legacy platform-specific caption generators (keeping for backward compatibility)
function generateInstagramCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your biggest ${industry.challenges[0]} challenge?`,
        `How do you choose your ${businessType} provider?`,
        `What makes a great ${businessType} experience for you?`
    ];
    const ctas = [
        `Comment below with your thoughts! 👇`,
        `Share this if you agree! 🔄`,
        `Tag someone who needs this! 👥`
    ];
    return {
        caption: `${contentPlan.valueProposition} ✨\n\n${businessName} brings ${industry.uniqueValue[0]} to ${location} with ${contentPlan.businessStrengths[0]}. ${contentPlan.marketOpportunities[0]} is just the beginning!\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateFacebookCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your experience with ${businessType} in ${location}?`,
        `How do you solve ${industry.challenges[0]}?`,
        `What makes you choose local businesses?`
    ];
    const ctas = [
        `Share your thoughts in the comments! 💬`,
        `Like and share if this resonates with you! 👍`,
        `Tag your friends who might be interested! 👥`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} understands the ${location} community and delivers ${industry.uniqueValue[0]} that makes a difference. ${contentPlan.marketOpportunities[0]} shows our commitment to serving you better.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateLinkedInCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What challenges do you face in ${businessType}?`,
        `How do you stay competitive in your industry?`,
        `What makes a business stand out in your community?`
    ];
    const ctas = [
        `Share your insights in the comments below. 💼`,
        `Connect with us to learn more about our approach. 🤝`,
        `Follow for more industry insights and local business strategies. 📈`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} combines ${contentPlan.businessStrengths[0]} with deep understanding of the ${location} market to deliver exceptional ${businessType} services. ${contentPlan.marketOpportunities[0]} demonstrates our commitment to innovation and community service.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function generateTwitterCaption(contentPlan, businessName, location, industry, contentGoal) {
    const businessType = contentPlan.businessType || 'business';
    const hooks = [
        `What's your take on ${businessType} trends?`,
        `How do you solve ${industry.challenges[0]}?`,
        `What makes local businesses special?`
    ];
    const ctas = [
        `Reply with your thoughts! 💭`,
        `RT if you agree! 🔄`,
        `Follow for more insights! 👀`
    ];
    return {
        caption: `${contentPlan.valueProposition}\n\n${businessName} brings ${industry.uniqueValue[0]} to ${location}. ${contentPlan.marketOpportunities[0]} shows our commitment to excellence.\n\n${hooks[0]}\n\n${ctas[0]}`,
        engagementHooks: hooks,
        callToAction: ctas[0]
    };
}
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
function getRandomElements(array, count) {
    const shuffled = [
        ...array
    ].sort(()=>0.5 - Math.random());
    return shuffled.slice(0, count);
}
function generateCreativeSeed() {
    return Math.floor(Math.random() * 10000);
}
function generateCreativeHeadline(businessType, businessName, location, context) {
    return {
        headline: `${businessName} - ${businessType}`,
        style: 'professional',
        tone: 'engaging'
    };
}
function generateCreativeSubheadline(businessType, services, location, tone) {
    return {
        subheadline: `Quality ${businessType} services in ${location}`,
        framework: 'benefit-focused'
    };
}
function generateCreativeCTA(businessType, tone, context) {
    return {
        cta: 'Learn more about our services',
        urgency: 'gentle',
        emotion: 'curiosity'
    };
}
function analyzeBusinessContext(businessType, businessName, location, services) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    return {
        creativePotential: industry.uniqueValue,
        emotionalTriggers: industry.customerPainPoints,
        industryInsights: industry.trends,
        localOpportunities: industry.seasonalOpportunities,
        competitiveAdvantages: industry.opportunities
    };
}
const CREATIVE_PROMPT_SYSTEM = {
    creativeVariation: {
        style: [
            'innovative',
            'authentic',
            'engaging',
            'professional',
            'creative'
        ],
        mood: [
            'inspiring',
            'confident',
            'warm',
            'energetic',
            'trustworthy'
        ],
        approach: [
            'strategic',
            'emotional',
            'analytical',
            'storytelling',
            'direct'
        ]
    },
    creativeConstraints: {
        avoidGeneric: [
            'template language',
            'cliché phrases',
            'generic claims'
        ]
    }
};
const CONTENT_VARIATION_ENGINE = {
    headlineStyles: [
        'Question-based',
        'Statistic-driven',
        'Story-opening',
        'Bold statement',
        'Emotional trigger',
        'Curiosity gap',
        'Local relevance',
        'Trend integration',
        'Problem-solution',
        'Benefit-focused',
        'Aspirational',
        'Contrarian'
    ],
    emotionalTones: [
        'Inspiring',
        'Humorous',
        'Empathetic',
        'Confident',
        'Curious',
        'Nostalgic',
        'Aspirational',
        'Relatable',
        'Surprising',
        'Authentic',
        'Warm',
        'Professional',
        'Innovative',
        'Trustworthy'
    ],
    creativeFrameworks: [
        'Before/After',
        'Problem/Solution',
        'Story Arc',
        'Contrast',
        'Metaphor',
        'Analogy',
        'Question/Answer',
        'Challenge/Overcome',
        'Journey',
        'Transformation',
        'Discovery',
        'Achievement'
    ]
};
class AntiRepetitionSystem {
    static usedCombinations = new Set();
    static maxHistory = 100;
    static generateUniqueVariation(businessType, platform, baseElements) {
        const variation = this.createVariation(businessType, platform, baseElements);
        this.recordVariation(variation);
        return variation;
    }
    static createVariation(businessType, platform, baseElements) {
        const creativeSeed = generateCreativeSeed();
        return {
            creativeSeed,
            style: 'business-specific',
            mood: 'professional',
            approach: 'strategic',
            headlineStyle: 'business-focused',
            framework: 'value-driven',
            signature: `business-${creativeSeed}`,
            contentStrategy: {
                name: 'Business Intelligence',
                approach: 'Strategic content based on business strengths'
            },
            writingStyle: {
                name: 'Professional Expert',
                voice: 'Industry authority with local expertise'
            },
            contentAngle: {
                type: 'Business Value',
                focus: 'Solving customer problems with business strengths'
            },
            marketInsights: [
                'Local market expertise',
                'Industry trends',
                'Customer pain points'
            ],
            engagementHooks: [
                'Problem identification',
                'Solution presentation',
                'Value demonstration'
            ],
            localPhrases: [
                'Local expertise',
                'Community focus',
                'Market knowledge'
            ]
        };
    }
    static recordVariation(variation) {
        this.usedCombinations.add(variation.signature);
        if (this.usedCombinations.size > this.maxHistory) {
            const oldestEntries = Array.from(this.usedCombinations).slice(0, 20);
            oldestEntries.forEach((entry)=>this.usedCombinations.delete(entry));
        }
    }
}
function enhanceDesignCreativity(designPrompt, businessType, location, context) {
    const industry = BUSINESS_INTELLIGENCE_SYSTEM.industryInsights[businessType.toLowerCase()] || BUSINESS_INTELLIGENCE_SYSTEM.industryInsights['retail'];
    const creativeElements = industry.uniqueValue.slice(0, 3);
    const visualStyle = 'professional business-focused design';
    const enhancedPrompt = designPrompt + '\n\nCREATIVE ENHANCEMENT:\n' + `- Business Type: ${businessType}\n` + `- Location: ${location}\n` + `- Industry Focus: ${industry.trends.slice(0, 2).join(', ')}\n` + `- Visual Style: ${visualStyle}`;
    return {
        enhancedPrompt,
        creativeElements,
        visualStyle
    };
}
// Regional Marketing Intelligence Functions
function getRegionalLanguageStyle(location) {
    const locationLower = location.toLowerCase();
    if (locationLower.includes('kenya') || locationLower.includes('nairobi') || locationLower.includes('mombasa')) {
        return 'Warm, community-focused, with occasional Swahili phrases like "karibu" (welcome), "asante" (thank you). Direct but friendly tone.';
    } else if (locationLower.includes('nigeria') || locationLower.includes('lagos') || locationLower.includes('abuja')) {
        return 'Energetic, aspirational, with pidgin English influences. Uses "finest", "sharp sharp", "no wahala" naturally.';
    } else if (locationLower.includes('south africa') || locationLower.includes('cape town') || locationLower.includes('johannesburg')) {
        return 'Multicultural blend, uses "lekker", "braai", "just now". Mix of English and local expressions.';
    } else if (locationLower.includes('ghana') || locationLower.includes('accra')) {
        return 'Friendly, respectful, with Twi influences. Uses "chale", "ɛyɛ" naturally in marketing.';
    } else if (locationLower.includes('india') || locationLower.includes('mumbai') || locationLower.includes('delhi')) {
        return 'Enthusiastic, family-oriented, with Hindi/English mix. Uses "achha", "best", "number one" frequently.';
    } else if (locationLower.includes('uk') || locationLower.includes('london') || locationLower.includes('manchester')) {
        return 'Polite but confident, uses "brilliant", "proper", "lovely". Understated but effective.';
    } else if (locationLower.includes('usa') || locationLower.includes('new york') || locationLower.includes('california')) {
        return 'Direct, confident, superlative-heavy. Uses "awesome", "amazing", "best ever" frequently.';
    }
    return 'Friendly, professional, community-focused with local cultural sensitivity.';
}
function getRegionalMarketingStyle(location) {
    const locationLower = location.toLowerCase();
    if (locationLower.includes('kenya') || locationLower.includes('nairobi')) {
        return 'Community-centered, emphasizes tradition meets modernity, family values, and local pride';
    } else if (locationLower.includes('nigeria') || locationLower.includes('lagos')) {
        return 'Bold, aspirational, success-oriented, emphasizes quality and status';
    } else if (locationLower.includes('south africa')) {
        return 'Inclusive, diverse, emphasizes heritage and innovation together';
    } else if (locationLower.includes('ghana')) {
        return 'Respectful, community-focused, emphasizes craftsmanship and tradition';
    } else if (locationLower.includes('india')) {
        return 'Family-oriented, value-conscious, emphasizes trust and relationships';
    } else if (locationLower.includes('uk')) {
        return 'Quality-focused, heritage-conscious, understated confidence';
    } else if (locationLower.includes('usa')) {
        return 'Innovation-focused, convenience-oriented, bold claims and superlatives';
    }
    return 'Community-focused, quality-oriented, culturally respectful';
}
function getLocalBusinessLanguage(location, businessType) {
    const locationLower = location.toLowerCase();
    const businessLower = businessType.toLowerCase();
    if (locationLower.includes('kenya')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return '"chakula kizuri" (good food), "asili" (authentic), "familia" (family), "mazingira" (environment)';
        } else if (businessLower.includes('tech') || businessLower.includes('digital')) {
            return '"teknolojia", "haraka" (fast), "rahisi" (easy), "bora" (best)';
        }
        return '"bora" (best), "karibu" (welcome), "mazuri" (good), "familia" (family)';
    } else if (locationLower.includes('nigeria')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return '"finest food", "correct taste", "no wahala", "sharp sharp service"';
        }
        return '"finest", "correct", "sharp sharp", "no wahala", "top notch"';
    }
    return 'quality, authentic, local, trusted, community';
}
function getLocalMarketingExamples(location, businessType) {
    const locationLower = location.toLowerCase();
    const businessLower = businessType.toLowerCase();
    if (locationLower.includes('kenya')) {
        if (businessLower.includes('restaurant') || businessLower.includes('food')) {
            return `- "Chakula Asili Kenya" (Authentic Kenya Food)
- "Familia Flavors Nairobi"
- "Taste Bora Kenya"
- "Karibu Kitchen Experience"`;
        }
        return `- "Bora ${businessType} Kenya"
- "Karibu Quality Service"
- "Kenya's Finest Choice"
- "Asili ${businessType} Experience"`;
    } else if (locationLower.includes('nigeria')) {
        return `- "Finest ${businessType} Lagos"
- "Sharp Sharp Service"
- "Correct ${businessType} Choice"
- "Top Notch Experience"`;
    }
    return `- "${location}'s Best ${businessType}"
- "Quality Meets Community"
- "Local Excellence Delivered"
- "Authentic ${businessType} Experience"`;
}
async function generateBusinessSpecificCaption(businessType, businessName, location, businessDetails, platform, contentGoal = 'awareness', trendingData, businessIntelligence) {
    // Use the unified system but return only caption components
    const unifiedContent = await generateUnifiedContent(businessType, businessName, location, businessDetails, platform, contentGoal, trendingData, businessIntelligence);
    return {
        caption: unifiedContent.caption,
        engagementHooks: unifiedContent.engagementHooks,
        callToAction: unifiedContent.callToAction
    };
}
}}),
"[project]/src/ai/regional-communication-engine.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Regional Communication Engine
 * Deep understanding of how people actually communicate, advertise, and connect in different regions
 */ __turbopack_context__.s({
    "RegionalCommunicationEngine": (()=>RegionalCommunicationEngine),
    "regionalEngine": (()=>regionalEngine)
});
class RegionalCommunicationEngine {
    regionalProfiles = new Map();
    constructor(){
        this.initializeRegionalProfiles();
    }
    initializeRegionalProfiles() {
        // KENYA - Nairobi and surrounding areas
        this.regionalProfiles.set('kenya', {
            region: 'Kenya',
            country: 'Kenya',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'high',
                humorStyle: [
                    'witty',
                    'playful',
                    'community-based',
                    'storytelling'
                ],
                persuasionTactics: [
                    'community benefit',
                    'family value',
                    'quality emphasis',
                    'local pride'
                ],
                attentionGrabbers: [
                    'Eh!',
                    'Sawa sawa!',
                    'Mambo!',
                    'Poa!',
                    'Uko ready?'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Community-Centered',
                    approach: 'Emphasize how the business serves the local community',
                    examples: [
                        'Serving our Nairobi family with love',
                        'Your neighborhood spot for authentic taste',
                        'Where Kenyans come together'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'facebook',
                        'whatsapp',
                        'instagram'
                    ]
                },
                {
                    type: 'Quality & Freshness',
                    approach: 'Highlight freshness, quality, and authentic preparation',
                    examples: [
                        'Fresh from the kitchen to your table',
                        'Made with love, served with pride',
                        'Authentic taste that reminds you of home'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'facebook'
                    ]
                },
                {
                    type: 'Swahili Integration',
                    approach: 'Natural mix of English and Swahili that feels authentic',
                    examples: [
                        'Chakula kizuri, bei nzuri!',
                        'Karibu for the best experience',
                        'Tupo hapa for you always'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'all'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'Mambo!',
                    'Sasa!',
                    'Niaje!',
                    'Poa!',
                    'Karibu!'
                ],
                excitement: [
                    'Poa kabisa!',
                    'Sawa sawa!',
                    'Fiti!',
                    'Bomba!',
                    'Noma!'
                ],
                approval: [
                    'Safi!',
                    'Poa!',
                    'Nzuri!',
                    'Fiti kabisa!',
                    'Bomba sana!'
                ],
                emphasis: [
                    'kabisa',
                    'sana',
                    'mzuri',
                    'noma',
                    'fiti'
                ],
                callToAction: [
                    'Njoo uone!',
                    'Karibu!',
                    'Tupatane!',
                    'Uko ready?',
                    'Twende!'
                ],
                endingPhrases: [
                    'Tutaonana!',
                    'Karibu tena!',
                    'Asante sana!',
                    'Mungu akubariki!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Community Connection',
                    importance: 'critical',
                    description: 'Kenyans value businesses that feel like part of the community',
                    doAndDonts: {
                        do: [
                            'Reference local landmarks',
                            'Use "our community" language',
                            'Show family values'
                        ],
                        dont: [
                            'Sound too corporate',
                            'Ignore local customs',
                            'Be overly formal'
                        ]
                    }
                },
                {
                    aspect: 'Language Mixing',
                    importance: 'important',
                    description: 'Natural mixing of English and Swahili is expected and appreciated',
                    doAndDonts: {
                        do: [
                            'Mix languages naturally',
                            'Use common Swahili phrases',
                            'Keep it conversational'
                        ],
                        dont: [
                            'Force Swahili if unsure',
                            'Use formal Swahili only',
                            'Ignore English speakers'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Family-owned and operated',
                    'Serving the community for [X] years',
                    'Made with love by local hands',
                    'Your neighbors you can trust'
                ],
                valuePropositions: [
                    'Fresh ingredients sourced locally',
                    'Authentic recipes passed down generations',
                    'Fair prices for quality food',
                    'A place where everyone is family'
                ],
                communityConnection: [
                    'Part of the Nairobi family',
                    'Supporting local farmers and suppliers',
                    'Where neighbors become friends',
                    'Celebrating our Kenyan heritage'
                ],
                localReferences: [
                    'Just off [local road/landmark]',
                    'Near [well-known local spot]',
                    'In the heart of [neighborhood]',
                    'Where locals have been coming for years'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'WhatsApp',
                    'Facebook',
                    'Instagram',
                    'TikTok'
                ],
                contentPreferences: [
                    'food photos',
                    'behind-the-scenes',
                    'customer testimonials',
                    'community events'
                ],
                engagementStyle: 'High interaction, lots of comments and shares, community-focused',
                hashtagUsage: 'Mix of English and Swahili hashtags, location-based tags',
                visualPreferences: [
                    'bright colors',
                    'authentic moments',
                    'people enjoying food',
                    'local settings'
                ]
            }
        });
        // NIGERIA - Lagos and surrounding areas
        this.regionalProfiles.set('nigeria', {
            region: 'Nigeria',
            country: 'Nigeria',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'high',
                humorStyle: [
                    'energetic',
                    'bold',
                    'confident',
                    'community-pride'
                ],
                persuasionTactics: [
                    'quality emphasis',
                    'value for money',
                    'social status',
                    'community respect'
                ],
                attentionGrabbers: [
                    'Oya!',
                    'See this one!',
                    'No be small thing!',
                    'This one sweet die!'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Bold & Confident',
                    approach: 'Strong, confident statements about quality and value',
                    examples: [
                        'The best in Lagos, no cap!',
                        'Quality wey go shock you!',
                        'This one na correct business!'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'twitter',
                        'facebook'
                    ]
                },
                {
                    type: 'Value Emphasis',
                    approach: 'Highlight exceptional value and quality for the price',
                    examples: [
                        'Quality food, affordable price',
                        'Where your money get value',
                        'Premium taste, pocket-friendly price'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'all'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'How far?',
                    'Wetin dey happen?',
                    'Oya!',
                    'My guy!'
                ],
                excitement: [
                    'E sweet die!',
                    'This one correct!',
                    'Na fire!',
                    'Too much!'
                ],
                approval: [
                    'Correct!',
                    'Na so!',
                    'Perfect!',
                    'E good die!'
                ],
                emphasis: [
                    'die',
                    'well well',
                    'proper',
                    'correct'
                ],
                callToAction: [
                    'Come try am!',
                    'Oya come!',
                    'Make you taste am!',
                    'No waste time!'
                ],
                endingPhrases: [
                    'See you soon!',
                    'We dey wait for you!',
                    'Come back again!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Confidence & Quality',
                    importance: 'critical',
                    description: 'Nigerians appreciate confident, bold statements about quality',
                    doAndDonts: {
                        do: [
                            'Be confident about your quality',
                            'Use bold language',
                            'Emphasize value'
                        ],
                        dont: [
                            'Be too modest',
                            'Undersell your quality',
                            'Sound uncertain'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Tested and trusted',
                    'Lagos people choice',
                    'Quality wey you fit trust',
                    'We no dey disappoint'
                ],
                valuePropositions: [
                    'Best quality for your money',
                    'Fresh ingredients, authentic taste',
                    'Where quality meets affordability',
                    'Premium service, reasonable price'
                ],
                communityConnection: [
                    'Proudly Nigerian',
                    'Serving Lagos with pride',
                    'Your neighborhood favorite',
                    'Where Lagos people gather'
                ],
                localReferences: [
                    'For Lagos Island',
                    'Victoria Island area',
                    'Mainland favorite',
                    'Ikeja corridor'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'Instagram',
                    'Twitter',
                    'WhatsApp',
                    'Facebook'
                ],
                contentPreferences: [
                    'food videos',
                    'customer reactions',
                    'quality showcases',
                    'value demonstrations'
                ],
                engagementStyle: 'High energy, lots of reactions, sharing culture',
                hashtagUsage: 'Mix of English and Pidgin, location tags, trending topics',
                visualPreferences: [
                    'vibrant colors',
                    'appetizing close-ups',
                    'happy customers',
                    'quality focus'
                ]
            }
        });
        // SOUTH AFRICA - Johannesburg/Cape Town
        this.regionalProfiles.set('south_africa', {
            region: 'South Africa',
            country: 'South Africa',
            communicationStyle: {
                directness: 'direct',
                formality: 'casual',
                emotionalExpression: 'medium',
                humorStyle: [
                    'laid-back',
                    'friendly',
                    'inclusive',
                    'warm'
                ],
                persuasionTactics: [
                    'quality focus',
                    'local pride',
                    'community value',
                    'authentic experience'
                ],
                attentionGrabbers: [
                    'Howzit!',
                    'Check this out!',
                    'Lekker!',
                    'Sharp!'
                ]
            },
            advertisingPatterns: [
                {
                    type: 'Lekker & Local',
                    approach: 'Emphasize local flavor and authentic South African experience',
                    examples: [
                        'Proper lekker food, hey!',
                        'Authentic South African taste',
                        'Made with love in Mzansi'
                    ],
                    effectiveness: 'high',
                    platforms: [
                        'instagram',
                        'facebook'
                    ]
                }
            ],
            localSlang: {
                greetings: [
                    'Howzit!',
                    'Sharp!',
                    'Sawubona!',
                    'Hey!'
                ],
                excitement: [
                    'Lekker!',
                    'Sharp sharp!',
                    'Eish!',
                    'Awesome!'
                ],
                approval: [
                    'Lekker!',
                    'Sharp!',
                    'Cool!',
                    'Nice one!'
                ],
                emphasis: [
                    'proper',
                    'lekker',
                    'sharp',
                    'hey'
                ],
                callToAction: [
                    'Come check us out!',
                    'Pop in!',
                    'Give us a try!'
                ],
                endingPhrases: [
                    'Cheers!',
                    'See you now!',
                    'Sharp!'
                ]
            },
            culturalNuances: [
                {
                    aspect: 'Rainbow Nation Unity',
                    importance: 'important',
                    description: 'Inclusive language that welcomes all South Africans',
                    doAndDonts: {
                        do: [
                            'Be inclusive',
                            'Celebrate diversity',
                            'Use local terms naturally'
                        ],
                        dont: [
                            'Exclude any group',
                            'Be overly formal',
                            'Ignore local culture'
                        ]
                    }
                }
            ],
            businessCommunication: {
                trustBuilders: [
                    'Proudly South African',
                    'Local family business',
                    'Trusted by locals',
                    'Authentic Mzansi experience'
                ],
                valuePropositions: [
                    'Lekker food, fair prices',
                    'Authentic local flavors',
                    'Quality you can trust',
                    'Where everyone is welcome'
                ],
                communityConnection: [
                    'Part of the local community',
                    'Supporting local suppliers',
                    'Where neighbors meet',
                    'Celebrating our heritage'
                ],
                localReferences: [
                    'In the heart of [area]',
                    'Your local [business type]',
                    'Joburg favorite',
                    'Cape Town gem'
                ]
            },
            socialMediaBehavior: {
                preferredPlatforms: [
                    'Facebook',
                    'Instagram',
                    'WhatsApp',
                    'Twitter'
                ],
                contentPreferences: [
                    'local culture',
                    'food heritage',
                    'community events',
                    'authentic moments'
                ],
                engagementStyle: 'Friendly, inclusive, community-focused',
                hashtagUsage: 'Local slang mixed with English, location-based',
                visualPreferences: [
                    'natural lighting',
                    'authentic settings',
                    'diverse people',
                    'local culture'
                ]
            }
        });
    // Add more regions as needed...
    }
    /**
   * Get regional communication profile
   */ getRegionalProfile(location) {
        const locationLower = location.toLowerCase();
        // Kenya detection
        if (locationLower.includes('kenya') || locationLower.includes('nairobi') || locationLower.includes('mombasa') || locationLower.includes('kisumu')) {
            return this.regionalProfiles.get('kenya');
        }
        // Nigeria detection
        if (locationLower.includes('nigeria') || locationLower.includes('lagos') || locationLower.includes('abuja') || locationLower.includes('kano')) {
            return this.regionalProfiles.get('nigeria');
        }
        // South Africa detection
        if (locationLower.includes('south africa') || locationLower.includes('johannesburg') || locationLower.includes('cape town') || locationLower.includes('durban')) {
            return this.regionalProfiles.get('south_africa');
        }
        return null;
    }
    /**
   * Generate regionally authentic content
   */ generateRegionalContent(businessType, businessName, location, contentType = 'headline') {
        const profile = this.getRegionalProfile(location);
        if (!profile) {
            return this.generateGenericContent(businessType, businessName, contentType);
        }
        switch(contentType){
            case 'headline':
                return this.generateRegionalHeadline(businessType, businessName, profile);
            case 'subheadline':
                return this.generateRegionalSubheadline(businessType, businessName, profile);
            case 'caption':
                return this.generateRegionalCaption(businessType, businessName, profile);
            case 'cta':
                return this.generateRegionalCTA(businessType, businessName, profile);
            default:
                return this.generateRegionalHeadline(businessType, businessName, profile);
        }
    }
    generateRegionalHeadline(businessType, businessName, profile) {
        const { localSlang, advertisingPatterns, businessCommunication } = profile;
        // Get relevant advertising pattern
        const relevantPattern = advertisingPatterns.find((p)=>p.effectiveness === 'high') || advertisingPatterns[0];
        // Create meaningful headlines that tell a story
        const meaningfulTemplates = [
            `What makes ${businessName} different in ${profile.region}?`,
            `The ${profile.region.toLowerCase()} secret everyone's talking about`,
            `Why ${businessName} is ${profile.region}'s best kept secret`,
            `${this.getRandomElement(businessCommunication.valuePropositions)} - ${businessName}`,
            `Discover what makes ${businessName} special`,
            `${businessName}: ${this.getRandomElement(businessCommunication.trustBuilders)}`
        ];
        // Add local flavor to meaningful content
        const selectedTemplate = this.getRandomElement(meaningfulTemplates);
        // Enhance with local expressions where appropriate
        if (Math.random() > 0.6) {
            const localTouch = this.getRandomElement(localSlang.excitement);
            return `${selectedTemplate} ${localTouch}`;
        }
        return selectedTemplate;
    }
    generateRegionalSubheadline(businessType, businessName, profile) {
        const { localSlang, businessCommunication } = profile;
        // Create meaningful subheadlines that provide context
        const meaningfulTemplates = [
            `${this.getRandomElement(businessCommunication.valuePropositions)} you can trust`,
            `Authentic ${businessType.toLowerCase()} with a local touch`,
            `Where tradition meets innovation`,
            `${this.getRandomElement(businessCommunication.trustBuilders)} since day one`,
            `Bringing ${profile.region}'s finest to your table`,
            `More than just ${businessType.toLowerCase()} - it's an experience`,
            `Crafted with care, served with pride`,
            `Your neighborhood's favorite gathering place`,
            `Quality ingredients, time-tested recipes`,
            `Where every customer becomes family`
        ];
        // Occasionally add local flair
        const baseSubheadline = this.getRandomElement(meaningfulTemplates);
        if (Math.random() > 0.8) {
            const localEmphasis = this.getRandomElement(localSlang.emphasis);
            return `${localEmphasis} ${baseSubheadline.toLowerCase()}`;
        }
        return baseSubheadline;
    }
    generateRegionalCaption(businessType, businessName, profile) {
        const { localSlang, businessCommunication, culturalNuances } = profile;
        // Create meaningful story-driven captions
        const storyTemplates = [
            {
                opening: `Ever wondered what makes ${businessName} stand out?`,
                story: `We've been ${this.getRandomElement(businessCommunication.trustBuilders)} for years, bringing you ${this.getRandomElement(businessCommunication.valuePropositions)}. Our secret? We understand what ${profile.region} truly values.`,
                proof: `From our carefully selected ingredients to our time-tested recipes, every detail matters. That's why we're ${this.getRandomElement(businessCommunication.communityConnection)}.`,
                action: `Ready to taste the difference? ${this.getRandomElement(localSlang.callToAction)}`
            },
            {
                opening: `Here's what makes ${businessName} special in ${profile.region}:`,
                story: `✨ ${this.getRandomElement(businessCommunication.valuePropositions)}\n✨ ${this.getRandomElement(businessCommunication.trustBuilders)}\n✨ ${this.getRandomElement(businessCommunication.communityConnection)}`,
                proof: `We don't just serve ${businessType.toLowerCase()} - we create experiences that bring people together. That's the ${profile.region} way!`,
                action: `Come see for yourself why locals choose us. ${this.getRandomElement(localSlang.callToAction)}`
            },
            {
                opening: `The story behind ${businessName}:`,
                story: `We started with a simple mission: to be ${this.getRandomElement(businessCommunication.trustBuilders)} while delivering ${this.getRandomElement(businessCommunication.valuePropositions)}.`,
                proof: `Today, we're proud to be ${this.getRandomElement(businessCommunication.communityConnection)}, serving authentic ${businessType.toLowerCase()} that reflects our heritage and values.`,
                action: `Join our growing family! ${this.getRandomElement(localSlang.callToAction)}`
            }
        ];
        const selectedStory = this.getRandomElement(storyTemplates);
        // Add local greeting and closing
        const greeting = Math.random() > 0.7 ? `${this.getRandomElement(localSlang.greetings)} ` : '';
        const excitement = Math.random() > 0.5 ? ` ${this.getRandomElement(localSlang.excitement)}` : '';
        const ending = this.getRandomElement(localSlang.endingPhrases);
        return `${greeting}${selectedStory.opening}

${selectedStory.story}

${selectedStory.proof}${excitement}

${selectedStory.action}

${ending}`;
    }
    generateRegionalCTA(businessType, businessName, profile) {
        const { localSlang, businessCommunication } = profile;
        // Create meaningful CTAs that provide clear value
        const meaningfulCTAs = [
            `Taste the difference at ${businessName}`,
            `Experience authentic ${businessType.toLowerCase()} today`,
            `Join our community of satisfied customers`,
            `Discover why locals choose ${businessName}`,
            `Book your table and taste the tradition`,
            `Visit us and see what makes us special`,
            `Come for the food, stay for the experience`,
            `Your next favorite meal awaits`,
            `Ready to become part of our family?`,
            `Let us show you what quality means`
        ];
        // Add local CTAs with context
        const localCTAs = localSlang.callToAction.map((cta)=>{
            if (Math.random() > 0.5) {
                return `${cta} - ${this.getRandomElement(businessCommunication.valuePropositions)}`;
            }
            return cta;
        });
        const allCTAs = [
            ...meaningfulCTAs,
            ...localCTAs
        ];
        return this.getRandomElement(allCTAs);
    }
    generateGenericContent(businessType, businessName, contentType) {
        // Fallback for unsupported regions
        const templates = {
            headline: `Experience the best at ${businessName}`,
            subheadline: `Quality ${businessType.toLowerCase()} you can trust`,
            caption: `Welcome to ${businessName}! We're committed to providing you with exceptional ${businessType} services. Visit us today!`,
            cta: `Visit ${businessName} today!`
        };
        return templates[contentType] || templates.headline;
    }
    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)] || array[0];
    }
    /**
   * Get regional hashtags with AI-powered contextual generation
   */ async getRegionalHashtags(location, businessType, businessName) {
        try {
            // Use AI to generate contextual regional hashtags
            const aiHashtags = await this.generateAIRegionalHashtags(location, businessType, businessName);
            if (aiHashtags.length > 0) {
                return aiHashtags;
            }
        } catch (error) {
            console.warn('AI regional hashtag generation failed, using contextual fallback:', error);
        }
        // Fallback to contextual generation
        return this.generateContextualRegionalHashtags(location, businessType, businessName);
    }
    /**
   * Generate regional hashtags using AI for maximum local relevance
   */ async generateAIRegionalHashtags(location, businessType, businessName) {
        const { GoogleGenerativeAI } = await __turbopack_context__.r("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = ai.getGenerativeModel({
            model: 'gemini-2.5-flash'
        });
        const prompt = `Generate 8 highly relevant, locally-focused hashtags for a ${businessType} business in ${location}.

Business Details:
- Type: ${businessType}
- Location: ${location}
${businessName ? `- Name: ${businessName}` : ''}

Requirements:
1. Create hashtags that are specific to this location and business type
2. Include local cultural references and location-specific terms
3. Avoid generic hashtags like #local, #business, #quality, #community
4. Make hashtags discoverable by locals and tourists
5. Consider local language, culture, and popular local terms
6. Include location-specific food/business culture references

Return ONLY a JSON array of hashtags (including the # symbol):
["#hashtag1", "#hashtag2", "#hashtag3", ...]`;
        try {
            const result = await model.generateContent(prompt);
            let response = result.response.text();
            // Remove markdown code blocks if present
            response = response.replace(/```json\s*|\s*```/g, '').trim();
            // Try to parse as complete JSON first
            try {
                const parsed = JSON.parse(response);
                if (Array.isArray(parsed) && parsed.length > 0) {
                    return parsed.slice(0, 8);
                }
            } catch  {
                // Fallback: extract JSON array from response
                const hashtagsMatch = response.match(/\[.*?\]/);
                if (hashtagsMatch) {
                    const hashtags = JSON.parse(hashtagsMatch[0]);
                    if (Array.isArray(hashtags) && hashtags.length > 0) {
                        return hashtags.slice(0, 8);
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to parse AI regional hashtag response:', error);
        }
        return [];
    }
    /**
   * Generate contextual regional hashtags without hardcoded placeholders
   */ generateContextualRegionalHashtags(location, businessType, businessName) {
        const hashtags = [];
        // Add business-specific hashtags
        if (businessName) {
            hashtags.push(`#${businessName.replace(/\s+/g, '')}`);
        }
        hashtags.push(`#${businessType.replace(/\s+/g, '')}Business`);
        // Add location-based hashtags (more specific than generic)
        const locationParts = location.split(',').map((part)=>part.trim());
        locationParts.forEach((part)=>{
            if (part.length > 2) {
                hashtags.push(`#${part.replace(/\s+/g, '')}`);
                // Add business type + location combination
                hashtags.push(`#${part.replace(/\s+/g, '')}${businessType.replace(/\s+/g, '')}`);
            }
        });
        // Add contextual hashtags based on location (more specific than hardcoded)
        if (location.toLowerCase().includes('nairobi')) {
            hashtags.push('#NairobiEats', '#KenyanCuisine', '#254Business');
        } else if (location.toLowerCase().includes('lagos')) {
            hashtags.push('#LagosEats', '#NaijaFlavors', '#LagosBusiness');
        } else if (location.toLowerCase().includes('johannesburg')) {
            hashtags.push('#JoziEats', '#SouthAfricanTaste', '#JHBBusiness');
        } else {
            // For other locations, create dynamic hashtags
            const cityName = locationParts[0]?.replace(/\s+/g, '') || 'Local';
            hashtags.push(`#${cityName}Eats`, `#${cityName}Business`);
        }
        return [
            ...new Set(hashtags)
        ].slice(0, 8); // Remove duplicates and limit to 8
    }
}
const regionalEngine = new RegionalCommunicationEngine();
}}),
"[project]/src/ai/advanced-content-generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Content Generator
 * Deep business understanding, cultural awareness, and competitive analysis
 */ __turbopack_context__.s({
    "AdvancedContentGenerator": (()=>AdvancedContentGenerator),
    "advancedContentGenerator": (()=>advancedContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/regional-communication-engine.ts [app-route] (ecmascript)");
;
;
class AdvancedContentGenerator {
    businessIntelligence = new Map();
    culturalDatabase = new Map();
    performanceHistory = new Map();
    constructor(){
        this.initializeCulturalDatabase();
        this.initializeBusinessIntelligence();
    }
    /**
   * Initialize cultural database with local knowledge
   */ initializeCulturalDatabase() {
        const cultures = {
            'United States': {
                primaryLanguage: 'English',
                localPhrases: [
                    'awesome',
                    'amazing',
                    'game-changer',
                    'must-have',
                    'life-changing'
                ],
                culturalValues: [
                    'innovation',
                    'convenience',
                    'quality',
                    'value',
                    'authenticity'
                ],
                localEvents: [
                    'Black Friday',
                    'Super Bowl',
                    'Memorial Day',
                    'Labor Day'
                ],
                communicationStyle: 'direct, enthusiastic, benefit-focused',
                localInfluencers: [
                    'lifestyle',
                    'fitness',
                    'food',
                    'tech',
                    'business'
                ]
            },
            'United Kingdom': {
                primaryLanguage: 'English',
                localPhrases: [
                    'brilliant',
                    'fantastic',
                    'proper',
                    'lovely',
                    'spot on'
                ],
                culturalValues: [
                    'tradition',
                    'quality',
                    'reliability',
                    'heritage',
                    'craftsmanship'
                ],
                localEvents: [
                    'Boxing Day',
                    'Bank Holiday',
                    'Wimbledon',
                    'Royal events'
                ],
                communicationStyle: 'polite, understated, witty',
                localInfluencers: [
                    'lifestyle',
                    'fashion',
                    'food',
                    'travel',
                    'culture'
                ]
            },
            'Canada': {
                primaryLanguage: 'English',
                localPhrases: [
                    'eh',
                    'beauty',
                    'fantastic',
                    'wonderful',
                    'great'
                ],
                culturalValues: [
                    'friendliness',
                    'inclusivity',
                    'nature',
                    'community',
                    'sustainability'
                ],
                localEvents: [
                    'Canada Day',
                    'Victoria Day',
                    'Thanksgiving',
                    'Winter Olympics'
                ],
                communicationStyle: 'friendly, inclusive, nature-focused',
                localInfluencers: [
                    'outdoor',
                    'lifestyle',
                    'food',
                    'wellness',
                    'community'
                ]
            },
            'Australia': {
                primaryLanguage: 'English',
                localPhrases: [
                    'mate',
                    'fair dinkum',
                    'ripper',
                    'bonzer',
                    'ace'
                ],
                culturalValues: [
                    'laid-back',
                    'outdoor lifestyle',
                    'mateship',
                    'adventure',
                    'authenticity'
                ],
                localEvents: [
                    'Australia Day',
                    'Melbourne Cup',
                    'ANZAC Day',
                    'AFL Grand Final'
                ],
                communicationStyle: 'casual, friendly, straightforward',
                localInfluencers: [
                    'outdoor',
                    'fitness',
                    'food',
                    'travel',
                    'lifestyle'
                ]
            }
        };
        Object.entries(cultures).forEach(([location, context])=>{
            this.culturalDatabase.set(location, context);
        });
    }
    /**
   * Initialize business intelligence database
   */ initializeBusinessIntelligence() {
        const businessTypes = {
            restaurant: {
                industryKeywords: [
                    'fresh',
                    'delicious',
                    'authentic',
                    'homemade',
                    'seasonal',
                    'local',
                    'chef-crafted'
                ],
                businessStrengths: [
                    'taste',
                    'atmosphere',
                    'service',
                    'ingredients',
                    'experience'
                ],
                targetEmotions: [
                    'hunger',
                    'comfort',
                    'satisfaction',
                    'joy',
                    'nostalgia'
                ],
                valuePropositions: [
                    'quality ingredients',
                    'unique flavors',
                    'memorable experience',
                    'value for money'
                ],
                localRelevance: [
                    'neighborhood favorite',
                    'local ingredients',
                    'community gathering'
                ],
                seasonalOpportunities: [
                    'seasonal menu',
                    'holiday specials',
                    'summer patio',
                    'winter comfort'
                ]
            },
            retail: {
                industryKeywords: [
                    'trendy',
                    'stylish',
                    'affordable',
                    'quality',
                    'exclusive',
                    'limited',
                    'new arrival'
                ],
                businessStrengths: [
                    'selection',
                    'price',
                    'quality',
                    'customer service',
                    'convenience'
                ],
                targetEmotions: [
                    'desire',
                    'confidence',
                    'satisfaction',
                    'excitement',
                    'belonging'
                ],
                valuePropositions: [
                    'best prices',
                    'latest trends',
                    'quality guarantee',
                    'exclusive access'
                ],
                localRelevance: [
                    'local fashion',
                    'community style',
                    'neighborhood store'
                ],
                seasonalOpportunities: [
                    'seasonal collections',
                    'holiday sales',
                    'back-to-school',
                    'summer styles'
                ]
            },
            fitness: {
                industryKeywords: [
                    'strong',
                    'healthy',
                    'fit',
                    'transformation',
                    'results',
                    'energy',
                    'powerful'
                ],
                businessStrengths: [
                    'expertise',
                    'results',
                    'community',
                    'equipment',
                    'motivation'
                ],
                targetEmotions: [
                    'motivation',
                    'confidence',
                    'achievement',
                    'energy',
                    'determination'
                ],
                valuePropositions: [
                    'proven results',
                    'expert guidance',
                    'supportive community',
                    'flexible schedules'
                ],
                localRelevance: [
                    'neighborhood gym',
                    'local fitness community',
                    'accessible location'
                ],
                seasonalOpportunities: [
                    'New Year resolutions',
                    'summer body',
                    'holiday fitness',
                    'spring training'
                ]
            },
            beauty: {
                industryKeywords: [
                    'glowing',
                    'radiant',
                    'beautiful',
                    'flawless',
                    'natural',
                    'luxurious',
                    'rejuvenating'
                ],
                businessStrengths: [
                    'expertise',
                    'products',
                    'results',
                    'relaxation',
                    'personalization'
                ],
                targetEmotions: [
                    'confidence',
                    'relaxation',
                    'beauty',
                    'self-care',
                    'transformation'
                ],
                valuePropositions: [
                    'expert care',
                    'premium products',
                    'personalized service',
                    'lasting results'
                ],
                localRelevance: [
                    'trusted local salon',
                    'community beauty expert',
                    'neighborhood favorite'
                ],
                seasonalOpportunities: [
                    'bridal season',
                    'holiday glam',
                    'summer skin',
                    'winter care'
                ]
            }
        };
        Object.entries(businessTypes).forEach(([type, intelligence])=>{
            this.businessIntelligence.set(type, intelligence);
        });
    }
    /**
   * Analyze business and context for content generation
   */ async analyzeBusinessContext(profile) {
        // Get business intelligence
        const businessIntelligence = this.businessIntelligence.get(profile.businessType) || {
            industryKeywords: [],
            businessStrengths: [],
            targetEmotions: [],
            valuePropositions: [],
            localRelevance: [],
            seasonalOpportunities: []
        };
        // Get cultural context
        const culturalContext = this.culturalDatabase.get(profile.location) || {
            primaryLanguage: 'English',
            localPhrases: [],
            culturalValues: [],
            localEvents: [],
            communicationStyle: 'friendly, professional',
            localInfluencers: []
        };
        // Get trending insights
        const trendingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
            businessType: profile.businessType,
            location: profile.location,
            targetAudience: profile.targetAudience
        });
        const trendingInsights = {
            currentTrends: trendingData.keywords,
            viralPatterns: trendingData.topics,
            platformSpecificTrends: trendingData.hashtags,
            seasonalTrends: trendingData.seasonalThemes,
            emergingTopics: trendingData.industryBuzz
        };
        // Analyze competitors (simulated for now)
        const competitiveAnalysis = {
            industryBenchmarks: this.generateIndustryBenchmarks(profile.businessType),
            competitorStrategies: this.analyzeCompetitorStrategies(profile.competitors),
            marketGaps: this.identifyMarketGaps(profile.businessType),
            differentiators: profile.uniqueSellingPoints,
            performanceTargets: this.setPerformanceTargets(profile.businessType)
        };
        return {
            businessIntelligence,
            culturalContext,
            competitiveAnalysis,
            trendingInsights
        };
    }
    /**
   * Generate highly engaging content based on analysis
   */ async generateEngagingContent(profile, platform, contentType = 'promotional') {
        const analysis = await this.analyzeBusinessContext(profile);
        // Generate content components
        const headline = await this.generateCatchyHeadline(profile, analysis, platform, contentType);
        const subheadline = await this.generateSubheadline(profile, analysis, headline);
        const caption = await this.generateEngagingCaption(profile, analysis, headline, platform);
        const cta = await this.generateCompellingCTA(profile, analysis, platform, contentType);
        const hashtags = await this.generateStrategicHashtags(profile, analysis, platform);
        const post = {
            headline,
            subheadline,
            caption,
            cta,
            hashtags,
            platform
        };
        // Store for performance tracking
        this.storePostForAnalysis(profile.businessName, post);
        return post;
    }
    /**
   * Generate catchy, business-specific headlines with regional authenticity
   */ async generateCatchyHeadline(profile, analysis, platform, contentType) {
        const { businessIntelligence, culturalContext, trendingInsights } = analysis;
        // Try regional communication first for authentic local content
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            const regionalHeadline = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'headline');
            // Enhance with trending elements if available
            if (trendingInsights.currentTrends.length > 0) {
                const trendingElement = this.getRandomElement(trendingInsights.currentTrends);
                return `${regionalHeadline} - ${trendingElement}`;
            }
            return regionalHeadline;
        }
        // Fallback to original method for unsupported regions
        // Combine business strengths with trending topics
        const powerWords = [
            ...businessIntelligence.industryKeywords,
            ...culturalContext.localPhrases
        ];
        const trendingWords = trendingInsights.currentTrends.slice(0, 5);
        // Create headline templates based on content type
        const templates = {
            promotional: [
                `${this.getRandomElement(powerWords)} ${profile.businessName} ${this.getRandomElement(businessIntelligence.valuePropositions)}`,
                `${this.getRandomElement(culturalContext.localPhrases)} ${this.getRandomElement(trendingWords)} at ${profile.businessName}`,
                `${profile.businessName}: ${this.getRandomElement(businessIntelligence.businessStrengths)} that ${this.getRandomElement(businessIntelligence.targetEmotions)}`
            ],
            educational: [
                `${this.getRandomElement(trendingWords)} secrets from ${profile.businessName}`,
                `Why ${profile.businessName} ${this.getRandomElement(businessIntelligence.businessStrengths)} matters`,
                `The ${this.getRandomElement(powerWords)} guide to ${this.getRandomElement(businessIntelligence.industryKeywords)}`
            ],
            entertaining: [
                `${this.getRandomElement(culturalContext.localPhrases)}! ${profile.businessName} ${this.getRandomElement(trendingWords)}`,
                `${profile.businessName} + ${this.getRandomElement(trendingWords)} = ${this.getRandomElement(powerWords)}`,
                `When ${this.getRandomElement(businessIntelligence.targetEmotions)} meets ${profile.businessName}`
            ],
            seasonal: [
                `${this.getRandomElement(trendingInsights.seasonalTrends)} ${this.getRandomElement(powerWords)} at ${profile.businessName}`,
                `${profile.businessName}'s ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,
                `${this.getRandomElement(culturalContext.localEvents)} special: ${this.getRandomElement(businessIntelligence.valuePropositions)}`
            ]
        };
        const selectedTemplate = this.getRandomElement(templates[contentType]);
        return this.capitalizeWords(selectedTemplate);
    }
    /**
   * Generate supporting subheadlines
   */ async generateSubheadline(profile, analysis, headline) {
        const { businessIntelligence, culturalContext } = analysis;
        const supportingElements = [
            ...businessIntelligence.valuePropositions,
            ...businessIntelligence.localRelevance,
            ...culturalContext.culturalValues
        ];
        const templates = [
            `${this.getRandomElement(supportingElements)} in ${profile.location}`,
            `${this.getRandomElement(businessIntelligence.businessStrengths)} you can trust`,
            `${this.getRandomElement(culturalContext.localPhrases)} experience awaits`
        ];
        return this.getRandomElement(templates);
    }
    /**
   * Generate engaging, culturally-aware captions with regional authenticity
   */ async generateEngagingCaption(profile, analysis, headline, platform) {
        const { businessIntelligence, culturalContext, trendingInsights } = analysis;
        // Try regional communication first for authentic local content
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'caption');
        }
        // Fallback to original method for unsupported regions
        // Platform-specific caption styles
        const platformStyles = {
            instagram: 'visual, lifestyle-focused, emoji-rich',
            facebook: 'community-focused, conversational, story-driven',
            twitter: 'concise, witty, trending-aware',
            linkedin: 'professional, value-driven, industry-focused',
            tiktok: 'trendy, fun, challenge-oriented'
        };
        const captionElements = [
            `At ${profile.businessName}, we believe ${this.getRandomElement(businessIntelligence.valuePropositions)}.`,
            `Our ${this.getRandomElement(businessIntelligence.businessStrengths)} brings ${this.getRandomElement(businessIntelligence.targetEmotions)} to ${profile.location}.`,
            `${this.getRandomElement(culturalContext.localPhrases)}! ${this.getRandomElement(trendingInsights.currentTrends)} meets ${this.getRandomElement(businessIntelligence.industryKeywords)}.`,
            `Join our ${profile.location} community for ${this.getRandomElement(businessIntelligence.localRelevance)}.`
        ];
        return captionElements.slice(0, 2).join(' ');
    }
    /**
   * Generate compelling CTAs with regional authenticity
   */ async generateCompellingCTA(profile, analysis, platform, contentType) {
        const { businessIntelligence, culturalContext } = analysis;
        // Try regional communication first for authentic local CTAs
        const regionalProfile = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalProfile(profile.location);
        if (regionalProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].generateRegionalContent(profile.businessType, profile.businessName, profile.location, 'cta');
        }
        // Fallback to original method for unsupported regions
        const ctaTemplates = {
            promotional: [
                `Visit ${profile.businessName} today!`,
                `Experience ${this.getRandomElement(businessIntelligence.businessStrengths)} now`,
                `Book your ${this.getRandomElement(businessIntelligence.targetEmotions)} experience`
            ],
            educational: [
                `Learn more at ${profile.businessName}`,
                `Discover the ${this.getRandomElement(businessIntelligence.industryKeywords)} difference`,
                `Get expert advice from ${profile.businessName}`
            ],
            entertaining: [
                `Join the fun at ${profile.businessName}!`,
                `Share your ${profile.businessName} experience`,
                `Tag a friend who needs this!`
            ],
            seasonal: [
                `Don't miss our ${this.getRandomElement(businessIntelligence.seasonalOpportunities)}`,
                `Limited time at ${profile.businessName}`,
                `Celebrate with ${profile.businessName}`
            ]
        };
        return this.getRandomElement(ctaTemplates[contentType]);
    }
    /**
   * Generate strategic hashtags with AI-powered contextual generation
   */ async generateStrategicHashtags(profile, analysis, platform) {
        try {
            // Use AI to generate contextual hashtags
            const aiHashtags = await this.generateAIHashtags(profile, platform);
            if (aiHashtags.length > 0) {
                return aiHashtags;
            }
        } catch (error) {
            console.warn('AI hashtag generation failed, using fallback:', error);
        }
        // Try regional hashtags as fallback
        try {
            const regionalHashtags = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$regional$2d$communication$2d$engine$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["regionalEngine"].getRegionalHashtags(profile.location, profile.businessType, profile.businessName);
            if (regionalHashtags.length > 0) {
                return regionalHashtags.slice(0, 10);
            }
        } catch (error) {
            console.warn('Regional hashtag generation failed:', error);
        }
        // Final fallback - generate contextual hashtags without hardcoded values
        return this.generateContextualFallbackHashtags(profile, platform);
    }
    /**
   * Generate hashtags using AI for maximum relevance and engagement
   */ async generateAIHashtags(profile, platform) {
        const { GoogleGenerativeAI } = await __turbopack_context__.r("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        const ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = ai.getGenerativeModel({
            model: 'gemini-2.5-flash'
        });
        const prompt = `Generate 10 highly relevant, engaging hashtags for a ${profile.businessType} business on ${platform}.

Business Details:
- Name: ${profile.businessName}
- Type: ${profile.businessType}
- Location: ${profile.location}
- Target Audience: ${profile.targetAudience || 'local customers'}
- Brand Voice: ${profile.brandVoice || 'professional and friendly'}

Requirements:
1. Create hashtags that are specific to this business and location
2. Include a mix of: business-specific, location-based, industry-relevant, and platform-optimized hashtags
3. Avoid generic hashtags like #business, #professional, #quality, #local
4. Make hashtags discoverable and relevant to the target audience
5. Consider current trends and seasonal relevance
6. Ensure hashtags are appropriate for ${platform}

Return ONLY a JSON array of hashtags (including the # symbol):
["#hashtag1", "#hashtag2", "#hashtag3", ...]`;
        try {
            const result = await model.generateContent(prompt);
            let response = result.response.text();
            // Remove markdown code blocks if present
            response = response.replace(/```json\s*|\s*```/g, '').trim();
            // Try to parse as complete JSON first
            try {
                const parsed = JSON.parse(response);
                if (Array.isArray(parsed) && parsed.length > 0) {
                    return parsed.slice(0, 10);
                }
            } catch  {
                // Fallback: extract JSON array from response
                const hashtagsMatch = response.match(/\[.*?\]/);
                if (hashtagsMatch) {
                    const hashtags = JSON.parse(hashtagsMatch[0]);
                    if (Array.isArray(hashtags) && hashtags.length > 0) {
                        return hashtags.slice(0, 10);
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to parse AI hashtag response:', error);
        }
        return [];
    }
    /**
   * Generate contextual fallback hashtags without hardcoded placeholders
   */ generateContextualFallbackHashtags(profile, platform) {
        const hashtags = [];
        // Business-specific hashtags (always include)
        hashtags.push(`#${profile.businessName.replace(/\s+/g, '')}`);
        // Varied business type hashtags
        const businessTypeVariations = [
            `#${profile.businessType.replace(/\s+/g, '')}Business`,
            `#${profile.businessType.replace(/\s+/g, '')}Life`,
            `#${profile.businessType.replace(/\s+/g, '')}Experience`,
            `#${profile.businessType.replace(/\s+/g, '')}Excellence`,
            `#${profile.businessType.replace(/\s+/g, '')}Quality`,
            `#${profile.businessType.replace(/\s+/g, '')}Expert`
        ];
        hashtags.push(this.getRandomElement(businessTypeVariations));
        // Location-based hashtags with variations
        const locationParts = profile.location.split(',').map((part)=>part.trim());
        locationParts.forEach((part)=>{
            if (part.length > 2) {
                const locationVariations = [
                    `#${part.replace(/\s+/g, '')}`,
                    `#${part.replace(/\s+/g, '')}Business`,
                    `#${part.replace(/\s+/g, '')}Local`,
                    `#${part.replace(/\s+/g, '')}Community`,
                    `#${part.replace(/\s+/g, '')}Life`
                ];
                hashtags.push(this.getRandomElement(locationVariations));
            }
        });
        // Dynamic contextual hashtags
        const contextualHashtags = this.getDynamicContextualHashtags(profile.businessType, platform);
        hashtags.push(...contextualHashtags);
        // Time-based hashtags with variation
        const timeBasedHashtags = this.getVariedTimeBasedHashtags();
        hashtags.push(...timeBasedHashtags);
        // Industry-specific dynamic hashtags
        const industryHashtags = this.getVariedIndustryHashtags(profile.businessType);
        hashtags.push(...industryHashtags);
        return [
            ...new Set(hashtags)
        ].slice(0, 10); // Remove duplicates and limit to 10
    }
    /**
   * Get a random element from an array
   */ getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
    /**
   * Get multiple random elements from an array
   */ getRandomElements(array, count) {
        const shuffled = [
            ...array
        ].sort(()=>0.5 - Math.random());
        return shuffled.slice(0, count);
    }
    /**
   * Get dynamic contextual hashtags with variation
   */ getDynamicContextualHashtags(businessType, platform) {
        const hashtags = [];
        // Platform-specific hashtags with variation
        const platformHashtags = {
            instagram: [
                '#InstagramContent',
                '#VisualStory',
                '#InstaGood',
                '#PhotoOfTheDay',
                '#InstagramBusiness'
            ],
            facebook: [
                '#FacebookPost',
                '#CommunityFirst',
                '#SocialConnection',
                '#FacebookBusiness',
                '#CommunityLove'
            ],
            linkedin: [
                '#LinkedInContent',
                '#ProfessionalNetwork',
                '#BusinessGrowth',
                '#LinkedInPost',
                '#ProfessionalLife'
            ],
            tiktok: [
                '#TikTokContent',
                '#CreativeVideo',
                '#TikTokBusiness',
                '#VideoContent',
                '#CreativeExpression'
            ]
        };
        if (platformHashtags[platform]) {
            const options = platformHashtags[platform];
            hashtags.push(...this.getRandomElements(options, 2));
        }
        return hashtags;
    }
    /**
   * Get varied time-based hashtags
   */ getVariedTimeBasedHashtags() {
        const today = new Date();
        const timeOptions = [
            // Day-based
            [
                `#${today.toLocaleDateString('en-US', {
                    weekday: 'long'
                })}Vibes`,
                `#${today.toLocaleDateString('en-US', {
                    weekday: 'long'
                })}Motivation`
            ],
            // Month-based
            [
                `#${today.toLocaleDateString('en-US', {
                    month: 'long'
                })}${today.getFullYear()}`,
                `#${today.toLocaleDateString('en-US', {
                    month: 'long'
                })}Goals`
            ],
            // Season-based
            this.getSeasonalHashtags(),
            // Time of day
            this.getTimeOfDayHashtags()
        ];
        const selectedOptions = this.getRandomElements(timeOptions.flat(), 2);
        return selectedOptions;
    }
    /**
   * Get seasonal hashtags
   */ getSeasonalHashtags() {
        const month = new Date().getMonth();
        if (month >= 2 && month <= 4) return [
            '#SpringVibes',
            '#FreshStart',
            '#SpringEnergy'
        ];
        else if (month >= 5 && month <= 7) return [
            '#SummerVibes',
            '#SummerEnergy',
            '#SunnyDays'
        ];
        else if (month >= 8 && month <= 10) return [
            '#AutumnVibes',
            '#FallFlavors',
            '#HarvestSeason'
        ];
        else return [
            '#WinterVibes',
            '#CozyMoments',
            '#WarmthInWinter'
        ];
    }
    /**
   * Get time of day hashtags
   */ getTimeOfDayHashtags() {
        const hour = new Date().getHours();
        if (hour < 12) return [
            '#MorningMotivation',
            '#FreshStart',
            '#MorningEnergy'
        ];
        else if (hour < 17) return [
            '#AfternoonBoost',
            '#MidDayMoments',
            '#AfternoonVibes'
        ];
        else return [
            '#EveningVibes',
            '#NightTime',
            '#EveningEnergy'
        ];
    }
    /**
   * Get varied industry-specific hashtags
   */ getVariedIndustryHashtags(businessType) {
        const industryVariations = {
            restaurant: [
                '#FoodieLife',
                '#CulinaryExperience',
                '#TasteOfExcellence',
                '#DiningExperience',
                '#FlavorJourney'
            ],
            retail: [
                '#ShoppingExperience',
                '#RetailTherapy',
                '#StyleStatement',
                '#QualityProducts',
                '#ShopLocal'
            ],
            service: [
                '#ServiceExcellence',
                '#CustomerFirst',
                '#ProfessionalService',
                '#TrustedService',
                '#QualityService'
            ],
            healthcare: [
                '#HealthAndWellness',
                '#CareYouCanTrust',
                '#HealthFirst',
                '#WellnessJourney',
                '#HealthcareExcellence'
            ],
            technology: [
                '#TechInnovation',
                '#DigitalSolutions',
                '#TechExcellence',
                '#InnovativeApproach',
                '#TechLeadership'
            ]
        };
        const businessKey = businessType.toLowerCase();
        const matchingVariations = Object.keys(industryVariations).find((key)=>businessKey.includes(key));
        if (matchingVariations) {
            return this.getRandomElements(industryVariations[matchingVariations], 2);
        }
        // Generic business hashtags with variation
        const genericOptions = [
            '#BusinessExcellence',
            '#QualityFirst',
            '#CustomerSatisfaction',
            '#ProfessionalService',
            '#TrustedBrand'
        ];
        return this.getRandomElements(genericOptions, 2);
    }
    /**
   * Generate regional subheadlines
   */ generateRegionalSubheadline(profile, regionalProfile, context) {
        const { businessCommunication, localSlang } = regionalProfile;
        const templates = [
            `${this.getRandomElement(businessCommunication.valuePropositions)} in ${profile.location}`,
            `${this.getRandomElement(businessCommunication.trustBuilders)} - ${this.getRandomElement(localSlang.approval)}`,
            `${this.getRandomElement(businessCommunication.communityConnection)} ${this.getRandomElement(localSlang.emphasis)}`
        ];
        return this.getRandomElement(templates);
    }
    // Helper methods
    getRandomElement(array) {
        return array[Math.floor(Math.random() * array.length)] || array[0];
    }
    capitalizeWords(str) {
        return str.replace(/\b\w/g, (l)=>l.toUpperCase());
    }
    generateIndustryBenchmarks(businessType) {
        return [
            `${businessType} industry standard`,
            'market leader performance',
            'customer satisfaction benchmark'
        ];
    }
    analyzeCompetitorStrategies(competitors) {
        return competitors.map((comp)=>`${comp} strategy analysis`);
    }
    identifyMarketGaps(businessType) {
        return [
            `${businessType} market opportunity`,
            'underserved customer segment',
            'innovation gap'
        ];
    }
    setPerformanceTargets(businessType) {
        return [
            'high engagement rate',
            'increased brand awareness',
            'customer acquisition'
        ];
    }
    storePostForAnalysis(businessName, post) {
        const existing = this.performanceHistory.get(businessName) || [];
        existing.push(post);
        this.performanceHistory.set(businessName, existing.slice(-50)); // Keep last 50 posts
    }
}
const advancedContentGenerator = new AdvancedContentGenerator();
}}),
"[project]/src/ai/revo-2.0-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Service - Next-Generation AI Content Creation
 * Uses Gemini 2.5 Flash Image Preview for enhanced content generation
 */ __turbopack_context__.s({
    "generateWithRevo20": (()=>generateWithRevo20),
    "testRevo20Availability": (()=>testRevo20Availability)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/creative-enhancement.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/advanced-content-generator.ts [app-route] (ecmascript)");
;
;
;
;
// Initialize AI clients
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](process.env.GEMINI_API_KEY);
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
// Revo 2.0 uses Gemini 2.5 Flash (image generation not available in preview models)
const REVO_2_0_MODEL = 'gemini-2.5-flash';
/**
 * Generate enhanced creative concept using GPT-4
 */ async function generateCreativeConcept(options) {
    const { businessType: businessType1, platform, brandProfile: brandProfile1, visualStyle = 'modern' } = options;
    const prompt = `You are a world-class creative director specializing in ${businessType1} businesses. 
Create an authentic, locally-relevant creative concept for ${platform} that feels genuine and relatable.

Business Context:
- Type: ${businessType1}
- Platform: ${platform}
- Style: ${visualStyle}
- Location: ${brandProfile1.location || 'Global'}
- Brand: ${brandProfile1.businessName || businessType1}

Create a concept that:
1. Feels authentic and locally relevant
2. Uses relatable human experiences
3. Connects emotionally with the target audience
4. Incorporates cultural nuances naturally
5. Avoids generic corporate messaging

Return your response in this exact JSON format:
{
  "concept": "Relatable, human concept that locals would connect with (2-3 sentences, conversational tone)",
  "catchwords": ["word1", "word2", "word3", "word4", "word5"],
  "visualDirection": "Authentic visual direction that feels real and community-focused (2-3 sentences)",
  "designElements": ["element1", "element2", "element3", "element4"],
  "colorSuggestions": ["#color1", "#color2", "#color3"],
  "moodKeywords": ["mood1", "mood2", "mood3", "mood4"],
  "targetEmotions": ["emotion1", "emotion2", "emotion3"]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.8,
        max_tokens: 1000
    });
    try {
        const content = response.choices[0].message.content || '{}';
        // Remove markdown code blocks if present
        const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        return JSON.parse(cleanContent);
    } catch (error) {
        return {
            concept: `Professional ${businessType1} content for ${platform}`,
            catchwords: [
                'quality',
                'professional',
                'trusted',
                'local',
                'expert'
            ],
            visualDirection: 'Clean, professional design with modern aesthetics',
            designElements: [
                'clean typography',
                'professional imagery',
                'brand colors',
                'modern layout'
            ],
            colorSuggestions: [
                '#2563eb',
                '#1f2937',
                '#f8fafc'
            ],
            moodKeywords: [
                'professional',
                'trustworthy',
                'modern',
                'clean'
            ],
            targetEmotions: [
                'trust',
                'confidence',
                'reliability'
            ]
        };
    }
}
async function generateWithRevo20(options) {
    const startTime = Date.now();
    try {
        // Step 1: Generate creative concept
        const concept = await generateCreativeConcept(options);
        // Step 2: Generate sophisticated content first (needed for image generation)
        const contentResult = await generateSophisticatedContent(options, concept);
        // Step 3: Build enhanced prompt with structured content
        const enhancedPrompt = buildEnhancedPromptWithStructuredContent(options, concept, contentResult);
        // Debug: Log the content being sent to image generation
        console.log('🎨 IMAGE GENERATION DEBUG:');
        console.log(`   Headline: "${contentResult.headline}"`);
        console.log(`   Subheadline: "${contentResult.subheadline}"`);
        console.log(`   CTA: "${contentResult.cta}"`);
        console.log(`   Business: ${brandProfile.businessName || businessType}`);
        console.log('');
        // Step 4: Generate image with Gemini 2.5 Flash Image Preview using structured content
        const imageResult = await generateImageWithGemini(enhancedPrompt, options);
        const processingTime = Date.now() - startTime;
        return {
            imageUrl: imageResult.imageUrl,
            model: 'Revo 2.0 Enhanced (Gemini 2.5 Flash Image Preview)',
            qualityScore: 9.5,
            processingTime,
            enhancementsApplied: [
                'Creative concept generation',
                'Business intelligence analysis',
                'Sophisticated content strategy',
                'Multi-component content generation',
                'Enhanced prompt engineering',
                'Brand consistency optimization',
                'Platform-specific formatting',
                'Cultural relevance integration'
            ],
            headline: contentResult.headline,
            subheadline: contentResult.subheadline,
            caption: contentResult.caption,
            cta: contentResult.cta,
            hashtags: contentResult.hashtags,
            businessIntelligence: contentResult.businessIntelligence
        };
    } catch (error) {
        throw new Error(`Revo 2.0 generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Build enhanced prompt with structured content for Gemini 2.5 Flash Image Preview
 */ function buildEnhancedPromptWithStructuredContent(options, concept, contentResult) {
    const { businessType: businessType1, platform, visualStyle, brandProfile: brandProfile1 } = options;
    // Create structured text for image integration
    const structuredText = [
        contentResult.headline,
        contentResult.subheadline,
        contentResult.cta
    ].filter((text)=>text && text.trim()).join(' | ');
    return `Create a professional ${visualStyle} social media design for ${platform} featuring:

BUSINESS: ${brandProfile1.businessName || businessType1} (${businessType1})
LOCATION: ${brandProfile1.location}

⚠️ CRITICAL: USE ONLY THE PROVIDED TEXT - DO NOT CREATE YOUR OWN TEXT ⚠️

EXACT TEXT TO USE (DO NOT CHANGE OR REPLACE):
- HEADLINE (large, prominent): "${contentResult.headline}"
- SUBHEADLINE (medium, supporting): "${contentResult.subheadline}"
- CALL-TO-ACTION (bold, actionable): "${contentResult.cta}"

DESIGN REQUIREMENTS:
- Visual Style: ${visualStyle}
- Platform: ${platform} (${options.aspectRatio || '1:1'} aspect ratio)
- Brand Colors: Primary ${brandProfile1.primaryColor || '#3B82F6'}, Accent ${brandProfile1.accentColor || '#10B981'}
- Background: ${brandProfile1.backgroundColor || '#F8FAFC'}

MANDATORY TEXT INTEGRATION RULES:
- Use EXACTLY the provided headline: "${contentResult.headline}" - DO NOT create different text
- Use EXACTLY the provided subheadline: "${contentResult.subheadline}" - DO NOT create different text
- Use EXACTLY the provided CTA: "${contentResult.cta}" - DO NOT create different text
- DO NOT add words like "NOSTALGIA" or any other text not provided above
- DO NOT create generic headlines - use only the business-specific text provided

VISUAL ELEMENTS:
- Concept: ${concept.concept}
- Style Direction: ${concept.visualDirection}
- Design Elements: ${concept.designElements?.join(', ')}
- Color Palette: ${concept.colorSuggestions?.join(', ')}
- Mood: ${concept.moodKeywords?.join(', ')}

QUALITY REQUIREMENTS:
- High-resolution, professional design
- Text must be clearly readable and well-positioned
- Brand colors should be prominently featured
- Design should be optimized for ${platform}
- Include visual elements that support the business type
${options.includePeopleInDesigns ? '- Include people/customers when contextually appropriate' : ''}

🚨 FINAL REMINDER: DO NOT CREATE ANY TEXT OTHER THAN WHAT IS SPECIFIED ABOVE 🚨
- NO generic words like "NOSTALGIA", "MEMORIES", "TRADITION", etc.
- NO made-up headlines or slogans
- USE ONLY: "${contentResult.headline}" | "${contentResult.subheadline}" | "${contentResult.cta}"

Create a visually stunning design that integrates ONLY the specified text elements seamlessly into the overall composition.`;
}
/**
 * Build enhanced prompt for Revo 2.0 (legacy version)
 */ function buildEnhancedPrompt(options, concept) {
    const { businessType: businessType1, platform, brandProfile: brandProfile1, aspectRatio = '1:1', visualStyle = 'modern' } = options;
    return `Create a high-quality, professional ${businessType1} design for ${platform}.

CREATIVE CONCEPT: ${concept.concept}

VISUAL DIRECTION: ${concept.visualDirection}

DESIGN REQUIREMENTS:
- Style: ${visualStyle}, premium quality
- Aspect Ratio: ${aspectRatio}
- Platform: ${platform} optimized
- Business: ${brandProfile1.businessName || businessType1}
- Location: ${brandProfile1.location || 'Professional setting'}

DESIGN ELEMENTS:
${concept.designElements.map((element)=>`- ${element}`).join('\n')}

MOOD & EMOTIONS:
- Target emotions: ${concept.targetEmotions.join(', ')}
- Mood keywords: ${concept.moodKeywords.join(', ')}

BRAND INTEGRATION:
- Colors: ${brandProfile1.primaryColor ? `Primary: ${brandProfile1.primaryColor}, Accent: ${brandProfile1.accentColor}, Background: ${brandProfile1.backgroundColor}` : concept.colorSuggestions.join(', ')}
- Business name: ${brandProfile1.businessName || businessType1}
- Logo: ${brandProfile1.logoDataUrl ? 'Include provided brand logo prominently' : 'No logo provided'}
- Professional, trustworthy appearance

QUALITY STANDARDS:
- Ultra-high resolution and clarity
- Professional composition
- Perfect typography and text rendering
- MAXIMUM 3 COLORS ONLY (use brand colors if provided)
- NO LINES: no decorative lines, borders, dividers, or linear elements
- Platform-optimized dimensions
- Brand consistency throughout
- Clean, minimalist design with 50%+ white space

Create a stunning, professional design that captures the essence of this ${businessType1} business.`;
}
/**
 * Generate image using Gemini 2.5 Flash Image Preview with logo support
 */ async function generateImageWithGemini(prompt, options) {
    const maxRetries = 3;
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            const model = ai.getGenerativeModel({
                model: REVO_2_0_MODEL,
                generationConfig: {
                    temperature: 0.7,
                    topP: 0.9,
                    topK: 40,
                    maxOutputTokens: 2048
                }
            });
            // Extract the exact text from the prompt to ensure it's used
            const headlineMatch = prompt.match(/- Headline: "([^"]+)"/);
            const subheadlineMatch = prompt.match(/- Subheadline: "([^"]+)"/);
            const ctaMatch = prompt.match(/- Call-to-Action: "([^"]+)"/);
            const exactHeadline = headlineMatch ? headlineMatch[1] : `${options.businessType} Excellence`;
            const exactSubheadline = subheadlineMatch ? subheadlineMatch[1] : `Experience quality ${options.businessType.toLowerCase()} services`;
            const exactCTA = ctaMatch ? ctaMatch[1] : 'Visit us today!';
            // Prepare the generation request with logo if available
            const generationParts = [
                `🚨 ABSOLUTE REQUIREMENT: You are creating a social media design. You MUST use EXACTLY these words and NO OTHER TEXT:

HEADLINE (large, prominent text): "${exactHeadline}"
SUBHEADLINE (medium, supporting text): "${exactSubheadline}"
CTA (bold, actionable text): "${exactCTA}"

FORBIDDEN WORDS: Do NOT use NOSTALGIA, MEMORIES, TRADITION, GRANDMA, HOMEMADE, or any other generic terms.
REQUIREMENT: Use ONLY the exact text specified above - do not create your own headlines or slogans.

Create professional, high-quality social media images with perfect text rendering and 2048x2048 resolution.`,
                prompt
            ];
            // If logo is provided, include it in the generation
            if (options.brandProfile.logoDataUrl) {
                // Extract the base64 data and mime type from the data URL
                const logoMatch = options.brandProfile.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);
                if (logoMatch) {
                    const [, mimeType, base64Data] = logoMatch;
                    generationParts.push({
                        inlineData: {
                            data: base64Data,
                            mimeType: mimeType
                        }
                    });
                    // Update the prompt to reference the provided logo
                    const logoPrompt = `\n\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;
                    generationParts[1] = prompt + logoPrompt;
                } else {}
            }
            // Use DALL-E 3 for actual image generation (Gemini doesn't generate images)
            const dallePrompt = `Create a professional ${options.visualStyle || 'modern'} social media design for ${options.platform} with:

EXACT TEXT TO INCLUDE:
- Main headline: "${exactHeadline}"
- Subheadline: "${exactSubheadline}"
- Call-to-action: "${exactCTA}"

DESIGN REQUIREMENTS:
- ${options.aspectRatio || '1:1'} aspect ratio
- Professional ${options.visualStyle || 'modern'} style
- Brand colors: ${options.brandProfile.primaryColor || '#3B82F6'} and ${options.brandProfile.accentColor || '#10B981'}
- Background: ${options.brandProfile.backgroundColor || '#F8FAFC'}
- High-quality, readable text
- Suitable for ${options.platform} social media
${options.includePeopleInDesigns ? '- Include people/customers when appropriate' : ''}

Create a visually appealing design that clearly displays all the specified text.`;
            console.log('🎨 Generating image with DALL-E 3...');
            const response = await openai.images.generate({
                model: "dall-e-3",
                prompt: dallePrompt,
                n: 1,
                size: "1024x1024",
                quality: "standard",
                style: "natural"
            });
            const imageUrl = response.data[0]?.url;
            if (!imageUrl) {
                throw new Error('No image URL returned from DALL-E 3');
            }
            console.log('✅ Image generated successfully with DALL-E 3');
            return {
                imageUrl
            };
        } catch (error) {
            lastError = error;
            console.error(`🚨 IMAGE GENERATION ATTEMPT ${attempt} FAILED:`, error.message);
            console.error('Error details:', error);
            if (attempt === maxRetries) {
                break;
            }
            const waitTime = Math.pow(2, attempt) * 1000;
            console.log(`⏳ Retrying in ${waitTime}ms...`);
            await new Promise((resolve)=>setTimeout(resolve, waitTime));
        }
    }
    throw new Error(`Revo 2.0 generation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);
}
/**
 * Generate caption and hashtags with AI-powered contextual generation
 */ async function generateSophisticatedContent(options, concept) {
    const { businessType: businessType1, platform, brandProfile: brandProfile1 } = options;
    try {
        console.log('Starting sophisticated content generation...');
        // Step 1: Create business profile for analysis
        const businessProfile = {
            businessName: brandProfile1.businessName || businessType1,
            businessType: businessType1,
            location: brandProfile1.location || 'Local area',
            targetAudience: brandProfile1.targetAudience || 'General audience',
            brandVoice: brandProfile1.brandVoice || 'professional',
            services: brandProfile1.services || [],
            keyFeatures: brandProfile1.keyFeatures || [],
            competitiveAdvantages: brandProfile1.competitiveAdvantages || []
        };
        console.log('Business profile created:', businessProfile);
        // Step 2: Generate business intelligence and strategic content plan
        const contentPlan = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StrategicContentPlanner"].generateBusinessSpecificContent(businessType1, businessProfile.businessName, businessProfile.location, {
            experience: 'Experienced',
            expertise: Array.isArray(businessProfile.keyFeatures) ? businessProfile.keyFeatures.join(', ') : businessProfile.keyFeatures || '',
            services: Array.isArray(businessProfile.services) ? businessProfile.services.join(', ') : businessProfile.services || '',
            location: businessProfile.location,
            targetAudience: businessProfile.targetAudience
        }, platform, 'awareness');
        // Step 3: Generate business-specific headline
        const businessHeadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificHeadline"])(businessType1, businessProfile.businessName, businessProfile.location, {
            experience: 'Experienced',
            expertise: Array.isArray(businessProfile.keyFeatures) ? businessProfile.keyFeatures.join(', ') : businessProfile.keyFeatures || '',
            services: Array.isArray(businessProfile.services) ? businessProfile.services.join(', ') : businessProfile.services || ''
        }, platform, 'awareness', {
            currentTrends: concept.catchwords || []
        }, contentPlan);
        // Step 4: Generate business-specific subheadline
        const businessSubheadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificSubheadline"])(businessType1, businessProfile.businessName, businessProfile.location, {
            experience: 'Experienced',
            expertise: Array.isArray(businessProfile.keyFeatures) ? businessProfile.keyFeatures.join(', ') : businessProfile.keyFeatures || '',
            services: Array.isArray(businessProfile.services) ? businessProfile.services.join(', ') : businessProfile.services || ''
        }, businessHeadline.headline, 'awareness', {
            currentTrends: concept.catchwords || []
        }, contentPlan);
        // Step 5: Generate business-specific caption
        const businessCaption = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBusinessSpecificCaption"])(businessType1, businessProfile.businessName, businessProfile.location, {
            experience: 'Experienced',
            expertise: Array.isArray(businessProfile.keyFeatures) ? businessProfile.keyFeatures.join(', ') : businessProfile.keyFeatures || '',
            services: Array.isArray(businessProfile.services) ? businessProfile.services.join(', ') : businessProfile.services || ''
        }, platform, 'awareness', {
            currentTrends: concept.catchwords || []
        }, contentPlan);
        console.log('Generated headline:', businessHeadline);
        console.log('Generated subheadline:', businessSubheadline);
        console.log('Generated caption:', businessCaption);
        // Step 6: Generate hashtags using advanced content generator
        const advancedGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AdvancedContentGenerator"]();
        const hashtags = await advancedGenerator.generateStrategicHashtags(businessProfile, {
            businessIntelligence: contentPlan
        }, platform);
        console.log('Generated hashtags:', hashtags);
        const result = {
            headline: businessHeadline.headline,
            subheadline: businessSubheadline.subheadline,
            caption: businessCaption.caption,
            cta: businessCaption.callToAction,
            hashtags: hashtags,
            businessIntelligence: {
                contentGoal: contentPlan.strategy?.goal || 'Build awareness',
                businessStrengths: contentPlan.businessStrengths || [],
                marketOpportunities: contentPlan.marketOpportunities || [],
                customerPainPoints: contentPlan.customerPainPoints || [],
                valueProposition: contentPlan.valueProposition || 'Quality service',
                localRelevance: contentPlan.localRelevance || {}
            }
        };
        // Step 7: Refine content to remove repetition and improve quality
        const refinedResult = refineContentQuality(result, businessProfile);
        console.log('Final sophisticated content result:', refinedResult);
        return refinedResult;
    } catch (error) {
        console.error('🚨 SOPHISTICATED CONTENT GENERATION FAILED - USING FALLBACK:', error);
        console.error('Error stack:', error.stack);
        // Fallback to simpler generation
        const fallbackResult = await generateSimpleFallback(options, concept);
        const fallbackContent = {
            headline: concept.catchwords?.[0] || `${businessType1} Excellence`,
            subheadline: concept.concept || `Experience quality ${businessType1.toLowerCase()} services`,
            caption: fallbackResult.caption,
            cta: `Visit ${brandProfile1.businessName || businessType1} today!`,
            hashtags: fallbackResult.hashtags,
            businessIntelligence: {
                contentGoal: 'Build awareness',
                businessStrengths: [
                    'Quality service',
                    'Professional expertise'
                ],
                marketOpportunities: [
                    'Local market growth'
                ],
                customerPainPoints: [],
                valueProposition: 'Quality service and expertise',
                localRelevance: {}
            }
        };
        console.log('⚠️ USING FALLBACK CONTENT:');
        console.log(`   Headline: "${fallbackContent.headline}"`);
        console.log(`   Subheadline: "${fallbackContent.subheadline}"`);
        console.log(`   CTA: "${fallbackContent.cta}"`);
        console.log('');
        return fallbackContent;
    }
}
/**
 * Refine content quality by removing repetition and improving readability
 */ function refineContentQuality(content, businessProfile) {
    const locationName = businessProfile.location.split(',')[0].trim(); // Get just the city name
    const businessName = businessProfile.businessName;
    // Function to reduce repetitive location mentions and improve readability
    const reduceLocationRepetition = (text)=>{
        const fullLocation = businessProfile.location;
        const locationVariations = [
            fullLocation,
            locationName
        ];
        let refined = text;
        // Count and replace location mentions more intelligently
        locationVariations.forEach((location)=>{
            const regex = new RegExp(`\\b${location.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
            const matches = refined.match(regex);
            if (matches && matches.length > 1) {
                let count = 0;
                refined = refined.replace(regex, (match)=>{
                    count++;
                    if (count === 1) return match; // Keep first mention
                    if (count === 2) return 'locally'; // Replace second with 'locally'
                    return ''; // Remove subsequent mentions
                });
            }
        });
        // Additional cleanup for common repetitive patterns
        refined = refined.replace(/\b(\w+)\s+\1\b/gi, '$1') // Remove duplicate adjacent words
        .replace(/\s*,\s*,/g, ',') // Fix double commas
        .replace(/\s+/g, ' ') // Clean up extra spaces
        .replace(/\.\s*\./g, '.') // Fix double periods
        .trim();
        return refined;
    };
    // Function to improve business strengths formatting
    const cleanBusinessStrengths = (strengths)=>{
        return strengths.map((strength)=>{
            // Remove redundant words and improve phrasing
            return strength.replace(/(\w+)\s+\1/gi, '$1') // Remove duplicate adjacent words
            .replace(/experienced\s+years\s+of\s+experience/gi, 'experienced professionals').replace(/5\+\s*years\s+years/gi, 'years of experience').replace(/years\s+of\s+experience\s+years/gi, 'years of experience').replace(/specialized in\s+/gi, '').replace(/quality\s+quality/gi, 'quality').replace(/fresh\s+fresh/gi, 'fresh').replace(/authentic\s+authentic/gi, 'authentic').replace(/\s+/g, ' ').trim();
        }).filter((strength)=>strength.length > 0 && !strength.match(/^(experienced|quality|fresh)$/i));
    };
    // Function to make content more concise and impactful
    const makeConcise = (text)=>{
        return text// Remove verbose phrases
        .replace(/over \d+ [^,]+ residents consistently rate/gi, 'locals love').replace(/our unparalleled/gi, 'our').replace(/truly satisfying experience/gi, 'great experience').replace(/locals rave about our/gi, 'known for our').replace(/the genuine community focus we bring to/gi, 'community-focused').replace(/make every visit[^.]+/gi, 'ensure quality service')// Simplify repetitive business descriptions
        .replace(/from intimate fine dining to grand private events/gi, 'for all occasions').replace(/and exceptional service/gi, '').replace(/quality and tradition/gi, 'excellence')// Fix grammar and redundancy issues
        .replace(/the locally market/gi, 'the local market').replace(/locally sourced ingredients and authentic recipes that stand out/gi, 'locally sourced, authentic recipes').replace(/our community focus means/gi, 'our focus ensures').replace(/local relevance and unparalleled quality at competitive prices/gi, 'quality at great prices').replace(/we're here year-round to provide that amazing experience you've been looking for/gi, 'delivering exceptional experiences year-round')// Clean up extra words
        .replace(/\s+/g, ' ').trim();
    };
    // Function to enforce word limits
    const enforceWordLimits = (text, maxWords)=>{
        const words = text.trim().split(/\s+/);
        if (words.length > maxWords) {
            return words.slice(0, maxWords).join(' ');
        }
        return text;
    };
    return {
        ...content,
        headline: enforceWordLimits(makeConcise(reduceLocationRepetition(content.headline)), 6),
        subheadline: enforceWordLimits(makeConcise(reduceLocationRepetition(content.subheadline)), 25),
        caption: makeConcise(reduceLocationRepetition(content.caption)),
        cta: makeConcise(reduceLocationRepetition(content.cta)),
        businessIntelligence: {
            ...content.businessIntelligence,
            businessStrengths: cleanBusinessStrengths(content.businessIntelligence.businessStrengths || [])
        }
    };
}
/**
 * Simple fallback content generation
 */ async function generateSimpleFallback(options, concept) {
    const { businessType: businessType1, platform, brandProfile: brandProfile1 } = options;
    const prompt = `Create engaging ${platform} content for a ${businessType1} business.

Business Details:
- Name: ${brandProfile1.businessName || businessType1}
- Type: ${businessType1}
- Location: ${brandProfile1.location || 'Local area'}
- Concept: ${concept.concept}
- Catchwords: ${concept.catchwords.join(', ')}

Create:
1. A catchy, engaging caption (2-3 sentences max) that incorporates the concept and catchwords naturally
2. 10 highly relevant, specific hashtags that are:
   - Specific to this business and location
   - Mix of business-specific, location-based, industry-relevant, and platform-optimized
   - Avoid generic hashtags like #business, #professional, #quality, #local
   - Discoverable and relevant to the target audience
   - Appropriate for ${platform}

Make the content authentic, locally relevant, and engaging for ${platform}.

Format as JSON:
{
  "caption": "Your engaging caption here",
  "hashtags": ["#SpecificHashtag1", "#LocationBasedHashtag", "#IndustryRelevant", ...]
}`;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 600
    });
    try {
        let responseContent = response.choices[0].message.content || '{}';
        // Remove markdown code blocks if present
        responseContent = responseContent.replace(/```json\s*|\s*```/g, '').trim();
        const result = JSON.parse(responseContent);
        // Validate the response
        if (result.caption && Array.isArray(result.hashtags) && result.hashtags.length > 0) {
            return {
                caption: result.caption,
                hashtags: result.hashtags.slice(0, 10) // Ensure max 10 hashtags
            };
        }
    } catch (error) {
        console.warn('Failed to parse AI content response:', error);
    }
    // Fallback with contextual generation (no hardcoded placeholders)
    return generateContextualFallback(businessType1, brandProfile1, platform, concept);
}
/**
 * Generate contextual fallback content without hardcoded placeholders
 */ function generateContextualFallback(businessType1, brandProfile1, platform, concept) {
    const businessName = brandProfile1.businessName || businessType1;
    const location = brandProfile1.location || 'your area';
    // Generate contextual caption
    const caption = `${concept.catchwords[0] || 'Discover'} what makes ${businessName} special in ${location}! ${concept.concept || 'Experience the difference with our exceptional service.'}`;
    // Generate contextual hashtags
    const hashtags = [];
    // Business-specific
    hashtags.push(`#${businessName.replace(/\s+/g, '')}`);
    hashtags.push(`#${businessType1.replace(/\s+/g, '')}Business`);
    // Location-based
    const locationParts = location.split(',').map((part)=>part.trim());
    locationParts.forEach((part)=>{
        if (part.length > 2) {
            hashtags.push(`#${part.replace(/\s+/g, '')}`);
        }
    });
    // Platform-specific contextual
    if (platform === 'instagram') {
        hashtags.push('#InstagramContent', '#VisualStory');
    } else if (platform === 'facebook') {
        hashtags.push('#FacebookPost', '#CommunityBusiness');
    } else if (platform === 'linkedin') {
        hashtags.push('#LinkedInBusiness', '#ProfessionalServices');
    } else if (platform === 'tiktok') {
        hashtags.push('#TikTokBusiness', '#CreativeContent');
    }
    // Add current date context
    const today = new Date();
    const dayName = today.toLocaleDateString('en-US', {
        weekday: 'long'
    });
    hashtags.push(`#${dayName}Vibes`);
    return {
        caption,
        hashtags: [
            ...new Set(hashtags)
        ].slice(0, 10) // Remove duplicates and limit to 10
    };
}
async function testRevo20Availability() {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_2_0_MODEL
        });
        const response = await model.generateContent('Create a simple test image with the text "Revo 2.0 Test" on a modern gradient background');
        const parts = response.candidates?.[0]?.content?.parts || [];
        let hasImage = false;
        for (const part of parts){
            if (part.inlineData) {
                hasImage = true;
            }
        }
        if (hasImage) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        return false;
    }
}
}}),
"[project]/src/app/api/generate-revo-2.0/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Generation API Route
 * Uses Gemini 2.5 Flash Image Preview for next-generation content creation
 */ __turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-2.0-service.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { businessType, platform, brandProfile, visualStyle, imageText, aspectRatio, includePeopleInDesigns, useLocalLanguage } = body;
        // Validate required fields
        if (!businessType || !platform || !brandProfile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Missing required fields: businessType, platform, brandProfile'
            }, {
                status: 400
            });
        }
        console.log('Revo 2.0 Enhanced generation request:', {
            businessType,
            platform,
            visualStyle: visualStyle || 'modern',
            aspectRatio: aspectRatio || '1:1'
        });
        // Generate content with Revo 2.0
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$2$2e$0$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateWithRevo20"])({
            businessType,
            platform,
            visualStyle: visualStyle || 'modern',
            imageText: imageText || `${brandProfile.businessName || businessType} - Premium Content`,
            brandProfile,
            aspectRatio,
            includePeopleInDesigns,
            useLocalLanguage
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            imageUrl: result.imageUrl,
            model: result.model,
            qualityScore: result.qualityScore,
            processingTime: result.processingTime,
            enhancementsApplied: result.enhancementsApplied,
            headline: result.headline,
            subheadline: result.subheadline,
            caption: result.caption,
            cta: result.cta,
            hashtags: result.hashtags,
            businessIntelligence: result.businessIntelligence,
            message: 'Revo 2.0 Enhanced content generated successfully'
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Revo 2.0 generation failed'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message: 'Revo 2.0 Enhanced Generation API',
        description: 'Use POST method to generate sophisticated content with Revo 2.0',
        requiredFields: [
            'businessType',
            'platform',
            'brandProfile'
        ],
        optionalFields: [
            'visualStyle',
            'imageText',
            'aspectRatio'
        ],
        model: 'Gemini 2.5 Flash Image Preview with Business Intelligence',
        version: '2.0.1',
        features: [
            'Business-specific headlines',
            'Strategic subheadlines',
            'Sophisticated captions',
            'Compelling CTAs',
            'AI-powered hashtags',
            'Business intelligence analysis'
        ]
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__eddc771e._.js.map