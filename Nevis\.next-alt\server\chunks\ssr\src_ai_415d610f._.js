module.exports = {

"[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Capabilities Configuration
 * Defines what each model version can do
 */ __turbopack_context__.s({
    "capabilityMatrix": (()=>capabilityMatrix),
    "featureAvailability": (()=>featureAvailability),
    "getCapabilityLevel": (()=>getCapabilityLevel),
    "getMaxQualityForPlatform": (()=>getMaxQualityForPlatform),
    "getModelsByFeature": (()=>getModelsByFeature),
    "getPlatformCapabilities": (()=>getPlatformCapabilities),
    "getSupportedAspectRatios": (()=>getSupportedAspectRatios),
    "hasCapability": (()=>hasCapability),
    "hasFeature": (()=>hasFeature),
    "modelCapabilities": (()=>modelCapabilities),
    "platformCapabilities": (()=>platformCapabilities)
});
const modelCapabilities = {
    'revo-1.0': {
        // Enhanced stable model capabilities with Gemini 2.5 Flash Image Preview
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: false,
        aspectRatios: [
            '1:1'
        ],
        maxQuality: 9,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        perfectTextRendering: true,
        highResolution: true // NEW: 2048x2048 support
    },
    'revo-1.5': {
        // Enhanced model with advanced features
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16'
        ],
        maxQuality: 8,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true // Real-time context and trends
    },
    'revo-2.0': {
        // Premium Next-Gen AI model
        contentGeneration: true,
        designGeneration: true,
        videoGeneration: false,
        enhancedFeatures: true,
        artifactSupport: true,
        aspectRatios: [
            '1:1',
            '16:9',
            '9:16',
            '4:3',
            '3:4'
        ],
        maxQuality: 10,
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        advancedPrompting: true,
        brandConsistency: true,
        realTimeContext: true,
        characterConsistency: true,
        intelligentEditing: true,
        multimodalReasoning: true // NEW: Advanced visual context understanding
    }
};
const capabilityMatrix = {
    contentGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    designGeneration: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'enhanced',
        'revo-2.0': 'premium'
    },
    videoGeneration: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'none'
    },
    artifactSupport: {
        'revo-1.0': 'none',
        'revo-1.5': 'full',
        'revo-2.0': 'premium'
    },
    brandConsistency: {
        'revo-1.0': 'enhanced',
        'revo-1.5': 'advanced',
        'revo-2.0': 'perfect'
    },
    characterConsistency: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    },
    intelligentEditing: {
        'revo-1.0': 'none',
        'revo-1.5': 'none',
        'revo-2.0': 'advanced'
    }
};
const featureAvailability = {
    // Content features
    hashtagGeneration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    catchyWords: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    subheadlines: [
        'revo-1.5',
        'revo-2.0'
    ],
    callToAction: [
        'revo-1.5',
        'revo-2.0'
    ],
    contentVariants: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Design features
    logoIntegration: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    brandColors: [
        'revo-1.0',
        'revo-1.5',
        'revo-2.0'
    ],
    designExamples: [
        'revo-1.5',
        'revo-2.0'
    ],
    textOverlay: [
        'revo-1.5',
        'revo-2.0'
    ],
    multipleAspectRatios: [
        'revo-1.5',
        'revo-2.0'
    ],
    // Advanced features
    realTimeContext: [
        'revo-1.5',
        'revo-2.0'
    ],
    trendingTopics: [
        'revo-1.5',
        'revo-2.0'
    ],
    marketIntelligence: [
        'revo-1.5',
        'revo-2.0'
    ],
    competitorAnalysis: [
        'revo-2.0'
    ],
    // Revo 2.0 exclusive features
    characterConsistency: [
        'revo-2.0'
    ],
    intelligentEditing: [
        'revo-2.0'
    ],
    inpainting: [
        'revo-2.0'
    ],
    outpainting: [
        'revo-2.0'
    ],
    multimodalReasoning: [
        'revo-2.0'
    ],
    // Revo 1.0 enhanced features (NEW with Gemini 2.5 Flash Image Preview)
    perfectTextRendering: [
        'revo-1.0',
        'revo-2.0'
    ],
    highResolution: [
        'revo-1.0',
        'revo-2.0'
    ],
    // Artifact features
    artifactReference: [
        'revo-1.5'
    ],
    exactUseArtifacts: [
        'revo-1.5'
    ],
    textOverlayArtifacts: [
        'revo-1.5'
    ]
};
const platformCapabilities = {
    Instagram: {
        'revo-1.0': {
            aspectRatios: [
                '1:1'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'hashtags'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '1:1',
                '9:16'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'hashtags',
                'stories',
                'reels-ready'
            ]
        }
    },
    Facebook: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'page-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'page-posts',
                'stories'
            ]
        }
    },
    Twitter: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'tweets'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'tweets',
                'threads'
            ]
        }
    },
    LinkedIn: {
        'revo-1.0': {
            aspectRatios: [
                '16:9'
            ],
            maxQuality: 7,
            features: [
                'basic-design',
                'professional-posts'
            ]
        },
        'revo-1.5': {
            aspectRatios: [
                '16:9',
                '1:1'
            ],
            maxQuality: 8,
            features: [
                'enhanced-design',
                'professional-posts',
                'articles'
            ]
        }
    }
};
function hasCapability(modelId, capability) {
    return modelCapabilities[modelId][capability];
}
function getCapabilityLevel(modelId, capability) {
    return capabilityMatrix[capability][modelId];
}
function hasFeature(modelId, feature) {
    return featureAvailability[feature].includes(modelId);
}
function getModelsByFeature(feature) {
    return [
        ...featureAvailability[feature]
    ];
}
function getPlatformCapabilities(modelId, platform) {
    return platformCapabilities[platform]?.[modelId] || null;
}
function getMaxQualityForPlatform(modelId, platform) {
    const platformCaps = getPlatformCapabilities(modelId, platform);
    return platformCaps?.maxQuality || modelCapabilities[modelId].maxQuality;
}
function getSupportedAspectRatios(modelId, platform) {
    if (platform) {
        const platformCaps = getPlatformCapabilities(modelId, platform);
        return platformCaps?.aspectRatios ? [
            ...platformCaps.aspectRatios
        ] : [
            ...modelCapabilities[modelId].aspectRatios
        ];
    }
    return [
        ...modelCapabilities[modelId].aspectRatios
    ];
}
}}),
"[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Pricing Configuration
 * Defines credit costs and pricing tiers for each model
 */ __turbopack_context__.s({
    "creditPackages": (()=>creditPackages),
    "getAllPricing": (()=>getAllPricing),
    "getCheapestModel": (()=>getCheapestModel),
    "getModelPricing": (()=>getModelPricing),
    "getModelsByTier": (()=>getModelsByTier),
    "getMostExpensiveModel": (()=>getMostExpensiveModel),
    "modelPricing": (()=>modelPricing),
    "pricingDisplay": (()=>pricingDisplay),
    "pricingTiers": (()=>pricingTiers),
    "usageCalculations": (()=>usageCalculations)
});
const modelPricing = {
    'revo-1.0': {
        creditsPerGeneration: 1.5,
        creditsPerDesign: 1.5,
        creditsPerVideo: 0,
        tier: 'enhanced' // Upgraded from basic
    },
    'revo-1.5': {
        creditsPerGeneration: 2,
        creditsPerDesign: 2,
        creditsPerVideo: 0,
        tier: 'premium'
    },
    'revo-2.0': {
        creditsPerGeneration: 5,
        creditsPerDesign: 5,
        creditsPerVideo: 0,
        tier: 'premium'
    }
};
const pricingTiers = {
    basic: {
        name: 'Basic',
        description: 'Reliable and cost-effective',
        maxCreditsPerGeneration: 2,
        features: [
            'Standard quality generation',
            'Basic brand consistency',
            'Core platform support',
            'Standard processing speed'
        ],
        recommendedFor: [
            'Small businesses',
            'Personal brands',
            'Budget-conscious users',
            'Basic content needs'
        ]
    },
    premium: {
        name: 'Premium',
        description: 'Enhanced features and quality',
        maxCreditsPerGeneration: 10,
        features: [
            'Enhanced quality generation',
            'Advanced brand consistency',
            'Full platform support',
            'Artifact integration',
            'Real-time context',
            'Trending topics',
            'Multiple aspect ratios'
        ],
        recommendedFor: [
            'Growing businesses',
            'Marketing agencies',
            'Content creators',
            'Professional brands'
        ]
    },
    enterprise: {
        name: 'Enterprise',
        description: 'Maximum quality and features',
        maxCreditsPerGeneration: 20,
        features: [
            'Premium quality generation',
            '4K resolution support',
            'Perfect text rendering',
            'Advanced style controls',
            'Priority processing',
            'Dedicated support',
            'Custom integrations'
        ],
        recommendedFor: [
            'Large enterprises',
            'Premium brands',
            'High-volume users',
            'Quality-focused campaigns'
        ]
    }
};
const creditPackages = {
    starter: {
        name: 'Starter Pack',
        credits: 50,
        price: 9.99,
        pricePerCredit: 0.20,
        bestFor: 'revo-1.0',
        estimatedGenerations: {
            'revo-1.0': 50,
            'revo-1.5': 25,
            'imagen-4': 5
        }
    },
    professional: {
        name: 'Professional Pack',
        credits: 200,
        price: 29.99,
        pricePerCredit: 0.15,
        bestFor: 'revo-1.5',
        estimatedGenerations: {
            'revo-1.0': 200,
            'revo-1.5': 100,
            'imagen-4': 20
        }
    },
    business: {
        name: 'Business Pack',
        credits: 500,
        price: 59.99,
        pricePerCredit: 0.12,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 500,
            'revo-1.5': 250,
            'imagen-4': 50
        }
    },
    enterprise: {
        name: 'Enterprise Pack',
        credits: 1000,
        price: 99.99,
        pricePerCredit: 0.10,
        bestFor: 'imagen-4',
        estimatedGenerations: {
            'revo-1.0': 1000,
            'revo-1.5': 500,
            'revo-2.0': 200,
            'imagen-4': 100
        }
    }
};
const usageCalculations = {
    // Calculate cost for a specific generation request
    calculateGenerationCost (modelId, type = 'content') {
        const pricing = modelPricing[modelId];
        switch(type){
            case 'content':
                return pricing.creditsPerGeneration;
            case 'design':
                return pricing.creditsPerDesign;
            case 'video':
                return pricing.creditsPerVideo || 0;
            default:
                return pricing.creditsPerGeneration;
        }
    },
    // Calculate total cost for multiple generations
    calculateBatchCost (requests) {
        return requests.reduce((total, request)=>{
            return total + this.calculateGenerationCost(request.modelId, request.type);
        }, 0);
    },
    // Estimate monthly cost based on usage patterns
    estimateMonthlyCost (usage) {
        const pricing = modelPricing[usage.modelId];
        const dailyCost = usage.generationsPerDay * pricing.creditsPerGeneration + usage.designsPerDay * pricing.creditsPerDesign + (usage.videosPerDay || 0) * (pricing.creditsPerVideo || 0);
        const monthlyCost = dailyCost * 30;
        // Recommend package based on monthly cost
        let recommendedPackage = 'starter';
        if (monthlyCost > 400) recommendedPackage = 'enterprise';
        else if (monthlyCost > 150) recommendedPackage = 'business';
        else if (monthlyCost > 50) recommendedPackage = 'professional';
        return {
            dailyCost,
            monthlyCost,
            recommendedPackage
        };
    },
    // Check if user has enough credits for a request
    canAfford (userCredits, modelId, type = 'content') {
        const cost = this.calculateGenerationCost(modelId, type);
        return userCredits >= cost;
    },
    // Get the best model within budget
    getBestModelForBudget (availableCredits, type = 'content') {
        const affordableModels = [];
        for (const [modelId, pricing] of Object.entries(modelPricing)){
            const cost = type === 'content' ? pricing.creditsPerGeneration : type === 'design' ? pricing.creditsPerDesign : pricing.creditsPerVideo || 0;
            if (cost <= availableCredits && cost > 0) {
                affordableModels.push(modelId);
            }
        }
        // Sort by quality (higher credit cost usually means higher quality)
        return affordableModels.sort((a, b)=>{
            const costA = this.calculateGenerationCost(a, type);
            const costB = this.calculateGenerationCost(b, type);
            return costB - costA; // Descending order (highest quality first)
        });
    }
};
const pricingDisplay = {
    // Format credits for display
    formatCredits (credits) {
        if (credits >= 1000) {
            return `${(credits / 1000).toFixed(1)}K`;
        }
        return credits.toString();
    },
    // Format price for display
    formatPrice (price) {
        return `$${price.toFixed(2)}`;
    },
    // Get pricing tier info
    getTierInfo (modelId) {
        const pricing = modelPricing[modelId];
        return pricingTiers[pricing.tier];
    },
    // Get cost comparison between models
    compareCosts (modelA, modelB) {
        const costA = modelPricing[modelA].creditsPerGeneration;
        const costB = modelPricing[modelB].creditsPerGeneration;
        const difference = Math.abs(costA - costB);
        const percentDifference = (difference / Math.min(costA, costB) * 100).toFixed(0);
        return {
            cheaper: costA < costB ? modelA : modelB,
            moreExpensive: costA > costB ? modelA : modelB,
            difference,
            percentDifference: `${percentDifference}%`,
            ratio: `${Math.max(costA, costB)}:${Math.min(costA, costB)}`
        };
    },
    // Get value proposition for each model
    getValueProposition (modelId) {
        const pricing = modelPricing[modelId];
        const tierInfo = pricingTiers[pricing.tier];
        return {
            model: modelId,
            tier: pricing.tier,
            creditsPerGeneration: pricing.creditsPerGeneration,
            valueScore: tierInfo.features.length / pricing.creditsPerGeneration,
            description: tierInfo.description,
            bestFor: tierInfo.recommendedFor
        };
    }
};
function getModelPricing(modelId) {
    return modelPricing[modelId];
}
function getAllPricing() {
    return modelPricing;
}
function getModelsByTier(tier) {
    return Object.entries(modelPricing).filter(([_, pricing])=>pricing.tier === tier).map(([modelId])=>modelId);
}
function getCheapestModel() {
    return Object.entries(modelPricing).reduce((cheapest, [modelId, pricing])=>{
        const currentCheapest = modelPricing[cheapest];
        return pricing.creditsPerGeneration < currentCheapest.creditsPerGeneration ? modelId : cheapest;
    }, 'revo-1.0');
}
function getMostExpensiveModel() {
    return Object.entries(modelPricing).reduce((mostExpensive, [modelId, pricing])=>{
        const currentMostExpensive = modelPricing[mostExpensive];
        return pricing.creditsPerGeneration > currentMostExpensive.creditsPerGeneration ? modelId : mostExpensive;
    }, 'revo-1.0');
}
}}),
"[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Model Configurations
 * Centralized configuration for all Revo model versions
 */ __turbopack_context__.s({
    "compareModels": (()=>compareModels),
    "getAllModelConfigs": (()=>getAllModelConfigs),
    "getLatestModels": (()=>getLatestModels),
    "getModelConfig": (()=>getModelConfig),
    "getModelForBudget": (()=>getModelForBudget),
    "getModelsByStatus": (()=>getModelsByStatus),
    "getModelsByTier": (()=>getModelsByTier),
    "getRecommendedModel": (()=>getRecommendedModel),
    "modelConfigs": (()=>modelConfigs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/capabilities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/pricing.ts [app-rsc] (ecmascript)");
;
;
// Base configurations for different AI services
const baseConfigs = {
    'gemini-2.0': {
        aiService: 'gemini-2.0',
        fallbackServices: [
            'gemini-2.5',
            'openai'
        ],
        maxRetries: 3,
        timeout: 30000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 85,
            enhancementLevel: 5
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 2048,
            topP: 0.9,
            topK: 40
        }
    },
    'gemini-2.5': {
        aiService: 'gemini-2.5',
        fallbackServices: [
            'gemini-2.0',
            'openai'
        ],
        maxRetries: 2,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 90,
            enhancementLevel: 7
        },
        promptSettings: {
            temperature: 0.8,
            maxTokens: 4096,
            topP: 0.95,
            topK: 50
        }
    },
    'openai': {
        aiService: 'openai',
        fallbackServices: [
            'gemini-2.5',
            'gemini-2.0'
        ],
        maxRetries: 3,
        timeout: 35000,
        qualitySettings: {
            imageResolution: '1024x1024',
            compressionLevel: 88,
            enhancementLevel: 6
        },
        promptSettings: {
            temperature: 0.7,
            maxTokens: 3000,
            topP: 0.9
        }
    },
    'gemini-2.5-flash-image': {
        aiService: 'gemini-2.5-flash-image',
        fallbackServices: [
            'imagen-4',
            'gemini-2.5'
        ],
        maxRetries: 3,
        timeout: 45000,
        qualitySettings: {
            imageResolution: '2048x2048',
            compressionLevel: 95,
            enhancementLevel: 8 // Reduced for cleaner designs (was 10)
        },
        promptSettings: {
            temperature: 0.4,
            maxTokens: 4096,
            topP: 0.7,
            topK: 30 // Fewer creative choices for consistency (was 60)
        }
    }
};
const modelConfigs = {
    'revo-1.0': {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        version: '1.0.0',
        description: 'Standard Model - Stable Foundation (Enhanced with Gemini 2.5 Flash Image Preview)',
        longDescription: 'Reliable AI engine with proven performance, now powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering. Perfect for consistent, high-quality content generation with 1:1 aspect ratio images and core features.',
        icon: 'Zap',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.0'],
        features: [
            'Enhanced AI Engine with Gemini 2.5 Flash Image Preview',
            '1:1 Images with High Resolution',
            'Core Features',
            'Proven Performance',
            'Multi-platform Support',
            'Enhanced Brand Consistency',
            'Perfect Text Rendering',
            'High-Resolution Output (2048x2048)'
        ],
        releaseDate: '2024-01-15',
        lastUpdated: '2025-01-27'
    },
    'revo-1.5': {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        version: '1.5.0',
        description: 'Enhanced Model - Advanced Features',
        longDescription: 'Advanced AI engine with superior capabilities. Enhanced content generation algorithms, superior quality control, and professional design generation with improved brand integration.',
        icon: 'Sparkles',
        badge: 'Enhanced',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-1.5'],
        config: {
            ...baseConfigs['gemini-2.5'],
            qualitySettings: {
                ...baseConfigs['gemini-2.5'].qualitySettings,
                enhancementLevel: 8
            }
        },
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-1.5'],
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations',
            'Professional Templates',
            'Advanced Brand Integration',
            'Real-time Context',
            'Trending Topics Integration'
        ],
        releaseDate: '2024-06-20',
        lastUpdated: '2024-12-15'
    },
    'revo-2.0': {
        id: 'revo-2.0',
        name: 'Revo 2.0',
        version: '2.0.0',
        description: 'Next-Gen Model - Advanced AI with native image generation',
        longDescription: 'Revolutionary AI model featuring native image generation, character consistency, intelligent editing, and multimodal reasoning for premium content creation.',
        icon: 'Rocket',
        badge: 'Next-Gen',
        badgeVariant: 'default',
        status: 'enhanced',
        capabilities: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$capabilities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelCapabilities"]['revo-2.0'],
        config: baseConfigs['gemini-2.5-flash-image'],
        pricing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$pricing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modelPricing"]['revo-2.0'],
        features: [
            'Next-Gen AI Engine',
            'Native Image Generation',
            'Character Consistency',
            'Intelligent Editing',
            'Inpainting & Outpainting',
            'Multimodal Reasoning',
            'All Aspect Ratios',
            'Perfect Brand Consistency'
        ],
        releaseDate: '2025-01-27',
        lastUpdated: '2025-01-27'
    }
};
function getModelConfig(modelId) {
    const config = modelConfigs[modelId];
    if (!config) {
        throw new Error(`Model configuration not found for: ${modelId}`);
    }
    return config;
}
function getAllModelConfigs() {
    return Object.values(modelConfigs);
}
function getModelsByStatus(status) {
    return getAllModelConfigs().filter((model)=>model.status === status);
}
function getModelsByTier(tier) {
    return getAllModelConfigs().filter((model)=>model.pricing.tier === tier);
}
function getLatestModels() {
    return getAllModelConfigs().sort((a, b)=>new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()).slice(0, 3);
}
function getRecommendedModel() {
    // Return Revo 1.5 as the recommended balanced option
    return modelConfigs['revo-1.5'];
}
function getModelForBudget(maxCredits) {
    return getAllModelConfigs().filter((model)=>model.pricing.creditsPerGeneration <= maxCredits).sort((a, b)=>a.pricing.creditsPerGeneration - b.pricing.creditsPerGeneration);
}
function compareModels(modelA, modelB) {
    const configA = getModelConfig(modelA);
    const configB = getModelConfig(modelB);
    return {
        quality: {
            a: configA.capabilities.maxQuality,
            b: configB.capabilities.maxQuality,
            winner: configA.capabilities.maxQuality > configB.capabilities.maxQuality ? modelA : modelB
        },
        cost: {
            a: configA.pricing.creditsPerGeneration,
            b: configB.pricing.creditsPerGeneration,
            winner: configA.pricing.creditsPerGeneration < configB.pricing.creditsPerGeneration ? modelA : modelB
        },
        features: {
            a: configA.features.length,
            b: configB.features.length,
            winner: configA.features.length > configB.features.length ? modelA : modelB
        },
        status: {
            a: configA.status,
            b: configB.status,
            recommendation: configA.status === 'stable' || configB.status === 'stable' ? configA.status === 'stable' ? modelA : modelB : modelA
        }
    };
}
}}),
"[project]/src/ai/models/versions/revo-1.0/config.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Configuration
 * Model-specific configuration and constants
 */ __turbopack_context__.s({
    "getPerformanceBenchmark": (()=>getPerformanceBenchmark),
    "getPromptTemplate": (()=>getPromptTemplate),
    "getRevo10Config": (()=>getRevo10Config),
    "isFeatureEnabled": (()=>isFeatureEnabled),
    "revo10Config": (()=>revo10Config),
    "revo10Constants": (()=>revo10Constants),
    "revo10Metrics": (()=>revo10Metrics),
    "revo10Prompts": (()=>revo10Prompts),
    "revo10Validation": (()=>revo10Validation),
    "shouldAlert": (()=>shouldAlert),
    "validateRequest": (()=>validateRequest)
});
const revo10Config = {
    aiService: 'gemini-2.5-flash-image-preview',
    fallbackServices: [
        'gemini-2.5',
        'gemini-2.0',
        'openai'
    ],
    maxRetries: 3,
    timeout: 45000,
    qualitySettings: {
        imageResolution: '2048x2048',
        compressionLevel: 95,
        enhancementLevel: 7 // Reduced for cleaner designs (was 10)
    },
    promptSettings: {
        temperature: 0.3,
        maxTokens: 4096,
        topP: 0.6,
        topK: 25 // Fewer creative choices for consistency (was 100)
    }
};
const revo10Constants = {
    // Model identification
    MODEL_ID: 'revo-1.0',
    MODEL_NAME: 'Revo 1.0',
    MODEL_VERSION: '1.0.0',
    // Capabilities
    SUPPORTED_ASPECT_RATIOS: [
        '1:1'
    ],
    SUPPORTED_PLATFORMS: [
        'Instagram',
        'Facebook',
        'Twitter',
        'LinkedIn'
    ],
    MAX_QUALITY_SCORE: 9.0,
    // Performance targets
    TARGET_PROCESSING_TIME: 30000,
    TARGET_SUCCESS_RATE: 0.97,
    TARGET_QUALITY_SCORE: 8.5,
    // Resource limits
    MAX_CONTENT_LENGTH: 2000,
    MAX_HASHTAGS: 15,
    MAX_IMAGE_SIZE: 2048,
    // Feature flags
    FEATURES: {
        ARTIFACTS_SUPPORT: false,
        REAL_TIME_CONTEXT: true,
        TRENDING_TOPICS: true,
        MULTIPLE_ASPECT_RATIOS: false,
        VIDEO_GENERATION: false,
        ADVANCED_PROMPTING: true,
        ENHANCED_DESIGN: true,
        PERFECT_TEXT_RENDERING: true,
        HIGH_RESOLUTION: true,
        NATIVE_IMAGE_GENERATION: true // NEW: Direct image generation capability
    },
    // Pricing
    CREDITS_PER_GENERATION: 1.5,
    CREDITS_PER_DESIGN: 1.5,
    TIER: 'enhanced' // Upgraded from basic
};
const revo10Prompts = {
    // Content generation prompts
    CONTENT_SYSTEM_PROMPT: `You are an elite social media content strategist for Revo 1.0, powered by Gemini 2.5 Flash Image Preview for enhanced quality and perfect text rendering.
Your expertise spans viral content creation, brand storytelling, and audience engagement optimization.

Your capabilities include:
- **Deep Local Market Knowledge**: Understanding of local business environment, competition, and market trends
- **Industry-Specific Insights**: 20+ years of experience across various industries
- **Community Connection**: Deep understanding of local culture, values, and business needs
- **Market Dynamics**: Knowledge of local economic conditions, competitive landscape, and business opportunities

When creating content:
- Write like a real industry professional, not AI
- Use local market insights and industry knowledge naturally
- Incorporate local phrases and community language authentically
- Share real, relatable stories that connect with the local community
- Position as the local expert with deep industry knowledge
- Focus on local relevance and community impact
- Use conversational, human language that builds trust and authority

Your mission is to create content that sounds like it's written by a real industry professional with deep local expertise - not generic marketing copy. Every post should demonstrate your local market knowledge and industry authority.`,
    CONTENT_USER_PROMPT_TEMPLATE: `Generate social media content for:
Business: {businessName}
Type: {businessType}
Platform: {platform}
Tone: {writingTone}
Location: {location}

Brand Information:
- Primary Color: {primaryColor}
- Visual Style: {visualStyle}
- Target Audience: {targetAudience}
- Services: {services}
- Key Features: {keyFeatures}
- Competitive Advantages: {competitiveAdvantages}
- Content Themes: {contentThemes}

Requirements:
- Create engaging, professional content that reflects the business's unique value proposition
- Incorporate services and key features naturally into the content
- Highlight competitive advantages when relevant
- Include relevant hashtags (5-15) that align with content themes
- Generate catchy words for the image that capture the brand essence
- Ensure platform-appropriate formatting and tone
- Maintain brand consistency with colors and visual style
- Use only clean, readable text (no special characters, symbols, or garbled text)
- Generate content in proper English with correct spelling and grammar
- Avoid any corrupted or unreadable character sequences
- Make the content location-specific and culturally relevant when appropriate`,
    // Design generation prompts
    DESIGN_SYSTEM_PROMPT: `You are a world-class graphic designer who creates 7 completely different types of social media designs, each with their own unique visual language and style. You have deep expertise in multiple industries and understand how to create designs that rival the best brands in the world.

Your design philosophy:
- Create designs that are VISUALLY APPEALING and engaging
- Each design type should look completely different from the others
- Focus on style-specific authenticity (watercolor should look like real watercolor, meme-style should look like a real meme)
- Make designs that look like something from successful, popular brands
- **CRITICAL: Make designs look like a human designer created them, not AI**
- **CRITICAL: Each design type must have its own unique visual identity**
- **IMPORTANT: Keep local/cultural elements subtle and natural, not overwhelming**
- **NEW: Understand the business industry and create designs that rival world-class brands**

When creating designs:
- Start with the specific style requirements for the chosen design type
- Use style-appropriate elements, colors, and typography
- Focus on visual impact and engagement
- Create designs people want to interact with
- Use current design trends that work for the specific style
- **MOST IMPORTANT: Make each design type genuinely unique and different**
- **SECOND MOST IMPORTANT: Make it look human-made, not AI-generated**
- **NEW: Study industry benchmarks and create designs that match world-class quality**

CRITICAL: You are a human designer who understands that each design type should look completely different. A watercolor quote should look nothing like a meme-style post. A split photo collage should look nothing like a branded poster. Each style must have its own visual language and approach.

**HUMAN DESIGN APPROACH:**
- Add slight imperfections and asymmetry (humans aren't perfect)
- Use natural spacing and proportions
- Avoid overly symmetrical, geometric perfection
- Make it feel organic and handcrafted
- Focus on the design style first, local elements second

**INDUSTRY INTELLIGENCE INTEGRATION:**
- Study and understand the business industry context
- Learn from world-class brands in the same industry
- Incorporate industry-specific design trends and best practices
- Create designs that feel authentic to the industry while being creative
- Match the quality and sophistication of industry leaders

Focus on creating designs that are both beautiful and engaging while maintaining the unique characteristics of each design type, looking genuinely human-made, and rivaling world-class industry standards.`,
    DESIGN_USER_PROMPT_TEMPLATE: `Create a world-class, human-made 2048x2048 social media design that people will actually want to engage with:

BUSINESS CONTEXT:
- Business: {businessName}
- Industry: {businessType}
- Platform: {platform}
- Target Message: {imageText}

DESIGN REQUIREMENTS:
- Create a design that's VISUALLY APPEALING and engaging
- Focus on the specific design style requirements
- Make it look like a human designer created it, not AI
- Keep local/cultural elements subtle and natural, not overwhelming
- Focus on the design style first, local elements second
- **NEW: Study industry benchmarks and create designs that rival world-class brands**

KEY DESIGN PRINCIPLES:
1. **HUMAN-MADE FIRST** - Make it look like a skilled human designer created it
2. **STYLE AUTHENTICITY** - Follow the specific style requirements exactly
3. **VISUAL UNIQUENESS** - Make this look completely different from other design types
4. **NATURAL IMPERFECTIONS** - Add slight asymmetry, natural spacing, organic feel
5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative
6. **INDUSTRY EXCELLENCE** - Match the quality of world-class brands in the industry

INDUSTRY INTELLIGENCE INTEGRATION:
- Study and understand the {businessType} industry context
- Learn from world-class brands in the same industry
- Incorporate industry-specific design trends and best practices
- Create designs that feel authentic to the industry while being creative
- Match the quality and sophistication of industry leaders

WHAT TO AVOID:
- Overly perfect, symmetrical, AI-generated looking designs
- Forced cultural elements that feel stereotypical
- Generic, template-like designs
- Overly complex or busy layouts
- Poor contrast or readability
- Designs that don't match industry quality standards

WHAT TO INCLUDE:
- Style-specific elements that match the chosen design type
- Unique visual approach for the specific style
- Subtle local touches that feel natural, not forced
- Human imperfections - slight asymmetry, natural spacing, organic feel
- Style-appropriate typography and layout
- Industry-specific design elements and quality standards

TECHNICAL REQUIREMENTS:
- Resolution: 2048x2048 pixels
- Format: Square (1:1)
- Text must be readable on mobile
- Logo integration should look natural

🎨 GOAL: Create a world-class design that looks genuinely human-made, follows the specific style requirements, feels unique and engaging, and rivals the quality of industry leaders. Focus on the design style first, add subtle local touches naturally, make it look like a skilled human designer created it, and ensure it matches world-class industry standards.`,
    // Error messages
    ERROR_MESSAGES: {
        GENERATION_FAILED: 'Revo 1.0 content generation failed. Please try again.',
        DESIGN_FAILED: 'Revo 1.0 design generation failed. Please try again.',
        INVALID_REQUEST: 'Invalid request for Revo 1.0. Please check your parameters.',
        SERVICE_UNAVAILABLE: 'Revo 1.0 service is temporarily unavailable.',
        TIMEOUT: 'Revo 1.0 generation timed out. Please try again.',
        QUOTA_EXCEEDED: 'Revo 1.0 usage quota exceeded. Please upgrade your plan.'
    }
};
const revo10Validation = {
    // Content validation
    content: {
        minLength: 10,
        maxLength: 2000,
        requiredFields: [
            'businessType',
            'platform',
            'businessName'
        ],
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Design validation
    design: {
        requiredFields: [
            'businessType',
            'platform',
            'visualStyle',
            'imageText'
        ],
        supportedAspectRatios: revo10Constants.SUPPORTED_ASPECT_RATIOS,
        maxImageTextLength: 200,
        supportedPlatforms: revo10Constants.SUPPORTED_PLATFORMS
    },
    // Brand profile validation
    brandProfile: {
        requiredFields: [
            'businessType',
            'businessName'
        ],
        optionalFields: [
            'location',
            'writingTone',
            'visualStyle',
            'primaryColor',
            'accentColor',
            'backgroundColor',
            'logoDataUrl',
            'targetAudience'
        ]
    }
};
const revo10Metrics = {
    // Expected performance benchmarks
    BENCHMARKS: {
        processingTime: {
            target: 30000,
            acceptable: 40000,
            maximum: 60000 // 60 seconds (upgraded from 45s)
        },
        qualityScore: {
            minimum: 7.0,
            target: 8.5,
            maximum: 9.0 // Upgraded from 7.5
        },
        successRate: {
            minimum: 0.95,
            target: 0.97,
            maximum: 0.99 // Upgraded from 98%
        }
    },
    // Monitoring thresholds
    ALERTS: {
        processingTimeHigh: 45000,
        qualityScoreLow: 7.5,
        successRateLow: 0.95,
        errorRateHigh: 0.05 // Alert if error rate exceeds 5% (upgraded from 8%)
    }
};
function getRevo10Config() {
    return revo10Config;
}
function isFeatureEnabled(feature) {
    return revo10Constants.FEATURES[feature];
}
function getPromptTemplate(type, templateName) {
    if (type === 'content') {
        return revo10Prompts.CONTENT_USER_PROMPT_TEMPLATE;
    } else if (type === 'design') {
        return revo10Prompts.DESIGN_USER_PROMPT_TEMPLATE;
    }
    throw new Error(`Unknown prompt template: ${type}/${templateName}`);
}
function validateRequest(type, request) {
    const errors = [];
    const validation = type === 'content' ? revo10Validation.content : revo10Validation.design;
    // Check required fields
    for (const field of validation.requiredFields){
        if (!request[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    // Check platform support
    if (request.platform && !validation.supportedPlatforms.includes(request.platform)) {
        errors.push(`Unsupported platform: ${request.platform}`);
    }
    // Design-specific validation
    if (type === 'design') {
        if (request.imageText && request.imageText.length > revo10Validation.design.maxImageTextLength) {
            errors.push(`Image text too long (max ${revo10Validation.design.maxImageTextLength} characters)`);
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function getPerformanceBenchmark(metric) {
    return revo10Metrics.BENCHMARKS[metric];
}
function shouldAlert(metric, value) {
    const alerts = revo10Metrics.ALERTS;
    switch(metric){
        case 'processingTime':
            return value > alerts.processingTimeHigh;
        case 'qualityScore':
            return value < alerts.qualityScoreLow;
        case 'successRate':
            return value < alerts.successRateLow;
        case 'errorRate':
            return value > alerts.errorRateHigh;
        default:
            return false;
    }
}
}}),
"[project]/src/ai/content-performance-analyzer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Content Performance Analyzer
 * Benchmarks against industry standards and continuously improves content quality
 */ __turbopack_context__.s({
    "ContentPerformanceAnalyzer": (()=>ContentPerformanceAnalyzer),
    "performanceAnalyzer": (()=>performanceAnalyzer)
});
class ContentPerformanceAnalyzer {
    industryBenchmarks = new Map();
    performanceHistory = new Map();
    contentPatterns = new Map();
    constructor(){
        this.initializeIndustryBenchmarks();
        this.initializeSuccessPatterns();
    }
    /**
   * Initialize industry benchmarks for different business types
   */ initializeIndustryBenchmarks() {
        const benchmarks = {
            restaurant: [
                {
                    businessType: 'restaurant',
                    platform: 'instagram',
                    averageEngagement: 3.2,
                    topPerformerEngagement: 8.5,
                    averageReach: 15.4,
                    bestPractices: [
                        'High-quality food photography',
                        'Behind-the-scenes content',
                        'Customer testimonials',
                        'Seasonal menu highlights',
                        'Local ingredient stories'
                    ],
                    commonMistakes: [
                        'Poor lighting in photos',
                        'Generic captions',
                        'Inconsistent posting',
                        'Ignoring local trends',
                        'Over-promotional content'
                    ],
                    successPatterns: [
                        'Food close-ups with natural lighting',
                        'Stories about ingredients and preparation',
                        'Customer experience highlights',
                        'Local community involvement',
                        'Seasonal and trending ingredients'
                    ]
                },
                {
                    businessType: 'restaurant',
                    platform: 'facebook',
                    averageEngagement: 2.8,
                    topPerformerEngagement: 6.2,
                    averageReach: 12.1,
                    bestPractices: [
                        'Community engagement',
                        'Event announcements',
                        'Customer reviews sharing',
                        'Local partnerships',
                        'Family-friendly content'
                    ],
                    commonMistakes: [
                        'Posting only promotional content',
                        'Ignoring customer comments',
                        'Not leveraging local events',
                        'Generic stock photos',
                        'Inconsistent brand voice'
                    ],
                    successPatterns: [
                        'Community event participation',
                        'Customer story sharing',
                        'Local ingredient sourcing stories',
                        'Family dining experiences',
                        'Seasonal celebration posts'
                    ]
                }
            ],
            retail: [
                {
                    businessType: 'retail',
                    platform: 'instagram',
                    averageEngagement: 2.9,
                    topPerformerEngagement: 7.8,
                    averageReach: 18.2,
                    bestPractices: [
                        'Product styling and flat lays',
                        'User-generated content',
                        'Trend-focused content',
                        'Behind-the-brand stories',
                        'Seasonal collections'
                    ],
                    commonMistakes: [
                        'Product-only posts',
                        'Poor product photography',
                        'Ignoring fashion trends',
                        'Not showcasing versatility',
                        'Generic product descriptions'
                    ],
                    successPatterns: [
                        'Lifestyle product integration',
                        'Trend-forward styling',
                        'Customer styling examples',
                        'Seasonal fashion guides',
                        'Sustainable fashion stories'
                    ]
                }
            ],
            fitness: [
                {
                    businessType: 'fitness',
                    platform: 'instagram',
                    averageEngagement: 4.1,
                    topPerformerEngagement: 9.3,
                    averageReach: 16.7,
                    bestPractices: [
                        'Transformation stories',
                        'Workout demonstrations',
                        'Motivational content',
                        'Community challenges',
                        'Expert tips and advice'
                    ],
                    commonMistakes: [
                        'Intimidating content for beginners',
                        'Only showing perfect bodies',
                        'Generic motivational quotes',
                        'Not addressing different fitness levels',
                        'Ignoring mental health aspects'
                    ],
                    successPatterns: [
                        'Inclusive fitness content',
                        'Real transformation journeys',
                        'Beginner-friendly workouts',
                        'Mental health and fitness connection',
                        'Community support stories'
                    ]
                }
            ],
            beauty: [
                {
                    businessType: 'beauty',
                    platform: 'instagram',
                    averageEngagement: 3.7,
                    topPerformerEngagement: 8.9,
                    averageReach: 14.3,
                    bestPractices: [
                        'Before/after transformations',
                        'Tutorial content',
                        'Product demonstrations',
                        'Skin care education',
                        'Inclusive beauty content'
                    ],
                    commonMistakes: [
                        'Over-filtered photos',
                        'Not showing diverse skin types',
                        'Generic beauty tips',
                        'Ignoring skincare science',
                        'Not addressing common concerns'
                    ],
                    successPatterns: [
                        'Natural beauty enhancement',
                        'Educational skincare content',
                        'Diverse model representation',
                        'Seasonal beauty tips',
                        'Self-care and confidence building'
                    ]
                }
            ]
        };
        Object.entries(benchmarks).forEach(([businessType, benchmarkArray])=>{
            this.industryBenchmarks.set(businessType, benchmarkArray);
        });
    }
    /**
   * Initialize success patterns for content optimization
   */ initializeSuccessPatterns() {
        const patterns = {
            'high-engagement-headlines': [
                'Question-based headlines that spark curiosity',
                'Numbers and statistics in headlines',
                'Emotional trigger words',
                'Local references and community connection',
                'Trending topic integration',
                'Problem-solution format',
                'Exclusive or limited-time offers',
                'Behind-the-scenes insights'
            ],
            'effective-captions': [
                'Storytelling approach',
                'Personal anecdotes and experiences',
                'Call-to-action integration',
                'Community questions and engagement',
                'Educational value provision',
                'Emotional connection building',
                'Local culture and language integration',
                'Trending hashtag utilization'
            ],
            'compelling-ctas': [
                'Action-oriented language',
                'Urgency and scarcity elements',
                'Clear value proposition',
                'Personalized messaging',
                'Community-focused calls',
                'Experience-based invitations',
                'Social proof integration',
                'Local relevance emphasis'
            ]
        };
        Object.entries(patterns).forEach(([category, patternList])=>{
            this.contentPatterns.set(category, patternList);
        });
    }
    /**
   * Analyze content performance against industry benchmarks
   */ analyzePerformance(post, profile, actualMetrics) {
        const benchmarks = this.industryBenchmarks.get(profile.businessType) || [];
        const platformBenchmark = benchmarks.find((b)=>b.platform === post.platform);
        if (!platformBenchmark) {
            return this.generateGenericOptimization();
        }
        // Analyze content elements
        const headlineAnalysis = this.analyzeHeadline(post.headline, platformBenchmark);
        const captionAnalysis = this.analyzeCaption(post.caption, platformBenchmark);
        const ctaAnalysis = this.analyzeCTA(post.cta, platformBenchmark);
        const hashtagAnalysis = this.analyzeHashtags(post.hashtags, platformBenchmark);
        // Generate optimization recommendations
        const optimization = {
            strengths: [
                ...headlineAnalysis.strengths,
                ...captionAnalysis.strengths,
                ...ctaAnalysis.strengths,
                ...hashtagAnalysis.strengths
            ],
            improvements: [
                ...headlineAnalysis.improvements,
                ...captionAnalysis.improvements,
                ...ctaAnalysis.improvements,
                ...hashtagAnalysis.improvements
            ],
            recommendations: this.generateRecommendations(platformBenchmark, profile),
            nextIterationFocus: this.identifyNextIterationFocus(platformBenchmark, profile),
            competitiveAdvantages: this.identifyCompetitiveAdvantages(platformBenchmark, profile)
        };
        return optimization;
    }
    /**
   * Analyze headline effectiveness
   */ analyzeHeadline(headline, benchmark) {
        const strengths = [];
        const improvements = [];
        // Check for success patterns
        const successPatterns = this.contentPatterns.get('high-engagement-headlines') || [];
        if (headline.includes('?')) {
            strengths.push('Uses question format to engage audience');
        } else {
            improvements.push('Consider using questions to increase engagement');
        }
        if (/\d+/.test(headline)) {
            strengths.push('Includes numbers for credibility');
        } else {
            improvements.push('Consider adding specific numbers or statistics');
        }
        if (headline.length > 10 && headline.length < 60) {
            strengths.push('Optimal headline length for platform');
        } else {
            improvements.push('Adjust headline length for better readability');
        }
        // Check for emotional triggers
        const emotionalWords = [
            'amazing',
            'incredible',
            'exclusive',
            'limited',
            'secret',
            'proven'
        ];
        if (emotionalWords.some((word)=>headline.toLowerCase().includes(word))) {
            strengths.push('Uses emotional trigger words');
        } else {
            improvements.push('Add emotional trigger words to increase appeal');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze caption effectiveness
   */ analyzeCaption(caption, benchmark) {
        const strengths = [];
        const improvements = [];
        if (caption.length > 50 && caption.length < 300) {
            strengths.push('Optimal caption length for engagement');
        } else {
            improvements.push('Adjust caption length for better engagement');
        }
        // Check for storytelling elements
        if (caption.includes('we') || caption.includes('our') || caption.includes('story')) {
            strengths.push('Uses storytelling approach');
        } else {
            improvements.push('Add storytelling elements to create connection');
        }
        // Check for community engagement
        if (caption.includes('?') || caption.includes('comment') || caption.includes('share')) {
            strengths.push('Encourages community engagement');
        } else {
            improvements.push('Add questions or engagement prompts');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze CTA effectiveness
   */ analyzeCTA(cta, benchmark) {
        const strengths = [];
        const improvements = [];
        const actionWords = [
            'visit',
            'book',
            'call',
            'order',
            'try',
            'discover',
            'experience'
        ];
        if (actionWords.some((word)=>cta.toLowerCase().includes(word))) {
            strengths.push('Uses strong action words');
        } else {
            improvements.push('Use more compelling action words');
        }
        if (cta.length > 5 && cta.length < 50) {
            strengths.push('Appropriate CTA length');
        } else {
            improvements.push('Optimize CTA length for clarity');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Analyze hashtag strategy
   */ analyzeHashtags(hashtags, benchmark) {
        const strengths = [];
        const improvements = [];
        if (hashtags.length >= 5 && hashtags.length <= 10) {
            strengths.push('Optimal number of hashtags');
        } else {
            improvements.push('Adjust hashtag count for better reach');
        }
        // Check for mix of popular and niche hashtags
        const hasPopular = hashtags.some((tag)=>tag.includes('trending') || tag.includes('viral'));
        const hasNiche = hashtags.some((tag)=>tag.length > 15);
        if (hasPopular && hasNiche) {
            strengths.push('Good mix of popular and niche hashtags');
        } else {
            improvements.push('Balance popular and niche hashtags for better reach');
        }
        return {
            strengths,
            improvements
        };
    }
    /**
   * Generate specific recommendations based on benchmarks
   */ generateRecommendations(benchmark, profile) {
        const recommendations = [];
        // Add benchmark-specific recommendations
        benchmark.bestPractices.forEach((practice)=>{
            recommendations.push(`Implement: ${practice}`);
        });
        // Add business-specific recommendations
        recommendations.push(`Leverage ${profile.location} local culture and events`);
        recommendations.push(`Highlight unique selling points: ${profile.uniqueSellingPoints.join(', ')}`);
        recommendations.push(`Target ${profile.targetAudience} with personalized messaging`);
        return recommendations.slice(0, 8); // Limit to top 8 recommendations
    }
    /**
   * Identify focus areas for next iteration
   */ identifyNextIterationFocus(benchmark, profile) {
        const focus = [];
        // Focus on top-performing patterns
        benchmark.successPatterns.forEach((pattern)=>{
            focus.push(`Enhance: ${pattern}`);
        });
        // Avoid common mistakes
        benchmark.commonMistakes.forEach((mistake)=>{
            focus.push(`Avoid: ${mistake}`);
        });
        return focus.slice(0, 6); // Limit to top 6 focus areas
    }
    /**
   * Identify competitive advantages
   */ identifyCompetitiveAdvantages(benchmark, profile) {
        const advantages = [];
        // Business-specific advantages
        profile.uniqueSellingPoints.forEach((usp)=>{
            advantages.push(`Unique advantage: ${usp}`);
        });
        // Location-based advantages
        advantages.push(`Local market expertise in ${profile.location}`);
        advantages.push(`Community connection and trust`);
        advantages.push(`Cultural understanding and relevance`);
        return advantages.slice(0, 5); // Limit to top 5 advantages
    }
    /**
   * Generate generic optimization for unknown business types
   */ generateGenericOptimization() {
        return {
            strengths: [
                'Content created with business context'
            ],
            improvements: [
                'Add industry-specific benchmarks',
                'Enhance local relevance'
            ],
            recommendations: [
                'Research industry best practices',
                'Analyze competitor content'
            ],
            nextIterationFocus: [
                'Improve targeting',
                'Enhance engagement'
            ],
            competitiveAdvantages: [
                'Personalized approach',
                'Local market focus'
            ]
        };
    }
    /**
   * Track performance over time for continuous improvement
   */ trackPerformance(businessName, metrics) {
        const history = this.performanceHistory.get(businessName) || [];
        history.push(metrics);
        this.performanceHistory.set(businessName, history.slice(-20)); // Keep last 20 records
    }
    /**
   * Get performance trends for a business
   */ getPerformanceTrends(businessName) {
        const history = this.performanceHistory.get(businessName) || [];
        if (history.length < 2) {
            return {
                trend: 'stable',
                averageScore: history[0]?.overallScore || 0,
                bestPerformingContent: []
            };
        }
        const recent = history.slice(-5);
        const older = history.slice(-10, -5);
        const recentAvg = recent.reduce((sum, m)=>sum + m.overallScore, 0) / recent.length;
        const olderAvg = older.reduce((sum, m)=>sum + m.overallScore, 0) / older.length;
        let trend = 'stable';
        if (recentAvg > olderAvg + 0.5) trend = 'improving';
        else if (recentAvg < olderAvg - 0.5) trend = 'declining';
        const averageScore = history.reduce((sum, m)=>sum + m.overallScore, 0) / history.length;
        return {
            trend,
            averageScore,
            bestPerformingContent: [
                'High-engagement headlines',
                'Community-focused content',
                'Local relevance'
            ]
        };
    }
}
const performanceAnalyzer = new ContentPerformanceAnalyzer();
}}),
"[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 - Enhanced AI Service with Gemini 2.5 Flash Image Preview
 * Upgraded from Gemini 2.0 to provide enhanced quality and perfect text rendering
 */ __turbopack_context__.s({
    "checkRevo10Health": (()=>checkRevo10Health),
    "generateRevo10Content": (()=>generateRevo10Content),
    "generateRevo10Design": (()=>generateRevo10Design),
    "generateRevo10Image": (()=>generateRevo10Image),
    "getRevo10ServiceInfo": (()=>getRevo10ServiceInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/advanced-content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$content$2d$performance$2d$analyzer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/content-performance-analyzer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/trending-content-enhancer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/creative-enhancement.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
// Advanced features integration (simplified for now)
// TODO: Import advanced features from Revo 1.5 when available
// Helper functions for advanced design generation
function getBusinessDesignDNA(businessType) {
    const designDNA = {
        'restaurant': 'Warm, appetizing colors (reds, oranges, warm yellows). High-quality food photography. Cozy, inviting atmosphere. Emphasis on freshness and quality.',
        'technology': 'Clean, modern aesthetics. Blue and tech-forward color schemes. Geometric shapes. Innovation and reliability focus. Professional typography.',
        'healthcare': 'Clean, trustworthy design. Calming blues and greens. Professional imagery. Focus on care and expertise. Accessible design principles.',
        'fitness': 'Dynamic, energetic design. Bold colors and strong contrasts. Action-oriented imagery. Motivational messaging. Strong, athletic typography.',
        'finance': 'Professional, trustworthy design. Conservative color palette. Clean lines. Security and stability focus. Authoritative typography.',
        'education': 'Approachable, inspiring design. Bright, optimistic colors. Clear information hierarchy. Growth and learning focus. Readable typography.',
        'retail': 'Attractive, commercial design. Brand-focused colors. Product-centric imagery. Sales and value focus. Eye-catching typography.',
        'real estate': 'Luxurious, aspirational design. Sophisticated color palette. High-quality property imagery. Trust and expertise focus. Elegant typography.',
        'default': 'Professional, modern design. Balanced color scheme. Clean, contemporary aesthetics. Quality and reliability focus. Professional typography.'
    };
    return designDNA[businessType.toLowerCase()] || designDNA['default'];
}
// NEW: 7 truly different design types for dynamic social media feeds
function getHumanDesignVariations(seed) {
    const variations = [
        {
            style: 'Watercolor Quotes',
            layout: 'Soft, artistic watercolor background with elegant typography overlay',
            composition: 'Centered or asymmetrical text with flowing watercolor elements',
            mood: 'Artistic, elegant, inspirational',
            elements: 'Watercolor textures, elegant fonts, soft color transitions, artistic backgrounds',
            description: 'Create a design that looks like an artist painted it with watercolors, with flowing, organic shapes and elegant typography that feels handcrafted and artistic.'
        },
        {
            style: 'Split Photo Collages',
            layout: 'Two or three photo sections with text overlay on one section',
            composition: 'Grid-based photo layout with text integrated naturally',
            mood: 'Modern, dynamic, photo-driven',
            elements: 'Photo sections, clean grid lines, integrated text, modern typography',
            description: 'Design with a clean grid layout that splits the image into photo sections, with text naturally integrated into one section. Think Instagram grid meets modern magazine layout.'
        },
        {
            style: 'Meme-Style Posts',
            layout: 'Bold, punchy text with minimal background and high contrast',
            composition: 'Centered text with simple, impactful background',
            mood: 'Fun, viral, shareable',
            elements: 'Bold typography, simple backgrounds, high contrast, meme-like simplicity',
            description: 'Create a design that feels like a viral meme - bold, simple text with minimal background elements. Think Twitter meme aesthetics but professional.'
        },
        {
            style: 'Polaroid-Style Testimonials',
            layout: 'Polaroid frame with photo area and handwritten-style text',
            composition: 'Polaroid border with content inside, vintage feel',
            mood: 'Authentic, personal, nostalgic',
            elements: 'Polaroid borders, vintage textures, handwritten fonts, authentic feel',
            description: 'Design that looks like a vintage Polaroid photo with a white border, containing either a photo area or text that feels handwritten and personal.'
        },
        {
            style: 'Minimal Photo-Driven Promos',
            layout: 'Large photo background with minimal text overlay',
            composition: 'Photo as hero element with subtle text placement',
            mood: 'Clean, premium, photo-focused',
            elements: 'Large photos, minimal text, clean typography, lots of white space',
            description: 'Create a design where a beautiful photo is the main focus, with minimal, elegant text overlay. Think high-end magazine or premium brand aesthetics.'
        },
        {
            style: 'Mixed-Media Artistic Posts',
            layout: 'Layered design with multiple textures, patterns, and artistic elements',
            composition: 'Complex layering with artistic elements and modern typography',
            mood: 'Creative, artistic, unique',
            elements: 'Multiple textures, artistic patterns, layered elements, creative typography',
            description: 'Design with multiple artistic layers - think digital art meets graphic design. Include textures, patterns, and creative elements that feel like modern digital art.'
        },
        {
            style: 'Branded Posters (Current Style)',
            layout: 'Illustration-heavy design with brand elements and structured layout',
            composition: 'Illustrated background with organized text and brand placement',
            mood: 'Professional, branded, consistent',
            elements: 'Illustrations, brand colors, structured typography, consistent branding',
            description: 'The current style - professional illustrated posters with brand consistency. Use when you need to maintain strong brand identity.'
        }
    ];
    return variations[seed % variations.length];
}
// NEW: Simple, clean design instructions for better visual appeal
function injectHumanImperfections(designPrompt, seed) {
    const instructions = [
        'Use natural spacing and proportions that feel balanced and appealing',
        'Create a design that feels modern and current, not overly perfect',
        'Focus on visual appeal and what people actually like to see',
        'Make it look like something from a successful, popular brand'
    ];
    const selectedInstruction = instructions[seed % instructions.length];
    return designPrompt + `

🎨 DESIGN FOCUS:
${selectedInstruction}

Keep the design simple, clean, and visually appealing.`;
}
// NEW: Simple creative approach for better designs
function injectCreativeRebellion(designPrompt, seed) {
    const approaches = [
        `DESIGN APPROACH: Create a design that's visually appealing and engaging. Focus on what looks good and what people want to engage with.`,
        `CREATIVE STYLE: Use a clean, modern approach that feels current and appealing. Make it look like something people would actually want to interact with.`,
        `VISUAL APPROACH: Design with a focus on visual appeal and engagement. Create something that stands out and looks good.`,
        `DESIGN PHILOSOPHY: Focus on creating designs that people want to engage with - clean, modern, and visually appealing.`
    ];
    const selectedApproach = approaches[seed % approaches.length];
    return designPrompt + `

🎨 DESIGN APPROACH:
${selectedApproach}

Focus on creating designs that are visually appealing and engaging.`;
}
// NEW: Simple design guidelines for better results
function addArtisticConstraints(designPrompt, seed) {
    const constraints = [
        `DESIGN FOCUS: Create a design that's visually appealing and engaging. Focus on clean, modern aesthetics that people actually like.`,
        `COMPOSITION APPROACH: Use simple, clean layouts that are easy to read and understand. Less is more.`,
        `CREATIVE ELEMENTS: Add modern, contemporary elements that make the design look good and engaging.`,
        `VISUAL BALANCE: Create a design that feels balanced and appealing, with elements that work together well.`,
        `DESIGN STYLE: Use a clean, modern approach that feels current and professional. Focus on visual appeal.`,
        `CREATIVE APPROACH: Design with a focus on what people actually want to see and engage with.`,
        `VISUAL HIERARCHY: Create clear visual hierarchy that guides the eye naturally through the design.`,
        `DESIGN PRINCIPLES: Focus on creating a design that's both beautiful and engaging. Make it look good.`
    ];
    const selectedConstraint = constraints[seed % constraints.length];
    return designPrompt + `

🎨 DESIGN GUIDELINE:
${selectedConstraint}

Keep the design simple, clean, and visually appealing.`;
}
function getPlatformOptimization(platform) {
    const optimizations = {
        'instagram': `
- Mobile-first design with bold, clear elements
- High contrast colors that pop on small screens
- Text minimum 24px equivalent for readability
- Center important elements for square crop compatibility
- Thumb-stopping power for fast scroll feeds
- Logo: Bottom right corner or naturally integrated`,
        'linkedin': `
- Professional, business-appropriate aesthetics
- Corporate design standards and clean look
- Clear value proposition for business audience
- Professional photography and imagery
- Thought leadership positioning
- Logo: Prominent placement for brand authority`,
        'facebook': `
- Desktop and mobile viewing optimization
- Engagement and shareability focus
- Clear value proposition in visual hierarchy
- Authentic, relatable imagery
- Community-focused design elements
- Logo: Top left or bottom right corner`,
        'twitter': `
- Rapid consumption and high engagement design
- Bold, contrasting colors for timeline visibility
- Minimal, impactful text elements
- Trending visual styles integration
- Real-time relevance
- Logo: Small, subtle placement`,
        'default': `
- Cross-platform compatibility
- Universal appeal and accessibility
- Balanced design for multiple contexts
- Professional appearance across devices
- Logo: Flexible placement based on composition`
    };
    return optimizations[platform.toLowerCase()] || optimizations['default'];
}
// Advanced real-time context gathering for Revo 1.0 (enhanced version)
async function gatherRealTimeContext(businessType, location, platform) {
    const context = {
        trends: [],
        weather: null,
        events: [],
        news: [],
        localLanguage: {},
        climateInsights: {},
        trendingTopics: [],
        timeContext: {
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            month: new Date().toLocaleDateString('en-US', {
                month: 'long'
            }),
            season: getSeason(),
            timeOfDay: getTimeOfDay()
        }
    };
    try {
        // Generate contextual trends based on business type and location
        context.trends = generateContextualTrends(businessType, location);
        // Generate weather-appropriate content suggestions
        context.weather = generateWeatherContext(location);
        // Generate local business opportunities
        context.events = generateLocalOpportunities(businessType, location);
        // NEW: Enhanced local language and cultural context
        context.localLanguage = generateLocalLanguageContext(location);
        // NEW: Advanced climate insights for business relevance
        context.climateInsights = generateClimateInsights(location, businessType);
        // NEW: Real-time trending topics (simulated for now, can be enhanced with actual APIs)
        context.trendingTopics = generateTrendingTopics(businessType, location, platform);
        // NEW: Local news and market insights
        context.news = generateLocalNewsContext(businessType, location);
        return context;
    } catch (error) {
        return context; // Return partial context
    }
}
// Advanced design enhancement functions
function shouldIncludePeopleInDesign(businessType, location, visualStyle) {
    const peopleBusinessTypes = [
        'restaurant',
        'fitness',
        'healthcare',
        'education',
        'retail',
        'hospitality',
        'beauty',
        'wellness',
        'consulting',
        'coaching',
        'real estate',
        'finance',
        'technology',
        'marketing',
        'events',
        'photography',
        'fashion'
    ];
    return peopleBusinessTypes.some((type)=>businessType.toLowerCase().includes(type) || visualStyle === 'lifestyle' || visualStyle === 'authentic');
}
function getLocalCulturalContext(location) {
    const culturalContexts = {
        'kenya': 'Subtle Kenyan elements: warm earth tones, natural textures, community feel',
        'nigeria': 'Subtle Nigerian elements: vibrant accents, natural patterns, community warmth',
        'south africa': 'Subtle South African elements: diverse representation, natural colors, community spirit',
        'ghana': 'Subtle Ghanaian elements: warm tones, natural textures, community connection',
        'uganda': 'Subtle Ugandan elements: natural colors, community feel, authentic representation',
        'tanzania': 'Subtle Tanzanian elements: coastal influences, natural textures, community warmth',
        'ethiopia': 'Subtle Ethiopian elements: natural earth tones, community connection, authentic feel',
        'rwanda': 'Subtle Rwandan elements: natural colors, community spirit, authentic representation',
        'default': 'Natural, authentic feel with subtle local elements that feel genuine, not forced'
    };
    const locationKey = location.toLowerCase();
    for (const [key, context] of Object.entries(culturalContexts)){
        if (locationKey.includes(key)) {
            return context;
        }
    }
    return culturalContexts['default'];
}
function getDesignVariations(seed) {
    const variations = [
        {
            style: 'Modern Minimalist',
            layout: 'Clean geometric layout with plenty of white space, single focal point, minimal text overlay',
            composition: 'Centered composition with asymmetrical elements, bold typography hierarchy',
            mood: 'Professional, clean, sophisticated',
            elements: 'Subtle gradients, clean lines, modern sans-serif fonts, minimal color palette'
        },
        {
            style: 'Dynamic Action',
            layout: 'Diagonal composition with movement, multiple focal points, energetic flow',
            composition: 'Rule of thirds with dynamic angles, overlapping elements, motion blur effects',
            mood: 'Energetic, exciting, forward-moving',
            elements: 'Bold colors, dynamic shapes, action-oriented imagery, strong directional lines'
        },
        {
            style: 'Lifestyle Authentic',
            layout: 'Natural, candid composition with real-world settings, human-centered design',
            composition: 'Environmental context, natural lighting, authentic moments captured',
            mood: 'Warm, relatable, trustworthy, human',
            elements: 'Natural lighting, authentic people, real environments, warm color tones'
        },
        {
            style: 'Corporate Professional',
            layout: 'Structured grid layout, balanced composition, formal presentation',
            composition: 'Symmetrical balance, clear hierarchy, professional spacing',
            mood: 'Trustworthy, established, reliable, premium',
            elements: 'Corporate colors, professional imagery, clean typography, structured layout'
        },
        {
            style: 'Creative Artistic',
            layout: 'Artistic composition with creative elements, unique perspectives, artistic flair',
            composition: 'Creative angles, artistic overlays, unique visual treatments',
            mood: 'Creative, innovative, unique, inspiring',
            elements: 'Artistic effects, creative typography, unique color combinations, artistic imagery'
        },
        {
            style: 'Tech Innovation',
            layout: 'Futuristic design with tech elements, digital aesthetics, modern interfaces',
            composition: 'Digital grid systems, tech-inspired layouts, modern UI elements',
            mood: 'Innovative, cutting-edge, digital, forward-thinking',
            elements: 'Digital effects, tech imagery, modern interfaces, futuristic elements'
        },
        {
            style: 'Cultural Heritage',
            layout: 'Traditional patterns mixed with modern design, cultural elements integrated',
            composition: 'Cultural motifs, traditional-modern fusion, heritage-inspired layouts',
            mood: 'Cultural, authentic, heritage-proud, modern-traditional',
            elements: 'Traditional patterns, cultural colors, heritage imagery, modern interpretation'
        },
        {
            style: 'Luxury Premium',
            layout: 'Elegant, sophisticated layout with premium materials and finishes',
            composition: 'Luxurious spacing, premium typography, elegant proportions',
            mood: 'Luxurious, premium, exclusive, sophisticated',
            elements: 'Premium materials, elegant typography, sophisticated colors, luxury imagery'
        }
    ];
    return variations[seed % variations.length];
}
function getAdvancedPeopleInstructions(businessType, location) {
    const culturalContext = getLocalCulturalContext(location);
    return `
**ADVANCED PEOPLE INTEGRATION:**
- Include diverse, authentic people with PERFECT FACIAL FEATURES
- Complete faces, symmetrical features, natural expressions, professional poses
- Faces fully visible, well-lit, anatomically correct with no deformations
- Cultural Context: ${culturalContext}
- Show people in varied, engaging settings:
  * Professional environments (modern offices, studios, workshops)
  * Lifestyle settings (contemporary homes, trendy cafes, outdoor spaces)
  * Industry-specific contexts (${businessType} environments)
  * Cultural celebrations and modern community gatherings
  * Urban settings (co-working spaces, tech hubs, modern city life)
  * Traditional meets modern (cultural heritage with contemporary life)
- Ensure representation reflects local demographics and cultural values
- Show real people in natural, engaging situations that vary by design
- People should be actively engaged with the business/service context
- Use authentic expressions of joy, confidence, success, and community
- Include intergenerational representation when appropriate
- Show modern African/local fashion and styling
- Ensure people are central to the story, not just decorative elements`;
}
// NEW: Industry Intelligence System with World-Class Design Benchmarks
function getIndustryDesignIntelligence(businessType) {
    const industryIntelligence = {
        'restaurant': {
            name: 'Restaurant & Food Service',
            worldClassBrands: [
                'Noma',
                'Eleven Madison Park',
                'The French Laundry',
                'Osteria Francescana',
                'Gaggan'
            ],
            designBenchmarks: {
                visualStyle: 'Sophisticated, appetizing, experiential',
                colorPalettes: [
                    'Warm earth tones',
                    'Rich burgundies',
                    'Cream whites',
                    'Deep greens',
                    'Gold accents'
                ],
                typography: 'Elegant serifs, sophisticated sans-serifs, handwritten touches',
                imagery: 'Food photography, intimate dining scenes, chef portraits, ingredient close-ups',
                layout: 'Clean, spacious, food-focused, premium feel',
                creativeElements: [
                    'Food textures',
                    'Culinary tools',
                    'Seasonal ingredients',
                    'Dining atmosphere',
                    'Chef artistry'
                ]
            },
            creativityFrameworks: [
                'Culinary storytelling through visual narrative',
                'Seasonal and ingredient-driven design evolution',
                'Chef personality and restaurant atmosphere integration',
                'Food photography as art form',
                'Dining experience visualization'
            ],
            industryTrends: [
                'Farm-to-table aesthetics',
                'Minimalist plating influence',
                'Chef celebrity culture',
                'Sustainable dining',
                'Global fusion'
            ]
        },
        'technology': {
            name: 'Technology & Innovation',
            worldClassBrands: [
                'Apple',
                'Tesla',
                'SpaceX',
                'Google',
                'Microsoft',
                'Adobe'
            ],
            designBenchmarks: {
                visualStyle: 'Futuristic, clean, innovative, premium',
                colorPalettes: [
                    'Deep blues',
                    'Pure whites',
                    'Accent colors',
                    'Gradients',
                    'Neon highlights'
                ],
                typography: 'Modern sans-serifs, geometric precision, clean hierarchy',
                imagery: 'Abstract tech elements, clean interfaces, innovation concepts, premium materials',
                layout: 'Grid-based, clean lines, lots of white space, focused messaging',
                creativeElements: [
                    'Geometric shapes',
                    'Digital interfaces',
                    'Innovation metaphors',
                    'Premium materials',
                    'Future concepts'
                ]
            },
            creativityFrameworks: [
                'Technology as art and innovation',
                'Clean, premium aesthetic with bold innovation',
                'Future-focused visual storytelling',
                'Interface and product integration',
                'Innovation and progress visualization'
            ],
            industryTrends: [
                'AI integration',
                'Sustainable tech',
                'Minimalist interfaces',
                'Premium positioning',
                'Innovation focus'
            ]
        },
        'healthcare': {
            name: 'Healthcare & Wellness',
            worldClassBrands: [
                'Mayo Clinic',
                'Cleveland Clinic',
                'Johns Hopkins',
                'Stanford Health',
                'Cleveland Clinic'
            ],
            designBenchmarks: {
                visualStyle: 'Trustworthy, caring, professional, accessible',
                colorPalettes: [
                    'Calming blues',
                    'Soft greens',
                    'Warm whites',
                    'Accent colors',
                    'Professional tones'
                ],
                typography: 'Clean, readable fonts, professional hierarchy, accessible sizing',
                imagery: 'Caring professionals, modern facilities, wellness concepts, community health',
                layout: 'Clean, organized, easy to navigate, trustworthy appearance',
                creativeElements: [
                    'Medical symbols',
                    'Wellness imagery',
                    'Community health',
                    'Professional care',
                    'Modern facilities'
                ]
            },
            creativityFrameworks: [
                'Care and compassion through visual design',
                'Trust and professionalism building',
                'Wellness and health promotion',
                'Community health engagement',
                'Modern healthcare accessibility'
            ],
            industryTrends: [
                'Telehealth integration',
                'Patient-centered care',
                'Digital health',
                'Wellness focus',
                'Community health'
            ]
        },
        'fitness': {
            name: 'Fitness & Wellness',
            worldClassBrands: [
                'Peloton',
                'Nike',
                'Adidas',
                'Equinox',
                'Planet Fitness',
                'CrossFit'
            ],
            designBenchmarks: {
                visualStyle: 'Energetic, motivational, premium, inclusive',
                colorPalettes: [
                    'Bold reds',
                    'Energetic oranges',
                    'Motivational yellows',
                    'Strong blacks',
                    'Accent colors'
                ],
                typography: 'Bold, energetic fonts, motivational messaging, strong hierarchy',
                imagery: 'Action shots, diverse athletes, motivational scenes, fitness environments',
                layout: 'Dynamic, energetic, motivational, inclusive',
                creativeElements: [
                    'Movement lines',
                    'Athletic energy',
                    'Diversity representation',
                    'Motivational elements',
                    'Fitness environments'
                ]
            },
            creativityFrameworks: [
                'Energy and motivation through visual design',
                'Inclusive fitness for all',
                'Athletic achievement celebration',
                'Community and belonging',
                'Personal transformation stories'
            ],
            industryTrends: [
                'Digital fitness',
                'Inclusive representation',
                'Community building',
                'Personal transformation',
                'Wellness integration'
            ]
        },
        'finance': {
            name: 'Finance & Banking',
            worldClassBrands: [
                'Goldman Sachs',
                'JP Morgan',
                'Morgan Stanley',
                'BlackRock',
                'Visa',
                'Mastercard'
            ],
            designBenchmarks: {
                visualStyle: 'Trustworthy, sophisticated, stable, premium',
                colorPalettes: [
                    'Deep blues',
                    'Professional grays',
                    'Gold accents',
                    'Clean whites',
                    'Trustworthy tones'
                ],
                typography: 'Professional serifs, clean sans-serifs, authoritative hierarchy',
                imagery: 'Modern buildings, professional environments, growth concepts, stability symbols',
                layout: 'Structured, professional, trustworthy, premium',
                creativeElements: [
                    'Financial symbols',
                    'Growth metaphors',
                    'Stability elements',
                    'Professional environments',
                    'Premium materials'
                ]
            },
            creativityFrameworks: [
                'Trust and stability through design',
                'Sophistication and premium positioning',
                'Growth and progress visualization',
                'Professional excellence',
                'Financial security representation'
            ],
            industryTrends: [
                'Digital banking',
                'Fintech innovation',
                'Sustainable finance',
                'Personal finance',
                'Cryptocurrency'
            ]
        },
        'education': {
            name: 'Education & Learning',
            worldClassBrands: [
                'Harvard',
                'MIT',
                'Stanford',
                'Coursera',
                'Khan Academy',
                'Duolingo'
            ],
            designBenchmarks: {
                visualStyle: 'Inspiring, accessible, modern, engaging',
                colorPalettes: [
                    'Inspiring blues',
                    'Creative purples',
                    'Warm oranges',
                    'Growth greens',
                    'Accent colors'
                ],
                typography: 'Readable fonts, inspiring hierarchy, accessible design',
                imagery: 'Learning environments, diverse students, innovation concepts, growth metaphors',
                layout: 'Engaging, organized, inspiring, accessible',
                creativeElements: [
                    'Learning symbols',
                    'Growth metaphors',
                    'Innovation elements',
                    'Diversity representation',
                    'Knowledge visualization'
                ]
            },
            creativityFrameworks: [
                'Inspiration and learning through design',
                'Accessibility and inclusion',
                'Innovation and progress',
                'Community and collaboration',
                'Personal growth stories'
            ],
            industryTrends: [
                'Online learning',
                'Personalized education',
                'STEM focus',
                'Global accessibility',
                'Innovation in learning'
            ]
        },
        'retail': {
            name: 'Retail & E-commerce',
            worldClassBrands: [
                'Amazon',
                'Apple',
                'Nike',
                'IKEA',
                'Zara',
                'Uniqlo'
            ],
            designBenchmarks: {
                visualStyle: 'Attractive, commercial, engaging, conversion-focused',
                colorPalettes: [
                    'Brand colors',
                    'Attractive accents',
                    'Commercial tones',
                    'Engaging highlights'
                ],
                typography: 'Commercial fonts, conversion-focused messaging, attractive hierarchy',
                imagery: 'Product showcases, lifestyle integration, commercial appeal, brand personality',
                layout: 'Commercial, attractive, conversion-optimized, engaging',
                creativeElements: [
                    'Product elements',
                    'Lifestyle integration',
                    'Commercial appeal',
                    'Brand personality',
                    'Conversion elements'
                ]
            },
            creativityFrameworks: [
                'Commercial appeal and conversion',
                'Brand personality expression',
                'Lifestyle integration',
                'Product storytelling',
                'Customer engagement'
            ],
            industryTrends: [
                'E-commerce growth',
                'Personalization',
                'Sustainability',
                'Mobile commerce',
                'Social commerce'
            ]
        },
        'real estate': {
            name: 'Real Estate & Property',
            worldClassBrands: [
                'Sotheby\'s',
                'Christie\'s',
                'Douglas Elliman',
                'Compass',
                'Zillow'
            ],
            designBenchmarks: {
                visualStyle: 'Luxurious, aspirational, trustworthy, premium',
                colorPalettes: [
                    'Luxury golds',
                    'Sophisticated grays',
                    'Premium whites',
                    'Rich browns',
                    'Accent colors'
                ],
                typography: 'Luxury fonts, sophisticated hierarchy, premium appearance',
                imagery: 'Luxury properties, premium environments, aspirational lifestyles, professional service',
                layout: 'Luxurious, sophisticated, premium, aspirational',
                creativeElements: [
                    'Luxury elements',
                    'Premium materials',
                    'Aspirational lifestyles',
                    'Professional service',
                    'Property showcase'
                ]
            },
            creativityFrameworks: [
                'Luxury and aspiration through design',
                'Trust and professionalism',
                'Premium positioning',
                'Lifestyle visualization',
                'Property storytelling'
            ],
            industryTrends: [
                'Digital property viewing',
                'Sustainable properties',
                'Luxury market growth',
                'Technology integration',
                'Global investment'
            ]
        },
        'default': {
            name: 'Professional Services',
            worldClassBrands: [
                'McKinsey',
                'Bain',
                'BCG',
                'Deloitte',
                'PwC',
                'EY'
            ],
            designBenchmarks: {
                visualStyle: 'Professional, trustworthy, modern, sophisticated',
                colorPalettes: [
                    'Professional blues',
                    'Trustworthy grays',
                    'Modern accents',
                    'Clean whites'
                ],
                typography: 'Professional fonts, clean hierarchy, trustworthy appearance',
                imagery: 'Professional environments, modern offices, business concepts, trust symbols',
                layout: 'Professional, organized, trustworthy, modern',
                creativeElements: [
                    'Professional elements',
                    'Business concepts',
                    'Trust symbols',
                    'Modern environments',
                    'Success indicators'
                ]
            },
            creativityFrameworks: [
                'Professional excellence through design',
                'Trust and credibility building',
                'Modern sophistication',
                'Business success visualization',
                'Professional service representation'
            ],
            industryTrends: [
                'Digital transformation',
                'Remote work',
                'Sustainability',
                'Innovation focus',
                'Global expansion'
            ]
        }
    };
    return industryIntelligence[businessType.toLowerCase()] || industryIntelligence['default'];
}
// NEW: Enhanced Creativity System with Industry Intelligence
function getEnhancedCreativityFramework(businessType, designStyle, seed) {
    const industryIntel = getIndustryDesignIntelligence(businessType);
    const creativityFrameworks = [
        {
            name: 'World-Class Benchmarking',
            approach: `Study and emulate the design excellence of ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}`,
            focus: 'Premium positioning, industry best practices, sophisticated aesthetics',
            elements: industryIntel.designBenchmarks.creativeElements,
            description: `Create designs that rival the sophistication and quality of ${industryIntel.name} industry leaders`
        },
        {
            name: 'Industry Trend Integration',
            approach: `Incorporate current ${industryIntel.name} trends: ${industryIntel.industryTrends.slice(0, 3).join(', ')}`,
            focus: 'Modern relevance, industry innovation, forward-thinking design',
            elements: [
                'Trend elements',
                'Innovation concepts',
                'Modern aesthetics',
                'Industry relevance'
            ],
            description: 'Design that feels current and relevant to the industry while maintaining creativity'
        },
        {
            name: 'Creative Storytelling',
            approach: industryIntel.creativityFrameworks[seed % industryIntel.creativityFrameworks.length],
            focus: 'Narrative design, emotional connection, brand storytelling',
            elements: [
                'Story elements',
                'Emotional triggers',
                'Narrative flow',
                'Brand personality'
            ],
            description: 'Use visual design to tell compelling stories that connect with the audience'
        },
        {
            name: 'Innovation & Disruption',
            approach: 'Challenge industry conventions with creative innovation',
            focus: 'Breaking norms, creative disruption, unique positioning',
            elements: [
                'Innovation elements',
                'Disruptive concepts',
                'Unique approaches',
                'Creative risk-taking'
            ],
            description: 'Create designs that stand out by challenging industry conventions'
        },
        {
            name: 'Cultural & Global Fusion',
            approach: 'Blend local cultural elements with global industry standards',
            focus: 'Cultural authenticity, global relevance, unique positioning',
            elements: [
                'Cultural elements',
                'Global standards',
                'Local authenticity',
                'Fusion concepts'
            ],
            description: 'Create designs that feel both locally authentic and globally competitive'
        }
    ];
    return creativityFrameworks[seed % creativityFrameworks.length];
}
// NEW: Industry-Specific Design Enhancement
function enhanceDesignWithIndustryIntelligence(designPrompt, businessType, designStyle, seed) {
    const industryIntel = getIndustryDesignIntelligence(businessType);
    const creativityFramework = getEnhancedCreativityFramework(businessType, designStyle, seed);
    const industryEnhancement = `
🏭 INDUSTRY INTELLIGENCE INTEGRATION:
**Industry:** ${industryIntel.name}
**World-Class Benchmarks:** ${industryIntel.worldClassBrands.slice(0, 3).join(', ')}
**Industry Visual Style:** ${industryIntel.designBenchmarks.visualStyle}
**Industry Color Palettes:** ${industryIntel.designBenchmarks.colorPalettes.slice(0, 3).join(', ')}
**Industry Typography:** ${industryIntel.designBenchmarks.typography}
**Industry Imagery:** ${industryIntel.designBenchmarks.imagery}
**Industry Layout:** ${industryIntel.designBenchmarks.layout}

🎨 CREATIVITY FRAMEWORK: ${creativityFramework.name}
**Approach:** ${creativityFramework.approach}
**Focus:** ${creativityFramework.focus}
**Creative Elements:** ${creativityFramework.elements.slice(0, 3).join(', ')}
**Description:** ${creativityFramework.description}

🚀 INDUSTRY TRENDS TO INCORPORATE:
${industryIntel.industryTrends.slice(0, 3).map((trend, i)=>`${i + 1}. ${trend}`).join('\n')}

🎯 DESIGN REQUIREMENTS:
- **Industry Benchmarking:** Create designs that rival ${industryIntel.name} industry leaders
- **Trend Integration:** Incorporate current industry trends naturally
- **Creative Innovation:** Use ${creativityFramework.name} approach for unique positioning
- **Quality Standards:** Match world-class design quality and sophistication
- **Industry Relevance:** Ensure design feels authentic to ${industryIntel.name} industry`;
    return designPrompt + industryEnhancement;
}
// NEW: Business Intelligence Engine - Local Marketing Expert System
function getBusinessIntelligenceEngine(businessType, location) {
    const businessIntelligence = {
        'restaurant': {
            name: 'Restaurant & Food Service',
            localExpertise: {
                experience: '25+ years in hospitality and culinary marketing',
                marketDynamics: [
                    'Seasonal menu optimization and local ingredient sourcing',
                    'Customer loyalty programs and repeat business strategies',
                    'Local competition analysis and unique positioning',
                    'Food trends and cultural preferences in the area',
                    'Pricing strategies for local market conditions'
                ],
                localPhrases: [
                    'Taste of [location]',
                    'Where locals eat',
                    'Fresh from our kitchen',
                    'Made with love',
                    'Family recipe',
                    'Local favorite',
                    'Chef\'s special',
                    'Daily fresh',
                    'Home-cooked taste',
                    'Local ingredients'
                ],
                contentStrategies: [
                    'Behind-the-scenes kitchen stories',
                    'Chef personality and cooking philosophy',
                    'Local ingredient sourcing stories',
                    'Customer testimonials and success stories',
                    'Seasonal menu highlights',
                    'Local food culture integration',
                    'Community involvement and events',
                    'Sustainability and local farming partnerships'
                ],
                engagementHooks: [
                    'Food memories and nostalgia',
                    'Local pride and community connection',
                    'Health and wellness benefits',
                    'Family traditions and gatherings',
                    'Adventure and trying new flavors',
                    'Social sharing and food photography',
                    'Exclusive offers and VIP experiences',
                    'Local events and celebrations'
                ]
            }
        },
        'technology': {
            name: 'Technology & Innovation',
            localExpertise: {
                experience: '22+ years in tech marketing and digital transformation',
                marketDynamics: [
                    'Local tech ecosystem and startup culture',
                    'Digital adoption rates in the region',
                    'Competitive landscape and innovation gaps',
                    'Local talent pool and skill development',
                    'Government tech initiatives and support'
                ],
                localPhrases: [
                    'Innovation hub',
                    'Digital transformation',
                    'Tech-forward solutions',
                    'Future-ready',
                    'Smart [location]',
                    'Digital innovation',
                    'Tech excellence',
                    'Innovation center',
                    'Digital leadership',
                    'Tech ecosystem'
                ],
                contentStrategies: [
                    'Local tech success stories',
                    'Innovation case studies',
                    'Digital transformation journeys',
                    'Tech talent development',
                    'Local startup ecosystem',
                    'Government tech partnerships',
                    'Digital skills training',
                    'Smart city initiatives'
                ],
                engagementHooks: [
                    'Career advancement and skill development',
                    'Innovation and future thinking',
                    'Local tech community building',
                    'Digital transformation success',
                    'Tech entrepreneurship',
                    'Smart city development',
                    'Digital inclusion',
                    'Tech for social good'
                ]
            }
        },
        'healthcare': {
            name: 'Healthcare & Wellness',
            localExpertise: {
                experience: '28+ years in healthcare marketing and patient care',
                marketDynamics: [
                    'Local health demographics and needs',
                    'Healthcare accessibility and insurance coverage',
                    'Competing healthcare providers and services',
                    'Local health trends and concerns',
                    'Community health initiatives and partnerships'
                ],
                localPhrases: [
                    'Your health, our priority',
                    'Caring for [location] families',
                    'Local healthcare excellence',
                    'Community health partner',
                    'Your wellness journey',
                    'Health close to home',
                    'Caring professionals',
                    'Local health experts',
                    'Community wellness',
                    'Health for everyone'
                ],
                contentStrategies: [
                    'Patient success stories and testimonials',
                    'Local health education and prevention',
                    'Community health initiatives',
                    'Healthcare professional spotlights',
                    'Local health trends and insights',
                    'Wellness tips and advice',
                    'Health technology integration',
                    'Community partnerships and events'
                ],
                engagementHooks: [
                    'Family health and wellness',
                    'Preventive care and early detection',
                    'Local health community',
                    'Professional healthcare expertise',
                    'Health technology innovation',
                    'Community health improvement',
                    'Patient-centered care',
                    'Health education and awareness'
                ]
            }
        },
        'fitness': {
            name: 'Fitness & Wellness',
            localExpertise: {
                experience: '24+ years in fitness marketing and community building',
                marketDynamics: [
                    'Local fitness culture and preferences',
                    'Competing gyms and fitness options',
                    'Seasonal fitness trends and activities',
                    'Local sports teams and community events',
                    'Health awareness and wellness trends'
                ],
                localPhrases: [
                    'Your fitness journey starts here',
                    'Stronger [location] community',
                    'Local fitness excellence',
                    'Your wellness partner',
                    'Fitness for everyone',
                    'Local strength',
                    'Community fitness',
                    'Your health transformation',
                    'Local fitness family',
                    'Wellness close to home'
                ],
                contentStrategies: [
                    'Member transformation stories',
                    'Local fitness challenges and events',
                    'Community fitness initiatives',
                    'Trainer spotlights and expertise',
                    'Local sports team partnerships',
                    'Seasonal fitness programs',
                    'Wellness education and tips',
                    'Community health partnerships'
                ],
                engagementHooks: [
                    'Personal transformation and goals',
                    'Community fitness challenges',
                    'Local sports pride',
                    'Health and wellness education',
                    'Fitness community building',
                    'Seasonal fitness motivation',
                    'Professional training expertise',
                    'Inclusive fitness for all'
                ]
            }
        },
        'finance': {
            name: 'Finance & Banking',
            localExpertise: {
                experience: '26+ years in financial services and local banking',
                marketDynamics: [
                    'Local economic conditions and growth',
                    'Competing financial institutions',
                    'Local business financing needs',
                    'Personal finance trends in the area',
                    'Community investment opportunities'
                ],
                localPhrases: [
                    'Your financial partner in [location]',
                    'Local financial expertise',
                    'Community banking excellence',
                    'Your financial future',
                    'Local financial solutions',
                    'Community financial partner',
                    'Your money, our care',
                    'Local financial guidance',
                    'Community wealth building',
                    'Financial security close to home'
                ],
                contentStrategies: [
                    'Local business success stories',
                    'Financial education and literacy',
                    'Community investment initiatives',
                    'Local economic insights',
                    'Personal finance success stories',
                    'Business financing solutions',
                    'Local financial trends',
                    'Community financial partnerships'
                ],
                engagementHooks: [
                    'Financial security and planning',
                    'Local business growth',
                    'Community economic development',
                    'Personal finance education',
                    'Investment opportunities',
                    'Business financing solutions',
                    'Local economic pride',
                    'Financial wellness for families'
                ]
            }
        },
        'education': {
            name: 'Education & Learning',
            localExpertise: {
                experience: '23+ years in educational marketing and community learning',
                marketDynamics: [
                    'Local education standards and performance',
                    'Competing educational institutions',
                    'Local learning needs and preferences',
                    'Community education initiatives',
                    'Employment and skill development needs'
                ],
                localPhrases: [
                    'Learning excellence in [location]',
                    'Your educational journey',
                    'Local learning excellence',
                    'Community education partner',
                    'Your learning success',
                    'Local educational leadership',
                    'Community learning center',
                    'Your knowledge partner',
                    'Local educational excellence',
                    'Learning close to home'
                ],
                contentStrategies: [
                    'Student success stories',
                    'Local educational achievements',
                    'Community learning initiatives',
                    'Educational innovation and technology',
                    'Local employment partnerships',
                    'Skill development programs',
                    'Community education events',
                    'Local learning trends'
                ],
                engagementHooks: [
                    'Personal growth and development',
                    'Career advancement opportunities',
                    'Local educational pride',
                    'Community learning initiatives',
                    'Innovation in education',
                    'Skill development and training',
                    'Local employment success',
                    'Educational excellence recognition'
                ]
            }
        },
        'retail': {
            name: 'Retail & E-commerce',
            localExpertise: {
                experience: '25+ years in retail marketing and customer experience',
                marketDynamics: [
                    'Local shopping preferences and trends',
                    'Competing retail options and malls',
                    'Local economic conditions and spending',
                    'Seasonal shopping patterns',
                    'Community shopping habits and events'
                ],
                localPhrases: [
                    'Your local shopping destination',
                    'Shopping excellence in [location]',
                    'Local retail leadership',
                    'Your shopping partner',
                    'Local retail excellence',
                    'Community shopping center',
                    'Your retail destination',
                    'Local shopping experience',
                    'Community retail partner',
                    'Shopping close to home'
                ],
                contentStrategies: [
                    'Local product highlights',
                    'Customer success stories',
                    'Community shopping events',
                    'Local brand partnerships',
                    'Seasonal shopping guides',
                    'Local shopping trends',
                    'Community retail initiatives',
                    'Local customer appreciation'
                ],
                engagementHooks: [
                    'Local product discovery',
                    'Community shopping events',
                    'Seasonal shopping excitement',
                    'Local brand support',
                    'Customer appreciation',
                    'Shopping convenience',
                    'Local retail pride',
                    'Community shopping experience'
                ]
            }
        },
        'real estate': {
            name: 'Real Estate & Property',
            localExpertise: {
                experience: '27+ years in real estate marketing and local property',
                marketDynamics: [
                    'Local property market conditions',
                    'Competing real estate agencies',
                    'Local property trends and values',
                    'Community development and growth',
                    'Local investment opportunities'
                ],
                localPhrases: [
                    'Your local real estate expert',
                    'Real estate excellence in [location]',
                    'Local property specialist',
                    'Your property partner',
                    'Local real estate leadership',
                    'Community property expert',
                    'Your real estate guide',
                    'Local property excellence',
                    'Community real estate partner',
                    'Property close to home'
                ],
                contentStrategies: [
                    'Local property success stories',
                    'Community development updates',
                    'Local property market insights',
                    'Property investment opportunities',
                    'Local neighborhood highlights',
                    'Community real estate events',
                    'Local property trends',
                    'Community property partnerships'
                ],
                engagementHooks: [
                    'Property investment opportunities',
                    'Local neighborhood pride',
                    'Community development',
                    'Property market insights',
                    'Local real estate success',
                    'Community property events',
                    'Property investment guidance',
                    'Local real estate expertise'
                ]
            }
        },
        'default': {
            name: 'Professional Services',
            localExpertise: {
                experience: '20+ years in professional services and local business',
                marketDynamics: [
                    'Local business environment and competition',
                    'Community business needs and trends',
                    'Local economic conditions',
                    'Business development opportunities',
                    'Community partnerships and networking'
                ],
                localPhrases: [
                    'Your local business partner',
                    'Professional excellence in [location]',
                    'Local business expertise',
                    'Your success partner',
                    'Local professional leadership',
                    'Community business partner',
                    'Your business guide',
                    'Local professional excellence',
                    'Community business expert',
                    'Success close to home'
                ],
                contentStrategies: [
                    'Local business success stories',
                    'Community business initiatives',
                    'Local business insights',
                    'Business development opportunities',
                    'Local business trends',
                    'Community business events',
                    'Local business partnerships',
                    'Community business support'
                ],
                engagementHooks: [
                    'Business growth and success',
                    'Local business community',
                    'Professional development',
                    'Business opportunities',
                    'Local business pride',
                    'Community business support',
                    'Business innovation',
                    'Local business expertise'
                ]
            }
        },
        'financial technology software': {
            name: 'Financial Technology Software',
            localExpertise: {
                experience: '15+ years in fintech and digital payments',
                marketDynamics: [
                    'Digital payment adoption rates in the region',
                    'Mobile banking and fintech competition',
                    'Financial inclusion and accessibility needs',
                    'Regulatory compliance and security requirements',
                    'Local banking partnerships and integrations'
                ],
                contentStrategies: [
                    'Digital financial innovation',
                    'Payment security and trust',
                    'Financial inclusion stories',
                    'Fintech industry insights',
                    'User experience excellence',
                    'Local market expansion',
                    'Partnership announcements',
                    'Technology advancement'
                ],
                engagementHooks: [
                    'Financial innovation',
                    'Digital payments',
                    'Financial inclusion',
                    'Secure transactions',
                    'Fintech solutions',
                    'Payment convenience',
                    'Financial empowerment',
                    'Digital banking'
                ]
            },
            localPhrases: [
                'Your digital payment partner',
                'Fintech innovation in [location]',
                'Digital financial solutions',
                'Your payment solution',
                'Financial technology excellence',
                'Digital banking for [location]',
                'Your fintech partner',
                'Payment innovation'
            ]
        },
        'default': {
            name: 'Professional Services',
            localExpertise: {
                experience: '20+ years in professional services',
                marketDynamics: [
                    'Local business environment and competition',
                    'Market trends and opportunities',
                    'Customer needs and preferences',
                    'Industry best practices and standards',
                    'Local economic conditions and growth'
                ],
                contentStrategies: [
                    'Professional excellence and expertise',
                    'Client success stories',
                    'Industry insights and trends',
                    'Local market knowledge',
                    'Service quality and reliability',
                    'Innovation and solutions',
                    'Community involvement',
                    'Professional development'
                ],
                engagementHooks: [
                    'Professional excellence',
                    'Client success',
                    'Industry expertise',
                    'Local market knowledge',
                    'Quality service',
                    'Innovation solutions',
                    'Community partnership',
                    'Professional growth'
                ]
            },
            localPhrases: [
                'Your local professional partner',
                'Excellence in [location]',
                'Local expertise you can trust',
                'Your success partner',
                'Professional solutions for [location]',
                'Local industry leadership',
                'Your trusted advisor',
                'Professional excellence'
            ]
        }
    };
    const result = businessIntelligence[businessType.toLowerCase()] || businessIntelligence['default'];
    return result;
}
// NEW: Dynamic Content Strategy Engine - Never Repetitive
function getDynamicContentStrategy(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const contentStrategies = [
        {
            name: 'Local Market Expert',
            approach: `Position as the ${businessIntel.name} expert in ${location} with ${businessIntel.localExpertise.experience}`,
            focus: 'Local expertise, community knowledge, market insights',
            hooks: businessIntel.localExpertise.engagementHooks.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'local expertise',
                'community focused',
                'trusted service',
                'proven results'
            ]).slice(0, 4),
            description: `Write like a ${businessIntel.localExpertise.experience} professional who knows ${location} inside and out`
        },
        {
            name: 'Community Storyteller',
            approach: `Share authentic stories about local ${businessIntel.name} success and community impact`,
            focus: 'Real stories, community connection, authentic experiences',
            hooks: businessIntel.localExpertise.engagementHooks.slice(4, 8),
            phrases: (businessIntel.localPhrases || [
                'community stories',
                'local success',
                'authentic experiences',
                'real results'
            ]).slice(4, 8),
            description: 'Share real, relatable stories that connect with the local community'
        },
        {
            name: 'Industry Innovator',
            approach: `Showcase cutting-edge ${businessIntel.name} solutions and industry leadership`,
            focus: 'Innovation, industry trends, competitive advantage',
            hooks: businessIntel.localExpertise.contentStrategies.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'innovative solutions',
                'industry leader',
                'cutting-edge',
                'advanced technology'
            ]).slice(0, 4),
            description: 'Position as an industry leader with innovative solutions and insights'
        },
        {
            name: 'Problem Solver',
            approach: `Address specific ${businessIntel.name} challenges that local businesses and people face`,
            focus: 'Problem identification, solution offering, value demonstration',
            hooks: businessIntel.localExpertise.marketDynamics.slice(0, 4),
            phrases: (businessIntel.localPhrases || [
                'problem solver',
                'effective solutions',
                'proven results',
                'reliable service'
            ]).slice(0, 4),
            description: 'Identify and solve real problems that matter to the local community'
        },
        {
            name: 'Success Catalyst',
            approach: `Inspire and guide local ${businessIntel.name} success through proven strategies`,
            focus: 'Success stories, proven methods, inspirational guidance',
            hooks: businessIntel.localExpertise.contentStrategies.slice(4, 8),
            phrases: (businessIntel.localPhrases || [
                'success catalyst',
                'proven strategies',
                'inspiring results',
                'growth partner'
            ]).slice(4, 8),
            description: 'Inspire success through proven strategies and real results'
        }
    ];
    return contentStrategies[seed % contentStrategies.length];
}
// NEW: Human Writing Style Generator - Authentic, Engaging
function getHumanWritingStyle(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const writingStyles = [
        {
            name: 'Conversational Expert',
            tone: 'Friendly, knowledgeable, approachable',
            voice: `Like a ${businessIntel.localExpertise.experience} professional chatting with a friend over coffee`,
            characteristics: [
                'Use local phrases naturally',
                'Share personal insights and experiences',
                'Ask engaging questions',
                'Use conversational language',
                'Show genuine enthusiasm for the business'
            ],
            examples: [
                `"You know what I love about ${location}? The way our community..."`,
                `"After ${businessIntel.localExpertise.experience} in this industry, I've learned..."`,
                `"Here's something that always makes me smile about our business..."`
            ]
        },
        {
            name: 'Storytelling Mentor',
            tone: 'Inspirational, narrative, engaging',
            voice: 'Like sharing a compelling story that teaches and inspires',
            characteristics: [
                'Start with intriguing hooks',
                'Build narrative tension',
                'Include relatable characters',
                'End with meaningful insights',
                'Use vivid, descriptive language'
            ],
            examples: [
                `"Last week, something incredible happened that reminded me why..."`,
                `"I'll never forget the day when..."`,
                `"There's a story behind every success, and this one..."`
            ]
        },
        {
            name: 'Local Champion',
            tone: 'Proud, community-focused, authentic',
            voice: 'Like a proud local business owner celebrating community success',
            characteristics: [
                'Celebrate local achievements',
                'Use local pride and identity',
                'Highlight community connections',
                'Show genuine local love',
                'Connect business to community values'
            ],
            examples: [
                `"This is why I'm so proud to be part of the ${location} community..."`,
                `"Our ${location} neighbors never cease to amaze me..."`,
                `"There's something special about doing business in ${location}..."`
            ]
        },
        {
            name: 'Problem-Solving Partner',
            tone: 'Helpful, solution-oriented, trustworthy',
            voice: 'Like a trusted advisor helping solve real problems',
            characteristics: [
                'Identify real problems',
                'Offer practical solutions',
                'Show understanding and empathy',
                'Build trust through expertise',
                'Focus on customer benefit'
            ],
            examples: [
                `"I've noticed that many ${location} businesses struggle with..."`,
                `"Here's a solution that's worked for countless local businesses..."`,
                `"Let me share what I've learned about solving this common challenge..."`
            ]
        },
        {
            name: 'Success Celebrator',
            tone: 'Enthusiastic, celebratory, motivational',
            voice: 'Like celebrating wins and inspiring future success',
            characteristics: [
                'Celebrate achievements',
                'Share success stories',
                'Inspire future action',
                'Use positive, uplifting language',
                'Connect success to community'
            ],
            examples: [
                `"I'm thrilled to share some amazing news from our ${location} community..."`,
                `"This success story is exactly why I love ${businessIntel.name} in ${location}..."`,
                `"Let's celebrate this incredible achievement together..."`
            ]
        }
    ];
    return writingStyles[seed % writingStyles.length];
}
// NEW: Anti-Repetition Content Engine
function generateUniqueContentVariation(businessType, location, seed) {
    const businessIntel = getBusinessIntelligenceEngine(businessType, location);
    const contentStrategy = getDynamicContentStrategy(businessType, location, seed);
    const writingStyle = getHumanWritingStyle(businessType, location, seed);
    // Generate unique content angle based on multiple factors
    const contentAngles = [
        {
            type: 'Local Insight',
            focus: `Share unique ${businessIntel.name} insights specific to ${location}`,
            examples: [
                `"What I've learned about ${businessIntel.name} in ${location} after ${businessIntel.localExpertise.experience}..."`,
                `"The ${businessIntel.name} landscape in ${location} is unique because..."`,
                `"Here's what makes ${location} special for ${businessIntel.name} businesses..."`
            ]
        },
        {
            type: 'Community Story',
            focus: `Tell a compelling story about local ${businessIntel.name} impact`,
            examples: [
                `"Last month, something incredible happened in our ${location} community..."`,
                `"I want to share a story that perfectly captures why we do what we do..."`,
                `"This is the kind of moment that makes ${businessIntel.name} in ${location} special..."`
            ]
        },
        {
            type: 'Industry Innovation',
            focus: `Showcase cutting-edge ${businessIntel.name} solutions`,
            examples: [
                `"We're excited to introduce something that's changing ${businessIntel.name} in ${location}..."`,
                `"Here's how we're innovating in the ${businessIntel.name} space..."`,
                `"This new approach is revolutionizing how we do ${businessIntel.name} in ${location}..."`
            ]
        },
        {
            type: 'Problem Solution',
            focus: `Address specific ${businessIntel.name} challenges in ${location}`,
            examples: [
                `"I've noticed that many ${location} businesses struggle with..."`,
                `"Here's a common challenge in ${businessIntel.name} and how we solve it..."`,
                `"Let me share what I've learned about overcoming this ${businessIntel.name} obstacle..."`
            ]
        },
        {
            type: 'Success Celebration',
            focus: `Celebrate local ${businessIntel.name} achievements`,
            examples: [
                `"I'm thrilled to share some amazing news from our ${location} community..."`,
                `"This success story is exactly why I love ${businessIntel.name} in ${location}..."`,
                `"Let's celebrate this incredible achievement together..."`
            ]
        }
    ];
    const selectedAngle = contentAngles[seed % contentAngles.length];
    return {
        contentStrategy: contentStrategy,
        writingStyle: writingStyle,
        contentAngle: selectedAngle,
        uniqueSignature: `${selectedAngle.type}-${contentStrategy.name}-${writingStyle.name}-${seed}`,
        localPhrases: (businessIntel.localPhrases || [
            'professional service',
            'quality results',
            'trusted expertise'
        ]).slice(0, 3),
        engagementHooks: businessIntel.localExpertise.engagementHooks.slice(0, 3),
        marketInsights: businessIntel.localExpertise.marketDynamics.slice(0, 2)
    };
}
// Helper functions for context generation
function getSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Fall';
    return 'Winter';
}
function getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'Morning';
    if (hour >= 12 && hour < 17) return 'Afternoon';
    if (hour >= 17 && hour < 21) return 'Evening';
    return 'Night';
}
function generateContextualTrends(businessType, location) {
    const trends = [
        {
            topic: `${businessType} innovation trends`,
            category: 'Industry',
            relevance: 'high'
        },
        {
            topic: `${location} business growth`,
            category: 'Local',
            relevance: 'high'
        },
        {
            topic: 'Digital transformation',
            category: 'Technology',
            relevance: 'medium'
        },
        {
            topic: 'Customer experience optimization',
            category: 'Business',
            relevance: 'high'
        },
        {
            topic: 'Sustainable business practices',
            category: 'Trends',
            relevance: 'medium'
        }
    ];
    return trends.slice(0, 3);
}
function generateWeatherContext(location) {
    // Simplified weather context based on location and season
    const season = getSeason();
    const contexts = {
        'Spring': {
            condition: 'Fresh and energizing',
            business_impact: 'New beginnings, growth opportunities',
            content_opportunities: 'Renewal, fresh starts, growth themes'
        },
        'Summer': {
            condition: 'Bright and active',
            business_impact: 'High energy, outdoor activities',
            content_opportunities: 'Vibrant colors, active lifestyle, summer solutions'
        },
        'Fall': {
            condition: 'Cozy and productive',
            business_impact: 'Planning, preparation, harvest',
            content_opportunities: 'Preparation, results, autumn themes'
        },
        'Winter': {
            condition: 'Focused and strategic',
            business_impact: 'Planning, reflection, indoor focus',
            content_opportunities: 'Planning, strategy, winter solutions'
        }
    };
    return {
        temperature: '22',
        condition: contexts[season].condition,
        business_impact: contexts[season].business_impact,
        content_opportunities: contexts[season].content_opportunities
    };
}
function generateLocalOpportunities(businessType, location) {
    const opportunities = [
        {
            name: `${location} Business Expo`,
            venue: 'Local Convention Center',
            relevance: 'networking'
        },
        {
            name: `${businessType} Innovation Summit`,
            venue: 'Business District',
            relevance: 'industry'
        },
        {
            name: 'Local Entrepreneur Meetup',
            venue: 'Community Center',
            relevance: 'community'
        }
    ];
    return opportunities.slice(0, 2);
}
// Get API keys (supporting both server-side and client-side)
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_GENAI_API_KEY;
if (!apiKey) {}
// Initialize Google GenAI client with Revo 1.0 configuration
const ai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](apiKey);
// Revo 1.0 uses Gemini 2.5 Flash Image Preview
const REVO_1_0_MODEL = 'gemini-2.5-flash-image-preview';
async function generateRevo10Content(input) {
    try {
        // Convert input to BusinessProfile for advanced analysis
        const businessProfile = {
            businessName: input.businessName,
            businessType: input.businessType,
            location: input.location,
            targetAudience: input.targetAudience,
            brandVoice: input.writingTone,
            uniqueSellingPoints: [
                input.competitiveAdvantages || 'Quality service'
            ],
            competitors: []
        };
        // 📊 GENERATE ADVANCED CONTENT WITH DEEP ANALYSIS
        const advancedContent = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$advanced$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["advancedContentGenerator"].generateEngagingContent(businessProfile, input.platform, 'promotional');
        // 🎯 GET TRENDING INSIGHTS FOR ENHANCED RELEVANCE
        const trendingEnhancement = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$trending$2d$content$2d$enhancer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["trendingEnhancer"].getTrendingEnhancement({
            businessType: input.businessType,
            platform: input.platform,
            location: input.location,
            targetAudience: input.targetAudience
        });
        // 📈 ANALYZE PERFORMANCE FOR CONTINUOUS IMPROVEMENT
        const performanceAnalysis = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$content$2d$performance$2d$analyzer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["performanceAnalyzer"].analyzePerformance(advancedContent, businessProfile);
        // Extract hashtags from advanced content for use in business-specific generation
        const hashtags = advancedContent.hashtags;
        // Gather real-time context data (keeping existing functionality)
        const realTimeContext = await gatherRealTimeContext(input.businessType, input.location, input.platform);
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the content generation prompt with enhanced brand context
        const contentPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].CONTENT_USER_PROMPT_TEMPLATE.replace('{businessName}', input.businessName).replace('{businessType}', input.businessType).replace('{platform}', input.platform).replace('{writingTone}', input.writingTone).replace('{location}', input.location).replace('{primaryColor}', input.primaryColor || '#3B82F6').replace('{visualStyle}', input.visualStyle || 'modern').replace('{targetAudience}', input.targetAudience).replace('{services}', input.services || '').replace('{keyFeatures}', input.keyFeatures || '').replace('{competitiveAdvantages}', input.competitiveAdvantages || '').replace('{contentThemes}', input.contentThemes.join(', ') || 'general business content');
        // 🎨 CREATIVE CAPTION GENERATION: Apply creative enhancement system
        // NEW: Get business intelligence and local marketing expertise
        const businessIntel = getBusinessIntelligenceEngine(input.businessType, input.location);
        const randomSeed = Math.floor(Math.random() * 10000) + Date.now();
        const uniqueContentVariation = generateUniqueContentVariation(input.businessType, input.location, randomSeed % 1000);
        // 🎯 NEW: Generate business-specific content strategy
        const businessDetails = {
            experience: '5+ years',
            expertise: input.keyFeatures,
            services: input.services,
            location: input.location,
            targetAudience: input.targetAudience
        };
        // Generate strategic content plan based on business type and goals
        const contentPlan = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StrategicContentPlanner"].generateBusinessSpecificContent(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness' // Can be dynamic based on business goals
        );
        // 🎨 NEW: Generate business-specific headlines and subheadlines with AI
        const businessHeadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateBusinessSpecificHeadline"])(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness', trendingEnhancement, advancedContent);
        const businessSubheadline = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateBusinessSpecificSubheadline"])(input.businessType, input.businessName, input.location, businessDetails, businessHeadline.headline, 'awareness', trendingEnhancement, advancedContent);
        // 📝 NEW: Generate AI-powered business-specific caption
        const businessCaption = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateBusinessSpecificCaption"])(input.businessType, input.businessName, input.location, businessDetails, input.platform, 'awareness', trendingEnhancement, advancedContent);
        // 🎯 BUSINESS-SPECIFIC CAPTION GENERATION COMPLETE
        // 🎯 BUSINESS-SPECIFIC CONTENT GENERATION COMPLETE
        // 🎯 FINAL: Return business-specific content package
        const finalContent = {
            content: businessCaption.caption,
            headline: businessHeadline.headline,
            subheadline: businessSubheadline.subheadline,
            callToAction: businessCaption.callToAction,
            hashtags: hashtags,
            catchyWords: businessHeadline.headline,
            contentStrategy: contentPlan.strategy,
            businessStrengths: contentPlan.businessStrengths,
            marketOpportunities: contentPlan.marketOpportunities,
            valueProposition: contentPlan.valueProposition,
            platform: input.platform,
            businessType: input.businessType,
            location: input.location,
            realTimeContext: realTimeContext,
            creativeContext: {
                style: businessHeadline.approach,
                tone: businessHeadline.emotionalImpact,
                framework: businessSubheadline.framework,
                businessInsights: contentPlan,
                variation: uniqueContentVariation
            },
            // 🧠 BUSINESS INTELLIGENCE DATA
            businessIntelligence: {
                contentGoal: contentPlan.strategy.goal,
                businessStrengths: contentPlan.businessStrengths,
                marketOpportunities: contentPlan.marketOpportunities,
                customerPainPoints: contentPlan.customerPainPoints,
                valueProposition: contentPlan.valueProposition,
                localRelevance: contentPlan.localRelevance
            },
            variants: [
                {
                    platform: input.platform,
                    aspectRatio: '1:1',
                    imageUrl: '' // Will be generated separately
                }
            ],
            generatedAt: new Date().toISOString()
        };
        return finalContent;
    } catch (error) {
        throw new Error(`Revo 1.0 content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Design(input) {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build the design generation prompt
        const designPrompt = `Create a creative design concept for ${input.businessName} (${input.businessType}) that feels like it was imagined by a human designer.

Think like a creative professional who:
- Starts with a visual story or concept, not a template
- Uses colors and elements that feel chosen, not generated
- Creates layouts that feel natural and organic
- Thinks about the emotional impact, not just technical perfection

DESIGN BRIEF:
- Business: ${input.businessName} (${input.businessType})
- Platform: ${input.platform}
- Style: ${input.visualStyle}
- Colors: Primary ${input.primaryColor}, Accent ${input.accentColor}, Background ${input.backgroundColor}
- Message: ${input.imageText}

CREATIVE APPROACH:
- What's the visual story you want to tell?
- How can you make this feel authentic and engaging?
- What unexpected elements would make this memorable?
- How can you avoid looking like every other business post?

DESIGN DESCRIPTION:
Describe your creative concept in natural, designer language. Focus on the creative vision, mood, and story - not technical specifications. Make it sound like a designer explaining their idea to a client.

Remember: You're a creative human designer, not an AI. Think with imagination and artistic vision.`;
        const result = await model.generateContent([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Prompts"].DESIGN_SYSTEM_PROMPT,
            designPrompt
        ]);
        const response = await result.response;
        const design = response.text();
        return {
            design: design.trim(),
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        throw new Error(`Revo 1.0 design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateRevo10Image(input) {
    try {
        // 🎨 CREATIVE ENHANCEMENT: Apply creative design system
        let creativeDesignEnhancement = '';
        if (input.creativeContext) {
            const designEnhancement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$creative$2d$enhancement$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enhanceDesignCreativity"])(input.designDescription, input.businessType, input.location || 'Global', input.creativeContext);
            creativeDesignEnhancement = `
🎨 CREATIVE DESIGN ENHANCEMENT SYSTEM ACTIVATED:
${designEnhancement.enhancedPrompt}

CREATIVE VISUAL STYLE: ${designEnhancement.visualStyle}
CREATIVE ELEMENTS TO INCORPORATE: ${designEnhancement.creativeElements.join(', ')}
BUSINESS CREATIVE INSIGHTS: ${input.creativeContext.businessInsights?.creativePotential?.slice(0, 3).join(', ') || 'Professional excellence'}
EMOTIONAL DESIGN TONE: ${input.creativeContext.tone} with ${input.creativeContext.style} approach
CREATIVE FRAMEWORK: ${input.creativeContext.framework} storytelling structure

ANTI-GENERIC REQUIREMENTS:
- NO template-like designs or stock photo aesthetics
- NO boring business layouts or predictable compositions
- NO generic color schemes or uninspiring visual elements
- CREATE something memorable, unique, and emotionally engaging
- USE unexpected visual metaphors and creative storytelling
- INCORPORATE cultural elements naturally and authentically
- DESIGN with emotional intelligence and creative sophistication
`;
        }
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL,
            generationConfig: {
                temperature: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.temperature,
                topP: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topP,
                topK: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.topK,
                maxOutputTokens: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revo10Config"].promptSettings.maxTokens
            }
        });
        // Build advanced professional design prompt
        const brandInfo = input.location ? ` based in ${input.location}` : '';
        const colorScheme = `Primary: ${input.primaryColor} (60% dominant), Accent: ${input.accentColor || '#1E40AF'} (30% secondary), Background: ${input.backgroundColor || '#FFFFFF'} (10% highlights)`;
        const logoInstruction = input.logoDataUrl ? 'Use the provided brand logo (do NOT create new logo - integrate existing one naturally)' : 'Create professional design without logo overlay';
        // Prepare structured content display with hierarchy
        const contentStructure = [];
        if (input.headline) contentStructure.push(`PRIMARY (Largest, most prominent): "${input.headline}"`);
        if (input.subheadline) contentStructure.push(`SECONDARY (Medium, supporting): "${input.subheadline}"`);
        if (input.callToAction) contentStructure.push(`CTA (Bold, action-oriented, prominent like "PAYA: YOUR FUTURE, NOW!" style): "${input.callToAction}"`);
        // 🎯 CTA PROMINENCE INSTRUCTIONS (like Paya example)
        const ctaInstructions = input.callToAction ? `

🎯 CRITICAL CTA DISPLAY REQUIREMENTS (LIKE PAYA EXAMPLE):
- The CTA "${input.callToAction}" MUST be displayed prominently on the design
- Make it BOLD, LARGE, and VISUALLY STRIKING like "PAYA: YOUR FUTURE, NOW!"
- Use high contrast colors to make the CTA stand out
- Position it prominently - top, center, or as a banner across the design
- Make the CTA text the MAIN FOCAL POINT of the design
- Use typography that commands attention - bold, modern, impactful
- Add visual elements (borders, backgrounds, highlights) to emphasize the CTA
- The CTA should be the FIRST thing people notice when they see the design
- Make it look like a professional marketing campaign CTA
- Ensure it's readable from mobile devices - minimum 32px equivalent font size
- EXAMPLE STYLE: Like "PAYA: YOUR FUTURE, NOW!" - bold, prominent, unmissable
    ` : '';
        // Get advanced design features
        const businessDesignDNA = getBusinessDesignDNA(input.businessType);
        const platformOptimization = getPlatformOptimization(input.platform);
        const shouldIncludePeople = shouldIncludePeopleInDesign(input.businessType, input.location || 'Global', input.visualStyle);
        const peopleInstructions = shouldIncludePeople ? getAdvancedPeopleInstructions(input.businessType, input.location || 'Global') : '';
        const culturalContext = getLocalCulturalContext(input.location || 'Global');
        // Generate human-like design variation for authentic, creative designs
        const designRandomSeed = Math.floor(Math.random() * 10000) + Date.now();
        const designSeed = designRandomSeed % 10000;
        const designVariations = getHumanDesignVariations(designSeed);
        // NEW: Get industry intelligence and creativity framework
        const industryIntel = getIndustryDesignIntelligence(input.businessType);
        const creativityFramework = getEnhancedCreativityFramework(input.businessType, designVariations.style, designSeed);
        let imagePrompt = `🎨 Create a ${designVariations.style.toLowerCase()} social media design for ${input.businessName} that looks completely different from typical business posts and feels genuinely human-made.

BUSINESS CONTEXT:
- Business: ${input.businessName} (${input.businessType})
- Platform: ${input.platform}
- Message: ${input.imageText}
- Location: ${input.location || 'Global'}

${ctaInstructions}

TEXT CONTENT TO DISPLAY:
${contentStructure.map((item)=>`- ${item}`).join('\n')}

DESIGN APPROACH:
- Create a design that's VISUALLY APPEALING and engaging
- Focus on the specific style: ${designVariations.style}
- Make it look genuinely different from other design types
- Each design type should have its own unique visual language
- **MOST IMPORTANT: Make it look like a human designer made it, not AI**
- **CRITICAL: Include ALL text content listed above in the design**

VISUAL STYLE:
- ${businessDesignDNA}
- ${platformOptimization}
- **SPECIFIC STYLE REQUIREMENTS: ${designVariations.description}**
- Use colors and elements that match this specific style
- Typography should match the style's mood and approach

🌍 SUBTLE LOCAL TOUCH (NOT OVERWHELMING):
- ${culturalContext}
- **Keep cultural elements subtle and natural - don't force them**
- Use local colors and textures naturally, not as obvious cultural markers
- Make it feel authentic to the location without being stereotypical
- Focus on the design style first, local elements second

DESIGN VARIATION:
**STYLE: ${designVariations.style}**
- Layout: ${designVariations.layout}
- Composition: ${designVariations.composition}
- Mood: ${designVariations.mood}
- Elements: ${designVariations.elements}

KEY DESIGN PRINCIPLES:
1. **STYLE-SPECIFIC APPROACH** - Follow the exact style requirements for ${designVariations.style}
2. **VISUAL UNIQUENESS** - Make this look completely different from other design types
3. **STYLE AUTHENTICITY** - If it's watercolor, make it look like real watercolor; if it's meme-style, make it look like a real meme
4. **HUMAN TOUCH** - Make it look like a human designer made it, not AI
5. **BUSINESS APPROPRIATENESS** - Keep it professional while being creative

WHAT TO AVOID:
- Overly complex layouts
- Too many competing elements
- Boring, generic business designs
- Poor contrast or readability
- Outdated design styles
- **MOST IMPORTANT: Don't make this look like the other design types - each should be genuinely unique**
- **AVOID: Overly perfect, symmetrical, AI-generated looking designs**
- **AVOID: Forced cultural elements that feel stereotypical**

WHAT TO INCLUDE:
- **Style-specific elements** that match ${designVariations.style}
- **Unique visual approach** for this specific style
- **Subtle local touches** that feel natural, not forced
- **Human imperfections** - slight asymmetry, natural spacing, organic feel
- **Style-appropriate typography** and layout

TECHNICAL REQUIREMENTS:
- Resolution: 2048x2048 pixels
- Format: Square (1:1)
- Text must be readable on mobile
- Logo integration should look natural

🎨 GOAL: Create a ${designVariations.style.toLowerCase()} design that looks completely different from other design types while feeling genuinely human-made. Focus on the specific style requirements, make it unique, and add subtle local touches without being overwhelming. The design should look like a skilled human designer created it, not AI.`;
        // NEW: Enhance with industry intelligence and creativity
        imagePrompt = enhanceDesignWithIndustryIntelligence(imagePrompt, input.businessType, designVariations.style, designSeed);
        // Inject multiple layers of human creativity to force AI out of its patterns
        imagePrompt = injectHumanImperfections(imagePrompt, designSeed);
        imagePrompt = injectCreativeRebellion(imagePrompt, designSeed);
        imagePrompt = addArtisticConstraints(imagePrompt, designSeed);
        if (input.creativeContext) {}
        // Prepare the generation request with logo if available
        const generationParts = [
            'You are a skilled graphic designer who creates visually appealing social media designs. Focus on creating designs that people actually want to engage with - clean, modern, and appealing. Keep it simple and focus on visual impact.',
            imagePrompt
        ];
        // If logo is provided, include it in the generation
        if (input.logoDataUrl) {
            // Extract the base64 data and mime type from the data URL
            const logoMatch = input.logoDataUrl.match(/^data:([^;]+);base64,(.+)$/);
            if (logoMatch) {
                const [, mimeType, base64Data] = logoMatch;
                generationParts.push({
                    inlineData: {
                        data: base64Data,
                        mimeType: mimeType
                    }
                });
                // Update the prompt to reference the provided logo
                const logoPrompt = `\n\nIMPORTANT: Use the provided logo image above in your design. Integrate it naturally into the layout - do not create a new logo. The logo should be prominently displayed but not overwhelming the design.`;
                generationParts[1] = imagePrompt + logoPrompt;
            } else {}
        }
        const result = await model.generateContent(generationParts);
        const response = await result.response;
        // Extract image data from Gemini response
        const parts = response.candidates?.[0]?.content?.parts || [];
        let imageUrl = '';
        for (const part of parts){
            if (part.inlineData) {
                const imageData = part.inlineData.data;
                const mimeType = part.inlineData.mimeType;
                imageUrl = `data:${mimeType};base64,${imageData}`;
                break;
            }
        }
        if (!imageUrl) {
            // Fallback: try to get text response if no image data
            const textResponse = response.text();
            throw new Error('No image data generated by Gemini 2.5 Flash Image Preview');
        }
        return {
            imageUrl: imageUrl,
            aspectRatio: '1:1',
            resolution: '2048x2048',
            quality: 'enhanced'
        };
    } catch (error) {
        throw new Error(`Revo 1.0 image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function checkRevo10Health() {
    try {
        const model = ai.getGenerativeModel({
            model: REVO_1_0_MODEL
        });
        const result = await model.generateContent('Hello');
        const response = await result.response;
        return {
            healthy: true,
            model: REVO_1_0_MODEL,
            response: response.text().substring(0, 50) + '...',
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return {
            healthy: false,
            model: REVO_1_0_MODEL,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        };
    }
}
function getRevo10ServiceInfo() {
    return {
        model: REVO_1_0_MODEL,
        version: '1.0.0',
        status: 'enhanced',
        aiService: 'gemini-2.5-flash-image-preview',
        capabilities: [
            'Enhanced content generation',
            'High-resolution image support (2048x2048)',
            'Perfect text rendering',
            'Advanced AI capabilities',
            'Enhanced brand consistency'
        ],
        pricing: {
            contentGeneration: 1.5,
            designGeneration: 1.5,
            tier: 'enhanced'
        },
        lastUpdated: '2025-01-27'
    };
}
// NEW: Enhanced local language and cultural context generator
function generateLocalLanguageContext(location) {
    const languageContexts = {
        'kenya': {
            primaryLanguage: 'Swahili & English',
            commonPhrases: [
                'Karibu',
                'Asante',
                'Jambo',
                'Mzuri sana'
            ],
            businessTerms: [
                'Biashara',
                'Mradi',
                'Kazi',
                'Ushirika'
            ],
            culturalNuances: 'Warm hospitality, community-first approach, respect for elders',
            marketingStyle: 'Personal, relationship-focused, community-oriented',
            localExpressions: [
                'Tuko pamoja',
                'Kazi yetu',
                'Jitihada zetu'
            ]
        },
        'nigeria': {
            primaryLanguage: 'English, Hausa, Yoruba, Igbo',
            commonPhrases: [
                'Oga',
                'Abeg',
                'Wetin dey happen',
                'How far'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Money',
                'Success'
            ],
            culturalNuances: 'Entrepreneurial spirit, networking culture, achievement focus',
            marketingStyle: 'Direct, motivational, success-oriented',
            localExpressions: [
                'No shaking',
                'I go do am',
                'We dey here'
            ]
        },
        'south africa': {
            primaryLanguage: 'English, Afrikaans, Zulu, Xhosa',
            commonPhrases: [
                'Howzit',
                'Lekker',
                'Ja',
                'Eish'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Diverse culture, innovation focus, global perspective',
            marketingStyle: 'Professional, inclusive, forward-thinking',
            localExpressions: [
                'Ubuntu',
                'Together we can',
                'Moving forward'
            ]
        },
        'ghana': {
            primaryLanguage: 'English, Twi, Ga, Ewe',
            commonPhrases: [
                'Akwaaba',
                'Medaase',
                'Yoo',
                'Chale'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Money',
                'Success'
            ],
            culturalNuances: 'Hospitality, respect, community values',
            marketingStyle: 'Warm, respectful, community-focused',
            localExpressions: [
                'Sankofa',
                'Unity in diversity',
                'Forward together'
            ]
        },
        'uganda': {
            primaryLanguage: 'English, Luganda, Runyankole',
            commonPhrases: [
                'Oli otya',
                'Webale',
                'Kale',
                'Nja'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Friendly, welcoming, community spirit',
            marketingStyle: 'Friendly, approachable, community-oriented',
            localExpressions: [
                'Tugende',
                'Together we grow',
                'Community first'
            ]
        },
        'tanzania': {
            primaryLanguage: 'Swahili & English',
            commonPhrases: [
                'Karibu',
                'Asante',
                'Jambo',
                'Mzuri'
            ],
            businessTerms: [
                'Biashara',
                'Kazi',
                'Mradi',
                'Ushirika'
            ],
            culturalNuances: 'Peaceful, community-focused, natural beauty appreciation',
            marketingStyle: 'Peaceful, natural, community-oriented',
            localExpressions: [
                'Uhuru na Umoja',
                'Peace and unity',
                'Natural beauty'
            ]
        },
        'ethiopia': {
            primaryLanguage: 'Amharic & English',
            commonPhrases: [
                'Selam',
                'Amesegenalu',
                'Endet',
                'Tena yistilign'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Ancient culture, hospitality, coffee culture',
            marketingStyle: 'Traditional, hospitable, culturally rich',
            localExpressions: [
                'Ethiopia first',
                'Coffee culture',
                'Ancient wisdom'
            ]
        },
        'rwanda': {
            primaryLanguage: 'Kinyarwanda, French & English',
            commonPhrases: [
                'Murakoze',
                'Amahoro',
                'Urugero',
                'Nta kibazo'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Innovation, cleanliness, community unity',
            marketingStyle: 'Innovative, clean, community-focused',
            localExpressions: [
                'Agaciro',
                'Dignity',
                'Unity and reconciliation'
            ]
        },
        'default': {
            primaryLanguage: 'English',
            commonPhrases: [
                'Hello',
                'Thank you',
                'Welcome',
                'Great'
            ],
            businessTerms: [
                'Business',
                'Work',
                'Success',
                'Growth'
            ],
            culturalNuances: 'Professional, friendly, community-oriented',
            marketingStyle: 'Professional, friendly, community-focused',
            localExpressions: [
                'Community first',
                'Quality service',
                'Local expertise'
            ]
        }
    };
    const locationKey = location.toLowerCase();
    for (const [key, context] of Object.entries(languageContexts)){
        if (locationKey.includes(key)) {
            return context;
        }
    }
    return languageContexts['default'];
}
// NEW: Advanced climate insights for business relevance
function generateClimateInsights(location, businessType) {
    const season = getSeason();
    const climateData = {
        'Spring': {
            businessImpact: 'Renewal and growth opportunities, seasonal business preparation',
            contentOpportunities: 'Fresh starts, new beginnings, seasonal preparation, growth themes',
            businessSuggestions: 'Launch new services, seasonal promotions, growth campaigns',
            localAdaptations: 'Spring cleaning services, seasonal menu changes, outdoor activities'
        },
        'Summer': {
            businessImpact: 'High energy and outdoor activities, peak business season',
            contentOpportunities: 'Vibrant colors, active lifestyle, summer solutions, outdoor themes',
            businessSuggestions: 'Summer specials, outdoor events, seasonal products',
            localAdaptations: 'Summer festivals, outdoor dining, seasonal services'
        },
        'Fall': {
            businessImpact: 'Planning and preparation, harvest and results focus',
            contentOpportunities: 'Preparation themes, results celebration, autumn aesthetics',
            businessSuggestions: 'Year-end planning, results showcase, preparation services',
            localAdaptations: 'Harvest celebrations, planning services, year-end reviews'
        },
        'Winter': {
            businessImpact: 'Strategic planning and indoor focus, reflection period',
            contentOpportunities: 'Planning themes, strategy focus, indoor solutions',
            businessSuggestions: 'Strategic planning, indoor services, year planning',
            localAdaptations: 'Indoor events, planning services, strategic consultations'
        }
    };
    // Add business-specific climate insights
    const businessClimateInsights = {
        'restaurant': {
            seasonalMenu: `${season} seasonal ingredients and dishes`,
            weatherAdaptation: `${season === 'Summer' ? 'Cooling beverages and light meals' : season === 'Winter' ? 'Warm comfort foods' : 'Seasonal specialties'}`,
            businessStrategy: `${season === 'Summer' ? 'Outdoor dining and seasonal menus' : 'Indoor comfort and seasonal specialties'}`
        },
        'fitness': {
            seasonalActivities: `${season === 'Summer' ? 'Outdoor workouts and water activities' : season === 'Winter' ? 'Indoor training and winter sports' : 'Seasonal fitness programs'}`,
            weatherAdaptation: `${season === 'Summer' ? 'Early morning and evening sessions' : 'Indoor and weather-appropriate activities'}`,
            businessStrategy: `${season === 'Summer' ? 'Outdoor fitness programs' : 'Indoor training focus'}`
        },
        'retail': {
            seasonalProducts: `${season} fashion and lifestyle products`,
            weatherAdaptation: `${season === 'Summer' ? 'Light clothing and outdoor gear' : season === 'Winter' ? 'Warm clothing and indoor items' : 'Seasonal essentials'}`,
            businessStrategy: `${season === 'Summer' ? 'Summer sales and outdoor products' : 'Seasonal collections and indoor focus'}`
        },
        'default': {
            seasonalFocus: `${season} business opportunities and seasonal services`,
            weatherAdaptation: `${season === 'Summer' ? 'Outdoor and seasonal services' : 'Indoor and year-round services'}`,
            businessStrategy: `${season} business strategies and seasonal promotions`
        }
    };
    const baseClimate = climateData[season];
    const businessClimate = businessClimateInsights[businessType.toLowerCase()] || businessClimateInsights['default'];
    return {
        season: season,
        businessImpact: baseClimate.businessImpact,
        contentOpportunities: baseClimate.contentOpportunities,
        businessSuggestions: baseClimate.businessSuggestions,
        localAdaptations: baseClimate.localAdaptations,
        businessSpecific: businessClimate,
        marketingAngle: `Leverage ${season.toLowerCase()} opportunities for ${businessType} business growth`
    };
}
// NEW: Real-time trending topics generator (can be enhanced with actual social media APIs)
function generateTrendingTopics(businessType, location, platform) {
    const platformTrends = {
        'Instagram': [
            {
                topic: 'Visual storytelling trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Authentic content creation',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Reels and short-form video',
                category: 'Format',
                relevance: 'medium'
            }
        ],
        'LinkedIn': [
            {
                topic: 'Professional networking trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Industry thought leadership',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Career development insights',
                category: 'Professional',
                relevance: 'medium'
            }
        ],
        'Facebook': [
            {
                topic: 'Community building strategies',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Local business networking',
                category: 'Community',
                relevance: 'high'
            },
            {
                topic: 'Family-friendly content',
                category: 'Content',
                relevance: 'medium'
            }
        ],
        'Twitter': [
            {
                topic: 'Real-time conversation trends',
                category: 'Platform',
                relevance: 'high'
            },
            {
                topic: 'Viral content strategies',
                category: 'Content',
                relevance: 'high'
            },
            {
                topic: 'Trending hashtags',
                category: 'Engagement',
                relevance: 'medium'
            }
        ]
    };
    const businessTrends = {
        'restaurant': [
            {
                topic: 'Local food culture trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Sustainable dining practices',
                category: 'Trends',
                relevance: 'high'
            },
            {
                topic: 'Food delivery innovations',
                category: 'Technology',
                relevance: 'medium'
            }
        ],
        'technology': [
            {
                topic: 'AI and automation trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Digital transformation',
                category: 'Business',
                relevance: 'high'
            },
            {
                topic: 'Remote work solutions',
                category: 'Workplace',
                relevance: 'medium'
            }
        ],
        'healthcare': [
            {
                topic: 'Telehealth adoption',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Preventive healthcare',
                category: 'Wellness',
                relevance: 'high'
            },
            {
                topic: 'Mental health awareness',
                category: 'Health',
                relevance: 'medium'
            }
        ],
        'fitness': [
            {
                topic: 'Home workout trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Mental wellness integration',
                category: 'Wellness',
                relevance: 'high'
            },
            {
                topic: 'Community fitness challenges',
                category: 'Engagement',
                relevance: 'medium'
            }
        ],
        'finance': [
            {
                topic: 'Digital banking trends',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Financial literacy',
                category: 'Education',
                relevance: 'high'
            },
            {
                topic: 'Investment opportunities',
                category: 'Wealth',
                relevance: 'medium'
            }
        ],
        'education': [
            {
                topic: 'Online learning platforms',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Skill development trends',
                category: 'Learning',
                relevance: 'high'
            },
            {
                topic: 'Personalized education',
                category: 'Innovation',
                relevance: 'medium'
            }
        ],
        'retail': [
            {
                topic: 'E-commerce growth',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Omnichannel shopping',
                category: 'Customer',
                relevance: 'high'
            },
            {
                topic: 'Sustainable products',
                category: 'Trends',
                relevance: 'medium'
            }
        ],
        'real estate': [
            {
                topic: 'Virtual property tours',
                category: 'Industry',
                relevance: 'high'
            },
            {
                topic: 'Sustainable properties',
                category: 'Trends',
                relevance: 'high'
            },
            {
                topic: 'Investment opportunities',
                category: 'Market',
                relevance: 'medium'
            }
        ],
        'default': [
            {
                topic: 'Digital transformation trends',
                category: 'Business',
                relevance: 'high'
            },
            {
                topic: 'Customer experience optimization',
                category: 'Strategy',
                relevance: 'high'
            },
            {
                topic: 'Local business growth',
                category: 'Community',
                relevance: 'medium'
            }
        ]
    };
    const platformSpecific = platformTrends[platform] || platformTrends['Instagram'];
    const businessSpecific = businessTrends[businessType.toLowerCase()] || businessTrends['default'];
    const localTrends = [
        {
            topic: `${location} business growth`,
            category: 'Local',
            relevance: 'high'
        },
        {
            topic: `${location} community development`,
            category: 'Community',
            relevance: 'high'
        },
        {
            topic: `${location} economic trends`,
            category: 'Local',
            relevance: 'medium'
        }
    ];
    return [
        ...platformSpecific,
        ...businessSpecific,
        ...localTrends
    ].slice(0, 5);
}
// NEW: Local news and market insights generator
function generateLocalNewsContext(businessType, location) {
    const newsInsights = [
        {
            type: 'Local Market',
            headline: `${location} business environment update`,
            impact: 'Local market conditions affecting business opportunities',
            businessRelevance: 'Market positioning and strategic planning',
            contentAngle: 'Local market expertise and insights'
        },
        {
            type: 'Industry Trends',
            headline: `${businessType} industry developments in ${location}`,
            impact: 'Industry-specific opportunities and challenges',
            businessRelevance: 'Competitive positioning and service innovation',
            contentAngle: 'Industry leadership and local expertise'
        },
        {
            type: 'Community Events',
            headline: `${location} community and business events`,
            impact: 'Networking and community engagement opportunities',
            businessRelevance: 'Community involvement and local partnerships',
            contentAngle: 'Community connection and local engagement'
        },
        {
            type: 'Economic Update',
            headline: `${location} economic indicators and business climate`,
            impact: 'Business planning and investment decisions',
            businessRelevance: 'Strategic planning and market timing',
            contentAngle: 'Economic expertise and market insights'
        }
    ];
    return newsInsights.slice(0, 3);
}
}}),
"[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Content Generator
 * Handles content generation for the stable foundation model
 */ __turbopack_context__.s({
    "Revo10ContentGenerator": (()=>Revo10ContentGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript)");
;
class Revo10ContentGenerator {
    modelId = 'revo-1.0';
    /**
   * Generate content using Revo 1.0 specifications
   */ async generateContent(request) {
        const startTime = Date.now();
        try {
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid content generation request for Revo 1.0');
            }
            // Prepare generation parameters for Revo 1.0
            const generationParams = this.prepareGenerationParams(request);
            // Generate content using Revo 1.0 service with Gemini 2.5 Flash Image Preview
            const postDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$revo$2d$1$2e$0$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo10Content"])({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                location: generationParams.location || 'Location',
                platform: generationParams.variants[0]?.platform || 'instagram',
                writingTone: generationParams.writingTone || 'professional',
                contentThemes: generationParams.contentThemes || [],
                targetAudience: generationParams.targetAudience || 'General',
                services: generationParams.services || '',
                keyFeatures: generationParams.keyFeatures || '',
                competitiveAdvantages: generationParams.competitiveAdvantages || '',
                dayOfWeek: generationParams.dayOfWeek || 'Monday',
                currentDate: generationParams.currentDate || new Date().toLocaleDateString(),
                primaryColor: generationParams.primaryColor,
                visualStyle: generationParams.visualStyle
            });
            // Generate image using the catchy words and brand profile data
            const { generateRevo10Image } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare structured text for image
            const imageTextComponents = [];
            if (postDetails.catchyWords) imageTextComponents.push(postDetails.catchyWords);
            if (postDetails.subheadline) imageTextComponents.push(postDetails.subheadline);
            if (postDetails.callToAction) imageTextComponents.push(postDetails.callToAction);
            const structuredImageText = imageTextComponents.join(' | ');
            // Get real-time context for enhanced design
            const realTimeContext = postDetails.realTimeContext || null;
            const imageResult = await generateRevo10Image({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                accentColor: generationParams.accentColor || '#1E40AF',
                backgroundColor: generationParams.backgroundColor || '#FFFFFF',
                imageText: structuredImageText,
                designDescription: `Professional ${generationParams.businessType} content with structured headline, subheadline, and CTA for ${generationParams.variants[0]?.platform || 'instagram'}`,
                logoDataUrl: generationParams.logoDataUrl,
                location: generationParams.location,
                headline: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                realTimeContext: realTimeContext,
                creativeContext: postDetails.creativeContext // 🎨 Pass creative context to image generation
            });
            // Update variants with the generated image
            postDetails.variants = postDetails.variants.map((variant)=>({
                    ...variant,
                    imageUrl: imageResult.imageUrl
                }));
            // Create the generated post
            const generatedPost = {
                id: new Date().toISOString(),
                date: new Date().toISOString(),
                content: postDetails.content,
                hashtags: postDetails.hashtags,
                status: 'generated',
                variants: postDetails.variants,
                catchyWords: postDetails.catchyWords,
                subheadline: postDetails.subheadline,
                callToAction: postDetails.callToAction,
                // Revo 1.0 doesn't include advanced features
                contentVariants: undefined,
                hashtagAnalysis: undefined,
                marketIntelligence: undefined,
                localContext: undefined,
                metadata: {
                    modelId: this.modelId,
                    modelVersion: '1.0.0',
                    generationType: 'standard',
                    processingTime: Date.now() - startTime,
                    qualityLevel: 'standard'
                }
            };
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateQualityScore(generatedPost);
            return {
                success: true,
                data: generatedPost,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 1.5,
                    enhancementsApplied: [
                        'enhanced-optimization',
                        'platform-formatting',
                        'gemini-2.5-flash-image'
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Validate content generation request for Revo 1.0
   */ validateRequest(request) {
        // Check required fields
        if (!request.profile || !request.platform) {
            return false;
        }
        // Check if profile has minimum required information
        if (!request.profile.businessType || !request.profile.businessName) {
            return false;
        }
        // Revo 1.0 doesn't support artifacts
        if (request.artifactIds && request.artifactIds.length > 0) {}
        return true;
    }
    /**
   * Prepare generation parameters optimized for Revo 1.0
   */ prepareGenerationParams(request) {
        const { profile, platform, brandConsistency } = request;
        const today = new Date();
        // Convert arrays to strings for AI processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        return {
            businessName: profile.businessName || profile.name || 'Business',
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: brandConsistency?.strictConsistency ? profile.designExamples || [] : [],
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek: today.toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: today.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            variants: [
                {
                    platform: platform,
                    aspectRatio: '1:1'
                }
            ],
            services: servicesString,
            targetAudience: profile.targetAudience,
            keyFeatures: keyFeaturesString,
            competitiveAdvantages: competitiveAdvantagesString,
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            },
            // Revo 1.0 specific constraints (updated to match config)
            modelConstraints: {
                maxComplexity: 'enhanced',
                enhancedFeatures: true,
                realTimeContext: true,
                trendingTopics: true,
                artifactSupport: false // Keep disabled for Revo 1.0
            }
        };
    }
    /**
   * Calculate quality score for generated content
   */ calculateQualityScore(post) {
        let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)
        // Content quality checks
        if (post.content && post.content.length > 50) score += 1;
        if (post.content && post.content.length > 100) score += 0.5;
        // Hashtag quality
        if (post.hashtags && post.hashtags.length >= 5) score += 1;
        if (post.hashtags && post.hashtags.length >= 10) score += 0.5;
        // Catchy words presence
        if (post.catchyWords && post.catchyWords.trim().length > 0) score += 1;
        // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)
        if (post.variants && post.variants.length > 0 && post.variants[0].imageUrl) {
            score += 1.5; // Increased from 1 for better image quality
        }
        // Cap at 10
        return Math.min(score, 10);
    }
    /**
   * Health check for content generator
   */ async healthCheck() {
        try {
            // Check if we can access the AI service
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'content',
            capabilities: [
                'Enhanced content generation with Gemini 2.5 Flash Image Preview',
                'Platform-specific formatting',
                'Hashtag generation',
                'Catchy words creation',
                'Brand consistency (enhanced)',
                'Perfect text rendering',
                'High-resolution image support'
            ],
            limitations: [
                'No real-time context',
                'No trending topics',
                'No artifact support',
                'Enhanced quality optimization',
                'Limited customization'
            ],
            averageProcessingTime: '20-30 seconds (enhanced for quality)',
            qualityRange: '8-9/10 (upgraded from 6-8/10)',
            costPerGeneration: 1.5 // Upgraded from 1 for enhanced capabilities
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Design Generator
 * Handles design generation for the stable foundation model
 */ __turbopack_context__.s({
    "Revo10DesignGenerator": (()=>Revo10DesignGenerator)
});
class Revo10DesignGenerator {
    modelId = 'revo-1.0';
    /**
   * Generate design using Revo 1.0 specifications
   */ async generateDesign(request) {
        const startTime = Date.now();
        try {
            // Validate request
            if (!this.validateRequest(request)) {
                throw new Error('Invalid design generation request for Revo 1.0');
            }
            // Generate design using basic Gemini 2.0 approach
            const designResult = await this.generateBasicDesign(request);
            const processingTime = Date.now() - startTime;
            const qualityScore = this.calculateQualityScore(designResult);
            return {
                success: true,
                data: designResult,
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore,
                    creditsUsed: 1.5,
                    enhancementsApplied: [
                        'enhanced-styling',
                        'brand-colors',
                        'platform-optimization',
                        'gemini-2.5-flash-image'
                    ]
                }
            };
        } catch (error) {
            const processingTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    modelId: this.modelId,
                    processingTime,
                    qualityScore: 0,
                    creditsUsed: 0,
                    enhancementsApplied: []
                }
            };
        }
    }
    /**
   * Generate basic design using Gemini 2.0
   */ async generateBasicDesign(request) {
        try {
            // Import the basic generation flow
            const { generateRevo10Design } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            // Prepare image text
            let imageText;
            if (typeof request.imageText === 'string') {
                imageText = request.imageText;
            } else {
                // Combine components for Revo 1.0 (simpler approach)
                imageText = request.imageText.catchyWords;
                if (request.imageText.subheadline) {
                    imageText += '\n' + request.imageText.subheadline;
                }
            }
            // Create a simplified generation request
            const generationParams = {
                businessType: request.businessType,
                location: request.brandProfile.location || '',
                writingTone: request.brandProfile.writingTone || 'professional',
                contentThemes: request.brandProfile.contentThemes || '',
                visualStyle: request.visualStyle,
                logoDataUrl: request.brandProfile.logoDataUrl,
                designExamples: request.brandConsistency?.strictConsistency ? request.brandProfile.designExamples || [] : [],
                primaryColor: request.brandProfile.primaryColor,
                accentColor: request.brandProfile.accentColor,
                backgroundColor: request.brandProfile.backgroundColor,
                dayOfWeek: new Date().toLocaleDateString('en-US', {
                    weekday: 'long'
                }),
                currentDate: new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }),
                variants: [
                    {
                        platform: request.platform,
                        aspectRatio: '1:1'
                    }
                ],
                services: '',
                targetAudience: request.brandProfile.targetAudience || '',
                keyFeatures: '',
                competitiveAdvantages: '',
                brandConsistency: request.brandConsistency || {
                    strictConsistency: false,
                    followBrandColors: true
                }
            };
            // First generate design description
            const designResult = await generateRevo10Design({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                accentColor: generationParams.accentColor || '#1E40AF',
                backgroundColor: generationParams.backgroundColor || '#FFFFFF',
                imageText: imageText || 'Your Text Here'
            });
            // Then generate the actual image using the design description
            const { generateRevo10Image } = await __turbopack_context__.r("[project]/src/ai/revo-1.0-service.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const imageResult = await generateRevo10Image({
                businessType: generationParams.businessType,
                businessName: generationParams.businessName || 'Business',
                platform: generationParams.variants[0]?.platform || 'instagram',
                visualStyle: generationParams.visualStyle || 'modern',
                primaryColor: generationParams.primaryColor || '#3B82F6',
                imageText: imageText || 'Your Text Here',
                designDescription: designResult.design
            });
            // Return the complete result with actual image URL
            return {
                platform: request.platform,
                imageUrl: imageResult.imageUrl,
                caption: imageText,
                hashtags: [],
                design: designResult.design,
                aspectRatio: imageResult.aspectRatio,
                resolution: imageResult.resolution,
                quality: imageResult.quality
            };
        } catch (error) {
            // Return a fallback variant
            return {
                platform: request.platform,
                imageUrl: '',
                caption: typeof request.imageText === 'string' ? request.imageText : request.imageText.catchyWords,
                hashtags: []
            };
        }
    }
    /**
   * Validate design generation request for Revo 1.0
   */ validateRequest(request) {
        // Check required fields
        if (!request.businessType || !request.platform || !request.brandProfile) {
            return false;
        }
        // Check image text
        if (!request.imageText) {
            return false;
        }
        // Revo 1.0 only supports 1:1 aspect ratio
        // We don't enforce this here as the generator will handle it
        // Warn about unsupported features
        if (request.artifactInstructions) {}
        return true;
    }
    /**
   * Calculate quality score for generated design
   */ calculateQualityScore(variant) {
        let score = 7; // Base score (upgraded from 5 for Gemini 2.5 Flash Image Preview)
        // Image generation success (enhanced for Gemini 2.5 Flash Image Preview)
        if (variant.imageUrl && variant.imageUrl.length > 0) {
            score += 2.5; // Increased from 2 for better image quality
        }
        // Caption quality
        if (variant.caption && variant.caption.length > 10) {
            score += 1;
        }
        // Hashtags presence
        if (variant.hashtags && variant.hashtags.length > 0) {
            score += 1;
        }
        // Platform optimization (basic check)
        if (variant.platform) {
            score += 0.5;
        }
        // Revo 1.0 now has higher quality ceiling due to Gemini 2.5 Flash Image Preview
        return Math.min(score, 9.0);
    }
    /**
   * Health check for design generator
   */ async healthCheck() {
        try {
            // Check if we can access the AI service
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get generator-specific information
   */ getGeneratorInfo() {
        return {
            modelId: this.modelId,
            type: 'design',
            capabilities: [
                'Enhanced image generation with Gemini 2.5 Flash Image Preview',
                '1:1 aspect ratio only',
                'Brand color integration',
                'Logo placement',
                'Platform optimization',
                'Text overlay (enhanced)',
                'Perfect text rendering',
                'High-resolution 2048x2048 output'
            ],
            limitations: [
                'Single aspect ratio (1:1)',
                'No artifact support',
                'Enhanced styling options',
                'Limited customization',
                'High-resolution support'
            ],
            supportedPlatforms: [
                'Instagram',
                'Facebook',
                'Twitter',
                'LinkedIn'
            ],
            supportedAspectRatios: [
                '1:1'
            ],
            averageProcessingTime: '25-35 seconds (enhanced for quality)',
            qualityRange: '7-9/10 (upgraded from 5-7.5/10)',
            costPerGeneration: 1.5,
            resolution: '2048x2048 (upgraded from 1024x1024)'
        };
    }
    /**
   * Get supported features for this design generator
   */ getSupportedFeatures() {
        return {
            aspectRatios: [
                '1:1'
            ],
            textOverlay: 'enhanced',
            brandIntegration: 'standard',
            logoPlacement: true,
            colorCustomization: true,
            templateSupport: false,
            artifactSupport: false,
            advancedStyling: true,
            multipleVariants: false,
            highResolution: true,
            perfectTextRendering: true // NEW: Gemini 2.5 Flash Image Preview feature
        };
    }
}
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 1.0 Model Implementation
 * Standard Model - Stable Foundation
 */ __turbopack_context__.s({
    "Revo10Implementation": (()=>Revo10Implementation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
;
;
;
class Revo10Implementation {
    model;
    contentGenerator;
    designGenerator;
    constructor(){
        try {
            this.model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModelConfig"])('revo-1.0');
            this.contentGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10ContentGenerator"]();
            this.designGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10DesignGenerator"]();
        } catch (error) {
            throw error;
        }
    }
    /**
   * Check if the model is available and ready to use
   */ async isAvailable() {
        try {
            // Check if the underlying AI service (Gemini 2.5 Flash Image Preview) is available
            // For now, we'll assume it's available if we have the API key
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY);
            return hasApiKey;
        } catch (error) {
            return false;
        }
    }
    /**
   * Validate a generation request for this model
   */ validateRequest(request) {
        try {
            // Basic validation
            if (!request || !request.modelId) {
                return false;
            }
            // Check if this is the correct model
            if (request.modelId !== 'revo-1.0') {
                return false;
            }
            // Content generation validation
            if ('profile' in request) {
                const contentRequest = request;
                return !!(contentRequest.profile && contentRequest.platform && contentRequest.profile.businessType);
            }
            // Design generation validation
            if ('businessType' in request) {
                const designRequest = request;
                return !!(designRequest.businessType && designRequest.platform && designRequest.visualStyle && designRequest.brandProfile);
            }
            return false;
        } catch (error) {
            return false;
        }
    }
    /**
   * Get model-specific information
   */ getModelInfo() {
        return {
            id: this.model.id,
            name: this.model.name,
            version: this.model.version,
            description: this.model.description,
            status: this.model.status,
            capabilities: this.model.capabilities,
            pricing: this.model.pricing,
            features: this.model.features,
            strengths: [
                'Reliable and stable performance',
                'Cost-effective for basic needs',
                'Proven track record',
                'Fast processing times',
                'Consistent quality',
                'Enhanced AI capabilities with Gemini 2.5 Flash Image Preview',
                'Perfect text rendering',
                'High-resolution 2048x2048 output',
                'Advanced image generation'
            ],
            limitations: [
                'Limited to 1:1 aspect ratio',
                'No artifact support',
                'Basic brand consistency',
                'No real-time context',
                'No video generation'
            ],
            bestUseCases: [
                'Small businesses starting out',
                'Personal brands',
                'Budget-conscious users',
                'Basic social media content',
                'Consistent daily posting'
            ]
        };
    }
    /**
   * Get performance metrics for this model
   */ async getPerformanceMetrics() {
        return {
            modelId: this.model.id,
            averageProcessingTime: 30000,
            successRate: 0.97,
            averageQualityScore: 8.5,
            costEfficiency: 'high',
            reliability: 'excellent',
            userSatisfaction: 4.5,
            lastUpdated: new Date().toISOString()
        };
    }
    /**
   * Health check for this specific model
   */ async healthCheck() {
        try {
            const isAvailable = await this.isAvailable();
            const contentGeneratorHealthy = await this.contentGenerator.healthCheck?.() ?? true;
            const designGeneratorHealthy = await this.designGenerator.healthCheck?.() ?? true;
            const healthy = isAvailable && contentGeneratorHealthy && designGeneratorHealthy;
            return {
                healthy,
                details: {
                    modelAvailable: isAvailable,
                    contentGenerator: contentGeneratorHealthy,
                    designGenerator: designGeneratorHealthy,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            return {
                healthy: false,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
}
;
;
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$config$2f$model$2d$configs$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/config/model-configs.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo10ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10ContentGenerator"]),
    "Revo10DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Revo10DesignGenerator"]),
    "Revo10Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Revo10Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$design$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/design-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Revo10ContentGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10ContentGenerator"]),
    "Revo10DesignGenerator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10DesignGenerator"]),
    "Revo10Implementation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Revo10Implementation"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$models$2f$versions$2f$revo$2d$1$2e$0$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_ai_415d610f._.js.map