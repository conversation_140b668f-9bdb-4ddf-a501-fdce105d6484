module.exports = {

"[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_node-fetch_src_utils_multipart-parser_7975f0c9.js",
  "server/chunks/ssr/node_modules_0f8e4341._.js",
  "server/chunks/ssr/[root-of-the-server]__02243159._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_cheerio_dist_esm_5eefae71._.js",
  "server/chunks/ssr/node_modules_ce35667b._.js",
  "server/chunks/ssr/node_modules_parse5_dist_a77ceaf0._.js",
  "server/chunks/ssr/node_modules_eb82991d._.js",
  "server/chunks/ssr/node_modules_undici_557f89fb._.js",
  "server/chunks/ssr/node_modules_1a0bc68a._.js",
  "server/chunks/ssr/[root-of-the-server]__174aa890._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_a9285cf6._.js",
  "server/chunks/ssr/[root-of-the-server]__5aad5e3f._.js",
  "server/chunks/ssr/node_modules_ab035ae4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/models/versions/revo-1.0/index.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_5c68708a._.js",
  "server/chunks/ssr/[root-of-the-server]__d3120ecf._.js",
  "server/chunks/ssr/node_modules_7aa2af8e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/models/versions/revo-1.5/index.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__db310d95._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_flows_generate-creative-asset_ts_f14899a5._.js",
  "server/chunks/ssr/src_ai_c5d75105._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_615342a7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript)");
    });
});
}}),

};