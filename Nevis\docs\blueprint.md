# **App Name**: LocalBuzz

## Core Features:

- Content Generation: Generates hyper-local, weather-aware social media content for various platforms using generative AI.
- Content Calendar: Provides a calendar view to display scheduled content, platform-specific formatting, and editing options.
- Dashboard Overview: Offers a dashboard overview to manage content and track basic engagement metrics, facilitating informed decision-making.
- Brand Analysis: Utilizes AI to scrape and analyze the brand's visual style, writing tone, and content themes from existing social media profiles using an analysis tool.
- Local Data Integration: Pulls weather data from OpenWeatherMap API and events data from Eventbrite API to create timely content.
- Image Generation: Generates visuals consistent with the brand's identity using DALL-E 3, and optimizes images for various social media platforms.

## Style Guidelines:

- Primary color: HSL(210, 70%, 50%) - A vibrant blue (#3399FF) to evoke trust and modernity.
- Background color: HSL(210, 20%, 95%) - A light desaturated blue (#F0F8FF) for a clean, professional backdrop.
- Accent color: HSL(180, 60%, 40%) - A teal-green (#33B2B2) to highlight key actions and information.
- Body and headline font: 'PT Sans', a sans-serif offering a modern look with a touch of warmth.
- Use clear and modern icons representing different content types and social platforms.
- Maintain a clean, intuitive layout with clear sections for calendar view, content editing, and analytics.
- Subtle animations on content loading and action confirmations to enhance user experience.