{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/services/rss-feed-service.ts"], "sourcesContent": ["/**\r\n * RSS Feed Service for Trending Content & Social Media Insights\r\n * Fetches and parses RSS feeds to extract trending topics, keywords, and themes\r\n */\r\n\r\nimport { parseStringPromise } from 'xml2js';\r\n\r\nexport interface RSSArticle {\r\n  title: string;\r\n  description: string;\r\n  link: string;\r\n  pubDate: Date;\r\n  category?: string;\r\n  keywords: string[];\r\n  source: string;\r\n}\r\n\r\nexport interface TrendingData {\r\n  keywords: string[];\r\n  topics: string[];\r\n  themes: string[];\r\n  articles: RSSArticle[];\r\n  lastUpdated: Date;\r\n}\r\n\r\nexport class RSSFeedService {\r\n  private cache: Map<string, { data: RSSArticle[]; timestamp: number }> = new Map();\r\n  private readonly cacheTimeout = parseInt(process.env.RSS_CACHE_DURATION || '1800') * 1000; // 30 minutes default\r\n\r\n  private readonly feedUrls = {\r\n    // Social Media & Marketing Trends\r\n    socialMediaToday: process.env.RSS_SOCIAL_MEDIA_TODAY,\r\n    socialMediaExaminer: process.env.RSS_SOCIAL_MEDIA_EXAMINER,\r\n    bufferBlog: process.env.RSS_BUFFER_BLOG,\r\n    hootsuiteBlogs: process.env.RSS_HOOTSUITE_BLOG,\r\n    sproutSocial: process.env.RSS_SPROUT_SOCIAL,\r\n    laterBlog: process.env.RSS_LATER_BLOG,\r\n\r\n    // Trending Topics & News\r\n    googleNewsTrending: process.env.RSS_GOOGLE_NEWS_TRENDING,\r\n    redditPopular: process.env.RSS_REDDIT_POPULAR,\r\n    buzzfeed: process.env.RSS_BUZZFEED,\r\n    twitterTrending: process.env.RSS_TWITTER_TRENDING,\r\n\r\n    // Business & Marketing\r\n    hubspotMarketing: process.env.RSS_HUBSPOT_MARKETING,\r\n    contentMarketingInstitute: process.env.RSS_CONTENT_MARKETING_INSTITUTE,\r\n    marketingProfs: process.env.RSS_MARKETING_PROFS,\r\n    marketingLand: process.env.RSS_MARKETING_LAND,\r\n    neilPatelBlog: process.env.RSS_NEIL_PATEL_BLOG,\r\n\r\n    // Industry News\r\n    techCrunch: process.env.RSS_TECHCRUNCH,\r\n    mashable: process.env.RSS_MASHABLE,\r\n    theVerge: process.env.RSS_THE_VERGE,\r\n    wired: process.env.RSS_WIRED,\r\n\r\n    // Platform-Specific\r\n    instagramBusiness: process.env.RSS_INSTAGRAM_BUSINESS,\r\n    facebookBusiness: process.env.RSS_FACEBOOK_BUSINESS,\r\n    linkedinMarketing: process.env.RSS_LINKEDIN_MARKETING,\r\n    youtubeCreator: process.env.RSS_YOUTUBE_CREATOR,\r\n    tiktokBusiness: process.env.RSS_TIKTOK_BUSINESS,\r\n\r\n    // Analytics & Data\r\n    googleAnalytics: process.env.RSS_GOOGLE_ANALYTICS,\r\n    hootsuiteInsights: process.env.RSS_HOOTSUITE_INSIGHTS,\r\n\r\n    // Design & Creative\r\n    canvaDesignSchool: process.env.RSS_CANVA_DESIGN_SCHOOL,\r\n    adobeBlog: process.env.RSS_ADOBE_BLOG,\r\n    creativeBloq: process.env.RSS_CREATIVE_BLOQ,\r\n\r\n    // Seasonal & Events\r\n    eventbriteBlog: process.env.RSS_EVENTBRITE_BLOG,\r\n  };\r\n\r\n  /**\r\n   * Fetch and parse a single RSS feed\r\n   */\r\n  private async fetchRSSFeed(url: string, sourceName: string): Promise<RSSArticle[]> {\r\n    try {\r\n      // Check cache first\r\n      const cached = this.cache.get(url);\r\n      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\r\n        return cached.data;\r\n      }\r\n\r\n\r\n      const response = await fetch(url, {\r\n        headers: {\r\n          'User-Agent': 'Nevis-AI-Content-Generator/1.0',\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n      }\r\n\r\n      const xmlData = await response.text();\r\n      const parsed = await parseStringPromise(xmlData);\r\n\r\n      const articles: RSSArticle[] = [];\r\n      const items = parsed.rss?.channel?.[0]?.item || parsed.feed?.entry || [];\r\n\r\n      const maxArticles = parseInt(process.env.RSS_MAX_ARTICLES_PER_FEED || '50');\r\n\r\n      for (const item of items.slice(0, maxArticles)) {\r\n        const article: RSSArticle = {\r\n          title: this.extractText(item.title),\r\n          description: this.extractText(item.description || item.summary),\r\n          link: this.extractText(item.link || item.id),\r\n          pubDate: new Date(this.extractText(item.pubDate || item.published) || Date.now()),\r\n          category: this.extractText(item.category),\r\n          keywords: this.extractKeywords(\r\n            this.extractText(item.title) + ' ' + this.extractText(item.description || item.summary)\r\n          ),\r\n          source: sourceName,\r\n        };\r\n\r\n        articles.push(article);\r\n      }\r\n\r\n      // Cache the results\r\n      this.cache.set(url, { data: articles, timestamp: Date.now() });\r\n\r\n      return articles;\r\n\r\n    } catch (error) {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extract text content from RSS item fields\r\n   */\r\n  private extractText(field: any): string {\r\n    if (!field) return '';\r\n\r\n    if (typeof field === 'string') return field;\r\n    if (Array.isArray(field) && field.length > 0) {\r\n      return typeof field[0] === 'string' ? field[0] : field[0]._ || '';\r\n    }\r\n    if (typeof field === 'object' && field._) return field._;\r\n\r\n    return '';\r\n  }\r\n\r\n  /**\r\n   * Extract keywords from text content\r\n   */\r\n  private extractKeywords(text: string): string[] {\r\n    if (!text) return [];\r\n\r\n    // Remove HTML tags and normalize text\r\n    const cleanText = text\r\n      .replace(/<[^>]*>/g, '')\r\n      .toLowerCase()\r\n      .replace(/[^\\w\\s]/g, ' ')\r\n      .replace(/\\s+/g, ' ')\r\n      .trim();\r\n\r\n    // Extract meaningful words (3+ characters, not common stop words)\r\n    const stopWords = new Set([\r\n      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'\r\n    ]);\r\n\r\n    const words = cleanText\r\n      .split(' ')\r\n      .filter(word => word.length >= 3 && !stopWords.has(word))\r\n      .slice(0, 10); // Limit to top 10 keywords per article\r\n\r\n    return Array.from(new Set(words)); // Remove duplicates\r\n  }\r\n\r\n  /**\r\n   * Fetch all RSS feeds and return trending data\r\n   */\r\n  public async getTrendingData(): Promise<TrendingData> {\r\n\r\n    const allArticles: RSSArticle[] = [];\r\n    const fetchPromises: Promise<RSSArticle[]>[] = [];\r\n\r\n    // Fetch all feeds concurrently\r\n    for (const [sourceName, url] of Object.entries(this.feedUrls)) {\r\n      if (url) {\r\n        fetchPromises.push(this.fetchRSSFeed(url, sourceName));\r\n      }\r\n    }\r\n\r\n    const results = await Promise.allSettled(fetchPromises);\r\n\r\n    // Collect all successful results\r\n    results.forEach((result) => {\r\n      if (result.status === 'fulfilled') {\r\n        allArticles.push(...result.value);\r\n      }\r\n    });\r\n\r\n    // Sort articles by publication date (newest first)\r\n    allArticles.sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());\r\n\r\n    // Extract trending keywords and topics\r\n    const allKeywords: string[] = [];\r\n    const allTopics: string[] = [];\r\n    const allThemes: string[] = [];\r\n\r\n    allArticles.forEach(article => {\r\n      allKeywords.push(...article.keywords);\r\n      if (article.title) allTopics.push(article.title);\r\n      if (article.category) allThemes.push(article.category);\r\n    });\r\n\r\n    // Count frequency and get top items\r\n    const keywordCounts = this.getTopItems(allKeywords, 50);\r\n    const topicCounts = this.getTopItems(allTopics, 30);\r\n    const themeCounts = this.getTopItems(allThemes, 20);\r\n\r\n\r\n    return {\r\n      keywords: keywordCounts,\r\n      topics: topicCounts,\r\n      themes: themeCounts,\r\n      articles: allArticles.slice(0, 100), // Return top 100 most recent articles\r\n      lastUpdated: new Date(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get top items by frequency\r\n   */\r\n  private getTopItems(items: string[], limit: number): string[] {\r\n    const counts = new Map<string, number>();\r\n\r\n    items.forEach(item => {\r\n      const normalized = item.toLowerCase().trim();\r\n      if (normalized.length >= 3) {\r\n        counts.set(normalized, (counts.get(normalized) || 0) + 1);\r\n      }\r\n    });\r\n\r\n    return Array.from(counts.entries())\r\n      .sort((a, b) => b[1] - a[1])\r\n      .slice(0, limit)\r\n      .map(([item]) => item);\r\n  }\r\n\r\n  /**\r\n   * Get trending keywords for a specific category\r\n   */\r\n  public async getTrendingKeywordsByCategory(category: 'social' | 'business' | 'tech' | 'design'): Promise<string[]> {\r\n    const trendingData = await this.getTrendingData();\r\n\r\n    const categoryFeeds = {\r\n      social: ['socialMediaToday', 'socialMediaExaminer', 'bufferBlog', 'hootsuiteBlogs'],\r\n      business: ['hubspotMarketing', 'contentMarketingInstitute', 'marketingProfs'],\r\n      tech: ['techCrunch', 'theVerge', 'wired'],\r\n      design: ['canvaDesignSchool', 'adobeBlog', 'creativeBloq'],\r\n    };\r\n\r\n    const categoryArticles = trendingData.articles.filter(article =>\r\n      categoryFeeds[category].includes(article.source)\r\n    );\r\n\r\n    const keywords: string[] = [];\r\n    categoryArticles.forEach(article => keywords.push(...article.keywords));\r\n\r\n    return this.getTopItems(keywords, 20);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const rssService = new RSSFeedService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAoBO,MAAM;IACH,QAAgE,IAAI,MAAM;IACjE,eAAe,SAAS,QAAQ,GAAG,CAAC,kBAAkB,IAAI,UAAU,KAAK;IAEzE,WAAW;QAC1B,kCAAkC;QAClC,kBAAkB,QAAQ,GAAG,CAAC,sBAAsB;QACpD,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;QAC1D,YAAY,QAAQ,GAAG,CAAC,eAAe;QACvC,gBAAgB,QAAQ,GAAG,CAAC,kBAAkB;QAC9C,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAC3C,WAAW,QAAQ,GAAG,CAAC,cAAc;QAErC,yBAAyB;QACzB,oBAAoB,QAAQ,GAAG,CAAC,wBAAwB;QACxD,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QAEjD,uBAAuB;QACvB,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,2BAA2B,QAAQ,GAAG,CAAC,+BAA+B;QACtE,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,eAAe,QAAQ,GAAG,CAAC,kBAAkB;QAC7C,eAAe,QAAQ,GAAG,CAAC,mBAAmB;QAE9C,gBAAgB;QAChB,YAAY,QAAQ,GAAG,CAAC,cAAc;QACtC,UAAU,QAAQ,GAAG,CAAC,YAAY;QAClC,UAAU,QAAQ,GAAG,CAAC,aAAa;QACnC,OAAO,QAAQ,GAAG,CAAC,SAAS;QAE5B,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,kBAAkB,QAAQ,GAAG,CAAC,qBAAqB;QACnD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QACrD,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAC/C,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;QAE/C,mBAAmB;QACnB,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB;QACjD,mBAAmB,QAAQ,GAAG,CAAC,sBAAsB;QAErD,oBAAoB;QACpB,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB;QACtD,WAAW,QAAQ,GAAG,CAAC,cAAc;QACrC,cAAc,QAAQ,GAAG,CAAC,iBAAiB;QAE3C,oBAAoB;QACpB,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB;IACjD,EAAE;IAEF;;GAEC,GACD,MAAc,aAAa,GAAW,EAAE,UAAkB,EAAyB;QACjF,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC/D,OAAO,OAAO,IAAI;YACpB;YAGA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,MAAM,WAAyB,EAAE;YACjC,MAAM,QAAQ,OAAO,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,QAAQ,OAAO,IAAI,EAAE,SAAS,EAAE;YAExE,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI;YAEtE,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,aAAc;gBAC9C,MAAM,UAAsB;oBAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;oBAClC,aAAa,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAC9D,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE;oBAC3C,SAAS,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK,KAAK,GAAG;oBAC9E,UAAU,IAAI,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACxC,UAAU,IAAI,CAAC,eAAe,CAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,OAAO;oBAExF,QAAQ;gBACV;gBAEA,SAAS,IAAI,CAAC;YAChB;YAEA,oBAAoB;YACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,MAAM;gBAAU,WAAW,KAAK,GAAG;YAAG;YAE5D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAU,EAAU;QACtC,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,OAAO,UAAU,UAAU,OAAO;QACtC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;YAC5C,OAAO,OAAO,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI;QACjE;QACA,IAAI,OAAO,UAAU,YAAY,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC;QAExD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBAAgB,IAAY,EAAY;QAC9C,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,sCAAsC;QACtC,MAAM,YAAY,KACf,OAAO,CAAC,YAAY,IACpB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,kEAAkE;QAClE,MAAM,YAAY,IAAI,IAAI;YACxB;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;SACvP;QAED,MAAM,QAAQ,UACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,OAClD,KAAK,CAAC,GAAG,KAAK,uCAAuC;QAExD,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,oBAAoB;IACzD;IAEA;;GAEC,GACD,MAAa,kBAAyC;QAEpD,MAAM,cAA4B,EAAE;QACpC,MAAM,gBAAyC,EAAE;QAEjD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,YAAY,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAG;YAC7D,IAAI,KAAK;gBACP,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;YAC5C;QACF;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;QAEzC,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,YAAY,IAAI,IAAI,OAAO,KAAK;YAClC;QACF;QAEA,mDAAmD;QACnD,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO,KAAK,EAAE,OAAO,CAAC,OAAO;QAElE,uCAAuC;QACvC,MAAM,cAAwB,EAAE;QAChC,MAAM,YAAsB,EAAE;QAC9B,MAAM,YAAsB,EAAE;QAE9B,YAAY,OAAO,CAAC,CAAA;YAClB,YAAY,IAAI,IAAI,QAAQ,QAAQ;YACpC,IAAI,QAAQ,KAAK,EAAE,UAAU,IAAI,CAAC,QAAQ,KAAK;YAC/C,IAAI,QAAQ,QAAQ,EAAE,UAAU,IAAI,CAAC,QAAQ,QAAQ;QACvD;QAEA,oCAAoC;QACpC,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;QACpD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAChD,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,WAAW;QAGhD,OAAO;YACL,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,UAAU,YAAY,KAAK,CAAC,GAAG;YAC/B,aAAa,IAAI;QACnB;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAe,EAAE,KAAa,EAAY;QAC5D,MAAM,SAAS,IAAI;QAEnB,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,aAAa,KAAK,WAAW,GAAG,IAAI;YAC1C,IAAI,WAAW,MAAM,IAAI,GAAG;gBAC1B,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI;YACzD;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,OAAO,OAAO,IAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IACrB;IAEA;;GAEC,GACD,MAAa,8BAA8B,QAAmD,EAAqB;QACjH,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;QAE/C,MAAM,gBAAgB;YACpB,QAAQ;gBAAC;gBAAoB;gBAAuB;gBAAc;aAAiB;YACnF,UAAU;gBAAC;gBAAoB;gBAA6B;aAAiB;YAC7E,MAAM;gBAAC;gBAAc;gBAAY;aAAQ;YACzC,QAAQ;gBAAC;gBAAqB;gBAAa;aAAe;QAC5D;QAEA,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UACpD,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAGjD,MAAM,WAAqB,EAAE;QAC7B,iBAAiB,OAAO,CAAC,CAAA,UAAW,SAAS,IAAI,IAAI,QAAQ,QAAQ;QAErE,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;IACpC;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/trending-content-enhancer.ts"], "sourcesContent": ["/**\r\n * Trending Content Enhancer\r\n * Integrates RSS feed data to enhance content generation with trending topics\r\n */\r\n\r\nimport { rssService, TrendingData } from '../services/rss-feed-service';\r\n\r\nexport interface TrendingEnhancement {\r\n  keywords: string[];\r\n  topics: string[];\r\n  hashtags: string[];\r\n  seasonalThemes: string[];\r\n  industryBuzz: string[];\r\n}\r\n\r\nexport interface ContentContext {\r\n  businessType?: string;\r\n  platform?: string;\r\n  location?: string;\r\n  targetAudience?: string;\r\n}\r\n\r\nexport class TrendingContentEnhancer {\r\n  private trendingCache: TrendingData | null = null;\r\n  private lastCacheUpdate: number = 0;\r\n  private readonly cacheTimeout = 30 * 60 * 1000; // 30 minutes\r\n\r\n  /**\r\n   * Get fresh trending data with caching\r\n   */\r\n  private async getTrendingData(): Promise<TrendingData> {\r\n    const now = Date.now();\r\n\r\n    if (this.trendingCache && (now - this.lastCacheUpdate) < this.cacheTimeout) {\r\n      return this.trendingCache;\r\n    }\r\n\r\n    this.trendingCache = await rssService.getTrendingData();\r\n    this.lastCacheUpdate = now;\r\n\r\n    return this.trendingCache;\r\n  }\r\n\r\n  /**\r\n   * Get trending enhancement data for content generation\r\n   */\r\n  public async getTrendingEnhancement(context: ContentContext = {}): Promise<TrendingEnhancement> {\r\n    try {\r\n      const trendingData = await this.getTrendingData();\r\n\r\n      // Filter and prioritize based on context\r\n      const relevantKeywords = this.filterKeywordsByContext(trendingData.keywords, context);\r\n      const relevantTopics = this.filterTopicsByContext(trendingData.topics, context);\r\n\r\n      // Generate hashtags from trending keywords\r\n      const hashtags = this.generateHashtags(relevantKeywords, context);\r\n\r\n      // Extract seasonal themes\r\n      const seasonalThemes = this.extractSeasonalThemes(trendingData);\r\n\r\n      // Extract industry-specific buzz\r\n      const industryBuzz = this.extractIndustryBuzz(trendingData, context.businessType);\r\n\r\n\r\n      return {\r\n        keywords: relevantKeywords.slice(0, 15),\r\n        topics: relevantTopics.slice(0, 10),\r\n        hashtags: hashtags.slice(0, 10),\r\n        seasonalThemes: seasonalThemes.slice(0, 5),\r\n        industryBuzz: industryBuzz.slice(0, 8),\r\n      };\r\n\r\n    } catch (error) {\r\n\r\n      // Return fallback data\r\n      return {\r\n        keywords: ['trending', 'viral', 'popular', 'latest', 'new'],\r\n        topics: ['social media trends', 'digital marketing', 'content creation'],\r\n        hashtags: ['#trending', '#viral', '#socialmedia', '#marketing'],\r\n        seasonalThemes: [],\r\n        industryBuzz: [],\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Filter keywords based on context relevance\r\n   */\r\n  private filterKeywordsByContext(keywords: string[], context: ContentContext): string[] {\r\n    const platformKeywords = {\r\n      instagram: ['visual', 'photo', 'story', 'reel', 'aesthetic', 'lifestyle'],\r\n      facebook: ['community', 'share', 'connect', 'family', 'local', 'event'],\r\n      twitter: ['news', 'update', 'breaking', 'discussion', 'opinion', 'thread'],\r\n      linkedin: ['professional', 'business', 'career', 'industry', 'networking', 'leadership'],\r\n      tiktok: ['viral', 'trend', 'challenge', 'creative', 'fun', 'entertainment'],\r\n      pinterest: ['inspiration', 'ideas', 'diy', 'design', 'home', 'style'],\r\n    };\r\n\r\n    const businessKeywords = {\r\n      restaurant: ['food', 'dining', 'menu', 'chef', 'cuisine', 'taste', 'fresh'],\r\n      retail: ['shopping', 'sale', 'fashion', 'style', 'product', 'deal', 'new'],\r\n      fitness: ['health', 'workout', 'training', 'wellness', 'strength', 'motivation'],\r\n      beauty: ['skincare', 'makeup', 'beauty', 'glow', 'treatment', 'style'],\r\n      tech: ['innovation', 'digital', 'technology', 'software', 'app', 'solution'],\r\n      healthcare: ['health', 'wellness', 'care', 'treatment', 'medical', 'patient'],\r\n    };\r\n\r\n    let filtered = [...keywords];\r\n\r\n    // Boost platform-relevant keywords\r\n    if (context.platform && platformKeywords[context.platform as keyof typeof platformKeywords]) {\r\n      const platformBoost = platformKeywords[context.platform as keyof typeof platformKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = platformBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = platformBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    // Boost business-relevant keywords\r\n    if (context.businessType && businessKeywords[context.businessType as keyof typeof businessKeywords]) {\r\n      const businessBoost = businessKeywords[context.businessType as keyof typeof businessKeywords];\r\n      filtered = filtered.sort((a, b) => {\r\n        const aBoost = businessBoost.some(boost => a.includes(boost)) ? -1 : 0;\r\n        const bBoost = businessBoost.some(boost => b.includes(boost)) ? -1 : 0;\r\n        return aBoost - bBoost;\r\n      });\r\n    }\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Filter topics based on context relevance\r\n   */\r\n  private filterTopicsByContext(topics: string[], context: ContentContext): string[] {\r\n    // Remove topics that are too generic or not suitable for social media\r\n    const filtered = topics.filter(topic => {\r\n      const lower = topic.toLowerCase();\r\n      return !lower.includes('error') &&\r\n        !lower.includes('404') &&\r\n        !lower.includes('page not found') &&\r\n        lower.length > 10 &&\r\n        lower.length < 100;\r\n    });\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Generate relevant hashtags from keywords\r\n   */\r\n  private generateHashtags(keywords: string[], context: ContentContext): string[] {\r\n    const hashtags: string[] = [];\r\n\r\n    // Convert keywords to hashtags\r\n    keywords.forEach(keyword => {\r\n      const cleanKeyword = keyword.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\r\n      if (cleanKeyword.length >= 3 && cleanKeyword.length <= 20) {\r\n        hashtags.push(`#${cleanKeyword}`);\r\n      }\r\n    });\r\n\r\n    // Add platform-specific hashtags\r\n    const platformHashtags = {\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#picoftheday'],\r\n      facebook: ['#community', '#local', '#share', '#connect'],\r\n      twitter: ['#news', '#update', '#discussion', '#trending'],\r\n      linkedin: ['#professional', '#business', '#career', '#networking'],\r\n      tiktok: ['#fyp', '#viral', '#trending', '#foryou'],\r\n      pinterest: ['#inspiration', '#ideas', '#diy', '#style'],\r\n    };\r\n\r\n    if (context.platform && platformHashtags[context.platform as keyof typeof platformHashtags]) {\r\n      hashtags.push(...platformHashtags[context.platform as keyof typeof platformHashtags]);\r\n    }\r\n\r\n    // Remove duplicates and return\r\n    return Array.from(new Set(hashtags));\r\n  }\r\n\r\n  /**\r\n   * Extract seasonal themes from trending data\r\n   */\r\n  private extractSeasonalThemes(trendingData: TrendingData): string[] {\r\n    const currentMonth = new Date().getMonth();\r\n    const seasonalKeywords = {\r\n      0: ['new year', 'resolution', 'fresh start', 'winter'], // January\r\n      1: ['valentine', 'love', 'romance', 'winter'], // February\r\n      2: ['spring', 'march madness', 'renewal', 'growth'], // March\r\n      3: ['easter', 'spring', 'bloom', 'fresh'], // April\r\n      4: ['mother\\'s day', 'spring', 'flowers', 'celebration'], // May\r\n      5: ['summer', 'graduation', 'father\\'s day', 'vacation'], // June\r\n      6: ['summer', 'july 4th', 'independence', 'freedom'], // July\r\n      7: ['summer', 'vacation', 'back to school', 'preparation'], // August\r\n      8: ['back to school', 'fall', 'autumn', 'harvest'], // September\r\n      9: ['halloween', 'october', 'spooky', 'fall'], // October\r\n      10: ['thanksgiving', 'gratitude', 'family', 'harvest'], // November\r\n      11: ['christmas', 'holiday', 'winter', 'celebration'], // December\r\n    };\r\n\r\n    const currentSeasonalKeywords = seasonalKeywords[currentMonth as keyof typeof seasonalKeywords] || [];\r\n\r\n    const seasonalThemes = trendingData.keywords.filter(keyword =>\r\n      currentSeasonalKeywords.some(seasonal =>\r\n        keyword.toLowerCase().includes(seasonal.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return seasonalThemes;\r\n  }\r\n\r\n  /**\r\n   * Extract industry-specific buzz from trending data\r\n   */\r\n  private extractIndustryBuzz(trendingData: TrendingData, businessType?: string): string[] {\r\n    if (!businessType) return [];\r\n\r\n    const industryKeywords = {\r\n      restaurant: ['food', 'dining', 'chef', 'cuisine', 'recipe', 'restaurant', 'menu'],\r\n      retail: ['shopping', 'fashion', 'style', 'product', 'brand', 'sale', 'deal'],\r\n      fitness: ['fitness', 'workout', 'health', 'gym', 'training', 'wellness', 'exercise'],\r\n      beauty: ['beauty', 'skincare', 'makeup', 'cosmetics', 'treatment', 'spa'],\r\n      tech: ['technology', 'tech', 'digital', 'software', 'app', 'innovation', 'ai'],\r\n      healthcare: ['health', 'medical', 'healthcare', 'wellness', 'treatment', 'care'],\r\n    };\r\n\r\n    const relevantKeywords = industryKeywords[businessType as keyof typeof industryKeywords] || [];\r\n\r\n    const industryBuzz = trendingData.keywords.filter(keyword =>\r\n      relevantKeywords.some(industry =>\r\n        keyword.toLowerCase().includes(industry.toLowerCase())\r\n      )\r\n    );\r\n\r\n    return industryBuzz;\r\n  }\r\n\r\n  /**\r\n   * Get trending prompt enhancement for AI content generation\r\n   */\r\n  public async getTrendingPromptEnhancement(context: ContentContext = {}): Promise<string> {\r\n    const enhancement = await this.getTrendingEnhancement(context);\r\n\r\n    const promptParts: string[] = [];\r\n\r\n    if (enhancement.keywords.length > 0) {\r\n      promptParts.push(`Trending keywords to consider: ${enhancement.keywords.slice(0, 8).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.seasonalThemes.length > 0) {\r\n      promptParts.push(`Current seasonal themes: ${enhancement.seasonalThemes.join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.industryBuzz.length > 0) {\r\n      promptParts.push(`Industry trending topics: ${enhancement.industryBuzz.slice(0, 5).join(', ')}`);\r\n    }\r\n\r\n    if (enhancement.hashtags.length > 0) {\r\n      promptParts.push(`Suggested hashtags: ${enhancement.hashtags.slice(0, 6).join(' ')}`);\r\n    }\r\n\r\n    return promptParts.join('\\n');\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const trendingEnhancer = new TrendingContentEnhancer();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAiBO,MAAM;IACH,gBAAqC,KAAK;IAC1C,kBAA0B,EAAE;IACnB,eAAe,KAAK,KAAK,KAAK;IAE/C;;GAEC,GACD,MAAc,kBAAyC;QACrD,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,IAAI,CAAC,aAAa,IAAI,AAAC,MAAM,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,YAAY,EAAE;YAC1E,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,IAAI,CAAC,aAAa,GAAG,MAAM,2IAAA,CAAA,aAAU,CAAC,eAAe;QACrD,IAAI,CAAC,eAAe,GAAG;QAEvB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA;;GAEC,GACD,MAAa,uBAAuB,UAA0B,CAAC,CAAC,EAAgC;QAC9F,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe;YAE/C,yCAAyC;YACzC,MAAM,mBAAmB,IAAI,CAAC,uBAAuB,CAAC,aAAa,QAAQ,EAAE;YAC7E,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,aAAa,MAAM,EAAE;YAEvE,2CAA2C;YAC3C,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;YAEzD,0BAA0B;YAC1B,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAElD,iCAAiC;YACjC,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,cAAc,QAAQ,YAAY;YAGhF,OAAO;gBACL,UAAU,iBAAiB,KAAK,CAAC,GAAG;gBACpC,QAAQ,eAAe,KAAK,CAAC,GAAG;gBAChC,UAAU,SAAS,KAAK,CAAC,GAAG;gBAC5B,gBAAgB,eAAe,KAAK,CAAC,GAAG;gBACxC,cAAc,aAAa,KAAK,CAAC,GAAG;YACtC;QAEF,EAAE,OAAO,OAAO;YAEd,uBAAuB;YACvB,OAAO;gBACL,UAAU;oBAAC;oBAAY;oBAAS;oBAAW;oBAAU;iBAAM;gBAC3D,QAAQ;oBAAC;oBAAuB;oBAAqB;iBAAmB;gBACxE,UAAU;oBAAC;oBAAa;oBAAU;oBAAgB;iBAAa;gBAC/D,gBAAgB,EAAE;gBAClB,cAAc,EAAE;YAClB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,wBAAwB,QAAkB,EAAE,OAAuB,EAAY;QACrF,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAU;gBAAS;gBAAS;gBAAQ;gBAAa;aAAY;YACzE,UAAU;gBAAC;gBAAa;gBAAS;gBAAW;gBAAU;gBAAS;aAAQ;YACvE,SAAS;gBAAC;gBAAQ;gBAAU;gBAAY;gBAAc;gBAAW;aAAS;YAC1E,UAAU;gBAAC;gBAAgB;gBAAY;gBAAU;gBAAY;gBAAc;aAAa;YACxF,QAAQ;gBAAC;gBAAS;gBAAS;gBAAa;gBAAY;gBAAO;aAAgB;YAC3E,WAAW;gBAAC;gBAAe;gBAAS;gBAAO;gBAAU;gBAAQ;aAAQ;QACvE;QAEA,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAQ;gBAAW;gBAAS;aAAQ;YAC3E,QAAQ;gBAAC;gBAAY;gBAAQ;gBAAW;gBAAS;gBAAW;gBAAQ;aAAM;YAC1E,SAAS;gBAAC;gBAAU;gBAAW;gBAAY;gBAAY;gBAAY;aAAa;YAChF,QAAQ;gBAAC;gBAAY;gBAAU;gBAAU;gBAAQ;gBAAa;aAAQ;YACtE,MAAM;gBAAC;gBAAc;gBAAW;gBAAc;gBAAY;gBAAO;aAAW;YAC5E,YAAY;gBAAC;gBAAU;gBAAY;gBAAQ;gBAAa;gBAAW;aAAU;QAC/E;QAEA,IAAI,WAAW;eAAI;SAAS;QAE5B,mCAAmC;QACnC,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;YACzF,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,mCAAmC;QACnC,IAAI,QAAQ,YAAY,IAAI,gBAAgB,CAAC,QAAQ,YAAY,CAAkC,EAAE;YACnG,MAAM,gBAAgB,gBAAgB,CAAC,QAAQ,YAAY,CAAkC;YAC7F,WAAW,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,QAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;gBACrE,OAAO,SAAS;YAClB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAgB,EAAE,OAAuB,EAAY;QACjF,sEAAsE;QACtE,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA;YAC7B,MAAM,QAAQ,MAAM,WAAW;YAC/B,OAAO,CAAC,MAAM,QAAQ,CAAC,YACrB,CAAC,MAAM,QAAQ,CAAC,UAChB,CAAC,MAAM,QAAQ,CAAC,qBAChB,MAAM,MAAM,GAAG,MACf,MAAM,MAAM,GAAG;QACnB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAkB,EAAE,OAAuB,EAAY;QAC9E,MAAM,WAAqB,EAAE;QAE7B,+BAA+B;QAC/B,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,eAAe,QAAQ,OAAO,CAAC,iBAAiB,IAAI,WAAW;YACrE,IAAI,aAAa,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI;gBACzD,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;YAClC;QACF;QAEA,iCAAiC;QACjC,MAAM,mBAAmB;YACvB,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;aAAe;YAC1E,UAAU;gBAAC;gBAAc;gBAAU;gBAAU;aAAW;YACxD,SAAS;gBAAC;gBAAS;gBAAW;gBAAe;aAAY;YACzD,UAAU;gBAAC;gBAAiB;gBAAa;gBAAW;aAAc;YAClE,QAAQ;gBAAC;gBAAQ;gBAAU;gBAAa;aAAU;YAClD,WAAW;gBAAC;gBAAgB;gBAAU;gBAAQ;aAAS;QACzD;QAEA,IAAI,QAAQ,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC,EAAE;YAC3F,SAAS,IAAI,IAAI,gBAAgB,CAAC,QAAQ,QAAQ,CAAkC;QACtF;QAEA,+BAA+B;QAC/B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;IAC5B;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAA0B,EAAY;QAClE,MAAM,eAAe,IAAI,OAAO,QAAQ;QACxC,MAAM,mBAAmB;YACvB,GAAG;gBAAC;gBAAY;gBAAc;gBAAe;aAAS;YACtD,GAAG;gBAAC;gBAAa;gBAAQ;gBAAW;aAAS;YAC7C,GAAG;gBAAC;gBAAU;gBAAiB;gBAAW;aAAS;YACnD,GAAG;gBAAC;gBAAU;gBAAU;gBAAS;aAAQ;YACzC,GAAG;gBAAC;gBAAiB;gBAAU;gBAAW;aAAc;YACxD,GAAG;gBAAC;gBAAU;gBAAc;gBAAiB;aAAW;YACxD,GAAG;gBAAC;gBAAU;gBAAY;gBAAgB;aAAU;YACpD,GAAG;gBAAC;gBAAU;gBAAY;gBAAkB;aAAc;YAC1D,GAAG;gBAAC;gBAAkB;gBAAQ;gBAAU;aAAU;YAClD,GAAG;gBAAC;gBAAa;gBAAW;gBAAU;aAAO;YAC7C,IAAI;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;YACtD,IAAI;gBAAC;gBAAa;gBAAW;gBAAU;aAAc;QACvD;QAEA,MAAM,0BAA0B,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAErG,MAAM,iBAAiB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAClD,wBAAwB,IAAI,CAAC,CAAA,WAC3B,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAA0B,EAAE,YAAqB,EAAY;QACvF,IAAI,CAAC,cAAc,OAAO,EAAE;QAE5B,MAAM,mBAAmB;YACvB,YAAY;gBAAC;gBAAQ;gBAAU;gBAAQ;gBAAW;gBAAU;gBAAc;aAAO;YACjF,QAAQ;gBAAC;gBAAY;gBAAW;gBAAS;gBAAW;gBAAS;gBAAQ;aAAO;YAC5E,SAAS;gBAAC;gBAAW;gBAAW;gBAAU;gBAAO;gBAAY;gBAAY;aAAW;YACpF,QAAQ;gBAAC;gBAAU;gBAAY;gBAAU;gBAAa;gBAAa;aAAM;YACzE,MAAM;gBAAC;gBAAc;gBAAQ;gBAAW;gBAAY;gBAAO;gBAAc;aAAK;YAC9E,YAAY;gBAAC;gBAAU;gBAAW;gBAAc;gBAAY;gBAAa;aAAO;QAClF;QAEA,MAAM,mBAAmB,gBAAgB,CAAC,aAA8C,IAAI,EAAE;QAE9F,MAAM,eAAe,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAA,UAChD,iBAAiB,IAAI,CAAC,CAAA,WACpB,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;QAIvD,OAAO;IACT;IAEA;;GAEC,GACD,MAAa,6BAA6B,UAA0B,CAAC,CAAC,EAAmB;QACvF,MAAM,cAAc,MAAM,IAAI,CAAC,sBAAsB,CAAC;QAEtD,MAAM,cAAwB,EAAE;QAEhC,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,+BAA+B,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QAClG;QAEA,IAAI,YAAY,cAAc,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY,IAAI,CAAC,CAAC,yBAAyB,EAAE,YAAY,cAAc,CAAC,IAAI,CAAC,OAAO;QACtF;QAEA,IAAI,YAAY,YAAY,CAAC,MAAM,GAAG,GAAG;YACvC,YAAY,IAAI,CAAC,CAAC,0BAA0B,EAAE,YAAY,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACjG;QAEA,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,YAAY,IAAI,CAAC,CAAC,oBAAoB,EAAE,YAAY,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;QACtF;QAEA,OAAO,YAAY,IAAI,CAAC;IAC1B;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/viral-hashtag-engine.ts"], "sourcesContent": ["/**\r\n * Viral Hashtag Engine - Real-time trending hashtag generation\r\n * Integrates with RSS feeds and trending data to generate viral hashtags\r\n */\r\n\r\nimport { trendingEnhancer } from './trending-content-enhancer';\r\n\r\nexport interface ViralHashtagStrategy {\r\n  trending: string[];      // Currently trending hashtags\r\n  viral: string[];         // High-engagement viral hashtags  \r\n  niche: string[];         // Business-specific niche hashtags\r\n  location: string[];      // Location-based hashtags\r\n  community: string[];     // Community engagement hashtags\r\n  seasonal: string[];      // Seasonal/timely hashtags\r\n  platform: string[];     // Platform-specific hashtags\r\n  total: string[];         // Final combined strategy (15 hashtags)\r\n}\r\n\r\nexport class ViralHashtagEngine {\r\n\r\n  /**\r\n   * Generate viral hashtag strategy using real-time trending data\r\n   */\r\n  async generateViralHashtags(\r\n    businessType: string,\r\n    businessName: string,\r\n    location: string,\r\n    platform: string,\r\n    services?: string,\r\n    targetAudience?: string\r\n  ): Promise<ViralHashtagStrategy> {\r\n\r\n\r\n    try {\r\n      // Get trending data from RSS feeds and trending enhancer\r\n      const trendingData = await trendingEnhancer.getTrendingEnhancement({\r\n        businessType,\r\n        location,\r\n        platform,\r\n        targetAudience\r\n      });\r\n\r\n\r\n      // Generate different hashtag categories\r\n      const trending = await this.getTrendingHashtags(trendingData, businessType, platform);\r\n      const viral = this.getViralHashtags(businessType, platform);\r\n      const niche = this.getNicheHashtags(businessType, services);\r\n      const location_tags = this.getLocationHashtags(location);\r\n      const community = this.getCommunityHashtags(businessType, targetAudience);\r\n      const seasonal = this.getSeasonalHashtags();\r\n      const platform_tags = this.getPlatformHashtags(platform);\r\n\r\n      // Combine and optimize for virality\r\n      const total = this.optimizeForVirality([\r\n        ...trending.slice(0, 4),\r\n        ...viral.slice(0, 3),\r\n        ...niche.slice(0, 2),\r\n        ...location_tags.slice(0, 2),\r\n        ...community.slice(0, 2),\r\n        ...seasonal.slice(0, 1),\r\n        ...platform_tags.slice(0, 1)\r\n      ]);\r\n\r\n\r\n      return {\r\n        trending,\r\n        viral,\r\n        niche,\r\n        location: location_tags,\r\n        community,\r\n        seasonal,\r\n        platform: platform_tags,\r\n        total\r\n      };\r\n\r\n    } catch (error) {\r\n      return this.getFallbackHashtags(businessType, location, platform);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get trending hashtags from RSS data\r\n   */\r\n  private async getTrendingHashtags(trendingData: any, businessType: string, platform: string): Promise<string[]> {\r\n    const hashtags = [...trendingData.hashtags];\r\n\r\n    // Add business-relevant trending hashtags\r\n    const businessTrending = this.getBusinessTrendingHashtags(businessType, platform);\r\n    hashtags.push(...businessTrending);\r\n\r\n    // Remove duplicates and return top trending\r\n    return Array.from(new Set(hashtags)).slice(0, 8);\r\n  }\r\n\r\n  /**\r\n   * Get high-engagement viral hashtags\r\n   */\r\n  private getViralHashtags(businessType: string, platform: string): string[] {\r\n    const viralHashtags = {\r\n      general: ['#viral', '#trending', '#fyp', '#explore', '#discover', '#amazing', '#incredible', '#mustsee'],\r\n      instagram: ['#instagood', '#photooftheday', '#instadaily', '#reels', '#explorepage'],\r\n      tiktok: ['#fyp', '#foryou', '#viral', '#trending', '#foryoupage'],\r\n      facebook: ['#viral', '#share', '#community', '#local', '#trending'],\r\n      twitter: ['#trending', '#viral', '#breaking', '#news', '#update'],\r\n      linkedin: ['#professional', '#business', '#networking', '#career', '#industry']\r\n    };\r\n\r\n    const general = viralHashtags.general.sort(() => 0.5 - Math.random()).slice(0, 4);\r\n    const platformSpecific = viralHashtags[platform.toLowerCase() as keyof typeof viralHashtags] || [];\r\n\r\n    return [...general, ...platformSpecific.slice(0, 3)];\r\n  }\r\n\r\n  /**\r\n   * Get business-specific niche hashtags\r\n   */\r\n  private getNicheHashtags(businessType: string, services?: string): string[] {\r\n    const nicheMap: Record<string, string[]> = {\r\n      restaurant: ['#foodie', '#delicious', '#freshfood', '#localeats', '#foodlover', '#tasty', '#chef', '#dining'],\r\n      bakery: ['#freshbaked', '#artisan', '#homemade', '#bakery', '#pastry', '#bread', '#dessert', '#sweet'],\r\n      fitness: ['#fitness', '#workout', '#health', '#gym', '#strong', '#motivation', '#fitlife', '#training'],\r\n      beauty: ['#beauty', '#skincare', '#makeup', '#glam', '#selfcare', '#beautiful', '#style', '#cosmetics'],\r\n      tech: ['#tech', '#innovation', '#digital', '#software', '#technology', '#startup', '#coding', '#ai'],\r\n      retail: ['#shopping', '#fashion', '#style', '#sale', '#newcollection', '#boutique', '#trendy', '#deals']\r\n    };\r\n\r\n    const baseNiche = nicheMap[businessType.toLowerCase()] || ['#business', '#service', '#quality', '#professional'];\r\n\r\n    // Add service-specific hashtags if provided\r\n    if (services) {\r\n      const serviceWords = services.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const serviceHashtags = serviceWords.slice(0, 3).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      baseNiche.push(...serviceHashtags);\r\n    }\r\n\r\n    return baseNiche.slice(0, 6);\r\n  }\r\n\r\n  /**\r\n   * Get location-based hashtags\r\n   */\r\n  private getLocationHashtags(location: string): string[] {\r\n    const locationParts = location.split(',').map(part => part.trim());\r\n    const hashtags: string[] = [];\r\n\r\n    locationParts.forEach(part => {\r\n      const cleanLocation = part.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '');\r\n      if (cleanLocation.length > 2) {\r\n        hashtags.push(`#${cleanLocation.toLowerCase()}`);\r\n      }\r\n    });\r\n\r\n    // Add generic location hashtags\r\n    hashtags.push('#local', '#community', '#neighborhood');\r\n\r\n    return hashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get community engagement hashtags\r\n   */\r\n  private getCommunityHashtags(businessType: string, targetAudience?: string): string[] {\r\n    const communityHashtags = ['#community', '#local', '#support', '#family', '#friends', '#together', '#love'];\r\n\r\n    if (targetAudience) {\r\n      const audienceWords = targetAudience.toLowerCase().split(/[,\\s]+/).filter(word => word.length > 3);\r\n      const audienceHashtags = audienceWords.slice(0, 2).map(word => `#${word.replace(/[^a-z0-9]/g, '')}`);\r\n      communityHashtags.push(...audienceHashtags);\r\n    }\r\n\r\n    return communityHashtags.slice(0, 5);\r\n  }\r\n\r\n  /**\r\n   * Get seasonal/timely hashtags\r\n   */\r\n  private getSeasonalHashtags(): string[] {\r\n    const now = new Date();\r\n    const month = now.getMonth();\r\n    const day = now.getDate();\r\n\r\n    // Seasonal hashtags based on current time\r\n    const seasonal: Record<number, string[]> = {\r\n      0: ['#newyear', '#january', '#fresh', '#newbeginnings'], // January\r\n      1: ['#february', '#love', '#valentine', '#winter'], // February  \r\n      2: ['#march', '#spring', '#fresh', '#bloom'], // March\r\n      3: ['#april', '#spring', '#easter', '#renewal'], // April\r\n      4: ['#may', '#spring', '#mothers', '#bloom'], // May\r\n      5: ['#june', '#summer', '#fathers', '#sunshine'], // June\r\n      6: ['#july', '#summer', '#vacation', '#hot'], // July\r\n      7: ['#august', '#summer', '#vacation', '#sunny'], // August\r\n      8: ['#september', '#fall', '#autumn', '#backtoschool'], // September\r\n      9: ['#october', '#fall', '#halloween', '#autumn'], // October\r\n      10: ['#november', '#thanksgiving', '#grateful', '#fall'], // November\r\n      11: ['#december', '#christmas', '#holiday', '#winter'] // December\r\n    };\r\n\r\n    return seasonal[month] || ['#today', '#now', '#current'];\r\n  }\r\n\r\n  /**\r\n   * Get platform-specific hashtags\r\n   */\r\n  private getPlatformHashtags(platform: string): string[] {\r\n    const platformHashtags: Record<string, string[]> = {\r\n      instagram: ['#instagram', '#insta', '#ig'],\r\n      facebook: ['#facebook', '#fb', '#social'],\r\n      twitter: ['#twitter', '#tweet', '#x'],\r\n      linkedin: ['#linkedin', '#professional', '#business'],\r\n      tiktok: ['#tiktok', '#tt', '#video']\r\n    };\r\n\r\n    return platformHashtags[platform.toLowerCase()] || ['#social', '#media'];\r\n  }\r\n\r\n  /**\r\n   * Get business-relevant trending hashtags\r\n   */\r\n  private getBusinessTrendingHashtags(businessType: string, platform: string): string[] {\r\n    // This would integrate with real trending APIs in production\r\n    const trendingByBusiness: Record<string, string[]> = {\r\n      restaurant: ['#foodtrends', '#eats2024', '#localfood', '#foodie'],\r\n      fitness: ['#fitness2024', '#healthtrends', '#workout', '#wellness'],\r\n      beauty: ['#beautytrends', '#skincare2024', '#makeup', '#selfcare'],\r\n      tech: ['#tech2024', '#innovation', '#ai', '#digital'],\r\n      retail: ['#fashion2024', '#shopping', '#style', '#trends']\r\n    };\r\n\r\n    return trendingByBusiness[businessType.toLowerCase()] || ['#trending', '#popular', '#new'];\r\n  }\r\n\r\n  /**\r\n   * Optimize hashtag selection for maximum virality\r\n   */\r\n  private optimizeForVirality(hashtags: string[]): string[] {\r\n    // Remove duplicates\r\n    const unique = Array.from(new Set(hashtags));\r\n\r\n    // Sort by estimated engagement potential (simplified scoring)\r\n    const scored = unique.map(tag => ({\r\n      tag,\r\n      score: this.calculateViralScore(tag)\r\n    }));\r\n\r\n    scored.sort((a, b) => b.score - a.score);\r\n\r\n    return scored.slice(0, 15).map(item => item.tag);\r\n  }\r\n\r\n  /**\r\n   * Calculate viral potential score for a hashtag\r\n   */\r\n  private calculateViralScore(hashtag: string): number {\r\n    let score = 0;\r\n\r\n    // High-engagement keywords get bonus points\r\n    const viralKeywords = ['viral', 'trending', 'fyp', 'explore', 'amazing', 'incredible'];\r\n    if (viralKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 10;\r\n    }\r\n\r\n    // Platform-specific hashtags get bonus\r\n    const platformKeywords = ['instagram', 'tiktok', 'reels', 'story'];\r\n    if (platformKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 5;\r\n    }\r\n\r\n    // Local hashtags get moderate bonus\r\n    const localKeywords = ['local', 'community', 'neighborhood'];\r\n    if (localKeywords.some(keyword => hashtag.toLowerCase().includes(keyword))) {\r\n      score += 3;\r\n    }\r\n\r\n    // Length penalty (very long hashtags perform worse)\r\n    if (hashtag.length > 20) score -= 2;\r\n    if (hashtag.length > 30) score -= 5;\r\n\r\n    return score + Math.random(); // Add randomness for variety\r\n  }\r\n\r\n  /**\r\n   * Fallback hashtags when trending data fails\r\n   */\r\n  private getFallbackHashtags(businessType: string, location: string, platform: string): ViralHashtagStrategy {\r\n    return {\r\n      trending: ['#trending', '#viral', '#popular', '#new'],\r\n      viral: ['#amazing', '#incredible', '#mustsee', '#wow'],\r\n      niche: [`#${businessType}`, '#quality', '#professional', '#service'],\r\n      location: ['#local', '#community', `#${location.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()}`],\r\n      community: ['#community', '#support', '#family', '#love'],\r\n      seasonal: ['#today', '#now'],\r\n      platform: [`#${platform.toLowerCase()}`],\r\n      total: [\r\n        '#trending', '#viral', `#${businessType}`, '#local', '#community',\r\n        '#amazing', '#quality', '#professional', '#popular', '#new',\r\n        '#support', '#service', `#${platform.toLowerCase()}`, '#today', '#love'\r\n      ]\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const viralHashtagEngine = new ViralHashtagEngine();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AAaO,MAAM;IAEX;;GAEC,GACD,MAAM,sBACJ,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,QAAiB,EACjB,cAAuB,EACQ;QAG/B,IAAI;YACF,yDAAyD;YACzD,MAAM,eAAe,MAAM,8IAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;gBACjE;gBACA;gBACA;gBACA;YACF;YAGA,wCAAwC;YACxC,MAAM,WAAW,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,cAAc;YAC5E,MAAM,QAAQ,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClD,MAAM,QAAQ,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClD,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;YAC/C,MAAM,YAAY,IAAI,CAAC,oBAAoB,CAAC,cAAc;YAC1D,MAAM,WAAW,IAAI,CAAC,mBAAmB;YACzC,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;YAE/C,oCAAoC;YACpC,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;mBAClC,SAAS,KAAK,CAAC,GAAG;mBAClB,MAAM,KAAK,CAAC,GAAG;mBACf,MAAM,KAAK,CAAC,GAAG;mBACf,cAAc,KAAK,CAAC,GAAG;mBACvB,UAAU,KAAK,CAAC,GAAG;mBACnB,SAAS,KAAK,CAAC,GAAG;mBAClB,cAAc,KAAK,CAAC,GAAG;aAC3B;YAGD,OAAO;gBACL;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA;gBACA,UAAU;gBACV;YACF;QAEF,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,UAAU;QAC1D;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,YAAiB,EAAE,YAAoB,EAAE,QAAgB,EAAqB;QAC9G,MAAM,WAAW;eAAI,aAAa,QAAQ;SAAC;QAE3C,0CAA0C;QAC1C,MAAM,mBAAmB,IAAI,CAAC,2BAA2B,CAAC,cAAc;QACxE,SAAS,IAAI,IAAI;QAEjB,4CAA4C;QAC5C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,CAAC,GAAG;IAChD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAgB,EAAY;QACzE,MAAM,gBAAgB;YACpB,SAAS;gBAAC;gBAAU;gBAAa;gBAAQ;gBAAY;gBAAa;gBAAY;gBAAe;aAAW;YACxG,WAAW;gBAAC;gBAAc;gBAAkB;gBAAe;gBAAU;aAAe;YACpF,QAAQ;gBAAC;gBAAQ;gBAAW;gBAAU;gBAAa;aAAc;YACjE,UAAU;gBAAC;gBAAU;gBAAU;gBAAc;gBAAU;aAAY;YACnE,SAAS;gBAAC;gBAAa;gBAAU;gBAAa;gBAAS;aAAU;YACjE,UAAU;gBAAC;gBAAiB;gBAAa;gBAAe;gBAAW;aAAY;QACjF;QAEA,MAAM,UAAU,cAAc,OAAO,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG;QAC/E,MAAM,mBAAmB,aAAa,CAAC,SAAS,WAAW,GAAiC,IAAI,EAAE;QAElG,OAAO;eAAI;eAAY,iBAAiB,KAAK,CAAC,GAAG;SAAG;IACtD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,YAAoB,EAAE,QAAiB,EAAY;QAC1E,MAAM,WAAqC;YACzC,YAAY;gBAAC;gBAAW;gBAAc;gBAAc;gBAAc;gBAAc;gBAAU;gBAAS;aAAU;YAC7G,QAAQ;gBAAC;gBAAe;gBAAY;gBAAa;gBAAW;gBAAW;gBAAU;gBAAY;aAAS;YACtG,SAAS;gBAAC;gBAAY;gBAAY;gBAAW;gBAAQ;gBAAW;gBAAe;gBAAY;aAAY;YACvG,QAAQ;gBAAC;gBAAW;gBAAa;gBAAW;gBAAS;gBAAa;gBAAc;gBAAU;aAAa;YACvG,MAAM;gBAAC;gBAAS;gBAAe;gBAAY;gBAAa;gBAAe;gBAAY;gBAAW;aAAM;YACpG,QAAQ;gBAAC;gBAAa;gBAAY;gBAAU;gBAAS;gBAAkB;gBAAa;gBAAW;aAAS;QAC1G;QAEA,MAAM,YAAY,QAAQ,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;YAAY;SAAgB;QAEhH,4CAA4C;QAC5C,IAAI,UAAU;YACZ,MAAM,eAAe,SAAS,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YACzF,MAAM,kBAAkB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACjG,UAAU,IAAI,IAAI;QACpB;QAEA,OAAO,UAAU,KAAK,CAAC,GAAG;IAC5B;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC/D,MAAM,WAAqB,EAAE;QAE7B,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,gBAAgB,KAAK,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ;YAC1E,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,WAAW,IAAI;YACjD;QACF;QAEA,gCAAgC;QAChC,SAAS,IAAI,CAAC,UAAU,cAAc;QAEtC,OAAO,SAAS,KAAK,CAAC,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAQ,qBAAqB,YAAoB,EAAE,cAAuB,EAAY;QACpF,MAAM,oBAAoB;YAAC;YAAc;YAAU;YAAY;YAAW;YAAY;YAAa;SAAQ;QAE3G,IAAI,gBAAgB;YAClB,MAAM,gBAAgB,eAAe,WAAW,GAAG,KAAK,CAAC,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAChG,MAAM,mBAAmB,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,cAAc,KAAK;YACnG,kBAAkB,IAAI,IAAI;QAC5B;QAEA,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IAEA;;GAEC,GACD,AAAQ,sBAAgC;QACtC,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,QAAQ;QAC1B,MAAM,MAAM,IAAI,OAAO;QAEvB,0CAA0C;QAC1C,MAAM,WAAqC;YACzC,GAAG;gBAAC;gBAAY;gBAAY;gBAAU;aAAiB;YACvD,GAAG;gBAAC;gBAAa;gBAAS;gBAAc;aAAU;YAClD,GAAG;gBAAC;gBAAU;gBAAW;gBAAU;aAAS;YAC5C,GAAG;gBAAC;gBAAU;gBAAW;gBAAW;aAAW;YAC/C,GAAG;gBAAC;gBAAQ;gBAAW;gBAAY;aAAS;YAC5C,GAAG;gBAAC;gBAAS;gBAAW;gBAAY;aAAY;YAChD,GAAG;gBAAC;gBAAS;gBAAW;gBAAa;aAAO;YAC5C,GAAG;gBAAC;gBAAW;gBAAW;gBAAa;aAAS;YAChD,GAAG;gBAAC;gBAAc;gBAAS;gBAAW;aAAgB;YACtD,GAAG;gBAAC;gBAAY;gBAAS;gBAAc;aAAU;YACjD,IAAI;gBAAC;gBAAa;gBAAiB;gBAAa;aAAQ;YACxD,IAAI;gBAAC;gBAAa;gBAAc;gBAAY;aAAU,CAAC,WAAW;QACpE;QAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;YAAC;YAAU;YAAQ;SAAW;IAC1D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAgB,EAAY;QACtD,MAAM,mBAA6C;YACjD,WAAW;gBAAC;gBAAc;gBAAU;aAAM;YAC1C,UAAU;gBAAC;gBAAa;gBAAO;aAAU;YACzC,SAAS;gBAAC;gBAAY;gBAAU;aAAK;YACrC,UAAU;gBAAC;gBAAa;gBAAiB;aAAY;YACrD,QAAQ;gBAAC;gBAAW;gBAAO;aAAS;QACtC;QAEA,OAAO,gBAAgB,CAAC,SAAS,WAAW,GAAG,IAAI;YAAC;YAAW;SAAS;IAC1E;IAEA;;GAEC,GACD,AAAQ,4BAA4B,YAAoB,EAAE,QAAgB,EAAY;QACpF,6DAA6D;QAC7D,MAAM,qBAA+C;YACnD,YAAY;gBAAC;gBAAe;gBAAa;gBAAc;aAAU;YACjE,SAAS;gBAAC;gBAAgB;gBAAiB;gBAAY;aAAY;YACnE,QAAQ;gBAAC;gBAAiB;gBAAiB;gBAAW;aAAY;YAClE,MAAM;gBAAC;gBAAa;gBAAe;gBAAO;aAAW;YACrD,QAAQ;gBAAC;gBAAgB;gBAAa;gBAAU;aAAU;QAC5D;QAEA,OAAO,kBAAkB,CAAC,aAAa,WAAW,GAAG,IAAI;YAAC;YAAa;YAAY;SAAO;IAC5F;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAkB,EAAY;QACxD,oBAAoB;QACpB,MAAM,SAAS,MAAM,IAAI,CAAC,IAAI,IAAI;QAElC,8DAA8D;QAC9D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;gBAChC;gBACA,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAEvC,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAAe,EAAU;QACnD,IAAI,QAAQ;QAEZ,4CAA4C;QAC5C,MAAM,gBAAgB;YAAC;YAAS;YAAY;YAAO;YAAW;YAAW;SAAa;QACtF,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,uCAAuC;QACvC,MAAM,mBAAmB;YAAC;YAAa;YAAU;YAAS;SAAQ;QAClE,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC7E,SAAS;QACX;QAEA,oCAAoC;QACpC,MAAM,gBAAgB;YAAC;YAAS;YAAa;SAAe;QAC5D,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW;YAC1E,SAAS;QACX;QAEA,oDAAoD;QACpD,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAClC,IAAI,QAAQ,MAAM,GAAG,IAAI,SAAS;QAElC,OAAO,QAAQ,KAAK,MAAM,IAAI,6BAA6B;IAC7D;IAEA;;GAEC,GACD,AAAQ,oBAAoB,YAAoB,EAAE,QAAgB,EAAE,QAAgB,EAAwB;QAC1G,OAAO;YACL,UAAU;gBAAC;gBAAa;gBAAU;gBAAY;aAAO;YACrD,OAAO;gBAAC;gBAAY;gBAAe;gBAAY;aAAO;YACtD,OAAO;gBAAC,CAAC,CAAC,EAAE,cAAc;gBAAE;gBAAY;gBAAiB;aAAW;YACpE,UAAU;gBAAC;gBAAU;gBAAc,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,iBAAiB,IAAI,WAAW,IAAI;aAAC;YAC7F,WAAW;gBAAC;gBAAc;gBAAY;gBAAW;aAAQ;YACzD,UAAU;gBAAC;gBAAU;aAAO;YAC5B,UAAU;gBAAC,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;aAAC;YACxC,OAAO;gBACL;gBAAa;gBAAU,CAAC,CAAC,EAAE,cAAc;gBAAE;gBAAU;gBACrD;gBAAY;gBAAY;gBAAiB;gBAAY;gBACrD;gBAAY;gBAAY,CAAC,CAAC,EAAE,SAAS,WAAW,IAAI;gBAAE;gBAAU;aACjE;QACH;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}]}